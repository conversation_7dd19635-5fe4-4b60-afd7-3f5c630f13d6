﻿using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Framework.UI.Elements.Groups;
using Ifs.Uma.Reporting.Report;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Fields;

namespace Ifs.Uma.Framework.Reporting.Generator
{
    public sealed class GroupToFormConverter
    {
        public Task GenerateForGroupAsync(ReportDoc report, CancellationToken cancelToken, GroupElement element)
        {
            ReportFormItem reportItem = new ReportFormItem();
            reportItem.Header = element.Header;
            reportItem.HasArrange = element.Location != null;

            foreach (var item in element.Form.Fields)
            {
                if (item.IsVisible)
                {
                    ReportFormField reportField = new ReportFormField();
                    reportField.Name = item.Name;
                    reportField.IsImageField = item is MediaPickerField || item is SignatureField;
                    reportField.IsBadgeField = item is BadgeField;
                    reportField.IsTimeStampUtc = item.FieldType == new DateTimeFieldUtc().FieldType;
                    reportField.IsKnownDateTimeField = item.FieldType == new KnownDateTimeField().FieldType;

                    if (item is BadgeField badge)
                    {
                        ReportBadgeItem reportBadgeItem = new ReportBadgeItem()
                            {
                                ForegroundColor = badge.Badge.ForegroundColor,
                                BackgroundColor = badge.Badge.BackgroundColor,
                                Text = item.HasValue ? item.FormatValue(item.Value) : string.Empty
                            };

                        reportField.BadgeItem = reportBadgeItem;
                    }
                    else if (item is KnownDateTimeField field)
                    {
                       reportField.KnownDateField = field; 
                    } 

                    // using the overload for FormatValue below, by passing in the value again, to be compatible with nullable bool fields
                    reportField.DisplayValue = item.HasValue ? item.FormatValue(item.Value) : string.Empty;
                    reportField.IsMultiline = item.Multiline;
                    reportField.FieldSize = GetFieldSize(item.SizeHint);
                    reportItem.Fields.Add(reportField);
                }
            }
            
            report.Items.Add(reportItem);

            return Task.FromResult(true);
        }

        private FieldSize GetFieldSize(SizeHint sizeHint)
        {
            switch (sizeHint)
            {
                case SizeHint.FullWidth:
                    return FieldSize.Full;
                case SizeHint.Large:
                    return FieldSize.Large;
                case SizeHint.Medium:
                    return FieldSize.Medium;
                case SizeHint.Small:
                default:
                    return FieldSize.Small;
            }
        }
    }
}
