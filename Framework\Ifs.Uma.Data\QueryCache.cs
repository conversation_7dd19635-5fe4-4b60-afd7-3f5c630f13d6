﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Data
{
    using ActualQueryCache = global::IQToolkit.QueryCache;

    public static class QueryCache
    {
        public static bool IsEnabled
        {
            get
            {
                return ActualQueryCache.IsEnabled;
            }
            set
            {
                ActualQueryCache.IsEnabled = value;
            }
        }

        public static void Clear()
        {
            ActualQueryCache cache = ActualQueryCache.Instance;
            if (cache != null)
            {
                cache.Clear();
            }
        }
    }
}
