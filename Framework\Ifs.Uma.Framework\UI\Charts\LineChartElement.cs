﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Charts;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Charts
{
    public class LineChartElement : ElementBase
    {
        private readonly IMetadata _metadata;
        private readonly IDataHandler _data;
        private readonly IExpressionRunner _expressionRunner;

        private CpiLineChart _lineChart;

        private readonly UmaColor[] _lineColors = UmaColors.Complementary.ToArray();

        private LineChartModel _lineChartModel;
        public LineChartModel LineChartModel
        {
            get { return _lineChartModel; }
            set
            {
                if (value != _lineChartModel)
                {
                    SetProperty(ref _lineChartModel, value);
                }
            }
        }

        private string _chartHeight;
        public string ChartHeight
        {
            get { return _chartHeight; }
            set
            {
                if (value != _chartHeight)
                {
                    SetProperty(ref _chartHeight, value);
                }
            }
        }

        private string _fullscreenButtonText = Strings.Fullscreen;
        public string FullscreenButtonText
        {
            get { return _fullscreenButtonText; }
            set
            {
                if (value != _fullscreenButtonText)
                {
                    SetProperty(ref _fullscreenButtonText, value);
                } 
            }
        }

        protected override BindingType BindingPropertyType => BindingType.None;

        public void ToggleFullScreen()
        {
            DisplayState = DisplayState == ElementDisplayState.Normal ? ElementDisplayState.FullScreen : ElementDisplayState.Normal;
        }

        public LineChartElement(IMetadata metadata, IDataHandler data, IExpressionRunner expressionRunner)
        {
            _metadata = metadata;
            _data = data;
            _expressionRunner = expressionRunner;
        }

        protected override bool OnInitialize()
        {
            _lineChart = _metadata.FindLineChart(Content.DatasourceProjection ?? ProjectionName, Content.LineChart);

            if (_lineChart == null)
            {
                return false;
            }

            Label = _lineChart.Label;
            ChartHeight = _lineChart.Height.ToLower();
            Content.Override = new CpiElementOverride
            {
                Visible = _lineChart.Visible,
                Collapsed = _lineChart.Collapsed
            };
            
            return true;
        }

        protected override bool OnLoad()
        {
            LoadData();
            return true;
        }

        private void LoadData()
        {
            ExecuteBackgroundTask(LoadDataAsync());
        }

        private async Task LoadDataAsync()
        {
            try
            {
                if (DataSource != null)
                {
                    EntityQuery query = new EntityQuery(DataSource)
                    {
                        SelectAttributes = GetChartSelectAttributes()
                    };

                    EntityQueryResult results = await _data.GetRecordsAsync(query, CancellationToken.None);
                    IEnumerable<LineChartSeries> seriesData = CreateLineChartSeries(results.Records);

                    LineChartModel model = new LineChartModel();

                    foreach (var series in seriesData)
                    {
                        model.Series.Add(series);
                    }

                    model.ChartHeight = ChartHeight;
                    model.AxisLabels = CreateAxisLabels();

                    LineChartModel = model;
                }
            }
            catch (Exception e)
            {
                await HandleException(e);
            }
        }

        private AxisLabels CreateAxisLabels()
        {
            AxisLabels chartAxisLabels = new AxisLabels
            {
                X = _lineChart.Series.XColumn.AxisLabel,
                Y = _lineChart.Series.YColumn.AxisLabel
            };
            return chartAxisLabels;
        }

        private string[] GetChartSelectAttributes()
        {
            HashSet<string> attributes = new HashSet<string>();
            AttributeFinder.FindInLineChart(attributes, _metadata, DataSource.ProjectionName ?? ProjectionName, _lineChart);

            return attributes.ToArray();
        }

        private IEnumerable<LineChartSeries> CreateLineChartSeries(IReadOnlyList<EntityRecord> records)
        {
            List<LineChartSeries> lineChartData = new List<LineChartSeries>();

            string xAxisColumn = _lineChart.Series.XColumn.Column.Attribute;

            int colorPos = 0;
            foreach (CpiLineContent yAxisColumn in _lineChart.Series.YColumn.Content)
            {
                CpiLineColumn column = yAxisColumn.Column;

                List<LineChartLinePoint> dataPoints = RecordsToDataPoints(records, xAxisColumn, column.Attribute);

                if (dataPoints.Count == 0)
                {
                    continue;
                }

                LineChartSeries chartSeries = new LineChartSeries
                {
                    Data = dataPoints,
                    Name = column.Label ?? column.Attribute,
                    Color = GetChartColor(column.OfflineEmphasis ?? column.Emphasis, colorPos++)
                };

                lineChartData.Add(chartSeries);
            }
            
            return lineChartData;
        }

        private List<LineChartLinePoint> RecordsToDataPoints(IReadOnlyList<EntityRecord> records, string xAxisColumn, string columnAtrribute)
        {
            List<LineChartLinePoint> dataPoints = new List<LineChartLinePoint>();
            foreach (EntityRecord record in records)
            {
                if (record.Row[xAxisColumn] != null && record.Row[columnAtrribute] != null)
                {
                    dataPoints.Add(new LineChartLinePoint
                    {
                        X = ObjectConverter.ToDateTime(record.Row[xAxisColumn]),
                        Y = ObjectConverter.ToDouble(record.Row[columnAtrribute])
                    });
                }
            }

            return dataPoints;
        }

        private UmaColor GetChartColor(Dictionary<string, CpiExpression>[] emphasisExpressions, int colorPosition)
        {
            if (emphasisExpressions != null)
            {
                string emphasis = _expressionRunner.GetEmphasis(emphasisExpressions, ViewData);
                UmaColor? emphasisColor = UmaColor.FromEmphasis(emphasis);
                if (emphasisColor.HasValue)
                {
                    return emphasisColor.Value;
                }
            }

            return _lineColors[colorPosition % _lineColors.Length];
        }

        protected override void OnDisplayStateChanged()
        {
            FullscreenButtonText = DisplayState == ElementDisplayState.FullScreen ? Strings.ExitFullscreen : Strings.Fullscreen;
            HasHeader = DisplayState == ElementDisplayState.Normal;
            LineChartModel.IsFullscreen = DisplayState == ElementDisplayState.FullScreen;
            base.OnDisplayStateChanged();
            if (!HasLoaded)
            {
                LoadData();
            }
        }

        protected override void OnReloadData()
        {
            base.OnReloadData();
            LoadData();
        }

        protected override void OnStoredDataChanged(DataChangeSet changeSet)
        {
            base.OnStoredDataChanged(changeSet);

            if (DataSource != null && DataSource.IsEffectedByChangeSet(changeSet))
            {
                LoadData();
            }
        }

        protected override void OnDataSourceChanged()
        {
            base.OnDataSourceChanged();
            LoadData();
        }
    }
}
