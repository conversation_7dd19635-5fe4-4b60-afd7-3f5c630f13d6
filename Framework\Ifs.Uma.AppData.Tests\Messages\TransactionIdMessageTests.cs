﻿using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;
using System.Linq;

namespace Ifs.Uma.AppData.Tests.Messages
{
    [TestFixture]
    public class TransactionIdMessageTests : DataContextTest<FwDataContext>
    {
        private FwDataContext _db = null;
        private MessageTestContext _ctx = null;

        [SetUp]
        public void SetupContexts()
        {
            _db = CreateDataContext();
            _ctx = new MessageTestContext(_db);
        }

        [Test(Description = "A start transaction message with a transaction id, should not be processed")]
        public void StartMessageWithTransactionId()
        {
            _ctx.ExecuteMessage(MessageType.TRANSTARTED, @"
{
    ""MessageId"": 1220575,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""TRANSTARTED"",
    ""MessageText"": ""{\""batch_transaction_started\"":null}"",
    ""MessageData"": null
}", transactionId: 1);

            Assert.AreEqual(1, _db.MessageIn.Count(x => x.TransactionId == 1));
        }

        [Test(Description = "A start message, with an update message, with transaction ids, should not be processed")]
        public void MessagesWithNoTransactionEnd()
        {
            _ctx.ExecuteMessage(MessageType.TRANSTARTED, @"
{
    ""MessageId"": 1220575,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""TRANSTARTED"",
    ""MessageText"": ""{\""batch_transaction_started\"":null}"",
    ""MessageData"": null
}", transactionId: 1);

            _ctx.ExecuteMessage(MessageType.UPDATE, @"
{
    ""MessageId"": 1220576,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""UPDATE"",
    ""MessageText"": ""{\""sync\"":{\""tst_off_work_order\"":[{\""obj_id\"":\""AADiuIAAFAACvoNAAB\"",\""obj_version\"":\""20180628125401\"",\""obj_key\"":\""789C8FFB70A840D1B3EBD16F22769D88\"",\""objstate\"":\""COMPLETED\"",\""wo_no\"":27.0,\""creation_date\"":\""2017-10-19T16:48:58\"",\""description\"":\""Transcoding the interface won\u0027t do anything, we need to generate the 1080p VGA transmitter\"",\""comments\"":null,\""company\"":null,\""image\"":\""/9j/4RhRRXhpZgAASUkqAAgAAAAMADIBAgAUAAAAngAA\""}]}}"",
    ""MessageData"": null
}", transactionId: 1);

            Assert.AreEqual(2, _db.MessageIn.Count(x => x.TransactionId == 1));
        }

        [Test(Description = "Start update and end messages with transaction ids, should be processed")]
        public void MessagesWithTransactionEnd()
        {
            _ctx.ExecuteMessage(MessageType.TRANSTARTED, @"
{
    ""MessageId"": 1220575,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""TRANSTARTED"",
    ""MessageText"": ""{\""batch_transaction_started\"":null}"",
    ""MessageData"": null
}", transactionId: 1);

            _ctx.ExecuteMessage(MessageType.UPDATE, @"
{
    ""MessageId"": 1220576,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""UPDATE"",
    ""MessageText"": ""{\""sync\"":{\""tst_off_work_order\"":[{\""obj_id\"":\""AADiuIAAFAACvoNAAB\"",\""obj_version\"":\""20180628125401\"",\""obj_key\"":\""789C8FFB70A840D1B3EBD16F22769D88\"",\""objstate\"":\""COMPLETED\"",\""wo_no\"":27.0,\""creation_date\"":\""2017-10-19T16:48:58\"",\""description\"":\""Transcoding the interface won\u0027t do anything, we need to generate the 1080p VGA transmitter\"",\""comments\"":null,\""company\"":null,\""image\"":\""/9j/4RhRRXhpZgAASUkqAAgAAAAMADIBAgAUAAAAngAA\""}]}}"",
    ""MessageData"": null
}", transactionId: 1);

            _ctx.ExecuteMessage(MessageType.TRANENDED, @"
{
    ""MessageId"": 1220577,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""TRANENDED"",
    ""MessageText"": ""{\""batch_transaction_ended\"":null}"",
    ""MessageData"": null
}", transactionId: 1);

            Assert.AreEqual(0, _db.MessageIn.Count(x => x.TransactionId == 1));
        }

        [Test(Description = "Overlapping messages with different transaction ids. Only messages with the first transaction id should be processed in this case")]
        public void TwoTransactionsWithOneEnd()
        {
            _ctx.ExecuteMessage(MessageType.TRANSTARTED, @"
{
    ""MessageId"": 1220575,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""TRANSTARTED"",
    ""MessageText"": ""{\""batch_transaction_started\"":null}"",
    ""MessageData"": null
}", transactionId: 1);

            _ctx.ExecuteMessage(MessageType.TRANSTARTED, @"
{
    ""MessageId"": 1220578,
    ""TransactionId"": 2,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""TRANSTARTED"",
    ""MessageText"": ""{\""batch_transaction_started\"":null}"",
    ""MessageData"": null
}", transactionId: 2);

            _ctx.ExecuteMessage(MessageType.UPDATE, @"
{
    ""MessageId"": 1220576,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""UPDATE"",
    ""MessageText"": ""{\""sync\"":{\""tst_off_work_order\"":[{\""obj_id\"":\""AADiuIAAFAACvoNAAB\"",\""obj_version\"":\""20180628125401\"",\""obj_key\"":\""789C8FFB70A840D1B3EBD16F22769D88\"",\""objstate\"":\""COMPLETED\"",\""wo_no\"":27.0,\""creation_date\"":\""2017-10-19T16:48:58\"",\""description\"":\""Transcoding the interface won\u0027t do anything, we need to generate the 1080p VGA transmitter\"",\""comments\"":null,\""company\"":null,\""image\"":\""/9j/4RhRRXhpZgAASUkqAAgAAAAMADIBAgAUAAAAngAA\""}]}}"",
    ""MessageData"": null
}", transactionId: 1);

            Assert.AreEqual(2, _db.MessageIn.Count(x => x.TransactionId == 1));
            Assert.AreEqual(1, _db.MessageIn.Count(x => x.TransactionId == 2));

            _ctx.ExecuteMessage(MessageType.UPDATE, @"
{
    ""MessageId"": 1220579,
    ""TransactionId"": 2,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""UPDATE"",
    ""MessageText"": ""{\""sync\"":{\""tst_off_work_order\"":[{\""obj_id\"":\""AADiuIAAFAACvoNAAB\"",\""obj_version\"":\""20180628125401\"",\""obj_key\"":\""789C8FFB70A840D1B3EBD16F22769D88\"",\""objstate\"":\""COMPLETED\"",\""wo_no\"":27.0,\""creation_date\"":\""2017-10-19T16:48:58\"",\""description\"":\""Transcoding the interface won\u0027t do anything, we need to generate the 1080p VGA transmitter\"",\""comments\"":null,\""company\"":null,\""image\"":\""/9j/4RhRRXhpZgAASUkqAAgAAAAMADIBAgAUAAAAngAA\""}]}}"",
    ""MessageData"": null
}", transactionId: 2);

            _ctx.ExecuteMessage(MessageType.TRANENDED, @"
{
    ""MessageId"": 1220577,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""TRANENDED"",
    ""MessageText"": ""{\""batch_transaction_ended\"":null}"",
    ""MessageData"": null
}", transactionId: 1);

            Assert.AreEqual(0, _db.MessageIn.Count(x => x.TransactionId == 1));
            Assert.AreEqual(2, _db.MessageIn.Count(x => x.TransactionId == 2));
        }

        [Test(Description = "Update message with no transaction id should be processed without an end message")]
        public void MessagesWithDefaultTransactionIds()
        {
            _ctx.ExecuteMessage(MessageType.UPDATE, @"
{
    ""MessageId"": 1220576,
    ""TransactionId"": 1,
    ""RelatedMessageId"": null,
    ""TransactionType"": ""UPDATE"",
    ""MessageText"": ""{\""sync\"":{\""tst_off_work_order\"":[{\""obj_id\"":\""AADiuIAAFAACvoNAAB\"",\""obj_version\"":\""20180628125401\"",\""obj_key\"":\""789C8FFB70A840D1B3EBD16F22769D88\"",\""objstate\"":\""COMPLETED\"",\""wo_no\"":27.0,\""creation_date\"":\""2017-10-19T16:48:58\"",\""description\"":\""Transcoding the interface won\u0027t do anything, we need to generate the 1080p VGA transmitter\"",\""comments\"":null,\""company\"":null,\""image\"":\""/9j/4RhRRXhpZgAASUkqAAgAAAAMADIBAgAUAAAAngAA\""}]}}"",
    ""MessageData"": null
}");

            Assert.AreEqual(0, _db.MessageIn.Count());
        }
    }
}
