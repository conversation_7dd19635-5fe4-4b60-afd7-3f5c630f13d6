﻿using System.Diagnostics;
using System.Linq;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.AppData.Messages
{
    public sealed class InitMessageIn : MessageIn
    {
        public InitMessageIn(MessageType messageType)
            : base(messageType)
        {
        }

        protected override void OnExecute(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper, 
            ILogger logger, DataChangeSet dataChangeSet, TransitionRow transitionRow, bool isInitializing)
        {
            InitializeStatus status = ctx.DatabaseInfos.Select(x => x.InitializeStatus).First();

            switch (MessageType)
            {
                case MessageType.INITSTARTED:
                    InitializeDatabase(ctx, command, clientKeysMapper);
                    SetInitializationStatus(ctx, InitializeStatus.InProgressSystem);
                    break;
                case MessageType.INITDATA:
                    SetInitializationStatus(ctx, InitializeStatus.InProgressData);
                    break;
                case MessageType.INITENDED:
                    SetInitializationStatus(ctx, InitializeStatus.Initialized);
                    break;
                case MessageType.METADATA_REFRESHED:
                    PrepareForMetadataRefresh(ctx, status);
                    break;
                case MessageType.INITFAILED:
                    InitializeDatabase(ctx, command, clientKeysMapper);
                    SetInitializationStatus(ctx, InitializeStatus.Failed);
                    break;
                case MessageType.INITREQUIRED:
                    if (status < InitializeStatus.Initialized)
                    {
                        ctx.ClearTransitions(command);
                        SetInitializationStatus(ctx, InitializeStatus.Required);
                    }
                    else
                    {
                        SetInitializationStatus(ctx, InitializeStatus.Initialized_WithInitRequired);
                    }
                    break;
            }
        }

        private void SetInitializationStatus(FwDataContext ctx, InitializeStatus status)
        {
            AssertNoChanges(ctx);

            DatabaseInfo info = ctx.DatabaseInfos.First();
            ctx.DatabaseInfos.Attach(info);
            info.InitializeStatus = status;
            ctx.SubmitChanges(false);
        }

        private void InitializeDatabase(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper)
        {
            ctx.InitializeAppTables(command);
            QueryCache.Clear();
            clientKeysMapper?.Load(ctx.Model, ctx.ClientKeysMap.ToArray());
        }

        [Conditional("DEBUG")]
        private void AssertNoChanges(FwDataContext ctx)
        {
            IChangeSet changeSet = ctx.GetChangeSet();
            Debug.Assert(!changeSet.Inserts.Any());
            Debug.Assert(!changeSet.Updates.Any());
            Debug.Assert(!changeSet.Deletes.Any());
        }

        private void PrepareForMetadataRefresh(FwDataContext ctx, InitializeStatus status)
        {
            string refreshType = GetMetadataRefreshType();

            if (refreshType == "Projection")
            {
                if (status >= InitializeStatus.Initialized)
                {
                    SetInitializationStatus(ctx, InitializeStatus.Initialized_WithInitRequired);
                }
                else if (status == InitializeStatus.UpdateMetadata_AfterMessagesSent)
                {
                    SetInitializationStatus(ctx, InitializeStatus.Required_AfterMessagesSent);
                }
                else if (status != InitializeStatus.Required_AfterMessagesSent)
                {
                    SetInitializationStatus(ctx, InitializeStatus.Required);
                }
            }
            else
            {
                // Client Change
                if (status >= InitializeStatus.Initialized)
                {
                    if (status != InitializeStatus.Initialized_WithInitRequired)
                    {
                        SetInitializationStatus(ctx, InitializeStatus.Initialized_WithMetaRefreshRequired);
                    }
                }
                else if (status > InitializeStatus.Required && status <= InitializeStatus.Initialized)
                {
                    SetInitializationStatus(ctx, InitializeStatus.Required);
                }
            }
        }

        private string GetMetadataRefreshType()
        {
            if (MessageType != MessageType.METADATA_REFRESHED)
            {
                return string.Empty;
            }

            JObject jObj = ReadDataAsJson();

            if (jObj != null)
            {
                return jObj["MetadataRefreshed"].ToString();
            }

            return string.Empty;
        }

        public string GetInitializationError()
        {
            if (MessageType != MessageType.INITFAILED)
            {
                return null;
            }
            
            try
            {
                JObject jObj = ReadDataAsJson();

                if (jObj != null)
                {
                    return jObj["InitializationEnded"].ToString();
                }
            }
            catch
            {
                //Fix for if we have a bad JSON string
                string messageText = MessageData;
                if (messageText != null && messageText.StartsWith("{\"initialization_ended\":\"") && messageText.EndsWith("\"}"))
                {
                    messageText = messageText.Replace("{\"initialization_ended\":\"", string.Empty);
                    messageText = messageText.Replace("\"}", string.Empty);
                    return messageText;
                }
            }

            return null;
        }
    }
}
