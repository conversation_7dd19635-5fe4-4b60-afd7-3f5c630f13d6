﻿using Ifs.Uma.AppData.AttributeExpressions;

namespace Ifs.Uma.AppData
{
    public struct AttributeFilter
    {
        public string AttributeName { get; }
        public object Value { get; }

        public AttributeFilter(string attributeName, object value)
        {
            AttributeName = attributeName;
            Value = value;
        }

        internal AttributeExpression ToAttributeExpression()
        {
            return AttributeExpression.Compare(AttributeName, AttributeCompareOperator.Equals, Value);
        }
    }
}
