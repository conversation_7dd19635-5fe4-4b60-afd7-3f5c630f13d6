﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;

namespace Ifs.Cloud.Client.Utils
{
    public static class Formatter
    {
#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
        private const int Second = 1;
        private const int Minute = 60 * Second;
        private const int Hour   = 60 * Minute;
        private const int Day    = 24 * Hour;
        private const int Month  = 30 * Day;
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row

        private const string InvariantShortDateFormat = "yyyy/MM/dd";
        private const string UserFriendlyFixedDateFormat = "MMMM d - hh:mm tt";                

        private const string StrAnd = "&";
        private const string StrAndhtml = "&amp;";
        private const string StrLess = "<";
        private const string StrLesshtml = "&lt;";
        private const string StrMore = ">";
        private const string StrMorehtml = "&gt;";
        private const string StrQuote = "\"";
        private const string StrQuotehtml = "&quot;";
        private const string StrApos = "'";
        private const string StrAposhtml = "&apos;";
        // Culture specific properties
        private static readonly TextInfo TextInfo = CultureInfo.CurrentCulture.TextInfo;

        private static readonly FormatProvider FrmPrv = new FormatProvider();        

        /// <summary>
        /// Returns a user friendly representation of an item count
        /// </summary>
        /// <param name="count">Item count</param>
        /// <returns>A user friendly representation of the given item count</returns>
        public static string GetUserFriendlyItemCount(int count)
        {
            switch (count)
            {
                case 0: return "No items available yet";
                case 1: return "One new item";
                default: return $"{count} new items";
            }
        }
        
        /// <summary>
        /// Returns a string representation of given date.
        /// </summary>
        /// <remarks>
        /// Returned value is culture-independent
        /// </remarks>
        /// <param name="date">Date</param>
        /// <returns>A string representation of given date</returns>
        public static string FormatInvariantShortDate(DateTime? date)
        {
            return date.HasValue ? date.Value.ToString(InvariantShortDateFormat) : string.Empty;
        }

        /// <summary>
        /// Returns value of given invariant short date string
        /// </summary>
        /// <remarks>
        /// Invariant representation is value is culture-independent
        /// </remarks>
        /// <param name="shortDate">Invariant short date string</param>
        /// <returns>Value of given string</returns>
        public static DateTime ParseInvariantShortDate(string shortDate)
        {
            return DateTime.ParseExact(shortDate, InvariantShortDateFormat, CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Converts given date into the short date format
        /// </summary>
        /// <remarks>
        /// Returned value is dependent upon the current culture
        /// </remarks>
        /// <param name="date">Date</param>
        /// <returns>Given date formatted into a short date</returns>
        public static string FormatShortDate(DateTime? date)
        {
            return date.HasValue ? string.Format(FrmPrv, "{0:d}", date) : string.Empty;
        }

        /// <summary>
        /// Converts given date into the long date format
        /// </summary>
        /// <remarks>
        /// Returned value is dependent upon the current culture
        /// </remarks>
        /// <param name="date">Date</param>
        /// <returns>Given date formatted into a long date</returns>
        public static string FormatLongDate(DateTime? date)
        {
            return date.HasValue ? string.Format(FrmPrv, "{0:d}", date) : string.Empty;
        }

        /// <summary>
        /// Returns a user friendly representation of a date/time
        /// </summary>
        /// <remarks>'Fixed' means we do not use rolling words such as
        /// 'yesterday' or 'last month'</remarks>
        /// <param name="time">The date/time</param>
        /// <returns>A user friendly fixed date/time representation</returns>
        public static string GetUserFriendlyFixedDateTime(DateTime? time)
        {
            return time.HasValue ? time.Value.ToString(UserFriendlyFixedDateFormat) : string.Empty;
        }

        /// <summary>
        /// Returns a user friendly representation of a date/time
        /// </summary>
        /// <remarks>'Mixed' means this might return rolling words such as
        /// 'yesterday' or 'last month' if it finds possible</remarks>
        /// <param name="time">The date/time</param>
        /// <param name="includeTimeComponent">If true, time component will be added; if not, only the date portion will be returned</param>
        /// <param name="dateMonthYearOrder">If true, format of the returned value would be in the order of date-month-year. 
        /// if false, the other way around</param>
        /// <returns>A user friendly mixed date/time representation</returns>
        public static string GetUserFiendlyMixedTime(DateTime? time, bool includeTimeComponent, bool dateMonthYearOrder)
        {
            if (!time.HasValue)
            {
                return string.Empty;
            }

            DateTime value = time.Value;

            TimeSpan ts = new TimeSpan(DateTime.Now.Ticks - value.Ticks);
            double delta = ts.TotalSeconds;

            string timeComponentStr = includeTimeComponent ? value.ToString("hh:mm tt") : string.Empty;

            if (delta < 0)
            {
                // let fall through
            }
            else if (delta < 24 * Hour)
            {
                return "today " + timeComponentStr;
            }
            else if (delta < 48 * Hour)
            {
                return "yesterday " + timeComponentStr;
            }
            else if (delta < 72 * Hour)
            {
                return "day before yesterday " + timeComponentStr;
            }

            if (value.Year == DateTime.Today.Year)
            {
                return value.ToString(dateMonthYearOrder ? (includeTimeComponent ? "MMMM dd - hh:mm tt" : "MMMM dd") : (includeTimeComponent ? "dd MMMM - hh:mm tt" : "dd MMMM"));
            }
            else
            {
                return value.ToString(dateMonthYearOrder ? (includeTimeComponent ? "yyyy MMMM dd - hh:mm tt" : "yyyy MMMM dd") : (includeTimeComponent ? "dd MMMM yyyy - hh:mm tt" : "dd MMMM yyyy"));
            }
        }

        /// <summary>
        /// Returns a user friendly representation of the elapsed time since given date/time
        /// </summary>
        /// <param name="time">The date/time</param>
        /// <returns>A user friendly representation of elapsed time since given date/time until now</returns>
        public static string GetUserFriendlyElapsedTime(DateTime? time)
        {
            if (!time.HasValue)
            {
                return string.Empty;
            }

            TimeSpan ts = new TimeSpan(DateTime.Now.Ticks - time.Value.Ticks);
            double delta = ts.TotalSeconds;

            if (delta < 0)
            {
                return "not yet";
            }
            else if (delta < 1 * Minute)
            {
                return "few seconds ago";
            }
            else if (delta < 2 * Minute)
            {
                return "a minute ago";
            }
            else if (delta < 45 * Minute)
            {
                return $"{ts.Minutes} {(ts.Minutes == 1 ? "minute" : "minutes")} ago";
            }
            else if (delta < 90 * Minute)
            {
                return "an hour ago";
            }
            else if (delta < 24 * Hour)
            {
                return $"{ts.Hours} {(ts.Hours == 1 ? "hour" : "hours")} ago";
            }
            else if (delta < 48 * Hour)
            {
                return "yesterday";
            }
            else if (delta < 30 * Day)
            {
                return $"{ts.Days} days ago";
            }
            else if (delta < 12 * Month)
            {
                int months = Convert.ToInt32(Math.Floor((double)ts.Days / 30));
                return months <= 1 ? "one month ago" : $"{months} months ago";
            }
            else
            {
                int years = Convert.ToInt32(Math.Floor((double)ts.Days / 365));
                return years <= 1 ? "one year ago" : $"{years} years ago";
            }
        }

        /// <summary>
        /// Returns given string - if it is null, an empty string is returned
        /// </summary>
        /// <param name="value">Text value</param>
        /// <returns>Returns given text value if it is not null; an empty string is returned if null</returns>
        public static string ReplaceNullWithEmpty(string value)
        {
            return value ?? string.Empty;
        }               
        
        /// <summary>
        /// Encodes given value so that it can be used as HTTP content
        /// </summary>
        /// <param name="value">Value to be encoded</param>
        /// <returns>Encoded value</returns>
        public static string HttpEncode(string value)
        {            
            return value.Replace(StrAnd, StrAndhtml).Replace(StrLess, StrLesshtml).Replace(StrMore, StrMorehtml).Replace(StrQuote, StrQuotehtml).Replace(StrApos, StrAposhtml);
        }

        /// <summary>
        /// Decodes given value that has been used as HTTP content
        /// </summary>
        /// <param name="value">Value to be decoded</param>
        /// <returns>Decoded value</returns>
        public static string HttpDecode(string value)
        {
            return value.Replace(StrLesshtml, StrLess).Replace(StrMorehtml, StrMore).Replace(StrQuotehtml, StrQuote).Replace(StrAposhtml, StrApos).Replace(StrAndhtml, StrAnd);
        }

        /// <summary>
        /// Creates a query string using given parameters so that it can be used in a Uri as the query.
        /// </summary>
        /// <param name="namesAndValues">parameters in a Name1, Value1, Name2, Value2, ..., NameN, ValueN, ... form</param>
        /// <returns>query string to be used as a query in a Uri</returns>
        public static string ToQueryString(params object[] namesAndValues)
        {
            if (namesAndValues.Length % 2 == 0)
            {
                Dictionary<string, string> parameters = new Dictionary<string, string>();
                for (int i = 0; i < namesAndValues.Length; i += 2)
                {
                    parameters[namesAndValues[i].ToString()] = namesAndValues[i + 1].ToString();
                }
                return ToQueryString(parameters);
            }
            else
            {
                throw new ArgumentException("The arguments are not in valid 'Name1, Value1, Name2, Value2, ...' form. Some element is missing");
            }
        }

        /// <summary>
        /// Creates a query string using given parameters so that it can be used in a Uri as the query.
        /// </summary>
        /// <param name="parameters">parameters</param>
        /// <returns>query string to be used as a query in a Uri</returns>
        public static string ToQueryString(Dictionary<string, string> parameters)
        {
            IList<string> parts = new List<string>();
            foreach (string key in parameters.Keys)
            {
                if (parameters[key].GetType() != typeof(List<string>))
                    parts.Add($"${Uri.EscapeDataString(key)}={Uri.EscapeDataString(parameters[key])}");
            }

            return string.Join("&", parts.ToArray());
        }

        public static string ToParamString(Dictionary<string, object> parameters)
        {
            IList<string> parts = new List<string>();
            foreach (string key in parameters.Keys)
            {
                if (parameters[key].GetType() == typeof(string))
                {
                    parts.Add($"{Uri.EscapeDataString(key)}='{Uri.EscapeDataString(parameters[key].ToString())}'");
                }
                else
                {
                    parts.Add($"{Uri.EscapeDataString(key)}={Uri.EscapeDataString(parameters[key].ToString())}");
                }
            }

            return string.Join(",", parts.ToArray());
        }

        /// <summary>
        /// Splits lines of given multi-line text
        /// </summary>
        /// <param name="multiLineText">Text to be split into lines</param>
        /// <returns>The lines of given text</returns>
        public static string[] SplitLines(string multiLineText)
        {
            return multiLineText.Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries);
        }

        /// <summary>
        /// Compares two strings for equality, ignoring the case
        /// </summary>
        /// <remarks>
        /// There are many ways to achieve this functionality in .NET and each method varies in terms of
        /// behavior and performance.
        /// This provides a centralized and unified method of comparison so that whenever the functionality is
        /// required, this should be used instead of any other means.
        /// </remarks>
        /// <param name="a">A</param>
        /// <param name="b">B</param>
        /// <returns>True if a and b are equal when case is ignored; false otherwise</returns>
        public static bool EqualsIgnoreCase(string a, string b)
        {
            return string.Equals(a, b, StringComparison.CurrentCultureIgnoreCase);
        }

        /// <summary>
        /// We 'normalize' a string in order to make it culture-neutral in comparisons.
        /// Two normalized strings can be compared ordinally, independent of the culture.
        /// </summary>
        /// <param name="s">String to be normalized</param>
        /// <returns>Normalized string.</returns>
        public static string NormalizeString(string s)
        {
            if (s != null)
            {
                return TextInfo.ToLower(s);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Normalizes the given character
        /// </summary>
        /// <param name="c">Character to be normalized</param>
        /// <returns>Normalized character</returns>
        /// <see cref="NormalizeString(string)"/>
        public static char NormalizeChar(char c)
        {
            return char.ToLower(c);
        }

        private class FormatProvider : IFormatProvider
        {
            public object GetFormat(Type formatType)
            {
                if (formatType == typeof(NumberFormatInfo))
                {
                    return NumberFormatInfo.CurrentInfo;
                }
                else if (formatType == typeof(DateTimeFormatInfo))
                {
                    return DateTimeFormatInfo.CurrentInfo;
                }
                return null;
            }
        }
    }
}
