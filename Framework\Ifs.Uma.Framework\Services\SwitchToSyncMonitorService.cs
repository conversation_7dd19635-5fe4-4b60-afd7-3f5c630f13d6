﻿using Ifs.Uma.Data.Sync;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Services
{
    internal sealed class SwitchToSyncMonitorService : InitializeStatusWatcher
    {
        private readonly ISystemNavigator _systemNavigator;
        private bool _savedInitializingState;

        public SwitchToSyncMonitorService(ILogger logger, ITransactionSyncService transactionSyncService,
            ISystemNavigator systemNavigator)
            : base(logger, transactionSyncService, ThreadOption.UIThread)
        {
            _systemNavigator = systemNavigator;
            _savedInitializingState = transactionSyncService.IsInitializing;
        }
        
        protected override bool OnMatchEventFilter(InitializeStatus status)
        {
            bool oldInitializingState = _savedInitializingState;
            bool newInitializingState = status < InitializeStatus.Initialized;
            
            _savedInitializingState = newInitializingState;

            return oldInitializingState != newInitializingState && newInitializingState;
        }

        protected override async void OnEvent(InitializeStatus status)
        {
            await _systemNavigator.NavigateToSyncMonitorAsync();
        }
    }
}
