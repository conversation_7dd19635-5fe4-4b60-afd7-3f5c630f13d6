﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.List
{
    internal sealed class ListIndexOf : ListFunction
    {
        public const string FunctionName = "IndexOf";

        public ListIndexOf()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            MarbleList list = PrepareList(context, parameters[0]);
            object value = parameters[1].GetValue();
            return list.GetIndex(context, value);
        }
    }
}
