﻿using System.Linq;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class EntityQueryApplyTests : FrameworkTest
    {
        [Test]
        public void Filter()
        {
            PrepareQuery(out EntityQuery query, out EntityRecord[] records);

            query.AddFilter("CustomerName", "John");
            CheckResults(query.ApplyTo(records), "2", "3");

            query.AddFilter("CustomerType", "TYPE_A");
            CheckResults(query.ApplyTo(records), "3");
        }

        [Test]
        public void Search()
        {
            PrepareQuery(out EntityQuery query, out EntityRecord[] records);

            query.Search = new AttributeSearch(new[] { "CustomerName" }, "oh");            
            CheckResults(query.ApplyTo(records), "2", "3");

            query.Search = new AttributeSearch(new[] { "CustomerName" }, "d");
            CheckResults(query.ApplyTo(records), "4");

            query.Search = new AttributeSearch(new[] { "CustomerName", "CustomerType" }, "A");
            CheckResults(query.ApplyTo(records), "1", "3");
        }

        [Test]
        public void WhereExpression()
        {
            PrepareQuery(out EntityQuery query, out EntityRecord[] records);
            
            AttributeExpression exp =
                    AttributeExpression.Or(
                        AttributeExpression.Compare("CustomerTypeRef.TypeDescription", AttributeCompareOperator.Equals, "Customer Type A"),
                        AttributeExpression.Compare("CustomerName", AttributeCompareOperator.Equals, "D"));

            query.FilterExpression = exp;

            CheckResults(query.ApplyTo(records), "1", "3", "4");
        }

        [Test]
        public void Sort()
        {
            PrepareQuery(out EntityQuery query, out EntityRecord[] records);
            
            query.AddSort("CustomerNo", Uma.Database.ESortOrder.Descending);

            CheckResults(query.ApplyTo(records), "4", "3", "2", "1");

            query.Sorts.Clear();
            query.AddSort("CustomerName", Uma.Database.ESortOrder.Ascending);
            query.AddSort("CustomerType", Uma.Database.ESortOrder.Ascending);

            CheckResults(query.ApplyTo(records), "1", "4", "3", "2");
        }

        [Test]
        public void Skip()
        {
            PrepareQuery(out EntityQuery query, out EntityRecord[] records);

            query.Skip = 1;
            CheckResults(query.ApplyTo(records), "2", "3", "4");

            query.Skip = 2;
            CheckResults(query.ApplyTo(records), "3", "4");
        }

        [Test]
        public void Take()
        {
            PrepareQuery(out EntityQuery query, out EntityRecord[] records);

            query.Take = 1;
            CheckResults(query.ApplyTo(records), "1");

            query.Take = 2;
            CheckResults(query.ApplyTo(records), "1", "2");
        }

        [Test]
        public void SkipAndTake()
        {
            PrepareQuery(out EntityQuery query, out EntityRecord[] records);

            query.Skip = 1;
            query.Take = 1;

            EntityQueryResult result = query.ApplyTo(records);
            Assert.IsTrue(result.HasMoreResults);
            CheckResults(result, "2");

            result = result.GetNextQuery().ApplyTo(records);
            Assert.IsTrue(result.HasMoreResults);
            CheckResults(result, "3");

            result = result.GetNextQuery().ApplyTo(records);
            Assert.IsFalse(result.HasMoreResults);
            CheckResults(result, "4");

            query.Skip = 2;
            query.Take = 2;
            CheckResults(query.ApplyTo(records), "3", "4");
        }

        private void PrepareQuery(out EntityQuery query, out EntityRecord[] records)
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Customers");
            query = new EntityQuery(source);
            query.Expand = new[] { "CustomerTypeRef" };
            FwDataContext ctx = CreateDataContext();
            records = ctx.Query(query).ToArray();
        }

        private void CheckResults(EntityQueryResult result, params string[] ids)
        {
            Assert.IsFalse(result.DataSourceOffline);
            string[] actualIds = result.Records.Select(x => (string)x.Row["CustomerNo"]).ToArray();
            Assert.That(actualIds, Is.EquivalentTo(ids));
        }
        
        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("EntityQuerySchema", "EntityQueryData");
        }
    }
}
