﻿using System.IO;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using static Ifs.Uma.Comm.TouchApps.EntityResource;

namespace Ifs.Uma.Comm.TouchApps
{
    public enum CrudOperation
    {
        Insert,
        Update,
        Delete,
        Prepare
    }

    internal class EntityUpdateResource : AppResource, ICustomResourceSerializer
    {
        public override string ResourceName => GetResourceUrl();

        protected override string ResourceSection => "entity";
        
        public int DeviceId { get; set; }
        
        public string Projection { get; set; }

        public string EntityName { get; set; }
        public string EntitySetName { get; set; }
        public string PrimaryKeyString { get; set; }

        public JObject Data { get; set; }

        public RemoteRow ExistingRow { get; set; }

        public CrudOperation CrudOperation { get; set; }

        public IMetaTable MetaTable { get; set; }

        public IClientKeysMapper ClientKeysMapper { get; set; }

        public string SerializeToJsonString()
        {
            return Data != null ? MessageUtils.JObjectToString(Data) : string.Empty;
        }

        public object EmptyResponseHandling(bool isSuccess)
        {
            return isSuccess ? ExecuteResult.True : ExecuteResult.False;
        }

        public object DeserializeJsonString(string jsonString)
        {
            if (CrudOperation == CrudOperation.Delete)
            {
                return ExecuteResult.None;
            }

            JObject jObj = JObject.Parse(jsonString);
            if (jObj != null)
            {
                if (CrudOperation == CrudOperation.Update)
                {
                    RemoteRow row = (RemoteRow)MetaTable.CloneRow(ExistingRow);
                    MessageUtils.ApplyJObjectToRow(MetaTable, ClientKeysMapper, jObj, row);
                    return new ExecuteResult(row);
                }
                else if (CrudOperation == CrudOperation.Prepare)
                {
                    EntityData entityData;
                    using (StringReader sr = new StringReader(jsonString))
                    using (JsonReader reader = new JsonTextReader(sr))
                    {
                        JsonSerializer serializer = new JsonSerializer();
                        entityData = serializer.Deserialize<EntityData>(reader);
                    }

                    RemoteRow row;
                    if (entityData.Result != null && entityData.Result.Count > 0)
                    {
                        row = MessageUtils.JObjectToRow(MetaTable, ClientKeysMapper, entityData.Result[0].ToObject<JObject>());
                    }
                    else
                    {
                        row = MessageUtils.JObjectToRow(MetaTable, ClientKeysMapper, jObj);
                    }

                    return new ExecuteResult(row);
                }
                else
                {
                    RemoteRow row = MessageUtils.JObjectToRow(MetaTable, ClientKeysMapper, jObj);
                    return new ExecuteResult(row);
                }
            }

            return ExecuteResult.None;
        }

        private string GetResourceUrl()
        {
            string url = Projection + ".svc";
            if (!string.IsNullOrEmpty(EntitySetName))
            {
                url += "\\" + EntitySetName;
            }

            if (!string.IsNullOrEmpty(PrimaryKeyString))
            {
                PrimaryKeyString = PrimaryKeyString.Replace("^", ",");
                PrimaryKeyString = PrimaryKeyString.Replace("/", "%2F");
                PrimaryKeyString = PrimaryKeyString.Remove(PrimaryKeyString.Length - 1);
                PrimaryKeyString = PrimaryKeyString.UnderscoreToCamelCase();
                PrimaryKeyString = char.ToUpper(PrimaryKeyString[0]) + PrimaryKeyString.Substring(1);

                if (!string.IsNullOrEmpty(PrimaryKeyString))
                {
                    url = url + "(" + PrimaryKeyString + ")";
                }
            }

            if (CrudOperation == CrudOperation.Prepare)
            {
                url += "\\" + "IfsApp." + Projection + "." + EntityName + "_Default()";
            }

            return url;
        }
    }
}
