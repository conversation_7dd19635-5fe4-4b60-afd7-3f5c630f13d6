﻿using System;
using System.Threading;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Localization;
using Ifs.Uma.Services;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.Services
{
    internal sealed class CommsBrokenWatcher : Service
    {
        private readonly IIfsConnection _connection;
        private readonly ITouchApp _touchApp;
        private readonly SynchronizationContext _uiContext;
        private readonly IDialogService _dialogService;
        private readonly ITransactionSyncDataHandler _syncDataHandler;
        private readonly ITransactionSyncService _transactionSyncService;

        public CommsBrokenWatcher(ILogger logger, ITouchApp touchApp, IIfsConnection connection,
            IDialogService dialogService, ITransactionSyncDataHandler syncDataHandler,
            ITransactionSyncService transactionSyncService)
            : base(logger)
        {
            _connection = connection;
            _touchApp = touchApp;
            _uiContext = SynchronizationContext.Current;
            _dialogService = dialogService;
            _syncDataHandler = syncDataHandler;
            _transactionSyncService = transactionSyncService;
        }

        protected override void OnStart()
        {
            _connection.TouchAppsComms.IsBrokenChanged += TouchAppsComms_IsBrokenChanged;
            base.OnStart();
        }

        protected override void OnStop()
        {
            _connection.TouchAppsComms.IsBrokenChanged -= TouchAppsComms_IsBrokenChanged;
            base.OnStop();
        }

        private void TouchAppsComms_IsBrokenChanged(object sender, EventArgs e)
        {
            TouchAppsComms comms = _connection.TouchAppsComms;
            BrokenCause cause = comms.BrokenCause;
            if (comms.IsBroken && cause != null)
            {
                _uiContext.Post(_ => OnCommsBroken(cause), null);
            }
        }

        private async void OnCommsBroken(BrokenCause cause)
        {
            try
            {
                TouchAppAccount account = _touchApp.CurrentSession.Account;
                bool deactivated = account?.IsActivated == false;

                if (deactivated || cause.Reason == BrokenReason.RequiresActivation)
                {
                    await _dialogService.WaitForDialogsToClose(ServiceCancelToken);

                    if (IsRunning)
                    {
                        string message = cause.Exception.Message;
                        if (cause.Reason == BrokenReason.RequiresActivation)
                        {
                            message = Strings.PleaseReactivate;
                        }

                        await _dialogService.Alert(Strings.DeviceDeactivated, message);
                        await _touchApp.LogoutAsync();
                    }
                }
                else if (cause.Reason == BrokenReason.RequiresInitialization)
                {
                    await _dialogService.WaitForDialogsToClose(ServiceCancelToken);

                    if (IsRunning && !_transactionSyncService.IsInitializing)
                    {
                        int dialogResult = await _dialogService.ShowAsync(Strings.DeviceMustBeReinitialized, null,
                            new[]
                            {
                                Strings.Logout,
                                Strings.Reinitialize
                            });

                        bool initializeNow = dialogResult == 1;
                        if (initializeNow)
                        {
                            _connection?.TouchAppsComms?.FixConnection();
                            _transactionSyncService.RequestInitialization(false);
                        }
                        else
                        {
                            //Set to require initialization before logging out
                            await _transactionSyncService.StopAsync();
                            _syncDataHandler.UpdateInitializeStatus(InitializeStatus.Required);
                            await _touchApp.LogoutAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogException(ex);
            }
        }
    }
}
