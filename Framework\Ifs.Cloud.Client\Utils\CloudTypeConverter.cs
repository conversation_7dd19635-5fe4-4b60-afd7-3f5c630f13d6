﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  2012-02-02 PKULLK Created.
#endregion

using System;
using System.Globalization;

namespace Ifs.Cloud.Client.Utils
{
    /// <summary>
    /// Converts between cloud values and client (local) values
    /// </summary>
    public static class CloudTypeConverter
    {
        #region Constants
        private static readonly NumberFormatInfo CloudNumberFormat = NumberFormatInfo.GetInstance(new CultureInfo("en-US"));        
        private static readonly DateTime UtcBaseDateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        #endregion

        #region Client-to-Cloud Conversion
        public static string IntToSerial(int intValue)
        {
            return intValue.ToString(CloudNumberFormat);
        }
        
        public static string FloatToSerial(float floatValue)
        {
            return floatValue.ToString(CloudNumberFormat);
        }

        public static string DoubleToSerial(double doubleValue)
        {
            return doubleValue.ToString(CloudNumberFormat);
        }
        
        public static string DateTimeToSerial(DateTime dateTime)
        {
            DateTime utcDateTime = dateTime.ToClientUniversalTime();
            long millis = (long)(utcDateTime - UtcBaseDateTime).TotalMilliseconds;
            return $"/Date({millis})/";
        }

        public static string BinaryToSerial(byte[] binary)
        {
            return Convert.ToBase64String(binary);
        }
        #endregion

        #region Cloud-to-Client Conversion

        public static int SerialToInt(string intStr)
        {
            return int.Parse(intStr, CloudNumberFormat);
        }

        public static float SerialToFloat(string floatStr)
        {
            return float.Parse(floatStr, CloudNumberFormat);
        }

        public static double SerialToDouble(string doubleStr)
        {
            return double.Parse(doubleStr, CloudNumberFormat);
        }

        public static DateTime SerialToDateTime(string dateStr)
        {
            long millis = long.Parse(dateStr.Substring(6, dateStr.Length - 8));
            DateTime utcTime = UtcBaseDateTime.Add(TimeSpan.FromMilliseconds(millis));
            return utcTime.ToClientLocalTime();
        }

        public static byte[] SerialToBinary(string binaryStr)
        {
            return Convert.FromBase64String(binaryStr);
        }
        #endregion
    }
}
