﻿using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    internal sealed class DisallowMethodChecker : IfsExpressionVisitor
    {
        public static void Check(Expression expression)
        {
            DisallowMethodChecker visitor = new DisallowMethodChecker();
            visitor.Visit(expression);
        }

        protected internal override Expression VisitMethod(MethodExpression exp)
        {
            throw new ExpressionException($"Method call '{exp.MethodName}' is not supported here");
        }
    }
}
