﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  xxxx-xx-xx SUKMLK Created.
//  2011-10-13 PKULLK Modified to be compatible with .NEt4.5 with async calls, etc.
#endregion

using System;
using System.Net.Http;

namespace Ifs.Cloud.Client.Types
{
    /// <summary>
    /// HTTP method type
    /// </summary>
    public enum HttpMethodType
    {
        Get, Post, Put, Delete, Patch, NotSet
    }

#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
    /// <summary>
    /// HTTP method type enum extension
    /// </summary>
    public static class HttpMethodTypeExtension
    {
        public const string Get     = "GET";
        public const string Patch   = "PATCH";
        public const string Post    = "POST";
        public const string Put     = "PUT";
        public const string Delete  = "DELETE";
        public const string NotSet  = "NOT_SET"; // This is NOT a standard HTTP Method type

        public static string GetString(this HttpMethodType methodType)
        {
            switch (methodType)
            {
                case HttpMethodType.Get:    return Get;
                case HttpMethodType.Post:   return Post;
                case HttpMethodType.Put:    return Put;
                case HttpMethodType.Delete: return Delete;
                case HttpMethodType.Patch:  return Patch;
                case HttpMethodType.NotSet: return NotSet;
            }

            throw new ArgumentException($"Unknown Http method {methodType} ", nameof(methodType));
        }

        public static HttpMethod GetMethod(this HttpMethodType methodType)
        {
            switch (methodType)
            {
                case HttpMethodType.Get:    return HttpMethod.Get;
                case HttpMethodType.Post:   return HttpMethod.Post;
                case HttpMethodType.Put:    return HttpMethod.Put;
                case HttpMethodType.Delete: return HttpMethod.Delete;
                case HttpMethodType.Patch:  return new HttpMethod("PATCH");
                default: throw new Exception($"Unsupported http method '{methodType}'");
            }
        }
    }
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row
}
