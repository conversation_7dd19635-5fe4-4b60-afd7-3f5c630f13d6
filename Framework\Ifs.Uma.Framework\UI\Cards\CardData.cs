﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Extensions;
using Ifs.Uma.Framework.UI.Forms;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Cards
{
    public class CardData : ViewData
    {
        private Field _editableField;
        public Field EditableField
        {
            get => _editableField;
            private set => SetProperty(ref _editableField, value, OnEditableFieldChanged);
        }
        public EntityDataSource DataSource { get; protected set; }

        private CardDef _cardDef;
        private CardDefItem _editableItem;
        private Task _validateAndSave;
        private bool _lookupSelected;

        public CardData(PageData pageData, RecordData record)
            : base(pageData, record)
        {
            record.PropertyChanged += Record_PropertyChanged;
            PageData.DefaultViewData.CommandsEnabledOnEmpty = true;
            PageData.DefaultViewData.CommandsEnabledOnHasChanges = true;
            PageData.DefaultViewData.Record.PropertyChanged += Record_PropertyChanged;
        }

        private void Record_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(Record.HasChanges) &&
                EditableField != null &&
                !(EditableField is BoolField))
            {
                if (Record.HasChanges)
                {
                    // No need to do anything when the user clicks Done
                    // The act of clicking Done will take focus away from the editable field
                    // and cause the record to be saved by the InteractionFinished event
                    PageData.EditMode.BeginEditing(() => Task.FromResult(true));

                    Field field = _cardDef.Metadata.CreateFieldType(Record.ProjectionName, _editableItem.Field, 0);
                }
                else
                {
                    PageData.EditMode.EndEditing();
                }
            }
        }

        public void SetCardDef(CardDef cardDef, CardDefItem editableItem)
        {
            _cardDef = cardDef;
            _editableItem = editableItem;
            if (editableItem?.Field == null || Record == null)
            {
                EditableField = null;
                return;
            }

            AttributeValue attrib = AttributeValue.Create(editableItem.Field.Attribute, Record);
            if (attrib == null)
            {
                EditableField = null;
                return;
            }

            Field field = cardDef.Metadata.CreateFieldType(Record.ProjectionName, editableItem.Field, 0);

            field.Name = editableItem.Field.Label;
            field.ShowLabel = editableItem.Field.ShowLabel.HasValue ? editableItem.Field.ShowLabel.Value : true;
            field.SizeHint = editableItem.Field.ControlSize.ToSizeHint();
            field.Multiline = editableItem.Field.Multiline;
            field.Tag = editableItem;

#if LIDAR_SERVICE
            if (editableItem.Field.LengthMeasure != null && DeviceInfo.OperatingSystem == Utility.OperatingSystem.iOS && PlatformServices.LidarService != null && PlatformServices.LidarService.IsLidarCapableDevice())
            {
                field.IsLidarEnabled = cardDef.ExpressionRunner.RunCheck(editableItem.Field.LengthMeasure.Enabled, this, true);
                if (field.IsLidarEnabled)
                {
                    if (!string.IsNullOrEmpty(editableItem.Field.LengthMeasure.MeasureUnit))
                    {
                        field.LidarMeasureUnit = cardDef.ExpressionRunner.InterpolateString(editableItem.Field.LengthMeasure.MeasureUnit, Record);
                    }

                    if (!string.IsNullOrEmpty(editableItem.Field.LengthMeasure.MediaAttribute))
                    {
                        field.LidarMediaAttribute = editableItem.Field.LengthMeasure.MediaAttribute;
                    }

                    if (EditableField != null)
                    {
                        EditableField.ValueChanged -= Field_ValueChanged;
                    }

                    field.ValueChanged += Field_ValueChanged;
                }
            }
#endif

            if (attrib.AttributePath.RefName != null)
            {
                field.Editability = FieldEditability.Never;
            }
            else
            {
                EntityInfo entityInfo = EntityInfo.Get(cardDef.Metadata, Record.ProjectionName, editableItem.Field.Entity);
                bool isPrimaryKey = entityInfo?.IsPrimaryKeyAttribute(editableItem.Field.Attribute) ?? false;
                field.SetFieldEditability(Record.EntityCrudType, isPrimaryKey);
            }

            if (field is LovField lookupField)
            {
                lookupField.RefName = editableItem.Field.Update?.Item;

                CpiExpression hideKeyExpression = editableItem.Field.View?.OfflineHideKey ?? editableItem.Field.View?.HideKey;
                bool hideKey = hideKeyExpression?.QuickCheck() ?? false;
                if (!hideKey)
                {
                    lookupField.Key = "${" + editableItem.Field.Attribute + "}";
                }
                lookupField.Description = editableItem.Field.View?.Description;
                lookupField.ExpressionRunner = cardDef.ExpressionRunner;
                lookupField.Command = Command.FromAsyncMethod(() => OpenLookup(lookupField, editableItem.Field, this));

                _cardDef = cardDef;
                EditableField = lookupField;

                if (_lookupSelected)
                {
                    lookupField.UpdateStates(Record);
                }
            }
            else
            {
                field.SetBinding(attrib, nameof(attrib.Value));
                _cardDef = cardDef;
                EditableField = field;
            }
        }

        private async Task OpenLookup(LovField field, CpiField fieldDef, ViewData cardData)
        {
            if (Resolver.TryResolve(out ILovService lovService))
            {
                bool selected = await lovService.OpenLovAsync(field, fieldDef, cardData);
                if (selected)
                {
                    field.UpdateStates(Record);
                    if (Record.HasChanges)
                    {
                        _lookupSelected = true;
                        _ = ValidateAndSave();
                    }
                }
            }
        }

        private void OnEditableFieldChanged(Field oldValue, Field newValue)
        {
            if (oldValue != null)
            {
                oldValue.InteractionFinished -= EditableField_InteractionFinished;
            }

            if (newValue != null)
            {
                newValue.InteractionFinished += EditableField_InteractionFinished;
            }
        }

        private void EditableField_InteractionFinished(object sender, EventArgs e)
        {
            if (Record.HasChanges)
            {
                _ = ValidateAndSave();
            }
        }

        private async Task ValidateAndSave()
        {
            if (_validateAndSave != null)
            {
                await _validateAndSave;
            }

            Task validateAndSave = ValidateAndSaveImpl();
            _validateAndSave = validateAndSave.ContinueWith(_ => _validateAndSave = null);
            PageData?.BackgroundTasks.Add(validateAndSave);
        }

        public async Task<bool> ValidateAndSaveAsync()
        {
            await ValidateAndSave();

            if (_validateAndSave != null)
            {
                await _validateAndSave;
            }

            return true;
        }

        private async Task ValidateAndSaveImpl()
        {
            if (_cardDef == null)
            {
                return;
            }

            try
            {
                if (EditableField?.Tag is CardDefItem item && item?.Field?.ValidateCommand != null)
                {
                    CpiCommand command = item.Field.ValidateCommand;
                    _cardDef.CommandExecutor.GetStates(Record.ProjectionName, this, command, true, out bool isVisible, out bool isEnabled);

                    if (isVisible && isEnabled)
                    {
                        await _cardDef.CommandExecutor.ExecuteAsync(Record.ProjectionName, this, command);
                    }
                }

                string entitySetName = PageData.DefaultViewData.Record.GetRemoteRow()?.EntitySetName;
                if (string.IsNullOrEmpty(entitySetName))
                {
                    entitySetName = PageData?.DataSource?.EntitySetName;
                }

                if (Record.HasChanges && !string.IsNullOrEmpty(entitySetName))
                {
                    Record.GetRemoteRow().EntitySetName = entitySetName;
                    if (DataSource is ArrayDataSource)
                    {
                        ArrayDataSource ads = (ArrayDataSource)DataSource;

                        string arrayKey = ObjPrimaryKey.FromPrimaryKeyQueryParams(_cardDef.Metadata.MetaModel, Record.GetRemoteRow(), Record.ProjectionName);

                        Record.GetRemoteRow().ArraySourceName = ads.ArrayPropertyName + arrayKey;
                        Record.GetRemoteRow().PrimaryKeyString = ads.ParentKey.ToFormattedKeyRef(Record.ProjectionName);
                    }

                    ExecuteResult result = await Record.SaveRecordAsync();

                    if (result.Exception != null)
                    {
                        throw result.Exception;
                    }

                    EditableField?.NotifyHighlightRequested();
                }
            }
            catch (Exception ex)
            {
                _cardDef.Logger.HandleException(ExceptionType.Unexpected, ex);

                try
                {
                    // If the record fails to save the user has no way to reset the
                    // record to the original state - instead we will always reload the row.
                    // Since cards only have one editable field not much user input has been lost
                    await Record.ReloadRecordAsync();
                }
                catch (Exception exReload)
                {
                    _cardDef.Logger.HandleException(ExceptionType.Unexpected, exReload);
                }

                await _cardDef.DialogService.ShowException(ex);
            }
        }

        private void Field_ValueChanged(object sender, EventArgs e)
        {
            Field field = (Field)sender;

            if (field.IsLidarMediaAttributeSet())
            {
                Record?.Assign(field.LidarMediaAttribute, field.GetLidarImage());
            }
        }
    }
}
