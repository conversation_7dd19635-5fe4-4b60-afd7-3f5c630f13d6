using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Android;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Runtime;
using Android.Support.V4.Content;
#if REMOTE_ASSISTANCE
using HelpLightning.SDK.Android.Binding;
#endif
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Framework.Services;
using Ifs.Uma.Framework.UI.Activities;
using Ifs.Uma.Framework.UI.Attachments;
using Ifs.Uma.Framework.UI.Dialogs;
using Ifs.Uma.Framework.UI.Elements.Calendars;
using Ifs.Uma.Framework.UI.Maps;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Localization;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI.Dialogs;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Newtonsoft.Json;
using Unity;
using Unity.Lifetime;
using static Ifs.Uma.Services.Location.LocationServiceBase;

namespace Ifs.Uma.Framework.App
{
    public abstract class IfsApplication : Application, IPlatformApplication
    {
        private readonly IUnityContainer _container = new UnityContainer();
        public IUnityContainer AppUnityContainer => _container;

        private readonly ISettings _appSettings;
        public ISettings AppSettings => _appSettings;

        public ILoggerManager LoggerManager { get; private set; }
        public bool DebugBuild { get; protected set; }

        public BroadcastReceiverPushNotification Receiver { get; private set; }

        protected IfsApplication(IntPtr javaReference, JniHandleOwnership transfer)
            : base(javaReference, transfer)
        {
            CultureUtils.FixCurrentCulture();

            _appSettings = AppDataSettings.Current;

            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

            PlatformServicesAndroid.Initialize();

            _container.RegisterInstance<IPlatformApplication>(this);
            _container.RegisterInstance<ISettings>(_appSettings);
            Receiver = new BroadcastReceiverPushNotification();
        }

        public override void OnCreate()
        {
            string key = PlatformServices.GetSyncfusionKey();
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(key);

#if REMOTE_ASSISTANCE
            HLClient.Instance.Init(ApplicationContext);
#endif

            base.OnCreate();
            OnSetupLogging();
            OnLaunched();
            RegisterReceiver(Receiver, new IntentFilter("android.intent.action.MAIN"), (ActivityFlags)ReceiverFlags.Exported);
            PerformRootCheck();
        }

        private void PerformRootCheck()
        {
            string deviceRootStatus = RootDetectionUtil.RootSettings.Get(RootDetectionUtil.RootStatusLocalContainer);

            if (deviceRootStatus == RootDetectionUtil.RootStatus.Rooted.ToString() || deviceRootStatus == RootDetectionUtil.RootStatus.RootedAndLogged.ToString())
            {
                return;
            }

            string buildTags = Build.Tags;
            bool buildTagCheck = buildTags != null && buildTags.Contains("test-keys");

            bool rootaccess = RootDetectionUtil.RootCheckSu() || RootDetectionUtil.RootCheckApk();

            if (buildTagCheck || rootaccess)
            {
                RootDetectionUtil.RootSettings.Set(RootDetectionUtil.RootStatusLocalContainer, RootDetectionUtil.RootStatus.Rooted);
            }
            else
            {
                RootDetectionUtil.RootSettings.Set(RootDetectionUtil.RootStatusLocalContainer, RootDetectionUtil.RootStatus.NotRooted);
            }
        }

        protected virtual void OnLaunched()
        {
            IActivityService activityService = new ActivityService(this);
            AppUnityContainer.RegisterInstance(activityService);

            AppUnityContainer.RegisterType<IDialogService, DialogService>(new ContainerControlledLifetimeManager());
            AppUnityContainer.RegisterType<IRegistrar, PlatformRegistrar>();
            AppUnityContainer.RegisterType<ISystemNavigator, SystemNavigator>();
            AppUnityContainer.RegisterType<IToastService, ToastService>(new ContainerControlledLifetimeManager());
            AppUnityContainer.RegisterType<ILocalStorage, LocalStorage>(new ContainerControlledLifetimeManager());
            AppUnityContainer.RegisterType<IFileService, FileService>(new ContainerControlledLifetimeManager());
            AppUnityContainer.RegisterType<IOpenIdAuthenticator, OpenIdAuthenticator>(new ContainerControlledLifetimeManager());

            NavigationService navigationService = new NavigationService(this);
            navigationService.ResolveNavigation += OnResolveNavigationAsync;
            navigationService.NavigationMenuType = typeof(AppMenu);
            AppUnityContainer.RegisterInstance<INavigationService>(navigationService);
            AppUnityContainer.RegisterInstance<INavigator>(navigationService);
            AppUnityContainer.RegisterInstance<IBreadcrumbManager>(navigationService);

            ThemeService themeService = new ThemeService();
            AppUnityContainer.RegisterInstance<IThemeService>(themeService);
            LoadThemeData(themeService);

            AppRegistration appReg = CreateAppRegistration();
            _container.RegisterInstance(appReg);

            TouchApp.CreateAndRegisterTouchApp(_container, appReg, DebugBuild);
        }

        private void LoadThemeData(ThemeService themeService)
        {
            ISettings settings = AppDataSettings.Current;
            string jsonString = settings.Get(ThemeProfile.ThemeLocalSettingsKeyName);

            if (jsonString != null)
            {
                List<ThemeData> themeData = JsonConvert.DeserializeObject<List<ThemeData>>(jsonString);
                if (themeData != null && themeData.Count > 0)
                {
                    ThemeProfile lightTheme = new ThemeProfile() { ColorScheme = ColorScheme.Light };
                    ThemeProfile darkTheme = new ThemeProfile() { ColorScheme = ColorScheme.Dark };

                    foreach (ThemeData item in themeData)
                    {
                        if (item.ThemeName.Equals("default", StringComparison.OrdinalIgnoreCase) ||
                            item.ThemeName.Equals("light", StringComparison.OrdinalIgnoreCase))
                        {
                            lightTheme.AddThemeData(item.Property, item.Value, item.ValueType);
                        }
                        else if (item.ThemeName.Equals("dark", StringComparison.OrdinalIgnoreCase) || item.ThemeName.Equals("working-late", StringComparison.OrdinalIgnoreCase))
                        {
                            darkTheme.AddThemeData(item.Property, item.Value, item.ValueType);
                        }
                    }

                    themeService.SetThemeProfiles(new List<ThemeProfile> { lightTheme, darkTheme });
                }
            }
        }

        protected virtual void OnSetupLogging()
        {
            LoggerManager = Utility.LoggerManager.Instance;
            _container.RegisterLoggerManager(LoggerManager);

            LoggerManager.AddDefaultLoggingFolder();

            LoggerManager.Logger.Log("Application Started", MessageType.Information);
            LoggerManager.Logger.Log("Application process id: " + Process.MyPid(), MessageType.Information);
        }

        protected abstract AppRegistration CreateAppRegistration();

        protected virtual Task OnResolveNavigationAsync(ResolveNavigationEventArgs args)
        {
            if (args.Location == FrameworkLocations.SyncMonitor)
            {
                args.PageType = typeof(SyncMonitor);
            }
            else if (args.Location == FrameworkLocations.Login)
            {
                args.PageType = typeof(Login);
            }
            else if (args.Location == FrameworkLocations.About)
            {
                args.PageType = typeof(About);
            }
            else if (args.Location == FrameworkLocations.AppHome ||
                     args.Location == FrameworkLocations.Home)
            {
                args.PageType = typeof(Home);
            }
            else if (args.Location == FrameworkLocations.Settings)
            {
                args.PageType = typeof(Settings);
            }
            else if (args.Location == FrameworkLocations.DatabaseViewer)
            {
                args.PageType = typeof(DatabaseTables);
            }
            else if (args.Location == FrameworkLocations.DatabaseViewerRow)
            {
                args.PageType = typeof(DatabaseTablesRowDetail);
            }
            else if (args.Location == FrameworkLocations.Attachments)
            {
                args.PageType = typeof(AttachmentsActivity);
            }
            else if (args.Location == FrameworkLocations.MediaDetails)
            {
                args.PageType = typeof(MediaDetailsActivity);
            }
            else if (args.Location == FrameworkLocations.DocumentDetails)
            {
                args.PageType = typeof(DocumentDetailsActivity);
            }
            else if (args.Location == FrameworkLocations.BugReporter)
            {
                IBugReporter br = AppUnityContainer.Resolve<ITouchApp>().Resolve<IBugReporter>();
                br.ShowDialog();
            }
            else if (args.Location == FrameworkLocations.LogCleaner)
            {
                IBugReporter br = AppUnityContainer.Resolve<ITouchApp>().Resolve<IBugReporter>();
                br.DeleteLogFiles();
            }
#if REMOTE_ASSISTANCE
            else if (args.Location == FrameworkLocations.RemoteAssistance)
            {
                args.PageType = typeof(Uma.RemoteAssistance.RemoteAssistanceTabActivity);
            }
#endif
            else if (args.Location == FrameworkLocations.MetadataPage)
            {
                args.PageType = typeof(MetadataPageActivity);
            }
            else if (args.Location == FrameworkLocations.Maps)
            {
                MapNavParam navParam = NavigationParameter.FromNavigationParameter<MapNavParam>(args.Parameter);
                AppLauncher.OpenMap(ApplicationContext.ApplicationContext, navParam.Address, navParam.Latitude, navParam.Longitude);
            }
            return Task.FromResult(true);
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            LoggerManager?.Logger?.HandleUnhandledException(e);
        }

        public Task<ChangePasswordDialogResult> ShowChangePasswordDialogAsync(string message)
        {
            return ChangePasswordDialog.Show(AppUnityContainer.Resolve<ITouchApp>());
        }

        public Task<string> ShowGetPinCodeDialogAsync(string message, bool reenterPin = false)
        {
            return OfflinePinCodeDialog.Show(message);
        }

        private class SystemNavigator : ISystemNavigator
        {
            private readonly INavigator _navigator;

            public SystemNavigator(INavigator navigator)
            {
                _navigator = navigator;
            }

            public Task<bool> NavigateToLoginAsync()
            {
                CalendarElementView.CalendarSettings.Set(CalendarElement.CalendarLastViewedDate, string.Empty);
                return _navigator.NavigateToRootAsync(FrameworkLocations.Login, null);
            }

            public Task<bool> NavigateToSyncMonitorAsync()
            {
                CalendarElement.ResetCalendarSettings(CalendarElementView.CalendarSettings);
                return _navigator.NavigateToRootAsync(FrameworkLocations.SyncMonitor, null);
            }

            public async Task<bool> NavigateToAppHomeAsync()
            {
                bool result = await _navigator.NavigateToRootAsync(FrameworkLocations.AppHome, null);
#if PUSH_NOTIFICATION
                await RequestNotificationPermission();
#endif
                await RequestLocationPermission();
                return result;
            }

            public async Task NavigateToComplete()
            {
                await RequestNotificationPermission();
            }

            public Task<bool> NavigateToAboutAsync()
            {
                return _navigator.NavigateToAsync(FrameworkLocations.About, null);
            }

            private async Task RequestLocationPermission()
            {
                Resolver.TryResolve(out IAppParameters appParams);

                string locationPermission = Login.LocationPermissionSettings.Get(SettingsExtensions.LocationPermissionDenied);
                if (appParams.GetLocationEnabled() && !(appParams.GetLocationOnDemandMode().Equals(LocationMode.Off) && appParams.GetLocationTrackingMode().Equals(LocationMode.Off)) && Build.VERSION.SdkInt >= BuildVersionCodes.M && ContextCompat.CheckSelfPermission(Application.Context, Manifest.Permission.AccessFineLocation) != Permission.Granted && locationPermission.Equals(LocationPermission.LocationPermissionNotYetGiven.ToString()))
                {
                    Resolver.TryResolve(out ITouchApp app);
                    string locationPermissionRequest = string.Format(Strings.LocationRequest, app.DisplayName);
                    string backgroundPermissionRequest = Strings.BackgroundLocationDisclosure;

                    Resolver.TryResolve(out IDialogService dialogService);
                    CustomButtonsResult result = await dialogService.CustomButtons(locationPermissionRequest, backgroundPermissionRequest, Strings.Yes, Strings.No);
                    if (result == CustomButtonsResult.Positive)
                    {
                        Resolver.TryResolve(out LocationService locationService);
                        LocationMode desiredMode = appParams.GetLocationTrackingMode();
                        TimeSpan? trackingInterval = appParams.GetLocationTrackingInterval();

                        if (trackingInterval.Value > TimeSpan.Zero)
                        {
                            await locationService.StartTracking(trackingInterval.Value, true, desiredMode);
                        }

                        Login.LocationPermissionSettings.Set(SettingsExtensions.LocationPermissionDenied, LocationPermission.LocationPermissionAllowed.ToString());
                    }
                    else
                    {
                        Login.LocationPermissionSettings.Set(SettingsExtensions.LocationPermissionDenied, LocationPermission.LocationPermissionDenied.ToString());
                    }
                }
            }

            private async Task RequestNotificationPermission()
            {
                if (Build.VERSION.SdkInt >= Android.OS.BuildVersionCodes.Tiramisu && IfsApplication.Context.CheckSelfPermission(Manifest.Permission.PostNotifications) == Permission.Denied)
                {
                    Resolver.TryResolve(out ITouchApp app);

                    string notificationPermissionRequest = string.Format(Strings.NotificationPermissionRequired, app.DisplayName);
                    string notificationPermissionRequestMessage = Strings.NotificationPermissionRequiredMessage;

                    Resolver.TryResolve(out IDialogService dialogService);
                    CustomButtonsResult result = await dialogService.CustomButtons(notificationPermissionRequest, notificationPermissionRequestMessage, Strings.Yes, Strings.No);
                    
                    if (result == CustomButtonsResult.Positive)
                    {
                        Resolver.TryResolve(out IPushNotificationService pushNotificationService);

                        if (pushNotificationService != null)
                        {
                            await ((PushNotificationService)pushNotificationService).RequestPushNotificationPermissionIfRequired(true);
                        }
                    }
                }
            }
        }
    }
}
