{"name": "FndTstOffline", "version": "1706901162:1948287535", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "enumerations": {}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}, "CustomerType": {"datatype": "Text", "keygeneration": "User"}}, "references": {"CustomerTypeRef": {"target": "TstCustomerType", "mapping": {"CustomerType": "TypeId"}}}, "arrays": {}, "actions": {}, "functions": {}}, "TstCustomerType": {"name": "TstCustomerType", "hasETag": true, "CRUD": "Read", "luname": "TstCustomerType", "ludependencies": ["TstCustomerType"], "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text", "keygeneration": "User"}, "TypeDescription": {"datatype": "Text", "keygeneration": "User"}}}, "OfflineQueryBasic": {"name": "OfflineQueryBasic", "CRUD": "Read", "luname": "OfflineQueryBasic", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "c"}, "select": {"columns": [{"name": "c.<PERSON><PERSON><PERSON><PERSON>"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}, "OfflineQueryFull": {"name": "OfflineQueryFull", "CRUD": "Read", "luname": "OfflineQueryFull", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "c"}, "joins": [{"type": "Inner", "entity": "TstCustomerType", "alias": "ct", "on": {"==": [{"var": "ct.TypeId"}, {"var": "c.CustomerType"}]}}], "where": {"==": [{"var": "ct.TypeId"}, "TEST"]}, "select": {"distinct": true, "columns": [{"name": "ct.TypeId"}, {"name": "c.CustomerTypeRef.TypeDescription", "as": "Description"}]}}, "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text"}, "Description": {"datatype": "Text"}}}, "OfflineQueryUnion": {"name": "OfflineQueryUnion", "CRUD": "Read", "luname": "OfflineQueryUnion", "syncpolicy": {"type": "None"}, "OfflineQuery": {"union": [{"entity": "TstCustomer", "alias": "wo"}, {"entity": "TstCustomerType", "alias": "co"}], "select": {"distinct": true, "columns": [{"name": "wo.CustomerNo"}, {"name": "co.TypeId"}]}}, "attributes": {"CustomerNo": {"datatype": "Text"}, "TypeId": {"datatype": "Text"}}}, "structures": {}, "actions": {}, "functions": {}}, "component": "FNDTST", "layout": {"lists": {}, "cards": {}, "selectors": {}, "pages": {}, "groups": {}, "menus": {}, "commands": {}}}}