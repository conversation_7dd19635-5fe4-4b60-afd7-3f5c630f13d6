Write-Output "============ Update Version"

$buildNumber = "0"
if (Test-Path Env:BUILD_NUMBER)
{
    $buildNumber = $(Get-ChildItem Env:BUILD_NUMBER).Value
}

$appVersionShortNumber = $appVersionMajorMinor + "." + $buildNumber

$appVersion =  $appVersionMajorMinor + "." + $buildNumber + "." + $appVersionRevision

$appVersion | Out-File ($deliverablesDir + "Version.txt")

$assemblyInfoFile = "$($solutionDir)UmaAssemblyInfo.cs"
$assemblyInfo = (Get-Content $assemblyInfoFile)
$assemblyInfo = $assemblyInfo -replace "AssemblyVersion\(`"[0-9\.]*`"\)", "AssemblyVersion(`"$appVersion`")"
$assemblyInfo | Set-Content $assemblyInfoFile

$global:buildNumber = $buildNumber
$global:appVersion = $appVersion
$global:appVersionShortNumber = $appVersionShortNumber

Write-Output ("==== Version - " + $appVersion)
Write-Output ("==== Short Version for iOS - " + $appVersionShortNumber)

Write-Output "============ End - Update Version"

Exit 0