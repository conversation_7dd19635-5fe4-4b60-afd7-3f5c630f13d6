<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
  <Ifs.Uma.UI.Controls.UmaTabPager xmlns:app="http://schemas.android.com/apk/res-auto"
      android:id="@+id/assistant_view_pager"
      android:layout_width="match_parent"
      android:layout_above="@+id/divider"
      android:layout_height="match_parent"
      android:layout_alignWithParentIfMissing="true" />
  <View
      android:id="@+id/divider"
      android:background="?android:attr/dividerHorizontal"
      android:layout_height="1dp"
      android:layout_above="@+id/button_panel"
      android:layout_width="match_parent" />
  <LinearLayout
      style="?android:attr/buttonBarStyle"
      android:id="@+id/button_panel"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_alignParentBottom="true"
      android:orientation="horizontal">
    <Button
        style="?android:attr/buttonBarButtonStyle"
        android:background="?android:attr/selectableItemBackground"
        android:id="@+id/prev_button"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:text="Previous" />
    <Button
        style="?android:attr/buttonBarButtonStyle"
        android:background="?android:attr/selectableItemBackground"
        android:id="@+id/next_button"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:text="Next" />
    <Button
        style="?android:attr/buttonBarButtonStyle"
        android:background="?android:attr/selectableItemBackground"
        android:id="@+id/done_button"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:text="Finish" />
    <Ifs.Uma.Framework.UI.Commands.CommandBlockView
        android:id="@+id/assistant_commands"
        android:orientation="horizontal"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:gravity="end"/>
  </LinearLayout>
</RelativeLayout>
