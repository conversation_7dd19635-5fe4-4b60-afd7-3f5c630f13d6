﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringToUpper : StringFunction
    {
        public const string FunctionName = "ToUpper";

        public StringToUpper()
            : base(FunctionName, 1, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify) => stringToModify.ToUpperInvariant();
    }
}
