﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData.Database.Expressions
{
    internal sealed class QueryRetargeter : IfsExpressionVisitor
    {
        private readonly QueryScope _scope;

        public static Expression Rewrite(Expression expression, QueryScope scope)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));
            if (scope == null) throw new ArgumentNullException(nameof(scope));

            IfsExpressionVisitor visitor = new QueryRetargeter(scope);
            return visitor.Visit(expression);
        }

        private QueryRetargeter(QueryScope scope)
        {
            _scope = scope;
        }

        protected internal override Expression VisitVarAccessExpression(VarAccessExpression exp)
        {
            ISelectColumnSpec selectColumnSpec = _scope.GetColumnSpec(exp.PropertyPath);

            if (selectColumnSpec == null)
            {
                throw new InvalidMetadataException($"Query '{_scope.Name}' failed to resolve attribute '{exp.PropertyPath}'");
            }

            return IfsExpression.DbColumnSpec(selectColumnSpec);
        }

        protected internal override Expression VisitAttributeAccessExpression(AttributeAccessExpression exp)
        {
            ISelectColumnSpec selectColumnSpec = _scope.GetColumnSpec(exp.Attribute);

            if (selectColumnSpec == null)
            {
                throw new InvalidMetadataException($"Query '{_scope.Name}' failed to resolve attribute '{exp.Attribute.Path}'");
            }

            return IfsExpression.DbColumnSpec(selectColumnSpec);
        }

        protected internal override Expression VisitQueryExpression(QueryExpression exp)
        {
            ISelectSpec selectSpec = QuerySelectSpec.Create(exp, _scope.CreateChild());
            return IfsExpression.DbSelectSpec(selectSpec);
        }
    }
}
