﻿using System;

namespace Ifs.Uma.Framework.UI.Lookups
{
    public class QuickItemSettings
    {
        public QuickItemOptions Options { get; private set; }

        private QuickItemSettings()
        {
        }

        public static QuickItemSettings Create(QuickItemOptions options)
        {
            return new QuickItemSettings()
            {
                Options = options,
            };
        }
    }

    /// <summary>
    /// Defines which Quick Item features are enabled in the Lookup
    /// </summary>
    [Flags]
    public enum QuickItemOptions
    {
        /// <summary>No Quick Item features will be enabled</summary>
        None = 0,
        /// <summary>Favorites functionality is available</summary>
        Favorites = 1,
        /// <summary>Recently Used items functionality is available</summary>
        RecentlyUsed = 2,
        /// <summary>Favorites and Recently Used items are available</summary>
        FavoritesAndRecentlyUsed = Favorites | RecentlyUsed
    }
}
