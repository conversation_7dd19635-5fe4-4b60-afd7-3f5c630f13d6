﻿using System;
using System.IO;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData.Database
{
    public interface IDatabaseController : IDataContextProvider
    {
        bool DoesDatabaseExist(int id);

        void CreateDatabaseForUser(int id, string userName, string password, MetadataBlob metadataBlob = null);
        void CreateAndLoginDatabaseFromData(int id, string exportData, out string userName);
        bool Login(int id, string userName, string password);
        void DeleteDatabase(int id);

        int ConnectedDatabaseId { get; }
        void ChangePassword(string newPassword);
        bool ValidatePassword(string password);
        Task<MetadataDifference> UpdateMetadata(MetadataBlob metadataBlob, Func<string, Task<MetadataBlob>> getClientMetaData, MetadataBlob navEntries);
        IMetadata GetMetadata();
        void ExportDataToStream(Stream stream);
        void Disconnect();

        bool TraceFlag { get; set; }
    }
}
