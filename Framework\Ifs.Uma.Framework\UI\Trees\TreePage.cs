﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Framework.UI.Trees.Nodes;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;
using Unity.Attributes;
using static Ifs.Uma.Services.Parameters.AppParametersExtensions;

namespace Ifs.Uma.Framework.UI.Trees
{
    public class TreePage : PageBase
    {
        private readonly IMetadata _metadata;
        private readonly IEventAggregator _eventAggregator;
        private readonly IDialogService _dialogService;
        private readonly IExpressionRunner _expressionRunner;
        private readonly IDataHandler _dataHandler;
        private readonly ILogger _logger;
        private readonly IOfflineChecker _offlineChecker;
        private readonly IRoamingProfile _roamingProfile;
        private readonly IInsightsLogger _insightsLogger;
        private readonly IPerfLogger _perfLogger;
        private IPageCreator _pageCreator;

        public TreeStructureMainAction MainAction { get; }

        public PageData PageData { get; }

        private ElementPage _contentPage;

        public ElementPage ContentPage
        {
            get => _contentPage;
            set
            {
                SetProperty(ref _contentPage, value);
            }
        }

        private CpiTree _tree;

        private CpiPage PageForTree
        {
            get
            {
                return _metadata.FindPage(ProjectionName, _tree.Name);
            }
        }

        private CpiTreeNode RootCpiTreeNode
        {
            get
            {
                if (_tree == null || _tree.Nodes == null)
                {
                    return null;
                }
                return _tree.Nodes.FirstOrDefault(x => x.Node != null && x.Node.Root)?.Node;
            }
        }

        private string _dialogTitle;

        public string DialogTitle
        {
            get { return _dialogTitle; }
            set { SetProperty(ref _dialogTitle, value); }
        }

        private bool _isOfflineWarningVisible;

        public bool IsOfflineWarningVisible
        {
            get { return _isOfflineWarningVisible; }
            set { SetProperty(ref _isOfflineWarningVisible, value); }
        }

        public UmaColor OfflineWarningEmphasisColorForeground
        {
            get
            {
                return UmaColors.Warning.Tint(UmaColors.Black, 0.5);
            }
        }

        public UmaColor OfflineWarningEmphasisColorBackground
        {
            get
            {
                return UmaColors.Warning.Tint(UmaColors.White, 0.8);
            }
        }

        private bool _isLoading;

        public bool IsLoading
        {
            get { return _isLoading; }
            set { SetProperty(ref _isLoading, value); }
        }

        private EventHandler _contentPageRecordChangedHandler;

        /// <summary>
        /// This event will be triggered when the platform-specific subclass of <see cref="Tree"/> should be set to null.
        /// </summary>
        public event EventHandler ClearTreeDataSource;

        /// <summary>
        /// Returns true if the root node of the tree contains a connection to itself.
        /// This is a special case and means the tree is recursive and should be treated differently when the tree page is navigated to with a filter.
        /// </summary>
        private bool IsRecursiveTree
        {
            get
            {
                return RootCpiTreeNode.Connections.Any(x => x.Connection?.Binding?.Property == RootCpiTreeNode.Name);
            }
        }

        public ViewableCollection<Node> BreadCrumbs { get; } = new ViewableCollection<Node>();

        private bool _breadCrumbsVisible;

        public bool BreadCrumbsVisible
        {
            get { return _breadCrumbsVisible; }
            private set { SetProperty(ref _breadCrumbsVisible, value); }
        }

        public ViewableCollection<Node> Ancestors { get; } = new ViewableCollection<Node>();

        private bool _ancestorsVisible;

        public bool AncestorsVisible
        {
            get { return _ancestorsVisible; }
            private set { SetProperty(ref _ancestorsVisible, value); }
        }

        private RootNode _rootNode;

        private Node _currentRootNode;

        public Node RootNode
        {
            get
            {
                if (_currentRootNode != null)
                {
                    return _currentRootNode;
                }
                else if (_rootNode == null)
                {
                    InitializeRootNode();
                }

                return _rootNode;
            }
            set
            {
                if (_currentRootNode == null)
                {
                    _rootNode.IsRoot = false;
                }
                else
                {
                    _currentRootNode.IsRoot = false;
                }

                _currentRootNode = value;
                _currentRootNode.IsRoot = true;
                UpdateBreadCrumbs();
                RootNodeChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        private Node _selectedNode;

        public Node SelectedNode
        {
            get => _selectedNode;
            set => SetProperty(ref _selectedNode, value, OnSelectedNodeChanged);
        }

        public event EventHandler RootNodeChanged;

        private bool _initialLoad = true;

        private TreeState _state;

        public bool OpenSelector { get; private set; }

        /// <summary>
        /// This is used to keep track of which node is being expanded, either automatically or by a user action (promotes node by selecting it or presses the expand button).
        /// Doing this will allow us to prevent the children of that node from being loaded, if required.
        /// </summary>
        public Node NodeToExpand { get; set; }

        /// <summary>
        /// Contains the primary key of the record that is filtered in the tree page, when navigated to the page with a filter.
        /// </summary>
        public ObjPrimaryKey FilteredRecordKey { get; private set; }

        private Dictionary<string, string> _lastVisibleNodes = new Dictionary<string, string>();

        public TreePage(IEventAggregator eventAggregator, IDialogService dialogService, IMetadata metadata, ILogger logger, IPerfLogger perfLogger, IInsightsLogger insightsLogger,
            IDataHandler dataHandler, IExpressionRunner expressionRunner, IOfflineChecker offlineChecker, IRoamingProfile roamingProfile, IAppParameters appParameters)
            : base(eventAggregator, dialogService)
        {
            _metadata = metadata;
            _eventAggregator = eventAggregator;
            _dialogService = dialogService;
            _logger = logger;
            _expressionRunner = expressionRunner;
            _dataHandler = dataHandler;
            _offlineChecker = offlineChecker;
            _insightsLogger = insightsLogger;
            _perfLogger = perfLogger;
            _roamingProfile = roamingProfile;

            Classification = PageClassification.Tree;

            MainAction = appParameters.GetTreeStructureMainAction();

            PageData = new PageData(_logger, _metadata, _dataHandler);
            PageData.Page = this;
            PageData.DefaultViewData.Record.PropertyChanged += Data_PropertyChanged;
        }

        [InjectionMethod]
        public void Initialize(PageCreator pageCreator)
        {
            _pageCreator = pageCreator;
        }

        private void InitializeRootNode()
        {
            IsOfflineWarningVisible = false;
            IsLoading = false;

            EntityQuery query = new EntityQuery(PageData.DataSource);
            query.SelectAttributes = Node.GetNodeAttributes(ProjectionName, RootCpiTreeNode);

            PageData.Filter?.Apply(query);

            NodeContext context = new NodeContext(ProjectionName, PageData, _tree, _metadata, _dataHandler, _expressionRunner, _logger, _eventAggregator, _dialogService);

            _rootNode = new RootNode(context, RootCpiTreeNode, query, MainAction);
            _rootNode.LoadingState.IsAnythingUpdatingChanged += IsAnythingUpdatingChanged;

            SelectedNode = _rootNode;

            UpdateBreadCrumbs();

            RootNodeChanged?.Invoke(null, new InitializedRootNode(false, _initialLoad));

            _initialLoad = false;
        }

        private async Task InitializeRootNode(PageValues filter)
        {
            IsOfflineWarningVisible = false;
            IsLoading = false;

            EntityDataSource rootNodeEntityDataSource = EntityDataSource.FromEntity(_metadata, ProjectionName, RootCpiTreeNode.Entity);

            EntityQuery query = new EntityQuery(rootNodeEntityDataSource);

            HashSet<string> selectAttributes = Node.GetNodeAttributes(ProjectionName, RootCpiTreeNode);
            CpiArray array = _metadata.FindArray(ProjectionName, RootCpiTreeNode.Entity, RootCpiTreeNode.Connections[0].Connection.Binding.BindName);

            foreach (KeyValuePair<string, string> item in array.Mapping)
            {
                selectAttributes.Add(item.Value);
            }

            query.SelectAttributes = selectAttributes;

            filter.Apply(query);

            EntityRecord record = await _dataHandler.GetRecordAsync(query, CancellationToken.None);
            FilteredRecordKey = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, record?.Row);

            IList<EntityRecord> breadcrumbRecords = await GetBreadcrumbRecords(record, rootNodeEntityDataSource);

            NodeContext context = new NodeContext(ProjectionName, PageData, _tree, _metadata, _dataHandler, _expressionRunner, _logger, _eventAggregator, _dialogService);

            _rootNode = new RootNode(context, RootCpiTreeNode, breadcrumbRecords, MainAction);
            _rootNode.LoadingState.IsAnythingUpdatingChanged += IsAnythingUpdatingChanged;

            SelectedNode = _rootNode;

            UpdateBreadCrumbs();

            RootNodeChanged?.Invoke(null, new InitializedRootNode(true, _initialLoad));
            _initialLoad = false;
        }

        public class InitializedRootNode : EventArgs
        {
            public bool WithFilter { get; }

            public bool InitialLoad { get; }

            public InitializedRootNode(bool withFilter, bool initialLoad)
            {
                WithFilter = withFilter;
                InitialLoad = initialLoad;
            }
        }

        public static async Task LoadNodesAndPageWithFilter(TreePage page, Tree tree)
        {
            await tree.LoadMoreNodes();

            if (tree.RootNode.TreeChildren.Count == 1)
            {
                await SetRootNodeAndPage(page, tree, tree.RootNode.TreeChildren[0]);
            }
            else
            {
                // Find the first node that's preceded by a shell node, this will be the one we navigated to
                for (int i = 0; i < tree.RootNode.TreeChildren.Count; i++)
                {
                    if ((i == 0 || tree.RootNode.TreeChildren[i - 1].GetType() == typeof(ShellNode)) && tree.RootNode.TreeChildren[i].GetType() == typeof(Node))
                    {
                        await SetRootNodeAndPage(page, tree, tree.RootNode.TreeChildren[i]);
                        break;
                    }
                }
            }
        }

        private static async Task SetRootNodeAndPage(TreePage page, Tree tree, Node rootNode)
        {
            page.RootNode = rootNode;
            tree.RootNode = page.RootNode;
            page.SelectedNode = page.RootNode;
            await tree.LoadMoreNodes();

            // Ensure the current root node is something that can be loaded as a page
            if (page.RootNode is Node)
            {
                await page.LoadElementPage(page.RootNode);
            }
        }

        private async Task<IList<EntityRecord>> GetBreadcrumbRecords(EntityRecord record, EntityDataSource entityDataSource)
        {
            List<EntityRecord> records = new List<EntityRecord> { record };
            List<string> ids = new List<string> { record.Row?.GetRowIdentifier(_metadata.MetaModel) };
            do
            {
                record = await GetParentRecord(record, entityDataSource);
                if (record != null)
                {
                    string id = record.Row?.GetRowIdentifier(_metadata.MetaModel);
                    if (ids.Contains(id))
                    {
                        // A loop exists and must be broken
                        break;
                    }
                    else
                    {
                        records.Add(record);
                        ids.Add(id);
                    }
                }
            }
            while (record != null);

            records.Reverse();
            return records;
        }

        private Task<EntityRecord> GetParentRecord(EntityRecord childRecord, EntityDataSource entityDataSource)
        {
            EntityQuery query = new EntityQuery(entityDataSource);

            HashSet<string> selectAttributes = Node.GetNodeAttributes(ProjectionName, RootCpiTreeNode);

            CpiArray array = _metadata.FindArray(entityDataSource.ProjectionName ?? ProjectionName, RootCpiTreeNode.Entity, RootCpiTreeNode.Connections[0].Connection.Binding.BindName);

            foreach (KeyValuePair<string, string> item in array.Mapping)
            {
                query.AddFilter(item.Key, childRecord.Row[item.Value]);
                selectAttributes.Add(item.Value);
            }

            query.SelectAttributes = selectAttributes;

            return _dataHandler.GetRecordAsync(query, CancellationToken.None);
        }

        public override async void OnNavigatedTo(NavigatedToArgs args)
        {
            base.OnNavigatedTo(args);

            if (args?.NavigationMode != NavigationMode.Back)
            {
                MetadataPageNavParam param = args?.GetNavParam<MetadataPageNavParam>();
                await LoadPageAsync(param);

                if (args?.NavigationMode == NavigationMode.New && RootNode != null && RootNode is Node)
                {
                    await LoadElementPage(RootNode, true);
                }
            }
        }

        public async Task LoadElementPage(Node node, bool newRecord = false)
        {
            if (ContentPage?.PageData?.DefaultViewData?.Record != null && _contentPageRecordChangedHandler != null)
            {
                ContentPage.PageData.DefaultViewData.Record.RecordLoaded -= _contentPageRecordChangedHandler;
                _contentPageRecordChangedHandler = null;
            }

            if (_tree == null)
            {
                ContentPage = null;
                UpdatePageTitle();
                return;
            }

            ContentPage = null;
            UpdatePageTitle(node.Title);

            PageValues filters = null;
            CpiTreeNavigateContainer treeNavigate = null;
            if (node.ViewData?.Record != null)
            {
                foreach (CpiTreeNavigateContainer treeNavigateContainer in node.CpiTreeNode?.Navigate ?? Enumerable.Empty<CpiTreeNavigateContainer>())
                {
                    if ((treeNavigateContainer.Navigate != null && _expressionRunner.RunCheck(treeNavigateContainer.Expression, node.ViewData, false)) || treeNavigateContainer.Default)
                    {
                        filters = PageValues.FromRecordData(treeNavigateContainer.Navigate.Filters, node.ViewData?.Record);
                        treeNavigate = treeNavigateContainer;
                        break;
                    }
                }
            }
            else
            {
                treeNavigate = node.CpiTreeNode?.Navigate?.First();
            }

            MetadataPageNavParam navParam = new MetadataPageNavParam(ProjectionName, treeNavigate?.Navigate?.PageName, filters);

            if (_pageCreator.CreatePage(navParam) is ElementPage page)
            {
                _state = new TreeState(page.PageData.ViewState, _tree.Name);
                _state.IsTreeOpened = true;

                if (newRecord)
                {
                    await page.LoadPageWithNewRecordAsync(navParam);
                }
                else
                {
                    await page.LoadPageAsync(navParam);
                }

                page.IsSubPage = true;
                ContentPage = page;

                if (ContentPage?.PageData?.DefaultViewData?.Record != null)
                {
                    _contentPageRecordChangedHandler = async (o, e) => await ContentPageRecordChanged(node);
                    ContentPage.PageData.DefaultViewData.Record.RecordLoaded += _contentPageRecordChangedHandler;
                }
            }
        }

        private async Task ContentPageRecordChanged(Node node)
        {
            // This is going to get called when a form has changes, this is not ideal as a user could cancel those changes but the tree will have already been reloaded. In the future this should only be called when the record has the changes saved.
            if (_rootNode != null)
            {
                _rootNode.LoadingState.IsAnythingUpdatingChanged -= IsAnythingUpdatingChanged;
            }
            _rootNode = null;
            _currentRootNode = null;
            await node.RefreshData();
            ClearTreeDataSource?.Invoke(this, EventArgs.Empty);
            InitializeRootNode();

            if (node != null)
            {
                SelectedNode = node;
                UpdatePageTitle(node?.Title);
            }

            DialogTitle = GetTreeTitle();
        }

        private void IsAnythingUpdatingChanged(object sender, EventArgs e)
        {
            IsLoading = _rootNode?.LoadingState.IsAnythingUpdating ?? false;
        }

        private EntityDataSource GetDataSource(PageValues pageFilter)
        {
            if (_tree.DatasourceFunction != null)
            {
                Dictionary<string, object> parameterValues = new Dictionary<string, object>();
                pageFilter?.ExtractFunctionParameters(_tree.DatasourceFunctionParams, parameterValues);
                return FunctionDataSource.Create(_metadata, _tree.DatasourceProjection ?? ProjectionName, _tree.DatasourceFunction, parameterValues);
            }

            return EntityDataSource.FromEntitySet(_metadata, _tree.DatasourceProjection ?? ProjectionName, _tree.DatasourceEntitySet);
        }

        private void UpdatePageTitle(string title = null)
        {
            if (!string.IsNullOrWhiteSpace(title) && !(SelectedNode is RootNode))
            {
                Title = title;
                return;
            }

            Title = GetTreeTitle();
        }

        private string GetTreeTitle()
        {
            string newTitle = null;
            if (RecordData.HasLoadedExistingRecord(PageData.DefaultViewData.Record))
            {
                newTitle = _expressionRunner.InterpolateString(_tree?.Label, PageData.DefaultViewData.Record);
            }

            if (string.IsNullOrEmpty(newTitle))
            {
                newTitle = PageForTree?.Label;
            }
            return newTitle;
        }

        private void Data_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(HasChanges))
            {
                HasChanges = PageData.DefaultViewData.Record.HasChanges;
            }
        }

        private void OnSelectedNodeChanged(Node oldValue, Node newValue)
        {
            if (oldValue != null)
            {
                oldValue.IsSelected = false;
            }

            if (newValue != null)
            {
                newValue.IsSelected = true;
            }
        }

        private void UpdateBreadCrumbs()
        {
            BreadCrumbsVisible = false;
            AncestorsVisible = false;
            BreadCrumbs.Clear();
            Ancestors.Clear();
            if (_currentRootNode == null)
            {
                return;
            }
            if (!(_currentRootNode is RootNode))
            {
                BreadCrumbsVisible = true;
                IEnumerable<Node> ancestors = _currentRootNode.GetFamilyTree();
                IEnumerable<Node> breadcrumbs = ancestors.Take(Node.DefaultMaxDepth);
                breadcrumbs = breadcrumbs.Reverse();
                int breadcrumbDepth = 1;
                foreach (Node node in breadcrumbs)
                {
                    if (IsRecursiveTree && FilteredRecordKey != null && node is RootNode)
                    {
                        continue;
                    }

                    node.BreadcrumbDepth = breadcrumbDepth;
                    breadcrumbDepth++;
                    BreadCrumbs.Add(node);
                }

                ancestors = ancestors.Skip(Node.DefaultMaxDepth);
                ancestors = ancestors.Reverse();
                foreach (Node node in ancestors)
                {
                    node.BreadcrumbDepth = 1;
                    Ancestors.Add(node);
                }
                AncestorsVisible = Ancestors.Any();
            }
        }

        public async Task UpdateOfflineMessage()
        {
            IsOfflineWarningVisible = await _offlineChecker.WillFailAsOffline(ProjectionName, _tree, null);
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter parameter)
        {
            MetadataPageNavParam navParam = parameter as MetadataPageNavParam;
            if (navParam == null)
            {
                return false;
            }

            try
            {
                ProjectionName = navParam.ProjectionName;
                _tree = navParam.Page == null ? null : _metadata.FindTree(ProjectionName, navParam.Page);

                OpenSelector = navParam.OpenSelector;

                if (_tree != null)
                {
                    using (_perfLogger.Track("PageLoad", ProjectionName + "." + _tree.Name))
                    {
                        _insightsLogger?.TrackAppFeature("Tree-" + ProjectionName + "." + navParam.Page);
                        Logger.Trace("Navigate: Tree" + ProjectionName + "." + navParam.Page);

                        Name = _tree.Name;
                        EntityDataSource dataSource = GetDataSource(navParam.Filter);

                        PageData.PageSettings = _roamingProfile.GetSettings("Trees", ProjectionName, _tree.Name);
                        PageData.Filter = navParam.Filter;
                        PageData.DataSource = dataSource;

                        PageData.SelectAttributes = _tree.SelectAttributes ?? new string[0];

                        UpdatePageTitle();
                        DialogTitle = GetTreeTitle();

                        if (IsRecursiveTree && navParam.Filter != null)
                        {
                            await InitializeRootNode(navParam.Filter);
                        }
                        else
                        {
                            InitializeRootNode();
                        }

                        await UpdateOfflineMessage();
                    }
                }
                else
                {
                    Logger.Error($"Could not find tree {ProjectionName}.{navParam.Page}");
                }

                return true;
            }
            catch (Exception ex)
            {
                await HandleException(ex);
            }

            return false;
        }

        public void SetLastVisibleNodeId(string lastVisibleNodeId)
        {
            _lastVisibleNodes[RootNode.Id] = lastVisibleNodeId;
        }

        public string GetLastVisibleNodeId()
        {
            if (_lastVisibleNodes.ContainsKey(RootNode.Id))
            {
                return _lastVisibleNodes[RootNode.Id];
            }

            return null;
        }

        public void ClearLastVisibleNodeIds()
        {
            _lastVisibleNodes = new Dictionary<string, string>();
        }
    }
}
