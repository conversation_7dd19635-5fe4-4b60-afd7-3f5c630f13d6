{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_RegexReplace>": {"name": "String_RegexReplace", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "RegexPattern", "dataType": "Text"}, {"name": "ReplacementString", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "String"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexReplace", "paramsArray": ["${TextInput}", "${RegexPattern}", "${ReplacementString}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<String_RegexReplace4>": {"name": "String_RegexReplace", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "RegexPattern", "dataType": "Text"}, {"name": "ReplacementString", "dataType": "Text"}, {"name": "RegexOptions", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "String"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexReplace", "paramsArray": ["${TextInput}", "${RegexPattern}", "${ReplacementString}", "${RegexOptions}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}