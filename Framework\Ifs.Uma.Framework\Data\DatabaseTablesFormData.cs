﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Model;
using Ifs.Uma.Localization;

namespace Ifs.Uma.Framework.Data
{
    public class DatabaseTablesFormData : FormData
    {
        public const string FormName = "Database Tables";

        private readonly IDatabaseController _dbController;
        private readonly EventHandler<ValueChangedEventArgs> _columnsSelectionChangedEventHandler;
        private readonly EventHandler<ValueChangedEventArgs> _filterByColumnChangedEventHandler;

        private TextField _noDataTextField;
        private TextField _filterByColumn;
        private ComboField _tableComboField;
        private ComboMultiSelectField _columnsComboMultiSelectField;
        private ITable _selectedTable;
        private List<IMetaDataMember> _selectedTableDataMembers;

        private IEnumerable<SelectableItem<IMetaDataMember>> _selectableColumnMeta;

        public IEnumerable<SelectableItem<IMetaDataMember>> SelectableColumnMeta
        {
            get { return _selectableColumnMeta; }
            set
            {
                _selectableColumnMeta = value;
                OnPropertyChanged(() => SelectableColumnMeta);
            }
        }

        private IEnumerable<SelectableItem<IMetaTable>> _selectableTableMeta;

        public IEnumerable<SelectableItem<IMetaTable>> SelectableTableMeta
        {
            get { return _selectableTableMeta; }
            set
            {
                if (_selectableTableMeta != value)
                {
                    _selectableTableMeta = value;
                    OnPropertyChanged(() => SelectableTableMeta);
                }
            }
        }

        private List<SortedDictionary<string, string>> _columnsToDisplay;

        public List<SortedDictionary<string, string>> ColumnsToDisplay
        {
            get { return _columnsToDisplay; }
            set
            {
                if (_columnsToDisplay != value)
                {
                    _columnsToDisplay = value;
                    OnPropertyChanged(() => ColumnsToDisplay);
                }
            }
        }

        private List<Dictionary<string, string>> _allColumns;

        public List<Dictionary<string, string>> AllColumns
        {
            get { return _allColumns; }
            set
            {
                if (_allColumns != value)
                {
                    _allColumns = value;
                    OnPropertyChanged(() => AllColumns);
                }
            }
        }

        private string _filterQuery;

        public string FilterQuery
        {
            get { return _filterQuery; }
            set
            {
                if (_filterQuery != value)
                {
                    _filterQuery = value;
                    OnPropertyChanged(() => FilterQuery);
                }
            }
        }

        public int ColumnsSelectedCount { get; private set; } = 0;

        public DatabaseTablesFormData(IDatabaseController dbController, EventHandler<ValueChangedEventArgs> columnsSelectionChangedEventHandler, EventHandler<ValueChangedEventArgs> filterByColumnChangedEventHandler)
        {
            _dbController = dbController;
            AllColumns = new List<Dictionary<string, string>>();
            ColumnsToDisplay = new List<SortedDictionary<string, string>>();
            _columnsSelectionChangedEventHandler = columnsSelectionChangedEventHandler;
            _filterByColumnChangedEventHandler = filterByColumnChangedEventHandler;

            var tableMetas = _dbController.GetMetaModel().GetTables();
            SelectableTableMeta = tableMetas
                .Where(x => x.TableImplementation != TableImplementation.None)
                .Select(t => SelectableItem<IMetaTable>.Create(t.TableName, t))
                .OrderBy(x => x.Label)
                .ToArray();

            SetupForm();
        }

        protected override Form OnSetupForm()
        {
            Form form = new Form();

            _tableComboField = new ComboField
            {
                Name = Strings.Tables,
                ItemsSource = SelectableTableMeta,
                Value = Strings.SelectTable
            };

            _tableComboField.ValueChanged += TableComboField_OnValueChanged;

            _columnsComboMultiSelectField = new ComboMultiSelectField
            {
                Name = Strings.Columns,
                IsVisible = false
            };

            _columnsComboMultiSelectField.ValueChanged += ColumnsComboMultiSelectField_OnValueChanged;

            _filterByColumn = new TextField
            {
                Name = Strings.KeywordSearch,
                ContentType = ContentType.Text,
                MaxLength = 100,
                IsReadOnly = false,
                IsVisible = false
            };

            _filterByColumn.ValueChanged += _filterByColumnChangedEventHandler;

            _noDataTextField = new TextField
            {
                Value = Strings.NoDataAvailable,
                IsReadOnly = true,
                IsVisible = false
            };

            form.AllFields.Add(_tableComboField);
            form.AllFields.Add(_columnsComboMultiSelectField);
            form.AllFields.Add(_filterByColumn);
            form.AllFields.Add(_noDataTextField);

            FormLayoutData compact = form.GetLayout(Form.LayoutVariants.Compact);
            compact.SetDefaultLayout(new[]
            {
                new[] { _tableComboField.Id },
                new[] { _columnsComboMultiSelectField.Id },
                new[] { _filterByColumn.Id },
                new[] { _noDataTextField.Id }
            });

            FormLayoutData regular = form.GetLayout(Form.LayoutVariants.Regular);
            regular.SetDefaultLayout(new[]
            {
                new[] { _tableComboField.Id, _columnsComboMultiSelectField.Id },
                new[] { _filterByColumn.Id }, new[] { _noDataTextField.Id }
            });

            form.LayoutVariant = Form.LayoutVariants.Compact;
            return form;
        }

        private void ColumnsComboMultiSelectField_OnValueChanged(object sender, ValueChangedEventArgs valueChangedEventArgs)
        {
            var comboMultiSelectField = sender as ComboMultiSelectField;

            int numberOfSelectedColumns = ((comboMultiSelectField.Value as IEnumerable)?.Cast<ISelectableItem<IMetaDataMember>>()).Count();

            if (numberOfSelectedColumns > 0)
            {
                InitFilterByColumnField(true);
            }
            else
            {
                InitFilterByColumnField(false);
            }
            _columnsSelectionChangedEventHandler?.Invoke(sender, valueChangedEventArgs);
        }

        private void TableComboField_OnValueChanged(object sender, ValueChangedEventArgs valueChangedEventArgs)
        {
            ResetForm();

            var metaTable = valueChangedEventArgs.NewValue as IMetaTable;
            _selectedTable = _dbController.CreateDataContext().GetTable(metaTable);

            // Check if selected table has any rows
            if (_selectedTable?.GetEnumerator().MoveNext() == false)
            {
                DisplayNoDataMessage(true);
                return;
            }

            _selectedTableDataMembers = _selectedTable?.Entity.DataMembers.ToList();

            SelectableColumnMeta = _selectedTableDataMembers?
                                    .Select(dm => SelectableItem<IMetaDataMember>.Create(dm.PropertyName, dm))
                                    .ToArray().OrderBy(x => (x.Value.PropertyName));

            if (SelectableColumnMeta == null)
            {
                DisplayNoDataMessage(true);
            }
            else
            {
                InitColumnComboField(true);
                InitFilterByColumnField(false);
            }
            
            _columnsComboMultiSelectField.Value = GetDefaultSelectedColumns();
        }

        private SelectableItem<IMetaDataMember>[] GetDefaultSelectedColumns()
        {
            List<SelectableItem<IMetaDataMember>> items = new List<SelectableItem<IMetaDataMember>>();

            if (SelectableColumnMeta != null)
            {
                foreach (var item in SelectableColumnMeta.Where(x => x.Value.ServerPrimaryKey))
                {
                    items.Add(item);
                }

                if (items.Count == 0)
                {
                    foreach (var item in SelectableColumnMeta.Where(x => x.Value.PrimaryKey))
                    {
                        items.Add(item);
                    }
                }

                foreach (var item in SelectableColumnMeta)
                {
                    bool interestingColumn = item.Value.ColumnName == "description" || item.Value.ColumnName == "name";
                    if (interestingColumn && !items.Contains(item))
                    {
                        items.Add(item);
                    }
                }
            }

            return items.ToArray();
        }

        public List<SortedDictionary<string, string>> FilterDataByKey(string queryText)
        {
            this.FilterQuery = queryText;
            if (!string.IsNullOrEmpty(queryText))
            {
                List<SortedDictionary<string, string>> filteredData = ColumnsToDisplay.Where(item => item.Any(i => i.Value.ToLower().Contains(queryText.ToLower()))).ToList();
                return filteredData;
            }

            return ColumnsToDisplay;
        }

        public async Task LoadRowData(ComboMultiSelectField comboMultiSelectField)
        {
            comboMultiSelectField = comboMultiSelectField ?? _columnsComboMultiSelectField;

            ClearPreviousData();

            IMetaDataMember[] selectedColumnDataMembers =
                (comboMultiSelectField.Value as IEnumerable)?.Cast<ISelectableItem<IMetaDataMember>>().Select(x => x.Value).OrderBy(x => (x.PropertyName)).ToArray();

            if (selectedColumnDataMembers == null || selectedColumnDataMembers.Length == 0) return;

            Task loadColumnDataTask = Task.Run(() =>
            {
                foreach (var row in _selectedTable)
                {
                    if (row == null)
                    {
                        continue;
                    }

                    SortedDictionary<string, string> selectedColumnsValues = new SortedDictionary<string, string>();
                    var allColumnValues = new Dictionary<string, string>();
                    foreach (var dataMember in _selectedTableDataMembers)
                    {
                        var columnValue = dataMember.GetValue(row) ?? "null";

                        allColumnValues.Add(dataMember.PropertyName, columnValue.ToString());

                        if (selectedColumnDataMembers.Contains(dataMember))
                        {
                            selectedColumnsValues.Add(dataMember.PropertyName, columnValue.ToString());
                        }
                    }

                    if (selectedColumnsValues.Any())
                    {
                        ColumnsToDisplay.Add(selectedColumnsValues);
                        AllColumns.Add(allColumnValues);
                    }
                    else if (allColumnValues.Any())
                    {
                        AllColumns.Add(allColumnValues);
                    }
                }
            });

            await loadColumnDataTask;

            if (loadColumnDataTask.IsCompleted && !loadColumnDataTask.IsFaulted)
            {
                ColumnsSelectedCount = selectedColumnDataMembers.Length;
            }
        }

        private void ResetForm()
        {
            ColumnsSelectedCount = 0;
            AllColumns.Clear();
            ColumnsToDisplay.Clear();
            DisplayNoDataMessage(false);
            InitColumnComboField(false);
            InitFilterByColumnField(false);
            ClearFilterQuery();
        }

        private void ClearPreviousData()
        {
            AllColumns.Clear();
            ColumnsToDisplay.Clear();
        }

        private void InitColumnComboField(bool isVisible)
        {
            if (isVisible)
            {
                _columnsComboMultiSelectField.ItemsSource = SelectableColumnMeta;
            }
            else
            {
                _columnsComboMultiSelectField.Value = null;
                _columnsComboMultiSelectField.ItemsSource = new List<ISelectableItem>();
            }
            _columnsComboMultiSelectField.IsVisible = isVisible;
        }

        private void InitFilterByColumnField(bool isVisible)
        {
            _filterByColumn.IsVisible = isVisible;
        }

        private void DisplayNoDataMessage(bool displayMessage)
        {
            _noDataTextField.IsVisible = displayMessage;
        }

        public Dictionary<string, string> GetRowAtPosition(int position)
        {
            if (!string.IsNullOrEmpty(FilterQuery))
            {
                return AllColumns.Where(item => item.Any(i => i.Value.ToLower().Contains(FilterQuery.ToLower()))).ToList()[position];
            }

            return position >= AllColumns.Count ? null : AllColumns[position];
        }

        public void ClearFilterQuery()
        {
            _filterByColumn.Value = string.Empty;
            FilterQuery = string.Empty;
        }
    }
}
