﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;
using static Ifs.Uma.Framework.UI.Cards.CardDefCreator;

namespace Ifs.Uma.Framework.UI.Elements.Calendars
{
    public class CalendarElement : ElementBase
    {
        public const string CalendarLastViewedDate = "LastViewedDate";
        public const string CalendarLastViewedScheduleType = "LastViewedScheduleType";
        public const string TimeFormat = "hh:mm";
        public const string DateFormat = "dd";
        public const string DayFormat = "EEEE";

        private readonly IMetadata _metadata;
        private readonly IDataHandler _data;
        private readonly ILogger _logger;
        private readonly IExpressionRunner _expressionRunner;
        private readonly ICardDefCreator _cardDefCreator;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IOnlineDataHandler _onlineDataHandler;

        private string _entitySetName;
        private const int NumberOfRecords = 1000; // Set a really high number of records to query
        private List<CalendarEvent> _sortedCalendarEvents;

        public ICalendarElement NativeCalendarElement { get; set; }

        private DateTime _workStartHour;
        public DateTime WorkStartHour
        {
            get { return _workStartHour; }
        }

        private DateTime _workEndHour;
        public DateTime WorkEndHour
        {
            get { return _workEndHour; }
        }

        private CpiCalendar _calendar;
        public CpiCalendar Calendar
        {
            get { return _calendar; }
        }

        private CalendarSlotSize _calendarSlotSize;
        public CalendarSlotSize CalendarSlotSize
        {
            get { return _calendarSlotSize; }
        }

        private bool _weeknumbers;
        public bool WeekNumbers
        {
            get { return _weeknumbers; }
        }

        private CpiSchedule _schedule;
        public CpiSchedule Schedule
        {
            get { return _schedule; }
        }

        private CpiCalendarEvent[] _events;
        public CpiCalendarEvent[] Events
        {
            get { return _events; }
        }

        private List<CpiCalendarEvent> _filteredEvents;
        public List<CpiCalendarEvent> FilteredEvents
        {
            get { return _filteredEvents; }
        }

        private readonly List<string> _calendarEventNames = new List<string>();
        public List<string> CalendarEventNames
        {
            get { return _calendarEventNames; }
        }

        private List<CalendarEvent> _calendarEvents;
        public List<CalendarEvent> CalendarEvents
        {
            get { return _calendarEvents; }
        }

        private List<CalendarResource> _cpiResources = new List<CalendarResource>();
        public List<CalendarResource> CpiResources
        {
            get { return _cpiResources; }
        }

        private List<CalendarResource> _resources = new List<CalendarResource>();
        public List<CalendarResource> Resources
        {
            get { return _resources; }
        }

        public DayOfWeek FirstDayOfWeek
        {
            get { return DayOfWeek.Monday; }
        }

        protected override BindingType BindingPropertyType => BindingType.Array;

        private int _currentCardEventIndex;
        public CalendarEvent CurrentCalendarEvent
        {
            get
            {
                if (_sortedCalendarEvents == null || _sortedCalendarEvents.Count == 0 || _currentCardEventIndex >= _sortedCalendarEvents.Count)
                {
                    return null;
                }

                return _sortedCalendarEvents[_currentCardEventIndex];
            }
        }

        public string CardCountLabel
        {
            get
            {
                // Only show the card label if there is more than one card to show
                if (_sortedCalendarEvents == null || _sortedCalendarEvents.Count < 2)
                {
                    return string.Empty;
                }

                return $"{_currentCardEventIndex + 1} / {_sortedCalendarEvents.Count}";
            }
        }

        public CalendarElement(IMetadata metadata, IDataHandler data, ILogger logger, IExpressionRunner expressionRunner, ICardDefCreator cardDefCreator, ICommandExecutor commandExecutor, IOnlineDataHandler onlineDataHandler)
        {
            _metadata = metadata;
            _data = data;
            _logger = logger;
            _expressionRunner = expressionRunner;
            _cardDefCreator = cardDefCreator;
            _commandExecutor = WrappedCommandExecutor.Create(commandExecutor, BeforeCommand, null);
            _onlineDataHandler = onlineDataHandler;
        }

        protected override bool OnInitialize()
        {
            _calendar = _metadata.FindCalendar(ProjectionName, Content.Calendar);
            HasHeader = false;
            LoadData();
            return _calendar != null;
        }

        protected override bool OnLoad()
        {
            return true;
        }

        private void LoadData()
        {
            if (_calendar != null)
            {
                Label = _calendar.Label;

                switch (_calendar.SlotTick)
                {
                    case "60":
                        _calendarSlotSize = CalendarSlotSize.Hour;
                        break;
                    case "1440":
                        _calendarSlotSize = CalendarSlotSize.Day;
                        break;
                    default:
                        _calendarSlotSize = CalendarSlotSize.Hour;
                        break;
                }

                _weeknumbers = _calendar.WeekNumbers;
                _schedule = _calendar.Schedule;
                _events = _calendar.Events;

                _calendarEventNames.Add(Strings.AllEvents);

                foreach (string name in _events.Select(x => x.Name))
                {
                    if (name != null && !_calendarEventNames.Contains(name))
                    {
                        _calendarEventNames.Add(name);
                    }
                }

                if (_schedule != null)
                {
                    LoadWorkingHours();
                }
            }
        }

        // Unit Test Method
        public async Task ForceLoadData()
        {
            if (DataSource == null)
            {
                return;
            }

            await LoadDataForEvents();

            if (_calendar.Resources.Count > 0)
            {
                foreach (KeyValuePair<string, CpiCalendarResource> resource in _calendar.Resources)
                {
                    LoadDataForResourceAsync(resource.Key, resource.Value);
                }
            }
        }

        protected override async void OnDataSourceChanged()
        {
            base.OnDataSourceChanged();

            await LoadDataForEvents();

            if (NativeCalendarElement != null)
            {
                NativeCalendarElement.CalendarIsLoaded = true;
            }

            if (_calendar.Resources?.Values.Count > 0)
            {
                foreach (KeyValuePair<string, CpiCalendarResource> resource in _calendar.Resources)
                {
                    if (resource.Key != null && resource.Value != null)
                    {
                        LoadDataForResourceAsync(resource.Key, resource.Value);
                    }
                }
            }

            foreach (CpiCalendarEvent calendarEvent in _events)
            {
                if (calendarEvent.Resources?.Values.Count > 0 && calendarEvent.Name != null)
                {
                    foreach (KeyValuePair<string, CpiCalendarResource> resource in calendarEvent.Resources)
                    {
                        if (resource.Key != null && resource.Value != null)
                        {
                            LoadDataForResourceAsync(resource.Key, resource.Value);
                        }
                    }
                }
            }

            _cpiResources = _resources.GroupBy(elem => elem.Label).Select(group => group.First()).ToList();

            if (_cpiResources.Count > 0)
            {
                NativeCalendarElement?.AddResourceSegments(_cpiResources);
            }

            _calendarEvents = GetSearchedCalendarEvents(string.Empty);
            NativeCalendarElement?.ShowCalendar(_calendarEvents);
        }

        public void OnCalendarLoaded()
        {
            OnDataSourceChanged();
        }

        protected override void OnStoredDataChanged(DataChangeSet changeSet)
        {
            base.OnStoredDataChanged(changeSet);
            bool shouldRefresh = false;
            List<string> entities = _events.Select(calEvent => calEvent.Entity).ToList();
            foreach (string entity in entities)
            {
                EntityDataSource dataSource = !string.IsNullOrEmpty(entity) ? EntityDataSource.FromEntitySet(_metadata, ProjectionName, entity) : DataSource;
                if (dataSource != null && dataSource.IsEffectedByChangeSet(changeSet))
                {
                    shouldRefresh = true;
                    break;
                }
            }

            if (shouldRefresh)
            {
                AssignCardData();
                _calendarEvents = GetSearchedCalendarEvents(string.Empty);
                NativeCalendarElement?.ReloadAppointments(_calendarEvents, true, true);
            }
        }

        public async Task FilterCalendarEvents(List<string> eventNames, string searchText = "", bool refreshAll = true)
        {
            _filteredEvents = new List<CpiCalendarEvent>();

            if (eventNames.Count == 0)
            {
                eventNames = _calendarEventNames.ToList();
                eventNames.RemoveAt(0);
            }

            if (NativeCalendarElement != null)
            {
                NativeCalendarElement.CalendarIsLoaded = refreshAll;
            }

            foreach (CpiCalendarEvent calendarEvent in _events)
            {
                if (calendarEvent.Name != null && eventNames.Contains(calendarEvent.Name))
                {
                    _filteredEvents.Add(calendarEvent);
                }
            }

            _calendarEvents = new List<CalendarEvent>();

            foreach (CpiCalendarEvent calEvent in _filteredEvents)
            {
                await ReloadDataForEventAsync(calEvent);
            }

            _calendarEvents = GetSearchedCalendarEvents(searchText);
            if (refreshAll)
            {
                NativeCalendarElement?.ReloadAppointments(_calendarEvents, true);
            }
            else
            {
                NativeCalendarElement?.ReloadAppointments(_calendarEvents, false);
            }
        }

        private async Task ReloadDataForEventAsync(CpiCalendarEvent calEvent)
        {
            _calendarEvents.RemoveAll(calendarEvent => calendarEvent.CpiCalendarEvent == calEvent);
            await LoadDataForEvent(calEvent);
        }

        private async Task LoadDataForEvent(CpiCalendarEvent calEvent)
        {
            EntityDataSource dataSource = !string.IsNullOrEmpty(calEvent.Entity) ? EntityDataSource.FromEntitySet(_metadata, ProjectionName, calEvent.Entity) : DataSource;

            if (_events != null && dataSource != null)
            {
                EntityQuery query = new EntityQuery(dataSource)
                {
                    SelectAttributes = GetCalendarSelectAttributes(calEvent)
                };

                query.Take = NumberOfRecords;

                if (dataSource == PageData.DataSource)
                {
                    PageData.Filter?.Apply(query);
                }

                await GetDatesFromQuery(query, calEvent);
            }
        }

        public async Task LoadDataForEvents()
        {
            _calendarEvents = new List<CalendarEvent>();

            foreach (CpiCalendarEvent calEvent in _events)
            {
                await LoadDataForEvent(calEvent);
            }
        }

        private string[] GetCalendarSelectAttributes(CpiCalendarEvent cpiCalendarEvent = null)
        {
            HashSet<string> attributes = new HashSet<string>();
            AttributeFinder.FindInCalendar(attributes, _metadata, ProjectionName, _calendar);

            if (cpiCalendarEvent?.Card != null)
            {
                CpiCard calendarCard = _metadata.FindCard(ProjectionName, cpiCalendarEvent.Card);

                if (calendarCard != null)
                {
                    AttributeFinder.FindInCard(attributes, _metadata, ProjectionName, calendarCard, false);
                }
            }

            return attributes.ToArray();
        }
        // @SONAR_STOP@
        public async void LoadWorkingHours()
        {
            EntityQuery query = null;
            EntityDataSource dataSource = !string.IsNullOrEmpty(_calendar.Entity) ? EntityDataSource.FromEntity(_metadata, ProjectionName, _calendar.Entity) : DataSource;

            if (dataSource != null)
            {
                query = new EntityQuery(dataSource);
                query.Take = 1;
            }

            EntityQueryResult results = await _data.GetRecordsAsync(query, CancellationToken.None);
            RecordData recordData = new RecordData(_logger, _metadata, _data);
            recordData.LoadRecord(ProjectionName, results.Records.FirstOrDefault());

            if (recordData.TryGetRecordValue(Schedule.WorkDayStart, out object startHour) && startHour is DateTime start)
            {
                _workStartHour = _metadata.IsEntityKnownTimeZone(recordData.EntityName, ProjectionName) ? start.ToClientLocalTime() : start;
            }

            if (recordData.TryGetRecordValue(Schedule.WorkDayEnd, out object endHour) && endHour is DateTime end)
            {
                _workEndHour = _metadata.IsEntityKnownTimeZone(recordData.EntityName, ProjectionName) ? end.ToClientLocalTime() : end;
            }
        }
        // @SONAR_START@

        private async Task GetDatesFromQuery(EntityQuery query, CpiCalendarEvent calEvent)
        {
            if (query == null)
            {
                return;
            }

            try
            {
                EntityQueryResult results = await _data.GetRecordsAsync(query, CancellationToken.None);
                CpiCard card = calEvent.Card == null ? null : _metadata.FindCard(ProjectionName, calEvent.Card);

                foreach (EntityRecord entityRecord in results.Records)
                {
                    RecordData recordData = new RecordData(_logger, _metadata, _data);
                    recordData.LoadRecord(ProjectionName, entityRecord);

                    CardData cardData = new CardData(PageData, recordData);

                    if (recordData[calEvent.Start] is DateTime dateFrom && recordData[calEvent.End] is DateTime dateTo)
                    {
                        CalendarEvent calendarEvent = null;
                        if (_metadata.IsEntityKnownTimeZone(recordData.EntityName, ProjectionName))
                        {
                            calendarEvent = CalendarEvent.CreateEvent(dateFrom.ToClientLocalTime(), dateTo.ToClientLocalTime(), cardData, _expressionRunner, card, calEvent, this);
                        }
                        else
                        {
                            calendarEvent = CalendarEvent.CreateEvent(dateFrom, dateTo, cardData, _expressionRunner, card, calEvent, this);
                        }

                        if (calendarEvent != null)
                        {
                            CardDefItem badgeItem = GetCardDefForAppointment(calendarEvent)?.BadgeItem;
                            Badge badge = badgeItem?.GetBadge(calendarEvent?.CardData);
                            calendarEvent.AppointmentBadge = badge;
                            _calendarEvents.Add(calendarEvent);
                        }
                    }
                }

                _sortedCalendarEvents = _calendarEvents.OrderBy(calendarEvent => GetRecordValue(calendarEvent.CardData.Record, calendarEvent.CpiCalendarEvent.Start)).ToList();
            }
            catch (Exception e)
            {
                await HandleException(e);
            }
        }

        public CardDef GetCardForEvent(CalendarEvent calendarEvent)
        {
            if (calendarEvent.CpiCard == null)
            {
                return null;
            }

            return _cardDefCreator.CreateCardDef(ProjectionName, calendarEvent.CpiCard, null, GetEventItemValue, _commandExecutor);
        }

        private object GetEventItemValue(AttributePath attribute, object dataContext)
        {
            ViewData viewData = (ViewData)dataContext;
            return viewData?.Record[attribute];
        }

        private void LoadDataForResourceAsync(string key, CpiCalendarResource resource)
        {
            if (DataSource != null)
            {
                EntityQuery query = new EntityQuery(DataSource)
                {
                    SelectAttributes = GetCalendarSelectAttributes()
                };

                query.Take = NumberOfRecords;

                CalendarResource calendarResource = new CalendarResource(resource.Label, resource.Filter, resource.Emphasis, key);
                _resources.Add(calendarResource);
            }
        }

        public CardDef GetCardDefForAppointment(CalendarEvent calendarEvent)
        {
            if (calendarEvent == null || calendarEvent.CpiCard == null)
            {
                return null;
            }

            return _cardDefCreator.CreateCardDef(ProjectionName, calendarEvent.CpiCard, null, GetItemValue, _commandExecutor);
        }

        private object GetValue(object dataContext, CpiField field)
        {
            AttributePath attributePath = AttributePath.Create(field.Attribute);

            if (attributePath == null || dataContext == null)
            {
                return null;
            }

            object value = GetItemValue(attributePath, dataContext);

            if (field?.Enumeration != null)
            {
                CpiEnumeration enumeration = _metadata.FindEnumeration(ProjectionName, field?.Enumeration);
                CpiEnumerationLabel label = enumeration.Labels.First(x => x.Value == value.ToString());
                value = label?.Label;
            }

            IBindValueConverter converter = new LabelConvertor(_expressionRunner, _metadata);
            object converterParameter = field.Label;

            return converter.Convert(value, typeof(object), converterParameter, null);
        }

        public string GetDisplayValue(object dataContext, CpiField field)
        {
            if (field?.Control == CpiControlType.ComputedField)
            {
                string value = null;

                if (dataContext is ViewData viewData)
                {
                    value = _expressionRunner.InterpolateString(field.Expression, viewData.Record);
                }

                return value;
            }
            else
            {
                object value = GetValue(dataContext, field);
                return value?.ToString();
            }
        }

        public CpiBadge GetCalendarEventBadge(CalendarEvent calendarEvent)
        {
            if (calendarEvent == null || calendarEvent.CpiCard == null)
            {
                return null;
            }

            foreach (CpiField field in calendarEvent.Day.Content.Select(field => field.Field).ToList())
            {
                if (field.Control == CpiControlType.Badge)
                {
                    CpiBadge badge = new CpiBadge(_metadata, _expressionRunner, ProjectionName, field);
                    badge = UpdateCpiBadge(badge, calendarEvent.CardData, field);
                    return badge;
                }
            }

            return null;
        }

        public CpiBadge UpdateCpiBadge(CpiBadge badgeTemp, ViewData viewData, CpiField field)
        {
            if (badgeTemp != null)
            {
                UpdateBadge(viewData, badgeTemp, field);
            }

            return badgeTemp;
        }

        public void UpdateBadge(ViewData viewData, CpiBadge badge, CpiField field)
        {
            badge.Text = viewData == null ? null : GetDisplayValue(viewData, field);
            badge.UpdateStates(viewData);
        }

        public int GetMinimumAppointmentTime(CalendarEvent calendarEvent)
        {
            // Check for events shorter than 26 minutes
            TimeSpan diff = calendarEvent.EndTime - calendarEvent.StartTime;
            return diff.TotalMinutes <= 25.0 ? calendarEvent.StartTime.AddMinutes(25).Minute : calendarEvent.EndTime.Minute;
        }

        private object GetItemValue(AttributePath attribute, object dataContext)
        {
            ViewData viewData = (ViewData)dataContext;
            return viewData?.Record[attribute];
        }

        public void BackCardPanel()
        {
            if (_sortedCalendarEvents == null || _sortedCalendarEvents.Count == 0)
            {
                return;
            }

            if (_currentCardEventIndex == 0)
            {
                _currentCardEventIndex = _sortedCalendarEvents.Count - 1;
            }
            else
            {
                _currentCardEventIndex--;
            }

            NativeCalendarElement?.ShowCardPanel();
            NativeCalendarElement?.FocusCalendarEvent(CurrentCalendarEvent);

            _entitySetName = CurrentCalendarEvent.CpiCalendarEvent.Entity;
        }

        public void ForwardCardPanel()
        {
            if (_sortedCalendarEvents == null || _sortedCalendarEvents.Count == 0)
            {
                return;
            }

            if (_currentCardEventIndex == _sortedCalendarEvents.Count - 1)
            {
                _currentCardEventIndex = 0;
            }
            else
            {
                _currentCardEventIndex++;
            }

            NativeCalendarElement?.ShowCardPanel();
            NativeCalendarElement?.FocusCalendarEvent(CurrentCalendarEvent);

            _entitySetName = CurrentCalendarEvent.CpiCalendarEvent.Entity;
        }

        public void InvokeNativeMethod(CalendarEvent calendarEvent)
        {
            if (calendarEvent == null)
            {
                return;
            }

            _currentCardEventIndex = _sortedCalendarEvents.IndexOf(calendarEvent);
            if (_currentCardEventIndex >= 0)
            {
                NativeCalendarElement?.ShowCardPanel();
            }
            else
            {
                CalendarEventField eventField = calendarEvent.CalendarEventFields.FirstOrDefault();
                CalendarEvent selectedCalendarEvent = null;

                if (eventField != null)
                {
                    selectedCalendarEvent = _sortedCalendarEvents.Find(r => r.CalendarEventFields.Any() && r.CalendarEventFields.FirstOrDefault()?.Id == eventField.Id && r.CalendarEventFields.FirstOrDefault()?.Label == eventField.Label);

                    if (selectedCalendarEvent != null)
                    {
                        _currentCardEventIndex = _sortedCalendarEvents.IndexOf(selectedCalendarEvent);
                        NativeCalendarElement?.ShowCardPanel();
                    }
                    else
                    {
                        _currentCardEventIndex = 0;
                        NativeCalendarElement?.CloseCardPanel();
                    }
                }
            }

            _entitySetName = CurrentCalendarEvent?.CpiCalendarEvent.Entity;
        }

        public object GetRecordValue(RecordData recordData, string label)
        {
            object value = null;
            recordData?.TryGetRecordValue(label, out value);
            return value;
        }

        public static void ResetCalendarSettings(ISettings settings)
        {
            settings.Set(CalendarLastViewedDate, string.Empty);
            settings.Set(CalendarLastViewedScheduleType, string.Empty);
        }

        public static void ResetCalendarDateSettings(ISettings settings)
        {
            settings.Set(CalendarLastViewedDate, string.Empty);
        }

        private Task<ExecuteResult> BeforeCommand(ViewData data, CpiCommand command, CommandOptions options)
        {
            data.Record.GetRemoteRow().EntitySetName = data.Record.GetRemoteRow().EntitySetName ?? _entitySetName;
            return Task.FromResult<ExecuteResult>(null);
        }

        public void AssignCardData()
        {
            if (CurrentCalendarEvent == null)
            {
                NativeCalendarElement.CloseCardPanel();
                return;
            }

            CardDef cardDef = GetCardDefForAppointment(CurrentCalendarEvent);

            if (cardDef == null)
            {
                NativeCalendarElement.CloseCardPanel();
                return;
            }

            NativeCalendarElement.RefreshCardData(cardDef);
        }

        public bool IsOverlappingAppointment(CalendarEvent currentCalendarEvent, CalendarEvent calendarEventToCompare, DateTime visibleDate)
        {
            if (currentCalendarEvent == null || calendarEventToCompare == null || currentCalendarEvent.IsAllDay || calendarEventToCompare.IsAllDay)
            {
                return false;
            }

            DateTime startTimeToCompare = calendarEventToCompare.StartTime;
            DateTime endTimeToCompare = calendarEventToCompare.EndTime;
            DateTime currentStartTime = currentCalendarEvent.StartTime;
            DateTime currentEndTime = currentCalendarEvent.EndTime;

            TimeSpan currentTimeDuration = currentEndTime - currentStartTime;
            TimeSpan durationToCompare = endTimeToCompare - startTimeToCompare;

            // When creating events we add +25 minutes if the duration is less than 25. This condition will handle those events.
            if (currentTimeDuration.TotalMinutes <= 25.0 && durationToCompare.TotalMinutes <= 25.0 && startTimeToCompare.DayOfYear == visibleDate.DayOfYear
                && (Math.Abs((currentEndTime - startTimeToCompare).TotalMinutes) <= 25.0 || Math.Abs((endTimeToCompare - currentStartTime).TotalMinutes) <= 25.0))
            {
                return true;
            }

            if (calendarEventToCompare.IsLongRunning && startTimeToCompare <= visibleDate && visibleDate <= endTimeToCompare)
            {
                startTimeToCompare = GetDateTimeForLongRunningEvent(startTimeToCompare, visibleDate, true);
                endTimeToCompare = GetDateTimeForLongRunningEvent(endTimeToCompare, visibleDate, false);
            }

            if (currentCalendarEvent.IsLongRunning)
            {
                currentStartTime = GetDateTimeForLongRunningEvent(currentStartTime, visibleDate, true);
                currentEndTime = GetDateTimeForLongRunningEvent(currentEndTime, visibleDate, false);
            }

            return (startTimeToCompare < currentStartTime && currentStartTime < endTimeToCompare) || (startTimeToCompare < currentEndTime && currentEndTime < endTimeToCompare)
                || (currentStartTime < startTimeToCompare && endTimeToCompare < currentEndTime) || startTimeToCompare == currentStartTime || endTimeToCompare == currentEndTime;
        }

        private DateTime GetDateTimeForLongRunningEvent(DateTime dateTime, DateTime visibleDate, bool isStartTime)
        {
            if (dateTime.DayOfYear != visibleDate.DayOfYear)
            {
                return isStartTime
                    ? new DateTime(visibleDate.Year, visibleDate.Month, visibleDate.Day, 0, 0, 0)
                    : new DateTime(visibleDate.Year, visibleDate.Month, visibleDate.Day, 23, 59, 59);
            }

            return dateTime;
        }

        public List<CalendarEvent> GetSearchedCalendarEvents(string searchedString, List<CalendarEvent> eventsToSearch = null)
        {
            char[] charsToTrim = { ',', ' ' };
            string label;

            List<CalendarEvent> searchedCalendarEvents = new List<CalendarEvent>();

            eventsToSearch = eventsToSearch ?? _calendarEvents.OrderBy(calendarEvent => GetRecordValue(calendarEvent.CardData.Record, calendarEvent.CpiCalendarEvent.Start)).ToList();

            HashSet<CalendarEvent> hsSearchedCalendarEvents = new HashSet<CalendarEvent>();

            if (string.IsNullOrEmpty(searchedString))
            {
                AssignCardData();
                _sortedCalendarEvents = _calendarEvents.OrderBy(calendarEvent => GetRecordValue(calendarEvent.CardData.Record, calendarEvent.CpiCalendarEvent.Start)).ToList();
                //need to remove duplicate
                foreach (CalendarEvent calendarEvent in _sortedCalendarEvents)
                {
                    hsSearchedCalendarEvents.Add(calendarEvent);
                }
            }
            else
            {
                foreach (CalendarEvent calendarEvent in eventsToSearch)
                {
                    label = string.Empty;

                    foreach (CalendarEventField item in calendarEvent.CalendarEventFields)
                    {
                        label = string.IsNullOrEmpty(item.Label) ? (label + item.Id + ", ") : (label + item.Label + ": " + item.Id + ", ");
                    }

                    label = label.TrimEnd(charsToTrim);

                    if (label.ToLower().Contains(searchedString.ToLower()))
                    {
                        hsSearchedCalendarEvents.Add(calendarEvent);
                    }
                }
            }

            searchedCalendarEvents = hsSearchedCalendarEvents.ToList();
            _sortedCalendarEvents = searchedCalendarEvents;
            return searchedCalendarEvents;
        }
    }

    public enum CalendarSlotSize
    {
        Hour,
        Day
    }

    public enum CalendarType
    {
        Day,
        Week,
        WorkWeek,
        Month,
        Agenda
    }
}
