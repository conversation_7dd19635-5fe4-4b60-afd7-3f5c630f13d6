﻿using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Messages
{
    [Table(Name = FwDataContext.FwTablePrefix + "ignore_message_in", Class = MetaTableClass.App)]
    public class IgnoreMessageIn
    {
        [Column(PrimaryKey = true)]
        public string TableName { get; set; }

        [Column(PrimaryKey = true)]
        public string ObjKey { get; set; }

        [Column(PrimaryKey = true)]
        public string ObjVersion { get; set; }
    }
}
