﻿using Ifs.Uma.AppData.Expressions;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Expressions
{
    [TestFixture]
    public class IfsExpressionFromStringTests
    {
        [Test]
        public void NegativeNum()
        {
            string parsed = IfsExpression.FromString("-50").ToString();
            Assert.AreEqual("-50", parsed);
        }

        [Test]
        public void BacketedMath()
        {
            string parsed = IfsExpression.FromString("5 * ((8 + 2) / 2.0) * 2").ToString();
            Assert.AreEqual("((5 * ((8 + 2) / 2)) * 2)", parsed);
        }

        [Test]
        public void OrderedMath()
        {
            string parsed = IfsExpression.FromString("5 * 8 + 2 - 1 / 2.0 * 2").ToString();
            Assert.AreEqual("(((5 * 8) + 2) - ((1 / 2) * 2))", parsed);
        }

        [Test]
        public void Vars()
        {
            string parsed = IfsExpression.FromString("5 + MyVar + 2.1").ToString();
            Assert.AreEqual("((5 + MyVar) + 2.1)", parsed);

            parsed = IfsExpression.FromString("MyVar + MyVar2").ToString();
            Assert.AreEqual("(MyVar + MyVar2)", parsed);

            parsed = IfsExpression.FromString("MyVar+MyVar2").ToString();
            Assert.AreEqual("(MyVar + MyVar2)", parsed);

            parsed = IfsExpression.FromString("Aaa2").ToString();
            Assert.AreEqual("Aaa2", parsed);
            
            parsed = IfsExpression.FromString("B").ToString();
            Assert.AreEqual("B", parsed);

            parsed = IfsExpression.FromString("Bbb2.ccc2").ToString();
            Assert.AreEqual("Bbb2.ccc2", parsed);
        }

        [Test]
        public void Methods()
        {
            string parsed = IfsExpression.FromString("MyMethod(3)").ToString();
            Assert.AreEqual("MyMethod(3)", parsed);

            parsed = IfsExpression.FromString("MyNs.MyMethod(3)").ToString();
            Assert.AreEqual("MyNs.MyMethod(3)", parsed);
            
            parsed = IfsExpression.FromString("MyNs.MyMethod(3, 'ahhh')").ToString();
            Assert.AreEqual("MyNs.MyMethod(3, 'ahhh')", parsed);

            parsed = IfsExpression.FromString("toTimestamp(addDays(StartDate, 5))").ToString();
            Assert.AreEqual("toTimestamp(addDays(StartDate, 5))", parsed);
            
            parsed = IfsExpression.FromString("5 + MyMethod(3 + 4) + 2.1").ToString();
            Assert.AreEqual("((5 + MyMethod((3 + 4))) + 2.1)", parsed);
        }

        [Test]
        public void Ternary()
        {
            string parsed = IfsExpression.FromString("Count > 0 ? TotalCost / Count : 0").ToString();
            Assert.AreEqual("IIF((Count > 0), (TotalCost / Count), 0)", parsed);

            parsed = IfsExpression.FromString("A > B ? C > D ? '0' : '1' : '2'").ToString(); // A > B ? (C > D ? '0' : '1') : '2'
            Assert.AreEqual("IIF((A > B), IIF((C > D), '0', '1'), '2')", parsed);

            parsed = IfsExpression.FromString("A > B ? '0' : '1' ? '2' : '3'").ToString(); // A > B ? '0' : ('1' ? '2' : '3') 
            Assert.AreEqual("IIF((A > B), '0', IIF(Convert('1'), '2', '3'))", parsed);
            
            parsed = IfsExpression.FromString("(A > B ? '0' : '1') ? '2' : '3'").ToString(); 
            Assert.AreEqual("IIF(Convert(IIF((A > B), '0', '1')), '2', '3')", parsed);
        }
    }
}
