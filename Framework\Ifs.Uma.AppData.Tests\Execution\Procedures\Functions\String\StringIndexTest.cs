﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    internal class StringIndexTest : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringIndexTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("HelloWorld", "o", ExpectedResult = 6)]
        [TestCase("HelloWorld", null, ExpectedResult = -1)]
        [TestCase("HelloWorld", "x", ExpectedResult = -1)]
        public async Task<object> String_LastIndexTest(object input, object targetChar)
        {
            _params["TextInput"] = input;
            _params["TargetChar"] = targetChar;
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "StringLastIndexTest", _params);
            return result?.Value;
        }

        [Test]
        [TestCase("HelloWorld", "o", ExpectedResult = 4)]
        [TestCase("HelloWorld", null, ExpectedResult = -1)]
        public async Task<object> String_FirstIndexTest(object input, object targetChar)
        {
            _params["TextInput"] = input;
            _params["TargetChar"] = targetChar;
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "StringTestFirstIndexOf", _params);
            return result?.Value;
        }

        [Test]
        [TestCase("HelloWorld", "o", 2, ExpectedResult = 6)]
        [TestCase("abcabcabc", "a", 3, ExpectedResult = 6)]
        [TestCase("abcabcabc", "a", 2, ExpectedResult = 3)]
        [TestCase("abcabcabc", "a", 1, ExpectedResult = 0)]
        [TestCase("abcabcabc", "a", 4, ExpectedResult = -1)]
        [TestCase("abcabcabc", null, 4, ExpectedResult = -1)]
        public async Task<object> String_SpecifiedIndeTest(object input, object targetChar, object occurrence)
        {
            _params["TextInput"] = input;
            _params["TargetChar"] = targetChar;
            _params["Occurrence"] = occurrence;
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "StringTestSpecifiedIndexOf", _params);
            return result?.Value;
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
