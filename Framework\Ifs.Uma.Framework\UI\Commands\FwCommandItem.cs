﻿using System.Collections.Generic;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.UI.Controls;

namespace Ifs.Uma.Framework.UI.Commands
{
    public abstract class FwCommandItem : CommandItem
    {
        public string ProjectionName { get; set; }

        private PageData _pageData;

        public PageData PageData
        {
            get => _pageData;
            set
            {
                if (SetProperty(ref _pageData, value))
                {
                    PageData oldValue = _pageData;
                    OnPageDataChanged(oldValue, _pageData);
                }
            }
        }

        private ViewData _viewData;

        public ViewData ViewData
        {
            get => _viewData;
            set
            {
                ViewData oldValue = _viewData;
                if (SetProperty(ref _viewData, value))
                {
                    OnViewDataChanged(oldValue, _viewData);
                }
            }
        }

        private string _bindingName;

        public string BindingName
        {
            get => _bindingName;
            protected set
            {
                if (SetProperty(ref _bindingName, value))
                {
                    UpdateViewData();
                }
            }
        }

        protected virtual void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            UpdateViewData();
        }

        private void UpdateViewData()
        {
            ViewData = PageData?.GetViewData(BindingName);
        }

        protected virtual void OnViewDataChanged(ViewData oldValue, ViewData newValue)
        {
        }

        public void UpdateStates(bool isUpdating)
        {
            OnUpdateStates(ViewData, isUpdating);
        }

        protected virtual void OnUpdateStates(ViewData viewData, bool isUpdating)
        {
        }

        public virtual void GetSelectAttributes(ICollection<string> attributes)
        {
        }
    }
}
