{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_ToUpper>": {"name": "String_ToUpper", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "ToUpper", "paramsArray": ["${TextInput}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}