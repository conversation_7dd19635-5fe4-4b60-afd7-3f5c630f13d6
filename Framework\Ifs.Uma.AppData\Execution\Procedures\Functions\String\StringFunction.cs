﻿using System;
using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal abstract class StringFunction : FwFunction
    {
        public const string FunctionNamespace = "String";
        private bool _returnWhenNull = false;
        public StringFunction(string name, int argCount, bool returnWhenNull) 
            : base(FunctionNamespace, name, argCount)
        {
            _returnWhenNull = returnWhenNull;
        }

        protected override sealed object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            object objectToModify = parameters[0].GetValue();
            if (_returnWhenNull && objectToModify == null)
                return null;

            string stringToModify = parameters[0].GetString();
            if (stringToModify == null)
                return null;

            return OnExecuteStringFunction(context, parameters, parameters[0].GetString());
        }

        protected abstract object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify);

        protected static Regex LikeToRegex(string pattern, RegexOptions regexOptions)
        {
            string converted = pattern.Replace('_', '.').Replace("%", ".*");
            return new Regex(converted, regexOptions, TimeSpan.FromSeconds(10));
        }

        protected static RegexOptions StringToRegexOptions(string stringRegexFlag)
        {
            if (string.IsNullOrEmpty(stringRegexFlag))
            {
                return RegexOptions.None;
            }

            RegexOptions regexOptions = default(RegexOptions);

            foreach (char regexChar in stringRegexFlag)
            {
                switch (regexChar)
                {
                    case 'I':
                    case 'i':
                        regexOptions = regexOptions | RegexOptions.IgnoreCase;
                        break;
                    case 'M':
                    case 'm':
                        regexOptions = regexOptions | RegexOptions.Multiline;
                        break;
                    case 'S':
                    case 's':
                        regexOptions = regexOptions | RegexOptions.Singleline;
                        break;
                    case 'N':
                    case 'n':
                        regexOptions = regexOptions | RegexOptions.ExplicitCapture | RegexOptions.Singleline;
                        break;
                    case 'X':
                    case 'x':
                        regexOptions = regexOptions | RegexOptions.IgnorePatternWhitespace;
                        break;
                }
            }

            return regexOptions;
        }
    }
}
