﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Attachments;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Elements.Groups;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Ifs.Uma.Framework.UI.Elements.Maps;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Framework.UI.RelatedPages;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Pages
{
    public class ElementPage : PageBase, IDisposable
    {
        private readonly IMetadata _metadata;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IDialogService _dialogService;
        private readonly INavigator _navigator;

        public AttachmentCommandItem AttachmentCommand { get; }

#if REMOTE_ASSISTANCE
        public RemoteAssistance.RemoteAssistanceCommandItem RemoteAssistanceCommand { get; }
#endif

        public PageCommandBlock Commands { get; }
        public ElementList Elements { get; }
        public PageData PageData { get; }

        private StatusIcon _statusIconObject;
        public StatusIcon StatusIconObject
        {
            get
            {
                return _statusIconObject;
            }
            set
            {
                SetProperty(ref _statusIconObject, value);
            }
        }

        private CpiPage _page;
        private RecordLoaderElement _recordLoader;
        private readonly IExpressionRunner _expressionRunner;
        private readonly IRoamingProfile _roamingProfile;
        private readonly IInsightsLogger _insightsLogger;
        private readonly IPerfLogger _perfLogger;
        private readonly IDataHandler _data;

        public event EventHandler Loaded;

        private bool _shouldProceedOnCarriageReturn;
        private bool _createNewRecord;
        private bool _disposed = false;
        private MetadataPageNavParam _navParam;

        private bool _actionPanelVisibility;
        public bool ActionPanelVisibility
        { 
            get 
            {
                return _actionPanelVisibility;
            }
            private set
            {
                if (_actionPanelVisibility != value)
                {
                    _actionPanelVisibility = value;
                    OnPropertyChanged(nameof(ActionPanelVisibility));
                }
            }
        }

        public ElementPage(IEventAggregator eventAggregator, IDialogService dialogService,
            IMetadata metadata, ElementCreator elementCreator, ILogger logger, IPerfLogger perfLogger, IInsightsLogger insightsLogger, IDataHandler data, INavigator navigator,
            ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, IRoamingProfile roamingProfile, IAppParameters appParameters,
            AttachmentCommandItem attachmentCommand)
            : base(eventAggregator, dialogService)
        {
            _metadata = metadata;
            _commandExecutor = WrappedCommandExecutor.Create(commandExecutor, AfterCommand);
            _expressionRunner = expressionRunner;
            _roamingProfile = roamingProfile;
            _dialogService = dialogService;
            _navigator = navigator;
            _insightsLogger = insightsLogger;
            _perfLogger = perfLogger;
            _data = data;

            _shouldProceedOnCarriageReturn = appParameters.ShouldProceedOnCarriageReturn();

            AttachmentCommand = attachmentCommand;
            AttachmentCommand.UpdatingState.ParentState = UpdatingState;
            
            Commands = new PageCommandBlock(UpdatingState, metadata, _commandExecutor, expressionRunner, OnCreateCommand, OnDeleteCommand);
            Elements = new ElementList(UpdatingState, elementCreator);
            Elements.PropertyChanged += Elements_OnPropertyChanged;
            RelatedPages = new RelatedPagesList(UpdatingState, metadata, _commandExecutor, expressionRunner, data, dialogService, navigator);

            PageData = new PageData(logger, metadata, data);
            PageData.Page = this;
            PageData.DefaultViewData.Record.RecordLoaded += Data_RecordLoaded;
            PageData.PropertyChanged += PageData_PropertyChanged;

#if REMOTE_ASSISTANCE
            RemoteAssistanceCommand = Resolver.Resolve<RemoteAssistance.RemoteAssistanceCommandItem>();
            RemoteAssistanceCommand.UpdatingState.ParentState = UpdatingState;
            RemoteAssistanceCommand.PageData = PageData;
#endif

            AttachmentCommand.PageData = PageData;
            
            Commands.PageData = PageData;
            Elements.PageData = PageData;
            RelatedPages.PageData = PageData;
        }

        private void Elements_OnPropertyChanged(object sender, PropertyChangedEventArgs propertyChangedEventArgs)
        {
            if (propertyChangedEventArgs.PropertyName == nameof(ElementList.PrimaryElement))
            {
                UpdateTitle();
                ActionPanelVisibility = Elements.PrimaryElement != null;

                if (Elements.PrimaryElement is MapElement element && element.PageData.Page is ElementPage page && page.IsGroupWithMapPage())
                {
                    ActionPanelVisibility = false;
                }
            }
        }

        private void PageData_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PageData.HasChanges))
            {
                HasChanges = PageData.HasChanges;
            }
            else if (e.PropertyName == nameof(PageData.EditingViewData))
            {
                if (PageData.EditingViewData != null)
                {
                    PageData.EditMode.BeginEditing(SaveChangesAsync, CancelChangesAsync);
                }
                else
                {
                    PageData.EditMode.EndEditing();
                }
            }
        }

        public bool IsGroupWithMapPage()
        {
            return _page.Content.IsGroupWithMapPage();
        }

        private void Data_RecordLoaded(object sender, EventArgs e)
        {
            UpdateTitle();

            if (PageData?.DefaultViewData?.Record?.IsNew() == false)
            {
                // Clear this flag since we are no longer in new record mode
                _createNewRecord = false;
            }
        }

        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (!IsActive)
            {
                foreach (ListElement list in Elements.Elements.Where(x => x is ListElement))
                {
                    _ = list.CancelDataLoading();
                }
            }
        }

        public override async void OnNavigatedTo(NavigatedToArgs args)
        {
            base.OnNavigatedTo(args);

            if (PageData.HasChanges)
            {
                return;
            }

            _createNewRecord = args.NavigationMode == NavigationMode.New;

            if (args.NavigationMode == NavigationMode.Back || args.NavigationMode == NavigationMode.BackRetainFullscreen || args.NavigationMode == NavigationMode.Refresh)
            {
                foreach (RecordLoaderElement recordLoader in Elements.Elements.OfType<RecordLoaderElement>())
                {
                    await recordLoader.ReloadRecordAsync();
                }

                Elements?.ReloadData();
                await AttachmentCommand.UpdateStates();

#if REMOTE_ASSISTANCE
                RemoteAssistanceCommand.UpdateStates();
#endif

                StatusIconObject = await StatusIcon.GetStatusIconAsync(_metadata, _data);
            }
            else
            {
                MetadataPageNavParam param = args.GetNavParam<MetadataPageNavParam>();
                await LoadPageAsync(param);
            }
        }

        public async Task<bool> LoadPageWithNewRecordAsync(NavigationParameter param)
        {
            _createNewRecord = true;
            return await LoadPageAsync(param);
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter param)
        {
            try
            {
                if (_navParam == null)
                {
                    _navParam = param as MetadataPageNavParam;
                }

                ProjectionName = _navParam?.ProjectionName;
                _page = _navParam?.Page == null ? null : _metadata.FindPage(ProjectionName, _navParam.Page);

                if (_page != null)
                {
                    using (_perfLogger.Track("PageLoad", ProjectionName + "." + _page.Name))
                    {
                        Name = _page.Name;
                        _insightsLogger?.TrackAppFeature("Page-" + ProjectionName + "." + _page.Name);
                        Logger.Trace("Navigate: Page-" + ProjectionName + "." + _page.Name);

                        PageData.PageSettings = _roamingProfile.GetSettings("Pages", ProjectionName, _page.Name);

                        EntityDataSource dataSource = GetDataSource(_navParam.Filter);

                        PageData.Filter = _navParam.Filter;
                        PageData.DataSource = dataSource;
                        PageData.CrudActions = _page.CrudActions;

                        PageData.DefaultViewData.CommandsEnabledOnEmpty = _page.Content != null && !_page.Content.Any(x => x.Selector != null || x.Singleton != null);

                        bool isCommandPage = _metadata.IsCommandPage(ProjectionName, _page.Name);

                        Commands.Load(_page.CommandGroups, _page.CrudActions, isCommandPage, ProjectionName, _page.Name);
                        AttachmentCommand.AttachmentsConfig = _page.Attachments;

                        Elements.LoadElements(ProjectionName, _page.CommandGroups, _page.Content, _page.StateIndicator);

                        if (_shouldProceedOnCarriageReturn)
                        {
                            foreach (GroupElement group in Elements.Elements.Where(x => x is GroupElement && x.IsVisible))
                            {
                                if (group.Form != null)
                                {
                                    foreach (Field field in group.Form.AllFields)
                                    {
                                        if (field is EntryField entryField)
                                        {
                                            entryField.ShowSoftKeyboardOnFocus = false;
                                        }

                                        field.TextScanned += Field_TextScanned;
                                    }
                                }
                            }
                        }

                        if (isCommandPage)
                        {
                            Classification = PageClassification.CommandPage;
                        }
                        else
                        {
                            RelatedPages.Load(_page.CommandGroups, _page.Workflows, ProjectionName, _page.Name);
                            Classification = PageClassification.Detail;
                        }

                        // Loading of UI element must be done before this
                        // so we can get the correct Select Attributes
                        PageData.SelectAttributes = GetSelectAttributes();

                        _recordLoader = Elements.PrimaryRecordLoader;

                        UpdateTitle();

                        if (_recordLoader != null)
                        {
                            if (_createNewRecord)
                            {
                                await _recordLoader.CreateRecordAsync();
                            }
                            else
                            {
                                await _recordLoader.LoadPageRecordAsync();
                            }
                        }

                        await PageData.WaitForBackgroundTasks();

                        Loaded?.Invoke(this, EventArgs.Empty);

#if REMOTE_ASSISTANCE
                        if (_page.RemoteAssistance?.NavigateSwitch != null)
                        {
                            foreach (CpiNavigateSwitch navigateSwitch in _page.RemoteAssistance.NavigateSwitch)
                            {
                                if (navigateSwitch.Navigate != null && _expressionRunner.RunCheck(navigateSwitch.Case, PageData.DefaultViewData, false))
                                {
                                    RemoteAssistanceCommand.RemoteAssistanceConfig = navigateSwitch.Navigate;
                                }
                            }
                        }
                        else
                        {
                            RemoteAssistanceCommand.RemoteAssistanceConfig = _page.RemoteAssistance?.Navigate;
                        }
#endif

                        StatusIcon icon = await StatusIcon.GetStatusIconAsync(_metadata, _data);
                        StatusIconObject = icon ?? StatusIconObject;

                        return true;
                    }
                }
                else
                {
                    Logger.Error($"Could not find page '{ProjectionName}.{_navParam?.Page}'");
                }
            }
            catch (Exception ex)
            {
                await HandleException(ex);
            }

            return false;
        }

        private EntityDataSource GetDataSource(PageValues pageFilter)
        {
            if (_page.DatasourceFunction != null)
            {
                Dictionary<string, object> parameterValues = new Dictionary<string, object>();
                pageFilter?.ExtractFunctionParameters(_page.DatasourceFunctionParams, parameterValues);
                return FunctionDataSource.Create(_metadata, _page.DatasourceProjection ?? ProjectionName, _page.DatasourceFunction, parameterValues);
            }

            return EntityDataSource.FromEntitySet(_metadata, _page.DatasourceProjection ?? ProjectionName, _page.DatasourceEntitySet);
        }

        private async Task OnCreateCommand()
        {
            if (_page?.Name != null)
            {
                await _navigator.NavigateToAsync(FrameworkLocations.MetadataPage, new MetadataPageNavParam(_page.DatasourceProjection ?? ProjectionName, _page.Name), NavigationMode.New);
            }
            else if (_recordLoader != null)
            {
                await _recordLoader.CreateRecordAsync();
            }
        }

        protected override async Task OnSaveChangesAsync()
        {
            List<RecordLoaderElement> recordLoaderToReload = new List<RecordLoaderElement>();
            foreach (RecordLoaderElement recordLoader in Elements.Elements.OfType<RecordLoaderElement>())
            {
                if (recordLoader.HasChanges())
                {
                    await recordLoader.SaveRecordAsync();
                }
                else
                {
                    recordLoaderToReload.Add(recordLoader);
                }
            }

            // Saving records may cause other data on the screen to change
            foreach (RecordLoaderElement recordLoader in recordLoaderToReload)
            {
                await recordLoader.ReloadRecordAsync();
            }
        }

        protected override async Task OnCancelChangesAsync()
        {
            foreach (RecordLoaderElement recordLoader in Elements.Elements.OfType<RecordLoaderElement>())
            {
                if (recordLoader.HasChanges())
                {
                    if (_createNewRecord)
                    {
                        await recordLoader.CreateRecordAsync();
                    }
                    else
                    {
                        await recordLoader.ReloadRecordAsync();
                    }
                }
            }
        }

        private async Task OnDeleteCommand()
        {
            if (_recordLoader != null)
            {
                bool confirm = await _dialogService.Confirm(PageData.CrudActions?.Delete?.Message ?? Strings.ConfirmDelete, null, Strings.Delete, ConfirmationType.Destructive);
                if (confirm)
                {
                    await _recordLoader.DeleteRecordAsync();

                    if (_navigator != null && !(Elements?.PrimaryElement is ListElement))
                    {
                        await _navigator.NavigateBackAsync();
                    }
                }
            }
        }

        private void Field_TextScanned(object sender, TextScannedEventArgs e)
        {
            if (sender is Field currentField && !string.IsNullOrEmpty(e.Text) && e.Text.EndsWith(Environment.NewLine))
            {
                Elements.MoveFocusToNextField(currentField);
            }
        }

        protected override async void OnStoredDataChangedAsync(DataChangeSet changeSet)
        {
            base.OnStoredDataChangedAsync(changeSet);

            StatusIconObject = await StatusIcon.GetStatusIconOnChangeAsync(_metadata, _data, changeSet) ?? StatusIconObject;

            Elements.NotifyStoredDataChanged(changeSet);
            Elements.SetInitialFocus();
        }

        private void UpdateTitle()
        {
            string title = null;
            if (Elements?.PrimaryElement != null && Elements.PrimaryElement.DisplayState == ElementDisplayState.FullScreen)
            {
                title = Elements.PrimaryElement.Header;
            }

            if (string.IsNullOrEmpty(title) && RecordData.HasLoadedExistingRecord(PageData.DefaultViewData.Record))
            {
                title = _expressionRunner.InterpolateString(_page?.Label, PageData.DefaultViewData.Record);
            }

            if (string.IsNullOrEmpty(title))
            {
                title = _page?.StaticLabel ?? _page?.Label;
            }

            BreadcrumbLabel = _page?.StaticLabel ?? title;
            Title = title;
        }

        private async Task<ExecuteResult> AfterCommand(ViewData data, CpiCommand command, CommandOptions options, ExecuteResult result)
        {
            if (!result.Failed && data == PageData.DefaultViewData && CommandChecker.CouldCommandChangeData(_metadata, data.Record.ProjectionName ?? ProjectionName, command))
            {
                foreach (RecordLoaderElement recordLoader in Elements.Elements.OfType<RecordLoaderElement>())
                {
                    if (recordLoader.HasChanges())
                    {
                        await recordLoader.SaveRecordAsync();
                    }
                    else
                    {
                        await recordLoader.ReloadRecordAsync();
                    }
                }

                await AttachmentCommand.UpdateStates();

#if REMOTE_ASSISTANCE
                RemoteAssistanceCommand.UpdateStates();
#endif
            }

            return result;
        }

        private string[] GetSelectAttributes()
        {
            HashSet<string> attributes = new HashSet<string>();

            AttributeFinder.FindInPage(attributes, _metadata, ProjectionName, _page);

            Commands.GetSelectAttributes(attributes);
            RelatedPages.GetSelectAttributes(attributes);
            Elements.GetSelectAttributes(attributes);

            return attributes.ToArray();
        }

        #region IDisposable Support

        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            _disposed = true;

            // Unsubscribe from events
            if (PageData?.DefaultViewData?.Record != null)
            {
                PageData.DefaultViewData.Record.RecordLoaded -= Data_RecordLoaded;
            }

            if (Elements != null)
            {
                Elements.PropertyChanged -= Elements_OnPropertyChanged;
            }

            if (PageData != null)
            {
                PageData.PropertyChanged -= PageData_PropertyChanged;
            }
        }

        ~ElementPage()
        {
            Dispose();
        }

        #endregion
    }
}
