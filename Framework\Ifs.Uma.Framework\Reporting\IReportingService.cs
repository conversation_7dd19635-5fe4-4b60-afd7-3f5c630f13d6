﻿using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Services.Attachments;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.Reporting
{
    public interface IReportingService
    {
        Task ShowPageReportAsync(string projectionName, string pageName, PageVal<PERSON> filter);

        Task<string> GenerateHtmlFromPage(string projectionName, string pageName, PageValues filter, CancellationToken cancelToken);

        Task<string> GenerateHtmlFromContent(string title, string logo, ElementList elements, bool allowSigning, CancellationToken cancelToken);
#if SIGNATURE_SERVICE
        string GenerateJsonFromContent(string assistantName, ElementList elements, bool allowSigning, CancellationToken cancelToken);
#endif
        Task<ReponseStream> DownloadFile(IIfsConnection connection, string logoRef, string empty);

        byte[] ReadStream(Stream stream);

        Task<ILocalFileInfo> SaveHtmlToPdf(string projectionName, string name, PageValues filters, string html, bool isSignature);
    }

    public interface IPdfGenerator
    {
        byte[] ConvertToPdfAsync(string html, string filePath = null);

        Task<bool> GeneratePdf(string fileName, string jsonContent, string entityName, string keyRef, string signingGuid, bool online, string logoRef, string savedPdfPath);
    }
}
