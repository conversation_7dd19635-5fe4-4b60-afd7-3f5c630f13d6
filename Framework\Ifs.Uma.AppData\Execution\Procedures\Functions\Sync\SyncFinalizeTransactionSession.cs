﻿using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Sync
{
    internal sealed class SyncFinalizeTransactionSession : SyncFunction
    {
        public const string FunctionName = "FinalizeTransactionSession";

        public SyncFinalizeTransactionSession()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            if (context.DbDataContext.TransactionSessions.Count(x => x.IsOpen == true) > 1)
            {
                Logger.Current.Error("Multiple open Transaction Sessions exist. Only the first one will be finalized.");
            }

            FndTransactionSession existing = context.DbDataContext.TransactionSessions.FirstOrDefault(x => x.IsOpen == true);

            if (existing != null)
            {
                if (existing.IsOpen == true)
                {
                    context.DbDataContext.TransactionSessions.Attach(existing);
                    existing.IsOpen = false;
                    context.DbDataContext.SubmitChanges(false);
                    Logger.Current.Information($"Finalized Transaction Session with ID '{existing.SessionId}'.");

                    return true;
                }
                else
                {
                    Logger.Current.Warning($"The Transaction Session '{existing.SessionId}' is already finalized.");
                }
            }
            else
            {
                Logger.Current.Warning("No open Transaction Sessions found.");
            }

            return false;
        }
    }
}
