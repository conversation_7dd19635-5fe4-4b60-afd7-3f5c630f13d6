﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData.AttributeExpressions
{
    public sealed class AttributeCompareExpression : AttributeExpression
    {
        public override AttributeExpressionType Type => AttributeExpressionType.Compare;
        public string AttributeName { get; }
        public AttributeCompareOperator Comparison { get;  }
        public object Value { get; }

        internal AttributeCompareExpression(string attributeName, AttributeCompareOperator comparison, object value)
        {
            if (attributeName == null) throw new ArgumentNullException(nameof(attributeName));

            AttributeName = attributeName;
            Comparison = comparison;
            Value = value;
        }

        internal override Expression ToExpression(EntityDataSource dataSource)
        {
            AttributePathInfo attribute = AttributePathInfo.Get(dataSource.Metadata, dataSource.ProjectionName, dataSource.EntityName, AttributeName);
            Expression columnAccess = attribute == null ? IfsExpression.VarAccess(AttributeName) : (Expression)IfsExpression.AttributeAccess(attribute);
            Expression valueExp = Expression.Constant(new DynamicValue(Value));

            ExpressionType type;
            switch (Comparison)
            {
                case AttributeCompareOperator.Equals:
                    type = ExpressionType.Equal;
                    break;
                case AttributeCompareOperator.NotEquals:
                    type = ExpressionType.NotEqual;
                    break;
                case AttributeCompareOperator.GreaterThan:
                    type = ExpressionType.GreaterThan;
                    break;
                case AttributeCompareOperator.GreaterThanOrEqual:
                    type = ExpressionType.GreaterThanOrEqual;
                    break;
                case AttributeCompareOperator.LessThan:
                    type = ExpressionType.LessThan;
                    break;
                case AttributeCompareOperator.LessThanOrEqual:
                    type = ExpressionType.LessThanOrEqual;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            return Expression.MakeBinary(type, columnAccess, valueExp);
        }

        internal override bool Match(EntityDataSource dataSource, EntityRecord record)
        {
            if (dataSource == null) throw new ArgumentNullException(nameof(dataSource));
            if (record == null) throw new ArgumentNullException(nameof(record));

            AttributePathInfo attribute = AttributePathInfo.Get(dataSource.Metadata, dataSource.ProjectionName, dataSource.EntityName, AttributeName);
            if (attribute != null)
            {
                object recordValue = record[attribute];
                if (Comparison == AttributeCompareOperator.Equals)
                {
                    return Equals(recordValue, Value);
                }
                else if (Comparison == AttributeCompareOperator.NotEquals)
                {
                    return !Equals(recordValue, Value);
                }
                else
                {
                    IComparable left = recordValue as IComparable;
                    IComparable right = Value as IComparable;

                    int compare = AttributeSort.Compare(left, right);
                    
                    switch (Comparison)
                    {
                        case AttributeCompareOperator.GreaterThan:
                            return compare > 0;
                        case AttributeCompareOperator.GreaterThanOrEqual:
                            return compare >= 0;
                        case AttributeCompareOperator.LessThan:
                            return compare < 0;
                        case AttributeCompareOperator.LessThanOrEqual:
                            return compare <= 0;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
            }

            return false;
        }

        public override string ToString()
        {
            return $"{AttributeName} {Comparison} {Value}";
        }
    }
}
