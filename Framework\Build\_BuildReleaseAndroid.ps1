Write-Output "============ Android"

# https://developer.xamarin.com/guides/cross-platform/ci/jen<PERSON>_walkthrough/
# https://developer.xamarin.com/guides/android/under_the_hood/build_process/

$androidManifestFile = "$($solutionDir)Ifs.Uma.Startup.Android\Properties\AndroidManifest.xml"
$androidSigningKeyStorePath = $solutionDir + "Ifs.Uma.Startup.Android\Google Play Upload Keystore\" +  $androidKeystoreAlias + "\" + $androidKeystoreAlias + ".keystore"

$androidManifest = (Get-Content $androidManifestFile)
$androidManifest = $androidManifest -replace "(?m)(android:versionCode=`")([\.\d]+)(`")", "`${1}$buildNumber`$3"
$androidManifest = $androidManifest -replace "(?m)(android:versionName=`")([\.\d]+)(`")", "`${1}$appVersion`$3"
$androidManifest | Set-Content -NoNewline $androidManifestFile

$packageId = ""
if ($androidManifest -join "`n" -match "package=`"(com.ifsworld.\w*?)`"")
{
    $packageId = $matches[1]
}
elseif ($androidManifest -join "`n" -match "package=`"(com.ifs.\w*?)`"")
{
    $packageId = $matches[1]
}
elseif ($androidManifest -join "`n" -match "package=`"(com.ifs.cloud.\w*?)`"")
{
    $packageId = $matches[1]
}

$releasePath = "$($solutionDir)Ifs.Uma.Startup.Android\bin\Release\"
$aabPath = ($releasePath + $packageId + ".aab")
$apksPath = ($releasePath + $packageId + ".apks")
$apkPath = ($releasePath + "universal.apk")
$signedAabPath = ($releasePath + $packageId + "-Signed.aab")

if (![string]::IsNullOrWhiteSpace($androidKeystoreAlias))
{
    Write-Output "Generating Play Store build..."

    msbuild "$($solutionDir)Ifs.Uma.Startup.Android\Ifs.Uma.Startup.Android.csproj" "/t:Clean;SignAndroidPackage" `
    /p:Configuration=Release /p:Platform=AnyCPU `
    /nr:false /v:m /m /p:RestorePackages=false /p:SolutionDir=$solutionDir `
    /p:AndroidKeyStore=true `
    /p:AndroidSigningKeyAlias=$androidKeystoreAlias `
    /p:AndroidSigningKeyPass=$androidSigningKeyPass `
    /p:AndroidSigningKeyStore=$androidSigningKeyStorePath `
    /p:AndroidSigningStorePass=$androidSigningKeyPass
    
    # bundletool is used to generate the APKS file from the AAB
    java -jar C:\Tools\bundletool\bundletool.jar build-apks --mode=universal --bundle=$aabPath --output=$apksPath `
    --ks=$androidSigningKeyStorePath `
    --ks-pass=pass:$androidSigningKeyPass `
    --ks-key-alias=$androidKeystoreAlias `
    --key-pass=pass:$androidSigningKeyPass
    
    Write-Output "End - Play Store build..."
}
else
{
    msbuild "$($solutionDir)Ifs.Uma.Startup.Android\Ifs.Uma.Startup.Android.csproj" "/t:Clean;SignAndroidPackage" `
        /p:Configuration=Release /p:Platform=AnyCPU `
        /nr:false /v:m /m /p:RestorePackages=false /p:SolutionDir=$solutionDir
        
    # bundletool is used to generate the APKS file from the AAB
    java -jar C:\Tools\bundletool\bundletool.jar build-apks --mode=universal --bundle=$aabPath --output=$apksPath
}

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "Extracting universal APK file..."

# this APKS file is a ZIP file containing the APK files, so extract it
C:\Tools\unzip\unzip.exe $apksPath -d $releasePath

# set the modified date of the APK as it sometimes doesn't have a value
$apkFile = Get-ChildItem $apkPath
$apkFile.LastWriteTime = (Get-Date)

Write-Output "End - Extracting universal APK file..."

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Copy-Item $apkPath ($deliverablesDir + $deliverableName + "_" + $appVersion +".apk")

if (Test-Path -Path $signedAabPath -PathType Leaf)
{
    Copy-Item $signedAabPath ($deliverablesDir + $deliverableName + "_" + $appVersion +"_Signed.aab")
}

Write-Output "============ End - Android"