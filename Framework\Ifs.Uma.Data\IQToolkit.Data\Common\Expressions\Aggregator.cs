﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Utility;

namespace IQToolkit.Data.Common
{
    internal static class Aggregator
    {
        /// <summary>
        /// Get a function that coerces a sequence of one type into another type.
        /// This is primarily used for aggregators stored in ProjectionExpression's, which are used to represent the 
        /// final transformation of the entire result set of a query.
        /// </summary>
        public static LambdaExpression GetAggregator(Type expectedType, Type actualType)
        {
            //JVB: Start
            if (expectedType == null) throw new ArgumentNullException("expectedType");
            TypeInfo expectedInfo = expectedType.GetTypeInfo();
            TypeInfo actualInfo = actualType.GetTypeInfo();
            if (!expectedInfo.IsAssignableFrom(actualInfo))
            {
                Type actualElementType = TypeHelper.GetElementType(actualType);
                //cannot assign the types but we may be able to fiddle 
                Type expectedElementType = TypeHelper.GetElementType(expectedType);
                ParameterExpression p = Expression.Parameter(actualType, "p");
                Expression body = null;
                if (expectedInfo.IsAssignableFrom(actualElementType.GetTypeInfo()))
                {
                    body = Expression.Call(typeof(Enumerable), "SingleOrDefault", new Type[] { actualElementType }, p);
                }
                else if (expectedType == typeof(IQueryable) ||
                    expectedType == typeof(IOrderedQueryable) ||
                    (expectedInfo.IsGenericType &&
                     (expectedType.GetGenericTypeDefinition() == typeof(IQueryable<>) ||
                      expectedType.GetGenericTypeDefinition() == typeof(IOrderedQueryable<>))))
                {
                    body = Expression.Call(typeof(Queryable), "AsQueryable", new Type[] { expectedElementType }, CoerceElement(expectedElementType, p));
                    if (body.Type != expectedType)
                    {
                        body = Expression.Convert(body, expectedType);
                    }
                }
                else if (expectedType.IsArray && expectedType.GetArrayRank() == 1)
                {
                    body = Expression.Call(typeof(Enumerable), "ToArray", new Type[] { expectedElementType }, CoerceElement(expectedElementType, p));
                }
                else if (expectedInfo.IsGenericType && expectedType.GetGenericTypeDefinition().GetTypeInfo().IsAssignableFrom(typeof(IList<>).GetTypeInfo()))
                {
                    Type gt = typeof(DeferredList<>).MakeGenericType(expectedType.GenericTypeArguments);
                    Type[] args = new Type[] { typeof(IEnumerable<>).MakeGenericType(expectedType.GenericTypeArguments) };
                    ConstructorInfo cn = gt.GetTypeInfo().DeclaredConstructors.Where(x => x.HasArgs(args)).FirstOrDefault();
                    body = Expression.New(cn, CoerceElement(expectedElementType, p));
                }
                else if (expectedInfo.IsAssignableFrom(typeof(List<>).MakeGenericType(actualElementType).GetTypeInfo()))
                {
                    // List<T> can be assigned to expectedType
                    body = Expression.Call(typeof(Enumerable), "ToList", new Type[] { expectedElementType }, CoerceElement(expectedElementType, p));
                }
                else
                {
                    // some other collection type that has a constructor that takes IEnumerable<T>
                    Type[] args = new Type[] { actualType };
                    ConstructorInfo ci = expectedInfo.DeclaredConstructors.Where(x => x.HasArgs(args)).FirstOrDefault();
                    if (ci != null)
                    {
                        body = Expression.New(ci, p);
                    }
                }
                if (body != null)
                {
                    return Expression.Lambda(body, p);
                }
            }
            //Type actualElementType = TypeHelper.GetElementType(actualType);
            //if (!expectedType.IsAssignableFrom(actualType))
            //{
            //    Type expectedElementType = TypeHelper.GetElementType(expectedType);
            //    ParameterExpression p = Expression.Parameter(actualType, "p");
            //    Expression body = null;
            //    if (expectedType.IsAssignableFrom(actualElementType))
            //    {
            //        body = Expression.Call(typeof(Enumerable), "SingleOrDefault", new Type[] { actualElementType }, p);
            //    }
            //    else if (expectedType.IsGenericType && 
            //        (expectedType == typeof(IQueryable) ||
            //         expectedType == typeof(IOrderedQueryable) ||
            //         expectedType.GetGenericTypeDefinition() == typeof(IQueryable<>) ||
            //         expectedType.GetGenericTypeDefinition() == typeof(IOrderedQueryable<>)))
            //    {
            //        body = Expression.Call(typeof(Queryable), "AsQueryable", new Type[] { expectedElementType }, CoerceElement(expectedElementType, p));
            //        if (body.Type != expectedType)
            //        {
            //            body = Expression.Convert(body, expectedType);
            //        }
            //    }
            //    else if (expectedType.IsArray && expectedType.GetArrayRank() == 1)
            //    {
            //        body = Expression.Call(typeof(Enumerable), "ToArray", new Type[] { expectedElementType }, CoerceElement(expectedElementType, p));
            //    }
            //    else if (expectedType.IsGenericType && expectedType.GetGenericTypeDefinition().IsAssignableFrom(typeof(IList<>)))
            //    {
            //        var gt = typeof(DeferredList<>).MakeGenericType(expectedType.GetGenericArguments());
            //        var cn = gt.GetConstructor(new Type[] {typeof(IEnumerable<>).MakeGenericType(expectedType.GetGenericArguments())});
            //        body = Expression.New(cn, CoerceElement(expectedElementType, p));
            //    }
            //    else if (expectedType.IsAssignableFrom(typeof(List<>).MakeGenericType(actualElementType)))
            //    {
            //        // List<T> can be assigned to expectedType
            //        body = Expression.Call(typeof(Enumerable), "ToList", new Type[] { expectedElementType }, CoerceElement(expectedElementType, p));
            //    }
            //    else
            //    {
            //        // some other collection type that has a constructor that takes IEnumerable<T>
            //        ConstructorInfo ci = expectedType.GetConstructor(new Type[] { actualType });
            //        if (ci != null)
            //        {
            //            body = Expression.New(ci, p);
            //        }
            //    }
            //    if (body != null)
            //    {
            //        return Expression.Lambda(body, p);
            //    }
            //}
            //JVB: End
            return null;
        }

        private static Expression CoerceElement(Type expectedElementType, Expression expression)
        {
            Type elementType = TypeHelper.GetElementType(expression.Type);
            //JVB: Start
            TypeInfo expectedElementInfo = expectedElementType.GetTypeInfo();
            TypeInfo elementInfo = elementType.GetTypeInfo();
            if (expectedElementType != elementType && (expectedElementInfo.IsAssignableFrom(elementInfo) || elementInfo.IsAssignableFrom(expectedElementInfo)))
            //if (expectedElementType != elementType && (expectedElementType.IsAssignableFrom(elementType) || elementType.IsAssignableFrom(expectedElementType)))
            //JVB: End
            {
                return Expression.Call(typeof(Enumerable), "Cast", new Type[] { expectedElementType }, expression);
            }
            return expression;
        }
    }
}