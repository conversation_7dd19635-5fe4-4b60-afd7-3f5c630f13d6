﻿using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements
{
    public class RepeatingSectionElement : ElementBase
    {
        private CpiRepeatingSection _repeatSection;
        private readonly ILogger _logger;
        private readonly IMetadata _metadata;
        private readonly IDataHandler _dataHandler;

        public ArrayDataSource ArrayDataSource { get; set; }
        public IElementCreator ElementCreator { get; set; }

        protected override BindingType BindingPropertyType => BindingType.None;

        private bool _dataLoaded = true;
        public bool DataLoaded
        {
            get => _dataLoaded;
            set => SetProperty(ref _dataLoaded, value);
        }

        public RepeatingSectionElement(ILogger logger, IMetadata metadata, IDataHandler dataHandler)
        {
            _logger = logger;
            _metadata = metadata;
            _dataHandler = dataHandler;
        }

        protected override bool OnInitialize()
        {
            _repeatSection = _metadata.FindRepeatingSection(ProjectionName, Content.RepeatingSection);
            return _repeatSection != null;
        }

        protected override bool OnLoad()
        {
            return true;
        }

        public async Task LoadData()
        {
            DataLoaded = false;

            if (ArrayDataSource != null)
            {
                EntityQuery query = new EntityQuery(ArrayDataSource);
                if (_repeatSection.OrderBy != null)
                {
                    query.AddSorts(_repeatSection.OrderBy);
                }

                EntityQueryResult result = await _dataHandler.GetRecordsAsync(query, CancellationToken.None);

                foreach (EntityRecord item in result.Records)
                {
                    RecordData recordData = new RecordData(_logger, _metadata, _dataHandler);
                    PageData pageData = new PageData(recordData);
                    pageData.DefaultViewData.Record.LoadRecord(ProjectionName, item);

                    foreach (CpiElementContent content in _repeatSection.Content)
                    {
                        ElementBase element = ElementCreator.CreateElement(ProjectionName, content);
                        element.Elements = Elements;
                        element.PageData = pageData;
                        element.InRepeatingSection = true;
                        element.Location.Row = Location.Row;
                        element.Location.Column = Location.Column;
                        element.Location.FullWidth = Location.FullWidth;
                        element.UpdatingState.ParentState = UpdatingState;
                        int index = Elements.Elements.IndexOf(this);

                        if (index >= 0)
                        {
                            Elements.Elements.Insert(Elements.Elements.IndexOf(this), element);
                        }
                    }
                }
            }

            DataLoaded = true;
        }
    }
}
