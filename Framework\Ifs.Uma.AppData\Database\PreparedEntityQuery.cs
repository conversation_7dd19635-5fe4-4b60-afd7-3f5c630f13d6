﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Database
{
    internal sealed class PreparedEntityQuery
    {
        public string ProjectionName { get; }
        public EntityQuery Query { get; }
        public ISelectSpec SelectSpec { get; }
        public IReadOnlyList<EntityQueryColumn> QueryColumns { get;  }
        public IEnumerable<string> UsedEntities { get;  }

        public PreparedEntityQuery(EntityQuery query, int? defaultTake, bool alwaysAddSystemAttributes)
        {
            ProjectionName = query.DataSource.ProjectionName;
            Query = query.Clone();

            if (!Query.Take.HasValue && defaultTake.HasValue)
            {
                Query.Take = defaultTake.Value;
            }

            SelectSpec = ToSelectSpec(alwaysAddSystemAttributes, out var columns);
            QueryColumns = columns;

            HashSet<string> entityNames = new HashSet<string>();
            entityNames.Add(query.DataSource.EntityName);
            foreach (IJoinSpec join in SelectSpec.Joins)
            {
                if (join.Table is ITableNameSpec table)
                {
                    entityNames.Add(RemoteNaming.ToEntityName(table.TableName));
                }
            }
            UsedEntities = entityNames.ToArray();
        }

        private ISelectSpec ToSelectSpec(bool alwaysAddSystemAttributes, out List<EntityQueryColumn> columns)
        {
            QueryScope scope = new QueryScope(Query.DataSource.ProjectionName, Query.DataSource.EntityName, Query.DataSource.Metadata);
            scope.EntityQueryAlias = "t0";

            QueryExpression queryExp = Query.DataSource.ToQueryExpression(scope.EntityQueryAlias);
            Expression where = GetWhereExpression(queryExp.Where);

            columns = CreateQueryColumns(alwaysAddSystemAttributes);
            ResultColumnExpression[] selectColumns = columns.Select(EntityQueryColumnToResult).ToArray();

            IEnumerable<SortExpression> sorts = GetSortExpressions(queryExp.Sorts);

            queryExp = IfsExpression.Query(queryExp.From, queryExp.Joins, where, sorts, false, selectColumns);

            ISelectSpec querySelectSpec = QuerySelectSpec.Create(queryExp, scope);
            return SqlSpec.CreateSelect(
                querySelectSpec.Columns,
                querySelectSpec.From,
                querySelectSpec.Joins,
                querySelectSpec.Where,
                querySelectSpec.GroupBy,
                OrderBy.Create(querySelectSpec.OrderBy.ToArray(), Query.Skip.GetValueOrDefault(0), Query.Take.GetValueOrDefault(0)),
                querySelectSpec.Distinct);
        }

        private ResultColumnExpression EntityQueryColumnToResult(EntityQueryColumn queryColumn)
        {
            if (queryColumn.RefName == null)
            {
                AttributePathInfo attribute = AttributePathInfo.Get(Query.DataSource.Metadata, Query.DataSource.ProjectionName, Query.DataSource.EntityName, queryColumn.Member.PropertyName);
                return IfsExpression.QueryResultColumn(IfsExpression.AttributeAccess(attribute), null);
            }
            else
            {
                AttributePathInfo attribute = AttributePathInfo.Get(Query.DataSource.Metadata, Query.DataSource.ProjectionName, Query.DataSource.EntityName, queryColumn.RefName + AttributePath.PathSeparator + queryColumn.Member.PropertyName);
                return IfsExpression.QueryResultColumn(IfsExpression.AttributeAccess(attribute), queryColumn.RefName + "$" + attribute.AttributeName);
            }
        }

        private List<EntityQueryColumn> CreateQueryColumns(bool alwaysAddSystemAttributes)
        {
            List<EntityQueryColumn> columns = new List<EntityQueryColumn>();

            foreach (IMetaDataMember member in Query.DataSource.Table.DataMembers)
            {
                if (ShouldSelectColumn(alwaysAddSystemAttributes, null, member.PropertyName, member))
                {
                    columns.Add(new EntityQueryColumn(null, Query.DataSource.Table, member, columns.Count));
                }
            }

            foreach (string refName in Query.GetRequiredExpands())
            {
                CpiReference reference = Query.DataSource.Metadata.FindReference(Query.DataSource.ProjectionName, Query.DataSource.EntityName, refName);
                if (reference != null)
                {
                    IMetaTable joinTable = Query.DataSource.Metadata.GetTableForEntityName(reference.Target);

                    if (joinTable != null)
                    {
                        foreach (IMetaDataMember member in joinTable.DataMembers)
                        {
                            if (ShouldSelectColumn(alwaysAddSystemAttributes, refName, member.PropertyName, member))
                            {
                                columns.Add(new EntityQueryColumn(refName, joinTable, member, columns.Count));
                            }
                        }
                    }
                }
            }

            return columns;
        }

        private bool ShouldSelectColumn(bool alwaysAddSystemAttributes, string refName, string attributeName, IMetaDataMember member)
        {
            if (alwaysAddSystemAttributes &&
                (attributeName == nameof(RemoteRow.RowId) ||
                 attributeName == nameof(RemoteRow.ObjId) ||
                 attributeName == nameof(RemoteRow.ObjVersion) ||
                 attributeName == PlatformServices.TimezoneService.Objsite ||
                 member.ServerPrimaryKey))
            {
                return true;
            }

            if (Query.SelectAttributes == null)
            {
                return true;
            }

            string selectName = refName == null ? attributeName : refName + "." + attributeName;
            return Query.SelectAttributes.Contains(selectName);
        }

        private Expression GetWhereExpression(Expression dataSourceWhere)
        {
            Expression where = null;

            AttributeExpression whereExpression = Query.GetWhereExpression();
            if (whereExpression != null)
            {
                Expression exp = whereExpression.ToExpression(Query.DataSource);
                where = exp;
            }

            if (Query.JsonWhereExpression != null)
            {
                Expression exp = Query.JsonWhereExpression.Expression;
                where = where == null ? exp : Expression.AndAlso(where, exp);
            }

            if (dataSourceWhere != null)
            {
                where = where == null ? dataSourceWhere : Expression.AndAlso(where, dataSourceWhere);
            }

            return where;
        }

        private IEnumerable<SortExpression> GetSortExpressions(IEnumerable<SortExpression> dataSourceSorts)
        {
            List<SortExpression> newSorts = new List<SortExpression>();

            foreach (AttributeSort sort in Query.Sorts)
            {
                AttributePathInfo attribute = AttributePathInfo.Get(Query.DataSource.Metadata, Query.DataSource.ProjectionName, Query.DataSource.EntityName, sort.AttributeName);
                if (attribute != null)
                {
                    newSorts.Add(IfsExpression.QuerySort(IfsExpression.AttributeAccess(attribute), sort.SortOrder));
                }
            }

            newSorts.AddRange(dataSourceSorts);

            if (newSorts.Count == 0)
            {
                foreach (IMetaDataMember member in Query.DataSource.Table.DataMembers)
                {
                    if (member.ServerPrimaryKey)
                    {
                        AttributePathInfo attribute = AttributePathInfo.Get(Query.DataSource.Metadata, Query.DataSource.ProjectionName, Query.DataSource.EntityName, member.PropertyName);
                        newSorts.Add(IfsExpression.QuerySort(IfsExpression.AttributeAccess(attribute), ESortOrder.Ascending));
                    }
                }
            }

            return newSorts;
        }
    }
}
