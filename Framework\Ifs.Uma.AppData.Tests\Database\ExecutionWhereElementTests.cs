﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Tests.Execution;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using Ifs.Uma.Tests.TestClasses;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Database
{
    [TestFixture]
    public class ExecutionWhereElementTests : FrameworkTest
    {
        private const string TstCustomerEntityName = "TstCustomer";

        [Test]
        public void Basic()
        {
            var vars = new Dictionary<string, object>();
            vars["Var1"] = 3;

            string json = @"{'==': [{'var':'CustomerName'}, {'var':'Var1'}] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("(t0.customer_name=@p0) @p0='3'", sql);
        }

        [Test]
        public void StructureVar()
        {
            var vars = new Dictionary<string, object>();
            RemoteRow row = new RemoteRow(RemoteNaming.ToTableName(TstCustomerEntityName));
            row["CustomerNo"] = 5;
            vars["Var1"] = row;

            string json = @"{'==': [{'var':'CustomerName'}, {'var':'Var1.CustomerNo'}] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("(t0.customer_name=@p0) @p0='5'", sql);
        }

        [Test]
        public void Add()
        {
            var vars = new Dictionary<string, object>();
            vars["Var1"] = 3;

            string json = @"{'==': [{'var':'CustomerName'}, {'+': [{'var':'Var1'}, 5.0]}] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("(t0.customer_name=(@p0+@p1)) @p0='3', @p1='5'", sql);
        }

        [Test]
        public void WhereAlias()
        {
            var vars = new Dictionary<string, object>();
            vars["CustomerName"] = "aaa";

            string json = @"{'==': [{'var':'i.CustomerName'}, {'var':'CustomerName'}] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("(t0.customer_name=@p0) @p0='aaa'", sql);
        }
        
        [Test]
        public void Or()
        {
            var vars = new Dictionary<string, object>();

            string json = @"{'or': [ {'==': [{'var':'CustomerName'}, 'aaa'] }, {'==': [{'var':'CustomerName'}, 'bbb'] }] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("((t0.customer_name=@p0) OR (t0.customer_name=@p1)) @p0='aaa', @p1='bbb'", sql);
        }

        [Test]
        public void In()
        {
            var vars = new Dictionary<string, object>();
            vars["Var1"] = "Test";

            string json = @"{'in': [{'var':'Var1'}, [ 'A', 'B' ] ] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("@p0 IN (@p1, @p2) @p0='Test', @p1='A', @p2='B'", sql);
        }

        [Test]
        public void DateTime_AddYears()
        {
            var vars = new Dictionary<string, object>();
            string json = "{ \"method\": [ \"DateTime.AddYears\", [ { \"var\": \"CustomerName\" }, 5 ] ] }";

            string sql = GetWhereSql(json, vars);

            Assert.AreEqual("IfsDateTimeAddYearsFunction(t0.customer_name, @p0) @p0='5'", sql);
        }
        [Test]
        public void DateTime_AddMonths()
        {
            var vars = new Dictionary<string, object>();
            string json = "{ \"method\": [ \"DateTime.AddMonths\", [ { \"var\": \"CustomerName\" }, 5 ] ] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("IfsDateTimeAddMonthsFunction(t0.customer_name, @p0) @p0='5'", sql);
        }

        [Test]
        public void DateTime_AddDays()
        {
            var vars = new Dictionary<string, object>();
            string json = "{ \"method\": [ \"DateTime.AddDays\", [ { \"var\": \"CustomerName\" }, 5 ] ] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("IfsDateTimeAddDaysFunction(t0.customer_name, @p0) @p0='5'", sql);
        }

        [Test]
        public void DateTime_AddHours()
        {
            var vars = new Dictionary<string, object>();
            string json = "{ \"method\": [ \"DateTime.AddHours\", [ { \"var\": \"CustomerName\" }, 5 ] ] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("IfsDateTimeAddHoursFunction(t0.customer_name, @p0) @p0='5'", sql);
        }

        [Test]
        public void DateTime_AddMinutes()
        {
            var vars = new Dictionary<string, object>();
            string json = "{ \"method\": [ \"DateTime.AddMinutes\", [ { \"var\": \"CustomerName\" }, 5 ] ] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("IfsDateTimeAddMinutesFunction(t0.customer_name, @p0) @p0='5'", sql);
        }

        [Test]
        public void DateTime_AddSeconds()
        {
            var vars = new Dictionary<string, object>();
            string json = "{ \"method\": [ \"DateTime.AddSeconds\", [ { \"var\": \"CustomerName\" }, 5 ] ] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("IfsDateTimeAddSecondsFunction(t0.customer_name, @p0) @p0='5'", sql);
        }

        [Test]
        public void System_DateTime()
        {
            var vars = new Dictionary<string, object>();
            string json = "{ \">\": [ { \"var\": \"CustomerName\" }, { \"method\": [ \"System.DateTime\" ] } ] }";

            string sql = GetWhereSql(json, vars);
            Assert.AreEqual("(t0.customer_name>IfsDateTimeNow)", sql);
        }

        [Test]
        public void DateTime_Timestamp()
        {
            string json = "{ \">\": [ { \"var\": \"CustomerName\" }, { \"method\": [ \"DateTime.Timestamp\" ] } ] }";

            string sql = GetWhereSql(json, null);
            Assert.AreEqual("(t0.customer_name>IfsDateTimeNow)", sql);
        }

        [Test]
        public void DateTime_Date0()
        {
            string json = "{ \">\": [ { \"var\": \"CustomerName\" }, { \"method\": [ \"DateTime.Date\" ] } ] }";

            string sql = GetWhereSql(json, null);
            Assert.AreEqual("(t0.customer_name>IfsDateTimeNowDate)", sql);
        }
        
        [Test]
        public void DateTime_Date1()
        {
            string json = "{ \">\": [ { \"var\": \"CustomerName\" }, { \"method\": [ \"DateTime.Date\",  [ { \"var\": \"CustomerName\" } ]],} ] }";

            string sql = GetWhereSql(json, null);
            Assert.AreEqual("(t0.customer_name>IfsDateTimeDateFunction(t0.customer_name))", sql);
        }

        [Test]
        public void DateTime_Time()
        {
            string json = "{ \">\": [ { \"var\": \"CustomerName\" }, { \"method\": [ \"DateTime.Time\",  [ { \"var\": \"CustomerName\" } ]],} ] }";

            string sql = GetWhereSql(json, null);
            Assert.AreEqual("(t0.customer_name>IfsDateTimeTimeFunction(t0.customer_name))", sql);
        }

        [Test]
        public void String_Like()
        {
            string json = "{ \"method\": [ \"String.Like\",  [ { \"var\": \"CustomerName\" }, \"CUST1%\" ]]}";

            string sql = GetWhereSql(json, null);
            Assert.AreEqual("(t0.customer_name LIKE @p0) @p0='CUST1%'", sql);
        }

        [Test]
        public void String_NotLike()
        {
            string json = "{ \"!\": { \"method\": [ \"String.Like\",  [ { \"var\": \"CustomerName\" }, \"CUST1%\" ]]} }";

            string sql = GetWhereSql(json, null);
            Assert.AreEqual(" NOT (t0.customer_name LIKE @p0) @p0='CUST1%'", sql);
        }

        [Test]
        public void MethodWithAnd()
        {
            string json = @"{'and': [ {'==': [{'var':'CustomerName'}, 'bbb'] }, { 'method': [ 'String.Like',  [ { 'var': 'CustomerName' }, 'CUST1%' ]]} ] }";
            
            string sql = GetWhereSql(json, null);
            Assert.AreEqual("((t0.customer_name=@p0) AND (t0.customer_name LIKE @p1)) @p0='bbb', @p1='CUST1%'", sql);
        }

        private string GetWhereSql(string json, Dictionary<string, object> vars)
        {
            JObject where = JObject.Parse(json);
            IExpressionRunner expressionRunner = Resolve<IExpressionRunner>();
            TestExecutionContext context = TestExecutor.CreateExecutionContext(TestOfflineProjection, expressionRunner, vars);

            EntityDataSource dataSource = EntityDataSource.FromEntity(context.Metadata, TestOfflineProjection, TstCustomerEntityName);

            JsonWhereExpression whereExpression = new JsonWhereExpression(dataSource, where, context, "i");

            QueryScope queryScope = new QueryScope(dataSource.ProjectionName, dataSource.EntityName, dataSource.Metadata);
            queryScope.EntityQueryAlias = "t0";
            queryScope.AddAlias(queryScope.EntityQueryAlias, dataSource.Table);

            Expression exp = QueryRetargeter.Rewrite(whereExpression.Expression, queryScope);
            ISqlExpression sqlExpression = SqlExpression.Create(exp);
            IWhereElement element = WhereElement.Create(EOperand.And, sqlExpression);

            return TestSqlBuilder.WriteSql(element);
        }
    }
}
