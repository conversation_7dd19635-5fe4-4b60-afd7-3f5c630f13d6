﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Database
{
    // Use this as the superclass for exceptions raised in the database driver.
    public class DbException : Exception
    {
        public DbExceptionType ExceptionType { get; protected set; }

        public DbException()
        {
        }

        public DbException(string message)
            : base(message)
        {
        }

        public DbException(string message, Exception inner)
            : base(message, inner)
        {
        }

        public DbException(string message, Exception inner, DbExceptionType exceptionType)
            : base(message, inner)
        {
            ExceptionType = exceptionType;
        }
    }

    public enum DbExceptionType
    {
        Unknown,
        NotNullConstraintFailed,
        KeyValueNotFound
    }
}
