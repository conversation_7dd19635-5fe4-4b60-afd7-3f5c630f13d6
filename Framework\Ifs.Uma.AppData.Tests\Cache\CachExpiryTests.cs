﻿using System;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.Metadata.Cpi;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Cache
{
    [TestFixture]
    public class CachExpiryTests
    {
        [Test]
        public void Never()
        {
            CacheExpiry expiry = new CacheExpiry(InvalidationPeriod.Never, TimeSpan.Zero);
            Assert.IsFalse(expiry.HasExpired(DateTime.MinValue));
            Assert.IsFalse(expiry.HasExpired(DateTime.MaxValue));
        }

        [Test]
        public void Weekly()
        {
            CacheExpiry expiry = new CacheExpiry(InvalidationPeriod.Weekly, TimeSpan.Zero);
            Assert.IsFalse(expiry.HasExpired(DateTime.Now));
            Assert.IsFalse(expiry.HasExpired(DateTime.MaxValue));
            Assert.IsTrue(expiry.HasExpired(DateTime.MinValue));
            Assert.IsTrue(expiry.HasExpired(DateTime.Now.Subtract(TimeSpan.FromDays(8))));
        }

        [Test]
        public void Daily()
        {
            CacheExpiry expiry = new CacheExpiry(InvalidationPeriod.Daily, TimeSpan.Zero);
            Assert.IsFalse(expiry.HasExpired(DateTime.Now));
            Assert.IsFalse(expiry.HasExpired(DateTime.MaxValue));
            Assert.IsTrue(expiry.HasExpired(DateTime.MinValue));
            Assert.IsTrue(expiry.HasExpired(DateTime.Now.Subtract(TimeSpan.FromDays(1))));
        }

        [Test]
        public void After()
        {
            CacheExpiry expiry = new CacheExpiry(InvalidationPeriod.After, TimeSpan.FromMinutes(30));
            Assert.IsFalse(expiry.HasExpired(DateTime.Now));
            Assert.IsFalse(expiry.HasExpired(DateTime.MaxValue));
            Assert.IsFalse(expiry.HasExpired(DateTime.Now.Subtract(TimeSpan.FromMinutes(25))));
            Assert.IsTrue(expiry.HasExpired(DateTime.MinValue));
            Assert.IsTrue(expiry.HasExpired(DateTime.Now.Subtract(TimeSpan.FromMinutes(35))));
        }
    }
}
