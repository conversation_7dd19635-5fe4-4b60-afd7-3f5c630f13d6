// Shared
Platforms=iOS,Android,Windows
Name=IFS MWO Service
AppName=ServiceEngApp
RedirectUri=ifsmwoservice
RemoteAssistance=true
SignatureService=false
LocationEnabled=true
LidarService=true
PushNotification=true

// iOS
iOSDisplayName=IFS MWO Service
// Below must be the one having the InHouse suffix, the build script will remove it when building the store app
BundleIdentifier=com.ifs.cloud.Service.InHouse
BundleName=IFS MWO Service

// NOTE: iOS usage descriptions are always needed since we don't remove code for things like media attachments and location APIs
// So it's okay to add some dummy text for apps that don't really use those features
NSLocationWhenInUseUsageDescription=Engineers&apos; locations are used to make planning easier and to enable us to provide arrival estimates for assignments
NSLocationAlwaysAndWhenInUseUsageDescription=Engineers&apos; locations are used to make planning easier and to enable us to provide arrival estimates for assignments
NSCameraUsageDescription=Camera access is required to scan barcodes, add pictures to assignments, and for remote assistance calls
NSPhotoLibraryUsageDescription=Photo access is required to enable pictures to be added to assignments
NSPhotoLibraryAddUsageDescription=Photo access is required to save documents and media items
NSMicrophoneUsageDescription=This is used for remote assistance calls

// Android
AndroidDisplayName=IFS MWO Service
AndroidPackageName=com.ifs.cloud.Service

// Windows
WindowsDisplayName=IFS MWO Service
WindowsDescription=IFS MWO Service
WindowsShortName=IFS MWO Service
IdentityName=IFS.IFSMWOService
PhoneProductId=dadda058-e46c-48af-83f7-4a4bdf5530e7
// Below are in Package.StoreAssociation and Package.xml
ReservedName=IFS MWO Service
LandingUrl=https://developer.microsoft.com/dashboard/Application?appId=9MSQBC5DB93V