﻿using System.Linq;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Utility;
using MessageType = Ifs.Uma.AppData.Messages.MessageType;

namespace Ifs.Uma.AppData.Tests.Messages
{
    internal sealed class MessageTestContext
    {
        private readonly FwDataContext _db;
        private readonly ILogger _logger;
        private readonly ClientKeysMapper _keyMapper;
        private long _serverMessageId;

        public MessageTestContext(FwDataContext db)
        {
            _db = db;
            _logger = new DebugLogger();

            ClientKeysMapper keyMapper = new ClientKeysMapper(_logger);
            keyMapper.Load(db.Model, db.ClientKeysMap.ToArray());
            _keyMapper = keyMapper;
        }

        public DataChangeSet ExecuteMessage(MessageType type, string message, long? clientRelatedMessageId = null, long? transactionId = null)
        {
            _serverMessageId++;

            MessageIn msg = MessageIn.Create(type);
            msg.ServerMessageId = _serverMessageId;
            msg.ClientRelatedMessageId = clientRelatedMessageId;
            msg.MessageData = message.Trim();
            msg.TransactionId = transactionId ?? 0;

            DataChangeSet changeSet = new DataChangeSet();
            msg.Execute(_db, _keyMapper, _logger, changeSet, false);
            return changeSet;
        }
    }
}
