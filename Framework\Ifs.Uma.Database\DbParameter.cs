﻿using System;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Database
{
    public abstract class DbParameter
    {
        /// <summary>
        /// This is the parameter name that is inserted into the SQL command itself.
        /// The parameter name that is bound to the SQL statement may differ.
        /// The parameter name could just be ? for every parameter
        /// </summary>
        public abstract string Name { get; }
        public abstract TypeCode ValueCode { get; }
        public abstract int ValueIndex { get; }
        public abstract IMetaEnumeration Enumeration { get; }
        
        /// <summary>
        /// This is a unique "Id" for every parameter attached to the command
        /// Often the Name is formed by adding a prolog to this Id
        /// </summary>
        public abstract string Id { get; }

        public object Value
        {
            get { return m_value; }
            set
            {
                m_value = ValidateValue(value);
            }
        }

        protected DbParameter()
        {
        }

        protected virtual object ValidateValue(object value)
        {
            if (value == null) return null;
            switch (ValueCode)
            {
                case TypeCode.Boolean:
                    return ObjectConverter.ToBoolean(value);
                case TypeCode.ByteArray:
                    return ObjectConverter.ToByteArray(value);
                case TypeCode.DateTime:
                    return ObjectConverter.ToDateTime(value);
                case TypeCode.Decimal:
                    return ObjectConverter.ToDecimal(value);
                case TypeCode.Double:
                    return ObjectConverter.ToDouble(value);
                case TypeCode.Enumeration:
                    if (Enumeration != null)
                    {
                        return Enumeration.ServerValue(value);
                    }
                    break;
                case TypeCode.Guid:
                    return ObjectConverter.ToGuid(value);
                case TypeCode.Int16:
                    return ObjectConverter.ToShort(value);
                case TypeCode.Int32:
                    return ObjectConverter.ToInt(value);
                case TypeCode.Int64:
                    return ObjectConverter.ToLong(value);
                case TypeCode.String:
                    return ObjectConverter.ToString(value);
            }
            throw new ArgumentOutOfRangeException("value");
        }

        private object m_value;

        public override string ToString()
        {
            return m_value != null ?
                string.Format(System.Globalization.CultureInfo.CurrentCulture,
                    Strings.ParameterValueFormat, Name, ObjectConverter.ToPrettyString(m_value)) :
                string.Format(System.Globalization.CultureInfo.CurrentCulture,
                    Strings.ParameterNullFormat, Name);
        }
    }
}
