﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.Convert
{
    [TestFixture]
    public class ConvertToBooleanTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.Convert.ConvertToBooleanTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("true", ExpectedResult = true)]
        [TestCase("True", ExpectedResult = true)]
        [TestCase("TRUE", ExpectedResult = true)]
        [TestCase("false", ExpectedResult = false)]
        [TestCase("False", ExpectedResult = false)]
        [TestCase("FALSE", ExpectedResult = false)]
        [TestCase(1, ExpectedResult = true)]
        [TestCase(0, ExpectedResult = false)]
        [TestCase(1.1, ExpectedResult = true)]
        [TestCase(2, ExpectedResult = true)]
        [TestCase(-1, ExpectedResult = true)]
        [TestCase((long)1, ExpectedResult = true)]
        [TestCase("HelloWorld", ExpectedResult = null)]
        [TestCase(null, ExpectedResult = null)]
        public async Task<bool?> Convert_ToBoolean(object input)
        {
            _params["TextInput"] = input;
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToBoolean", _params);

            CheckResults(result);

            return result?.Value as bool?;
        }

        private static void CheckResults(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
