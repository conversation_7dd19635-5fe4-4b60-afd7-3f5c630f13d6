﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Elements.Groups;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Framework.UI.RelatedPages;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Assistants
{
    public sealed class AssistantPage : AssistantBase, IAssistantRunner
    {
        public CommandBlock CustomCommands { get; }

        private AssistantStep _currentStep;
        public AssistantStep CurrentStep
        {
            get { return _currentStep; }
            private set
            {
                if (_currentStep != value)
                {
                    AssistantStep temp = _currentStep;
                    AssistantNavigationDirection direction;
                    
                    if (value == null && IsCustom)
                    {
                        direction = AssistantNavigationDirection.Finished; // this usually indicates a Custom Assistant at the final step
                    }
                    else 
                    {
                        direction = GetDirection(value);
                    } 

                    if (_currentStep?.Commands != null)
                    {
                        _currentStep.Commands.CommandExecuted -= CommandExecuted;
                    }

                    if (_currentStep?.Elements != null)
                    {
                        foreach (GroupElement group in _currentStep.Elements.Elements.Where(x => x is GroupElement))
                        {
                            if (group.Form != null)
                            {
                                foreach (Field field in group.Form.AllFields)
                                {
                                    field.InteractionFinished -= Field_InteractionFinished;
                                    field.TextScanned -= Field_TextScanned;
                                }
                            }
                        }

                        foreach (ListElement list in _currentStep.Elements.Elements.Where(x => x is ListElement))
                        {
                            list.CommandExecuted -= CommandExecuted;
                        }
                    }

                    _currentStep = value;

                    if (_currentStep?.Elements != null)
                    {
                        foreach (ListElement list in _currentStep.Elements.Elements.Where(x => x is ListElement))
                        {
                            list.CommandExecuted += CommandExecuted;

                            bool isSingleListPage = MetadataExtensions.IsSingleListPage(_currentStep.CpiStep.Content);
                            list.DisplayState = (isSingleListPage || _currentStep.IsSingleStep) ? ElementDisplayState.Expanded : ElementDisplayState.Normal;
                        }
                    }

                    if ((CpiAssistant.SaveMode == CpiSaveMode.OnLostFocus || _shouldProceedOnCarriageReturn) && _currentStep?.Elements != null)
                    {
                        foreach (GroupElement group in _currentStep.Elements.Elements.Where(x => x is GroupElement))
                        {
                            if (group.Form != null)
                            {
                                foreach (Field field in group.Form.AllFields)
                                {
                                    if (CpiAssistant.SaveMode == CpiSaveMode.OnLostFocus)
                                    {
                                        field.InteractionFinished += Field_InteractionFinished;
                                    }

                                    if (_shouldProceedOnCarriageReturn)
                                    {
                                        if (field is EntryField entryField)
                                        {
                                            entryField.ShowSoftKeyboardOnFocus = false;
                                        }

                                        field.TextScanned += Field_TextScanned;
                                    }
                                }
                            }
                        }
                    }

                    NotifyDefaultValuesForFields(direction);

                    if (_currentStep?.Commands != null)
                    {
                        _currentStep.Commands.CommandExecuted += CommandExecuted;
                    }

                    OnPropertyChanged(nameof(CurrentStep));
                    CurrentStepChanged?.Invoke(this, new AssistantNavigationEventArgs(direction, temp, value));

                    IsActiveStepModified = false;
                    IsActiveStepValid = _currentStep != null && _currentStep.CalculateIsValid();
                }
            }
        }

        public List<AssistantStep> Steps { get; } = new List<AssistantStep>();

        public event EventHandler<AssistantNavigationEventArgs> CurrentStepChanged;

        private bool _backCommandRunning;
        private bool _saveAllowed = true;
        private readonly bool _shouldProceedOnCarriageReturn;
        private readonly IMetadata _metadata;
        private readonly ElementCreator _elementCreator;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IExpressionRunner _expressionRunner;
        private readonly IDataHandler _dataHandler;
        private readonly IInsightsLogger _insightsLogger;
        private readonly IPerfLogger _perfLogger;
        private readonly IRoamingProfile _roamingProfile;
        private MetadataPageNavParam _navParam;

        public AssistantPage(IEventAggregator eventAggregator, IDialogService dialogService, IDataHandler data, INavigator navigator,
            IMetadata metadata, ILogger logger, IPerfLogger perfLogger, IInsightsLogger insightsLogger, ElementCreator elementCreator, ICommandExecutor commandExecutor,
            IExpressionRunner expressionRunner, IRoamingProfile roamingProfile, IAppParameters appParameters)
            : base(eventAggregator, dialogService)
        {
            _metadata = metadata;
            _elementCreator = elementCreator;
            _commandExecutor = commandExecutor;
            _expressionRunner = expressionRunner;
            _dataHandler = data;
            _insightsLogger = insightsLogger;
            _perfLogger = perfLogger;
            _roamingProfile = roamingProfile;

            Classification = PageClassification.Assistant;

            Data = new PageData(logger, metadata, data);
            Data.DefaultViewData.CommandsEnabledOnEmpty = true;
            Data.DefaultViewData.CommandsEnabledOnHasChanges = true;
            Data.DefaultViewData.Record.PropertyChanged += Record_PropertyChanged;
            Data.DefaultViewData.Record.DataChanged += Record_DataChanged;
            Data.Page = this;

            _shouldProceedOnCarriageReturn = appParameters.ShouldProceedOnCarriageReturn();

            Commands = new AssistantCommandBlock(UpdatingState, metadata, this, _commandExecutor, _expressionRunner)
            {
                PageData = Data
            };

            CustomCommands = new CommandBlock(UpdatingState, _metadata, _commandExecutor, _expressionRunner, true)
            {
                PageData = Data
            };
            CustomCommands.CommandExecuted += CommandExecuted;

            RelatedPages = new RelatedPagesList(UpdatingState, metadata, _commandExecutor, expressionRunner, data, dialogService, navigator)
            {
                PageData = Data
            };

            commandExecutor.Page = this;
        }

        private void InitializeViewState()
        {
            IsActiveStepDirty = false;
            IsActiveStepModified = false;
        }

        private void Record_DataChanged(object sender, EventArgs e)
        {
            IsActiveStepModified = true;
            IsActiveStepValid = CurrentStep != null && CurrentStep.CalculateIsValid();

            UpdateTitle();
        }

        private void Record_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(RecordData.HasChanges))
            {
                HasChanges = Data.DefaultViewData.Record.HasChanges;
                IsActiveStepDirty = HasChanges;
            }
        }

        private void NotifyDefaultValuesForFields(AssistantNavigationDirection direction)
        {
            if (_currentStep?.Elements.Elements == null)
            {
                return;
            }

            foreach (GroupElement group in _currentStep.Elements.Elements.Where(x => x is GroupElement))
            {
                if (group.Form != null)
                {
                    foreach (Field field in group.Form.AllFields)
                    {
                        if (group.IsVisible && direction != AssistantNavigationDirection.Backward)
                        {
                            field.NotifyDefaultValueForField();
                        }
                    }
                }
            }
        }

        protected override async void OnStoredDataChangedAsync(DataChangeSet changeSet)
        {
            base.OnStoredDataChangedAsync(changeSet);

            StatusIconObject = await StatusIcon.GetStatusIconOnChangeAsync(_metadata, _dataHandler, changeSet) ?? StatusIconObject;

            CurrentStep?.Elements?.NotifyStoredDataChanged(changeSet);
            CurrentStep?.Elements?.SetInitialFocus();

            if (CurrentStep?.Elements?.HasRepeatingSections == true)
            {
                await CurrentStep.CheckAllElementsLoaded();
            }
        }

        public async Task RunBackCommand()
        {
            if (_backCommandRunning)
            {
                return;
            }

            _backCommandRunning = true;
            using (DialogService.ShowLoadingDialog(Strings.LoadingEllipsis, false))
            {
                // Null check is done in TryRunAsync
                await CpiAssistant.BackCommand.TryRunAsync(ProjectionName, Data.DefaultViewData, _commandExecutor);
            }
            _backCommandRunning = false;
        }

        public override async void OnNavigatedTo(NavigatedToArgs args)
        {
            base.OnNavigatedTo(args);

            MetadataPageNavParam param;
            if (args.NavigationMode == NavigationMode.Refresh)
            {
                param = _navParam;
            }
            else
            {
                param = args.GetNavParam<MetadataPageNavParam>();
            }

            await LoadPageAsync(param);
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter param)
        {
            _navParam = param as MetadataPageNavParam;

            if (_navParam?.ProjectionName != null && _navParam?.Page != null)
            {
                ProjectionName = _navParam.ProjectionName;
                CpiAssistant assistant = _metadata.FindAssistant(ProjectionName, _navParam.Page);

                if (assistant != null)
                {
                    using (_perfLogger.Track("PageLoad", ProjectionName + "." + assistant.Name))
                    {
                        _insightsLogger?.TrackAppFeature("Assistant-" + ProjectionName + "." + assistant.Name);
                        Logger.Trace("Navigate: Assistant-" + ProjectionName + "." + assistant.Name);

                        try
                        {
                            return await LoadAssistant(ProjectionName, assistant);
                        }
                        catch (Exception ex)
                        {
                            await HandleException(ex);
                        }
                    }
                }
                else
                {
                    Logger.Error($"Could not find assistant {ProjectionName}.{_navParam.Page}");
                }
            }

            return false;
        }

        public async Task<bool> LoadAssistantAsModal(string projectionName, CpiAssistant assistant)
        {
            IsModal = true;
            return await LoadAssistant(projectionName, assistant);
        }

        private async Task<bool> LoadAssistant(string projectionName, CpiAssistant assistant)
        {
            StatusIconObject = await StatusIcon.GetStatusIconAsync(_metadata, _dataHandler) ?? StatusIconObject;

            ProjectionName = projectionName ?? throw new ArgumentNullException(nameof(projectionName));
            CpiAssistant = assistant ?? throw new ArgumentNullException(nameof(assistant));

            if (assistant.InitCommand == null && assistant.CancelCommand == null && assistant.FinishCommand == null)
            {
                // this indicates a Custom Assistant - assign dummy object to Finish Command and set IsCustom
                assistant.FinishCommand = new CpiCommand();
                IsCustom = true;
            }

            Name = CpiAssistant.Name;
            Data.DataSource = EntityDataSource.FromEntitySet(_metadata, CpiAssistant.DatasourceProjection ?? ProjectionName, CpiAssistant.DatasourceEntitySet);
            Data.PageSettings = _roamingProfile.GetSettings("Assistants", ProjectionName, assistant.Name);

            // The entity should allow Create and Update operations
            CpiEntity entity = _metadata.FindEntity(ProjectionName, CpiAssistant.Entity);
            CpiCrudType entityCrudType = _dataHandler.GetCrudType(ProjectionName, CpiAssistant.Entity);
            if (entity != null && !entityCrudType.CanUse(CpiCrudType.CreateAndUpdate))
            {
                throw new InvalidOperationException($"{entity.Name} has not been granted the create/update permissions needed to proceed. Please grant them through the web client.");
            }
            else if (entity == null)
            {
                _saveAllowed = false;
            }

            Name = assistant.Name;
            UpdateTitle();

            InitializeViewState();

            if (!IsModal) // In modal assistants, we load the record before this point
            {
                ExecuteResult result = await Data.DefaultViewData.Record.LoadNewRecordAsync(ProjectionName, CpiAssistant.Entity);

                if (!await CheckExecuteResult(result))
                {
                    return false;
                }
            }

            if (!await SetupAndInitialize())
            {
                HasChanges = false;
                return false;
            }

            // If only the values set are the keys, then try to load the record from local DB if it exists
            if (entity?.Keys != null && Data.DataSource != null && Data.DefaultViewData.Record.ChangedMembers.Any())
            {
                bool onlyKeyColumnsSet = true;

                foreach (string column in Data.DefaultViewData.Record.ChangedMembers)
                {
                    if (entity.Keys.Any(x => x == column) == false)
                    {
                        onlyKeyColumnsSet = false;
                        break;
                    }
                }

                if (onlyKeyColumnsSet)
                {
                    EntityQuery query = new EntityQuery(Data.DataSource);
                    query.SetFilter(Data.DefaultViewData.Record.ToPrimaryKey());
                    await Data.DefaultViewData.Record.LoadRecordAsync(query, false);
                    Data.DefaultViewData.PageData.Page = this;
                }
            }

            Data.DefaultViewData.Record.ResetHasChanges();

            CreateSteps();

            if (!Steps.Any())
            {
                throw new InvalidOperationException("No steps defined for this assistant.");
            }

            CustomCommands.Load(ProjectionName, assistant.CommandGroups, true, true, true, null, null);

            Commands.Load(assistant);
            OnStepsLoaded(EventArgs.Empty);

            await GoToFirstStep();

            return true;
        }

        private async Task<bool> SetupAndInitialize()
        {
            if (!string.IsNullOrEmpty(_navParam?.Action) && CpiAssistant.Setups != null)
            {
                if (CpiAssistant.Setups.TryGetValue(_navParam.Action, out CpiCommand command))
                {
                    CommandOptions options = new CommandOptions();
                    options.Vars = _navParam.ActionParameters?.ToDictionary();
                    if (!await command.TryRunAsync(ProjectionName, Data.DefaultViewData, _commandExecutor, options))
                    {
                        return false;
                    }
                }
            }

            return await CpiAssistant.InitCommand.TryRunAsync(ProjectionName, Data.DefaultViewData, _commandExecutor);
        }

        private async Task<bool> GoToFirstStep()
        {
            int startStepIndex = 0;

            if (StartStep.HasValue)
            {
                int idx = StartStep.Value - 1;

                CpiExpression enabledExpression = Steps[idx].CpiStep.OfflineEnabled ?? Steps[idx].CpiStep.Enabled;

                if (idx < 0 || idx >= Steps.Count)
                {
                    Logger.Log($"Initial step ({idx}) is not in the range of active steps [1 .. {Steps.Count}].", MessageType.Error);
                }
                else if (enabledExpression != null)
                {
                    bool enabled = _expressionRunner.RunCheck(enabledExpression, Data.DefaultViewData, true);

                    if (!enabled)
                    {
                        Logger.Log($"Cannot start on initial step {idx} because it is not enabled.", MessageType.Error);
                    }
                }
                else
                {
                    startStepIndex = idx;
                }
            }

            return await SwitchStep(startStepIndex);
        }

        private async Task<bool> SwitchStep(int index)
        {
            AssistantStep step = Steps[index];

            CpiExpression enabledExpression = step?.CpiStep?.OfflineEnabled ?? step?.CpiStep?.Enabled;
            if (enabledExpression != null)
            {
                bool enabled = _expressionRunner.RunCheck(enabledExpression, Data.DefaultViewData, true);
                if (!enabled)
                {
                    int newIndex;
                    if (GetDirection(step) == AssistantNavigationDirection.Forward)
                    {
                        newIndex = index + 1;
                        if (newIndex < Steps.Count)
                        {
                            return await SwitchStep(newIndex);
                        }
                        else
                        {
                            return await FinishAssistant();
                        }
                    }
                    else
                    {
                        newIndex = index - 1;
                        if (newIndex < 0)
                        {
                            //Don't navigate back if there is no enabled earlier step
                            return false;
                        }

                        return await SwitchStep(newIndex);
                    }
                }
            }

            ActiveStep = index + 1;
            CurrentStep = step;

            Commands.LoadStep(step);

            return true;
        }

        private void CommandExecuted(object sender, CommandExecutedEventArgs e)
        {
            if (IsModal && !e.Result.Failed && e.Result?.Value != null)
            {
                Result = e.Result;
                OnClosed(EventArgs.Empty);
            }

            CurrentStep.Elements.SetInitialFocus();
        }

        public async Task InvokeCommandFromJs(string json)
        {
            try
            {
                JObject jo = JObject.Parse(json);

                string commandPart = jo.ContainsKey("command") ? jo.SelectToken("command").Value<string>() : string.Empty;
                string dataPart = jo.ContainsKey("data") ? jo.SelectToken("data").Value<string>() : string.Empty;
                string dateTime = jo.ContainsKey("date") ? jo.SelectToken("date").Value<string>() : string.Empty;
                string knownDateTime = jo.ContainsKey("utcTimeValue") ? jo.SelectToken("utcTimeValue").Value<string>() : string.Empty;

                string[] commandParts = commandPart.Split(new[] { '_' }, 2);

                CommandItem command = null;
                if (commandParts[0] == Name)
                {
                    CommandGroup group = CustomCommands.CommandGroups.FirstOrDefault();
                    if (group != null)
                    {
                        command = group.FirstOrDefault(x => x is CpiCommandItem cpiCommandItem && cpiCommandItem.Name == commandPart);
                    }
                }
                else if (commandParts[0] == CurrentStep.CpiStep.Name)
                {
                    CommandGroup group = CurrentStep.Commands.CommandGroups.FirstOrDefault();
                    if (group != null)
                    {
                        command = group.FirstOrDefault(x => x is CpiCommandItem cpiCommandItem && cpiCommandItem.Name == commandPart);
                    }
                }
                else if (dateTime != string.Empty)
                {
                    await DialogService.ShowTimeZoneDialog(dateTime);
                }
                else if (knownDateTime != string.Empty)
                {
                    string[] timeZoneInformation = jo.SelectToken("info").Value<string>().Split('#');
                    string[] timeZoneTitle = jo.SelectToken("title").Value<string>().Split('#');
                    List<TimeZoneUtcInfo> tzInfo = new List<TimeZoneUtcInfo>();

                    for (int i = 0; timeZoneInformation.Length > i; i++)
                    {
                        TimeZoneUtcInfo utcInfo = new TimeZoneUtcInfo
                        {
                            TimeZoneInfo = timeZoneInformation[i],
                            Title = timeZoneTitle[i]
                        };
                        tzInfo.Add(utcInfo);
                    }
                    await DialogService.ShowTimeZoneDialog(tzInfo);
                }
                else
                {
                    ListElement listElement = null;
                    ElementList repeatingElements = new ElementList(UpdatingState, _elementCreator);

                    foreach (ListElement repeatElement in CurrentStep.Elements.Elements.Where(x => (x is ListElement list1 && list1.CardName == commandParts[0]) || (x is ListElement list2 && list2.ListName == commandParts[0])))
                    {
                        repeatingElements.Elements.Add(repeatElement);
                    }

                    if (repeatingElements.Elements.Count > 1)
                    {
                        listElement = (ListElement)repeatingElements.Elements.FirstOrDefault(x => x is ListElement list && list.Record?.ToPrimaryKey()?.ToKeyRef() == dataPart);
                    }
                    else
                    {
                        listElement = (ListElement)repeatingElements.Elements.ElementAt(0);
                    }

                    CpiCommand cardCommand = _metadata.FindCommand(ProjectionName, commandPart);

                    if (cardCommand != null && cardCommand.Selection != CpiCommandSelection.Global)
                    {
                        ListElementItem listItem = null;

                        foreach (ListElement element in repeatingElements.Elements.Where(x => (x is ListElement)))
                        {
                            listItem = element.ListData.Items.FirstOrDefault(x => x is ListElementItem item && item.Record?.ToPrimaryKey()?.ToKeyRef() == dataPart);
                            if (listItem != null)
                            {
                                break;
                            }
                        }

                        if (listItem != null)
                        {
                            EntityQuery query = new EntityQuery(EntityDataSource.FromEntity(_metadata, ProjectionName, listItem.DataSource.EntityName));
                            ObjPrimaryKey pk = ObjPrimaryKey.FromKeyRef(_metadata.MetaModel, query.DataSource.EntityName, dataPart);
                            query.SetFilter(pk);
                            EntityQueryResult result = await _dataHandler.GetRecordsAsync(query, CancellationToken.None);

                            if (result.Records.Any())
                            {
                                RecordData recordData = new RecordData(Logger, _metadata, _dataHandler);
                                PageData pageData = new PageData(recordData);
                                pageData.DefaultViewData.Record.LoadRecord(ProjectionName, result.Records.FirstOrDefault());
                                pageData.Page = this;
                                await _commandExecutor.ExecuteAsync(ProjectionName, pageData.DefaultViewData, cardCommand);
                            }
                        }
                    }
                    else if (listElement != null && cardCommand != null && cardCommand.Selection == CpiCommandSelection.Global)
                    {
                        listElement.ViewData.PageData.Page = this;
                        await _commandExecutor.ExecuteAsync(ProjectionName, listElement.ViewData, cardCommand);
                    }
                    else
                    {
                        Logger.Error($"Could not determine the owner of command '{commandPart}'");
                    }
                }

                if (command != null)
                {
                    await command.ExecuteAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);
            }
        }

        private async Task<ExecuteResult> SaveRecord()
        {
            if (!_saveAllowed)
            {
                // Ignore if saving is not allowed (for structures)
                Logger.Log($"Skipping save record for assistant {CpiAssistant.Name} since the entity is not defined.", MessageType.Information);
                return ExecuteResult.Ok;
            }

            if (Data.DefaultViewData.Record?.GetRemoteRow() != null)
            {
                Data.DefaultViewData.Record.GetRemoteRow().EntitySetName = CpiAssistant.DatasourceEntitySet;
            }

            if (Data?.DefaultViewData?.Record != null)
            {
                return await Data.DefaultViewData.Record.SaveRecordAsync();
            }

            return ExecuteResult.None;
        }

        private void CreateSteps()
        {
            Steps.Clear();

            foreach (CpiAssistantStep cpiStep in CpiAssistant.Steps)
            {
                AssistantStep step = CreateStep(cpiStep);
                Steps.Add(step);
            }

            if (CpiAssistant.Cancel != null)
            {
                CancelStep = CreateStep(CpiAssistant.Cancel);
            }

            if (CpiAssistant.Final != null)
            {
                FinalStep = CreateStep(CpiAssistant.Final);
            }
        }

        private AssistantStep CreateStep(CpiAssistantStep cpiStep)
        {
            AssistantStep step = new AssistantStep(_metadata, _commandExecutor, _expressionRunner, _elementCreator, ProjectionName, Data, this, cpiStep);
            step.UpdatingState.ParentState = UpdatingState;
            step.IsModal = IsModal;
            return step;
        }

        public async Task<bool> ExecuteAsync(AssistantCommand command)
        {
            try
            {
                switch (command)
                {
                    case AssistantCommand.Previous:
                        return await GoToPreviousStep();
                    case AssistantCommand.Next:
                        return await GoToNextStep();
                    case AssistantCommand.Finish:
                        {
                            bool doFinish = true;
                            if (!string.IsNullOrEmpty(CpiAssistant.FinishCommand?.Message))
                            {
                                doFinish = await DialogService.CustomButtons(Strings.Finish, CpiAssistant.FinishCommand.Message, Strings.Yes, Strings.No) == CustomButtonsResult.Positive;
                            }

                            if (doFinish)
                            {
                                return await FinishAssistant();
                            }
                        }
                        return true;
                    case AssistantCommand.Cancel:
                        return await CancelAssistant();
                    case AssistantCommand.Restart:
                        return await ResetAssistant();
                    default:
                        throw new ArgumentOutOfRangeException(nameof(command), command, null);
                }
            }
            catch (Exception ex)
            {
                await HandleException(ex);
                return false;
            }
        }

        private async Task<bool> ValidateStep()
        {
            bool isValid = CurrentStep != null && await CurrentStep.Elements.ValidateAsync() && CurrentStep.CalculateIsValid();
            IsActiveStepValid = isValid;
            return isValid;
        }

        private async void Field_TextScanned(object sender, TextScannedEventArgs e)
        {
            if (sender is Field currentField && !string.IsNullOrEmpty(e.Text) && e.Text.EndsWith(Environment.NewLine))
            {
                if (!CurrentStep.Elements.MoveFocusToNextField(currentField))
                {
                    // If there's no field to move focus to, execute the first command
                    CommandItem item = CustomCommands.CommandGroups.FirstOrDefault()?.FirstOrDefault();
                    if (item != null)
                    {
                        // When in the last field, it's not possible to remove focus
                        // If the command clears the field, it will not happen if it still has focus
                        // Make the field read only and switch back to prevent this behaviour
                        bool readOnly = currentField.IsReadOnly;
                        currentField.IsReadOnly = true;
                        await item.ExecuteAsync();
                        currentField.IsReadOnly = readOnly;
                    }
                }
            }
        }

        private async Task<bool> GoToNextStep()
        {
            CurrentStep.Elements.ClearValidations();

            if (await ValidateStep())
            {
                ExecuteResult result = await CurrentStep.ExecuteNextAsync();

                if (ExecuteResult.Cancel.Value.Equals(result.Value))
                {
                    // TEOFF-2311: The assistant will not move to the next step if the next command exits with CANCEL
                    return false;
                }

                if (await CheckExecuteResult(result))
                {
                    int newIndex = ActiveStep.GetValueOrDefault(); // ActiveStep is 1 based, indices are 0 based
                    if (newIndex < Steps.Count)
                    {
                        return await SwitchStep(newIndex);
                    }
                }
            }

            return false;
        }

        private async void Field_InteractionFinished(object sender, EventArgs e)
        {
            if (CpiAssistant.SaveMode == CpiSaveMode.OnLostFocus)
            {
                await CheckExecuteResult(await SaveRecord());
            }
        }

        private async Task<bool> GoToPreviousStep()
        {
            int newIndex = ActiveStep.GetValueOrDefault() - 2; // ActiveStep is 1 based, indices are 0 based
            if (newIndex < 0)
                newIndex = 0;
            return await SwitchStep(newIndex);
        }

        private async Task<bool> ResetAssistant()
        {
            return await LoadAssistant(ProjectionName, CpiAssistant);
        }

        private async Task<bool> CancelAssistant()
        {
            bool doCancel = await DialogService.CustomButtons(Strings.Cancel, CpiAssistant.CancelCommand?.Message ?? Strings.ConfirmCancel, Strings.Yes, Strings.No) == CustomButtonsResult.Positive;

            if (doCancel && CpiAssistant.CancelCommand != null)
            {
                ExecuteResult result = await _commandExecutor.ExecuteAsync(ProjectionName, Data.DefaultViewData, CpiAssistant.CancelCommand);
                doCancel = !result.Failed && !Equals(result.Value, ExecuteResult.Cancel.Value);
            }

            if (doCancel)
            {
                Data.DefaultViewData.Record.ResetHasChanges();

                if (IsModal)
                {
                    Result = ExecuteResult.Cancel;
                    OnClosed(EventArgs.Empty);
                }
                else
                {
                    if (CpiAssistant.Cancel == null)
                    {
                        await LoadAssistant(ProjectionName, CpiAssistant);
                    }
                    else
                    {
                        SwitchToCompleteStep(CancelStep);
                    }
                }

                return true;
            }

            return false;
        }

        private async Task<bool> FinishAssistant()
        {
            CurrentStep.Elements.ClearValidations();

            if (await ValidateStep())
            {
                ExecuteResult result = await SaveRecord();
                bool success = await CheckExecuteResult(result);

                if (CpiAssistant.FinishCommand != null)
                {
                    result = await _commandExecutor.ExecuteAsync(ProjectionName, Data.DefaultViewData, CpiAssistant.FinishCommand);

                    if (ExecuteResult.Cancel.Value.Equals(result.Value))
                    {
                        // TEOFF-2448: The assistant will not move to the finish step if the finish command exits with CANCEL
                        return false;
                    }

                    success = result != null && !result.Failed;
                }

                if (success)
                {
                    Data.DefaultViewData.Record.ResetHasChanges();

                    bool autoRestart = _expressionRunner.RunCheck(CpiAssistant.OfflineAutoRestart ?? CpiAssistant.AutoRestart, Data.DefaultViewData, false);

                    if (autoRestart)
                    {
                        return await ResetAssistant();
                    }
                    else
                    {
                        if (IsModal)
                        {
                            if (HasFinalStep)
                            {
                                SwitchToCompleteStep(FinalStep);
                            }
                            else
                            {
                                Result = ExecuteResult.Ok;
                                OnClosed(EventArgs.Empty);
                            }
                        }
                        else
                        {
                            SwitchToCompleteStep(FinalStep);
                        }
                    }

                    return true;
                }
            }

            return false;
        }

        private void SwitchToCompleteStep(AssistantStep step)
        {
            ActiveStep = null;
            CurrentStep = step;
            Commands.LoadStep(CurrentStep);
        }

        private void UpdateTitle()
        {
            Title = CpiAssistant?.Label != null ? _expressionRunner.InterpolateString(CpiAssistant.Label, Data.DefaultViewData.Record) : string.Empty;
        }

        private AssistantNavigationDirection GetDirection(AssistantStep newStep)
        {
            if (newStep.CpiStep == CpiAssistant.Cancel)
            {
                return AssistantNavigationDirection.Cancelled;
            }

            if (newStep.CpiStep == CpiAssistant.Final)
            {
                return AssistantNavigationDirection.Finished;
            }

            if (CurrentStep == null
                ||
                !Steps.Contains(CurrentStep)
                ||
                !Steps.Contains(newStep))
            {
                return AssistantNavigationDirection.Forward;
            }

            return Steps.IndexOf(newStep) > Steps.IndexOf(CurrentStep) ? AssistantNavigationDirection.Forward : AssistantNavigationDirection.Backward;
        }

        public enum AssistantNavigationDirection
        {
            Forward,
            Backward,
            Cancelled,
            Finished
        }

        public class AssistantNavigationEventArgs : EventArgs
        {
            public AssistantNavigationDirection Direction { get; private set; }
            public AssistantStep FromStep { get; private set; }
            public AssistantStep ToStep { get; private set; }

            public AssistantNavigationEventArgs(AssistantNavigationDirection direction, AssistantStep fromStep, AssistantStep toStep)
            {
                Direction = direction;
                FromStep = fromStep;
                ToStep = toStep;
            }
        }
    }
}
