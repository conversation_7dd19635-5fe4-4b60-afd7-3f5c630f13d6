﻿using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;

namespace Ifs.Uma.Framework.Extensions
{
    public static class SizeExtensions
    {
        public static SizeHint ToSizeHint(this CpiControlSize cpiControlSize)
        {
            switch (cpiControlSize)
            {
                case CpiControlSize.Small:
                    return SizeHint.Small;
                case CpiControlSize.Large:
                    return SizeHint.Large;
                case CpiControlSize.FullWidth:
                    return SizeHint.FullWidth;
                default:
                    return SizeHint.Medium;
            }
        }
    }
}
