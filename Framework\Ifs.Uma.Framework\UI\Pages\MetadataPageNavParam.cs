using System;
using System.Runtime.Serialization;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Pages
{
    [DataContract]
    public sealed class MetadataPageNavParam : NavigationParameter
    {
        [DataMember]
        public string ProjectionName { get; private set; }

        [DataMember]
        public string Page { get; private set; }

        [DataMember]
        public PageValues Filter { get; private set; }

        [DataMember]
        public string Action { get; private set; }

        [DataMember]
        public bool OpenSelector { get; private set; }

        [DataMember]
        public PageValues ActionParameters { get; private set; }

        [DataMember]
        public string WorkflowName { get; private set; }

        [DataMember]
        public int? WorkflowStartSequence { get; private set; }

        private MetadataPageNavParam()
        {
        }

        public MetadataPageNavParam(string projectionName, string page)
            : this(projectionName, page, null)
        {
        }

        public MetadataPageNavParam(string projectionName, string page, PageValues filter)
        {
            ProjectionName = projectionName;
            Page = page;
            Filter = filter;
        }

        public MetadataPageNavParam(string projectionName, string page, bool openSelector, PageValues filter)
        {
            ProjectionName = projectionName;
            Page = page;
            OpenSelector = openSelector;
            Filter = filter;
        }

        public MetadataPageNavParam(string projectionName, string assistant, string action, PageValues actionParameters)
        {
            ProjectionName = projectionName;
            Page = assistant;
            Action = action;
            ActionParameters = actionParameters;
        }

        public static MetadataPageNavParam FromWorkflow(string projectionName, string workflowName, int? workflowStartSequence, PageValues filter)
        {
            if (projectionName == null)
                throw new ArgumentNullException(nameof(projectionName));

            if (workflowName == null)
                throw new ArgumentNullException(nameof(workflowName));

            if (workflowStartSequence == null)
                throw new ArgumentNullException(nameof(workflowStartSequence));

            MetadataPageNavParam navParam = new MetadataPageNavParam()
            {
                ProjectionName = projectionName,
                WorkflowName = workflowName,
                WorkflowStartSequence = workflowStartSequence,
                Filter = filter
            };

            return navParam;
        }

        public PageClassification GetPageClassification(IMetadata metadata)
        {
            PageClassification classification = PageClassification.Detail;
            if (WorkflowName != null)
            {
                classification = PageClassification.Workflow;
            }
            else
            {
                // We expect the Page property to be not null if it's not part of a workflow
                classification = metadata.GetPageClassification(ProjectionName, Page);
            }

            return classification;
        }
    }
}
