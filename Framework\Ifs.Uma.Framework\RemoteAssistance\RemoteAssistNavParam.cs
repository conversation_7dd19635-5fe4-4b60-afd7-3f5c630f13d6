﻿#if REMOTE_ASSISTANCE
using System.Runtime.Serialization;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    [DataContract]
    public sealed class RemoteAssistNavParam : NavigationParameter
    {
        [DataMember]
        public string RefUrl { get; private set; }

        [DataMember]
        public string LuName { get; private set; }

        [DataMember]
        public string KeyRef { get; private set; }

        public RemoteAssistNavParam(string refUrl, string luName, string keyRef)
        {
            RefUrl = refUrl;
            LuName = luName;
            KeyRef = keyRef;
        }
    }
}
#endif
