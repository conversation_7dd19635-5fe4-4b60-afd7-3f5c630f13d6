// Shared
Platforms=iOS,Android,Windows
Name=IFS Notify Me
AppName=NotifyMe
RedirectUri=ifsnotifyme
RemoteAssistance=false
SignatureService=false
LocationEnabled=false
LidarService=false
PushNotification=true

// iOS
iOSDisplayName=IFS Notify Me
// Below must be the one having the InHouse suffix, the build script will remove it when building the store app
BundleIdentifier=com.ifs.cloud.NotifyMe.InHouse
BundleName=IFS Notify Me

// NOTE: iOS usage descriptions are always needed since we don't remove code for things like media attachments and location APIs
// So it's okay to add some dummy text for apps that don't really use those features
NSLocationWhenInUseUsageDescription=This application requires location services to work.
NSLocationAlwaysAndWhenInUseUsageDescription=This application requires location services to work.
NSCameraUsageDescription=This application requires access to the camera to scan barcodes.
NSPhotoLibraryUsageDescription=This app needs access to photos.
NSPhotoLibraryAddUsageDescription=This app needs access to save media and documents.
NSMicrophoneUsageDescription=This is used for remote assistance calls.

// Android
AndroidDisplayName=IFS Notify Me
AndroidPackageName=com.ifs.cloud.NotifyMe

// Windows
WindowsDisplayName=IFS Notify Me
WindowsDescription=IFS Notify Me
WindowsShortName=IFS Notify Me
IdentityName=IFS.30449D8D6E347
PhoneProductId=d5f0f96d-16bc-456f-b341-28b0d54b835c
// Below are in Package.StoreAssociation and Package.xml
ReservedName=IFS Notify Me
LandingUrl=https://developer.microsoft.com/dashboard/Application?appId=9NJQBTGRBG6Q