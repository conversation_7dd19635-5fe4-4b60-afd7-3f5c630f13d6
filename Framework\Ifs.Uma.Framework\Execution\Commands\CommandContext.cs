﻿using System;
using System.Collections.Generic;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility.Formatters;

namespace Ifs.Uma.Framework.Execution.Commands
{
    public sealed class CommandContext : ExecutionContext
    {
        public const string CrudOperationVarName = "CrudOperation";

        public ViewData Data { get; }
        public CommandOptions Options { get; }

        public CommandContext(string projectionName, IMetadata metadata, IExpressionRunner expressionRunner, ViewData data, string debugName, IReadOnlyDictionary<string, VariableStorage> vars, CommandOptions options)
            : base(projectionName, metadata, expressionRunner, debugName, vars)
        {
            Data = data;
            Options = options ?? new CommandOptions();
        }

        protected override ExecutionException NewException(string message)
        {
            throw new CommandException(message);
        }

        protected override bool OnAssign(string name, object value)
        {
            if (base.OnAssign(name, value))
            {
                return true;
            }

            if (name.StartsWith(ViewState.ViewStatePrefix))
            {
                return Data.PageData?.ViewState?.Assign(name, value) ?? false;
            }

            if (Data.Record != null && Data.Record.Assign(name, value))
            {
                return true;
            }

            return false;
        }

        public void Assign(IDictionary<string, string> attributes, RecordData from)
        {
            foreach (var kvp in attributes)
            {
                Assign(kvp.Key, from.ReadParamValue(kvp.Value));
            }
        }

        public void ReadParamsInto(IDictionary<string, string> attributes, RecordData into)
        {
            foreach (var kvp in attributes)
            {
                into.Assign(kvp.Key, ReadParamValue(kvp.Value));
            }
        }

        protected override bool OnTryGetValue(string name, out object value)
        {
            name = ViewData.StripRecordPrefix(name);
            
            if (base.OnTryGetValue(name, out value))
            {
                return true;
            }

            if (Data.TryGetValue(name, out value))
            {
                return true;
            }

            if (name == CrudOperationVarName && !string.IsNullOrEmpty(Options.CrudOperation))
            {
                value = Options.CrudOperation;
                return true;
            }

            return false;
        }

        public override bool TryCallMethod(string methodName, object[] args, out object result)
        {
            if (base.TryCallMethod(methodName, args, out result))
            {
                return true;
            }
            
            return Data.TryCallMethod(methodName, args, out result);
        }

        protected override string FormatParamValue(string paramName, object value)
        {
            if (Data.Record?.EntityName != null)
            {
                AttributePathInfo attribute = AttributePathInfo.Get(Metadata, Data.Record.ProjectionName, Data.Record.EntityName, paramName);
                if (attribute != null)
                {
                    IValueFormatter formatter = AttributeFormatter.For(attribute.Member);
                    return formatter.Format(value);
                }
            }

            return base.FormatParamValue(paramName, value);
        }
    }
}
