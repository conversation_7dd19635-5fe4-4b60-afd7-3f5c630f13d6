﻿using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Permissions;
using Ifs.Uma.Data;
using Ifs.Uma.Tests;
using Ifs.Uma.Tests.TestClasses;
using Ifs.Uma.Utility;
using NUnit.Framework;
using Unity;
using Unity.Lifetime;

namespace Ifs.Uma.AppData.Tests.Attachments
{
    [TestFixture]
    public class MediaHandlerTests : FrameworkTest
    {
        protected override void BeforeTest()
        {
            base.BeforeTest();

            Container.RegisterType<IOnlineAttachmentHandler, AlwaysOfflineOnlineAttachmentHandler>(new ContainerControlledLifetimeManager());
            Container.RegisterType<ILocalStorage, TestLocalStorage>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IAppPermissions, TestAppPermissions>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IMediaHandler, MediaHandler>(new ContainerControlledLifetimeManager());

            PrepareDatabase<FwDataContext>("Attachments.MediaHandlerTestsSchema", "Attachments.MediaHandlerTestsData");
        }

        [Test]
        public async Task CleanUpOldMedia()
        {
            IMediaHandler mediaHandler = Resolve<IMediaHandler>();
            FwDataContext ctx = CreateDataContext();

            // Insert a customer record

            RemoteRow customer = new RemoteRow("tst_customer");
            customer["CustomerNo"] = "CUST1";
            ITable customerTable = ctx.GetTable(ctx.Model.GetTable(customer.TableName));
            customerTable.InsertOnSubmit(customer);
            ctx.SubmitChanges(false);

            // Attach a media item to the customer

            long mediaItemRowId;
            using (MemoryStream ms = new MemoryStream(new byte[] { 0x47 }))
            {
                mediaItemRowId = await mediaHandler.AddMediaAsync("TstCustomer", "CUSTOMER_NO=CUST1^", " TestMediaItemTitle", "TestMediaItemDescription", "myfile.jpg", 0, 0, ms);
            }

            MediaLibraryItem mediaLibItem = ctx.MediaLibraryItems.SingleOrDefault(x => x.RowId == mediaItemRowId);
            Assert.IsNotNull(mediaLibItem, "Failed to setup MediaLibraryItem");
            Assert.AreEqual(AttachmentStatus.RequiresUpload, mediaLibItem.AttachmentStatus, "MediaLibraryItem should be in status RequiresUpload after creation");
            MediaLibrary mediaLibrary = ctx.MediaLibraries.SingleOrDefault();
            Assert.IsNotNull(mediaLibrary, "Failed to setup MediaLibrary");

            ILocalFileInfo localFile = await mediaHandler.GetLocalFileForMediaAsync(mediaLibItem);
            bool fileExists = await localFile.ExistsAsync();
            Assert.IsTrue(fileExists, "File was not stored");

            // Check the media item still exists after a cleanup

            await mediaHandler.CleanupOldMedia();

            Assert.IsNotNull(ctx.MediaLibraryItems.FirstOrDefault(), "MediaLibraryItem was cleaned up while record still exists");
            Assert.IsNotNull(ctx.MediaLibraries.FirstOrDefault(), "MediaLibrary was cleaned up while record still exists");

            // Remove the customer and check the media library was cleaned up

            customerTable.DeleteOnSubmit(customer);
            ctx.SubmitChanges(false);

            await mediaHandler.CleanupOldMedia();

            Assert.IsNotNull(ctx.MediaLibraryItems.FirstOrDefault(), "MediaLibraryItem was cleaned up before it finished uploading");
            Assert.IsNull(ctx.MediaLibraries.FirstOrDefault(), "Failed to cleanup MediaLibrary");

            fileExists = await localFile.ExistsAsync();
            Assert.IsTrue(fileExists, "File was cleared up too early");

            // Set the file to uploaded and make sure the edm file and local file get cleaned up

            ctx.MediaLibraryItems.Attach(DocumentHandler.MobileAttachmentsProjection, mediaLibItem);
            mediaLibItem.AttachmentStatus = AttachmentStatus.Uploaded;
            ctx.SubmitChanges(false);

            await mediaHandler.CleanupOldMedia();

            Assert.IsNull(ctx.MediaLibraryItems.FirstOrDefault(), "Failed to cleanup MediaLibraryItem");

            fileExists = await localFile.ExistsAsync();
            Assert.IsFalse(fileExists, "File was not deleted");
        }
    }
}
