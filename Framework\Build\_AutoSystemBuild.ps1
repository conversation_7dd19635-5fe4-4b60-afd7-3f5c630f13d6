$solutionDir = "../"
$solutionFile = $solutionDir + "Ifs.Uma.sln"

Write-Output "============ Restore NuGet packages"

& "$($solutionDir).nuget/nuget" restore $solutionFile

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Restore NuGet packages"

Write-Output "============ Parse Build Parameters"

& .\_BuildParameters.ps1

Write-Output "============ End - Parse Build Parameters"

Write-Output "============ Build"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

msbuild $solutionFile /m /t:Shared\Ifs_Uma_System_Tests:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=Any CPU" /p:RestorePackages=false
msbuild $solutionFile /m /t:Shared\Ifs_Uma_System_Prepare_Test:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=Any CPU" /p:RestorePackages=false
msbuild $solutionFile /m /t:Shared\Ifs_Uma_System_Flow_Tests:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=Any CPU" /p:RestorePackages=false
msbuild $solutionFile /m /t:Shared\Ifs_Uma_System_Stability_Tests:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=Any CPU" /p:RestorePackages=false

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Build"
