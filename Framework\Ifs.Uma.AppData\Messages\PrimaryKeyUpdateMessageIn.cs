﻿using System.Collections.Generic;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.AppData.Messages
{
    internal sealed class PrimaryKeyUpdateMessageIn : MessageIn
    {
        private const string ClientKeysMap_TableName = "table_name";
        private const string ClientKeysMap_ClientKeys = "client_keys";
        private const string ClientKeysMap_ServerKeys = "server_keys";

        public PrimaryKeyUpdateMessageIn()
            : base(MessageType.PRIMARY_KEY_UPDATE)
        {
        }

        protected override void OnExecute(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper, 
            ILogger logger, DataChangeSet dataChangeSet, TransitionRow transitionRow, bool isInitializing)
        {
            JObject pkData = ReadDataAsJson();
            if (pkData != null)
            {
                Dictionary<string, object> values = new Dictionary<string, object>();
                foreach (KeyValuePair<string, JToken> value in pkData)
                {
                    values[RemoteNaming.ToColumnName(value.Key)] = value.Value.ToString();
                }

                ClientKeysMap map = new ClientKeysMap();
                map.TableName = (string)values[ClientKeysMap_TableName];
                map.ClientKeys = (string)values[ClientKeysMap_ClientKeys];
                map.ServerKeys = (string)values[ClientKeysMap_ServerKeys];

                IDbRowHandler<ClientKeysMap> keyMapRowHandler = ctx.CreateDbRowHandler<ClientKeysMap>(command);
                keyMapRowHandler.UpsertRow(map);
                clientKeysMapper?.RegisterKeys(map);
            }
        }
    }
}
