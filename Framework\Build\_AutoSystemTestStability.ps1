$solutionDir = "../"

Write-Output "============ Stability Test"

Push-Location $solutionDir

packages\NUnit.ConsoleRunner.3.9.0\tools\nunit3-console.exe --x86 --labels=All --workers=1 "--result=TestStabilityResult.xml;format=nunit2" `
	"Ifs.Uma.System.Stability.Tests\bin\Release\Ifs.Uma.System.Stability.Tests.dll"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Pop-Location

Write-Output "============ End - Stability Test"

