﻿using System.Linq;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.AppData.Messages
{
    internal sealed class ResponseMessageIn : MessageIn
    {
        // OTHER: (reponse with related row): update existing related row excluding server pk using row_id
        // OTHER: (reponse no related row - failed transaction fixed): update existing linked record using server pk

        public ResponseMessageIn()
            : base(MessageType.OTHER)
        {
        }

        protected override void OnExecute(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper,
            ILogger logger, DataChangeSet dataChangeSet, TransitionRow transitionRow, bool isInitializing)
        {
            if (transitionRow?.Operation == OperationType.Perform ||
                transitionRow?.Operation == OperationType.Delete)
            {
                // No local changes to be done for a reponse to a perform or a delete
                return;
            }

            MessageTableData tableData = GetMessageData(ctx.Model, logger);
            if (tableData == null)
            {
                // Nothing to update
                return;
            }

            if (clientKeysMapper != null)
            {
                tableData = clientKeysMapper.MapServerToClientKeys(tableData);
            }

            if (transitionRow == null)
            {
                UpdateUsingServerPk(logger, ctx, command, tableData, dataChangeSet);
            }
            else
            {
                UpdateUsingRowId(ctx, command, tableData, transitionRow.ModifiedRowId, dataChangeSet);
            }

            RecordIgnoreMessageIn(ctx, command, tableData);
        }

        private MessageTableData GetMessageData(IMetaModel metaModel, ILogger logger)
        {
            JObject tableData = ReadDataAsJson();

            if (tableData != null)
            {
                tableData = tableData["Response"] as JObject;
            }

            if (tableData == null)
            {
                logger?.Warning("Received message without message text (Check failed transactions).");
                return null;
            }
            
            MessageTableData data = MessageUtils.ExtractTableData(metaModel, tableData, logger).FirstOrDefault();

            if (data != null && data.RowData.ColumnData.Count == 0)
            {
                return null;
            }

            return data;
        }

        private void UpdateUsingServerPk(ILogger logger, FwDataContext ctx, DbCommand command, MessageTableData data, DataChangeSet dataChangeSet)
        {
            IMetaTable table = ctx.Model.GetTable(data.TableName);

            ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(table, data.RowData.ColumnData);
            if (key == null)
            {
                logger.Warning($"ResponseUsingServerPk missing primary key for {data.TableName} record. Ignoring message.");
                return;
            }

            MessageRowDataAccessor accessor = new MessageRowDataAccessor();
            DbRowHandlerCache cache = ctx.CreateDbRowHandlerCache(command, accessor, KeyChoice.DefaultKey);
            DbRowHandler rowHandler = cache.GetHandler(table);

            rowHandler.Update(data.RowData);

            if (dataChangeSet != null)
            {
                if (data.RowData.SyncRowId.HasValue)
                {
                    dataChangeSet.AddRow(table, data.RowData.SyncRowId.Value);
                }
                else
                {
                    dataChangeSet.AddTable(table);
                }
            }
        }

        private void UpdateUsingRowId(FwDataContext ctx, DbCommand command, MessageTableData data, long rowId, DataChangeSet dataChangeSet)
        {
            IMetaTable table = ctx.Model.GetTable(data.TableName);
            IMetaDataMember rowIdColumn = table.AutoIncrement();

            if (rowIdColumn == null)
            {
                return;
            }

            MessageRowDataAccessor accessor = new MessageRowDataAccessor(exludeServerPrimaryKey: true);
            accessor.AddValue(rowIdColumn, rowId);

            DbRowHandlerCache cache = ctx.CreateDbRowHandlerCache(command, accessor, KeyChoice.AutoIncrementKey);
            DbRowHandler rowHandler = cache.GetHandler(table);

            rowHandler.Update(data.RowData);

            if (dataChangeSet != null)
            {
                dataChangeSet.AddRow(table, rowId);
            }
        }

        private void RecordIgnoreMessageIn(FwDataContext ctx, DbCommand command, MessageTableData data)
        {
            IgnoreMessageIn row = CreateIgnoreMessageInRow(data);
            if (row != null)
            {
                IDbRowHandler<IgnoreMessageIn> rowHandler = ctx.CreateDbRowHandler<IgnoreMessageIn>(command);
                rowHandler.UpsertRow(row);
            }
        }
    }
}
