﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class ApiMatchExpression : IfsApiExpression
    {
        public override IfsApiMethodName ApiMethodName => IfsApiMethodName.Match;

        public override IfsApiMethodHandleType ApiMethodHandleType => IfsApiMethodHandleType.String;

        protected override MethodInfo LogicMethodInfo => typeof(ApiMatchExpression).GetTypeInfo().GetDeclaredMethod(nameof(Match));

        public ApiMatchExpression(List<Expression> expressions)
        {
            Parameters = expressions;
        }

        private static bool Match(List<DynamicValue> parameters)
        {
            if (parameters.Count != 2)
            {
                throw new ArgumentException(nameof(parameters));
            }

            string stringValue = parameters[0].GetCleanString();
            string regexPatternWithFlags = parameters[1].GetCleanString();

            if (string.IsNullOrEmpty(stringValue) || string.IsNullOrEmpty(regexPatternWithFlags))
            {
                throw new ArgumentException("Regex Pattern and String cannot be null or empty.");
            }

            string pattern = regexPatternWithFlags;
            RegexOptions options = RegexOptions.None;

            // Page Designer sends regex patterns with the syntax /<pattern>/[flags] 
            // Pattern and the flags are extracted before applying the regex match. 
            // This logic can be modified or removed if the pattern format changes in the future.
            if (regexPatternWithFlags.StartsWith("/") && regexPatternWithFlags.LastIndexOf('/') > 0)
            {
                int lastSlashIndex = regexPatternWithFlags.LastIndexOf('/');
                pattern = regexPatternWithFlags.Substring(1, lastSlashIndex - 1); 
                string flags = regexPatternWithFlags.Substring(lastSlashIndex + 1); 

                foreach (char flag in flags)
                {
                    switch (flag)
                    {
                        case 'i': 
                            options |= RegexOptions.IgnoreCase;
                            break;
                        case 'm': 
                            options |= RegexOptions.Multiline;
                            break;
                        case 's': 
                            options |= RegexOptions.Singleline;
                            break;
                        default:
                            // To Do: Log a warning message for unsupported regex flags
                            break;
                    }
                }
            }

            if (options == RegexOptions.Multiline)
            {
                int breakIndex = stringValue.LastIndexOf('\r');
                stringValue = stringValue.Substring(breakIndex + 1);
            }

            try
            {
                Regex regex = new Regex(pattern, options);
                return regex.IsMatch(stringValue);
            }
            catch (ArgumentException ex)
            {
                throw new ArgumentException($"Invalid regex pattern: {pattern}", ex);
            }
        }
    }
}
