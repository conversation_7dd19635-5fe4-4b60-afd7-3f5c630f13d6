﻿using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Location;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Framework.UI.Forms;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Services.Attachments.Media;
using Ifs.Uma.Services.Location;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;
using static Ifs.Uma.Services.Location.LocationServiceBase;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public sealed class MediaFormData : FormData, IActivable
    {
        public const string RemoveMediaCommandId = "RemoveMediaCommand";
        public const string CancelMediaUploadCommandId = "CancelMediaUploadCommand";
        public const string UploadMediaCommandId = "UploadMediaCommand";
        public const string DownloadMediaCommandId = "DownloadMediaCommand";
        public const string BadgeFieldId = "BadgeField";

        private readonly IFileService _fileService;
        private readonly IMediaHandler _mediaHandler;
        private readonly IMetadata _metadata;
        private readonly IToastService _toastService;
        private readonly INavigator _navigator;
        private readonly IEventAggregator _eventAggregator;
        private readonly IAppParameters _appParams;

        private string _formTitleText;
        public string FormTitleText
        {
            get => _formTitleText;
            set
            {
                if (_formTitleText != value)
                {
                    _formTitleText = value;
                    OnPropertyChanged(() => FormTitleText);
                }
            }
        }

        private MediaLibrary _connection;
        public MediaLibrary Connection
        {
            get => _connection;
            private set
            {
                if (_connection != value)
                {
                    _connection = value;
                    OnPropertyChanged(() => Connection);
                }
            }
        }

        private MediaLibraryItem _mediaItem;
        public MediaLibraryItem MediaItem
        {
            get => _mediaItem;
            private set
            {
                if (_mediaItem != value)
                {
                    _mediaItem = value;
                    OnPropertyChanged(() => MediaItem);
                }
            }
        }

        private PickedFile _pickedFile;
        public PickedFile PickedFile
        {
            get => _pickedFile;
            set => SetProperty(ref _pickedFile, value);
        }

        private bool _isActive;
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (SetProperty(ref _isActive, value))
                {
                    OnIsActiveChanged();
                }
            }
        }

        private bool _navigateBackOnSave;
        public bool NavigateBackOnSave
        {
            get => _navigateBackOnSave;
            set => SetProperty(ref _navigateBackOnSave, value);
        }

        private bool _navigateBackOnCancel;
        public bool NavigateBackOnCancel
        {
            get => _navigateBackOnCancel;
            set => SetProperty(ref _navigateBackOnCancel, value);
        }

        private bool _navigateBackOnDelete;
        public bool NavigateBackOnDelete
        {
            get => _navigateBackOnDelete;
            set => SetProperty(ref _navigateBackOnDelete, value);
        }

        private bool CanAddMedia { get; set; }
        private bool CanReplaceMedia { get; set; }

        public Command DownloadMediaCommand { get; }
        public Command UploadMediaCommand { get; }
        public Command CancelMediaUploadCommand { get; }
        public Command RemoveMediaCommand { get; }

        private readonly ImageOptions _imageOptions;

        private Field _statusField;
        private BadgeField _badgeField;
        private LargeTextEdit _failReasonField;
        private CommandField _downloadMediaField;
        private CommandField _uploadMediaField;
        private CommandField _cancelUploadMediaField;
        private CommandField _removeMediaField;
        private MediaNavParam _navParam;
        private bool _downloadRequested;

        public MediaFormData(IMetadata metadata, IFileService fileService, IAppParameters appParams, IMediaHandler mediaHandler,
            IToastService toast, INavigator navigator, IEventAggregator eventAggregator)
        {
            _mediaHandler = mediaHandler;
            _metadata = metadata;
            _toastService = toast;
            _navigator = navigator;
            _eventAggregator = eventAggregator;
            _fileService = fileService;
            _appParams = appParams;

            _imageOptions = new ImageOptions(appParams.GetPictureMaxDimension(), appParams.GetPictureMaxBytes(), appParams.IsExistingDeviceMediaAllowed());

            UpdateFormTitleText();

            DownloadMediaCommand = Command.FromMethod(OnDownloadMediaCommand);
            UploadMediaCommand = Command.FromMethod(OnUploadMediaCommand);
            CancelMediaUploadCommand = Command.FromMethod(OnCancelMediaUploadCommand);
            RemoveMediaCommand = Command.FromMethod(OnRemoveMediaCommand);

            SetupForm();
            UpdateFormState();
            UpdateFieldStates();
        }

        #region Form

        protected override Form OnSetupForm()
        {
            FormBuilder<MediaFormData> fb = new FormBuilder<MediaFormData>(_metadata, this);
            fb.OverrideFieldType(fb.Path(x => x.PickedFile), typeof(MediaPickerField));

            MediaPickerField imagePicker = (MediaPickerField)fb.AddField(x => x.PickedFile);
            imagePicker.Name = Strings.Media;
            imagePicker.Editability = FieldEditability.InsertingOrUpdating;
            imagePicker.IsRequired = true;
            imagePicker.FileService = _fileService;
            imagePicker.ImageOptions = _imageOptions;
            imagePicker.ValueChanged += FilePicker_ValueChanged;

            if (_appParams.IsEnhancedMediaEnabled())
            {
                imagePicker.AllowVideo = true;
                imagePicker.MaxVideoBytes = _appParams.GetVideoMaxMb() != 0 ? Utils.ConvertMbToBytes(_appParams.GetVideoMaxMb()) : Utils.ConvertMbToBytes(MediaHandler.DefaultVideoMaxMb);
                // Disable Audio Support
                //imagePicker.AllowAudio = true;
                imagePicker.FileExtensions = _appParams?.GetMediaFileExtensions()?.Split(',');
            }

            _statusField = fb.AddField(x => x.MediaItem.AttachmentStatus);
            _statusField.Editability = FieldEditability.Never;
            _statusField.Name = Strings.Status;
            _statusField.Converter = new AttachmentStatusConverter();

            _badgeField = fb.AddBadgeField(BadgeFieldId);
            _badgeField.Editability = FieldEditability.Never;
            _badgeField.Name = Strings.Status;
            _badgeField.Converter = new AttachmentStatusConverter();

            Badge reportBadgeItem = new Badge()
            {
                Color = UmaColors.IfsBlueLight
            };

            _badgeField.Badge = reportBadgeItem;
            _badgeField.Badge.ShowText = true;
            _badgeField.Badge.ShowImage = false;

            TextField titleField = (TextField)fb.AddField(x => x.MediaItem.Name);
            titleField.Editability = FieldEditability.InsertingOrUpdating;
            titleField.Name = _appParams.IsEnhancedMediaEnabled() ? Strings.Title : Strings.Description;
            titleField.IsRequired = true;
            titleField.MaxLength = 200;

            TextField descField = (TextField)fb.AddField(x => x.MediaItem.Description);
            descField.Editability = FieldEditability.InsertingOrUpdating;
            descField.Name = Strings.Description;
            descField.IsRequired = false;
            descField.MaxLength = 200;

            fb.OverrideFieldType(fb.Path(x => x.MediaItem.FailReason), typeof(LargeTextEdit));
            _failReasonField = (LargeTextEdit)fb.AddField(x => x.MediaItem.FailReason);
            _failReasonField.Name = Strings.FailureReason;
            _failReasonField.Editability = FieldEditability.Never;

            _cancelUploadMediaField = fb.AddCommandField(nameof(CancelMediaUploadCommand), Strings.CancelUpload, CancelMediaUploadCommand);
            CancelMediaUploadCommand.Color = UmaColor.FromGraniteToken("granite-color-signal-failure");

            _downloadMediaField = fb.AddCommandField(nameof(DownloadMediaCommand), Strings.DownloadMedia, DownloadMediaCommand);
            _downloadMediaField.Icon = IconUtils.Download;
            _downloadMediaField.Icon.Color = UmaColor.FromGraniteToken("granite-color-signal-info");
            DownloadMediaCommand.Color = UmaColor.FromGraniteToken("granite-color-text-link");

            _uploadMediaField = fb.AddCommandField(nameof(UploadMediaCommand), Strings.Retry, UploadMediaCommand);
            _uploadMediaField.Icon = IconUtils.Upload;
            _uploadMediaField.Icon.Color = UmaColor.FromGraniteToken("granite-color-signal-info");
            UploadMediaCommand.Color = UmaColor.FromGraniteToken("granite-color-text-link");

            _removeMediaField = fb.AddCommandField(RemoveMediaCommandId, string.Empty, RemoveMediaCommand);
            RemoveMediaCommand.Color = UmaColor.FromGraniteToken("granite-color-signal-failure");

            if (_appParams.IsEnhancedMediaEnabled())
            {
                fb.SetLayout(new[]
                {
                    new[] { imagePicker.Id },
                    new[] { _badgeField.Id, _uploadMediaField.Id, _cancelUploadMediaField.Id, _downloadMediaField.Id },
                    new[] { _failReasonField.Id },
                    new[] { titleField.Id },
                    new[] { descField.Id },
                    new[] { _removeMediaField.Id }
                });
            }
            else
            {
                fb.SetLayout(new[]
                {
                    new[] { imagePicker.Id },
                    new[] { titleField.Id },
                    new[] { _statusField.Id },
                    new[] { _failReasonField.Id },
                    new[] { _downloadMediaField.Id, _uploadMediaField.Id }
                });
            }

            return fb.Form;
        }

        private void UpdateFormState()
        {
            bool isNew = IsNew();

            if (Connection == null)
            {
                Form.EditState = FieldEditState.ReadOnly;
            }
            else
            {
                if (isNew && CanAddMedia)
                {
                    Form.EditState = FieldEditState.Insert;
                }
                else if (!isNew && CanReplaceMedia)
                {
                    Form.EditState = FieldEditState.Update;
                }
                else
                {
                    Form.EditState = FieldEditState.ReadOnly;
                }
            }
        }

        private void UpdateFieldStates()
        {
            bool isNew = IsNew();

            _failReasonField.IsVisible = MediaItem?.AttachmentStatus == AttachmentStatus.UploadFailed || MediaItem?.AttachmentStatus == AttachmentStatus.DownloadFailed;

            DownloadMediaCommand.IsEnabled = !isNew && (MediaItem?.AttachmentStatus == AttachmentStatus.RequiresDownload || MediaItem?.AttachmentStatus == AttachmentStatus.DownloadFailed || MediaItem?.AttachmentStatus == AttachmentStatus.Unknown || MediaItem?.AttachmentStatus == null);
            UploadMediaCommand.IsEnabled = PickedFile != null && Connection != null && (MediaItem?.AttachmentStatus == AttachmentStatus.UploadFailed);
            CancelMediaUploadCommand.IsEnabled = PickedFile != null && Connection != null && MediaItem?.AttachmentStatus == AttachmentStatus.Uploading;
            RemoveMediaCommand.IsEnabled = PickedFile != null && Connection != null && !isNew && CanReplaceMedia && MediaItem?.AttachmentStatus != AttachmentStatus.Preparing && MediaItem?.AttachmentStatus != AttachmentStatus.Uploading && MediaItem?.AttachmentStatus != AttachmentStatus.Downloading;

            _statusField.IsVisible = _appParams.IsEnhancedMediaEnabled() ? PickedFile != null && Connection != null && MediaItem != null && !isNew : (MediaItem?.AttachmentStatus).ShouldDisplay(_downloadRequested);
            _badgeField.IsVisible = _appParams.IsEnhancedMediaEnabled() ? PickedFile != null && Connection != null && MediaItem != null && !isNew : (MediaItem?.AttachmentStatus).ShouldDisplay(_downloadRequested);
            _downloadMediaField.IsVisible = DownloadMediaCommand.IsEnabled;
            _uploadMediaField.IsVisible = UploadMediaCommand.IsEnabled;
            _badgeField.Badge.Text = ReturnBadgeText();

            _badgeField.Badge.Color = MediaItem?.AttachmentStatus == AttachmentStatus.UploadFailed || MediaItem?.AttachmentStatus == AttachmentStatus.DownloadFailed
                    ? UmaColor.FromGraniteToken("granite-color-signal-failure")
                    : MediaItem?.AttachmentStatus == AttachmentStatus.Downloaded ? UmaColor.FromGraniteToken("granite-color-signal-ok") : UmaColor.FromGraniteToken("granite-color-signal-info");

            if (_appParams.IsEnhancedMediaEnabled())
            {
                _cancelUploadMediaField.IsVisible = CancelMediaUploadCommand.IsEnabled;
                _removeMediaField.IsVisible = RemoveMediaCommand.IsEnabled;
            }
            else
            {
                _cancelUploadMediaField.IsVisible = false;
                _removeMediaField.IsVisible = false;
            }

            if (PickedFile != null)
            {
                PickedFile.IsNew = isNew;
            }
        }

        private string ReturnBadgeText()
        {
            if (MediaItem?.AttachmentStatus != null)
            {
                AttachmentStatus status = (AttachmentStatus)MediaItem.AttachmentStatus;
                return status.ToLocalisedString();
            }
            else
            {
                return Strings.RequiresDownload;
            }
        }

        private void UpdateFormTitleText()
        {
            if (IsNew())
            {
                FormTitleText = Strings.AddMedia;
            }
            else
            {
                FormTitleText = Strings.MediaDetails;
            }
        }

        private bool IsNew()
        {
            return Connection != null && Connection.RowId == 0;
        }

        #endregion

        private void OnIsActiveChanged()
        {
            if (IsActive)
            {
                _eventAggregator.GetEvent<MediaStatusChangedEvent>().Subscribe(OnMediaStatusChanged, ThreadOption.UIThread);
            }
            else
            {
                _eventAggregator.GetEvent<MediaStatusChangedEvent>().Unsubscribe(OnMediaStatusChanged);
            }
        }

        public async Task Load(MediaNavParam navParam)
        {
            _navParam = navParam;

            CanAddMedia = navParam != null && await _mediaHandler.CanCreateNew(_navParam.EntityName, _navParam.KeyRef);
            CanReplaceMedia = navParam != null && await _mediaHandler.CanReplaceMedia(_navParam.EntityName, _navParam.KeyRef);

            if (_navParam != null && _navParam.MediaRowId.HasValue)
            {
                await LoadExistingMediaAsync(_navParam.MediaRowId.Value);
            }
            else
            {
                LoadNewMedia();
            }
        }

        private async Task ReloadAsync(long? mediaRowId)
        {
            if (mediaRowId == null)
            {
                LoadNewMedia();
            }
            else
            {
                await LoadExistingMediaAsync(mediaRowId.Value);
            }
        }

        public void LoadNewMedia()
        {
            bool canAddNew = CanAddMedia && _navParam?.EntityName != null && _navParam?.KeyRef != null;
            if (canAddNew)
            {
                MediaLibrary connection = new MediaLibrary();
                connection.LuName = _navParam.EntityName;
                connection.KeyRef = _navParam.KeyRef;
                MediaLibraryItem media = new MediaLibraryItem();
                InitializeMedia(connection, media, null);
            }
            else
            {
                InitializeMedia(null, null, null);
            }
        }

        private async Task LoadExistingMediaAsync(long mediaRowId)
        {
            MediaInfo media = await _mediaHandler.GetMediaAsync(mediaRowId);
            if (media == null)
            {
                LoadNewMedia();
            }
            else
            {
                PickedFile pickedFile = null;
                ILocalFileInfo file = await _mediaHandler.GetLocalFileForMediaAsync(media.MediaItem);
                bool fileExists = await file.ExistsAsync();
                if (fileExists)
                {
                    pickedFile = new PickedFile()
                    {
                        Data = file,
                        FileName = media.MediaItem.MediaFile,
                        Latitude = media.MediaItem.Latitude,
                        Longitude = media.MediaItem.Longitude,
                        IsNew = false
                    };

                    if (media.MediaItem.AttachmentStatus == AttachmentStatus.RequiresDownload || media.MediaItem.AttachmentStatus == null)
                    {
                        media.MediaItem.AttachmentStatus = AttachmentStatus.Downloaded;
                    }
                }
                else if (media.MediaItem.AttachmentStatus == AttachmentStatus.RequiresDownload || media.MediaItem.AttachmentStatus == AttachmentStatus.Downloading || media.MediaItem.AttachmentStatus == AttachmentStatus.DownloadFailed || media.MediaItem.AttachmentStatus == null)
                {
                    if (media.MediaItem.AttachmentStatus == null)
                    {
                        pickedFile = new PickedFile()
                        {
                            DataBackup = file,
                            FileName = media.MediaItem.MediaFile,
                            IsNew = false
                        };
                    }
                    else
                    {
                        pickedFile = new PickedFile()
                        { 
                            FileName = media.MediaItem.MediaFile,
                            IsNew = false
                        };
                    }
                }
                InitializeMedia(media.Library, media.MediaItem, pickedFile);
                if (_appParams.IsEnhancedMediaEnabled())
                {
                    _removeMediaField.Name = Strings.DeleteFile;
                    _removeMediaField.Value = Strings.DeleteFile;
                }
                else
                {
                    _removeMediaField.IsVisible = false;
                    RemoveMediaCommand.IsEnabled = false;
                }
            }
        }

        private void InitializeMedia(MediaLibrary mediaLibraryConnection, MediaLibraryItem mediaItem, PickedFile pickedFile)
        {
            if (MediaItem != null)
            {
                MediaItem.PropertyChanged -= MediaItem_PropertyChanged;
            }

            Form.ClearValidation();

            Connection = mediaLibraryConnection;
            MediaItem = mediaItem;
            PickedFile = pickedFile;
            _downloadRequested = false;
            HasChanges = false;

            UpdateFormState();
            UpdateFieldStates();
            UpdateFormTitleText();

            if (MediaItem != null)
            {
                MediaItem.PropertyChanged += MediaItem_PropertyChanged;
            }
        }

        private void MediaItem_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MediaItem.AttachmentStatus) ||
                e.PropertyName == nameof(MediaItem.FailReason))
            {
                UpdateFieldStates();
            }
            else
            {
                if (e.PropertyName == nameof(PickedFile))
                {
                    _removeMediaField.Name = Strings.DeleteFile;
                    _removeMediaField.Value = Strings.DeleteFile;
                }
                HasChanges = true;
            }
        }

        private void FilePicker_ValueChanged(object sender, Uma.UI.Observables.ValueChangedEventArgs e)
        {
            if (e.Source == Uma.UI.Observables.ValueChangedSource.Observer)
            {
                if (PickedFile != null)
                {
                    if (IsNew())
                    {
                        MediaItem.Name = PickedFile.DisplayName;
                    }

                    MediaItem.MediaFile = PickedFile.FileName;
                }
                else
                {
                    MediaItem.Name = string.Empty;
                }

                HasChanges = true;
                UpdateFieldStates();
            }
        }

        protected override bool OnValidate()
        {
            Form.ClearValidation();
            return Form.ValidateRequiredFields();
        }

        protected override async Task OnSaveChangesAsync()
        {
            if (!IsNew())
            {
                await _mediaHandler.DisconnectMediaAsync(MediaItem);
            }

            if (PickedFile == null)
            {
                return;
            }

            if (PickedFile.Source == FileSource.Camera)
            {
                HasChanges = false;

                if (_appParams.GetLocationEnabled() && _appParams.GetLocationOnDemandMode() != LocationMode.Off && PlatformServices.Provider.IsGpsEnabledInDevice())
                {
                    _toastService.Show(ToastType.Info, Strings.GettingLocation);
                    ILocationService locationService = Resolver.Resolve<ILocationService>();
                    GpsLocation location = await locationService.GetGpsLocationAsync(); //checks if location permission is enabled and returns location

                    if (location != null)
                    {
                        // Got current location
                        PickedFile.Latitude = location.Latitude;
                        PickedFile.Longitude = location.Longitude;
                    }
                    else
                    {
                        PickedFile.Latitude = null;
                        PickedFile.Longitude = null;

                        if (Resolver.TryResolve(out IDialogService dialogService))
                        {
                            await dialogService.Alert(Strings.AddMedia, Strings.UnableRetrieveLocationContinue);
                        }
                    }
                }
            }

            Stream dataStream = await PickedFile.ReadAsync();
            long mediaRowId = await _mediaHandler.AddMediaAsync(Connection.LuName, Connection.KeyRef,
                    MediaItem.Name, MediaItem.Description, MediaItem.MediaFile, PickedFile.Latitude, PickedFile.Longitude, dataStream);

            bool didNavigateBack = false;
            if (_navigator.CanNavigateBack() && NavigateBackOnSave)
            {
                // If the user is adding a new media item and came to this screen by the 
                // media list then take them back to the media list instead
                // of leaving them on the media they just added
                HasChanges = false; // Make sure we reset disabled UI before navigating back
                didNavigateBack = await _navigator.NavigateBackAsync();
            }

            _toastService.Show(ToastType.Success, Strings.MediaCreatedSuccessfully);

            if (NavigateBackOnSave && !didNavigateBack)
            {
                await ReloadAsync(mediaRowId);
            }
            else
            {
                LoadNewMedia();
            }
        }

        protected override async Task OnCancelChangesAsync()
        {
            MediaInfo media = await _mediaHandler.GetMediaAsync(MediaItem.RowId);
            bool hasPageChanges = true;

            if ((media != null && media.MediaItem.MediaFile == MediaItem.MediaFile && media.MediaItem.Name == MediaItem.Name && media.MediaItem.Description == MediaItem.Description)
                || (string.IsNullOrEmpty(MediaItem.Name) && MediaItem.MediaItemType == null))
            {
                hasPageChanges = false;
            }

            if (hasPageChanges && Resolver.TryResolve(out IDialogService dialogService))
            {
                CustomButtonsResult result = await dialogService.CustomButtons(null, Strings.UnsavedChanges, Strings.SaveChanges, Strings.Discard);

                if (result == CustomButtonsResult.Negative)
                {
                    await ReloadAsync(MediaItem?.RowId);
                }
                else if (result == CustomButtonsResult.Positive)
                {
                    await OnSaveChangesAsync();
                }
            }

            if (!hasPageChanges)
            {
                await NavigateToMediaDetailsPage(NavigateBackOnCancel);
            }
        }

        private async void OnDownloadMediaCommand()
        {
            _downloadRequested = true;

            var file = await _mediaHandler.GetLocalFileForMediaAsync(MediaItem);
            if (!await file.ExistsAsync())
            {
                await _mediaHandler.RequestDownloadAsync(MediaItem);
            }

            UpdateFieldStates();
        }

        private async void OnRemoveMediaCommand()
        {
            if (CanReplaceMedia && Resolver.TryResolve(out IDialogService dialogService))
            {
                CustomButtonsResult result = await dialogService.CustomButtons(null, Strings.ConfirmDeletionMessage, Strings.KeepFile, Strings.ConfirmDeletion);

                if (result == CustomButtonsResult.Negative)
                {
                    await _mediaHandler.RemoveMediaAsync(MediaItem.RowId);
                    await NavigateToMediaDetailsPage(NavigateBackOnDelete);
                    _toastService.Show(ToastType.Success, Strings.MediaDeletedSuccessfully);
                }
            }

            UpdateFieldStates();
        }

        private async Task NavigateToMediaDetailsPage(bool navigateBack)
        {
            bool didNavigateBack = false;

            if (_navigator.CanNavigateBack() && navigateBack)
            {
                HasChanges = false;
                didNavigateBack = await _navigator.NavigateBackAsync();
            }

            if (navigateBack && !didNavigateBack)
            {
                await ReloadAsync(MediaItem?.RowId);
            }
        }

        private async void OnCancelMediaUploadCommand()
        {
            if (Resolver.TryResolve(out IDialogService dialogService))
            {
                CustomButtonsResult result = await dialogService.CustomButtons(null, Strings.UploadCancellationMessage, Strings.Continue, Strings.ConfirmCancellation);

                if (result == CustomButtonsResult.Negative)
                {
                    var file = await _mediaHandler.GetLocalFileForMediaAsync(MediaItem);
                    if (await file.ExistsAsync())
                    {
                        await _mediaHandler.CancelUploadAsync(MediaItem);
                        await _mediaHandler.RemoveMediaAsync(MediaItem.RowId);
                        _toastService.Show(ToastType.Error, Strings.MediaUploadCanceled);
                    }

                    await NavigateToMediaDetailsPage(NavigateBackOnCancel);
                    UpdateFieldStates();
                }
            }
        }

        private async void OnUploadMediaCommand()
        {
            var file = await _mediaHandler.GetLocalFileForMediaAsync(MediaItem);
            if (await file.ExistsAsync())
            {
                await _mediaHandler.RequestUploadAsync(MediaItem);
            }

            UpdateFieldStates();
        }

        private async void OnMediaStatusChanged(MediaStatusChangedEventArgs args)
        {
            MediaLibraryItem media = MediaItem;
            if (media != null && media.RowId == args.MediaItemRowId)
            {
                media.AttachmentStatus = args.Status;
                media.FailReason = args.FailReason;

                if (PickedFile == null)
                {
                    ILocalFileInfo file = await _mediaHandler.GetLocalFileForMediaAsync(media);
                    if (await file.ExistsAsync())
                    {
                        PickedFile = new PickedFile()
                        {
                            Data = file,
                            FileName = System.IO.Path.GetFileName(file.FilePath)
                        };
                    }
                }

                UpdateFieldStates();
            }
        }
    }
}
