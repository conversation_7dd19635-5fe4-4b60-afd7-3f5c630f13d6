﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_" + DbTableName, Columns = nameof(Entity) + "," + nameof(ProviderName), Unique = true)]
    public class MobileObjectConnectionConfig : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "mobile_object_connection_config";

        [Column(Mandatory = true, ServerPrimaryKey = true)]
        public string Entity { get; set; }

        [Column(Mandatory = true, ServerPrimaryKey = true)]
        public string ProviderName { get; set; }

        [Column]
        public bool? Prefetch { get; set; }

        [Column]
        public bool? Private { get; set; }

        [Column]
        public bool? Readonly { get; set; }

        public MobileObjectConnectionConfig()
            : base(DbTableName)
        {
        }
    }
}
