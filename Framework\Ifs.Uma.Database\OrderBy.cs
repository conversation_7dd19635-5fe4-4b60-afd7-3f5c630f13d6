﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Database
{
    public interface IOrderBy : IEnumerable<ISortColumnSpec>, IWriteSql
    {
        int Offset { get; }
        int Limit { get; }
    }

    public class OrderBy : IOrderBy
    {
        public static IOrderBy Create(IEnumerable<ISortColumnSpec> columns)
        {
            return Create(columns, 0, 0);
        }

        public static IOrderBy Create(IEnumerable<ISortColumnSpec> columns, int offset, int limit)
        {
            return columns != null && columns.Any(x => x != null) ?
                new OrderBy(columns, offset, limit) : null;
        }

        public int Offset { get; private set; }
        public int Limit { get; private set; }

        public IEnumerator<ISortColumnSpec> GetEnumerator()
        {
            return m_columns.GetEnumerator();
        }

        System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
        {
            if (sb != null && builder != null)
            {
                sb.Append(builder.OrderByClause);
                builder.WriteCommaSeparatedList(sb, info, SqlWriteMode.SortColumn, m_columns);
                switch (builder.LimitPlacement)
                {
                    case LimitPosition.LimitFirstAtEnd:
                        builder.WriteLimit(sb, Limit);
                        builder.WriteOffset(sb, Offset);
                        break;
                    case LimitPosition.OffsetFirstAtEnd:
                        builder.WriteOffset(sb, Offset);
                        builder.WriteLimit(sb, Limit);
                        break;
                }
            }
        }

        protected OrderBy(IEnumerable<ISortColumnSpec> columns, int offset, int limit)
        {
            if (columns == null) throw new ArgumentNullException("columns");
            m_columns = columns;
            Offset = Math.Max(offset, 0);
            Limit = Math.Max(limit, 0);
        }

        private IEnumerable<ISortColumnSpec> m_columns;
    }
}
