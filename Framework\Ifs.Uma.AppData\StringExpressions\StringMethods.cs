﻿using System;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.StringExpressions
{
    internal static class StringMethods
    {
        public static void RegisterMethods(Action<string, string> addMethod)
        {
            addMethod("addYears", nameof(AddYears));
            addMethod("addMonths", nameof(AddMonths));
            addMethod("addDays", nameof(AddDays));
            addMethod("addHours", nameof(AddHours));
            addMethod("addMinutes", nameof(AddMinutes));
            addMethod("addSeconds", nameof(AddSeconds));
            addMethod("today", nameof(Today));
            addMethod("now", nameof(Now));
            addMethod("date", nameof(Date));
            addMethod("timestamp", nameof(Timestamp));
            addMethod("time", nameof(Time));
            addMethod("toDate", nameof(ToDate));
            addMethod("toTimestamp", nameof(ToTimestamp));
            addMethod("toTime", nameof(ToTime));
            addMethod("toNumber", nameof(ToNumber));
        }
        
        private static DynamicValue AddYears(DynamicValue date, DynamicValue value)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            int val = (int)value.ToNumber();

            return new DynamicValue(dateTime.Value.AddYears(val));
        }

        private static DynamicValue AddMonths(DynamicValue date, DynamicValue value)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            int val = (int)value.ToNumber();

            return new DynamicValue(dateTime.Value.AddMonths(val));
        }

        private static DynamicValue AddDays(DynamicValue date, DynamicValue value)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            double val = value.ToNumber();

            return new DynamicValue(dateTime.Value.AddDays(val));
        }

        private static DynamicValue AddHours(DynamicValue date, DynamicValue value)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            double val = value.ToNumber();

            return new DynamicValue(dateTime.Value.AddHours(val));
        }

        private static DynamicValue AddMinutes(DynamicValue date, DynamicValue value)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            double val = value.ToNumber();

            return new DynamicValue(dateTime.Value.AddMinutes(val));
        }

        private static DynamicValue AddSeconds(DynamicValue date, DynamicValue value)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            int val = (int)value.ToNumber();

            return new DynamicValue(dateTime.Value.AddSeconds(val));
        }

        private static DynamicValue Today()
        {
            return new DynamicValue(DateTime.Now.Date);
        }

        private static DynamicValue Now()
        {
            return new DynamicValue(DateTime.Now);
        }

        private static DynamicValue Date(DynamicValue y, DynamicValue mon, DynamicValue d)
        {
            int years = (int)y.ToNumber();
            int months = (int)mon.ToNumber();
            int day = (int)d.ToNumber();

            return new DynamicValue(new DateTime(years, months, day, 0, 0, 0));
        }

        private static DynamicValue Timestamp(DynamicValue y, DynamicValue mon, DynamicValue d, DynamicValue h, DynamicValue min, DynamicValue s)
        {
            int years = (int)y.ToNumber();
            int months = (int)mon.ToNumber();
            int day = (int)d.ToNumber();
            int hours = (int)h.ToNumber();
            int mins = (int)min.ToNumber();
            int secs = (int)s.ToNumber();

            return new DynamicValue(new DateTime(years, months, day, hours, mins, secs));
        }

        private static DynamicValue Time(DynamicValue h, DynamicValue min, DynamicValue s)
        {
            int hours = (int)h.ToNumber();
            int mins = (int)min.ToNumber();
            int secs = (int)s.ToNumber();

            return new DynamicValue(new DateTime(1, 1, 1, hours, mins, secs));
        }

        private static DynamicValue ToDate(DynamicValue date)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            string val = DateFormatter.Format(dateTime.Value.Date, DateFormats.Date);
            return new DynamicValue(val);
        }

        private static DynamicValue ToTimestamp(DynamicValue date)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            string val = DateFormatter.Format(dateTime.Value, DateFormats.Timestamp);
            return new DynamicValue(val);
        }

        private static DynamicValue ToTime(DynamicValue date)
        {
            DateTime? dateTime = date.ToDateTime();

            if (dateTime == null)
            {
                return DynamicValue.NullValue;
            }

            string val = DateFormatter.Format(dateTime.Value, DateFormats.Time);
            return new DynamicValue(val);
        }

        private static DynamicValue ToNumber(DynamicValue value)
        {
            double num = value.ToNumber();
            string val = DoubleFormatter.Format(num, null, NumberFormat.Unformatted);
            return new DynamicValue(val);
        }
    }
}
