﻿using System;
using System.Threading;
using Ifs.Uma.Utility;
using SQLitePCL;

namespace Ifs.Uma.Database.SQLite
{
    internal class SQLiteConnection : DbConnection
    {
        internal SQLiteConnection(SQLiteFactory factory, string connectionString, SQLiteConnectionPool pool,
            IMapEnumeration enumMapper, ILogger logger, bool traceFlag)
            : base(logger, traceFlag, enumMapper)
        {
            if (factory == null) throw new ArgumentNullException("factory");
            if (string.IsNullOrEmpty(connectionString)) throw new ArgumentNullException("connectionString");
            if (pool == null) throw new ArgumentNullException("pool");
            m_factory = factory;
            m_builder = new SQLiteConnectionStringBuilder();
            m_builder.ConnectionString = connectionString;
            m_pool = pool;
            m_db = null;
            m_timeout = TimeSpan.FromSeconds(m_builder.TransactionTimeout);
        }

        private SQLiteFactory m_factory;
        private SQLiteConnectionPool m_pool;
        private SQLiteConnectionStringBuilder m_builder;
        private SQLiteConnectionData m_db;
        private TimeSpan m_timeout;
    
        internal void DisposeCommand(SQLiteCommand command)
        {
            CommandDisposed(command);
        }

        internal void DisposeTransaction(SQLiteTransaction transaction)
        {
            TransactionDisposed(transaction);
        }

        public override string ConnectionString { get { return m_builder.ConnectionString; } }

        protected override void DoOpen()
        {
            SQLiteConnectionPool pool = m_pool;
            if (pool == null) throw new ObjectDisposedException("SQLiteConnection");
            GetDbConnection(pool, false);
        }

        protected override DbStatus DoStatus()
        {
            SQLiteConnectionPool pool = m_pool;
            if (pool == null) throw new ObjectDisposedException("SQLiteConnection");
            // quick accept
            if (m_db != null || pool.PoolDataExists(ConnectionString)) return DbStatus.Valid;
            // try the obvious reject
            if (!DataSourceHelper.Instance.FileExists(m_builder.DataSource)) return DbStatus.FileNotFound;
            // try to open the thing
            try
            {
                GetDbConnection(pool, false);
                if (m_db != null)
                {
                    if (!string.IsNullOrEmpty(m_builder.Password))
                    {
                        // If we are connecting to a SQLCipher database with an incorrect password
                        // we will be unable to read from sqlite_master
                        using (DbCommand cmd = CreateCommand(null))
                        {
                            try
                            {
                                cmd.CommandText = "SELECT count(*) FROM sqlite_master;";
                                cmd.ExecuteScalar();
                            }
                            catch (Exception)
                            {
                                return DbStatus.Unauthorized;
                            }
                        }
                    }

                    return DbStatus.Valid;
                }
            }
            catch (SQLiteException)
            {
            }
            // cannot open the connection
            return DbStatus.Invalid;
        }

        protected override void DoCreate()
        {
            if (m_db != null) throw new SQLiteException("Cannot create an open database");
            DoDrop();
            SQLiteConnectionPool pool = m_pool;
            if (pool == null) throw new ObjectDisposedException("SQLiteConnection");
            GetDbConnection(pool, true);
        }

        protected override void DoDrop()
        {
            if (m_db != null) throw new SQLiteException("Cannot drop an open database");
            SQLiteConnectionPool pool = m_pool;
            if (pool == null) throw new ObjectDisposedException("SQLiteConnection");
            // start deleting the file
            var t = DataSourceHelper.Instance.DeleteFileAsync(m_builder.DataSource).ConfigureAwait(false);
            // while that's going on clear the pool
            pool.ClearConnection(m_builder.ConnectionString);
            // wait for the file deletion to complete
            t.GetAwaiter().GetResult();
        }

        internal string GetErrorMessage()
        {
            SQLiteConnectionData db = m_db;
            return db != null ? raw.sqlite3_errmsg(db.Db) : string.Empty;
        }

        internal int GetChanges()
        {
            SQLiteConnectionData db = m_db;
            return db != null ? raw.sqlite3_changes(db.Db) : 0;
        }

        internal sqlite3_stmt[] PrepareCommand(string query)
        {
            SQLiteConnectionData db = m_db;
            if (db == null) throw new ObjectDisposedException("SQLiteConnection");
            return SQLiteHelper.PrepareCommand(db.Db, query);
        }

        internal void EnterReadLock()
        {
            SQLiteConnectionData db = m_db;
            if (db != null && db.RwLock != null)
            {
                if (db.RwLock.TryEnterReadLock(m_timeout))
                {
                    //Logger.Trace(Environment.CurrentManagedThreadId +  " Enter Read Lock");
                }
                else
                {
                    throw new SQLiteException(raw.SQLITE_LOCKED, "Local database read timeout");
                }
            }
        }

        internal void ExitReadLock()
        {
            SQLiteConnectionData db = m_db;
            if (db != null && db.RwLock != null)
            {
                //Logger.Trace(Environment.CurrentManagedThreadId + " Exit Read Lock");
                db.RwLock.ExitReadLock();
            }
        }

        internal void EnterWriteLock()
        {
            SQLiteConnectionData db = m_db;
            if (db != null && db.RwLock != null)
            {
                if (db.RwLock.TryEnterWriteLock(m_timeout))
                {
                    //Logger.Trace(Environment.CurrentManagedThreadId + " Enter Write Lock");
                }
                else
                {
                    throw new SQLiteException(raw.SQLITE_LOCKED, "Local database write timeout");
                }
            }
        }

        internal void ExitWriteLock()
        {
            SQLiteConnectionData db = m_db;
            if (db != null && db.RwLock != null)
            {
                //Logger.Trace(Environment.CurrentManagedThreadId + " Exit Write Lock");
                db.RwLock.ExitWriteLock();
            }
        }

        private void GetDbConnection(SQLiteConnectionPool pool, bool create)
        {
            if (m_db == null)
            {
                m_db = pool.GetConnection(ConnectionString,
                    (k) => { return m_builder.CreatePoolData(); },
                    create, TraceFlag ? Logger : null);
            }
        }

        protected override DbTransaction NewTransaction()
        {
            if (m_db == null) throw new SQLiteException("Database must be open");
            return new SQLiteTransaction(this, Logger, TraceFlag);
        }

        protected override DbCommand NewCommand(DbTransaction transaction)
        {
            if (m_db == null) throw new SQLiteException("Database must be open");
            return new SQLiteCommand(this, transaction, Logger, TraceFlag);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                SQLiteFactory factory = Interlocked.Exchange(ref m_factory, null);
                if (factory != null)
                {
                    factory.DisposeConnection(this);
                }
            }
            SQLiteConnectionData db = Interlocked.Exchange(ref m_db, null);
            if (db != null)
            {
                SQLiteConnectionPool pool = m_pool;
                if (pool != null && disposing)
                {
                    pool.ReturnConnection(m_builder.ConnectionString, db, TraceFlag ? Logger : null);
                }
                else
                {
                    SafeNativeMethods.SQLiteClose(db.Db);
                }
            }
            base.Dispose(disposing);
        }
    }
}
