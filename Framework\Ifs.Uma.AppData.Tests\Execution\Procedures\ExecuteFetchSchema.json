{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}, "CustomerAddresses": {"name": "CustomerAddresses", "entity": "TstCustomerAddress", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}, "CustomerType": {"datatype": "Text", "keygeneration": "User"}, "CustomerTypeDesc": {"datatype": "Text", "keygeneration": "User"}}, "references": {"CustomerTypeRef": {"target": "TstCustomerType", "mapping": {"CustomerType": "TypeId"}}}, "arrays": {"CustomerAddressArray": {"target": "TstCustomerAddress", "mapping": {"CustomerNo": "AddressCustomerNo"}}}}, "TstCustomerType": {"name": "TstCustomerType", "hasETag": true, "CRUD": "Read", "luname": "TstCustomerType", "ludependencies": ["TstCustomerType"], "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text", "keygeneration": "User"}, "TypeDescription": {"datatype": "Text", "keygeneration": "User"}}}, "TstCustomerAddress": {"name": "TstCustomerAddress", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomerAddress", "ludependencies": ["TstCustomerAddress"], "keys": ["AddressCustomerNo", "AddressId"], "attributes": {"AddressCustomerNo": {"datatype": "Text", "keygeneration": "User"}, "AddressId": {"datatype": "Text", "keygeneration": "User"}, "AddressLine": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<FetchCustomer>": {"name": "FetchCustomer", "type": "Function", "layers": [{"vars": [{"name": "Record"}], "execute": [{"call": {"method": "fetch", "args": {"entity": "Customers"}}, "assign": "Record"}, {"call": {"method": "return", "args": {"name": "Record"}}}]}]}, "Function<FetchCustomerType>": {"name": "FetchCustomerType", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "RefRecord"}], "execute": [{"call": {"method": "fetch", "args": {"name": "Customer.CustomerTypeRef"}}, "assign": "RefRecord"}, {"call": {"method": "return", "args": {"name": "RefRecord"}}}]}]}, "Function<FetchCustomerAddress>": {"name": "FetchCustomerAddress", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "RefRecord"}], "execute": [{"call": {"method": "fetch", "args": {"name": "Customer.CustomerAddressArray"}}, "assign": "RefRecord"}, {"call": {"method": "return", "args": {"name": "RefRecord"}}}]}]}, "Function<FetchFromArrayWithWhere>": {"name": "FetchFromArrayWithWhere", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "RefRecord"}], "execute": [{"call": {"method": "fetch", "args": {"name": "Customer.CustomerAddressArray", "where": {"==": [{"var": "AddressId"}, "WORK"]}}}, "assign": "RefRecord"}, {"call": {"method": "return", "args": {"name": "RefRecord"}}}]}]}, "Function<FetchWhereAliased>": {"name": "FetchWhereAliased", "type": "Function", "layers": [{"vars": [{"name": "CustomerNo"}, {"name": "Record"}], "execute": [{"call": {"method": "set", "args": {"value": "501"}}, "assign": "CustomerNo"}, {"call": {"method": "fetch", "args": {"entity": "Customers", "alias": "i", "where": {"==": [{"var": "i.Customer<PERSON>o"}, {"var": "CustomerNo"}]}}}, "assign": "Record"}, {"call": {"method": "return", "args": {"name": "Record"}}}]}]}, "Function<FetchOrderBy>": {"name": "FetchOrderBy", "type": "Function", "layers": [{"vars": [{"name": "RefRecord"}], "execute": [{"call": {"method": "fetch", "args": {"name": "CustomerAddresses", "alias": "i", "orderby": [{"AddressCustomerNo": "asc"}, {"i.AddressLine": "desc"}]}}, "assign": "RefRecord"}, {"call": {"method": "return", "args": {"name": "RefRecord"}}}]}]}}}}