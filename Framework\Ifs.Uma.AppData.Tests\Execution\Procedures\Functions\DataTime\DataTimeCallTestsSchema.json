{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<DateTime_Timestamp0>": {"name": "DateTime_Timestamp0", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "Timestamp", "paramsArray": []}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_TimestampUtc0>": {"name": "DateTime_TimestampUtc0", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "TimestampUtc"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "TimestampUtc", "paramsArray": []}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_Timestamp6>": {"name": "DateTime_Timestamp6", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "Timestamp", "paramsArray": [2000, 7, 9, "12", 34, 56]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_Date0>": {"name": "DateTime_Date0", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Date"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "Date", "paramsArray": []}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_Date1>": {"name": "DateTime_Date1", "type": "Function", "params": [{"name": "MyDate", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Date"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "Date", "paramsArray": ["${MyDate}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_Date3>": {"name": "DateTime_Date3", "type": "Function", "params": [{"name": "Years", "dataType": "Number"}, {"name": "Months", "dataType": "Number"}], "layers": [{"vars": [{"name": "Result", "dataType": "Date"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "Date", "paramsArray": ["${Years}", "${Months}", 2]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_Time1>": {"name": "DateTime_Time1", "type": "Function", "params": [{"name": "MyDate", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Time"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "Time", "paramsArray": ["${MyDate}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_Time3>": {"name": "DateTime_Time3", "type": "Function", "params": [{"name": "Hours", "dataType": "Number"}, {"name": "<PERSON>s", "dataType": "Number"}], "layers": [{"vars": [{"name": "Result", "dataType": "Time"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "Time", "paramsArray": ["${Hours}", "${Mins}", 2]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_ToFormattedString>": {"name": "DateTime_ToFormattedString", "type": "Function", "params": [{"name": "Format", "datatype": "Text", "collection": false}], "layers": [{"vars": [{"name": "Input", "dataType": "Timestamp", "collection": false}, {"name": "Result", "dataType": "Text", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "Timestamp", "namespace": "DateTime", "paramsArray": [2022, 4, 3, 13, 14, 15]}}, "assign": "Input"}, {"call": {"method": "proc", "args": {"name": "ToFormattedString", "namespace": "DateTime", "paramsArray": ["${Input}", "${Format}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_ToOracleFormat>": {"name": "DateTime_ToOracleFormat", "type": "Function", "params": [{"name": "Format", "datatype": "Text", "collection": false}], "layers": [{"vars": [{"name": "Input", "dataType": "Timestamp", "collection": false}, {"name": "Result", "dataType": "Text", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "Timestamp", "namespace": "DateTime", "paramsArray": [2022, 4, 3, 13, 14, 15]}}, "assign": "Input"}, {"call": {"method": "proc", "args": {"name": "ToOracleFormat", "namespace": "DateTime", "paramsArray": ["${Input}", "${Format}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}