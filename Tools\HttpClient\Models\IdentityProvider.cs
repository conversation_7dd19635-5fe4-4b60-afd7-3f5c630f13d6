using System.Text.Json;
using System.Text.Json.Serialization;

namespace Ifs.Tools.HttpClient.Models
{
    public class IdentityProvider
    {
        public string ClientId { get; set; } = "IFS_aurena_native";
        public string Scope { get; set; } = "openid";
        public string Resource { get; set; } = "resource";
        public string ResourceName { get; set; } = "resource";
        
        public string AuthorizationEndpoint { get; set; } = string.Empty;
        public string TokenEndpoint { get; set; } = string.Empty;
        public string LogoutEndpoint { get; set; } = string.Empty;
        public string UserInfoEndpoint { get; set; } = string.Empty;
        
        public string? RefreshToken { get; set; }
        public string? AccessToken { get; set; }
        public string? IdToken { get; set; }

        public bool RefreshTokenExpired => string.IsNullOrEmpty(RefreshToken);

        public static IdentityProvider? FromDiscoveryDocument(string json)
        {
            try
            {
                var discovery = JsonSerializer.Deserialize<DiscoveryDocument>(json);
                if (discovery == null) return null;

                return new IdentityProvider
                {
                    AuthorizationEndpoint = discovery.AuthorizationEndpoint ?? string.Empty,
                    TokenEndpoint = discovery.TokenEndpoint ?? string.Empty,
                    LogoutEndpoint = discovery.LogoutEndpoint ?? string.Empty,
                    UserInfoEndpoint = discovery.UserInfoEndpoint ?? string.Empty
                };
            }
            catch
            {
                return null;
            }
        }

        public void UpdateAuthTokens(TokenResponse? tokenResponse)
        {
            if (tokenResponse == null || tokenResponse.IsEmpty())
            {
                IdToken = null;
                AccessToken = null;
                RefreshToken = null;
            }
            else
            {
                IdToken = tokenResponse.IdToken;
                AccessToken = tokenResponse.AccessToken;
                RefreshToken = tokenResponse.RefreshToken;
            }
        }
    }

    public class DiscoveryDocument
    {
        [JsonPropertyName("authorization_endpoint")]
        public string? AuthorizationEndpoint { get; set; }

        [JsonPropertyName("token_endpoint")]
        public string? TokenEndpoint { get; set; }

        [JsonPropertyName("userinfo_endpoint")]
        public string? UserInfoEndpoint { get; set; }

        [JsonPropertyName("end_session_endpoint")]
        public string? LogoutEndpoint { get; set; }
    }

    public class TokenResponse
    {
        [JsonPropertyName("id_token")]
        public string? IdToken { get; set; }

        [JsonPropertyName("access_token")]
        public string? AccessToken { get; set; }

        [JsonPropertyName("refresh_token")]
        public string? RefreshToken { get; set; }

        [JsonPropertyName("token_type")]
        public string? TokenType { get; set; }

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        public bool IsEmpty()
        {
            return string.IsNullOrEmpty(AccessToken);
        }
    }
}
