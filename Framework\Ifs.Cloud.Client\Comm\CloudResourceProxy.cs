﻿﻿using Ifs.Cloud.Client.Types;
using Ifs.Cloud.Client.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Entities;
using System.Text;
using Ifs.Uma.Utility;

namespace Ifs.Cloud.Client.Comm
{
    public class CloudResourceProxy<T> where T : BaseResource
    {
        private readonly ContextProvider _ctx;

        /// <summary>
        /// The cloud resource request record
        /// </summary>
        public RequestContent<T> RequestRecord { get; }

        /// <summary>
        /// The cloud resource wrapped in RequestRecord
        /// </summary>
        public T Resource => RequestRecord.Resource;

        /// <summary>
        /// Constructs an instance 
        /// </summary>
        /// <param name="ctx">The context</param>
        /// <param name="resource">The cloud resource this proxy should handle</param>
        public CloudResourceProxy(ContextProvider ctx, T resource)
        {
            _ctx = ctx;
            if (resource is Interfaces.ICustomResourceSerializer)
            {
                throw new Exception("Unsupported! Resources implementing Ifs.Cloud.Client.Interfaces.ICustomResourceSerializer must use Ifs.Cloud.Client.Comm.CustomSerializedResourceProxy");
            }
            RequestRecord = new RequestContent<T>(resource);
        }

        /// <summary>
        /// Executes POST on cloud resource assigned to this proxy
        /// </summary>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public Task<List<T>> ExecutePostAsync(int page, int pageSize)
        {
            return ExecutePostAsync(page, pageSize, CancellationToken.None);
        }

        public Task<List<T>> ExecutePostAsync(int page, int pageSize, CancellationToken cancellationToken)
        {
            return ExecuteQueryMethods(RequestRecord, HttpMethodType.Post, page, pageSize, cancellationToken);
        }

        public async Task<byte[]> ExecuteBinaryGet()
        {
            CallRequest<T> cr = CallRequest<T>.ForBinaryResource(_ctx, RequestRecord);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();            
            var content = await response.GetBinaryContent();
            
            //TODO: dispose of this properly at some point, but for a workaround dispose manually here
            response.Dispose();

            return content;
        }

        public async Task<CallResponseStream> ExecuteBinaryGetAsOutResponseStream()
        {
            CallRequest<T> cr = CallRequest<T>.ForBinaryResource(_ctx, RequestRecord);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();                        
            return await response.GetContentAsStream();
        }

        /// <summary>
        /// Executes GET on cloud resource assigned to this proxy
        /// </summary>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<List<T>> ExecuteGetAsync(int page, int pageSize, CancellationToken cancellationToken)
        {
            return await ExecuteQueryMethods(HttpMethodType.Get, page, pageSize, cancellationToken);
        }                

        public async Task<T> ExecutePutAsync()
        {
            CallRequest<T> cr = CallRequest<T>.ForResource(_ctx, RequestRecord, HttpMethodType.Put, -1, 0);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();
            byte[] content = await response.GetBinaryContent();

            //TODO: dispose of this properly at some point, but for a workaround dispose manually here
            response.Dispose();

            List<T> resultList = new List<T>();
            if (content != null && content.Length != 0)
            {
                //if (System.Diagnostics.Debugger.IsAttached)
                //{
                //    System.Diagnostics.Debug.WriteLine("RESPONSE: " + Encoding.UTF8.GetString(content, 0, content.Length));
                //}
                T[] results = ContentSerializationHelper.DeserializeJsonBinary<T[]>(content);
                resultList.AddRange(results);
            }
            return resultList.Count != 0 ? resultList[0] : null;
        }        

        public async Task DeleteAsync()
        {
            CallRequest<T> cr = CallRequest<T>.ForResource(_ctx, RequestRecord, HttpMethodType.Delete, -1, 0);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();
            
            //TODO: dispose of this properly at some point, but for a workaround dispose manually here
            response?.Dispose();
        }

        private Task<List<T>> ExecuteQueryMethods(HttpMethodType callType, int page, int pageSize, CancellationToken cancellationToken)
        {
            return ExecuteQueryMethods(RequestRecord, callType, page, pageSize, cancellationToken);
        }

        private async Task<List<T>> ExecuteQueryMethods(RequestContent<T> requestContent, HttpMethodType callType, int page, int pageSize, CancellationToken cancellationToken)
        {
            List<T> resultList = new List<T>();
            if (_ctx == null)
            {
                return resultList;
            }

            CallRequest<T> cr = CallRequest<T>.ForResource(_ctx, requestContent, callType, page, 0);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync(cancellationToken).ConfigureAwait(false);            
            byte[] content = await response.GetBinaryContent().ConfigureAwait(false);

            //TODO: dispose of this properly at some point, but for a workaround dispose manually here
            response.Dispose();

            if (content != null && content.Length != 0)
            {
                //if (System.Diagnostics.Debugger.IsAttached)
                //{
                //    System.Diagnostics.Debug.WriteLine("RESPONSE: " + Encoding.UTF8.GetString(content, 0, content.Length));
                //}

                if (LoggerManager.Instance.LoggingLevel == MessageType.Trace)
                {
                    ODataDebugWrapper resultSetDebug = new ODataDebugWrapper();
                    resultSetDebug = ContentSerializationHelper.DeserializeJsonBinary<ODataDebugWrapper>(content);
                    string log = Encoding.UTF8.GetString(content, 0, content.Length);
                    Logger.Current.Log("Server Response: " + log, MessageType.Trace);

                    if (resultSetDebug != null && resultSetDebug.Response != null)
                        content = Encoding.UTF8.GetBytes(resultSetDebug.Response.Body);
                }
                
                if (requestContent.Resource.SingleResponse)
                {
                    T result = ContentSerializationHelper.DeserializeJsonBinary<T>(content);
                    resultList.Add(result);
                }
                else
                {
                    ODataWrapperClassArray<T> resultSet = new ODataWrapperClassArray<T>();
                    resultSet = ContentSerializationHelper.DeserializeJsonBinary<ODataWrapperClassArray<T>>(content);
                    if (resultSet.Value != null)
                    {
                        resultList.AddRange(resultSet.Value);
                    }
                }
            }            
            return resultList;
        }

        public void ResetProxyResource(T resource)
        {
            RequestRecord.ResetResource(resource);
        }

        public async Task<CallResponseStream> ExecuteGetStream(string query)
        {
            CallRequest<T> cr = CallRequest<T>.ForBinaryGet(_ctx, RequestRecord, query);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();
            return await response.GetContentAsStream();
        }

        public async Task ExecutePutStream(string query, Stream stream)
        {
            CallRequest<T> cr = CallRequest<T>.ForBinaryPut(_ctx, RequestRecord, query, stream);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();
            response.Dispose();
        }

        public async Task ExecutePatchStream(string query, string eTag, long messageId, Stream stream, DataFormat dataFormat, bool offline = true, string fileName = null)
        {
            CallRequest<T> cr = CallRequest<T>.ForLobPatch(_ctx, RequestRecord, query, eTag, messageId, stream, dataFormat, offline, fileName);

            CallResponse response = await cr.ExecuteRequestWithRetryAsync();
            response.Dispose();
        }
    }
}
