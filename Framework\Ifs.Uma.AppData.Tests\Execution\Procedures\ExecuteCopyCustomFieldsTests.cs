﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteCopyCustomFieldsTests : ProcedureTest
    {
        [Test]
        public async Task CopyCustomFields()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallActionAsync(TestOfflineProjection, "TestCopyCustomFields", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteCopyCustomFieldsSchema", "Execution.Procedures.ExecuteCopyCustomFieldsData");
        }
    }
}
