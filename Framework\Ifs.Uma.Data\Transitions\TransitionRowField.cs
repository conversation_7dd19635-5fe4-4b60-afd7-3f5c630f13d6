﻿using System;

namespace Ifs.Uma.Data.Transitions
{
    [Table(Name = "fnd$transition_row_changes", System = true)]
    public class TransitionRowField
    {
        public TransitionRowField()
        {
        }
        [Column(PrimaryKey = true)]
        public long TransitionRowId { get; set; }
        [Column(PrimaryKey = true, MaxLength = 30, Mandatory = true)]
        public string FieldName { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Performance", "CA1819:PropertiesShouldNotReturnArrays",
            Justification = "Serialised objects, so byte[] is required.")]
        [Column]
        public byte[] NewValue { get; set; }
        [Column]
        public string DataType { get; set; }
    }
}
