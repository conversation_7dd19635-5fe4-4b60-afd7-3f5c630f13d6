import groovy.json.JsonSlurperClassic

pipeline {
    agent any


    environment {
        GIT_REPOSITORY_USERNAME = 'ifs-pd'
        GIT_REPOSITORY = 'ifs-technology-mobile-aurenanative'
    }

    options {
        disableConcurrentBuilds()
        skipDefaultCheckout()
		
    }

    parameters {
        string(defaultValue: null, description: 'Pull Request ID', name: 'PR_ID')
        string(defaultValue: null, description: 'Pull Request Branch Name', name: 'BRANCH_NAME')
		string(defaultValue: null, description: 'Pull Request Commit Hash', name: 'COMMIT_HASH')
		
    }

    stages {
	
        stage('Checkout') {
            steps {
                checkout([$class                           : 'GitSCM',
                          branches                         : [[name: "origin/${BRANCH_NAME}"]],
                          poll                             : true,
                          doGenerateSubmoduleConfigurations: false,
                          extensions                       : [
                                  [$class: 'CleanBeforeCheckout']
                          ],
                          submoduleCfg                     : [],
                          userRemoteConfigs                : [[
                                                                      credentialsId: 'BitBucket',
                                                                      url          : 'https://bitbucket.org/ifs-pd/ifs-technology-mobile-aurenanative.git'
                                                              ]]])
						
            }
        }
		

        stage('Update Commit Build Status') {
            steps {
                // Update PR status to INPROGRESS
                httpRequest([
                        acceptType        : 'APPLICATION_JSON',
                        authentication    : 'BitBucket',
                        contentType       : 'APPLICATION_JSON',
                        httpMode          : 'POST',
                        requestBody       : '''{
                            "key":"UxxNative-PR-${PR_ID}-${COMMIT_HASH}",
                            "name":"UxxNative-Pull-Request-Build",
                            "url":"https://tinyurl.com/35j7d46m/blue/organizations/jenkins/''' + env.JOB_NAME + '''/detail/''' + env.JOB_NAME + '''/''' + env.BUILD_NUMBER + '''/pipeline",
                            "description":"UxxNative Pull Request Build is In-Progress",
                            "state":"INPROGRESS"
                        }''',
                        responseHandle    : 'NONE',
                        url               : "https://api.bitbucket.org/2.0/repositories/ifs-pd/ifs-technology-mobile-aurenanative/commit/${COMMIT_HASH}/statuses/build",
                        validResponseCodes: '200,201'
                ])
            }
        }

        stage('Build') {
            steps {
                //Build autobuilder.bat
                bat "AutoBuild.bat"
				
            }
        }
	}


        post {


            success {
                // Update PR status to SUCCESSFUL
                httpRequest([
                        acceptType        : 'APPLICATION_JSON',
                        authentication    : 'BitBucket',
                        contentType       : 'APPLICATION_JSON',
                        httpMode          : 'POST',
                        requestBody       : '''{
                            "key":"UxxNative-PR-${PR_ID}-${COMMIT_HASH}",
                            "name":"UxxNative-Pull-Request-Build",
                            "url":"https://tinyurl.com/35j7d46m/blue/organizations/jenkins/''' + env.JOB_NAME + '''/detail/''' + env.JOB_NAME + '''/''' + env.BUILD_NUMBER + '''/pipeline",
                            "description":"UxxNative Pull Request Build is In-Progress",
                            "state":"SUCCESSFUL"
                        }''',
                        responseHandle    : 'NONE',
                        url               : "https://api.bitbucket.org/2.0/repositories/ifs-pd/ifs-technology-mobile-aurenanative/commit/${COMMIT_HASH}/statuses/build",
                        validResponseCodes: '200,201'
                ])

                script {
                    currentBuild.result = 'SUCCESS'
                }
            }

            failure {
                // Update PR status to FAILED
                httpRequest([
                        acceptType        : 'APPLICATION_JSON',
                        authentication    : 'BitBucket',
                        contentType       : 'APPLICATION_JSON',
                        httpMode          : 'POST',
                        requestBody       : '''{
                            "key":"UxxNative-PR-${PR_ID}-${COMMIT_HASH}",
                            "name":"UxxNative-Pull-Request-Build",
                            "url":"https://tinyurl.com/35j7d46m/blue/organizations/jenkins/''' + env.JOB_NAME + '''/detail/''' + env.JOB_NAME + '''/''' + env.BUILD_NUMBER + '''/pipeline",
                            "description":"UxxNative Pull Request Build is In-Progress",
                            "state":"FAILED"
                        }''',
                        responseHandle    : 'NONE',
                        url               : "https://api.bitbucket.org/2.0/repositories/ifs-pd/ifs-technology-mobile-aurenanative/commit/${COMMIT_HASH}/statuses/build",
                        validResponseCodes: '200,201'
                ])

            }

            unstable {
                // Update PR status to FAILED
                httpRequest([
                        acceptType        : 'APPLICATION_JSON',
                        authentication    : 'BitBucket',
                        contentType       : 'APPLICATION_JSON',
                        httpMode          : 'POST',
                        requestBody       : '''{
                            "key":"UxxNative-PR-${PR_ID}-${COMMIT_HASH}",
                            "name":"UxxNative-Pull-Request-Build",
                            "url":"https://tinyurl.com/35j7d46m/blue/organizations/jenkins/''' + env.JOB_NAME + '''/detail/''' + env.JOB_NAME + '''/''' + env.BUILD_NUMBER + '''/pipeline",
                            "description":"UxxNative Pull Request Build is In-Progress",
                            "state":"FAILED"
                        }''',
                        responseHandle    : 'NONE',
                        url               : "https://api.bitbucket.org/2.0/repositories/ifs-pd/ifs-technology-mobile-aurenanative/commit/${COMMIT_HASH}/statuses/build",
                        validResponseCodes: '200,201'
                ])
            }
        }
}