<?xml version='1.0' encoding='utf-8'?>
<dataexport>
  <data>

    <fnd__mobile_client_security>
      <projection>FndTstOffline</projection>
      <object_name>TstCustomer</object_name>
      <grant_type>ENTITY_READ</grant_type>
      <granted>true</granted>
    </fnd__mobile_client_security>

    <fnd__mobile_client_security>
      <projection>FndTstOffline</projection>
      <object_name>TstCompany</object_name>
      <grant_type>ENTITY_READ</grant_type>
      <granted>false</granted>
    </fnd__mobile_client_security>

    <fnd__mobile_client_security>
      <projection>FndTstOffline</projection>
      <object_name>TstCustomer</object_name>
      <grant_type>ENTITY_WRITE</grant_type>
      <granted>false</granted>
    </fnd__mobile_client_security>

    <fnd__mobile_client_security>
      <projection>FndTstOffline</projection>
      <object_name>TstCompany</object_name>
      <grant_type>ENTITY_WRITE</grant_type>
      <granted>true</granted>
    </fnd__mobile_client_security>

  </data>
</dataexport>
