﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using MessageType = Ifs.Uma.AppData.Messages.MessageType;

namespace Ifs.Uma.Comm.TouchApps
{
    public class SyncCommsProvider : ITransactionSyncCommsProvider
    {
        private const int MaxRows = 30;
        private const string IfsAppPrefix = "IfsApp";
        private readonly TouchAppsComms _comms;
        private readonly ILogger _logger;

        public BrokenReason? BrokenReason => _comms?.BrokenCause?.Reason;

        public bool IsSyncNowFailed { get; set; }

        public SyncCommsProvider(TouchAppsComms comms, ILogger logger)
        {
            _comms = comms;
            _logger = logger;
        }

        #region ISyncCommsProvider

        public async Task RequestInitialization()
        {
            InitializationResource resource = new InitializationResource();
            resource.DeviceId = _comms.DeviceId;
            resource.AppName = _comms.ClientInfo.AppName;
            //resource.AppVersion = _comms.ClientInfo.AppVersion;
            InitializationResource response = await _comms.SynchronizeNow(resource).ConfigureAwait(false);

            if (response != null)
            {
                IsSyncNowFailed = response.SyncNowValue;
            }
        }

        public Task<IReadOnlyList<MessageIn>> GetMessages(IEnumerable<long> receivedMessageIds, int maxDataChunkSizeKb, bool isInitializing = false)
        {
            return GetMessagesImpl(receivedMessageIds, maxDataChunkSizeKb, isInitializing);
        }

        public Task SendMessages(IEnumerable<MessageOut> messages, int maxDataChunkSizeKb)
        {
            return SendMessagesImpl(messages, maxDataChunkSizeKb);
        }

        public Task<bool> IsAvailable()
        {
            return Task.FromResult(_comms.IsAvailable());
        }

        public async Task<MetadataBlob> GetMetadata()
        {
            MetadataResource resource = await _comms.GetMetadataAsync().ConfigureAwait(false);
            return MetadataBlob.FromJson(resource.Contents);
        }

        public async Task<MetadataBlob> GetClientMetadata(string name)
        {
            ClientMetadataResource resource = await _comms.GetClientMetadataAsync(name).ConfigureAwait(false);
            return MetadataBlob.FromJson(resource.Contents);
        }

        public async Task<MetadataBlob> GetNavigatorEntries()
        {
            if (IsNavigationEndPointAvailable())
            {
                LoadNavigatorResource resource = await _comms.GetNavigatorEntriesAsync().ConfigureAwait(false);
                return MetadataBlob.FromJson(resource.Value);
            }
            else
            {
                return null;
            }
        }
        #endregion

        private bool IsNavigationEndPointAvailable()
        {
            if (!string.IsNullOrEmpty(_comms.ServerVersion))
            {
                string[] parts = _comms.ServerVersion.Split('.');

                if (parts.Length >= 2)
                {
                    string major = parts[0];
                    string minor = new string(parts[1].Where(c => char.IsDigit(c)).ToArray());

                    double serverVersionInDouble = double.Parse(major + "." + minor, CultureInfo.InvariantCulture);

                    // The server endpoint is availble from 23R1 (including 23R1)
                    if (serverVersionInDouble >= 23.1)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private async Task<IReadOnlyList<MessageIn>> GetMessagesImpl(IEnumerable<long> receivedMessageIds, int maxDataChunkSizeKb, bool isInitializing)
        {
            MessageResource resource = new MessageResource();
            resource.MobileContext = new MobileContext();
            resource.MobileContext.DeviceId = _comms.DeviceId.ToString();
            resource.MobileContext.AppName = _comms.ClientInfo.AppName;
            //resource.MobileContext.AppVersion = _comms.ClientInfo.AppVersion;
            resource.ReceivedMessageIds = receivedMessageIds.ToArray();
            resource.MaxRows = MaxRows;

            MessageResource[] result = (await _comms.GetMessagesAsync(resource, Math.Max(maxDataChunkSizeKb, 1), isInitializing).ConfigureAwait(false)).ToArray();

            List<MessageIn> messages = new List<MessageIn>();

            foreach (MessageResource messageResource in result)
            {
                MessageType messageType = ParseMessageType(messageResource.TransactionType);
                MessageIn msg = MessageIn.Create(messageType);
                msg.ServerMessageId = messageResource.MessageId.GetValueOrDefault();
                msg.ClientRelatedMessageId = messageResource.RelatedMessageId;
                msg.MessageData = messageResource.MessageData ?? messageResource.MessageText;
                msg.TransactionId = messageResource.TransactionId.GetValueOrDefault();

                messages.Add(msg);
            }

            return messages;
        }

        private MessageType ParseMessageType(string messageType)
        {
            if (messageType == "REMOVE")
            {
                messageType = MessageType.DELETE.ToString();
            }

            if (!string.IsNullOrEmpty(messageType) &&
                Enum.TryParse(messageType, out MessageType type))
            { 
                return type;
            }

            _logger.Warning($"Received unknown message type '{messageType}'. Ignoring message.");

            return MessageType.UNKNOWN;
        }
        
        private async Task SendMessagesImpl(IEnumerable<MessageOut> messages, int maxDataChunkSizeKb)
        {
            int batchSize = 0;
            List<MessageOut> messagesToSend = new List<MessageOut>(messages);
            List<Tuple<MessageOut, string>> batch = new List<Tuple<MessageOut, string>>();

            int maxUploadSize = maxDataChunkSizeKb * 1024;
            while (messagesToSend.Count > 0)
            {
                MessageOut message = messagesToSend[0];
                string messageContents = message.Serialize(true, _comms.DeviceId);
                int messageSize = messageContents.Length;

                bool sendBatch = false;
                if (batch.Count == 0 || (batchSize + messageSize) < maxUploadSize)
                {
                    batch.Add(Tuple.Create(message, messageContents));
                    batchSize += messageSize;
                    messagesToSend.RemoveAt(0);
                }
                else
                {
                    sendBatch = true;
                }

                if (messagesToSend.Count == 0 || sendBatch)
                {
                    await SendMessagesBatch(batch).ConfigureAwait(false);

                    batch.Clear();
                    batchSize = 0;
                }
            }
        }

        private static readonly HashSet<string> _fwActionsWithMobContext = new HashSet<string>()
        {
            "SaveClientProfileValue",
            "MobileDeviceLocations",
        };

        private async Task SendMessagesBatch(IEnumerable<Tuple<MessageOut, string>> messages)
        {
            foreach (Tuple<MessageOut, string> item in messages)
            {
                List<string> entityMessages = new List<string>();
                List<string> actionMessages = new List<string>();
                MessageType methodType = MessageType.INSERT;
                MessageInResource resource = new MessageInResource();

                methodType = item.Item1.MessageType;

                IDictionary<string, object> data = (Dictionary<string, object>)item.Item1.Data.RowData.ColumnData;
                bool outMessageContainsLobAttributesToStream = item.Item1.LobAttributeTypes != null;

                resource.ProjectionName = item.Item1.Projection;
                resource.TimeZoneAwareResource = item.Item1.IsMessageTimeZoneAware;
                resource.SiteTimeZone = string.Empty;

                if (item.Item1.MessageType == MessageType.ACTION)
                {
                    resource.EntitySetName = item.Item1.Data.TableName;
                    resource.ActionName = item.Item1.Data.TableName;
                    if (!string.IsNullOrEmpty(item.Item1.SiteTimeZone))
                    {
                        resource.SiteTimeZone = item.Item1.SiteTimeZone;
                    }
                }
                else
                {
                    resource.EntitySetName = item.Item1.EntitySetName;
                }

                resource.ArraySource = item.Item1.ArraySource;
                resource.PrimaryKeyString = item.Item1.Data.PrimaryKeyString;

                if (item.Item1.MessageType == MessageType.BOUND_ACTION)
                {
                    IDictionary<string, object> dataWithoutKeys = new Dictionary<string, object>();
                    foreach (KeyValuePair<string, object> it in item.Item1.Data.RowData.ColumnData.Where(x => !item.Item1.Data.RowData.Keys.Keys.Contains(x.Key)))
                    {
                        dataWithoutKeys.Add(it);
                    }

                    data = dataWithoutKeys;
                    resource.ActionName = IfsAppPrefix + "." + resource.ProjectionName + "." + item.Item1.Data.TableName.Replace('.', '_');

                    if (!string.IsNullOrEmpty(item.Item1.SiteTimeZone))
                    {
                        resource.SiteTimeZone = item.Item1.SiteTimeZone;
                    }
                }

                if (_fwActionsWithMobContext.Contains(resource.ActionName))
                {                  
                        data.Add("AppName", _comms.ClientInfo.AppName);
                        data.Add("DeviceId", _comms.DeviceId.ToString());
                }
                else if (item.Item1.Projection.Equals("MobileClientRuntime"))
                {
                    MobileContext mc = new MobileContext();
                    mc.DeviceId = _comms.DeviceId.ToString();
                    mc.AppName = _comms.ClientInfo.AppName;
                    data.Add("MobileContext", mc);
                }

                if (item.Item1.MessageType == MessageType.ACTION)
                {
                    string dateTimeFormat = _comms.IsServerTimeZoneAware ? ObjectConverter.DateTimeZoneFormat : ObjectConverter.DateTimeFormat;
                    IsoDateTimeConverter iso = new IsoDateTimeConverter() { DateTimeFormat = dateTimeFormat, Culture = CultureInfo.InvariantCulture };
                    entityMessages.Add(JsonConvert.SerializeObject(data, new DecimalJsonConverter(), iso));
                }
                else
                {
                    string dateTimeFormat = _comms.IsServerTimeZoneAware ? ObjectConverter.DateTimeZoneFormat : ObjectConverter.DateTimeFormat;
                    IsoDateTimeConverter iso = new IsoDateTimeConverter() { DateTimeFormat = dateTimeFormat, Culture = CultureInfo.InvariantCulture };

                    //TERT-773 Entity Lob Handling : Removing Binary or LongText from existing Entity Data 
                    data = RemoveLobAttributesInEntity(data, item.Item1, outMessageContainsLobAttributesToStream);
                    entityMessages.Add(JsonConvert.SerializeObject(data, new DecimalJsonConverter(), iso));
                }

                resource.MessageId = item.Item1.ClientMessageId;
                resource.ETagString = item.Item1.Data.ETagString;

                StringBuilder sbContents = new StringBuilder();
                using (StringWriter sw = new StringWriter(sbContents))
                using (JsonTextWriter writer = new JsonTextWriter(sw))
                {
                    if (entityMessages.Count > 0)
                    {
                        foreach (string message in entityMessages)
                        {
                            writer.WriteRawValue(message);
                        }
                    }
                }

                resource.Contents = sbContents.ToString();

                //DEBUG - Uncomment one of the following lines to show a sync error
                //NOTE 1: you may need to force initialize to get the right type of error
                //NOTE 2: to get the error after initializing you will need to trigger a sync by either opening the sync screen, or leaving and re-entering the app
                //
                //Sync error
                //throw Cloud.Client.Exceptions.CloudException.FromCause("Forced debugging error: All your base are belong to us", System.Net.HttpStatusCode.InternalServerError, new Exception());
                //Transaction error
                //throw new Exception("Forced debugging error: Transaction took an arrow in the knee");
                //TO-BLOCK
                if (methodType == MessageType.UPDATE)
                {
                    if (!outMessageContainsLobAttributesToStream)
                    {
                        resource.ETagString = string.IsNullOrEmpty(resource.ETagString) ? "*" : resource.ETagString;
                        await _comms.PatchCustomResourceAsync(resource, 0, resource.MessageId, CancellationToken.None, resource.ETagString).ConfigureAwait(false);
                    }
                    else
                    { 
                        await _comms.PrepareEntityLobStreamAsync(resource, item.Item1);
                    }
                }
                else if (methodType == MessageType.DELETE)
                {
                    await _comms.DeleteCustomResourceAsync(resource, resource.MessageId, resource.ETagString).ConfigureAwait(false);
                }
                else if (methodType == MessageType.INSERT)
                {
                    if (!outMessageContainsLobAttributesToStream)
                    {
                        await _comms.PostCustomResourceAsync(resource, 0, resource.MessageId, CancellationToken.None, resource.ETagString).ConfigureAwait(false);
                    }
                    else
                    {
                        await _comms.PrepareEntityLobStreamAsync(resource, item.Item1);
                    }
                }
                else if (item.Item1.MessageType == MessageType.ACTION)
                {
                    //TERT-178 Lob Handling : Checking for Lob action parameters with types
                    if (outMessageContainsLobAttributesToStream)
                    {
                        //TERT-178 Lob Handling : Creating the ActionResource to pass to StreamLobParamsCustomResourceAsync
                        //Here we can't set Metadata and ClientKeysMapper since the device is not initialized yet. However, those are not needed for FndTempLobStore call.

                        ActionResource actionResource = new ActionResource();
                        actionResource.DeviceId = _comms.DeviceId;
                        actionResource.Projection = resource.ProjectionName;
                        actionResource.MethodName = resource.EntitySetName;
                        actionResource.MethodType = MethodType.Action;
                        actionResource.Metadata = null;
                        actionResource.ClientKeysMapper = null;
                        actionResource.Data = MessageUtils.ParametersToJObject(item.Item1.Data.RowData.ColumnData, false);
                        actionResource.ReturnType = null;

                        await _comms.StreamLobActionCustomResourceAsync(actionResource, item.Item1.LobAttributeTypes, 0, resource.MessageId, CancellationToken.None, resource.ETagString);
                    }
                    else
                    {
                        await _comms.PostCustomResourceAsync(resource, 0, resource.MessageId, CancellationToken.None, resource.ETagString).ConfigureAwait(false);
                    }
                }
                else
                {
                    await _comms.PostCustomResourceAsync(resource, 0, resource.MessageId, CancellationToken.None, resource.ETagString).ConfigureAwait(false);
                }
            }
        }

        private IDictionary<string, object> RemoveLobAttributesInEntity(IDictionary<string, object> entityData, MessageOut messageOutItem, bool entityContainsLobAttributes)
        {
            if (entityContainsLobAttributes)
            {
                Dictionary<string, object> entityDataWithoutLobs = new Dictionary<string, object>(entityData);

                foreach (KeyValuePair<string, object> dataItem in messageOutItem.LobAttributeTypes)
                {
                    if (dataItem.Value.ToString() == CpiDataType.Binary.ToString() || dataItem.Value.ToString() == CpiDataType.LongText.ToString())
                    {
                        entityDataWithoutLobs.Remove(dataItem.Key);
                    }
                }

                return entityDataWithoutLobs;
            }

            return entityData;
        }
    }
}
