$ErrorActionPreference = "Stop" 

$solutionDir = "../"
$solutionFile = $solutionDir + "Ifs.Uma.sln"

Write-Output "============ Restore NuGet packages"

& "$($solutionDir).nuget/nuget" restore $solutionFile

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Restore NuGet packages"

Write-Output "============ Build - Android"

msbuild $solutionFile /t:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=Any CPU" /p:RestorePackages=false

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Build - Android"

Write-Output "============ Build - iOS"

msbuild $solutionFile /t:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=iPhone" /p:RestorePackages=false

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Build - iOS"

Write-Output "============ Build - Windows"

msbuild $solutionFile /m /t:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=x86" /p:RestorePackages=false /p:AppxBundlePlatforms=x86 /p:UseDotNetNativeToolchain=false

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Build - Windows"

Write-Output "============ Test"

Push-Location $solutionDir

msbuild "UnitTests.sln" /m /t:Rebuild /nr:false /v:m /p:Configuration=Release /p:RestorePackages=false

if ($LastExitCode -ne 0) { Exit $LastExitCode }

packages\NUnit.ConsoleRunner.3.9.0\tools\nunit3-console.exe --x86 "--result=TestResult.xml;format=nunit2" `
	"Ifs.Uma.Utility.Tests\bin\Release\Ifs.Uma.Utility.Tests.dll" `
	"Ifs.Uma.UI.Tests\bin\Release\Ifs.Uma.UI.Tests.dll" `
	"Ifs.Uma.Framework.Tests\bin\Release\Ifs.Uma.Framework.Tests.dll" `
	"Ifs.Uma.AppData.Tests\bin\Release\Ifs.Uma.AppData.Tests.dll" `
	"Ifs.Uma.Data.Tests\bin\Release\Ifs.Uma.Data.Tests.dll" `
	"Ifs.Uma.Database.Tests\bin\Release\Ifs.Uma.Database.Tests.dll"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Pop-Location

Write-Output "============ End - Test"
