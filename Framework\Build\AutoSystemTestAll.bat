call PrepareBuildEnv.bat
if %errorlevel% neq 0 exit /b %errorlevel%

PowerShell -executionPolicy bypass .\_AutoSystemBuild.ps1
PowerShell -executionPolicy bypass .\_AutoSystemPrepareData.ps1
PowerShell -executionPolicy bypass .\_AutoSystemTestFw.ps1
PowerShell -executionPolicy bypass .\_AutoSystemTestFlow.ps1
PowerShell -executionPolicy bypass .\_AutoSystemTestStability.ps1
if %errorlevel% neq 0 exit /b %errorlevel%
