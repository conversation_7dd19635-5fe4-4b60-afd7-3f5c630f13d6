﻿using System.Linq.Expressions;
using System.Reflection;

namespace Ifs.Uma.AppData.Expressions
{
    internal sealed class ValueProviderReplacer : IfsExpressionVisitor
    {
        private static readonly MethodInfo GetValueMethod =
            typeof(ValueProviderReplacer).GetTypeInfo().GetDeclaredMethod(nameof(GetValue));

        private readonly ParameterExpression _valueProviderParam;

        public static Expression Rewrite(Expression expression, ParameterExpression valueProviderParam)
        {
            IfsExpressionVisitor visitor = new ValueProviderReplacer(valueProviderParam);
            return visitor.Visit(expression);
        }

        private ValueProviderReplacer(ParameterExpression valueProviderParam)
        {
            _valueProviderParam = valueProviderParam;
        }

        protected internal override Expression VisitVarAccessExpression(VarAccessExpression exp)
        {
            return Expression.Call(GetValueMethod, _valueProviderParam, Expression.Constant(exp.PropertyPath));
        }

        private static DynamicValue GetValue(IExpressionValueProvider valueProvider, string propertyPath)
        {
            object value;
            if (valueProvider != null && valueProvider.TryGetValue(propertyPath, out value))
            {
                return new DynamicValue(value);
            }
            
            return new DynamicValue(null);
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            Expression operand = Visit(node.Operand);

            if (node.NodeType == ExpressionType.Convert && node.Type == operand.Type && node.Method == null)
            {
                return operand;
            }

            // Must override here since the base does some unwanted validation
            return node.Update(operand);
        }
    }
}
