﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility.Formatters;

namespace Ifs.Uma.AppData.Formatters
{
    internal sealed class BooleanFormatter : IValueFormatter
    {
        private readonly string _trueLabel;
        private readonly string _falseLabel;

        public BooleanFormatter(CpiField fieldDef)
        {
            if (fieldDef == null) throw new ArgumentNullException(nameof(fieldDef));

            _trueLabel = fieldDef.TrueLabel;
            _falseLabel = fieldDef.FalseLabel;
        }

        public BooleanFormatter(string trueLabel, string falseLabel)
        {
            _trueLabel = trueLabel;
            _falseLabel = falseLabel;
        }

        public string Format(object value)
        {
            if (value is bool bValue)
            {
                if (bValue)
                {
                    return string.IsNullOrEmpty(_trueLabel) ? Strings.Yes : _trueLabel;
                }
                else
                {
                    return string.IsNullOrEmpty(_falseLabel) ? Strings.No : _falseLabel;
                }
            }

            return OrdinalValueFormatter.Instance.Format(value);
        }
    }
}
