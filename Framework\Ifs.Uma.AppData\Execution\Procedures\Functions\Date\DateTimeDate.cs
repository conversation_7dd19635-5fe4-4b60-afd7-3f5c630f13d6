﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Date
{ 
    internal sealed class DateTimeDate : DateTimeFunction
    {
        public const string FunctionName = "Date";

        public DateTimeDate()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return DateTime.Now.Date;
        }
    }

    internal sealed class DateTimeDate1 : DateTimeFunction
    {
        public DateTimeDate1()
            : base(DateTimeDate.FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime? value = parameters[0].GetTimestamp();
            return value.HasValue ? value.Value.Date : (DateTime?)null;
        }
    }

    internal sealed class DateTimeDate3 : DateTimeFunction
    {
        public DateTimeDate3()
            : base(DateTimeDate.FunctionName, 3)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            long? year = parameters[0].GetInteger();
            long? month = parameters[1].GetInteger();
            long? day = parameters[2].GetInteger();

            if (year.HasValue && month.HasValue && day.HasValue)
            {
                return new DateTime((int)year.Value, (int)month.Value, (int)day.Value);
            }
            else
            {
                return null;
            }
        }
    }
}
