﻿using System;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Database.SQLite
{
    internal class SQLiteParameter : DbParameter
    {
        internal SQLiteParameter(string id, TypeCode valueCode, IMetaEnumeration enumeration, int valueIndex)
        {
            if (string.IsNullOrEmpty(id)) throw new ArgumentNullException("id");
            string prolog = SQLiteBuilder.Instance.ParameterProlog;
            if (string.IsNullOrEmpty(prolog))
            {
                m_id = id;
                m_name = id;
            }
            else if (id.StartsWith(prolog, StringComparison.OrdinalIgnoreCase))
            {
                if (id.Length == prolog.Length) throw new ArgumentOutOfRangeException("id");
                m_id = id.Substring(prolog.Length);
                m_name = id;
            }
            else
            {
                m_id = id;
                m_name = prolog + id;
            }
            m_code = valueCode;
            m_enumeration = enumeration;
            m_index = valueIndex;
        }

        public override string Name { get { return m_name; } }
        public override string Id { get { return m_id; } }
        public override TypeCode ValueCode { get { return m_code; } }
        public override int ValueIndex { get { return m_index; } }
        public override IMetaEnumeration Enumeration { get { return m_enumeration; } }

        private string m_id;
        private string m_name;
        private TypeCode m_code;
        private IMetaEnumeration m_enumeration;
        private int m_index;
    }
}
