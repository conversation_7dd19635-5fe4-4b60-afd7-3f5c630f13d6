﻿using System;
using Ifs.Uma.AppData;

namespace Ifs.Uma.Framework.Data
{
    internal class RecordRefViewData : ViewData, IDisposable
    {
        private bool _disposed = false;
        private readonly RecordRef _recordRef;

        public RecordRefViewData(PageData pageData, ViewData parent, RecordRef recordRef)
            : base(pageData, CreateRecordData(parent))
        {
            if (recordRef == null) throw new ArgumentNullException(nameof(recordRef));

            Parent = parent;
            CanEdit = false;

            _recordRef = recordRef;
            //TODO: Temp fix for projection name null, this needs to be investigated
            if (string.IsNullOrEmpty(_recordRef.ProjectionName))
            {
                _recordRef.ProjectionName = pageData.DataSource.ProjectionName;
            }

            _recordRef.RecordLoaded += RecordRef_RecordChanged;

            LoadRecord();
        }

        private static RecordData CreateRecordData(ViewData parent)
        {
            if (parent == null) throw new ArgumentNullException(nameof(parent));
            if (parent.Record == null) throw new ArgumentException(nameof(parent));

            return parent.Record.CreateNew();
        }

        private void RecordRef_RecordChanged(object sender, EventArgs e)
        {
            if (_recordRef.Row != Record.GetRemoteRow())
            {
                LoadRecord();
            }
        }

        private void LoadRecord()
        {
            if (_recordRef.Row == null)
            {
                Record.LoadRecord(null, null);
            }
            else
            {
                EntityRecord record = new EntityRecord(_recordRef.Row, null);
                Record.LoadRecord(_recordRef.ProjectionName, record);
            }
        }

        #region IDisposable Support

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
            {
                return;
            }

            if (disposing)
            {
                // Unsubscribe from events
                if (_recordRef != null)
                {
                    _recordRef.RecordLoaded -= RecordRef_RecordChanged;
                }

                // Dispose of disposable objects
                Record?.Dispose();
            }

            _disposed = true;
        }

        // Ensure that Dispose is called when the object is finalized
        ~RecordRefViewData()
        {
            Dispose();
        }

        #endregion
    }
}
