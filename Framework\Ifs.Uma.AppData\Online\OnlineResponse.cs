﻿using System;

namespace Ifs.Uma.AppData.Online
{
    public sealed class OnlineResponse<T>
    {
        public static OnlineResponse<T> Offline { get; } = new OnlineResponse<T>();

        public T Result { get; }

        public bool Failed => IsOffline || Exception != null;
        public Exception Exception { get; }
        public bool IsOffline => this == Offline;

        private OnlineResponse()
        { 
        }

        public OnlineResponse(T result)
        {
            if (result == null) throw new ArgumentNullException(nameof(result));

            Result = result;
        }

        public OnlineResponse(Exception exception)
        {
            AggregateException ag = exception as AggregateException;
            if (ag?.InnerException != null)
            {
                exception = ag.InnerException;
            }

            Exception = exception;
        }

        public void CheckFailure()
        {
            if (Failed)
            {
                throw new AggregateException(Exception);
            }
        }
    }
}
