﻿using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class MessageResource : AppResource, ICanUseCompression
    {
        //TO-DO - till server is fixed
//#if DEBUG
        //private const bool UseMessageCompression = false;
//#else
        private const bool UseMessageCompression = true;
//#endif

        public override string ResourceName => "MobileClientRuntime.svc/SignAndFetch";

        [DataMember]
        public int MaxRows { get; set; }

        [DataMember]
        public long[] ReceivedMessageIds { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? MessageId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TransactionType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long? TransactionId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long? RelatedMessageId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MessageText { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MessageData { get; set; }
       
        [DataMember(Name ="MobileContext")]
        public MobileContext MobileContext { get; set; }

        public bool ShouldUseCompression() => UseMessageCompression;
    }

    public class MobileContext : AppResource
    {
        [DataMember]
        public string DeviceId { get; set; }
    
        [DataMember]
        public string AppName { get; set; }

        public override string ResourceName => throw new System.NotImplementedException();
    }
}
