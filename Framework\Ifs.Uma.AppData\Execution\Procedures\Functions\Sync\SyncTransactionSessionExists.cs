﻿using System.Linq;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Sync
{
    internal sealed class SyncTransactionSessionExists : SyncFunction
    {
        public const string FunctionName = "TransactionSessionExists";

        public SyncTransactionSessionExists()
            : base(FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string id = parameters[0].GetString();

            if (string.IsNullOrEmpty(id))
            {
                throw new ProcedureException($"SessionId parameter of {FunctionNamespace}.{FunctionName} cannot be null.");
            }

            if (context.DbDataContext.TransactionSessions.Any(x => x.SessionId == id))
            {
                return true;
            }

            return false;
        }
    }
}
