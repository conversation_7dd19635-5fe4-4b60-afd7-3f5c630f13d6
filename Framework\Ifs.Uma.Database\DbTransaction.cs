﻿using System;
using System.Threading;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public abstract class DbTransaction : IDisposable
    {
        public void Commit()
        {
            int oldStatus = Interlocked.Exchange(ref m_status, TRANSACTION_START_COMMIT);
            if (oldStatus != TRANSACTION_DONE)
            {
                try
                {
                    DoCommit();
                    Interlocked.Exchange(ref m_status, TRANSACTION_DONE);
                }
                catch (Exception ex)
                {
                    Interlocked.Exchange(ref m_status, TRANSACTION_COMMIT_FAILED);
                    Logger.Error(Strings.CommitTransactionError, ex.FormatXml(false, false));
                    throw;
                }
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        public abstract DbConnection Connection { get; }

        internal void Start()
        {
            // Exception will be caught and handled in DbCommand.BeginTransaction
            DoStart();
            m_status = TRANSACTION_STARTED;
        }

        protected DbTransaction(ILogger logger, bool traceFlag)
        {
            m_status = TRANSACTION_CREATED;
            Logger = logger;
            TraceFlag = traceFlag;
        }

        ~DbTransaction()
        {
            Dispose(false);
        }

        protected ILogger Logger { get; private set; }
        protected bool TraceFlag { get; private set; }

        protected virtual void Dispose(bool disposing)
        {
            int oldStatus = Interlocked.Exchange(ref m_status, TRANSACTION_DONE);
            if (oldStatus != TRANSACTION_DONE && oldStatus != TRANSACTION_CREATED)
            {
                if (!disposing)
                {
                    Logger.Warning(Strings.TransactionNotDisposed);
                }
                try
                {
                    DoRollback();
                }
                catch (Exception ex)
                {
                    Logger.Error(Strings.RollbackTransactionError, ex.FormatXml(false, false));
                    throw;
                }
            }
        }

        private const int TRANSACTION_CREATED = 0;
        private const int TRANSACTION_STARTED = 1;
        private const int TRANSACTION_START_COMMIT = 2;
        private const int TRANSACTION_COMMIT_FAILED = 3;
        private const int TRANSACTION_DONE = 4;
        private int m_status;

        protected abstract void DoCommit();
        protected abstract void DoRollback();
        protected virtual void DoStart() { }
    }
}
