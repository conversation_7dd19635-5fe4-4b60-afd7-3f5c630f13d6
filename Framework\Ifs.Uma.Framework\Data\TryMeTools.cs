﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Database;
using Ifs.Uma.Data;

namespace Ifs.Uma.Framework.Data
{
    public class TryMeTools
    {
        public static void GetListOfDateTimeMembersForSettingUpTryMeData(DataContext ctx)
        {
#if DEBUG
            //This will print out a list of date columns ready to copy and paste into your code. The chances are you won't want 
            //all of them, so you will need to delete the unneeded ones after pasting

            List<string> output = new List<string>();

            foreach (var table in ctx.Model.GetTables())
            {
                var members = table.DataMembers.Where(x => x.ColumnType == typeof(DateTime) || x.ColumnType == typeof(DateTime?)).Select(x => string.Format("\"{0}\"", x.PropertyName));
                if (members.Any())
                {
                    output.Add(string.Format("dateColumnsToChange.Add(typeof({0}), new string[] {{ {1} }});",
                        table.RowType.Name,
                        string.Join(", ", members)));
                }
            }

            Debug.WriteLine(string.Format("\r\n######\r\n{0}\r\n#####\r\n", string.Join("\r\n", output)));
#endif
        }

        public static void ChangeDatesAccordingToMedian(DataContext ctx,
            Dictionary<Type, string[]> dateColumnsToCalculateShift,
            Dictionary<Type, string[]> dateColumnsToChange)
        {
            TimeSpan requiredShift = CalculateMedianDates(ctx, dateColumnsToCalculateShift);

            if (requiredShift != TimeSpan.Zero)
            {
                ChangeDates(ctx, dateColumnsToChange, requiredShift);
            }
        }

        private static TimeSpan CalculateMedianDates(DataContext ctx, Dictionary<Type, string[]> dateColumnsToCalculateShift)
        {
            List<DateTime> dates = new List<DateTime>();
            foreach (var rowType in dateColumnsToCalculateShift.Keys)
            {
                dates.AddRange(GoThroughDateValues(ctx, rowType, dateColumnsToCalculateShift[rowType], ProcessMode.GetDates, null));
            }

            if (dates.Any())
            {
                DateTime medianDate = MedianCalculator.Median(dates);

                return DateTime.Now.Date.Subtract(medianDate);
            }

            return TimeSpan.Zero;
        }

        public static void ChangeDates(DataContext ctx, Dictionary<Type, string[]> dateColumnsToChange, TimeSpan requiredShift)
        {
            List<DateTime> dates = new List<DateTime>();
            foreach (var rowType in dateColumnsToChange.Keys)
            {
                GoThroughDateValues(ctx, rowType, dateColumnsToChange[rowType], ProcessMode.ChangeDates, requiredShift);
            }
            ctx.SubmitChanges(false);
        }

        private enum ProcessMode
        {
            GetDates,
            ChangeDates
        }

        private static List<DateTime> GoThroughDateValues(DataContext ctx, Type rowType, string[] columnNames, ProcessMode mode, TimeSpan? requiredShift)
        {
            List<DateTime> dates = new List<DateTime>();

            IMetaTable metaTable = ctx.Model.GetTable(rowType);
            if (metaTable != null)
            {
                IEnumerable<IMetaDataMember> dateMembers = metaTable.DataMembers.Where(x => columnNames.Contains(x.PropertyName));

                if (dateMembers.Any())
                {
                    var table = ctx.GetTable(metaTable);
                    if (table != null)
                    {
                        foreach (var dateMember in dateMembers)
                        {
                            foreach (object row in table)
                            {
                                object rowColValue = dateMember.GetValue(row);

                                if (rowColValue != null)
                                {
                                    DateTime? date = rowColValue as DateTime?;
                                    if (date != null && date.HasValue)
                                    {
                                        if (mode == ProcessMode.GetDates)
                                        { 
                                            dates.Add(date.Value.Date);
                                        }
                                        else if (requiredShift.HasValue && row is RowBase)
                                        {
                                            table.Attach(row);
                                            
                                            try //catch invalid property names and invalid DateTime results
                                            {
                                                ((RowBase)row)[dateMember.PropertyName] = date.Value.Add(requiredShift.Value);
                                            }
                                            catch { }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return dates;
        }
    }

    public static class MedianCalculator //http://stackoverflow.com/questions/4140719/calculate-median-in-c-sharp
    {
        /// <summary>
        /// Partitions the given list around a pivot element such that all elements on left of pivot are &lt;= pivot
        /// and the ones at thr right are > pivot. This method can be used for sorting, N-order statistics such as
        /// as median finding algorithms.
        /// Pivot is selected ranodmly if random number generator is supplied else its selected as last element in the list.
        /// Reference: Introduction to Algorithms 3rd Edition, Corman et al, pp 171
        /// </summary>
        private static int Partition<T>(this IList<T> list, int start, int end, Random rnd = null) where T : IComparable<T>
        {
            if (rnd != null)
                list.Swap(end, rnd.Next(start, end));

            var pivot = list[end];
            var lastLow = start - 1;
            for (var i = start; i < end; i++)
            {
                if (list[i].CompareTo(pivot) <= 0)
                    list.Swap(i, ++lastLow);
            }
            list.Swap(end, ++lastLow);
            return lastLow;
        }

        /// <summary>
        /// Returns Nth smallest element from the list. Here n starts from 0 so that n=0 returns minimum, n=1 returns 2nd smallest element etc.
        /// Note: specified list would be mutated in the process.
        /// Reference: Introduction to Algorithms 3rd Edition, Corman et al, pp 216
        /// </summary>
        public static T NthOrderStatistic<T>(this IList<T> list, int n, Random rnd = null) where T : IComparable<T>
        {
            return NthOrderStatistic(list, n, 0, list.Count - 1, rnd);
        }
        private static T NthOrderStatistic<T>(this IList<T> list, int n, int start, int end, Random rnd) where T : IComparable<T>
        {
            while (true)
            {
                var pivotIndex = list.Partition(start, end, rnd);
                if (pivotIndex == n)
                    return list[pivotIndex];

                if (n < pivotIndex)
                    end = pivotIndex - 1;
                else
                    start = pivotIndex + 1;
            }
        }

        public static void Swap<T>(this IList<T> list, int i, int j)
        {
            if (i == j) //This check is not required but Partition function may make many calls so its for perf reason
                return;
            var temp = list[i];
            list[i] = list[j];
            list[j] = temp;
        }

        /// <summary>
        /// Note: specified list would be mutated in the process.
        /// </summary>
        public static T Median<T>(this IList<T> list) where T : IComparable<T>
        {
            return list.NthOrderStatistic((list.Count - 1) / 2);
        }

        public static double Median<T>(this IEnumerable<T> sequence, Func<T, double> getValue)
        {
            var list = sequence.Select(getValue).ToList();
            var mid = (list.Count - 1) / 2;
            return list.NthOrderStatistic(mid);
        }
    }
}
