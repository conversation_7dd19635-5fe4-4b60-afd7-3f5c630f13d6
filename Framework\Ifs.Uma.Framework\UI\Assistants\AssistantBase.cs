﻿using System;
using System.Linq;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Services;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Assistants
{
    public abstract class AssistantBase : PageBase
    {
        public PageData Data { get; protected set; }

        private StatusIcon _statusIconObject;
        public StatusIcon StatusIconObject
        {
            get
            {
                return _statusIconObject;
            }
            set
            {
                SetProperty(ref _statusIconObject, value);
            }
        }

        public int? ActiveStep
        {
            get
            {
                Data.ViewState.TryGetValue(Name + ".activeStep", out object valueHolder);

                if (valueHolder != null)
                {
                    return Convert.ToInt32(valueHolder);
                }

                return null;
            }
            protected set
            {
                Data.ViewState.Assign(Name + ".activeStep", value);
            }
        }

        public int? StartStep
        {
            get
            {
                Data.ViewState.TryGetValue(Name + ".startStep", out object valueHolder);

                if (valueHolder != null)
                {
                    return Convert.ToInt32(valueHolder);
                }

                return null;
            }
            set
            {
                Data.ViewState.Assign(Name + ".startStep", value);
            }
        }

        public bool IsActiveStepDirty
        {
            get
            {
                Data.ViewState.TryGetValue(Name + ".isActiveStepDirty", out object valueHolder);
                return (bool)valueHolder;
            }
            set
            {
                Data.ViewState.Assign(Name + ".isActiveStepDirty", value);
            }
        }

        public bool IsUserInteractionEndingTheAssistance
        {
            get
            {
                Data.ViewState.TryGetValue(Name + ".isUserInteractionEndingTheAssistance", out object valueHolder);
                return (bool)valueHolder;
            }
            set
            {
                Data.ViewState.Assign(Name + ".isUserInteractionEndingTheAssistance", value);
            }
        }

        public bool IsActiveStepModified
        {
            get
            {
                Data.ViewState.TryGetValue(Name + ".isActiveStepModified", out object valueHolder);
                return (bool)valueHolder;
            }
            set
            {
                Data.ViewState.Assign(Name + ".isActiveStepModified", value);
            }
        }

        public bool IsActiveStepValid
        {
            get
            {
                Data.ViewState.TryGetValue(Name + ".isActiveStepValid", out object valueHolder);
                return (bool)valueHolder;
            }
            set
            {
                Data.ViewState.Assign(Name + ".isActiveStepValid", value);
            }
        }

        public AssistantCommandBlock Commands { get; protected set; }

        public bool IsModal { get; protected set; }

        public ExecuteResult Result { get; protected set; }

        public event EventHandler<EventArgs> Closed; // This event is used when running as a modal dialog
        public event EventHandler<EventArgs> StepsLoaded;

        public CpiAssistant CpiAssistant { get; protected set; }
        public AssistantStep CancelStep { get; protected set; }
        public AssistantStep FinalStep { get; protected set; }
        public bool IsCustom { get; protected set; }

        protected bool HasFinalStep
        {
            get
            {
                return FinalStep?.Description != null ||
                   (FinalStep?.Elements != null && FinalStep.Elements.Elements?.Count > 1) || // need to account for OfflineWarningElement which comes by default
                   (FinalStep?.Commands != null && FinalStep.Commands.CommandGroups?.Any() == true);
            }
        }

        public AssistantBase(IEventAggregator eventAggregator, IDialogService dialogService)
            : base(eventAggregator, dialogService)
        {
        }

        protected virtual void OnClosed(EventArgs args)
        {
            EventHandler<EventArgs> closedEvent = Closed;
            closedEvent?.Invoke(this, args);
        }

        protected void OnStepsLoaded(EventArgs args)
        {
            EventHandler<EventArgs> stepsLoadedEvent = StepsLoaded;
            stepsLoadedEvent?.Invoke(this, args);
        }
    }
}
