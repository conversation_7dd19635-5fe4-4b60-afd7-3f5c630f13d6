﻿using System.Runtime.Serialization;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Attachments
{
    [DataContract]
    public sealed class DocumentNavParam : NavigationParameter
    {
        [DataMember]
        public string EntityName { get; private set; }

        [DataMember]
        public string KeyRef { get; private set; }

        [DataMember]
        public long? DocumentReferenceRowId { get; private set; }

        [DataMember]
        public bool RevisionEnabled { get; private set; }

        public DocumentNavParam(string entityName, string keyRef)
        {
            EntityName = entityName;
            KeyRef = keyRef;
        }

        public DocumentNavParam(string entityName, string keyRef, long? documentReferenceRowId, bool revisionEnabled)
        {
            EntityName = entityName;
            KeyRef = keyRef;
            DocumentReferenceRowId = documentReferenceRowId;
            RevisionEnabled = revisionEnabled;
        }
    }
}
