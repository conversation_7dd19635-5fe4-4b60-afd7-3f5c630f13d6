﻿using System;
using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Messages;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class InitializationResource : AppResource, ICustomResourceSerializer
    {
        public override string ResourceName => "MobileClientRuntime.svc/SynchronizeNow";

        [DataMember]
        public string AppName { get; set; }

        //[DataMember]
        //public string AppVersion { get; set; }

        [DataMember]
        public int DeviceId { get; set; }

        [DataMember]
        public bool SyncNowValue { get; set; }

        public object DeserializeJsonString(string jsonString)
        {
            JObject jObj = JObject.Parse(jsonString);

            if (jObj != null)
            {
                object result;
                try
                {
                    result = MessageUtils.ParseToken(null, null, null, jObj);
                }
                catch (Exception)
                {
                    //TO-DO:This can be removed after server supports all data structures
                    result = (bool)jObj.GetValue("value");
                }

                return (bool)result;
            }

            return true;
        }

        public object EmptyResponseHandling(bool isSuccess)
        {
            return isSuccess ? ExecuteResult.True : ExecuteResult.False;
        }

        public string SerializeToJsonString()
        {
            JObject initializationResource = new JObject();
            JObject mobileContext = new JObject();
            mobileContext.Add("AppName", AppName);
            //jObj.Add("AppVersion", AppVersion);
            mobileContext.Add("DeviceId", DeviceId.ToString());
            initializationResource.Add("MobileContext", mobileContext);

            return MessageUtils.JObjectToString(initializationResource);
        }
    }
}
