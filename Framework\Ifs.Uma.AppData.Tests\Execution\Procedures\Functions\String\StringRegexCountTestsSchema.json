{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_RegexCount>": {"name": "String_RegexCount", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "RegexPattern", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Integer"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexCount", "paramsArray": ["${TextInput}", "${RegexPattern}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<String_RegexCount3>": {"name": "String_RegexCount", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "RegexPattern", "dataType": "Text"}, {"name": "Position", "dataType": "Integer"}], "layers": [{"vars": [{"name": "Result", "dataType": "Integer"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexCount", "paramsArray": ["${TextInput}", "${RegexPattern}", "${Position}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<String_RegexCount4>": {"name": "String_RegexCount", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "RegexPattern", "dataType": "Text"}, {"name": "Position", "dataType": "Integer"}, {"name": "RegexOptions", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Integer"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexCount", "paramsArray": ["${TextInput}", "${RegexPattern}", "${Position}", "${RegexOptions}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}