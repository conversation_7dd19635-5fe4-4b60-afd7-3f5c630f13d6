{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"WorkOrders": {"name": "WorkOrders", "entity": "FndMotOffWorkOrder", "array": true, "defaultfilter": false}, "WorkPackages": {"name": "WorkPackages", "entity": "FndMotWorkPackage", "array": true, "defaultfilter": false}, "TimeReports": {"name": "TimeReports", "entity": "FndMotOffTimeReport", "array": true, "defaultfilter": false}}, "entities": {"FndMotOffWorkOrder": {"name": "FndMotOffWorkOrder", "hasETag": true, "hasKeys": true, "CRUD": "Create,Read,Update,Delete", "luname": "FndMotOffWorkOrder", "ludependencies": ["FndMotOffWorkOrder"], "keys": ["WoNo"], "attributes": {"WoNo": {"datatype": "Number", "keygeneration": "Server", "required": true, "editable": false, "updatable": false, "insertable": false, "unbound": false, "multiselect": false}, "Description": {"datatype": "Text", "size": 200, "keygeneration": "User", "required": true, "editable": true, "updatable": true, "insertable": true, "unbound": false, "multiselect": false}, "PackageNo": {"datatype": "Number", "keygeneration": "User", "required": false, "editable": true, "updatable": true, "insertable": true, "unbound": false, "multiselect": false}}, "references": {"WorkPackageRef": {"target": "FndMotWorkPackage", "mapping": {"PackageNo": "WorkPackageNo"}}}, "arrays": {"TimeReportsArray": {"target": "FndMotOffTimeReport", "datasource": null, "mapping": {"WoNo": "WorkOrderNo"}}}}, "FndMotOffTimeReport": {"name": "FndMotOffTimeReport", "hasETag": true, "hasKeys": true, "CRUD": "Create,Read,Update,Delete", "luname": "FndMotOffTimeReport", "ludependencies": ["FndMotOffTimeReport"], "keys": ["WorkOrderNo", "SeqNo"], "attributes": {"WorkOrderNo": {"datatype": "Number", "keygeneration": "User", "required": true, "editable": true, "updatable": false, "insertable": true, "unbound": false, "multiselect": false}, "SeqNo": {"datatype": "Number", "keygeneration": "Server", "required": true, "editable": false, "updatable": false, "insertable": false, "unbound": false, "multiselect": false}, "DateTimeFrom": {"datatype": "Timestamp", "keygeneration": "User", "required": true, "editable": true, "updatable": true, "insertable": true, "unbound": false, "multiselect": false}, "DateTimeTo": {"datatype": "Timestamp", "keygeneration": "User", "required": true, "editable": true, "updatable": true, "insertable": true, "unbound": false, "multiselect": false}}, "references": {}, "arrays": {}, "actions": {}, "functions": {}}, "FndMotWorkPackage": {"name": "FndMotWorkPackage", "hasETag": true, "hasKeys": true, "CRUD": "Create,Read,Update,Delete", "luname": "FndMotWorkPackage", "ludependencies": ["FndMotWorkPackage"], "keys": ["WorkPackageNo"], "attributes": {"WorkPackageNo": {"datatype": "Number", "keygeneration": "Server", "required": true, "editable": false, "updatable": false, "insertable": false, "unbound": false, "multiselect": false}, "Description": {"datatype": "Text", "size": 200, "keygeneration": "User", "required": true, "editable": true, "updatable": true, "insertable": true, "unbound": false, "multiselect": false}}}}}}