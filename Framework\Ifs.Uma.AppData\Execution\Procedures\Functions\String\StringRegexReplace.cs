﻿using System;
using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringRegexReplace : StringFunction
    {
        public const string FunctionName = "RegexReplace";

        public StringRegexReplace()
            : base(FunctionName, 3, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();
            if (string.IsNullOrEmpty(regexPattern))
            {
                return stringToModify;
            }

            string replacementString = parameters[2].GetString();
            return Regex.Replace(stringToModify, regexPattern, replacementString, RegexOptions.None, TimeSpan.FromSeconds(10));
        }
    }

    internal sealed class StringRegexReplace4 : StringFunction
    {
        public const string FunctionName = "RegexReplace";

        public StringRegexReplace4()
            : base(FunctionName, 4, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();
            if (string.IsNullOrEmpty(regexPattern))
            {
                return stringToModify;
            }

            string replacementString = parameters[2].GetString();
            string regexOptionsParam = parameters[3].GetString();

            RegexOptions regexOptions = StringToRegexOptions(regexOptionsParam);

            return Regex.Replace(stringToModify, regexPattern, replacementString, regexOptions, TimeSpan.FromSeconds(10));
        }
    }
}
