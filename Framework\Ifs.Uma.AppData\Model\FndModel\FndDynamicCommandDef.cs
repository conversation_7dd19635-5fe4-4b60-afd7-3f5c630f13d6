﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndDynamicCommandDef : RemoteRow
    {
        public const string DbTableName = "fnd_dynamic_command_def";

        [Column]
        public string Method { get; set; }

        [Column]
        public string ArgName { get; set; }

        [Column]
        public string ArgValue { get; set; }

        [Column]
        public string Result { get; set; } // Not supported in dynamic assistants yet

        [Column]
        public string Assign { get; set; }

        [Column]
        public string Next { get; set; } // Not supported in dynamic assistants yet

        [Column]
        public bool? Baund { get; set; } // Should've been "Bound" but it's misspelled in the server, so need to use this till they fix it

        [Column]
        public string Name { get; set; }

        [Column]
        public string Projection { get; set; }

        [Column]
        public string Params { get; set; }

        public FndDynamicCommandDef()
            : base(DbTableName)
        {
        }
    }
}
