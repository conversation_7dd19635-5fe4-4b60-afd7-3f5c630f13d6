﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = FwDataContext.FwTablePrefix + "open_id_data", System = true)]
    public class OpenIdData
    {
        [Column(PrimaryKey = true)]
        public string Username { get; set; }

        [Column]
        public string Ipi { get; set; }

        [Column]
        public string RefreshToken { get; set; }

        [Column]
        public string AccessToken { get; set; }

        [Column]
        public string IdToken { get; set; }
    }
}
