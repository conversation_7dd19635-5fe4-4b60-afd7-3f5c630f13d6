﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Permissions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.AppData.Attachments.Media
{
    public sealed class MediaHandler : DataAccessor<FwDataContext>, IMediaHandler
    {
        public const string MediaLibraryProvider = "MediaLibrary";
        public const string MediaProjectionName = "MobileAttachments";
        public const int DefaultVideoMaxMb = 20000;

        private const string ReplaceMediaLibraryItemActivity = "ReplaceMediaLibraryItem";
        private const string ServerCapabilities = "SERVER_CAPABILITIES";
        private const string MediaConnectCapability = "ConnectMedia";
        private static readonly string[] MediaEntities = new[] { "MediaLibrary", "MediaLibraryItem" };

        private readonly ConcurrentDictionary<string, MobileObjectConnectionConfig> _configs = new ConcurrentDictionary<string, MobileObjectConnectionConfig>();
        private readonly IMetaModel _model;
        private readonly ILocalStorage _localStorage;
        private readonly IEventAggregator _eventAggregator;
        private readonly IOnlineAttachmentHandler _attachmentHandler;
        private readonly ILogger _logger;

        private readonly int _dbId;
        private readonly bool _mediaReadAllowed;
        private readonly bool _mediaWriteAllowed;
        private readonly bool _replaceMediaAllowed;

        public MediaHandler(IDatabaseController db, ILocalStorage localStorage, IEventAggregator eventAggregator,
            ILogger logger, IPerfLogger perfLogger, IOnlineAttachmentHandler attachmentHandler, IAppPermissions permissions)
            : base(db, logger, perfLogger)
        {
            _localStorage = localStorage;
            _eventAggregator = eventAggregator;
            _model = db.GetMetaModel();
            _dbId = db.ConnectedDatabaseId;
            _attachmentHandler = attachmentHandler;
            _logger = logger;
            _mediaReadAllowed = permissions.IsEntityReadGranted(MediaProjectionName, MediaEntities);
            _mediaWriteAllowed = permissions.IsEntityWriteGranted(MediaProjectionName, MediaEntities);
            _replaceMediaAllowed = permissions.IsClientFeatureGranted(MediaProjectionName + "." + ReplaceMediaLibraryItemActivity);
        }

        public async Task<ILocalFileInfo> GetLocalFileForMediaAsync(MediaLibraryItem mediaItem)
        {
            if (mediaItem.MediaFile == null && mediaItem.RowId != 0)
            {
                // MediaFile may have been filled in by the sync service so we
                // should go get the db record to make sure we can get the file extension
                mediaItem = (await WithDataContextAsync(ctx => ctx.MediaLibraryItems.FirstOrDefault(x => x.RowId == mediaItem.RowId), null)) ?? mediaItem;
            }

            string fileName = GetFileName(mediaItem);
            string filePath = Path.Combine(AttachmentFolders.GetMediaFolderPath(_dbId), fileName);
            return await _localStorage.PrivateStorage.GetFileInfoAsync(filePath);
        }

        public async Task<ILocalFileInfo> GetLocalFileForLogoAsync(string fileName)
        {
            string filePath = Path.Combine(AttachmentFolders.GetMediaFolderPath(_dbId), fileName);
            return await _localStorage.PrivateStorage.GetFileInfoAsync(filePath);
        }

        private string GetFileName(MediaLibraryItem media)
        {
            // Must always ignore MediaFile on the row - it may come from the server or
            // another device making it useless. Just always use a predictable location

            string fileName = media.ItemId.ToString().Replace('-', 'N');
            if (!string.IsNullOrEmpty(media.MediaFile))
            {
                fileName += Path.GetExtension(media.MediaFile);
            }

            return fileName;
        }

        public async Task RemoveMediaAsync(long mediaRowId)
        {
            MediaInfo mediaInfo = await GetMediaAsync(mediaRowId);
            if (mediaInfo.MediaItem != null)
            {
                await WithDataContextAsync(ctx =>
                {
                    ObjPrimaryKey pk = ObjPrimaryKey.FromPrimaryKey(_model, mediaInfo.MediaItem);
                    mediaInfo.MediaItem.PrimaryKeyString = pk.ToFormattedKeyRef(MediaProjectionName, true);
                    ctx.MediaLibraryItems.DeleteOnSubmit(MediaProjectionName, mediaInfo.MediaItem);
                    ctx.SubmitChanges(true);
                });
            }
        }

        public async Task AddMediaFile(string fileName, Stream dataStream)
        {
            ILocalFileInfo file = await GetLocalFileForLogoAsync(fileName);

            if (await file.ExistsAsync())
            {
                return;
            }

            await file.WriteStreamAsync(dataStream);
            await file.SetReadOnly();
        }

        public async Task<long> AddMediaAsync(string entityName, string keyRef, string title, string description, string fileName, double? latitude, double? longitude, Stream dataStream)
        {
            if (entityName == null)
                throw new ArgumentNullException(nameof(entityName));

            if (keyRef == null)
                throw new ArgumentNullException(nameof(keyRef));

            if (string.IsNullOrEmpty(fileName))
                throw new ArgumentException($"{nameof(fileName)} must have a value", nameof(fileName));

            MediaLibrary library = null;
            MediaLibraryItem media = null;
            MediaItem mediaItem = null;
            bool createdLibrary = false;

            await WithDataContextAsync(ctx => CreateAndConnectMedia(ctx, entityName, keyRef, title, description, fileName, latitude, longitude, out library, out media, out mediaItem, out createdLibrary));

            ILocalFileInfo file = await GetLocalFileForMediaAsync(media);
            await file.WriteStreamAsync(dataStream);
            await file.SetReadOnly();

            await WithDataContextAsync(ctx =>
            {
                ctx.MediaLibraryItems.Attach(DocumentHandler.MobileAttachmentsProjection, media);
                media.AttachmentStatus = AttachmentStatus.RequiresUpload;
                ctx.SubmitChanges(false);
            });

            List<RowBase> changedRows = new List<RowBase>();
            if (createdLibrary)
            {
                changedRows.Add(library);
            }

            if (mediaItem != null)
            {
                changedRows.Add(mediaItem);
            }

            changedRows.Add(media);

            FireDataChangeEvent(_eventAggregator, changedRows);

            return media.RowId;
        }

        public async Task UploadMediaAsync(long mediaItemId, string fileName, Stream dataStream)
        {
            MediaLibraryItem media = null;

            await WithDataContextAsync(ctx =>
            {
                media = ctx.MediaLibraryItems.FirstOrDefault(x => x.ItemId == mediaItemId);

                if (media != null)
                {
                    ctx.MediaLibraryItems.Attach(DocumentHandler.MobileAttachmentsProjection, media);
                    media.MediaFile = fileName;
                    ctx.SubmitChanges(false);
                }
            });

            if (media == null)
            {
                throw new ExecutionException(string.Format(Strings.MediaNotFound, mediaItemId));
            }

            ILocalFileInfo file = await GetLocalFileForMediaAsync(media);
            await file.WriteStreamAsync(dataStream);

            await WithDataContextAsync(ctx =>
            {
                ctx.MediaLibraryItems.Attach(DocumentHandler.MobileAttachmentsProjection, media);
                media.AttachmentStatus = AttachmentStatus.RequiresUpload;
                ctx.SubmitChanges(false);
            });

            FireDataChangeEvent(_eventAggregator, media);
        }

        internal static void CreateAndConnectMedia(FwDataContext ctx, string luName, string keyRef, string title, string description, string fileName, double? latitude, double? longitude,
            out MediaLibrary preparedMediaLibrary, out MediaLibraryItem preparedMediaLibraryItem, out MediaItem preparedMediaItem, out bool createdMediaLibrary)
        {
            if (luName == null)
                throw new ArgumentNullException(nameof(luName));

            if (keyRef == null)
                throw new ArgumentNullException(nameof(keyRef));

            createdMediaLibrary = false;

            MediaLibrary library = ctx.MediaLibraries.FirstOrDefault(x => x.LuName == luName && x.KeyRef == keyRef && x.MainLibrary == true);
            if (library == null)
            {
                library = new MediaLibrary();
                library.LibraryId = (string)ctx.GenerateClientKey<MediaLibrary>(nameof(MediaLibrary.LibraryId));
                library.LuName = luName;
                library.KeyRef = keyRef;
                library.MainLibrary = true;
                ctx.MediaLibraries.InsertOnSubmit(DocumentHandler.MobileAttachmentsProjection, library);
                createdMediaLibrary = true;
            }

            MobileClientParam param = ctx.AppParameters.FirstOrDefault(x => x.Parameter == ServerCapabilities);

            MediaLibraryItem media = new MediaLibraryItem();

            if (param?.Value.Contains(MediaConnectCapability) == true)
            {
                MediaItem mediaItem = new MediaItem();

                mediaItem.ItemId = (long)ctx.GenerateClientKey<MediaItem>(nameof(MediaItem.ItemId));
                mediaItem.Name = title ?? Strings.Media;
                mediaItem.Description = description ?? fileName;
                mediaItem.MediaFile = fileName;
                mediaItem.MediaItemType = MediaType.Image;
                mediaItem.Latitude = latitude;
                mediaItem.Longitude = longitude;
                ctx.MediaItems.InsertOnSubmit(DocumentHandler.MobileAttachmentsProjection, mediaItem);
                ctx.SubmitChanges(true);
                preparedMediaItem = mediaItem;
                media.ItemId = mediaItem.ItemId;
                media.Latitude = mediaItem.Latitude;
                media.Longitude = mediaItem.Longitude;
            }
            else
            {
                media.ItemId = (long)ctx.GenerateClientKey<MediaLibraryItem>(nameof(MediaLibraryItem.ItemId));
                preparedMediaItem = null;
            }

            media.LibraryId = library.LibraryId;
            media.LibraryItemId = (long)ctx.GenerateClientKey<MediaLibraryItem>(nameof(MediaLibraryItem.LibraryItemId));
            media.Name = title ?? Strings.Media;
            media.Description = description ?? fileName;
            media.MediaFile = fileName;
            media.MediaItemType = MediaType.Image;
            media.AttachmentStatus = AttachmentStatus.Preparing;

            MobileObjectConnectionConfig config = ctx.ObjectConnectionConfigs.FirstOrDefault(x => x.Entity == luName && x.ProviderName == MediaLibraryProvider);
            media.PrivateMediaItem = config?.Private == true;

            ctx.MediaLibraryItems.InsertOnSubmit(DocumentHandler.MobileAttachmentsProjection, media);
            ctx.SubmitChanges(true);

            preparedMediaLibrary = library;
            preparedMediaLibraryItem = media;
        }

        internal static void ConnectMedia(FwDataContext context, FndMediaKeys mediaKeys, string luName, string keyRef,
            out MediaLibrary preparedMediaLibrary, out MediaLibraryItem preparedMediaLibraryItem, out bool createdMediaLibrary)
        {
            if (mediaKeys == null)
                throw new ArgumentNullException(nameof(mediaKeys));

            if (luName == null)
                throw new ArgumentNullException(nameof(luName));

            if (keyRef == null)
                throw new ArgumentNullException(nameof(keyRef));

            createdMediaLibrary = false;

            MediaLibrary library = context.MediaLibraries.FirstOrDefault(x => x.LuName == luName && x.KeyRef == keyRef && x.MainLibrary == true);
            if (library == null)
            {
                library = new MediaLibrary();
                library.LibraryId = (string)context.GenerateClientKey<MediaLibrary>(nameof(MediaLibrary.LibraryId));
                library.LuName = luName;
                library.KeyRef = keyRef;
                library.MainLibrary = true;
                context.MediaLibraries.InsertOnSubmit(DocumentHandler.MobileAttachmentsProjection, library);
                createdMediaLibrary = true;
            }

            MediaLibraryItem mediaReference = context.MediaLibraryItems.FirstOrDefault(x => x.ItemId == mediaKeys.ItemId);
            MediaLibraryItem media = new MediaLibraryItem();
            media.ItemId = mediaReference?.ItemId;
            media.LibraryId = library.LibraryId;
            media.LibraryItemId = (long)context.GenerateClientKey<MediaLibraryItem>(nameof(MediaLibraryItem.LibraryItemId));
            media.Name = mediaReference?.Name ?? Strings.Media;
            media.Description = media.Name;
            media.MediaFile = mediaReference?.MediaFile;
            media.MediaItemType = mediaReference?.MediaItemType;
            media.AttachmentStatus = AttachmentStatus.Uploaded;
            media.DefaultMedia = createdMediaLibrary;
            MobileObjectConnectionConfig config = context.ObjectConnectionConfigs.FirstOrDefault(x => x.Entity == luName && x.ProviderName == MediaLibraryProvider);
            media.PrivateMediaItem = config?.Private == true;

            context.MediaLibraryItems.InsertOnSubmit(DocumentHandler.MobileAttachmentsProjection, media);
            context.SubmitChanges(true);

            preparedMediaLibraryItem = media;
            preparedMediaLibrary = library;
        }

        public async Task<MediaInfo> GetMediaAsync(long mediaRowId)
        {
            return await WithDataContextAsync(ctx =>
            {
                return (from media in ctx.MediaLibraryItems
                        join lib in ctx.MediaLibraries on media.LibraryId equals lib.LibraryId
                        where media.RowId == mediaRowId
                        select new MediaInfo
                        {
                            Library = lib,
                            MediaItem = media
                        }).FirstOrDefault();
            });
        }

        public async Task<IEnumerable<MediaInfo>> GetMediaAsync(string entityName, string keyRef)
        {
            await _attachmentHandler.DownloadAttachmentsIfNeeded(entityName, keyRef, false, CancellationToken.None);

            return await WithDataContextAsync(ctx =>
                GetMediaQuery(ctx, entityName, keyRef).ToArray());
        }

        public async Task<int> GetMediaCountAsync(string entityName, string keyRef)
        {
            await _attachmentHandler.DownloadAttachmentsIfNeeded(entityName, keyRef, true, CancellationToken.None);

            return await WithDataContextAsync(ctx =>
                GetMediaQuery(ctx, entityName, keyRef).Count());
        }

        private static IQueryable<MediaInfo> GetMediaQuery(FwDataContext ctx, string entityName, string keyRef)
        {
            return from media in ctx.MediaLibraryItems
                   join lib in ctx.MediaLibraries on media.LibraryId equals lib.LibraryId
                   where lib.LuName == entityName && lib.KeyRef == keyRef
                   orderby media.Description
                   select new MediaInfo
                   {
                       Library = lib,
                       MediaItem = media
                   };
        }

        public async Task CleanupOldMedia()
        {
            HashSet<string> fileNames = new HashSet<string>();

            await WithDataContextAsync(ctx =>
            {
                CleanupMediaLibraries(ctx);
                CleanupMediaLibraryItems(ctx);

                // Use ToBatch here so we do not lock the rest of the app up
                // if there is a lot of media on the device
                foreach (MediaLibraryItem mediaItem in ctx.MediaLibraryItems.ToBatch())
                {
                    string fileName = GetFileName(mediaItem);
                    fileNames.Add(fileName);
                }
            });

            string mediaFolderPath = AttachmentFolders.GetMediaFolderPath(_dbId);
            ILocalFolderInfo mediaFolder = await _localStorage.PrivateStorage.GetFolderInfoAsync(mediaFolderPath);

            List<ILocalFileInfo> filesToDelete = new List<ILocalFileInfo>();
            foreach (ILocalFileInfo file in await mediaFolder.GetFilesAsync())
            {
                string fileName = Path.GetFileName(file.FilePath);
                if (!fileNames.Contains(fileName))
                {
                    filesToDelete.Add(file);
                }
            }

            if (filesToDelete.Count > 0)
            {
                _logger.Trace("CleanupOldMedia: Removing {0} local files", filesToDelete.Count.ToString());

                foreach (ILocalFileInfo file in filesToDelete)
                {
                    try
                    {
                        await file.DeleteAsync();
                    }
                    catch (Exception)
                    {
                        // ignored
                    }
                }
            }
        }

        private void CleanupMediaLibraries(FwDataContext ctx)
        {
            try
            {
                string[] entities = ctx.ObjectConnectionConfigs
                    .Where(x => x.ProviderName == MediaLibraryProvider)
                    .Select(x => x.Entity)
                    .ToArray();

                List<MediaLibrary> mediaLibsToRemove = new List<MediaLibrary>();
                foreach (MediaLibrary mediaLib in ctx.MediaLibraries.Where(x => entities.Contains(x.LuName)).ToBatch())
                {
                    ObjPrimaryKey pk = ObjPrimaryKey.FromKeyRef(_model, mediaLib.LuName, mediaLib.KeyRef);
                    if (pk != null && !ctx.RecordExists(pk))
                    {
                        mediaLibsToRemove.Add(mediaLib);
                    }
                }

                if (mediaLibsToRemove.Count > 0)
                {
                    _logger.Trace("CleanupOldMedia: Removing {0} MediaLibrary records", mediaLibsToRemove.Count.ToString());
                    ctx.MediaLibraries.DeleteAllOnSubmit(DocumentHandler.MobileAttachmentsProjection, mediaLibsToRemove);
                    ctx.SubmitChanges(false);
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private void CleanupMediaLibraryItems(FwDataContext ctx)
        {
            try
            {
                MediaLibraryItem[] itemsToRemove = (
                        from item in ctx.MediaLibraryItems
                        join mediaLib in ctx.MediaLibraries on item.LibraryId equals mediaLib.LibraryId
                        into mediaLibs
                        where
                            item.AttachmentStatus != AttachmentStatus.RequiresUpload &&
                            item.AttachmentStatus != AttachmentStatus.Uploading &&
                            item.AttachmentStatus != AttachmentStatus.UploadFailed &&
                            !mediaLibs.Any()
                        select item
                        ).ToBatch().ToArray();

                if (itemsToRemove.Length > 0)
                {
                    _logger.Trace("CleanupOldMedia: Removing {0} MediaLibraryItem records", itemsToRemove.Length.ToString());
                    ctx.MediaLibraryItems.DeleteAllOnSubmit(DocumentHandler.MobileAttachmentsProjection, itemsToRemove);
                    ctx.SubmitChanges(false);
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        public async Task RequestDownloadAsync(MediaLibraryItem mediaItem)
        {
            await ChangeAttachmentStatus(mediaItem, AttachmentStatus.Downloading);
        }

        public async Task RequestUploadAsync(MediaLibraryItem mediaItem)
        {
            await ChangeAttachmentStatus(mediaItem, AttachmentStatus.RequiresUpload);
        }

        public async Task CancelUploadAsync(MediaLibraryItem mediaItem)
        {
            await ChangeAttachmentStatus(mediaItem, AttachmentStatus.UploadCanceled);
        }

        private async Task ChangeAttachmentStatus(MediaLibraryItem mediaItem, AttachmentStatus newStatus)
        {
            await WithDataContextAsync(ctx =>
            {
                // Don't change the original row on a background thread
                MediaLibraryItem updateRow = CloneRow(mediaItem, false);
                ctx.MediaLibraryItems.Attach(DocumentHandler.MobileAttachmentsProjection, updateRow);
                updateRow.AttachmentStatus = newStatus;
                ctx.SubmitChanges(false);
            });

            mediaItem.AttachmentStatus = newStatus;
            FireDataChangeEvent(_eventAggregator, mediaItem);
        }

        private async Task<MobileObjectConnectionConfig> GetConfigAsync(string entityName)
        {
            if (_configs.TryGetValue(entityName, out MobileObjectConnectionConfig cachedConfig))
            {
                return cachedConfig;
            }

            return await WithDataContextAsync(ctx =>
            {
                MobileObjectConnectionConfig config = ctx.ObjectConnectionConfigs.FirstOrDefault(x => x.Entity == entityName && x.ProviderName == MediaLibraryProvider);
                _configs[entityName] = config;
                return config;
            });
        }

        public async Task<bool> IsEnabledFor(string entityName)
        {
            if (!_mediaReadAllowed)
            {
                return false;
            }

            IMetaTable table = _model.GetTable(RemoteNaming.ToTableName(entityName));
            if (table == null || table.TableImplementation == TableImplementation.View)
            {
                return false;
            }

            return await GetConfigAsync(entityName) != null;
        }

        public async Task<bool> CanCreateNew(string entityName, string keyRef)
        {
            MobileObjectConnectionConfig config = await GetConfigAsync(entityName);
            return _mediaWriteAllowed && config?.Readonly != true;
        }

        public async Task<bool> CanReplaceMedia(string entityName, string keyRef)
        {
            return _replaceMediaAllowed && await CanCreateNew(entityName, keyRef);
        }

        public async Task<ILocalFileInfo> GetMediaFileForDisplayAsync(string entityName, string keyRef, CancellationToken cancelToken)
        {
            List<MediaLibraryItem> mediaItems = await GetMediaItems(entityName, keyRef);

            foreach (MediaLibraryItem mediaItem in mediaItems)
            {
                while (true)
                {
                    cancelToken.ThrowIfCancellationRequested();

                    if (mediaItem == null)
                    {
                        break;
                    }

                    if (mediaItem.AttachmentStatus == null ||
                        mediaItem.AttachmentStatus == AttachmentStatus.RequiresDownload ||
                        mediaItem.AttachmentStatus == AttachmentStatus.Downloading)
                    {
                        if (mediaItem.AttachmentStatus == null)
                        {
                            await RequestDownloadAsync(mediaItem);
                        }

                        await Task.Delay(500, cancelToken);
                    }
                    else if (mediaItem.AttachmentStatus == AttachmentStatus.DownloadFailed)
                    {
                        break;
                    }
                    else
                    {
                        ILocalFileInfo fileInfo = await GetLocalFileForMediaAsync(mediaItem);

                        // Verify if the file exists and it's a valid image
                        if (fileInfo != null && await fileInfo.ExistsAsync())
                        {
                            using (Stream imageStream = await fileInfo.OpenStreamAsync(LocalFileMode.OpenOrCreate, LocalFileAccess.Read))
                            {
                                if (imageStream != null)
                                {
                                    byte[] buffer = new byte[6];

                                    int offset = 0;
                                    while (offset < buffer.Length)
                                    {
                                        int read = imageStream.Read(buffer, offset, buffer.Length - offset);

                                        // Not enough data to qualify as an image
                                        if (read == 0)
                                        {
                                            break;
                                        }

                                        offset += read;
                                    }

                                    // Check if image data is displayable
                                    if (IsValidImage(buffer))
                                    {
                                        return fileInfo;
                                    }
                                }
                            }
                        }

                        break;
                    }
                }
            }

            return null;
        }

        private async Task<List<MediaLibraryItem>> GetMediaItems(string entityName, string keyRef)
        {
            return await WithDataContextAsync(ctx =>
            {
                IQueryable<MediaLibraryItem> items = GetMediaQuery(ctx, entityName, keyRef)
                    .Where(x => x.MediaItem.MediaItemType == MediaType.Image)
                    .OrderByDescending(x => x.MediaItem.DefaultMedia == true)
                    .ThenByDescending(x => x.Library.MainLibrary == true)
                    .ThenBy(x => x.MediaItem.LibraryId)
                    .ThenBy(x => x.MediaItem.LibraryItemId)
                    .Select(x => x.MediaItem);

                return items.ToList();
            }, null);
        }

        public async Task DisconnectMediaAsync(MediaLibraryItem mediaItem)
        {
            if (mediaItem != null)
            {
                await WithDataContextAsync(ctx =>
                {
                    ObjPrimaryKey pk = ObjPrimaryKey.FromPrimaryKey(_model, mediaItem);
                    mediaItem.PrimaryKeyString = pk.ToFormattedKeyRef(MediaProjectionName, true);
                    ctx.MediaLibraryItems.DeleteOnSubmit(MediaProjectionName, mediaItem);
                    ctx.SubmitChanges(true);
                });
            }
        }

        public static bool IsValidImage(byte[] bytes)
        {
            byte[] bmp = new byte[] { 0x42, 0x4D };
            byte[] gif = new byte[] { 0x47, 0x49, 0x46 };
            byte[] png = new byte[] { 0x89, 0x50, 0x4E, 0x47 };
            byte[] jpeg = new byte[] { 0xFF, 0xD8, 0xFF, 0xE0 };

            if (bmp.SequenceEqual(bytes.Take(bmp.Length)) ||
                gif.SequenceEqual(bytes.Take(gif.Length)) ||
                png.SequenceEqual(bytes.Take(png.Length)) ||
                jpeg.SequenceEqual(bytes.Take(jpeg.Length)))
            {
                return true;
            }

            return false;
        }

        public MediaInfo AddBinaryMedia(FwDataContext context, string entityName, string keyRef, string title, string description, string fileName, Stream dataStream)
        {
            // If entityName includes ":", treat it as "special/dynamic step" scenario
            bool copyMediaFile = false;

            if (entityName == null)
                throw new ArgumentNullException(nameof(entityName));

            if (keyRef == null)
                throw new ArgumentNullException(nameof(keyRef));

            if (string.IsNullOrEmpty(fileName))
                throw new ArgumentException($"{nameof(fileName)} must have a value", nameof(fileName));

            if (entityName.Contains(":"))
            {
                // Attempt a safer split:
                string[] parts = entityName.Split(':');
                if (parts.Length >= 2)
                {
                    copyMediaFile = true;
                    entityName = parts[parts.Length - 1]; // Use the last part as the entityName
                }
                else
                {
                    _logger.Error($"Unexpected format for entityName '{entityName}'. No valid part after ':' to parse.");
                    return null;
                }
            }

            if (!IsEnabledFor(context, entityName))
            {
                _logger.Error($"Media attachment is not enabled for {entityName}");
                return null;
            }

            // Check dataStream for null, which would fail the write operation
            if (!copyMediaFile && dataStream == null)
            {
                _logger.Error($"Input dataStream is null. Cannot write media file {fileName}.");
                return null;
            }

            string mediaFileName = GetElementName(fileName, copyMediaFile);
            title = GetElementName(title, copyMediaFile);
            description = GetElementName(description, copyMediaFile);

            CreateAndConnectMedia(context, entityName, keyRef, title, description, mediaFileName, null, null, out MediaLibrary library, out MediaLibraryItem media, out MediaItem mediaItem, out bool createdLibrary);

            ILocalFileInfo file = GetLocalFileForMedia(context, media);

            Task.Run(async () =>
            {
                if (copyMediaFile == false)
                {
                    await file.WriteStreamAsync(dataStream);
                    await file.SetReadOnly();
                }
                else
                {
                    // Copy from a previously stored file from picture step
                    ILocalFileInfo originalFile = await GetLocalFileForLogoAsync(fileName);

                    if (originalFile == null)
                    {
                        _logger.Error($"Could not find original file named '{fileName}' to copy.");
                        return;
                    }

                    try
                    {
                        await originalFile.RenameAsync(Path.GetFileName(file.FilePath));
                    }
                    catch (Exception ex)
                    {
                        _logger.HandleException(ExceptionType.Recoverable, ex);
                    }
                }
            }).ConfigureAwait(false).GetAwaiter().GetResult();

            context.MediaLibraryItems.Attach(DocumentHandler.MobileAttachmentsProjection, media);
            media.AttachmentStatus = AttachmentStatus.RequiresUpload;
            context.SubmitChanges(false);

            List<RowBase> changedRows = new List<RowBase>();
            if (createdLibrary)
            {
                changedRows.Add(library);
            }

            if (mediaItem != null)
            {
                changedRows.Add(mediaItem);
            }

            changedRows.Add(media);

            FireDataChangeEvent(_eventAggregator, changedRows);

            MediaInfo info = new MediaInfo()
            {
                Library = library,
                MediaItem = media
            };

            return info;
        }

        private string GetElementName(string elementInput, bool copyMediaFile)
        {
            if (!copyMediaFile)
            {
                return elementInput;
            }                

            // file name example: step_3_[uuid]_mountain.jpg
            // the file name to be retrieved from this method is [uuid]_mountain.jpg
            var match = Regex.Match(
                elementInput,
                @"^step_[^_]+_(?<file>.+)$",
                RegexOptions.IgnoreCase
            );

            // If it doesn't match, return null or handle accordingly
            if (!match.Success)
            {
                return elementInput;
            }

            // Return the part matched by the named group "file"
            return match.Groups["file"].Value;
        }

        private bool IsEnabledFor(FwDataContext context, string entityName)
        {
            if (!_mediaReadAllowed)
            {
                return false;
            }

            IMetaTable table = _model.GetTable(RemoteNaming.ToTableName(entityName));
            if (table == null || table.TableImplementation == TableImplementation.View)
            {
                return false;
            }

            return GetConfig(context, entityName) != null;
        }

        private MobileObjectConnectionConfig GetConfig(FwDataContext context, string entityName)
        {
            if (_configs.TryGetValue(entityName, out MobileObjectConnectionConfig cachedConfig))
            {
                return cachedConfig;
            }

            MobileObjectConnectionConfig config = context.ObjectConnectionConfigs.FirstOrDefault(x => x.Entity == entityName && x.ProviderName == MediaLibraryProvider);
            _configs[entityName] = config;
            return config;
        }

        private ILocalFileInfo GetLocalFileForMedia(FwDataContext context, MediaLibraryItem mediaItem)
        {
            if (mediaItem.MediaFile == null && mediaItem.RowId != 0)
            {
                // MediaFile may have been filled in by the sync service so we
                // should go get the db record to make sure we can get the file extension
                mediaItem = context.MediaLibraryItems.FirstOrDefault(x => x.RowId == mediaItem.RowId) ?? mediaItem;
            }

            string fileName = GetFileName(mediaItem);
            string filePath = Path.Combine(AttachmentFolders.GetMediaFolderPath(_dbId), fileName);
            return _localStorage.PrivateStorage.GetFileInfoAsync(filePath).Result;
        }
    }
}
