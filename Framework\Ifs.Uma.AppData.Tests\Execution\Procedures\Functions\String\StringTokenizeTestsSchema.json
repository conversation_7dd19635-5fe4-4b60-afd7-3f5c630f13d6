﻿{
  "name": "FndTstOffline",
  "version": "1706901162:1948287535",
  "component": "FNDMOT",
  "projection": {
    "service": "FndMotOffline.svc",
    "version": "1948287535",
    "contains": {},
    "entities": {},
    "procedures": {

      "Function<String_Tokenize>": {
        "name": "String_Tokenize",
        "type": "Function",
        "params": [
          {
            "name": "TextInput",
            "dataType": "Text"
          },
          {
            "name": "Delimiter",
            "dataType": "Text"
          }
        ],
        "layers": [
          {
            "vars": [
              {
                "name": "Result",
                "dataType": "List<Text>"
              }
            ],
            "execute": [
              {
                "call": {
                  "method": "proc",
                  "args": {
                    "namespace": "String",
                    "name": "Tokenize",
                    "paramsArray": [ "${TextInput}", "${Delimiter}" ]
                  }
                },
                "assign": "Result"
              },
              {
                "call": {
                  "method": "return",
                  "args": { "name": "Result" }
                }
              }
            ]
          }
        ]
      }
    }
  }
}
