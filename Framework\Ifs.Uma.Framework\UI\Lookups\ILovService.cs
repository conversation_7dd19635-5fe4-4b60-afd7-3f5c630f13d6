﻿using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Fields;

namespace Ifs.Uma.Framework.UI.Lookups
{
    public interface ILovService
    {
        Task<bool> OpenLovAsync(LookupField field, CpiField cpifield, ViewData viewData);
        Task<ObjPrimaryKey> OpenLovAsync(EntityQuery entityQuery, CpiSelector selector, string title, ObjPrimaryKey selectedRecord);
        Task<bool> OpenLovAsync(LookupField field, CpiReference reference, RecordData data);
    }
}
