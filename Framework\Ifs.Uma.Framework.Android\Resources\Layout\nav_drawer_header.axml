<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rootLayout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/nav_header_height"
    android:background="?attr/colorPrimary"
    android:theme="@style/ThemeOverlay.AppCompat.Dark"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingTop="24dp">
    <Ifs.Uma.UI.Images.UmaImageView
        android:id="@+id/header_icon"
        android:layout_width="@dimen/nav_header_icon_width"
        android:layout_height="@dimen/nav_header_icon_height"
        android:layout_marginBottom="@dimen/nav_header_vertical_spacing"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="@dimen/nav_header_vertical_spacing" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:orientation="vertical">
        <TextView
            android:id="@+id/header_app_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.AppCompat.Title"
            android:textDirection="locale"
            android:textColor="@color/IfsWhite" />
        <TextView
            android:id="@+id/header_app_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.AppCompat.Subhead"
            android:textDirection="locale"
            android:textColor="@color/IfsWhite" />
        <TextView
            android:id="@+id/header_app_version"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/IfsGrayLight" />
    </LinearLayout>
</LinearLayout>
