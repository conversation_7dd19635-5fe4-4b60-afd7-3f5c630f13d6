﻿using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Ifs.Uma.AppData.Execution
{
    internal static class ExecutionUtils
    {
        public static Dictionary<string, object> CloneParameters(IMetadata metadata, IReadOnlyDictionary<string, object> parameters, CpiProc proc, RemoteRow boundRecord, object resultVar)
        {
            Dictionary<string, object> clonedParameters = ExecutionUtils.CloneParameters(metadata, parameters, proc, resultVar);
            clonedParameters[ProcedureExecutor.RecordVarName] = ExecutionUtils.CloneRow(metadata, boundRecord);
            return clonedParameters;
        }

        public static Dictionary<string, object> CloneParameters(IMetadata metadata, IReadOnlyDictionary<string, object> parameters, CpiProc proc, object resultVar)
        {
            Dictionary<string, object> clonedParameters = ExecutionUtils.CloneParameters(metadata, parameters);
            if (proc.Parameters != null && proc.Parameters.Any(x => x.Name == ProcedureExecutor.ResultVarName))
            {
                clonedParameters[ProcedureExecutor.ResultVarName] = ExecutionUtils.CloneValue(metadata, resultVar);
            }
            return clonedParameters;
        }

        public static Dictionary<string, object> CloneParameters(IMetadata metadata, IReadOnlyDictionary<string, object> parameters)
        {
            Dictionary<string, object> ret = new Dictionary<string, object>();
            if (parameters != null)
            {
                foreach (var kvp in parameters)
                {
                    ret[kvp.Key] = CloneValue(metadata, kvp.Value);
                }
            }

            return ret;
        }

        public static object CloneValue(IMetadata metadata, object value)
        {
            if (value != null)
            {
                if (value is RemoteRow row)
                {
                    return CloneRow(metadata, row);
                }
                else if (value is MarbleList mList)
                {
                    return mList.Clone();
                }
                else if (value is IList<object> list)
                {
                    return new List<object>(list);
                }
                else
                {
                    return value;
                }
            }

            return value;
        }

        public static RemoteRow CloneRow(IMetadata metadata, RemoteRow row)
        {
            string entitySetName = row.EntitySetName;
            string eTagString = row.ETag;
            string arraySource = row.ArraySourceName;
            string primarKeyString = row.PrimaryKeyString;
            if (row != null)
            {
                IMetaTable metaTable = metadata.MetaModel.GetTable(row.TableName);
                if (metaTable != null)
                {
                    RemoteRow clonedRow = (RemoteRow)metaTable.CloneRow(row);
                    clonedRow.EntitySetName = entitySetName;
                    clonedRow.ETag = eTagString;
                    clonedRow.ArraySourceName = arraySource;
                    clonedRow.PrimaryKeyString = primarKeyString;
                    return clonedRow;
                }
            }
            row.EntitySetName = entitySetName;
            row.ETag = eTagString;
            row.ArraySourceName = arraySource;
            row.PrimaryKeyString = primarKeyString;
            return row;
        }

        public static string ValueToPreviewString(object value)
        {
            if (value == null)
            {
                return "null";
            }

            if (value is RemoteRow row)
            {
                string entityName = RemoteNaming.ToEntityName(row.TableName);
                return "Record<" + entityName + ">";
            }

            if (value is MarbleList mList)
            {
                return mList.TypeInfo == null ? "List" : mList.TypeInfo.ToString();
            }

            if (value is IList<object>)
            {
                return "List";
            }

            string strValue = ObjectConverter.ToString(value);
            if (strValue != null && strValue.Length > 100)
            {
                strValue = strValue.Substring(0, 97) + "...";
            }

            if (value is string)
            {
                strValue = "'" + strValue + "'";
            }

            return strValue;
        }
        
        public static bool TryConvert(object input, CpiDataType dataType, string subType, bool isCollection, out object output)
        {
            output = input;

            if (input == null)
            {
                return true;
            }

            if (isCollection)
            {
                if (input is MarbleList mList)
                {
                    if (mList.TypeInfo.DataType != dataType || mList.TypeInfo.SubType != subType)
                    {
                        return false;
                    }
                }
                else if (input is IList listToConvert)
                {
                    CpiTypeInfo typeInfo = new CpiTypeInfo();
                    typeInfo.DataType = dataType;
                    typeInfo.SubType = subType;
                    typeInfo.IsCollection = true;
                    MarbleList list = new MarbleList(typeInfo);
                    foreach (object item in listToConvert)
                    {
                        if (!list.TryConvertAndAdd(item))
                        {
                            return false;
                        }
                    }
                    output = list;
                }
                else
                {
                    return false;
                }
            }
            else if (dataType == CpiDataType.Structure || dataType == CpiDataType.Entity)
            {
                RemoteRow structure = input as RemoteRow;
                if (structure == null)
                {
                    return false;
                }

                if (!string.IsNullOrEmpty(subType))
                {
                    string tableName = RemoteNaming.ToTableName(subType);
                    if (tableName != structure.TableName)
                    {
                        return false;
                    }
                }
            }
            else if (!MetadataExtensions.TryConvertToType(dataType, input, out output))
            {
                return false;
            }

            return true;
        }
    }
}
