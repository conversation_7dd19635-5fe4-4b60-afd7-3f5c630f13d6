﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using SQLitePCL;

namespace Ifs.Uma.Database.SQLite
{
    internal class SQLiteDataReader : DbDataReader
    {
        internal SQLiteDataReader(SQLiteConnection connection, SQLiteCommand command, sqlite3_stmt statement,
            ILogger logger, bool traceFlag, string sql, DbTransaction transaction)
            : base(logger, traceFlag, sql)
        {
            if (connection == null) throw new ArgumentNullException("connection");
            if (command == null) throw new ArgumentNullException("command");
            m_connection = connection;
            m_command = command;
            m_statement = statement;
            m_transaction = transaction;
        }

        protected override void Dispose(bool disposing)
        {
            sqlite3_stmt statement = Interlocked.Exchange(ref m_statement, null);
            if (statement != null)
            {
                raw.sqlite3_finalize(statement);
            }

            if (disposing)
            {
                SQLiteConnection connection = Interlocked.Exchange(ref m_connection, null);
                if (connection != null && m_gotLock)
                {
                    connection.ExitReadLock();
                }
                SQLiteCommand command = Interlocked.Exchange(ref m_command, null);
                if (command != null)
                {
                    command.DisposeReader(this);
                }
            }

            base.Dispose(disposing);
        }

        private SQLiteConnection m_connection;
        private SQLiteCommand m_command;
        private DbTransaction m_transaction;
        private sqlite3_stmt m_statement;
        private bool m_gotLock;

        protected override bool DoRead()
        {
            SQLiteConnection connection = m_connection;
            sqlite3_stmt statement = m_statement;
            bool result = false;
            if (statement != null && connection != null)
            {
                if (m_transaction == null && !m_gotLock)
                {
                    connection.EnterReadLock();
                    m_gotLock = true;
                }
                int r = raw.sqlite3_step(statement);
                switch (r)
                {
                    case raw.SQLITE_ROW:
                        result = true;
                        break;
                    case raw.SQLITE_DONE:
                        break;
                    default:
                        throw new SQLiteException(r, connection.GetErrorMessage());
                }
            }
            return result;
        }

        protected override bool DoIsDBNull(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return raw.sqlite3_column_type(statement, i) == raw.SQLITE_NULL;
        }

        protected override bool DoGetBool(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return raw.sqlite3_column_int(statement, i) != 0;
        }

        protected override byte[] DoGetBytes(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return raw.sqlite3_column_blob(statement, i);
        }

        protected override DateTime DoGetDateTime(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return new DateTime(raw.sqlite3_column_int64(statement, i));
        }

        protected override long DoGetInt64(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return raw.sqlite3_column_int64(statement, i);
        }

        protected override string DoGetString(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return raw.sqlite3_column_text(statement, i);
        }

        protected override double DoGetDouble(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return raw.sqlite3_column_double(statement, i);
        }

        protected override int DoFieldCount()
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return raw.sqlite3_column_count(statement);
        }

        protected override Type DoGetFieldType(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            switch (raw.sqlite3_column_type(statement, i))
            {
                case raw.SQLITE_TEXT:
                    return typeof(string);
                case raw.SQLITE_INTEGER:
                    return typeof(long);
                case raw.SQLITE_FLOAT:
                    return typeof(double);
                case raw.SQLITE_BLOB:
                    return typeof(byte[]);
                case raw.SQLITE_NULL:
                    return null;
            }
            throw new ArgumentOutOfRangeException("i");
        }

        protected override object DoGetValue(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            switch (raw.sqlite3_column_type(statement, i))
            {
                case raw.SQLITE_TEXT:
                    return raw.sqlite3_column_text(statement, i);
                case raw.SQLITE_INTEGER:
                    return raw.sqlite3_column_int64(statement, i);
                case raw.SQLITE_FLOAT:
                    return raw.sqlite3_column_double(statement, i);
                case raw.SQLITE_BLOB:
                    return raw.sqlite3_column_blob(statement, i);
                case raw.SQLITE_NULL:
                    return null;
            }
            throw new ArgumentOutOfRangeException("i");
        }

        protected override decimal DoGetDecimal(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return (decimal)raw.sqlite3_column_double(statement, i);
        }

        protected override int DoGetInt32(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return raw.sqlite3_column_int(statement, i);
        }

        protected override short DoGetInt16(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return (short)raw.sqlite3_column_int(statement, i);
        }

        protected override Guid DoGetGuid(int i)
        {
            sqlite3_stmt statement = m_statement;
            if (statement == null) throw new ObjectDisposedException("SQLiteDataReader");
            return Guid.ParseExact(raw.sqlite3_column_text(statement, i), "D");
        }
    }
}
