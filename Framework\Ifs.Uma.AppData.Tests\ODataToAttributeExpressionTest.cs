﻿using Ifs.Uma.AppData.AttributeExpressions;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class OdataToAttributeExpressionConverterTests
    {
        [Test]
        public void GetLikeExpression_StartsWith()
        {
            string value = "startswith(CompanyName,'Alfr')";
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(value);
            Assert.IsNotNull(attrExpression);
            Assert.AreEqual("CompanyName LIKE 'Alfr%'", attrExpression.ToString());
        }

        [Test]
        public void GetLikeExpression_EndsWith()
        {
            string value = "endswith(CompanyName,'Alfr')";
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(value);
            Assert.IsNotNull(attrExpression);
            Assert.AreEqual("CompanyName LIKE '%Alfr'", attrExpression.ToString());
        }

        [Test]
        public void GetLikeExpression_Contains()
        {
            string value = "contains(CompanyName,'Alfr')";
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(value);
            Assert.IsNotNull(attrExpression);
            Assert.AreEqual("CompanyName LIKE '%Alfr%'", attrExpression.ToString());
        }

        [Test]
        public void GetCompareExpression_Equals_For_Numbers()
        {
            string value = "age eq 21";
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(value);
            Assert.IsNotNull(attrExpression);
            Assert.AreEqual("age Equals 21", attrExpression.ToString());
        }

        [Test]
        public void GetCompareExpression_Equals_For_Strings()
        {
            string value = "Food eq 'Milk'";
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(value);
            Assert.IsNotNull(attrExpression);
            Assert.AreEqual("Food Equals Milk", attrExpression.ToString());
        }

        [Test]
        public void GetCompareExpression_Not_Equals_For_Numbers()
        {
            string value = "age ne 21";
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(value);
            Assert.IsNotNull(attrExpression);
            Assert.AreEqual("age NotEquals 21", attrExpression.ToString());
        }

        [Test]
        public void GetCompareExpression_Not_Equals_For_Strings()
        {
            string value = "Food ne 'Milk'";
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(value);
            Assert.IsNotNull(attrExpression);
            Assert.AreEqual("Food NotEquals Milk", attrExpression.ToString());
        }

        [Test]
        [TestCase("endswith(CompanyName,'Alfr') and Food ne 'Milk' or endswith(CompanyName,'Alfr') ", "((CompanyName LIKE '%Alfr' And Food NotEquals Milk) Or CompanyName LIKE '%Alfr')")]
        [TestCase("endswith(CompanyName,'Alfr') AND Food ne 'Milk'", "(CompanyName LIKE '%Alfr' And Food NotEquals Milk)")]
        [TestCase("endswith(CompanyName,'Alfr') OR Food ne 'Milk'", "(CompanyName LIKE '%Alfr' Or Food NotEquals Milk)")]
        public void GetComplexExpression(string input, string expectedValue)
        {
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(input);
            Assert.IsNotNull(attrExpression);
            Assert.AreEqual(expectedValue, attrExpression.ToString());
        }

        [Test]
        [TestCase(null)]
        [TestCase("age NotEquals 21 and ")]
        [TestCase("^&*(()")]
        [TestCase("EndsWITH(sdsd, sdsds)")]
        [TestCase("endswith(CompanyName,'Alfr') OR *&*()'")]
        public void Test_Invalid_Expression(string input)
        {
            AttributeExpression attrExpression = OdataToAttributeExpressionConverter.GetFilterExpression(input);
            Assert.AreEqual(attrExpression, null);
        }
    }
}
