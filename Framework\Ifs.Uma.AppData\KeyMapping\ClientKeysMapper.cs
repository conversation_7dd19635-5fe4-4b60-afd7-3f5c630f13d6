﻿using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;

namespace Ifs.Uma.AppData.KeyMapping
{
    public sealed class ClientKeysMapper : IClientKeysMapper
    {
        private readonly ILogger _logger;
        private readonly LockedDictionary<ObjPrimaryKey, ObjPrimaryKey> _serverToClientKeyMap;
        private readonly LockedDictionary<string, string> _serverToClientMediaLibId;

        private IMetaModel _metaModel;
        private IMetaDataMember _mediaLibraryId;

        public ClientKeysMapper(ILogger logger)
        {
            _logger = logger;
            _serverToClientKeyMap = new LockedDictionary<ObjPrimaryKey, ObjPrimaryKey>();
            _serverToClientMediaLibId = new LockedDictionary<string, string>();
        }

        public void Load(IMetaModel metaModel, ClientKeysMap[] keys)
        {
            _metaModel = metaModel;
            _serverToClientKeyMap.Clear();
            _serverToClientMediaLibId.Clear();
            _mediaLibraryId = null;

            if (keys != null)
            {
                RegisterKeys(keys);
            }
        }

        public MessageTableData MapServerToClientKeys(MessageTableData data)
        {
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (_metaModel == null) throw new InvalidOperationException($"{nameof(ClientKeysMapper)} has not yet loaded");

            if (_serverToClientKeyMap.Count == 0 || _metaModel == null)
            {
                // Nothing to map
                return data;
            }

            IMetaTable table = _metaModel.GetTable(data.TableName);
            if (table != null)
            {
                DictionaryMemberAccessor memberAccessor = new DictionaryMemberAccessor(data.RowData.ColumnData);

                IDictionary<IMetaDataMember, object> alteredValues = ExtractClientKeys(table, memberAccessor);

                if (alteredValues != null)
                {
                    MessageTableData newData = new MessageTableData();
                    newData.TableName = data.TableName;
                    newData.RowData = new MessageRowData();
                    Dictionary<string, object> newDataValues = new Dictionary<string, object>();
                    foreach (var item in data.RowData.ColumnData)
                    {
                        newDataValues.Add(item.Key, item.Value);
                    }
                    foreach (var item in alteredValues)
                    {
                        newDataValues[item.Key.ColumnName] = item.Value;
                    }
                    newData.RowData.ColumnData = newDataValues;
                    return newData;
                }
            }

            return data;
        }

        public void MapServerToClientKeys(RemoteRow row)
        {
            if (row == null) throw new ArgumentNullException(nameof(row));
            if (_metaModel == null) throw new InvalidOperationException($"{nameof(ClientKeysMapper)} has not yet loaded");

            if (_serverToClientKeyMap.Count == 0 || _metaModel == null)
            {
                // Nothing to map
                return;
            }

            IMetaTable table = _metaModel.GetTable(row.TableName);
            if (table != null)
            {
                RowMemberAccessor memberAccessor = new RowMemberAccessor(table, row);

                IDictionary<IMetaDataMember, object> alteredValues = ExtractClientKeys(table, memberAccessor);

                if (alteredValues != null)
                {
                    foreach (var item in alteredValues)
                    {
                        item.Key.SetValue(row, item.Value);
                    }
                }
            }
        }

        [SuppressMessage("Microsoft.Design", "CA1031:DoNotCatchGeneralExceptionTypes",
                Justification = "Don't want primary key mapping to fail")]
        private IDictionary<IMetaDataMember, object> ExtractClientKeys(IMetaTable table, IMemberAccessor memberAccessor)
        {
            IDictionary<IMetaDataMember, object> alteredValues = null;

            try
            {
                ObjPrimaryKey serverPk = ObjPrimaryKey.FromPrimaryKey(table, memberAccessor);
                if (serverPk != null)
                {
                    ObjPrimaryKey clientPk;
                    if (_serverToClientKeyMap.TryGetValue(serverPk, out clientPk))
                    {
                        clientPk.ExtractValues(ref alteredValues);
                    }
                }

                IEnumerable<IMetaRelation> relations = _metaModel.GetRelations(table);
                foreach (IMetaRelation relation in relations)
                {
                    ObjPrimaryKey relationServerPk = ObjPrimaryKey.FromRelation(_metaModel, relation, memberAccessor);
                    if (relationServerPk != null)
                    {
                        ObjPrimaryKey relationClientPk;
                        if (_serverToClientKeyMap.TryGetValue(relationServerPk, out relationClientPk))
                        {
                            relationClientPk.ExtractValues(relation, ref alteredValues);
                        }
                    }
                }

                if (table.TableName == MediaLibrary.DbTableName)
                {
                    MediaLibraryHack_FixKey(table, memberAccessor, ref alteredValues);
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }

            return alteredValues;
        }
        
        public void RegisterKeys(params ClientKeysMap[] keys)
        {
            if (keys == null) throw new ArgumentNullException(nameof(keys));
            if (_metaModel == null) throw new InvalidOperationException($"{nameof(ClientKeysMapper)} has not yet loaded");

            foreach (ClientKeysMap ckm in keys)
            {
                string tableName = MessageUtils.ServerTableNameToClientTableName(ckm.TableName);
                IMetaTable table = _metaModel.GetTable(tableName);
                if (table != null)
                {
                    if (table.TableName == MediaLibrary.DbTableName)
                    {
                        MediaLibraryHack_RegisterLibraryId(ckm.ServerKeys, ckm.ClientKeys);
                    }
                    else
                    {
                        ObjPrimaryKey serverKey = ObjPrimaryKey.FromKeySeparatedValues(table, ckm.ServerKeys);
                        ObjPrimaryKey clientKey = ObjPrimaryKey.FromKeySeparatedValues(table, ckm.ClientKeys);

                        if (serverKey != null && clientKey != null)
                        {
                            _serverToClientKeyMap[serverKey] = clientKey;
                        }
                    }
                }
            }
        }
        
        public void UnregisterKeys(params ClientKeysMap[] keys)
        {
            if (keys == null) throw new ArgumentNullException(nameof(keys));
            if (_metaModel == null) throw new InvalidOperationException($"{nameof(ClientKeysMapper)} has not yet loaded");

            foreach (ClientKeysMap ckm in keys)
            {
                string tableName = MessageUtils.ServerTableNameToClientTableName(ckm.TableName);
                IMetaTable table = _metaModel.GetTable(tableName);
                if (table != null)
                {
                    ObjPrimaryKey serverKey = ObjPrimaryKey.FromKeySeparatedValues(table, ckm.ServerKeys);

                    if (serverKey != null)
                    {
                        _serverToClientKeyMap.Remove(serverKey);
                    }
                }
            }
        }

        private void MediaLibraryHack_FixKey(IMetaTable table, IMemberAccessor memberAccessor, ref IDictionary<IMetaDataMember, object> alteredValues)
        {
            try
            {
                // We have a weird issue with keys on MediaLibrary. 
                // - A MediaLibrary is inserted with keys '-1 and COM50'
                // - A MediaLibraryItem is attached to it
                // - Server tells us the MediaLibrary has library id '2896'
                // - Server sends a new MediaLibrary with keys '2896 and COM50^CUST161^' to 
                //   attach the same media item to a different record
                // - Since the key does not match '-1 and COM50' the key is not updated

                if (_serverToClientMediaLibId.Count == 0)
                {
                    return;
                }

                if (_mediaLibraryId == null)
                {
                    _mediaLibraryId = table.FindMemberByPropertyName(nameof(MediaLibrary.LibraryId));
                }

                if (memberAccessor.TryGetValue(_mediaLibraryId, out object value) && value != null)
                {
                    string serverLibraryId = (string)_mediaLibraryId.ConvertValue(value);
                    if (_serverToClientMediaLibId.TryGetValue(serverLibraryId, out string clientlibraryId))
                    {
                        if (alteredValues == null)
                        {
                            alteredValues = new Dictionary<IMetaDataMember, object>();
                        }

                        alteredValues[_mediaLibraryId] = clientlibraryId;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private void MediaLibraryHack_RegisterLibraryId(string serverKey, string clientKey)
        {
            try
            {
                _serverToClientMediaLibId[serverKey] = clientKey;
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }
    }
}
