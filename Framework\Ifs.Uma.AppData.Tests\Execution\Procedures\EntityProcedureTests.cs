﻿using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class EntityProcedureTests : ProcedureTest
    {
        private const string TstCustomerEntityName = "TstCustomer";
        private const string TstCustomerServerGenEntityName = "TstCustomerServerGen";
        private const string TstCustomerWithProcsEntityName = "TstCustomerWithProcs";
        private const string CustomerNoAttributeName = "CustomerNo";
        private const string CustomerNameAttributeName = "CustomerName";
        private static readonly string TstCustomerTableName = RemoteNaming.ToTableName(TstCustomerEntityName);
        private static readonly string TstCustomerServerGenTableName = RemoteNaming.ToTableName(TstCustomerServerGenEntityName);
        private static readonly string TstCustomerWithProcsTableName = RemoteNaming.ToTableName(TstCustomerWithProcsEntityName);
        private static readonly string CustomerNoColumnName = RemoteNaming.ToColumnName(CustomerNoAttributeName);

        [Test]
        public async Task Create()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.EntityPrepareAsync(TestOfflineProjection, TstCustomerEntityName);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow row = result.Value as RemoteRow;
            Assert.IsNotNull(row);
            Assert.AreEqual(0, row.RowId);
            Assert.AreEqual(TstCustomerTableName, row.TableName);
        }

        [Test]
        public async Task CreateAndPrepare()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.EntityPrepareAsync(TestOfflineProjection, TstCustomerWithProcsEntityName);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow row = result.Value as RemoteRow;
            Assert.IsNotNull(row);
            Assert.AreEqual(0, row.RowId);
            Assert.AreEqual(TstCustomerWithProcsTableName, row.TableName);
            Assert.AreEqual("TestPrepareValue", row[CustomerNameAttributeName]);
        }

        [Test]
        public async Task CreateServerGen()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.EntityPrepareAsync(TestOfflineProjection, TstCustomerServerGenEntityName);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow row = result.Value as RemoteRow;
            Assert.IsNotNull(row);
            Assert.AreEqual(0, row.RowId);
            Assert.AreEqual(TstCustomerServerGenTableName, row.TableName);
            Assert.AreEqual(null, row[CustomerNoAttributeName]);
        }

        [Test]
        public async Task Insert()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow row = new RemoteRow(TstCustomerTableName);
            row[CustomerNoAttributeName] = "500";

            ExecuteResult result = await executor.EntityInsertAsync(TestOfflineProjection, row);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow returnedRow = result.Value as RemoteRow;
            Assert.IsNotNull(returnedRow);
            Assert.AreNotEqual(0, returnedRow.RowId);
            Assert.AreEqual(TstCustomerTableName, returnedRow.TableName);
            Assert.AreEqual("500", returnedRow[CustomerNoAttributeName]);
            
            RemoteRow insertedRow = ReloadRow(returnedRow);
            Assert.IsNotNull(insertedRow);
            Assert.AreEqual("500", insertedRow[CustomerNoAttributeName]);
        }

        [Test]
        public async Task InsertWithProcedure()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow row = new RemoteRow(TstCustomerWithProcsTableName);
            row[CustomerNoAttributeName] = "500";

            ExecuteResult result = await executor.EntityInsertAsync(TestOfflineProjection, row);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow returnedRow = result.Value as RemoteRow;
            Assert.IsNotNull(returnedRow);
            Assert.AreNotEqual(0, returnedRow.RowId);
            Assert.AreEqual(TstCustomerWithProcsTableName, returnedRow.TableName);
            Assert.AreEqual("500", returnedRow[CustomerNoAttributeName]);
            Assert.AreEqual("TestInsertValue", returnedRow[CustomerNameAttributeName]);

            RemoteRow insertedRow = ReloadRow(returnedRow);
            Assert.IsNotNull(insertedRow);
            Assert.AreEqual("500", insertedRow[CustomerNoAttributeName]);
            Assert.AreEqual("TestInsertValue", insertedRow[CustomerNameAttributeName]);
        }

        [Test]
        public async Task InsertServerGenKey()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            // Insert first row and make sure primary key is created
            {
                RemoteRow row = new RemoteRow(TstCustomerServerGenTableName);

                ExecuteResult result = await executor.EntityInsertAsync(TestOfflineProjection, row);

                Assert.IsNotNull(result);
                Assert.IsFalse(result.Failed);

                RemoteRow returnedRow = result.Value as RemoteRow;
                Assert.IsNotNull(returnedRow);
                Assert.AreEqual("-1", returnedRow[CustomerNoAttributeName]);

                RemoteRow insertedRow = ReloadRow(returnedRow);
                Assert.IsNotNull(insertedRow);
                Assert.AreEqual("-1", insertedRow[CustomerNoAttributeName]);
            }

            // Insert second row and expect a different primary key
            {
                RemoteRow row2 = new RemoteRow(TstCustomerServerGenTableName);

                ExecuteResult result2 = await executor.EntityInsertAsync(TestOfflineProjection, row2);

                Assert.IsNotNull(result2);
                Assert.IsFalse(result2.Failed);

                RemoteRow returnedRow2 = result2.Value as RemoteRow;
                Assert.IsNotNull(returnedRow2);
                Assert.AreEqual("-2", returnedRow2[CustomerNoAttributeName]);

                RemoteRow insertedRow2 = ReloadRow(returnedRow2);
                Assert.IsNotNull(insertedRow2);
                Assert.AreEqual("-2", insertedRow2[CustomerNoAttributeName]);
            }

            // Verify client keys tables
            {
                IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
                FwDataContext ctx = dataContextProvider.CreateDataContext();
                ClientGeneratedKey genKey = ctx.ClientGeneratedKeys
                    .FirstOrDefault(x => x.TableName == TstCustomerServerGenTableName && x.ColumnName == CustomerNoColumnName);

                Assert.IsNotNull(genKey);
                Assert.AreEqual(-2L, genKey.ClientId);
            }
        }

        [Test]
        public async Task Update()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow row = new RemoteRow(TstCustomerTableName);
            row[CustomerNoAttributeName] = "500";
            row[CustomerNameAttributeName] = "test1";

            ExecuteResult result = await executor.EntityInsertAsync(TestOfflineProjection, row);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow insertedRow = result.Value as RemoteRow;
            Assert.IsNotNull(insertedRow);
            insertedRow[CustomerNameAttributeName] = "test2";

            result = await executor.EntityUpdateAsync(TestOfflineProjection, insertedRow, new[] { CustomerNameAttributeName });

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow returnedRow = result.Value as RemoteRow;
            Assert.IsNotNull(returnedRow);
            Assert.AreEqual(insertedRow.RowId, returnedRow.RowId);
            Assert.AreEqual(TstCustomerTableName, returnedRow.TableName);
            Assert.AreEqual("500", returnedRow[CustomerNoAttributeName]);
            Assert.AreEqual("test2", returnedRow[CustomerNameAttributeName]);

            RemoteRow updatedRow = ReloadRow(returnedRow);
            Assert.IsNotNull(updatedRow);
            Assert.AreEqual("test2", updatedRow[CustomerNameAttributeName]);
        }

        [Test]
        public async Task UpdateWithProcedure()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow row = new RemoteRow(TstCustomerWithProcsTableName);
            row[CustomerNoAttributeName] = "500";
            row[CustomerNameAttributeName] = "test1";

            ExecuteResult result = await executor.EntityInsertAsync(TestOfflineProjection, row);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow insertedRow = result.Value as RemoteRow;
            Assert.IsNotNull(insertedRow);
            Assert.AreEqual("TestInsertValue", insertedRow[CustomerNameAttributeName]);
            insertedRow[CustomerNameAttributeName] = "test2";

            result = await executor.EntityUpdateAsync(TestOfflineProjection, insertedRow, new[] { CustomerNameAttributeName });

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow returnedRow = result.Value as RemoteRow;
            Assert.IsNotNull(returnedRow);
            Assert.AreEqual(insertedRow.RowId, returnedRow.RowId);
            Assert.AreEqual(TstCustomerWithProcsTableName, returnedRow.TableName);
            Assert.AreEqual("500", returnedRow[CustomerNoAttributeName]);
            Assert.AreEqual("TestUpdateValue", returnedRow[CustomerNameAttributeName]);

            RemoteRow updatedRow = ReloadRow(returnedRow);
            Assert.IsNotNull(updatedRow);
            Assert.AreEqual("TestUpdateValue", updatedRow[CustomerNameAttributeName]);
        }

        [Test]
        public async Task Delete()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow row = new RemoteRow(TstCustomerTableName);
            row[CustomerNoAttributeName] = "500";

            ExecuteResult result = await executor.EntityInsertAsync(TestOfflineProjection, row);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow insertedRow = result.Value as RemoteRow;

            result = await executor.EntityDeleteAsync(TestOfflineProjection, insertedRow);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow updatedRow = ReloadRow(insertedRow);
            Assert.IsNull(updatedRow);
        }

        [Test]
        public async Task DeleteWithProcedure()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow row = new RemoteRow(TstCustomerWithProcsTableName);
            row[CustomerNoAttributeName] = "500";

            ExecuteResult result = await executor.EntityInsertAsync(TestOfflineProjection, row);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow insertedRow = result.Value as RemoteRow;

            result = await executor.EntityDeleteAsync(TestOfflineProjection, insertedRow);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow updatedRow = ReloadRow(insertedRow);
            Assert.IsNull(updatedRow);
        }

        [Test]
        public async Task EventSynchronizationEnded()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            
            ExecuteResult result = await executor.CallEventAsync(TestOfflineProjection, "SynchronizationEnded", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("SynchronizationEndedEventValue", result.Value); // Note that the actual SynchronizationEnded event does not return a value
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.EntityProceduresSchema", null);
        }
    }
}
