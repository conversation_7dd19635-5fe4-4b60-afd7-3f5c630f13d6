﻿using System;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Localization;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Services
{
    internal class SyncFailingWatcher : TransactionSyncEventWatcher
    {
        private readonly IDialogService _dialogService;
        private readonly INavigator _navigator;
        private readonly IIfsConnection _connection;
        private readonly ITransactionSyncDataHandler _transactionSyncData;

        private bool _isSyncFailing;

        public SyncFailingWatcher(ILogger logger, 
            ITransactionSyncService service,
            IDialogService dialogService,
            ITransactionSyncDataHandler transactionSyncData,
            INavigator navigator,
            IIfsConnection connection) 
            : base(logger, service, ThreadOption.UIThread)
        {
            _dialogService = dialogService;
            _navigator = navigator;
            _connection = connection;
            _transactionSyncData = transactionSyncData;
        }

        protected override bool OnMatchSyncEventFilter(TransactionSyncEventArgs e)
        {
            return e.EventType == SyncEventType.SyncEnded;
        }

        protected override void OnStop()
        {
            base.OnStop();

            _isSyncFailing = false;
        }

        protected override void OnSyncEvent(TransactionSyncEventArgs e)
        {
            bool isFailing = e.ServiceStatus.IsFailing;
            if (isFailing)
            {
                if (!_isSyncFailing)
                {
                    _isSyncFailing = true;
                    if (!Service.IsInitializing && !_connection.TouchAppsComms.IsBroken)
                    {
                        OnSyncBeginsFailing(e.ServiceStatus?.LastFailMessage);
                    }
                }
            }
            else
            {
                _isSyncFailing = false;
            }
        }

        private async void OnSyncBeginsFailing(string message)
        {
            try
            {
                await _dialogService.WaitForDialogsToClose(ServiceCancelToken);

                if (IsRunning && _isSyncFailing)
                {
                    TransitionRow transitionRow = await _transactionSyncData.GetFirstFailedTransaction();
                    if (transitionRow != null)
                    {
                        if (transitionRow.ErrorCode != null && !string.Equals(transitionRow.ErrorCode, "ApplicationErrorIgnore"))
                        {
                            CustomButtonsResult result = await _dialogService.CustomButtons(string.Format("{0}: {1} {2}", Strings.Error, transitionRow.TableName, transitionRow.SyncState), transitionRow.ErrorMessage, Strings.Details, Strings.Dismiss);

                            if (result == CustomButtonsResult.Positive)
                            {
                                SyncMonitorNavParams navParams = new SyncMonitorNavParams(SyncPageTab.Transactions);
                                await _navigator.NavigateToAsync(FrameworkLocations.SyncMonitor, navParams);
                            }
                        }
                    }
                    else if (!string.IsNullOrEmpty(message))
                    {
                        bool result = await _dialogService.Confirm(
                        Strings.SyncProcessFailing, message,
                        Strings.Details, ConfirmationType.Normal);

                        if (result)
                        {
                            await _navigator.NavigateToAsync(FrameworkLocations.SyncMonitor, null);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogException(ex);
            }
        }
    }
}
