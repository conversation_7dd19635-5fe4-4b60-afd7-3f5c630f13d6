﻿using System;
using System.Globalization;
using Ifs.Uma.Utility;
using SQLitePCL;

namespace Ifs.Uma.Database.SQLite.CustomFunctions
{
    internal sealed class DateTimeFormat : SQLiteCustomFunction
    {
        public const string FunctionName = "ifs_datetime_format";

        public DateTimeFormat()
            : base(FunctionName, true, 2)
        {
        }

        protected override void OnExecute(sqlite3_context ctx, sqlite3_value[] args)
        {
            if (raw.sqlite3_value_type(args[0]) == raw.SQLITE_NULL)
            {
                raw.sqlite3_result_null(ctx);
                return;
            }

            long dateTicks = raw.sqlite3_value_int64(args[0]);
            DateTime dateTime = new DateTime(dateTicks, DateTimeKind.Unspecified);
            string format = raw.sqlite3_value_text(args[1]);

            string result;
            if (string.IsNullOrEmpty(format))
            {
                result = ObjectConverter.ToString(dateTime);
            }
            else
            {
                result = dateTime.ToString(format, CultureInfo.CurrentCulture);
            }

            raw.sqlite3_result_text(ctx, result);
        }
    }
}
