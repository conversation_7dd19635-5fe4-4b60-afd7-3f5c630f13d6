﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteSuperTests : ProcedureTest
    {
        [Test]
        public async Task Super()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Param1"] = "-Woo-";

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "CallSuper", parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("Layer2Layer1-Woo-", result.Value);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteSuperSchema", null);
        }
    }
}
