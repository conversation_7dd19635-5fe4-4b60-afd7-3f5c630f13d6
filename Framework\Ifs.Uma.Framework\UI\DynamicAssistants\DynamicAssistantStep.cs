﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Forms;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.DynamicAssistants
{
    public class DynamicAssistantStep : ObservableBase
    {
        public CommandBlock Commands { get; set; }
        public ElementList Elements { get; set; }

        public FndDynamicAssistStep DynamicStep { get; }

        private bool _isReadOnly;
        public bool IsReadOnly
        {
            get => _isReadOnly;
            set
            {
                if (SetProperty(ref _isReadOnly, value))
                {
                    if (Field != null)
                    {
                        Field.IsReadOnly = value;
                    }

                    if (RemarkField != null)
                    {
                        RemarkField.IsReadOnly = value;
                    }
                }
            }
        }

        public Field Field { get; set; }
        public Field RemarkField { get; set; }
        public RecordData Record { get; set; }
        public double? LoopOccurrence { get; set; }

        private bool _isCurrentStep;
        public bool IsCurrentStep
        {
            get { return _isCurrentStep; }
            set
            {
                SetProperty(ref _isCurrentStep, value);
                ShowNext = _isCurrentStep && !IsCompleteStep;
            }
        }

        private bool _isNextStepLoading;
        public bool IsNextStepLoading
        {
            get { return _isNextStepLoading; }
            set { SetProperty(ref _isNextStepLoading, value); }
        }

        private bool _shouldNextCommandBeEnabled;
        public bool ShouldNextCommandBeEnabled
        {
            get { return _shouldNextCommandBeEnabled; }
            set { SetProperty(ref _shouldNextCommandBeEnabled, value); }
        }

        private bool _isLastStep;
        public bool IsLastStep
        {
            get { return _isLastStep; }
            set { SetProperty(ref _isLastStep, value); }
        }

        private bool _isCompleteStep;
        public bool IsCompleteStep
        {
            get { return _isCompleteStep; }
            set
            {
                SetProperty(ref _isCompleteStep, value);
                ShowNext = IsCurrentStep && !_isCompleteStep;
            }
        }

        private bool _showNext;
        public bool ShowNext
        {
            get { return _showNext; }
            set { SetProperty(ref _showNext, value); }
        }

        private readonly string _projectionName;

        private readonly IMetadata _metadata;
        private readonly IFileService _fileService;
        private readonly ILovService _lovService;
        private readonly IAppParameters _appParameters;
        private readonly IExpressionRunner _expressionRunner;

        public DynamicAssistantStep(string projectionName, FndDynamicAssistStep step, IMetadata metadata, IExpressionRunner expressionRunner, IFileService fileService, ILovService lovService, IAppParameters appParameters)
        {
            _projectionName = projectionName;
            DynamicStep = step;
            _metadata = metadata;
            _fileService = fileService;
            _lovService = lovService;
            _appParameters = appParameters;
            _expressionRunner = expressionRunner;

            PrepareStep();
        }

        private void PrepareStep()
        {
            if (string.IsNullOrEmpty(DynamicStep.ProjectionName))
            {
                DynamicStep.ProjectionName = _projectionName;
            }

            if (!DynamicStep.Visible.HasValue)
            {
                DynamicStep.Visible = true;
            }

            if (!DynamicStep.Enabled.HasValue)
            {
                DynamicStep.Enabled = true;
            }

            if (string.IsNullOrEmpty(DynamicStep.StepLabel))
            {
                DynamicStep.StepLabel = DynamicStep.Label;
            }

            if (!string.IsNullOrEmpty(DynamicStep.Description) && DynamicStep.Description.IsLabelTranslatable())
            {
                DynamicStep.Description = DynamicStep.Description.Substring(DynamicStep.Description.IndexOf(":") + 1);
            }

            if (string.IsNullOrEmpty(DynamicStep.BindAttributeLabel))
            {
                DynamicStep.BindAttributeLabel = DynamicStep.BindAttribute;
            }

            CpiField fieldForStep = CreateCpiField(DynamicStep);

            if (fieldForStep != null)
            {
                Field = _metadata.CreateFieldType(DynamicStep.ProjectionName, fieldForStep, 0);
            }

            if (DynamicStep.Datatype == "Multichoice")
            {
                ComboMultiSelectField multiSelectField = new ComboMultiSelectField();

                multiSelectField.MaxItems = DynamicStep.DynamicLovMaxSelectableOptions ?? 0;
                multiSelectField.AutoCloseOnMaxItems = true;

                if (DynamicStep.DynamicLovOptions != null)
                {
                    Dictionary<string, string> items = DynamicStep.DynamicLovOptions.SplitToDictionary();
                    SelectableItem<object>[] enumerationValues = items.Select(x => new SelectableItem<object>(
                                string.IsNullOrEmpty(x.Value.ToString()) ? string.Format(CultureInfo.InvariantCulture, "[{0}]", x.Key) : x.Value.ToString(),
                                x.Key))
                            .ToArray();

                    multiSelectField.ItemsSource = enumerationValues;
                }

                Field = multiSelectField;
            }

            if (DynamicStep.Reference != null)
            {
                CpiReference reference = _metadata.FindReference(DynamicStep.ProjectionName, DynamicStep.Entity, DynamicStep.Reference);

                if (reference == null) throw new InvalidOperationException($"Could not find LOV reference {DynamicStep.Reference} for entity {DynamicStep.Entity}");

                LovField lovField = new LovField()
                {
                    RefName = DynamicStep.Reference,
                    Description = DynamicStep.Label,
                    ExpressionRunner = _expressionRunner
                };
                lovField.Command = Command.FromAsyncMethod(() => OpenLookup(lovField, reference));
                Field = lovField;
            }

            if (Field != null)
            {
                if (Field is MediaPickerField imagePickerField)
                {
                    imagePickerField.FileService = _fileService;
                    imagePickerField.ImageOptions = new ImageOptions(_appParameters.GetPictureMaxDimension(),
                                                                     _appParameters.GetPictureMaxBytes(), _appParameters.IsExistingDeviceMediaAllowed());
                    imagePickerField.ValueType = FilePickerValueType.ByteArray;

                    if (_appParameters.IsEnhancedMediaEnabled())
                    {
                        imagePickerField.MaxVideoBytes = _appParameters.GetVideoMaxMb() != 0 ? Utils.ConvertMbToBytes(_appParameters.GetVideoMaxMb()) : Utils.ConvertMbToBytes(MediaHandler.DefaultVideoMaxMb);
                    }
                }

                if (Field is ComboField comboField)
                {
                    if (DynamicStep.Enumeration != null)
                    {
                        CpiEnumeration enumeration = _metadata.FindEnumeration(DynamicStep.ProjectionName, DynamicStep.Enumeration);
                        if (enumeration != null)
                        {
                            SelectableItem<object>[] enumerationValues = enumeration.Labels.Select(x => new SelectableItem<object>(
                                string.IsNullOrEmpty(x.Label) ? string.Format(CultureInfo.InvariantCulture, "[{0}]", x.Value) : x.Label,
                                x.Value))
                            .ToArray();

                            comboField.ItemsSource = enumerationValues;

                            if (enumerationValues.Length <= 4)
                            {
                                RadioGroupField radioGroup = new RadioGroupField();
                                radioGroup.ItemsSource = enumerationValues;
                                Field = radioGroup;
                            }
                        }
                    }
                }

                if (Field is TextField textField)
                {
                    textField.MaskFormat = DynamicStep.InputMask;
                    textField.AccentedCharsAllowed = _appParameters.IsAccentedCharsAllowed();
                }

                Field.Name = DynamicStep.Name ?? DynamicStep.BindAttribute;
                Field.Label = DynamicStep.Label;
                Field.IsRequired = DynamicStep.Required.GetValueOrDefault();
                Field.IsVisible = DynamicStep.Visible.GetValueOrDefault(true);
                Field.Editability = DynamicStep.Editable.GetValueOrDefault(true) ? FieldEditability.InsertingOrUpdating : FieldEditability.Never;
            }

            if (DynamicStep.RemarkNeeded.GetValueOrDefault() == true && !string.IsNullOrEmpty(DynamicStep.RemarkAttribute))
            {
                if (string.IsNullOrEmpty(DynamicStep.RemarkAttributeLabel))
                {
                    DynamicStep.RemarkAttributeLabel = DynamicStep.RemarkAttribute;
                }

                RemarkField = _metadata.CreateFieldType(DynamicStep.ProjectionName, CreateRemarkField(DynamicStep), 0);

                if (RemarkField is TextField textField && DynamicStep.InputMask != null)
                {
                    textField.MaskFormat = DynamicStep.InputMask;
                }
            }
        }

        private async Task OpenLookup(LovField field, CpiReference reference)
        {
            bool selected = await _lovService.OpenLovAsync(field, reference, field.Data);

            if (selected)
            {
                field.NotifyValidationRequired();
            }
        }

        private CpiField CreateCpiField(FndDynamicAssistStep step)
        {
            if (step.BindAttribute == null) return null;

            CpiAttribute attribute = _metadata.FindAttribute(step.ProjectionName, step.Entity, step.BindAttribute);

            CpiControlType controlType;
            if (!Enum.TryParse(step.ControlType, true, out controlType))
            {
                controlType = CpiControlType.Field;
            }

            CpiField field = new CpiField
            {
                Array = false,
                Attribute = step.BindAttribute,
                Label = step.BindAttributeLabel ?? step.BindAttribute ?? step.Label ?? string.Empty,
                Datatype = MetadataExtensions.GetDynamicDataType(step.Datatype),
                Entity = step.Entity,
                Control = controlType,
                Multiline = step.MultiLine.GetValueOrDefault(false),
                Enumeration = step.Enumeration,
                Maxlength = attribute?.Size,
                Scale = attribute?.Scale,
                Precision = attribute?.Precision,
                Format = attribute?.Format
            };

            return field;
        }

        private CpiField CreateRemarkField(FndDynamicAssistStep step)
        {
            if (step.RemarkAttribute == null) return null;

            CpiAttribute attribute = _metadata.FindAttribute(step.ProjectionName, step.Entity, step.RemarkAttribute);

            CpiField field = new CpiField
            {
                Attribute = step.RemarkAttribute,
                Label = step.RemarkAttributeLabel ?? step.RemarkAttribute ?? string.Empty,
                Entity = step.Entity,
                Multiline = (step.InputMask == null),
                Datatype = CpiDataType.Text,
                Maxlength = attribute?.Size
            };
            
            return field;
        }
    }
}
