<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <include layout="@layout/app_info_header" />
        <TextView
            android:id="@+id/label_designed_by"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="8dp"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:textSize="@dimen/abc_text_size_medium_material"
            android:textColor="@color/IfsBlack" 
            android:textDirection="locale" />
        <TextView
            android:id="@+id/label_copyright"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/activity_vertical_margin"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:textSize="@dimen/abc_text_size_medium_material"
            android:textColor="@color/IfsBlack" 
            android:textDirection="locale"/>
        <TextView
            android:id="@+id/label_all_rights"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/activity_vertical_margin"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:textSize="@dimen/abc_text_size_medium_material"
            android:textColor="@color/IfsBlack"
            android:textDirection="locale"/>
        <TextView
            android:id="@+id/label_ifs_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/activity_vertical_margin"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:textSize="@dimen/abc_text_size_medium_material"
            android:textColor="@color/IfsBlack"
            android:textDirection="locale"/>
        <TextView
            android:id="@+id/label_about"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:paddingEnd="@dimen/activity_horizontal_margin"
            android:textSize="@dimen/abc_text_size_medium_material"
            android:textColor="@color/IfsGrayDark" />
        <TextView
            android:id="@+id/label_privacy_policy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:textSize="@dimen/abc_text_size_medium_material"
            android:textColor="@color/ifspurpletextselector"
            android:clickable="true"
            android:focusable="true"/>
        <Button
            android:id="@+id/button_show_licenses"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:fontFamily="normal"
            android:textAllCaps="false"
            android:textSize="@dimen/abc_text_size_medium_material"
            android:textColor="@color/ifspurpletextselector"
            style="@style/Widget.AppCompat.Button.Borderless"/>
    </LinearLayout>  
</ScrollView>
