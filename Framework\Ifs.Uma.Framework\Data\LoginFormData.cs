﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Framework.MSTeams;
using Ifs.Uma.Framework.Services;
using Ifs.Uma.Localization;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.Framework.Data
{
    public class LoginFormData : FormData
    {
        private const string SettingsGroup = "Login";
        private const string ServiceUrlSettingName = "ServiceUrl";
        private const string DevScaffoldUrlSettingName = "DevScaffoldUrl";
        private const string DevScaffoldSettingName = "DevScaffold";
        private const string SystemIdSettingName = "SystemId";
        private const string AppNameSettingName = "AppName";
        private const string SwitchUserFlag = "SwitchUser";

        private static bool _firstOpen = true;

        private readonly ITouchApp _app;
        private readonly ILogger _logger;
        private readonly ISettings _settings;
        private readonly ISettings _appSettings;
        private readonly IDialogService _dialogService;

        #region Properties

        public bool DeveloperMode
        {
            get
            {
                return _app.DeveloperMode;
            }
        }

        private bool _devScaffold;

        public bool DevScaffold
        {
            get => _devScaffold;
            set => SetProperty(ref _devScaffold, value);
        }

        private string _devScaffoldUrl;
        public string DevScaffoldUrl
        {
            get => _devScaffoldUrl;
            set => SetProperty(ref _devScaffoldUrl, value);
        }

        private string _serviceUrl;
        public string ServiceUrl
        {
            get => _serviceUrl;
            set => SetProperty(ref _serviceUrl, value);
        }

        private string _systemId;
        public string SystemId
        {
            get => _systemId;
            set => SetProperty(ref _systemId, value);
        }

        private string _appName;
        public string AppName
        {
            get => _appName;
            set => SetProperty(ref _appName, value);
        }

        private string _username;
        public string Username
        {
            get => _username;
            set => SetProperty(ref _username, value);
        }

        private string _pinCode;
        public string PinCode
        {
            get => _pinCode;
            set => SetProperty(ref _pinCode, value);
        }

        private bool _canInteract = true;
        public bool CanInteract
        {
            get => _canInteract;
            private set
            {
                if (SetProperty(ref _canInteract, value))
                {
                    UpdateFieldStates();
                }
            }
        }

        public ButtonData Proceed { get; }
        public ButtonData Disconnect { get; }

        #endregion

        private Field _devScaffoldField;
        private Field _loginDetailsHeaderField;
        private TextField _systemIdField;
        private TextField _appNameField;
        private TextField _usernameField;
        private TextField _pinCodeField;
        private TextField _serviceUrlField;
        private TextField _devScaffoldUrlField;
        private TextField _pinCodeMessageField;
        private CommandField _tryMeModeField;
        private TouchAppAccount _account;
        private ISettings _pinCodeSettings;

        public LoginFormData(ITouchApp app, ILogger logger, ISettings appSettings, IDialogService dialogService)
        {
            _app = app ?? throw new ArgumentNullException(nameof(app));
            _logger = logger;
            if (appSettings == null)
                throw new ArgumentNullException(nameof(appSettings));
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));

            Proceed = new ButtonData(DoProceed);
            Disconnect = new ButtonData(DoDisconnect);
            Disconnect.Text = Strings.Disconnect;

            _settings = appSettings.GetSubGroup(SettingsGroup);
            _appSettings = appSettings;

            SystemId = _settings.Get(SystemIdSettingName);
            AppName = app.Registration.AppName;
            if (AppName == null)
            {
                AppName = _settings.Get(AppNameSettingName);
            }
            ServiceUrl = _settings.Get(ServiceUrlSettingName);
            DevScaffoldUrl = _settings.Get(DevScaffoldUrlSettingName);
            DevScaffold = _settings.GetBoolean(DevScaffoldSettingName, false);

            SetupForm();

            _account = _app.Accounts.FirstOrDefault();

            if (_account != null)
            {
                if (string.IsNullOrEmpty(Username))
                {
                    Username = _account.UserDisplayName;
                }

                DevScaffold = _account.DevScaffold;
            }

            _ = TrySwitchUser();

            UpdateFieldStates();

            _ = TryAutoLogin(_account);
        }

        protected override Form OnSetupForm()
        {
            Form form = new Form();
            form.EditState = FieldEditState.Insert;

            _devScaffoldField = new BoolField();
            _devScaffoldField.SetBinding(this, () => DevScaffold);
            _devScaffoldField.Name = Strings.ConnectToDevScaffold;
            _devScaffoldField.Id = "Connect to Dev Server";
            _devScaffoldField.IsRequired = true;
            _devScaffoldField.IsVisible = false;
            _devScaffoldField.ValueChanged += DevScaffold_ValueChanged;
            form.AllFields.Add(_devScaffoldField);

            _loginDetailsHeaderField = new HeaderField();
            _loginDetailsHeaderField.Name = Strings.LoginDetails;
            _loginDetailsHeaderField.Value = Strings.LoginDetails;
            _loginDetailsHeaderField.Id = "Login Details";
            _loginDetailsHeaderField.SizeHint = SizeHint.FullWidth;
            form.AllFields.Add(_loginDetailsHeaderField);

            _devScaffoldUrlField = new TextField();
            _devScaffoldUrlField.Name = Strings.DevScaffoldUrl;
            _devScaffoldUrlField.Id = "Dev Server Url";
            _devScaffoldUrlField.SetBinding(this, () => DevScaffoldUrl);
            _devScaffoldUrlField.ContentType = Uma.UI.Model.ContentType.Url;
            _devScaffoldUrlField.AllowContentAction = false;
            _devScaffoldUrlField.IsRequired = true;
            _devScaffoldUrlField.BarcodeEnabled = false;
            form.AllFields.Add(_devScaffoldUrlField);

            _serviceUrlField = new TextField();
            _serviceUrlField.Name = Strings.ServerURL;
            _serviceUrlField.Id = "Server URL";
            _serviceUrlField.SetBinding(this, () => ServiceUrl);
            _serviceUrlField.ContentType = Uma.UI.Model.ContentType.Url;
            _serviceUrlField.AllowContentAction = false;
            _serviceUrlField.IsRequired = true;
            _serviceUrlField.BarcodeEnabled = false;
            form.AllFields.Add(_serviceUrlField);

            _systemIdField = new TextField();
            _systemIdField.Name = Strings.SystemID;
            _systemIdField.Id = "System ID";
            _systemIdField.SetBinding(this, () => SystemId);
            _systemIdField.IsRequired = true;
            _systemIdField.BarcodeEnabled = false;
            form.AllFields.Add(_systemIdField);

            _appNameField = new TextField();
            _appNameField.Name = Strings.AppName;
            _appNameField.Id = "App Name";
            _appNameField.IsRequired = true;
            _appNameField.SetBinding(this, () => AppName);
            _appNameField.BarcodeEnabled = false;
            form.AllFields.Add(_appNameField);

            _usernameField = new TextField();
            _usernameField.Name = Strings.Username;
            _usernameField.Id = "Username";
            _usernameField.IsReadOnly = true;
            _usernameField.SetBinding(this, () => Username);
            _usernameField.BarcodeEnabled = false;
            form.AllFields.Add(_usernameField);

            _pinCodeField = new TextField();
            _pinCodeField.Name = Strings.PinCode;
            _pinCodeField.Id = "Pin Code";
            _pinCodeField.ContentType = Uma.UI.Model.ContentType.Password;
            _pinCodeField.SetBinding(this, () => PinCode);
            _pinCodeField.IsRequired = true;
            _pinCodeField.BarcodeEnabled = false;
            _pinCodeField.IsVisible = false;
            _pinCodeField.ValueChanged += PinCodeField_ValueChanged;
            form.AllFields.Add(_pinCodeField);

            _pinCodeMessageField = new TextField();
            _pinCodeMessageField.Name = string.Empty;
            _pinCodeMessageField.IsReadOnly = true;
            _pinCodeMessageField.IsVisible = false;
            _pinCodeMessageField.ShowLabel = false;
            form.AllFields.Add(_pinCodeMessageField);

            if (Ifs.Uma.Utility.DeviceInfo.OperatingSystem == OperatingSystem.iOS)
            {
                // Add a blank header field for iOS to improve spacing between the try me
                // button and the rest of the form
                HeaderField spacer = new HeaderField();
                spacer.Name = string.Empty;
                spacer.Value = string.Empty;
                spacer.IsVisible = _app.TryMeModeAvailable;
                form.AllFields.Add(spacer);
            }

            _tryMeModeField = new CommandField();
            _tryMeModeField.Value = Strings.TryMe;
            _tryMeModeField.Id = "Try Me";
            _tryMeModeField.Icon = IconUtils.CaretForward;
            _tryMeModeField.Command = Command.FromAsyncMethod(TryMe);
            _tryMeModeField.IsVisible = _app.TryMeModeAvailable;
            _tryMeModeField.SizeHint = SizeHint.FullWidth;
            form.AllFields.Add(_tryMeModeField);

            foreach (string layoutVariant in new[] { Form.LayoutVariants.Compact, Form.LayoutVariants.Regular })
            {
                FormLayoutData layout = form.GetLayout(layoutVariant);
                layout.SetDefaultLayout(form.AllFields.Select(x => new[] { x.Id }).ToArray());
            }

            return form;
        }

        private void PinCodeField_ValueChanged(object sender, Uma.UI.Observables.ValueChangedEventArgs e)
        {
            UpdateFieldStates();
        }

        private void DevScaffold_ValueChanged(object sender, Uma.UI.Observables.ValueChangedEventArgs e)
        {
            UpdateFieldStates();
        }

        private void UpdateFieldStates()
        {
            bool devMode = _app.DeveloperMode;

            bool activated = _account?.IsActivated ?? false;
            bool isConnected = _account != null;

            _devScaffoldField.IsVisible = devMode || DevScaffold;
            _devScaffoldField.IsReadOnly = activated || isConnected;

            _devScaffoldUrlField.IsVisible = DevScaffold;
            _devScaffoldUrlField.IsReadOnly = activated;

            _serviceUrlField.IsVisible = !DevScaffold;
            _serviceUrlField.IsReadOnly = activated || isConnected;

            //TO-DO: System ID needs to be removed in the final code
            _systemIdField.IsVisible = false;
            //_systemIdField.IsReadOnly = activated || isConnected;

            _appNameField.IsVisible = devMode;
            _appNameField.IsReadOnly = activated || isConnected;

            _usernameField.IsVisible = DevScaffold || isConnected;
            _usernameField.IsReadOnly = isConnected;
            // In the DevScaffold mode, there won't be a dedicated Try Me button.
            // If the DevSetCredentialsIfNeeded method is called, the password is also filled automatically.
            // Therefore, a dedicated Try Me link is required in this state.
            bool usePin = _account?.PinAuthentication ?? false;
            _pinCodeField.IsVisible = (isConnected && usePin && activated);
            _pinCodeField.IsReadOnly = !(isConnected && usePin && activated);
            if (DevScaffold)
            {
                _pinCodeField.Label = "Password";
                _pinCodeField.Name = Strings.Password;
                _pinCodeField.MaxLength = 40;
                _pinCodeField.IsVisible = true;
                _pinCodeField.IsReadOnly = false;
            }
            _tryMeModeField.CanExecute = CanInteract;

            bool canProceed = !usePin || !string.IsNullOrEmpty(PinCode) || !_pinCodeField.IsVisible;

            Proceed.IsEnabled = canProceed && CanInteract;
            Proceed.Text = GetProceedText();

            Disconnect.IsVisible = activated || isConnected;
            Disconnect.IsEnabled = CanInteract;

            //PinCode Validation
            _pinCodeSettings = _appSettings.GetSubGroup(OfflinePinCodeConfigurations.PinCodeSettingsKey);
            int attemptsLeft = Convert.ToInt32(_pinCodeSettings.Get(OfflinePinCodeConfigurations.PinCodeAttemptsLeftKey));
            int time = Convert.ToInt32(_pinCodeSettings.Get(OfflinePinCodeConfigurations.PinCodeLockDurationKey));
            string state = _pinCodeSettings.Get(OfflinePinCodeConfigurations.PinCodeStateKey);
            string lockTimeString = _pinCodeSettings.Get(OfflinePinCodeConfigurations.PinCodeLockedTimeKey);

            if (state == OfflinePinCodeConfigurations.PinCodeStateLocked)
            {
                _pinCodeMessageField.IsVisible = true;
                _pinCodeField.IsVisible = false;
                _pinCodeField.Value = string.Empty;
                _pinCodeMessageField.Value = string.Format(Strings.AccountLockedMessage, time);
            }
            else if (state == OfflinePinCodeConfigurations.PinCodeStateBlocked)
            {
                _pinCodeMessageField.IsVisible = true;
                _pinCodeField.IsVisible = false;
                _pinCodeField.Value = string.Empty;
                _pinCodeMessageField.Value = Strings.AccountBlockedMessage;
            }
            else if (attemptsLeft > 0)
            {
                _pinCodeMessageField.IsVisible = true;
                _pinCodeMessageField.Value = string.Format(Strings.InvalidPinCodeAttempts, attemptsLeft);
            }
            else
            {
                _pinCodeMessageField.IsVisible = false;
            }

            if (state == OfflinePinCodeConfigurations.PinCodeStateLocked && lockTimeString != null && lockTimeString != string.Empty)
            {
                DateTime locktime = DateTime.Parse(lockTimeString);

                DateTime now = DateTime.Now;
                double diffInSeconds = (now - locktime).TotalSeconds;
                if (diffInSeconds > time)
                {
                    _pinCodeField.IsVisible = true;
                    _pinCodeMessageField.IsVisible = false;
                    _pinCodeMessageField.Name = string.Empty;

                    OfflinePinCodeConfigurations.ResetPinCodeSettings(_pinCodeSettings);
                }
                else
                {
                    int secsToWait = time - Convert.ToInt32(diffInSeconds);

                    _ = EnablePinCodeEntryAsync(secsToWait);
                }
            }
        }

        private async Task EnablePinCodeEntryAsync(int time)
        {
            await Task.Delay(time * 1000);

            EnablePinCodeEntryNow();
        }

        private void EnablePinCodeEntryNow()
        {
            _pinCodeField.IsVisible = true;
            _pinCodeMessageField.IsVisible = false;
            _pinCodeMessageField.Name = string.Empty;

            _app.ResetPinCodeAttempt();
            OfflinePinCodeConfigurations.ResetPinCodeSettings(_pinCodeSettings);
        }

        private string GetProceedText()
        {
            bool activated = _account?.IsActivated ?? false;
            bool isConnected = _account != null;

            if (activated)
            {
                return Strings.Login;
            }
            else if (isConnected)
            {
                return Strings.Activate;
            }
            else
            {
                return Strings.Connect;
            }
        }

        private bool Validate()
        {
            Form.ClearValidation();
            return Form.ValidateRequiredFields();
        }

        #region Login etc.

        private async Task TryAutoLogin(TouchAppAccount account)
        {
#if DEBUG
            if (account == null)
            {
                DevSetCredentialsIfNeeded(null, "https://ifs24r1bnddevlkp-ext-rnd.corporate.ifs.com/", "lkppde1719");

                if (_app.Registration.AppName == null && AppName == null)
                {
                    AppName = "FndMotOffline";
                }
            }
#endif

            if (_firstOpen &&
                account != null &&
                account.IsActivated &&
                account.IdentityProvider != null &&
                account.UserName != null &&
                !account.PinAuthentication)
            {
                await Task.Delay(50);
                await Login();
            }

            _firstOpen = false;
        }

        private void DevSetCredentialsIfNeeded(string devScaffoldUrl, string serviceUrl, string systemId)
        {
            //Only set the following values if they're empty (i.e. no saved values from a previous login)
            if (string.IsNullOrEmpty(ServiceUrl))
            {
                ServiceUrl = serviceUrl;
                DevScaffoldUrl = devScaffoldUrl;
                SystemId = systemId;
                DevScaffold = !string.IsNullOrEmpty(devScaffoldUrl) && string.IsNullOrEmpty(systemId);
            }
        }

        private async Task DoProceed()
        {
            if (_account != null || DevScaffold)
            {
                await Login();
            }
            else
            {
                await Connect();
            }

            _appSettings.Set(SwitchUserFlag, false);
        }

        private async Task Login()
        {
            CanInteract = false;

            TrimInput();

            if (!Validate())
            {
                CanInteract = true;
                return;
            }

            try
            {
                TouchAppAccount account = _account;
                string url = ServiceUrl;
                string systemId = SystemId;
                string username = Username;
                string dbPassword = PinCode;

                if (DevScaffold)
                {
                    url = DevScaffoldUrl;
                    systemId = TouchAppAccount.SystemIdForDevScaffold;
                }

                if (account == null)
                {
                    SaveInput();

                    _account = await _app.ActivateNewAccountBasic(url, systemId, AppName, username, dbPassword);
                }
                else if (account.IsActivated)
                {
                    await _app.Login(account, dbPassword);
                }
                else
                {
                    await _app.Activate(account, dbPassword);
                }

                UpdateFieldStates();

                if (PushNotificationServiceBase.PushNotificationData.ToBeExecuted)
                {
                    await PushNotificationServiceBase.DoPushCommandAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.HandleException(ExceptionType.Recoverable, ex);
                await _dialogService.ShowException(ex);
            }

            CanInteract = true;
        }

        private async Task Connect()
        {
            CanInteract = false;

            TrimInput();

            if (!Validate())
            {
                CanInteract = true;
                return;
            }

            if (!ServiceUrl.ToLower().Contains("https"))
            {
                await _dialogService.Alert(Strings.Error, Strings.HttpsValidationMessage);
                CanInteract = true;
                return;
            }

            try
            {
                ServiceUrl = ServiceUrl.TrimEnd('/');

                SaveInput();

                _account = await _app.ActivateNewAccountOpenId(ServiceUrl, SystemId, AppName);
                UpdateFieldStates();
            }
            catch (Exception ex)
            {
                _logger?.HandleException(ExceptionType.Recoverable, ex);
                await _dialogService.ShowException(ex);
            }

            CanInteract = true;
        }

        private void TrimInput()
        {
            ServiceUrl = ServiceUrl?.Trim();
            DevScaffoldUrl = DevScaffoldUrl?.Trim();
            SystemId = SystemId?.Trim();
            AppName = AppName?.Trim();
            Username = Username?.Trim();
        }

        private void SaveInput()
        {
            _settings.Set(ServiceUrlSettingName, ServiceUrl);
            _settings.Set(DevScaffoldUrlSettingName, DevScaffoldUrl);
            _settings.Set(DevScaffoldSettingName, DevScaffold);
            _settings.Set(SystemIdSettingName, SystemId);
            _settings.Set(AppNameSettingName, AppName);
        }

        private async Task TrySwitchUser()
        {
            if (_appSettings.GetBoolean(SwitchUserFlag, false))
            {
                if (DeviceInfo.OperatingSystem == OperatingSystem.iOS)
                {
                    await Task.Delay(50);
                }

                await DoDisconnect();
            }
        }

        private async Task DoDisconnect()
        {
            CanInteract = false;

            try
            {
                if (_account != null)
                {
                    await _app.RemoveAccount(_account);
                    _account = null;

                    OfflinePinCodeConfigurations.ResetPinCodeSettings(_pinCodeSettings);
                    UpdateFieldStates();
                }

                await MSAccessTokenProvider.LogOut();
            }
            catch (Exception ex)
            {
                _logger?.HandleException(ExceptionType.Recoverable, ex);
                await _dialogService.ShowException(ex);
            }

            CanInteract = true;
        }

        public async Task TryMe()
        {
            CanInteract = false;

            try
            {
                await _app.LoginTryMeMode();
            }
            catch (Exception ex)
            {
                _logger?.HandleException(ExceptionType.Recoverable, ex);
                await _dialogService.ShowException(ex);
            }

            CanInteract = true;
        }

        #endregion
    }
}
