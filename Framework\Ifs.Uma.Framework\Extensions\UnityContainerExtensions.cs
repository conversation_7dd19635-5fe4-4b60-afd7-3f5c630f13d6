﻿using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.Services;
using Ifs.Uma.Services.Location;
using Ifs.Uma.Utility;
using Unity.Lifetime;

namespace Unity
{
    public static class UnityContainerExtensions
    {
        public static void RegisterLoggerManager(this IUnityContainer container, ILoggerManager loggerManager)
        {
            container.RegisterInstance<ILoggerManager>(loggerManager);
            container.RegisterInstance<ILogger>(loggerManager.Logger);
            container.RegisterInstance<IPerfLogger>(loggerManager.PerfLogger);
            container.RegisterInstance<IInsightsLogger>(loggerManager.Insights);
        }

        public static void RegisterService<T>(this IUnityContainer container)
            where T : IService
        {
            container.RegisterService<T, T>();
        }

        public static void RegisterService<T, TImpl>(this IUnityContainer container) 
            where TImpl : IService, T
        {
            IServiceManager serviceManager = container.Resolve<IServiceManager>();
            container.RegisterType<T, TImpl>(new ContainerControlledLifetimeManager());
            serviceManager.RegisterService<TImpl>(() => (TImpl)container.Resolve<T>());
        }

        public static void RegisterLocationService<T>(this IUnityContainer container)
           where T : LocationServiceBase
        {
            container.RegisterService<T>();
            container.RegisterType<ILocationLogger, T>();
            container.RegisterType<ILocationService, T>();
        }
    }
}
