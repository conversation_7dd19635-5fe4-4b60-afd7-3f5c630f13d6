﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Rewrites queries with skip &amp; take to use the nested queries with inverted ordering technique
    /// </summary>
    internal class SkipToNestedOrderByRewriter : DbExpressionVisitor
    {
        private SkipToNestedOrderByRewriter()
        {
        }

        public static Expression Rewrite(Expression expression)
        {
            return new SkipToNestedOrderByRewriter().Visit(expression);
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            // select * from table order by x skip s take t 
            // =>
            // select * from (select top s * from (select top s + t from table order by x) order by -x) order by x

            node = (SelectExpression)base.VisitSelect(node);

            if (node.Skip != null && node.Take != null && node.OrderBy.Count > 0)
            {
                var skip = node.Skip;
                var take = node.Take;
                var skipPlusTake = PartialEvaluator.Eval(Expression.Add(skip, take));

                node = node.SetTake(skipPlusTake).SetSkip(null);
                node = node.AddRedundantSelect(new TableAlias());
                node = node.SetTake(take);

                // propogate order-bys to new layer
                node = (SelectExpression)OrderByRewriter.Rewrite(node);
                var inverted = node.OrderBy.Select(ob => new OrderExpression(
                    ob.OrderType == OrderType.Ascending ? OrderType.Descending : OrderType.Ascending,
                    ob.Expression
                    ));
                node = node.SetOrderBy(inverted);

                node = node.AddRedundantSelect(new TableAlias());
                node = node.SetTake(Expression.Constant(0)); // temporary
                node = (SelectExpression)OrderByRewriter.Rewrite(node);
                var reverted = node.OrderBy.Select(ob => new OrderExpression(
                    ob.OrderType == OrderType.Ascending ? OrderType.Descending : OrderType.Ascending,
                    ob.Expression
                    ));
                node = node.SetOrderBy(reverted);
                node = node.SetTake(null);
            }

            return node;
        }
    }
}