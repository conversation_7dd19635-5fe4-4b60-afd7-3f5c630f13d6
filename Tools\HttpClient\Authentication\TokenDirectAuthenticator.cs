using System.Text;
using Ifs.Tools.HttpClient.Models;

namespace Ifs.Tools.HttpClient.Authentication
{
    /// <summary>
    /// Handles TOKEN_DIRECT authentication by creating Basic auth tokens from username/password
    /// This replicates the logic from PlatformServices.GetAuthenticationToken()
    /// </summary>
    public class TokenDirectAuthenticator
    {
        /// <summary>
        /// Creates a Basic authentication token from username and password
        /// This is the TOKEN_DIRECT method used by IFS systems
        /// </summary>
        /// <param name="systemId">System ID (optional)</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Base64 encoded authentication token</returns>
        public static string GetAuthenticationToken(string systemId, string username, string password)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                throw new ArgumentException("Username and password are required for TOKEN_DIRECT authentication");
            }

            // The TOKEN_DIRECT method creates a Basic auth token from username:password
            // This is the same logic as in PlatformServices.GetAuthenticationToken()
            string stringToEncrypt = username + ":" + password;
            return Convert.ToBase64String(Encoding.UTF8.GetBytes(stringToEncrypt));
        }

        /// <summary>
        /// Creates the Authorization header value for TOKEN_DIRECT authentication
        /// </summary>
        /// <param name="systemId">System ID (optional)</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Authorization header value (e.g., "Basic dXNlcjpwYXNz")</returns>
        public static string GetAuthorizationHeader(string systemId, string username, string password)
        {
            var token = GetAuthenticationToken(systemId, username, password);
            return "Basic " + token;
        }
    }
}
