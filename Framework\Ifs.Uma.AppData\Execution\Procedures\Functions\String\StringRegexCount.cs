﻿using System;
using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringRegexCount : StringFunction
    {
        public const string FunctionName = "RegexCount";
        public StringRegexCount()
            : base(FunctionName, 2, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();

            if (!string.IsNullOrEmpty(regexPattern))
            {
                Regex regex = new Regex(regexPattern, RegexOptions.None, TimeSpan.FromSeconds(10));
                return regex.Matches(stringToModify).Count;
            }

            return null;
        }
    }

    internal sealed class StringRegexCount3 : StringFunction
    {
        public const string FunctionName = "RegexCount";
        public StringRegexCount3()
            : base(FunctionName, 3, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();

            long? position = parameters[2].GetInteger();

            if (!string.IsNullOrEmpty(regexPattern))
            {
                if (position.HasValue && (position.Value < 0 || position.Value > stringToModify.Length))
                {
                    return 0;
                }

                Regex regex = new Regex(regexPattern, RegexOptions.None, TimeSpan.FromSeconds(10));
                if (position != null)
                {
                    return regex.Matches(stringToModify, (int)position.Value).Count;
                }
            }

            return null;
        }
    }

    internal sealed class StringRegexCount4 : StringFunction
    {
        public const string FunctionName = "RegexCount";
        public StringRegexCount4()
            : base(FunctionName, 4, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();

            long? position = parameters[2].GetInteger();
            string regexOptionsParam = parameters[3].GetString();

            if (!string.IsNullOrEmpty(regexPattern) && position.HasValue && position.Value >= 0)
            {
                RegexOptions regexOptions = StringToRegexOptions(regexOptionsParam);

                Regex regex = new Regex(regexPattern, regexOptions, TimeSpan.FromSeconds(10));
                return regex.Matches(stringToModify, (int)position.Value).Count;
            }

            return null;
        }
    }
}
