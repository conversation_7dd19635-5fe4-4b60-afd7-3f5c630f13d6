﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.Convert
{
    [TestFixture]
    public class ConvertToNumberTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.Convert.ConvertToNumberTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase(502, ExpectedResult = 502)]
        [TestCase(502.7, ExpectedResult = 502.7)]
        [TestCase("502.7", ExpectedResult = 502.7)]
        [TestCase("2015-09-15T07:00:00", ExpectedResult = null)]
        [TestCase((long)502, ExpectedResult = 502)]
        [TestCase(true, ExpectedResult = 1)]
        [TestCase(false, ExpectedResult = 0)]
        [TestCase(null, ExpectedResult = null)]
        public async Task<object> Convert_ToNumber(object input)
        {
            _params["TextInput"] = input;
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToNumber", _params);

            CheckResults(result);

            return result?.Value;
        }

        private static void CheckResults(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
