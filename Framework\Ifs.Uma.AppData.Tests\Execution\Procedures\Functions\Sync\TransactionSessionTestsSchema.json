{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<TestInitiateTransactionSession>": {"name": "TestInitiateTransactionSession", "type": "Function", "params": [{"name": "SessionId", "datatype": "Text", "collection": false}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean", "trueValue": "TRUE", "falseValue": "FALSE", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "InitiateTransactionSession", "namespace": "Sync", "paramsArray": ["${SessionId}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<TestTransactionSessionExists>": {"name": "TestTransactionSessionExists", "type": "Function", "params": [{"name": "SessionId", "datatype": "Text", "collection": false}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean", "trueValue": "TRUE", "falseValue": "FALSE", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "TransactionSessionExists", "namespace": "Sync", "paramsArray": ["${SessionId}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<TestFinalizeTransactionSession>": {"name": "TestFinalizeTransactionSession", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean", "trueValue": "TRUE", "falseValue": "FALSE", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "FinalizeTransactionSession", "namespace": "Sync"}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<TestClearTransactionSession>": {"name": "TestClearTransactionSession", "type": "Function", "params": [{"name": "SessionId", "datatype": "Text", "collection": false}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean", "trueValue": "TRUE", "falseValue": "FALSE", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "ClearTransactionSession", "namespace": "Sync", "paramsArray": ["${SessionId}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<TestClearAllTransactionSessions>": {"name": "TestClearAllTransactionSessions", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean", "trueValue": "TRUE", "falseValue": "FALSE", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "ClearAllTransactionSessions", "namespace": "Sync"}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Action<SetCustomerName>": {"name": "SetCustomerName", "type": "Action", "params": [{"name": "Input", "datatype": "Text", "collection": false}], "layers": [{"vars": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer", "collection": false}], "execute": [{"call": {"method": "fetch", "args": {"entity": "TstCustomer", "name": "Customers", "alias": "t", "where": {"==": [{"var": "t.<PERSON><PERSON><PERSON>"}, "1"]}}}, "assign": "Customer"}, {"call": {"method": "if", "args": {"expression": {"!=": [{"var": "Customer"}, null]}}}, "result": {"TRUE": [{"call": {"method": "set", "args": {"name": "Input"}}, "assign": "Customer.CustomerName"}, {"call": {"method": "saveAndSend", "args": {"name": "Customer"}}}]}}]}]}}}}