﻿using System;
using System.Globalization;
using System.Net;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Assets;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.Utility;
using Newtonsoft.Json;

namespace Ifs.Uma.Framework.UI.Elements.Maps
{
    public class MapPin
    {
        public static MapPin CreateMapPin(string projectionName, IMetadata metadata, IExpressionRunner expressionRunner, CardData cardData, CpiMapPin cpiMapPin, CpiCard card)
        {
            CpiMapPinStyle pinStyle = null;

            if (cpiMapPin.PinStyles != null)
            {
                pinStyle = metadata.FindMapPinStyle(projectionName, expressionRunner.GetEmphasis(cpiMapPin.PinStyles, cardData));
            }

            string emphasis = UmaColor.FromEmphasis(expressionRunner.GetEmphasis(pinStyle?.Emphasis, cardData))?.ToHtmlHex()
                ?? UmaColor.FromEmphasis(expressionRunner.GetEmphasis(cpiMapPin.OfflineEmphasis ?? cpiMapPin.Emphasis, cardData))?.ToHtmlHex();

            cardData.Record.TryGetRecordValue(cpiMapPin.Latitude, out object lat);
            cardData.Record.TryGetRecordValue(cpiMapPin.Longitude, out object lon);

            if (lat == null || lon == null)
            {
                return null;
            }

            return new MapPin()
            {
                Label = WebUtility.HtmlEncode(expressionRunner.InterpolateString(cpiMapPin.Label, cardData.Record)),
                Latitude = Convert.ToString(lat, CultureInfo.InvariantCulture),
                Longitude = Convert.ToString(lon, CultureInfo.InvariantCulture),
                CpiCard = card,
                CardData = cardData,
                GenericIcon = AssetUtils.MapPin.GetBase64String(),
                StyleIcon = IconUtils.Load(pinStyle?.Icon ?? string.Empty).GetBase64String(),
                GenericIconEmphasis = emphasis,
                StyleIconEmphasis = UmaColor.FromHex(emphasis).ToForegroundColor().ToHtmlHex()
            };
        }

        public string Id { get; } = Guid.NewGuid().ToString();

        public string Label { get; private set; }

        [JsonIgnore]
        public CpiCard CpiCard { get; private set; }

        [JsonIgnore]
        public CardData CardData { get; private set; }

        public string Latitude { get; private set; }

        public string Longitude { get; private set; }

        public string GenericIconEmphasis { get; private set; }

        public string GenericIcon { get; private set; }

        public string StyleIcon { get; private set; }

        public string StyleIconEmphasis { get; private set; }
    }
}
