﻿using System;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Framework.UI.Observables;
using Ifs.Uma.Localization;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.Framework.UI.Navigation
{
    public sealed class AppMenuViewModel : EditAwareViewModel
    {
        private readonly INavigator _navigator;
        private readonly ITouchApp _touchApp;
        private readonly IAppParameters _appParameters;
        private readonly IDialogService _dialogService;
        private readonly ISettings _settings;

        public IHomePageItems HomePageItems { get; }

        public ViewableCollection<AppMenuItem> MenuItems { get; } = new ViewableCollection<AppMenuItem>();
        public ViewableCollection<AppMenuItem> SystemMenuItems { get; } = new ViewableCollection<AppMenuItem>();

        public bool DeveloperMode => _touchApp.DeveloperMode;

        public event EventHandler CurrentItemChanged;

        private AppMenuItem _currentItem;

        public AppMenuViewModel(IEventAggregator eventAggregator, INavigator navigator, ITouchApp touchApp, IAppParameters appParameters, IDialogService dialogService, IHomePageItems homePageItems, ISettings settings)
            : base(eventAggregator)
        {
            _navigator = navigator;
            _touchApp = touchApp;
            _appParameters = appParameters;
            _dialogService = dialogService;
            HomePageItems = homePageItems;
            _settings = settings;

            SetupSystemMenuItems();
        }

        public AppMenuItem CurrentItem
        {
            get { return _currentItem; }
            set
            {
                if (_currentItem != value)
                {
                    _currentItem = value;
                }
            }
        }

        public void SetupMenuItems()
        {
            MenuItems.Clear();

            if (HomePageItems.DashboardItems.Count > 1)
            {
                AppMenuItem dashboardItem = new AppMenuItem()
                {
                    Label = Strings.Dashboard,
                    Location = FrameworkLocations.Home,
                    NavParam = null,
                    Image = IconUtils.AppMenu
                };
                MenuItems.Add(dashboardItem);
            }

            foreach (AppMenuItem menuItem in HomePageItems.AppMenuItems)
            {
                menuItem.Label = menuItem.Label?.Trim();
                MenuItems.Add(menuItem);
            }

            _currentItem = MenuItems.FirstOrDefault();
            if (_currentItem != null)
            {
                _currentItem.IsSelected = true;
                _currentItem.IsHomeItem = true;
            }
        }

        private void SetupSystemMenuItems()
        {
            SystemMenuItems.Clear();

#if REMOTE_ASSISTANCE
            if (DeviceInfo.OperatingSystem != OperatingSystem.Windows && _settings.GetBoolean(SettingsExtensions.RemoteAssistanceEnabled, false))
            {
                SystemMenuItems.Add(new AppMenuItem()
                {
                    Label = Strings.RemoteAssistance,
                    Location = FrameworkLocations.RemoteAssistance,
                    Image = IconUtils.Phone
                });
            }
#endif

            if (_touchApp.DeveloperMode && !_touchApp.CurrentSession.IsTryMeMode)
            {
                SystemMenuItems.Add(new AppMenuItem()
                {
                    Label = Strings.UpdateMetadata,
                    Location = FrameworkLocations.UpdateMetadata,
                    Image = IconUtils.RefreshAlt
                });
            }

            SystemMenuItems.Add(new AppMenuItem()
            {
                Label = Strings.Logout,
                Location = FrameworkLocations.Logout,
                Image = IconUtils.Logout
            });

            if (_appParameters.IsMultiUserEnabled())
            {
                SystemMenuItems.Add(new AppMenuItem()
                {
                    Label = Strings.SwitchUser,
                    Location = FrameworkLocations.SwitchUser,
                    Image = IconUtils.People
                });
            }

            SystemMenuItems.Add(new AppMenuItem()
            {
                Label = Strings.Sync,
                Location = FrameworkLocations.SyncMonitor,
                Image = IconUtils.Sync
            });

            SystemMenuItems.Add(new AppMenuItem()
            {
                Label = Strings.Settings,
                Location = FrameworkLocations.Settings,
                Image = IconUtils.Settings
            });
        }

        public async Task SelectMenuItem(AppMenuItem item)
        {
            ClearAppMenuSelections();

            if (item != null)
            {
                item.IsSelected = true;
                if (item.Location == FrameworkLocations.Logout)
                {
                    await _touchApp.LogoutAsync();
                }
                else if (item.Location == FrameworkLocations.SwitchUser)
                {
                    if (PlatformServices.SimulateNoConnection || !PlatformServices.IsNetworkAvailable())
                    {
                        bool result = await _dialogService.Confirm(string.Empty, Strings.SwitchUserOfflineWarning, Strings.Ok, ConfirmationType.Normal);

                        if (!result)
                        {
                            _currentItem.IsSelected = true;
                            item.IsSelected = false;
                            return;
                        }
                    }

                    _touchApp.SwitchUser();
                }
                else if (item.Location == FrameworkLocations.UpdateMetadata)
                {
                    if (PlatformServices.SimulateNoConnection || !PlatformServices.IsNetworkAvailable())
                    {
                        await _dialogService.Alert(string.Empty, Strings.YouMustBeOnline);
                        _currentItem.IsSelected = true;
                        item.IsSelected = false;
                        return;
                    }

                    if (_touchApp.TryResolve(out ITransactionSyncService transactionSyncService))
                    {
                        transactionSyncService.RequestMetadataUpdate();
                    }
                }
                else if (item.Location == FrameworkLocations.External)
                {
                    ExternalNavParam navParam = item.NavParam as ExternalNavParam;
                    await _navigator.NavigateToUrlAsync(navParam?.Url);
                    _currentItem.IsSelected = true;
                    item.IsSelected = false;
                }
                else
                {
                    await item.NavigateToAsync(_navigator);

                    if (_currentItem != null && _currentItem.IsSelected)
                    {
                        _currentItem.IsSelected = false;
                    }

                    _currentItem = item;
                    _currentItem.IsSelected = true;
                    CurrentItemChanged?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public void NotifySelectedItemChanged()
        {
            if (_currentItem != null)
            {
                _currentItem.IsSelected = true;
            }

            CurrentItemChanged?.Invoke(this, EventArgs.Empty);
        }

        public void ClearAppMenuSelections()
        {
            foreach (AppMenuItem i in MenuItems)
            {
                i.IsSelected = false;
            }

            foreach (AppMenuItem i in SystemMenuItems)
            {
                i.IsSelected = false;
            }
        }

        protected override void OnEnableEditingGroup(EnableEditingGroupEvent obj)
        {
            IsEditingAllowed = true;
        }

        protected override void OnDisableEditingGroup(DisableEditingGroupEvent obj)
        {
            IsEditingAllowed = false;
        }
    }
}
