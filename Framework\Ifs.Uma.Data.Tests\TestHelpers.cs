﻿using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using IQToolkit.Data.Common;
using IQToolkit.Data.Common.Language;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Data.Tests
{
    public static class TestHelpers
    {
        public static string GetSql<T>(IQueryable<T> query)
        {
            Expression expression = query.Expression;
            LambdaExpression lambda = expression as LambdaExpression;

            if (lambda != null)
            {
                expression = lambda.Body;
            }

            TestSqlBuilder sqlBuilder = new TestSqlBuilder();
            MappingSource mappingSource = new AttributeMappingSource(null, null, null);
            IMetaModel metaModel = mappingSource.GetModel(typeof(DataContext));
            SqlBuilderLanguage language = new SqlBuilderLanguage(sqlBuilder);
            QueryTranslator translator = new QueryTranslator(language, metaModel, QueryPolicy.Default);
            expression = translator.Translate(expression);
            expression = translator.Linguist.Parameterize(expression);
            return translator.Linguist.Format(((ProjectionExpression)expression).Select);
        }

        public static string GetSql(IWriteSql query)
        {
            StringBuilder sb = new StringBuilder();
            TestSqlBuilder sqlBuilder = new TestSqlBuilder();
            query.WriteSql(sb, new TestSi(), sqlBuilder, SqlWriteMode.Statement);
            return sb.ToString();
        }

        private class TestSi : IStatementInfo
        {
            public DateTime StatementNow { get; } = new DateTime(2000, 1, 1, 12, 0, 0);
            public IEnumerable<DbParameter> Parameters => Enumerable.Empty<DbParameter>();

            public void WriteParameter(StringBuilder sb, Type parameterType, object value, int valueIndex)
            {
                string str = ObjectConverter.ToString(value);
                sb.Append(str == null ? "null" : "'" + str + "'");
            }
        }

        public static bool Execute<T>(IQueryable<T> query)
        {
            TestSqlBuilder sqlBuilder = new TestSqlBuilder();
            MappingSource mappingSource = new AttributeMappingSource(null, null, null);
            IMetaModel metaModel = mappingSource.GetModel(typeof(DataContext));
            IMetaTable mappingEntity = metaModel.GetEntity(typeof(Transition));
            SqlBuilderLanguage language = new SqlBuilderLanguage(sqlBuilder);
            QueryTranslator translator = new QueryTranslator(language, metaModel, QueryPolicy.Default);
            return ExecuteInner(translator, query.Expression) != null;
        }

        private static object ExecuteInner(QueryTranslator translator, Expression expression)
        {
            LambdaExpression lambda = expression as LambdaExpression;

            if (lambda != null)
            {
                expression = lambda.Body;
            }

            // translate query into client & server parts
            Expression translation = translator.Translate(expression);
            Expression provider = Expression.New(typeof(TestExecutorCreator));
            Expression plan = translator.Police.BuildExecutionPlan(translation, provider);

            if (lambda != null)
            {
                // compile & return the execution plan so it can be used multiple times
                LambdaExpression fn = Expression.Lambda(lambda.Type, plan, lambda.Parameters);
                return fn.Compile();
            }
            else
            {
                // compile the execution plan and invoke it
                Expression<Func<object>> efn = Expression.Lambda<Func<object>>(Expression.Convert(plan, typeof(object)));
                Func<object> fn = efn.Compile();
                return fn();
            }
        }

        private class TestExecutorCreator : QueryExecutor, ICreateExecutor
        {
            public QueryExecutor CreateExecutor()
            {
                return this;
            }

            public override int RowsAffected
            {
                get { return 0; }
            }

            public override object Convert(object value, Type type)
            {
                return System.Convert.ChangeType(value, type, System.Globalization.CultureInfo.InvariantCulture);
            }

            public override IEnumerable<T> Execute<T>(QueryCommand command, Func<FieldReader, T> fnProjector, object[] paramValues)
            {
                return new T[0];
            }

            public override IEnumerable<int> ExecuteBatch(QueryCommand query, IEnumerable<object[]> paramSets, int batchSize, bool stream)
            {
                throw new NotImplementedException();
            }

            public override IEnumerable<T> ExecuteBatch<T>(QueryCommand query, IEnumerable<object[]> paramSets, Func<FieldReader, T> fnProjector, int batchSize, bool stream)
            {
                throw new NotImplementedException();
            }

            public override IEnumerable<T> ExecuteDeferred<T>(QueryCommand query, Func<FieldReader, T> fnProjector, object[] paramValues)
            {
                throw new NotImplementedException();
            }

            public override int ExecuteCommand(QueryCommand query, object[] paramValues)
            {
                throw new NotImplementedException();
            }
        }

        private class TestSqlBuilder : SqlBuilder
        {
        }
    }
}
