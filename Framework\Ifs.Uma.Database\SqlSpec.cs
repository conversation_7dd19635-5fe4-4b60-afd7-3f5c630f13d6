﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Database
{
    public enum ESqlSpec
    {
        Select,
        Insert,
        Update,
        Delete
    }

    public interface ISqlSpec : IWriteSql
    {
        ESqlSpec Kind { get; }
    }

    public interface ISelectSpec : ISqlSpec
    {
        bool Distinct { get; }
        IEnumerable<ISelectColumnSpec> Columns { get; }
        ITableSpec From { get; }
        IEnumerable<IJoinSpec> Joins { get; }
        IEnumerable<IWhereElement> Where { get; }
        IEnumerable<IAliasedColumnSpec> GroupBy { get; }
        IOrderBy OrderBy { get; }
    }

    public interface IDeleteSpec : ISqlSpec
    {
        int Top { get; }
        string TableName { get; }
        IEnumerable<IWhereElement> Where { get; }
    }

    public interface IInsertBase
    {
        string TableName { get; }
        string IdentityColumn { get; }
    }

    public interface IInsertSpec : ISqlSpec, IInsertBase
    {
        IEnumerable<IColumnUpdateSpec> Columns { get; }
        IEnumerable<string> UpsertUniqueColumns { get; }
        IUpdateSpec ToUpdate(); // validates upsert columns
        IInsertSpec ToInsert(); // removes upsert columns
    }

    public interface IUpdateSpec : ISqlSpec
    {
        string TableName { get; }
        IEnumerable<IColumnUpdateSpec> Columns { get; }
        IEnumerable<IWhereElement> Where { get; }
    }

    public abstract class SqlSpec : ISqlSpec
    {
        #region SqlSpec Factories

        public static IInsertSpec CreateInsert(string tableName, IEnumerable<IColumnUpdateSpec> columns,
            string identityColumn, IEnumerable<string> upsertUniqueColumns)
        {
            return !string.IsNullOrEmpty(tableName) && columns != null && columns.Any(x => x != null) ?
                new InsertSpec(tableName, columns, identityColumn, upsertUniqueColumns) : null;
        }

        public static ISelectSpec CreateSelect(IEnumerable<UnionSelectInformation> unionSelects)
        {
            return new SelectSpec(unionSelects);
        }

        public static ISelectSpec CreateSelect(IEnumerable<ISelectColumnSpec> columns, ITableSpec from,
            IEnumerable<IJoinSpec> joins, IEnumerable<IWhereElement> where,
            IEnumerable<IAliasedColumnSpec> groupBy, IOrderBy orderBy, bool distinct)
        {
            return from != null && columns != null && columns.Any(x => x != null) ?
                new SelectSpec(columns, from, joins, where, groupBy, orderBy, distinct) : null;
        }

        public static IUpdateSpec CreateUpdate(string tableName, IEnumerable<IColumnUpdateSpec> columns,
            IEnumerable<IWhereElement> where)
        {
            return !string.IsNullOrEmpty(tableName) && columns != null && columns.Any(x => x != null) ?
                new UpdateSpec(tableName, columns, where) : null;
        }

        public static IDeleteSpec CreateDelete(string tableName, IEnumerable<IWhereElement> where)
        {
            // a non-positive top means "no top" 
            return CreateDelete(tableName, where, -1);
        }

        public static IDeleteSpec CreateDelete(string tableName, IEnumerable<IWhereElement> where, int top)
        {
            return !string.IsNullOrEmpty(tableName) ? new DeleteSpec(tableName, where, top) : null;
        }

        #endregion

        public abstract ESqlSpec Kind { get; }
        public abstract void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode);

        protected SqlSpec() { }

        protected class InsertSpec : SqlSpec, IInsertSpec
        {
            public InsertSpec(string tableName, IEnumerable<IColumnUpdateSpec> columns, string identityColumn,
                IEnumerable<string> upsertUniqueColumns)
            {
                TableName = tableName;
                Columns = columns;
                IdentityColumn = identityColumn;
                UpsertUniqueColumns = upsertUniqueColumns;
            }

            public override ESqlSpec Kind { get { return ESqlSpec.Insert; } }
            public string TableName { get; private set; }
            public IEnumerable<IColumnUpdateSpec> Columns { get; private set; }
            public string IdentityColumn { get; private set; }
            public IEnumerable<string> UpsertUniqueColumns { get; set; }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (sb != null && builder != null && info != null)
                {
                    sb.Append(builder.SupportsUpsert && ToUpdate() != null ?
                        builder.UpsertIntoClause : builder.InsertIntoClause);
                    sb.Append(builder.DecorateTableName(TableName));
                    sb.Append(builder.ColumnsProlog);
                    builder.WriteCommaSeparatedList(sb, info, SqlWriteMode.Column, Columns);
                    sb.Append(builder.ColumnsEpilog);
                    sb.Append(builder.ValuesProlog);
                    builder.WriteCommaSeparatedList(sb, info, SqlWriteMode.ColumnValue, Columns);
                    sb.Append(builder.ValuesEpilog);
                    if (!string.IsNullOrEmpty(IdentityColumn))
                    {
                        sb.Append(builder.ScopeIdentityEpilog);
                    }
                }
            }

            public IUpdateSpec ToUpdate()
            {
                IUpdateSpec result = null;
                if (UpsertUniqueColumns != null)
                {
                    ICollection<IColumnUpdateSpec> updateColumns = new List<IColumnUpdateSpec>();
                    ICollection<IWhereElement> whereColumns = new List<IWhereElement>();
                    foreach (IColumnUpdateSpec column in Columns.Where(x => x != null))
                    {
                        IColumnSpecAndValue value = column as IColumnSpecAndValue;
                        if (value != null && UpsertUniqueColumns.Contains(column.ColumnName))
                        {
                            whereColumns.Add(WhereElement.Create(EOperand.And, value, EComparisonMethod.Equals, value.Value));
                        }
                        else
                        {
                            updateColumns.Add(column);
                        }
                    }
                    if (updateColumns.Any() && whereColumns.Any())
                    {
                        result = CreateUpdate(TableName, updateColumns, whereColumns);
                    }
                }
                return result;
            }

            public IInsertSpec ToInsert()
            {
                return CreateInsert(TableName, Columns, IdentityColumn, null);
            }
        }

        protected class SelectSpec : SqlSpec, ISelectSpec
        {
            public SelectSpec(IEnumerable<ISelectColumnSpec> columns, ITableSpec from,
                IEnumerable<IJoinSpec> joins, IEnumerable<IWhereElement> where,
                IEnumerable<IAliasedColumnSpec> groupBy, IOrderBy orderBy, bool distinct)
            {
                Distinct = distinct;
                Columns = columns;
                From = from;
                Joins = joins;
                Where = where;
                GroupBy = groupBy;
                OrderBy = orderBy;
            }

            public SelectSpec(IEnumerable<UnionSelectInformation> unionSelects)
            {
                UnionSelectInformations = unionSelects.ToList();
            }

            public override ESqlSpec Kind { get { return ESqlSpec.Select; } }
            public bool Distinct { get; private set; }
            public IEnumerable<ISelectColumnSpec> Columns { get; private set; }
            public ITableSpec From { get; private set; }
            public IEnumerable<IJoinSpec> Joins { get; private set; }
            public IEnumerable<IWhereElement> Where { get; private set; }
            public IEnumerable<IAliasedColumnSpec> GroupBy { get; private set; }
            public IOrderBy OrderBy { get; private set; }
            public List<UnionSelectInformation> UnionSelectInformations { get; private set; }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (sb != null && info != null && builder != null)
                {
                    if (UnionSelectInformations == null)
                    {
                        sb.Append(Distinct ? builder.SelectDistinctClause : builder.SelectClause);
                        if (builder.LimitPlacement == LimitPosition.LimitOnlyAtStart && OrderBy != null)
                        {
                            builder.WriteLimit(sb, OrderBy.Limit);
                        }
                        builder.WriteCommaSeparatedList(sb, info, SqlWriteMode.SelectColumn, Columns);
                        sb.Append(builder.FromClause);
                        From.WriteSql(sb, info, builder, mode);
                        if (Joins != null && Joins.Any(x => x != null))
                        {
                            foreach (IJoinSpec join in Joins)
                            {
                                if (join != null)
                                {
                                    join.WriteSql(sb, info, builder, mode);
                                }
                            }
                        }
                        builder.WriteWhere(sb, info, Where);
                        if (GroupBy != null && GroupBy.Any(x => x != null))
                        {
                            sb.Append(builder.GroupByClause);
                            builder.WriteCommaSeparatedList(sb, info, SqlWriteMode.Column, GroupBy);
                        }
                        if (OrderBy != null)
                        {
                            OrderBy.WriteSql(sb, info, builder, mode);
                        }
                    }
                    else
                    {
                        for (int i = 0; i < UnionSelectInformations.Count; i++)
                        {
                            UnionSelectInformation unionSelectInformation = UnionSelectInformations[i];

                            sb.Append(builder.SelectClause);
                            builder.WriteCommaSeparatedList(sb, info, SqlWriteMode.Column, unionSelectInformation.Columns);
                            sb.Append(builder.FromClause);
                            unionSelectInformation.TableNameSpec.WriteSql(sb, info, builder, mode);
                            if (i < UnionSelectInformations.Count - 1)
                            {
                                sb.Append(builder.UnionAll);
                            }
                        }
                    }
                }
            }
        }

        protected class UpdateSpec : SqlSpec, IUpdateSpec
        {
            public UpdateSpec(string tableName, IEnumerable<IColumnUpdateSpec> columns, IEnumerable<IWhereElement> where)
            {
                TableName = tableName;
                Columns = columns;
                Where = where;
            }
            public override ESqlSpec Kind { get { return ESqlSpec.Update; } }
            public string TableName { get; private set; }
            public IEnumerable<IColumnUpdateSpec> Columns { get; private set; }
            public IEnumerable<IWhereElement> Where { get; private set; }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (sb != null && info != null && builder != null)
                {
                    sb.Append(builder.UpdateProlog);
                    sb.Append(builder.DecorateTableName(TableName));
                    sb.Append(builder.UpdateEpilog);
                    builder.WriteCommaSeparatedList(sb, info, SqlWriteMode.ColumnAssign, Columns);
                    builder.WriteWhere(sb, info, Where);
                }
            }
        }

        protected class DeleteSpec : SqlSpec, IDeleteSpec
        {
            public DeleteSpec(string tableName, IEnumerable<IWhereElement> where, int top)
            {
                TableName = tableName;
                Where = where;
                Top = top;
            }
            public override ESqlSpec Kind { get { return ESqlSpec.Delete; } }
            public int Top { get; private set; }
            public string TableName { get; private set; }
            public IEnumerable<IWhereElement> Where { get; private set; }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (sb != null && info != null && builder != null &&
                    !builder.HandleDeleteTop(this, sb, info))
                {
                    sb.Append(builder.DeleteProlog(Top));
                    sb.Append(builder.DecorateTableName(TableName));
                    builder.WriteWhere(sb, info, Where);
                    sb.Append(builder.DeleteEpilog(Top));
                }
            }
        }
    }

    public struct UnionSelectInformation
    {
        public ITableNameSpec TableNameSpec { get; set; }

        public IEnumerable<IUnionColumnSpec> Columns { get; set; }
    }
}
