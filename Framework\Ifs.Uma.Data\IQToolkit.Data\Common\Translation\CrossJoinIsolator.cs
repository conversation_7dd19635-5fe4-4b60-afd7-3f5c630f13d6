﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Isolates cross joins from other types of joins using nested sub queries
    /// </summary>
    internal class CrossJoinIsolator : DbExpressionVisitor
    {
        ILookup<TableAlias, ColumnExpression> columns;
        Dictionary<ColumnExpression, ColumnExpression> map = new Dictionary<ColumnExpression, ColumnExpression>();
        JoinType? lastJoin;

        public static Expression Isolate(Expression expression)
        {
            return new CrossJoinIsolator().Visit(expression);
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            var saveColumns = this.columns;
            this.columns = ReferencedColumnGatherer.Gather(node).ToLookup(c => c.Alias);
            var saveLastJoin = this.lastJoin;
            this.lastJoin = null;
            var result = base.VisitSelect(node);
            this.columns = saveColumns;
            this.lastJoin = saveLastJoin;
            return result;
        }

        protected override Expression VisitJoin(JoinExpression node)
        {
            if (node == null) return null;
            var saveLastJoin = this.lastJoin;
            this.lastJoin = node.Join;
            node = (JoinExpression)base.VisitJoin(node);
            this.lastJoin = saveLastJoin;

            if (this.lastJoin != null && (node.Join == JoinType.CrossJoin) != (this.lastJoin == JoinType.CrossJoin))
            {
                var result = this.MakeSubquery(node);
                return result;
            }
            return node;
        }

        //private static bool IsCrossJoin(Expression expression)
        //{
        //    var jex = expression as JoinExpression;
        //    if (jex != null)
        //    {
        //        return jex.Join == JoinType.CrossJoin;
        //    }
        //    return false;
        //}

        private Expression MakeSubquery(Expression expression)
        {
            var newAlias = new TableAlias();
            var aliases = DeclaredAliasGatherer.Gather(expression);

            var decls = new List<ColumnDeclaration>();
            foreach (var ta in aliases) 
            {
                foreach (var col in this.columns[ta])
                {
                    string name = decls.GetAvailableColumnName(col.Name);
                    var decl = new ColumnDeclaration(name, col);
                    decls.Add(decl);
                    var newCol = new ColumnExpression(col.Type, newAlias, col.Name);
                    this.map.Add(col, newCol);
                }
            }

            return new SelectExpression(newAlias, decls, expression, null);
        }

        protected override Expression VisitColumn(ColumnExpression node)
        {
            ColumnExpression mapped;
            if (this.map.TryGetValue(node, out mapped))
            {
                return mapped;
            }
            return node;
        }
    }
}