﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Database
{
    internal class QueryScope
    {
        private static readonly Regex IdentifierRegex = new Regex(@"^[0-9a-zA-Z_$]+$", RegexOptions.None, TimeSpan.FromSeconds(10));

        public string Name { get; }
        public IMetadata Metadata { get; }

        private readonly Dictionary<string, IMetaTable> _aliases = new Dictionary<string, IMetaTable>();
        private readonly HashSet<string> _implicitJoins = new HashSet<string>(); // alias.RefName
        private readonly QueryScope _parent;
        private readonly string _projectionName;

        private string _entityQueryAlias;
        public string EntityQueryAlias
        {
            get { return _entityQueryAlias ?? _parent?.EntityQueryAlias; }
            set { _entityQueryAlias = value; }
        }

        public QueryScope(string projectionName, string name, IMetadata metadata)
        {
            _projectionName = projectionName;
            Name = name;
            Metadata = metadata ?? throw new ArgumentNullException(nameof(metadata));
        }

        private QueryScope(string projectionName, string name, IMetadata metadata, QueryScope parent)
            : this(projectionName, name, metadata)
        {
            _parent = parent;
        }

        public void AddAlias(string alias, IMetaTable table)
        {
            if (string.IsNullOrWhiteSpace(alias)) throw new ArgumentNullException(nameof(alias));
            if (table == null) throw new ArgumentNullException(nameof(table));

            if (!ValidateIdentifier(alias))
            {
                throw new InvalidMetadataException($"Query '{RemoteNaming.ToEntityName(table.TableName)}' contains invalid alias identifier '{alias}'");
            }

            if (_aliases.ContainsKey(alias))
            {
                throw new InvalidMetadataException($"Query '{RemoteNaming.ToEntityName(table.TableName)}' contains duplicate alias '{alias}'");
            }

            _aliases.Add(alias, table);
        }
        
        public string GetFullAlias(string alias)
        {
            return alias;
        }

        public IEnumerable<IJoinSpec> GetImplicitJoins()
        {
            List<IJoinSpec> joins = null;
            foreach (string implicitJoin in _implicitJoins)
            {
                string[] parts = implicitJoin.Split(AttributePath.PathSeparator);
                if (parts.Length < 2)
                {
                    continue;
                }
                string alias = parts[0];
                string refName = parts[1];
                IMetaTable baseTable;
                if (!_aliases.TryGetValue(alias, out baseTable))
                {
                    continue;
                }
                string baseEntity = RemoteNaming.ToEntityName(baseTable.TableName);
                CpiReference reference = Metadata.FindReference(_projectionName, baseEntity, refName);
                if (reference == null)
                {
                    continue;
                }

                string refTableAlias = GetFullAlias(alias + "$" + refName);
                IMetaTable joinTable = Metadata.GetTableForEntityName(reference.Target);
                ITableSpec joinTableSpec = TableSpec.Create(joinTable.TableName, refTableAlias);
                List<IJoinColumn> joinColumns = new List<IJoinColumn>();
                foreach (KeyValuePair<string, string> kvpMap in reference.Mapping)
                {
                    IMetaDataMember joinMember = joinTable.FindMemberByPropertyName(kvpMap.Value);
                    IMetaDataMember baseTableMember = baseTable.FindMemberByPropertyName(kvpMap.Key);
                    joinColumns.Add(JoinColumn.Create(joinTableSpec.TableAlias, joinMember.ColumnName, alias, baseTableMember.ColumnName));
                }
                if (joinColumns.Count > 0)
                {
                    if (joins == null)
                    {
                        joins = new List<IJoinSpec>();
                    }
                    joins.Add(JoinSpec.Create(EJoinType.LeftOuter, joinTableSpec, joinColumns));
                }
            }
            return joins;
        }

        internal QueryScope CreateChild()
        {
            return new QueryScope(_projectionName, Name, Metadata, this);
        }

        internal ISelectColumnSpec GetColumnSpec(string attributePath)
        {
            string[] parts = attributePath.Split(AttributePath.PathSeparator);

            if (parts.Length < 2)
            {
                return null;
            }

            string alias = parts[0];
            IMetaTable table;
            if (!TryGetAlias(alias, out table, out QueryScope scope))
            {
                return null;
            }

            if (parts.Length == 2)
            {
                string attributeName = parts[1];
                IMetaDataMember member = table.FindMemberByPropertyName(attributeName);

                if (member == null)
                {
                    return null;
                }

                return ColumnSpec.Create(member.ColumnName, GetFullAlias(alias), null);
            }
            else
            {
                string refName = parts[1];
                string attributeName = parts[2];
                RegisterOrGetImplicitJoinAlias(alias, refName, out IMetaTable refTable, out string refAlias);

                IMetaDataMember member = refTable?.FindMemberByPropertyName(attributeName);

                if (member == null)
                {
                    return null;
                }

                return ColumnSpec.Create(member.ColumnName, GetFullAlias(refAlias), null);
            }
        }

        internal IUnionColumnSpec GetColumnSpec(string entityName, string attributePath)
        {
            IMetaTable table = Metadata.GetTableForEntityName(entityName);

            if (table == null)
            {
                return null;
            }

            IMetaDataMember member = table.FindMemberByPropertyName(attributePath);

            bool columnAsNull;
            string columnName;

            if (member == null)
            {
                columnName = RemoteNaming.ToColumnName(attributePath);
                columnAsNull = true;
            }
            else
            {
                columnName = member.ColumnName;
                columnAsNull = false;
            }

            return ColumnSpec.CreateUnionSelect(columnName, columnAsNull);
        }

        internal ISelectColumnSpec GetColumnSpec(AttributePathInfo attribute)
        {
            // AttributeInfo is used when doing an EntityQuery. Here no alias is provided
            // and the column should come from the entity the query is for.
  
            string entityAlias = EntityQueryAlias;
            if (entityAlias == null)
            {
                return null;
            }
            else
            {
                return GetColumnSpec(entityAlias + AttributePath.PathSeparator + attribute.Path);
            }
        }

        private bool TryGetAlias(string alias, out IMetaTable table, out QueryScope scope)
        {
            if (_aliases.TryGetValue(alias, out table))
            {
                scope = this;
                return true;
            }
            else if (_parent != null)
            {
                return _parent.TryGetAlias(alias, out table, out scope);
            }
            else
            {
                scope = null;
                return false;
            }
        }

        private void RegisterOrGetImplicitJoinAlias(string tableAlias, string refName, out IMetaTable refTable, out string refAlias)
        {
            refTable = null;
            refAlias = null;

            if (!TryGetAlias(tableAlias, out IMetaTable table, out QueryScope scope))
            {
                return;
            }

            string baseEntity = RemoteNaming.ToEntityName(table.TableName);
            CpiReference reference = Metadata.FindReference(_projectionName, baseEntity, refName);
            if (reference == null)
            {
                return;
            }

            refTable = Metadata.GetTableForEntityName(reference.Target);
            if (refTable == null)
            {
                return;
            }

            if (!ValidateIdentifier(refName))
            {
                throw new InvalidMetadataException($"Query '{scope.Name}' contains invalid reference identifier '{refName}'");
            }

            refAlias = tableAlias + "$" + refName;
            scope._implicitJoins.Add(tableAlias + AttributePath.PathSeparator + refName);
        }

        public bool ValidateIdentifier(string identifier)
        {
            return IdentifierRegex.IsMatch(identifier);
        }
    }
}
