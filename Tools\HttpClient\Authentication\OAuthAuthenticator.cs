using System.Text;
using System.Text.Json;
using Ifs.Tools.HttpClient.Models;

namespace Ifs.Tools.HttpClient.Authentication
{
    /// <summary>
    /// Handles OAuth2 authentication using the Resource Owner Password Credentials Grant (grant_type=password)
    /// This replicates the logic from GetDirectAccessToken() methods
    /// </summary>
    public class OAuthAuthenticator
    {
        private readonly System.Net.Http.HttpClient _httpClient;

        public OAuthAuthenticator(System.Net.Http.HttpClient? httpClient = null)
        {
            _httpClient = httpClient ?? new System.Net.Http.HttpClient();
        }

        /// <summary>
        /// Gets an access token using OAuth2 Resource Owner Password Credentials Grant
        /// This is used when the server supports OAuth2 and we have username/password
        /// </summary>
        /// <param name="identityProvider">Identity provider with token endpoint</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Token response with access token</returns>
        public async Task<TokenResponse?> GetDirectAccessToken(IdentityProvider identityProvider, string username, string password)
        {
            if (string.IsNullOrEmpty(username?.Trim()) || string.IsNullOrEmpty(password?.Trim()))
            {
                return null;
            }

            if (string.IsNullOrEmpty(identityProvider.TokenEndpoint))
            {
                throw new InvalidOperationException("Token endpoint is required for OAuth authentication");
            }

            var values = new Dictionary<string, string>
            {
                ["client_id"] = identityProvider.ClientId,
                ["grant_type"] = "password",
                ["username"] = username,
                ["password"] = password
            };

            var content = new FormUrlEncodedContent(values);

            try
            {
                var response = await _httpClient.PostAsync(identityProvider.TokenEndpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(json);

                    if (tokenResponse != null)
                    {
                        // Set dummy ID token for direct access (as done in the original code)
                        tokenResponse.IdToken = "dummy";
                    }

                    return tokenResponse;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new UnauthorizedAccessException($"OAuth authentication failed: {response.StatusCode} - {errorContent}");
                }
            }
            catch (HttpRequestException ex)
            {
                throw new InvalidOperationException($"Failed to connect to OAuth token endpoint: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates the Authorization header value for OAuth Bearer token authentication
        /// </summary>
        /// <param name="accessToken">Access token from OAuth response</param>
        /// <returns>Authorization header value (e.g., "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")</returns>
        public static string GetAuthorizationHeader(string accessToken)
        {
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new ArgumentException("Access token is required for OAuth authentication");
            }

            return "Bearer " + accessToken;
        }
    }
}
