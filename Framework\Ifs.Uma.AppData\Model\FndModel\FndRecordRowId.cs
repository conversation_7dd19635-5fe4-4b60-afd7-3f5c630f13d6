﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndRecordRowId : RemoteRow
    {
        public const string DbTableName = "fnd_record_row_id";

        [Column]
        public string RecordTableName { get; set; }

        [Column]
        public long? RecordRowId { get; set; }

        public FndRecordRowId()
            : base(DbTableName)
        {
        }
    }
}
