﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Unity;
using Unity.Lifetime;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class PrepareCacheTests : ProcedureTest
    {
        [Test]
        public async Task Parameters()
        {
            await Run("TestParameter", true, "TstCustomer");
        }

        [Test]
        public async Task Vars()
        {
            await Run("TestVars", false, "TstCustomerAddress");
        }

        [Test]
        public async Task EntitySet()
        {
            await Run("TestEntitySet", false, "TstCustomer");
        }

        [Test]
        public async Task Array()
        {
            await Run("TestArray", true, "TstCustomer", "TstCustomerAddress");
        }

        [Test]
        public async Task ArrayByVar()
        {
            await Run("TestArrayByVar", true, "TstCustomer", "TstCustomerAddress");
        }

        [Test]
        public async Task Where()
        {
            await Run("TestWhere", true, "TstCustomer", "TstCustomerType");
        }

        [Test]
        public async Task WhereAliased()
        {
            await Run("TestWhereAliased", true, "TstCustomer", "TstCustomerType");
        }

        [Test]
        public async Task Reference()
        {
            await Run("TestReference", true, "TstCustomer", "TstCustomerType");
        }

        [Test]
        public async Task ReferenceByVar()
        {
            await Run("TestReferenceByVar", true, "TstCustomer", "TstCustomerType");
        }

        private async Task Run(string functionName, bool addCustomer, params string[] expectedEntities)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            if (addCustomer)
            {
                RemoteRow customer = new RemoteRow("tst_customer");
                customer["CustomerNo"] = "501";
                customer["CustomerName"] = "Test Customer 2";
                customer["CustomerType"] = "TYPE_B";
                parameters["Customer"] = customer;
            }
            
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, functionName, parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            TestCachePreparer preparer = (TestCachePreparer)Resolve<ICachePreparer>();

            CollectionAssert.AreEquivalent(expectedEntities, preparer.EntityNames);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            Container.RegisterType<ICachePreparer, TestCachePreparer>(new ContainerControlledLifetimeManager());

            PrepareDatabase<FwDataContext>("Execution.Procedures.PrepareCacheSchema", null);
        }

        private sealed class TestCachePreparer : ICachePreparer
        {
            public HashSet<string> EntityNames { get; } = new HashSet<string>();

            public Task<PrepareCacheResult> PrepareCacheAsync(string projectionName, string entityName, CancellationToken cancelToken = default(CancellationToken))
            {
                EntityNames.Add(entityName);
                return Task.FromResult(PrepareCacheResult.Ready);
            }

            public Task<PrepareCacheResult> AddToCacheAsync(EntityQuery query, CancellationToken cancelToken = default(CancellationToken))
            {
                throw new System.NotImplementedException();
            }

            public Task<bool> IsCacheReadyAsync(string projectionName, string entityName)
            {
                EntityNames.Add(entityName);
                return Task.FromResult(true);
            }

            public Task ClearCache()
            {
                throw new System.NotImplementedException();
            }

            public Task<bool> RefreshCache()
            {
                throw new System.NotImplementedException();
            }
        }
    }
}
