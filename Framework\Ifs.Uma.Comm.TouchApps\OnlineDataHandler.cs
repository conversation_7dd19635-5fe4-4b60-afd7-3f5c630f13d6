﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Exceptions;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;
using Prism.Events;

namespace Ifs.Uma.Comm.TouchApps
{
    public sealed class OnlineDataHandler : IOnlineDataHandler
    {
        private readonly IPerfLogger _perfLogger;
        private readonly IMetadata _metadata;
        private readonly IClientKeysMapper _clientKeysMapper;
        private readonly ILogger _logger;
        private readonly IEventAggregator _eventAggregator;
        private readonly TouchAppsComms _comms;

        public OnlineDataHandler(IIfsConnection connection, IPerfLogger perfLogger, ILogger logger, IMetadata metadata, IClientKeysMapper clientKeysMapper,
            IEventAggregator eventAggregator)
        {
            _perfLogger = perfLogger;
            _metadata = metadata;
            _clientKeysMapper = clientKeysMapper;
            _logger = logger;
            _eventAggregator = eventAggregator;
            _comms = connection.TouchAppsComms;
        }

        public string UserName => _comms.UserName;

        public int DeviceId => _comms.DeviceId;

        public string AppName => _comms.ClientInfo.AppName;

        public bool IsOnline => _comms.IsAvailable();

        private bool IsSiteTZEnabled => _metadata.IsSiteTimezoneEnabled();

        public async Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken)
        {
            if (!IsOnline)
            {
                return EntityQueryResult.Offline;
            }

            query = query.Clone();

            query.DataSource.EntitySetName = query.DataSource.EntitySetName ?? query.EntitySetName;

            if (query.DataSource.EntitySetName == null)
            {
                query.DataSource.EntitySetName = _metadata.GetFirstEntitySet(query.DataSource.ProjectionName, query.DataSource.EntityName);
            }

            string name = "GetRecords<" + query.DataSource.ProjectionName + "." + (query.DataSource.EntitySetName ?? query.DataSource.EntityName) + ">";
            using (_perfLogger.Track(nameof(OnlineDataHandler), name))
            {
                EntityResource resource = new EntityResource();
                resource.Query = query;
                resource.DeviceId = _comms.DeviceId;
                resource.ClientKeysMapper = _clientKeysMapper;

                if (_metadata.GetEntityTimeZoneType(query.DataSource.ProjectionName, query.DataSource.EntityName) == TimezoneRefType.Site && !IsSiteTZEnabled)
                {
                    resource.TimeZoneAwareResource = false;
                }
                else
                {
                    resource.TimeZoneAwareResource = _metadata.IsEntityKnownTimeZone(query.DataSource.EntityName, query.DataSource.ProjectionName);
                }

                try
                {
                    return (EntityQueryResult)await _comms.DownloadCustomResourceAsync(resource, 0, 0, cancelToken);
                }
                catch (CloudException ex) when (ex.InnerException is OperationCanceledException)
                {
                    throw ex.InnerException;
                }
            }
        }

        public async Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken, string entitySetName = "")
        {
            async Task<ExecuteResult> Impl()
            {
                EntityUpdateResource resource = new EntityUpdateResource();
                resource.DeviceId = _comms.DeviceId;
                resource.Projection = projectionName;

                if (_metadata.GetEntityTimeZoneType(projectionName, entityName) == TimezoneRefType.Site && !IsSiteTZEnabled)
                {
                    resource.TimeZoneAwareResource = false;
                }
                else
                {
                    resource.TimeZoneAwareResource = _metadata.IsEntityKnownTimeZone(entityName, projectionName);
                }

                resource.EntityName = entityName;
                resource.EntitySetName = entitySetName;
                resource.MetaTable = _metadata.GetTableForEntityName(entityName);
                resource.ClientKeysMapper = _clientKeysMapper;
                resource.CrudOperation = CrudOperation.Prepare;
                return (ExecuteResult)await _comms.DownloadCustomResourceAsync<EntityUpdateResource>(resource, 0, 0, cancelToken);
            }

            return await Execute("EntityPrepare<" + projectionName + "." + entityName + ">", Impl);
        }

        public async Task<ExecuteResult> EntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken)
        {
            string entityName = RemoteNaming.ToEntityName(row.TableName);
            async Task<ExecuteResult> Impl()
            {
                IMetaTable table = _metadata.MetaModel.GetTable(row.TableName);
                TimezoneRefType tzRefType = _metadata.GetEntityTimeZoneType(projectionName, entityName);
                string siteColumnName = _metadata.GetEntityTimeZoneColumn(entityName, projectionName);
                EntityUpdateResource resource = new EntityUpdateResource();
                resource.DeviceId = _comms.DeviceId;
                resource.Projection = projectionName;

                if (tzRefType == TimezoneRefType.Site && !IsSiteTZEnabled)
                {
                    resource.TimeZoneAwareResource = false;
                }
                else
                {
                    resource.TimeZoneAwareResource = _metadata.IsEntityKnownTimeZone(entityName, projectionName);
                }

                resource.EntityName = entityName;
                resource.EntitySetName = row.EntitySetName;
                resource.MetaTable = table;
                resource.ClientKeysMapper = _clientKeysMapper;
                resource.CrudOperation = CrudOperation.Insert;
                resource.Data = MessageUtils.RowToJObject(table, row, false, null, _comms.IsServerTimeZoneAware, tzRefType, siteColumnName, IsSiteTZEnabled);
                ExecuteResult result = (ExecuteResult)await _comms.PostCustomResourceAsync(resource, 0, 0, cancelToken);

                if (!result.Failed)
                {
                    FireDataChangeEventForEntity(_metadata.FindEntity(projectionName, entityName));
                }

                return result;
            }

            return await Execute("EntityInsert<" + projectionName + "." + entityName + ">", Impl);
        }

        public async Task<ExecuteResult> EntityUpdateAsync(string projectionName, RemoteRow row, IEnumerable<string> changedMembers, CancellationToken cancelToken)
        {
            string entityName = RemoteNaming.ToEntityName(row.TableName);
            async Task<ExecuteResult> Impl()
            {
                IMetaTable table = _metadata.MetaModel.GetTable(row.TableName);
                TimezoneRefType tzRefType = _metadata.GetEntityTimeZoneType(projectionName, entityName);
                string siteColumnName = _metadata.GetEntityTimeZoneColumn(entityName, projectionName);
                EntityUpdateResource resource = new EntityUpdateResource();
                resource.DeviceId = _comms.DeviceId;

                if (tzRefType == TimezoneRefType.Site && !IsSiteTZEnabled)
                {
                    resource.TimeZoneAwareResource = false;
                }
                else
                {
                    resource.TimeZoneAwareResource = _metadata.IsEntityKnownTimeZone(RemoteNaming.ToEntityName(row.TableName), projectionName);
                }

                resource.Projection = projectionName;
                resource.EntityName = entityName;
                resource.EntitySetName = row.EntitySetName;
                resource.PrimaryKeyString = row.PrimaryKeyString;
                resource.MetaTable = table;
                resource.ClientKeysMapper = _clientKeysMapper;
                resource.ExistingRow = row;
                resource.CrudOperation = CrudOperation.Update;
                resource.Data = MessageUtils.RowToJObject(table, row, true, x => changedMembers.Contains(x.PropertyName), _comms.IsServerTimeZoneAware, tzRefType, siteColumnName, IsSiteTZEnabled);
                ExecuteResult result = (ExecuteResult)await _comms.PatchCustomResourceAsync(resource, 0, 0, cancelToken, row.ETag);

                if (!result.Failed)
                {
                    FireDataChangeEventForEntity(_metadata.FindEntity(projectionName, entityName));
                }

                return result;
            }

            return await Execute("EntityUpdate<" + projectionName + "." + entityName + ">", Impl);
        }

        public async Task<ExecuteResult> EntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken)
        {
            string entityName = RemoteNaming.ToEntityName(row.TableName);

            async Task<ExecuteResult> Impl()
            {
                IMetaTable table = _metadata.MetaModel.GetTable(row.TableName);
                EntityUpdateResource resource = new EntityUpdateResource();
                resource.DeviceId = _comms.DeviceId;
                resource.Projection = projectionName;
                resource.EntitySetName = row.EntitySetName;
                resource.PrimaryKeyString = row.PrimaryKeyString;
                resource.EntityName = entityName;
                resource.MetaTable = table;
                resource.ClientKeysMapper = _clientKeysMapper;
                resource.CrudOperation = CrudOperation.Delete;
                resource.Data = MessageUtils.RowToJObject(table, row, false, x => x.ServerPrimaryKey);
                await _comms.DeleteCustomResourceAsync(resource, 0, row.ETag).ConfigureAwait(false);

                FireDataChangeEventForEntity(_metadata.FindEntity(projectionName, entityName));
                return new ExecuteResult(null);
            }

            return await Execute("EntityDelete<" + projectionName + "." + entityName + ">", Impl);
        }

        public async Task<ExecuteResult> CallActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            async Task<ExecuteResult> Impl()
            {
                ActionResource actionResource = new ActionResource();
                actionResource.DeviceId = _comms.DeviceId;

                if (_metadata.GetActionTimeZoneType(projectionName, name) == TimezoneRefType.Site && !IsSiteTZEnabled)
                {
                    actionResource.TimeZoneAwareResource = false;
                }
                else
                {
                    actionResource.TimeZoneAwareResource = _metadata.IsActionKnownTimeZone(projectionName, name);
                }

                actionResource.Projection = projectionName;
                actionResource.MethodName = name;
                actionResource.MethodType = MethodType.Action;
                actionResource.Metadata = _metadata;
                actionResource.ClientKeysMapper = _clientKeysMapper;
                actionResource.Data = MessageUtils.ParametersToJObject(parameters, false);
                actionResource.ReturnType = _metadata.FindAction(projectionName, name)?.ReturnType;
                actionResource.ValidateDateParameters(_metadata.FindAction(projectionName, name));

                ExecuteResult executeResult;
                CpiAction action = _metadata.FindAction(projectionName, name);

                //TERT-178 Lob Handling : Checking for Lob action parameters
                if ((action?.Parameters != null) && (action.Parameters.Any(x => x.DataType == CpiDataType.LongText || x.DataType == CpiDataType.Binary)))
                {
                    executeResult = await _comms.StreamLobActionCustomResourceAsync(actionResource, null, 0, 0, cancelToken);
                }
                else
                {
                    executeResult = (ExecuteResult)await _comms.PostCustomResourceAsync(actionResource, 0, 0, cancelToken);
                }

                if (!executeResult.Failed)
                {
                    FireDataChangeEventForCall(ActionInfo.Get(_metadata, projectionName, name));
                }

                return executeResult;
            }

            return await Execute("Action<" + projectionName + "." + name + ">", Impl);
        }

        public async Task<ExecuteResult> CallBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            string entityName = RemoteNaming.ToEntityName(row.TableName);

            async Task<ExecuteResult> Impl()
            {
                IMetaTable table = _metadata.MetaModel.GetTable(row.TableName);

                ActionResource resource = new ActionResource();
                resource.DeviceId = _comms.DeviceId;
                resource.Projection = projectionName;
                resource.MethodType = MethodType.BoundAction;
                resource.EntitySetName = row.EntitySetName;
                resource.PrimaryKeyString = row.PrimaryKeyString;
                resource.MethodName = entityName + "_" + name;
                resource.Metadata = _metadata;
                resource.ClientKeysMapper = _clientKeysMapper;
                resource.ReturnType = _metadata.FindBoundAction(projectionName, entityName, name)?.ReturnType;

                if (_metadata.GetEntityTimeZoneType(projectionName, entityName) == TimezoneRefType.Site && !IsSiteTZEnabled)
                {
                    resource.TimeZoneAwareResource = false;
                }
                else
                {
                    resource.TimeZoneAwareResource = _metadata.IsEntityKnownTimeZone(entityName, projectionName);
                }

                //JObject data = MessageUtils.RowToJObject(table, row, true,
                //    x => x.ServerPrimaryKey || x.PropertyName == nameof(row.ObjId) || x.PropertyName == nameof(row.ObjVersion));
                JObject data = new JObject();
                MessageUtils.AddParametersToJObject(parameters, data, false);
                resource.Data = data;
                CpiAction action = _metadata.FindBoundAction(projectionName, entityName, name);
                if (action != null)
                {
                    resource.ValidateDateParameters(action);
                }              
                ExecuteResult result = (ExecuteResult)await _comms.PostCustomResourceAsync(resource, 0, 0, cancelToken, row.ETag);

                if (!result.Failed)
                {
                    FireDataChangeEventForCall(ActionInfo.Get(_metadata, entityName, name));
                }

                return result;
            }

            return await Execute("Action<" + projectionName + "." + entityName + "_" + name + ">", Impl);
        }

        public async Task<EntityQueryResult> GetFunctionRecordsAsync(EntityQuery query, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            if (!IsOnline)
            {
                return EntityQueryResult.Offline;
            }

            FunctionDataSource funcDataSource = query.DataSource as FunctionDataSource;

            if (funcDataSource == null)
            {
                throw new InvalidOperationException("Cannot retrieve data for the function as the data source could not be determined.");
            }

            string name = "GetRecords<" + funcDataSource.ProjectionName + "." + funcDataSource.FunctionName + ">";
            using (_perfLogger.Track(nameof(OnlineDataHandler), name))
            {
                FunctionResource resource = CreateFunctionResource(funcDataSource.ProjectionName, funcDataSource.FunctionName, parameters, null, funcDataSource.EntityName);
                resource.Query = query;

                try
                {
                    return (EntityQueryResult)await _comms.DownloadCustomResourceAsync(resource, 0, 0, cancelToken);
                }
                catch (CloudException ex) when (ex.InnerException is OperationCanceledException)
                {
                    throw ex.InnerException;
                }
            }
        }

        public async Task<ExecuteResult> CallFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            async Task<ExecuteResult> Impl()
            {
                FunctionResource resource = CreateFunctionResource(projectionName, name, parameters);
                return (ExecuteResult)await _comms.DownloadCustomResourceAsync<FunctionResource>(resource, 0, 0, cancelToken);
            }

            return await Execute("Function<" + projectionName + "." + name + ">", Impl);
        }

        public async Task<ExecuteResult> CallBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            string entityName = RemoteNaming.ToEntityName(row.TableName);

            async Task<ExecuteResult> Impl()
            {
                FunctionResource resource = CreateFunctionResource(projectionName, name, parameters, row);
                return (ExecuteResult)await _comms.DownloadCustomResourceAsync<FunctionResource>(resource, 0, 0, cancelToken);
            }

            return await Execute("Function<" + projectionName + "." + entityName + "_" + name + ">", Impl);
        }

        private FunctionResource CreateFunctionResource(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, RemoteRow row = null, string entity = null)
        {
            bool bound = row != null;
            string entityName = bound ? RemoteNaming.ToEntityName(row.TableName) : null;

            FunctionResource resource = new FunctionResource();
            resource.DeviceId = _comms.DeviceId;
            resource.MethodType = bound ? MethodType.BoundFunction : MethodType.Function;
            TimezoneRefType tzRefType = TimezoneRefType.Server;
            string siteColumnName = string.Empty;

            if (bound)
            {
                tzRefType = _metadata.GetEntityTimeZoneType(projectionName, RemoteNaming.ToEntityName(row.TableName));
                resource.EntitySetName = row.EntitySetName;
                resource.PrimaryKeyString = row.PrimaryKeyString;
                resource.TimeZoneAwareResource = _metadata.IsEntityKnownTimeZone(RemoteNaming.ToEntityName(row.TableName), projectionName);
            }
            else
            {
                tzRefType = _metadata.GetFunctionTimeZoneType(projectionName, name, entity);
                resource.TimeZoneAwareResource = _metadata.IsFunctionKnownTimeZone(projectionName, name, entity);
            }

            if (tzRefType == TimezoneRefType.Site && !IsSiteTZEnabled)
            {
                resource.TimeZoneAwareResource = false;
            }

            resource.Projection = projectionName;
            resource.MethodName = bound ? (entityName + "_" + name) : name;
            resource.Metadata = _metadata;
            resource.ClientKeysMapper = _clientKeysMapper;
            resource.ReturnType = bound ? _metadata.FindBoundFunction(projectionName, entityName, name)?.ReturnType :
                                          _metadata.FindFunction(projectionName, name)?.ReturnType;
            resource.Data = MessageUtils.ParametersToJObject(parameters, true);

            return resource;
        }

        private async Task<ExecuteResult> Execute(string name, Func<Task<ExecuteResult>> execute)
        {
            if (!IsOnline)
            {
                return ExecuteResult.Offline;
            }

            using (_perfLogger.Track(nameof(OnlineDataHandler), name))
            {
                try
                {
                    return await execute();
                }
                catch (CloudException ex) when (ex.InnerException is OperationCanceledException)
                {
                    return new ExecuteResult(ex.InnerException);
                }
                catch (Exception ex)
                {
                    return new ExecuteResult(ex);
                }
            }
        }

        private void FireDataChangeEventForEntity(CpiEntity entity)
        {
            if (entity != null)
            {
                DataChangeSet changeSet = new DataChangeSet();
                IMetaTable table = _metadata.GetTableForEntityName(entity.Name);
                if (table != null)
                {
                    changeSet.AddTable(table);
                }

                if (entity.LuDependencies != null)
                {
                    foreach (string entityName in entity.LuDependencies)
                    {
                        IMetaTable luTable = _metadata.GetTableForEntityName(entityName);
                        if (luTable != null)
                        {
                            changeSet.AddTable(luTable);
                        }
                    }
                }

                FireDataChangeEvent(changeSet);
            }
        }

        private void FireDataChangeEventForCall(CallInfo callInfo)
        {
            if (callInfo != null)
            {
                FireDataChangeEvent(callInfo.GetExpectedChangeSet());
            }
        }

        private void FireDataChangeEvent(DataChangeSet changeSet)
        {
            if (!changeSet.IsEmpty)
            {
                try
                {
                    DataChangedEventArgs args = new DataChangedEventArgs(changeSet, DataChangedCauses.Online);
                    _eventAggregator.GetEvent<DataChangedEvent>().Publish(args);
                }
                catch (Exception ex)
                {
                    _logger.HandleException(ExceptionType.Recoverable, ex);
                }
            }
        }
    }
}
