﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Images;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Navigation
{
    public class AppMenuItem : ObservableBase
    {
        public bool IsDynamic { get; set; }
        public int? ItemOrder { get; set; }

        public string ProjectionName { get; set; }
        public string CountFunction { get; set; }
        public string CountEntitySet { get; set; }
        public string ShowIn { get; set; }

        public string VisibleFunction { get; set; }

        private string _label;
        public string Label
        {
            get { return _label; }
            set { SetProperty(ref _label, value); }
        }

        private string _subText;
        public string SubText
        {
            get { return _subText; }
            set { SetProperty(ref _subText, value); }
        }

        private NavigationLocation _location;
        public NavigationLocation Location
        {
            get { return _location; }
            set { SetProperty(ref _location, value); }
        }

        private NavigationParameter _navParam;
        public NavigationParameter NavParam
        {
            get { return _navParam; }
            set { SetProperty(ref _navParam, value); }
        }

        private bool _isSelected;
        public bool IsSelected
        {
            get { return _isSelected; }
            set { SetProperty(ref _isSelected, value); }
        }

        private UmaImage _image;
        public UmaImage Image
        {
            get { return _image; }
            set { SetProperty(ref _image, value); }
        }

        private UmaColor _color;
        public UmaColor Color
        {
            get => _color;
            set => SetProperty(ref _color, value);
        }

        private bool _isHomeItem;
        public bool IsHomeItem
        {
            get { return _isHomeItem; }
            set { SetProperty(ref _isHomeItem, value); }
        }

        public string DashboardItemAccessibilityId { get; } = AutomationIdentifiers.DashboardMenuItem;

        public AppMenuItem()
        {
            Color = UmaColors.IfsPurple;
        }

        public virtual Task<bool> NavigateToAsync(INavigator navigator)
        {
            if (Location == FrameworkLocations.SyncMonitor)
            {
                return navigator.NavigateFromRootAsync(Location, NavParam);
            }
            else if (Location == FrameworkLocations.External)
            {
                ExternalNavParam navParam = NavParam as ExternalNavParam;
                IsSelected = false;
                return navigator.NavigateToUrlAsync(navParam?.Url);
            }
            else if (!IsHomeItem)
            {
                return navigator.NavigateFromRootAsync(Location, NavParam);
            }
            else
            {
                return navigator.NavigateToRootAsync(Location, NavParam);
            }
        }

        public async Task UpdateCounter(IMetadata metadata, IDataHandler dataHandler, DataChangeSet changeSet, CancellationToken cancellationToken)
        {
            if (metadata == null) throw new ArgumentNullException(nameof(metadata));
            if (dataHandler == null) throw new ArgumentNullException(nameof(dataHandler));
            
            try
            {
                if (!string.IsNullOrEmpty(CountFunction))
                {
                    await UpdateCounterUsingFunction(metadata, dataHandler, changeSet, cancellationToken);
                }
                else if (!string.IsNullOrEmpty(CountEntitySet))
                {
                    await UpdateCounterUsingEntitySet(metadata, dataHandler, changeSet);
                }
                else
                {
                    SubText = null;
                }
            }
            catch (Exception ex)
            {
                Logger.Current.HandleException(Utility.ExceptionType.Recoverable, ex);
                SubText = null;
            }
        }

        private async Task UpdateCounterUsingFunction(IMetadata metadata, IDataHandler dataHandler, DataChangeSet changeSet, CancellationToken cancellationToken)
        {
            FunctionInfo function = FunctionInfo.Get(metadata, ProjectionName, CountFunction);

            if (function == null)
            {
                return;
            }

            if (changeSet != null && !function.AreResultsEffectedByChangeSet(changeSet))
            {
                return;
            }

            SubText = null;
            ExecuteResult result = await dataHandler.PerformFunctionAsync(ProjectionName, CountFunction, null, cancellationToken);
            SubText = result?.Value?.ToString();
        }
        
        private async Task UpdateCounterUsingEntitySet(IMetadata metadata, IDataHandler dataHandler, DataChangeSet changeSet)
        {
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, ProjectionName, CountEntitySet);

            if (source == null || (changeSet != null && !source.IsEffectedByChangeSet(changeSet)))
            {
                return;
            }

            AggregateQuery aggregate = new AggregateQuery(source, AppData.Database.AggregateType.Count, null);

            SubText = null;
            ExecuteResult result = await dataHandler.GetAggregateAsync(aggregate);
            SubText = result?.Value?.ToString();
        }
    }
}
