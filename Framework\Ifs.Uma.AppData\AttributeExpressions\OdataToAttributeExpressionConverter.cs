﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.AttributeExpressions
{
    public static class OdataToAttributeExpressionConverter
    {
        #region private const variables
        /*
         * Please exercise caution when modifying the values of the const variables.
         * These names are case-sensitive and correspond to specific comparison operations. 
         * when adding new operators, please make sure that you are adding it to the correct category.
         */
        #region functions
        private const string _StartsWith = "startswith";
        private const string _EndsWith = "endswith";
        private const string _Contains = "contains";
        #endregion

        #region operators
        private const string _NotEquals = "ne";
        private const string _Equals = "eq";
        #endregion

        #region patterns
        // funcname(col_name, value)
        // startswith(CompanyName22,'Alfr'), endswith(CompanyName22,'Futterkiste')
        // This patter is used to extract the function name, column name and value from the filter expression
        private const string _PatternForFunctionExpression = @"(\w+)\(([^,]+),\s*'([^']+)'\)";

        // col operator value
        // Name ne 'Milk', Food eq Milk
        // This patter is used to extract the column name, operator and value from the filter expression
        private const string _PatternForComparisonExpression = @"(\w+)\s+(\w+)\s+(\S+)";

        // This pattern is used to identify compound expressions
        private const string _PatternForCompoundExpression = @"\s+(and|or|AND|OR)\s+";
        #endregion

        #endregion

        #region methods
        public static AttributeExpression GetFilterExpression(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                Logger.Current.Error("Invalid Odata expression");
                return null;
            }

            if (Regex.IsMatch(value, _PatternForCompoundExpression, RegexOptions.None, TimeSpan.FromMilliseconds(10)))
            {
                return GetCompoundExpression(value);
            }
            else if (Regex.IsMatch(value, _PatternForFunctionExpression, RegexOptions.None, TimeSpan.FromMilliseconds(10)))
            {
                return GetLikeExpression(value);
            }
            else if (Regex.IsMatch(value, _PatternForComparisonExpression, RegexOptions.None, TimeSpan.FromMilliseconds(10)))
            {
                return GetCompareExpression(value);
            }
            else
            {
                Logger.Current.Error("Invalid Odata expression check the format of {0}", value);
                return null;
            }
        }

        private static AttributeExpression GetLikeExpression(string value)
        {
            GroupCollection expression = GetExpressionAsGroupCollection(value, _PatternForFunctionExpression);

            FilterInfo filterInfo = new FilterInfo
            {
                ComparisonType = expression[1].Value,
                ColunmName = expression[2].Value,
                Value = expression[3].Value
            };

            AttributeExpression attrExpression = null;

            switch (filterInfo.ComparisonType)
            {
                case _StartsWith:
                    attrExpression = AttributeExpression.Like(filterInfo.ColunmName, filterInfo.Value + "%");
                    break;
                case _EndsWith:
                    attrExpression = AttributeExpression.Like(filterInfo.ColunmName, "%" + filterInfo.Value);
                    break;
                case _Contains:
                    attrExpression = AttributeExpression.Like(filterInfo.ColunmName, "%" + filterInfo.Value + "%");
                    break;
                default:
                    Logger.Current.Error("Invalid comparison type {0}", filterInfo.ComparisonType);
                    break;
            }

            return attrExpression;
        }

        private static AttributeExpression GetCompareExpression(string value)
        {
            GroupCollection expression = GetExpressionAsGroupCollection(value, _PatternForComparisonExpression);

            FilterInfo filterInfo = new FilterInfo
            {
                ColunmName = expression[1].Value,
                ComparisonType = expression[2].Value,
                Value = expression[3].Value.Contains("'") ? expression[3].Value.Replace("'", string.Empty) : expression[3].Value
            };

            AttributeExpression attrExpression = null;

            switch (filterInfo.ComparisonType)
            {
                case _NotEquals:
                    attrExpression = AttributeExpression.Compare(filterInfo.ColunmName, AttributeCompareOperator.NotEquals, filterInfo.Value);
                    break;
                case _Equals:
                    attrExpression = AttributeExpression.Compare(filterInfo.ColunmName, AttributeCompareOperator.Equals, filterInfo.Value);
                    break;
                default:
                    Logger.Current.Error("Invalid comparison type {0}", filterInfo.ComparisonType);
                    break;
            }

            return attrExpression;
        }

        private static AttributeExpression GetCompoundExpression(string value)
        {
            CompoundExpressionInfo compoundExpressionInfo = GetCompoundExpressionInfo(value);

            if (compoundExpressionInfo.Conditions.Count != compoundExpressionInfo.Separators.Count + 1)
            {
                return null;
            }

            List<AttributeExpression> attributeExpressions = new List<AttributeExpression>();
            for (int i = 0; i < compoundExpressionInfo.Conditions.Count; i++)
            {
                attributeExpressions.Add(GetFilterExpression(compoundExpressionInfo.Conditions[i]));
            }

            AttributeExpression combinedExpression = attributeExpressions[0];
            for (int i = 0; i < compoundExpressionInfo.Separators.Count; i++)
            {
                if (compoundExpressionInfo.Separators[i].Equals("and", StringComparison.OrdinalIgnoreCase))
                {
                    combinedExpression = AttributeExpression.And(combinedExpression, attributeExpressions[i + 1]);
                }
                else if (compoundExpressionInfo.Separators[i].Equals("or", StringComparison.OrdinalIgnoreCase))
                {
                    combinedExpression = AttributeExpression.Or(combinedExpression, attributeExpressions[i + 1]);
                }
                else
                {
                    Logger.Current.Error("Invalid separator {0}", compoundExpressionInfo.Separators[i]);
                }
            }
            return combinedExpression;
        }

        private static CompoundExpressionInfo GetCompoundExpressionInfo(string value)
        {
            List<string> conditions = new List<string>();
            List<string> separators = new List<string>();

            MatchCollection matches = Regex.Matches(value, _PatternForCompoundExpression, RegexOptions.IgnoreCase);
            int startIndex = 0;

            foreach (Match match in matches)
            {
                int endIndex = match.Index;
                string condition = value.Substring(startIndex, endIndex - startIndex).Trim();

                if (Regex.IsMatch(condition, _PatternForFunctionExpression, RegexOptions.None, TimeSpan.FromMilliseconds(10)) || Regex.IsMatch(condition, _PatternForComparisonExpression, RegexOptions.None, TimeSpan.FromMilliseconds(10)))
                {
                    conditions.Add(condition);
                }
                separators.Add(match.Value.Trim());

                startIndex = endIndex + match.Length;
            }

            // Add the last condition after the final separator (if any)
            if (startIndex < value.Length)
            {
                if (Regex.IsMatch(value.Substring(startIndex).Trim(), _PatternForFunctionExpression, RegexOptions.None, TimeSpan.FromMilliseconds(10)) || Regex.IsMatch(value.Substring(startIndex).Trim(), _PatternForComparisonExpression, RegexOptions.None, TimeSpan.FromMilliseconds(10)))
                {
                    conditions.Add(value.Substring(startIndex).Trim());
                }
            }

            return new CompoundExpressionInfo() { Conditions = conditions, Separators = separators };
        }

        private static GroupCollection GetExpressionAsGroupCollection(string value, string regexPattern)
        {
            Regex regex = new Regex(regexPattern, RegexOptions.None, TimeSpan.FromMilliseconds(10));
            Match match = regex.Match(value);

            return match.Groups;
        }
    }
    #endregion

    internal class FilterInfo
    {
        public string ColunmName { get; set; }
        public string ComparisonType { get; set; }
        public string Value { get; set; }
    }

    internal class CompoundExpressionInfo
    {
        public List<string> Conditions { get; set; }
        public List<string> Separators { get; set; }
    }
}
