﻿using System;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class ObjPrimaryKeyTests : FrameworkTest
    {
        [Test]
        public void DateTimePrimaryKeyTests()
        {
            IMetadata metadata = Resolve<IMetadata>();

            RemoteRow absence = new RemoteRow("absence");
            absence["AccountDate"] = new DateTime(2021, 06, 28, 14, 22, 00);
            absence["AbsenceReason"] = "Vacation";

            ObjPrimaryKey absencePk = ObjPrimaryKey.FromPrimaryKey(metadata.MetaModel, absence);
            Assert.AreEqual("AccountDate=2021-06-28T14:22:00Z^", absencePk.ToFormattedKeyRef(TestOfflineProjection));

            RemoteRow timeReport = new RemoteRow("time_report");
            timeReport["Date"] = new DateTime(2021, 06, 28, 14, 22, 00);
            timeReport["Description"] = "Project Work";

            ObjPrimaryKey timeReportPk = ObjPrimaryKey.FromPrimaryKey(metadata.MetaModel, timeReport);
            Assert.AreEqual("Date=2021-06-28^", timeReportPk.ToFormattedKeyRef(TestOfflineProjection));

            RemoteRow appointment = new RemoteRow("appointment");
            appointment["Time"] = new DateTime(2021, 06, 28, 14, 22, 00);
            appointment["Description"] = "Private Appointment";

            ObjPrimaryKey appointmentPk = ObjPrimaryKey.FromPrimaryKey(metadata.MetaModel, appointment);
            Assert.AreEqual("Time=14:22:00^", appointmentPk.ToFormattedKeyRef(TestOfflineProjection));
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("ObjPrimaryKeySchema", null);
        }
    }
}
