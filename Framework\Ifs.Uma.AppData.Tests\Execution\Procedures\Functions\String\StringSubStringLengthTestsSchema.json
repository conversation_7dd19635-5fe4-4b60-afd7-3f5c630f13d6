{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_SubStringLength>": {"name": "String_SubStringLength", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "StartPos", "dataType": "Integer"}, {"name": "EndPos", "dataType": "Integer"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "SubStringLength", "paramsArray": ["${TextInput}", "${StartPos}", "${EndPos}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}