﻿using System;
using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Uma.AppData.Execution;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class MessageInResource : AppResource, ICustomResourceSerializer
    {
        public override string ResourceName => GetResourceUrl();
        public string ProjectionName { get; set; }
        public string EntitySetName { get; set; }
        public string ArraySource { get; set; }
        public long MessageId { get; set; }
        public string PrimaryKeyString { get; set; }
        public string ETagString { get; set; }
        public string ActionName { get; set; }

        public string Contents { get; set; }

        public object DeserializeJsonString(string jsonString)
        {
            return new MessageInResource() { Contents = jsonString };
        }

        public string SerializeToJsonString()
        {
            return Contents;
        }

        public object EmptyResponseHandling(bool isSuccess)
        {
            return isSuccess ? ExecuteResult.True : ExecuteResult.False;
        }

        private string GetResourceUrl()
        {
            if (string.IsNullOrEmpty(EntitySetName))
            {
                throw new InvalidOperationException("Cannot create URL without specifying entity set name");
            }

            string url = ProjectionName + ".svc\\" + EntitySetName;
            if (!string.IsNullOrEmpty(PrimaryKeyString))
            {
                PrimaryKeyString = PrimaryKeyString.Replace("^", ",");
                PrimaryKeyString = PrimaryKeyString.Replace("/", "%2F");
                PrimaryKeyString = PrimaryKeyString.Remove(PrimaryKeyString.Length - 1); 
                PrimaryKeyString = char.ToUpper(PrimaryKeyString[0]) + PrimaryKeyString.Substring(1);
                if (!string.IsNullOrEmpty(PrimaryKeyString))
                {
                    url = url + "(" + PrimaryKeyString + ")";
                }

                if (!string.IsNullOrEmpty(ArraySource))
                {
                    url += "\\" + ArraySource;
                }

                if (!string.IsNullOrEmpty(ActionName))
                {
                    url = url + "/" + ActionName;
                }
            }

            return url;
        }
    }
}
