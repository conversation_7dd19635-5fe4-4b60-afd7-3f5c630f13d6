﻿using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements
{
    public class StateIndicatorElement : ElementBase
    {
        private readonly IExpressionRunner _expressionRunner;
        private readonly IMetadata _metadata;

        protected override BindingType BindingPropertyType => BindingType.Reference;

        private StateIndicatorData _stateIndicator;
        public StateIndicatorData StateIndicator
        {
            get => _stateIndicator;
            protected set
            {
                if (_stateIndicator != null)
                {
                    _stateIndicator.PropertyChanged -= StateIndicator_PropertyChanged;
                }

                SetProperty(ref _stateIndicator, value);

                if (_stateIndicator != null)
                {
                    _stateIndicator.PropertyChanged += StateIndicator_PropertyChanged;
                }
            }
        }

        private CpiStateIndicator _cpiStateIndicator;

        public StateIndicatorElement(IMetadata metadata, IExpressionRunner expressionRunner)
        {
            _metadata = metadata;
            _expressionRunner = expressionRunner;
            HasHeader = false;

            StateIndicator = new StateIndicatorData();
        }

        protected override bool OnLoad()
        {
            return !string.IsNullOrWhiteSpace(Content?.StateIndicator);
        }

        protected override bool OnInitialize()
        {
            _cpiStateIndicator = _metadata.FindStateIndicator(ProjectionName, Content.StateIndicator);
            Label = _cpiStateIndicator?.Name;

            return _cpiStateIndicator != null;
        }

        protected override void OnDataChanged()
        {
            base.OnDataChanged();

            foreach (CpiState state in _cpiStateIndicator.States)
            {
                string emphasis = _expressionRunner.GetEmphasis(state.OfflineEmphasis ?? state.Emphasis, PageData.DefaultViewData);

                if (!string.IsNullOrEmpty(emphasis))
                {
                    StateIndicator.SelectedState = new State()
                    {
                        Completed = state.Completed,
                        Name = state.Name,
                        EmphasisColor = UmaColor.FromEmphasis(emphasis)
                    };

                    break;
                }
                else
                {
                    StateIndicator.SelectedState = null;
                }
            }
        }

        private void StateIndicator_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            SetIsCollapsed(StateIndicator.SelectedState == null);
        }
    }
}
