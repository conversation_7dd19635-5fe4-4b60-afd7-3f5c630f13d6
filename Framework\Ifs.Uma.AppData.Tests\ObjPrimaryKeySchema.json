{"name": "FndTstOffline", "component": "FNDTST", "version": "**********:**********", "projection": {"service": "FndTstOffline.svc", "version": "**********", "contains": {"Customers": {"name": "Absences", "entity": "Absence", "array": true}, "TimeReports": {"name": "TimeReports", "entity": "TimeReport", "array": true}, "Appointments": {"name": "Appointments", "entity": "Appointment", "array": true}}, "entities": {"Absence": {"name": "Absence", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "Absence", "ludependencies": ["Absence"], "keys": ["AccountDate"], "attributes": {"AccountDate": {"datatype": "Timestamp", "keygeneration": "User"}, "AbsenceReason": {"datatype": "Text"}}}, "TimeReport": {"name": "TimeReport", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TimeReport", "ludependencies": ["TimeReport"], "keys": ["Date"], "attributes": {"Date": {"datatype": "Date", "keygeneration": "User"}, "Description": {"datatype": "Text"}}}, "Appointment": {"name": "Appointment", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "Appointment", "ludependencies": ["Appointment"], "keys": ["Time"], "attributes": {"Time": {"datatype": "Time", "keygeneration": "User"}, "Description": {"datatype": "Text"}}}}}}