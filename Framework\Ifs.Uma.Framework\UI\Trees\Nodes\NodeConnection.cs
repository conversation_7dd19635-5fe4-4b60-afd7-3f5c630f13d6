﻿using System.Linq;
using Ifs.Uma.AppData;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.UI.Trees.Nodes
{
    public class NodeConnection
    {
        private readonly CpiTree _tree;

        public NodeConnection(IMetadata metadata, RecordData record, EntityDataSource dataSource, CpiTree tree, CpiTreeConnection cpiTreeConnection)
        {
            _tree = tree;
            CpiTreeConnection = cpiTreeConnection;
            Node = GetNode(cpiTreeConnection.Binding.Property);
            if (metadata != null && record != null)
            {
                EntityDataSource connectionDataSource = dataSource.SelectArray(record.GetRemoteRow(), cpiTreeConnection.Binding.BindName);

                if (connectionDataSource != null)
                {
                    Query = new EntityQuery(connectionDataSource);
                    Query.SelectAttributes = Nodes.Node.GetNodeAttributes(dataSource.ProjectionName, Node);
                    EntitySyncPolicy syncPolicy = metadata.GetEntitySyncPolicy(Query.DataSource.EntityName);
                    IsOnlineOnly = syncPolicy == EntitySyncPolicy.OnlineOnly;
                }
            }
        }

        public bool IsOnlineOnly { get; }

        public CpiTreeConnection CpiTreeConnection { get; }

        public EntityQuery Query { get; }

        public CpiTreeNode Node { get; }

        private CpiTreeNode GetNode(string name)
        {
            return _tree.Nodes?.Select(x => x.Node).Where(x => x.Name == name).FirstOrDefault();
        }
    }
}
