﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Ifs.Uma.Reporting.Report;
using Ifs.Uma.UI.Controls;
using static Ifs.Uma.UI.Fields.KnownDateTimeField;

namespace Ifs.Uma.Framework.Reporting.Generator
{
    public sealed class ListToTableConverter
    {
        public async Task GenerateForListAsync(ReportDoc report, CancellationToken cancelToken, ListElement element)
        {
            await LoadAllItems(element, cancelToken);

            ReportTableItem reportItem = new ReportTableItem();
            reportItem.Header = element.Header;

            CardDefItem[] cardDefItems = element.ListData.ItemCardDef.Items.ToArray();
            List<ReportTableColumn> hashFields = new List<ReportTableColumn>();
            List<ReportTableColumn> tempColumns = new List<ReportTableColumn>();
            CommandBlock commands = element.ListData.ItemCardDef?.CreateCommandBlock();

            foreach (ListElementItem listItem in element.ListData.Items)
            {
                foreach (CardDefItem cardDefItem in cardDefItems)
                {
                    cardDefItem.UpdateIsVisible(listItem);

                    ReportTableColumn column = new ReportTableColumn();
                    column.Name = cardDefItem.Name;
                    var col = hashFields.Find(item => item.Name == column.Name);

                    if (cardDefItem.IsVisible && col == null)
                    {
                        hashFields.Add(column);
                    }
                }
            }

            foreach (CardDefItem cardDefItem in cardDefItems)
            {
                ReportTableColumn column = new ReportTableColumn();
                column.Name = cardDefItem.Name;
                var col = hashFields.Find(item => item.Name == column.Name);

                if (cardDefItem.IsVisible && col != null)
                {
                    reportItem.Columns.Add(column);
                }
            }

            if (commands != null)
            {
                // Add a blank column to host the commands
                ReportTableColumn column = new ReportTableColumn();
                reportItem.Columns.Add(column);
            }

            if (element.HasCommands)
            {
                //Check for global commands
                ReportTableRow row = new ReportTableRow();
                CommandBlock globalcommands = element.CreateCommandBlock();

                string pk = element.Record?.ToPrimaryKey()?.ToKeyRef();
                if (globalcommands != null && !string.IsNullOrEmpty(pk))
                {
                    globalcommands.ViewData = element.ViewData;
                    globalcommands.PageData = element.PageData;

                    foreach (CommandGroup commandGroup in globalcommands.CommandGroups)
                    {
                        foreach (CommandItem command in commandGroup.Where(x => x.IsEnabled && x.IsVisible))
                        {
                            if (command is CpiCommandItem cpiCommand)
                            {
                                ReportTableValue columnValue = new ReportTableValue();
                                columnValue.CellValueType = ReportTableCellType.Command;
                                columnValue.CommandItem = new ReportCommandItem() { Name = cpiCommand.Name, Text = cpiCommand.Text, Data = pk };
                                row.Values.Add(columnValue);
                            }
                        }
                    }

                    row.Global = true;
                    reportItem.Rows.Add(row);
                }
            }

            foreach (ListElementItem listItem in element.ListData.Items)
            {
                ReportTableRow row = new ReportTableRow();
                tempColumns = hashFields.ToList();
                foreach (CardDefItem cardDefItem in cardDefItems)
                {
                    ReportTableValue columnValue = new ReportTableValue();
                    cardDefItem.UpdateIsVisible(listItem);
                    var col = tempColumns.FirstOrDefault(item => item.Name == cardDefItem.Name);
                    string displayValue = null;
                    if (cardDefItem.ControlType == CardDefItem.ControlTypes.HeaderLabel)
                    {
                        columnValue.CellValueType = ReportTableCellType.Header;
                        displayValue = element.ListData.ItemCardDef.GetHeaderText(listItem);
                    }
                    else if (cardDefItem.ControlType == CardDefItem.ControlTypes.Badge)
                    {
                        columnValue.CellValueType = ReportTableCellType.Badge;
                        Badge data = cardDefItem.GetBadge(listItem);
                        if (data != null)
                        {
                            ReportBadgeItem reportBadgeItem = new ReportBadgeItem()
                            {
                                ForegroundColor = data.ForegroundColor,
                                BackgroundColor = data.BackgroundColor,
                                Text = data.Text
                            };
                            columnValue.BadgeItem = reportBadgeItem;
                            displayValue = data.Text;
                        }
                    }
                    else if (cardDefItem.ControlType == CardDefItem.ControlTypes.Image)
                    {
                        columnValue.CellValueType = ReportTableCellType.Image;
                        byte[] imageData = await element.ListData.ItemCardDef.GetCardImageBytes(listItem, cancelToken);
                        if (imageData != null)
                        {
                            displayValue = Convert.ToBase64String(imageData);
                        }
                    }
                    else if (cardDefItem.ControlType == CardDefItem.ControlTypes.Markdown)
                    {
                        columnValue.CellValueType = ReportTableCellType.Markdown;

                        MarkdownData data = cardDefItem.GetMarkdownData(listItem);

                        if (data != null)
                        {
                            ReportMarkdownItem markdownItem = new ReportMarkdownItem()
                            {
                                Header = cardDefItem.Label,
                                MarkdownText = data.Text,
                                TextColor = data.TextColor,
                                BackgroundColor = data.BackgroundColor
                            };

                            columnValue.MarkdownItem = markdownItem;
                            displayValue = data.Text;
                        }
                    }
                    else
                    {
                        displayValue = cardDefItem.GetDisplayValue(listItem);

                        if (cardDefItem.TimeStampIsInUtc && displayValue != string.Empty)
                        {
                            TimeZoneInformationAbbreviation timeZoneInformation = new TimeZoneInformationAbbreviation();
                            timeZoneInformation.PrimaryTimeZoneAbbreviation = cardDefItem.GetPrimaryTimeZoneAbbreviation(listItem);
                            timeZoneInformation.AdditionalTimeZones = cardDefItem.GetTimeZoneUtcInformationForDialog(listItem);
                            columnValue.TimeZoneInformation = timeZoneInformation;
                        }
                    }

                    if (col != null)
                    {
                        columnValue.DisplayValue = displayValue;
                        row.Values.Add(columnValue);
                        tempColumns.Remove(col);
                    }
                }

                // Only setting background color for now
                // In the future we must support highlighting fields (use ListElement.GetFieldsToApplyConditionalFormatting method) and foreground color
                row.BackgroundColor = element.GetConditionalFormatColor(listItem);

                if (commands != null)
                {
                    commands.ViewData = listItem;
                    commands.Load(element.ProjectionName, element.ListData.ItemCardDef.CommandGroups, false, false, false);

                    if (commands.CommandGroups.Any() == true)
                    {
                        foreach (CommandGroup commandGroup in commands.CommandGroups)
                        {
                            foreach (CommandItem command in commandGroup.Where(x => x.IsEnabled && x.IsVisible))
                            {
                                if (command is CpiCommandItem cpiCommand)
                                {
                                    ReportTableValue columnValue = new ReportTableValue();
                                    columnValue.CellValueType = ReportTableCellType.Command;
                                    columnValue.CommandItem = new ReportCommandItem() { Name = cpiCommand.Name, Text = cpiCommand.Text, Data = listItem.Record.ToPrimaryKey().ToKeyRef() };
                                    row.Values.Add(columnValue);
                                }
                            }
                        }
                    }
                }

                row.Global = false;
                reportItem.Rows.Add(row);
            }

            report.Items.Add(reportItem);
        }

        private static async Task LoadAllItems(ListElement element, CancellationToken cancelToken)
        {
            element.DisplayState = ElementDisplayState.FullScreen;

            CancellationTokenRegistration reg = cancelToken.Register(() => element.ListData.CancelUpdateAsync());
            using (reg)
            {
                await element.ListData.WaitForUpdateAsync();
                await element.ListData.UpdateAsync();

                while (element.ListData.HasMoreItems)
                {
                    cancelToken.ThrowIfCancellationRequested();

                    await element.ListData.ContinueUpdateAsync();
                }
            }
        }
    }
}
