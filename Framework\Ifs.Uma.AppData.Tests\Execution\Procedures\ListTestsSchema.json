{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}, "WorkOrders": {"name": "WorkOrders", "entity": "FndMotOffWorkOrder", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "CRUD": "Create,Read,Update,Delete", "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}, "FndMotOffWorkOrder": {"name": "FndMotOffWorkOrder", "hasETag": true, "hasKeys": true, "CRUD": "Create,Read,Update,Delete", "luname": "FndMotOffWorkOrder", "ludependencies": ["FndMotOffWorkOrder"], "keys": ["WoNo"], "attributes": {"WoNo": {"datatype": "Number", "keygeneration": "Server", "required": true, "editable": false, "updatable": false, "insertable": false, "unbound": false, "multiselect": false}, "Description": {"datatype": "Text", "size": 200, "keygeneration": "User", "required": true, "editable": true, "updatable": true, "insertable": true, "unbound": false, "multiselect": false}}}}, "procedures": {"Function<TestListAdd>": {"name": "TestListAdd", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subType": "TstCustomer", "collection": true}, {"name": "Customer", "dataType": "Structure", "subType": "TstCustomer", "collection": false}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CA"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"namespace": "List", "name": "Add", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CB"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"namespace": "List", "name": "Add", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<TestListRemove>": {"name": "TestList<PERSON><PERSON>ove", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subType": "TstCustomer", "collection": true}, {"name": "Customer", "dataType": "Structure", "subType": "TstCustomer", "collection": false}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CA"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CB"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "proc", "args": {"name": "Remove", "namespace": "List", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<TestListClear>": {"name": "TestListClear", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subType": "TstCustomer", "collection": true}, {"name": "Customer", "dataType": "Structure", "subType": "TstCustomer", "collection": false}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CA"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "proc", "args": {"name": "Clear", "namespace": "List", "paramsArray": ["${Result}"]}}}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<TestListCount>": {"name": "TestListCount", "type": "Function", "params": [], "layers": [{"vars": [{"name": "TheList", "dataType": "Structure", "subType": "TstCustomer", "collection": true}, {"name": "Customer", "dataType": "Structure", "subType": "TstCustomer", "collection": false}, {"name": "ListCount", "dataType": "Integer", "collection": false}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CA"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${TheList}", "${Customer}"]}}}, {"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${TheList}", "${Customer}"]}}}, {"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${TheList}", "${Customer}"]}}}, {"call": {"method": "proc", "args": {"name": "Count", "namespace": "List", "paramsArray": ["${TheList}"]}}, "assign": "ListCount"}, {"call": {"method": "return", "args": {"name": "ListCount"}}}]}]}, "Function<TestListGet>": {"name": "TestListGet", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subType": "TstCustomer", "collection": true}, {"name": "Customer", "dataType": "Structure", "subType": "TstCustomer", "collection": false}, {"name": "Customer1", "dataType": "Structure", "subType": "TstCustomer", "collection": false}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CA"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"namespace": "List", "name": "Add", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CB"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"namespace": "List", "name": "Add", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "proc", "args": {"name": "Get", "namespace": "List", "paramsArray": ["${Result}", 1]}}, "assign": "Customer1"}, {"call": {"method": "return", "args": {"name": "Customer1"}}}]}]}, "Function<TestListIndexOf>": {"name": "TestListIndexOf", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subType": "TstCustomer", "collection": true}, {"name": "Customer", "dataType": "Structure", "subType": "TstCustomer", "collection": false}, {"name": "ListIndex", "dataType": "Number", "collection": false}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CA"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"namespace": "List", "name": "Add", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CB"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"namespace": "List", "name": "Add", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "proc", "args": {"name": "IndexOf", "namespace": "List", "paramsArray": ["${Result}", "${Customer}"]}}, "assign": "ListIndex"}, {"call": {"method": "return", "args": {"name": "ListIndex"}}}]}]}, "Function<TestListIndexOfAdvanced1>": {"name": "TestListIndexOfAdvanced1", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subType": "FndMotOffWorkOrder", "collection": true}, {"name": "WorkOrder", "dataType": "Structure", "subType": "FndMotOffWorkOrder", "collection": false}, {"name": "WorkOrderToFind", "dataType": "Structure", "subType": "FndMotOffWorkOrder", "collection": false}, {"name": "ListIndex", "dataType": "Number", "collection": false}], "execute": [{"call": {"method": "for", "args": {"entity": "FndMotOffWorkOrder", "name": "WorkOrders"}}, "assign": "WorkOrder", "result": {"TRUE": [{"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${Result}", "${WorkOrder}"]}}}]}}, {"call": {"method": "fetch", "args": {"entity": "FndMotOffWorkOrder", "name": "WorkOrders", "where": {"==": [{"var": "WoNo"}, 3]}}}, "assign": "WorkOrderToFind"}, {"call": {"method": "proc", "args": {"name": "IndexOf", "namespace": "List", "paramsArray": ["${Result}", "${WorkOrderToFind}"]}}, "assign": "ListIndex"}, {"call": {"method": "return", "args": {"name": "ListIndex"}}}]}]}, "Function<TestListIndexOfAdvanced2>": {"name": "TestListIndexOfAdvanced2", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subType": "FndMotOffWorkOrder", "collection": true}, {"name": "WorkOrder", "dataType": "Structure", "subType": "FndMotOffWorkOrder", "collection": false}, {"name": "WorkOrderToFind", "dataType": "Structure", "subType": "FndMotOffWorkOrder", "collection": false}, {"name": "ListIndex", "dataType": "Number", "collection": false}], "execute": [{"call": {"method": "for", "args": {"entity": "FndMotOffWorkOrder", "name": "WorkOrders"}}, "assign": "WorkOrder", "result": {"TRUE": [{"call": {"method": "proc", "args": {"name": "Add", "namespace": "List", "paramsArray": ["${Result}", "${WorkOrder}"]}}}]}}, {"call": {"method": "create", "args": {"entity": "FndMotOffWorkOrder"}}, "assign": "WorkOrderToFind"}, {"call": {"method": "set", "args": {"value": 7}}, "assign": "WorkOrderToFind.WoNo"}, {"call": {"method": "proc", "args": {"name": "IndexOf", "namespace": "List", "paramsArray": ["${Result}", "${WorkOrderToFind}"]}}, "assign": "ListIndex"}, {"call": {"method": "return", "args": {"name": "ListIndex"}}}]}]}}}}