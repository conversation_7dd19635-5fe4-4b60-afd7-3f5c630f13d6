﻿using System;
using System.Collections.Generic;
using Windows.ApplicationModel.Background;
using Windows.Foundation;

namespace Ifs.Uma.BackgroundTasks
{
    public sealed class TimeZoneChangedBackgroundTask : IBackgroundTask
    {
        private const string TaskName = "TimeZoneChangedBackgroundTask";
        private const string TaskEntryPoint = "Ifs.Uma.BackgroundTasks.TimeZoneChangedBackgroundTask";

        public void Run(IBackgroundTaskInstance taskInstance) { }

        public static BackgroundTaskRegistration RegisterTask()
        {
            try
            {
                foreach (KeyValuePair<Guid, IBackgroundTaskRegistration> current in BackgroundTaskRegistration.AllTasks)
                {
                    if (current.Value.Name == TaskName)
                    {
                        current.Value.Unregister(true);
                        break;
                    }
                }

                IAsyncOperation<BackgroundAccessStatus> access = BackgroundExecutionManager.RequestAccessAsync();
                BackgroundTaskBuilder task = new BackgroundTaskBuilder
                {
                    Name = TaskName,
                    TaskEntryPoint = TaskEntryPoint
                };

                SystemTrigger trigger = new SystemTrigger(SystemTriggerType.TimeZoneChange, false);
                task.SetTrigger(trigger);
                return task.Register();
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}
