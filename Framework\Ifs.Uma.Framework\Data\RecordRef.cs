﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.Data
{
    public sealed class RecordRef : ObservableBase
    {
        public event EventHandler RecordLoaded;

        private readonly ILogger _logger;
        private readonly RecordData _record;
        private readonly IDataHandler _data;
        private readonly CancellingUpdater _loader;

        public string ProjectionName { get; set; }

        public string RefName { get; }

        private RemoteRow _row;
        public RemoteRow Row
        {
            get => _row;
            private set
            {
                if (SetProperty(ref _row, value))
                {
                    RecordLoaded?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        private bool _pauseUpdates;
        public bool PauseUpdates
        {
            get => _pauseUpdates;
            set
            {
                if (SetProperty(ref _pauseUpdates, value) && !value)
                {
                    UpdateRow();
                }
            }
        }

        private EntityDataSource _source;
        private CpiReference _ref;
        private ObjKey _key;
        private ObjKey _loadedKey;

        internal RecordRef(ILogger logger, RecordData record, IDataHandler data, string projectionName, string refName, bool pauseUpdates)
        {
            if (logger == null) throw new ArgumentNullException(nameof(logger));
            if (record == null) throw new ArgumentNullException(nameof(record));
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (refName == null) throw new ArgumentNullException(nameof(refName));

            _logger = logger;
            _record = record;
            _data = data;
            ProjectionName = projectionName;
            RefName = refName;
            _loader = new CancellingUpdater(LoadRecord);
            _pauseUpdates = pauseUpdates;

            _record.DataChanged += Record_DataChanged;

            if (!_pauseUpdates)
            {
                UpdateRow();
            }
        }

        private void Record_DataChanged(object sender, EventArgs e)
        {
            UpdateRow();
        }

        private void UpdateRow()
        {
            _ref = _record.Metadata.FindReference(_record.ProjectionName, _record.EntityName, RefName);
            if (_ref?.Mapping == null || _ref.Target == null) _ref = null;
            
            _source = _ref == null ? null : EntityDataSource.FromEntity(_record.Metadata, _record.ProjectionName, _ref.Target);

            ObjKey key = CreateKey();

            if (!EqualityComparer<ObjKey>.Default.Equals(key, _loadedKey))
            {
                _key = key;

                Task updateTask = _loader.UpdateAsync();
                _record.BackgroundTasks.Add(updateTask);
            }
        }

        public void ReloadRecord()
        {
            _loadedKey = null;
            UpdateRow();
        }

        public void LoadRecord(RemoteRow refRow)
        {
            Row = refRow;

            _ref = _record.Metadata.FindReference(_record.ProjectionName, _record.EntityName, RefName);
            if (_ref?.Mapping == null || _ref.Target == null) _ref = null;

            _source = _ref == null ? null : EntityDataSource.FromEntity(_record.Metadata, _record.ProjectionName, _ref.Target);

            _loadedKey = CreateKey();
        }

        private async Task LoadRecord(CancellationToken token)
        {
            if (PauseUpdates)
            {
                return;
            }

            try
            {
                ObjKey key = _key;

                if (Equals(_key, _loadedKey))
                {
                    return;
                }
                
                if (_key == null || _source == null)
                {
                    Row = null;
                    _loadedKey = null;
                    return;
                }

                Row = (await _data.GetRecordAsync(_source, key, token))?.Row;
                _loadedKey = key;
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private ObjKey CreateKey()
        {
            if (_source == null || _ref == null)
            {
                return null;
            }

            Dictionary<string, object> values = new Dictionary<string, object>();
            foreach (var mapping in _ref.Mapping)
            {
                object refValue;
                if (!_record.TryGetRecordValue(mapping.Key, out refValue))
                {
                    return null;
                }

                if (refValue == null)
                {
                    return null;
                }

                values[RemoteNaming.ToColumnName(mapping.Value)] = refValue;
            }

            return ObjKey.FromValues(_source.Table, values);
        }

        public Task WaitForLoadAsync()
        {
            return _loader.WaitAsync();
        }

        internal void Dispose()
        {
            _record.DataChanged -= Record_DataChanged;
        }
    }
}
