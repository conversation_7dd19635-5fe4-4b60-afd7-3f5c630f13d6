﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Ifs.Uma.Database.SQLite.CustomFunctions;
using Ifs.Uma.Utility;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Database.SQLite
{
    internal class SQLiteBuilder : SqlBuilder
    {
        internal static SqlBuilder Instance { get { return g_instance; } }

        protected SQLiteBuilder()
        {
        }

        private static SqlBuilder g_instance = new SQLiteBuilder();

        public override string DecorateName(string name)
        {
            return "\"" + name + "\"";
        }

        public override string DecorateTableName(string name)
        {
            return DecorateName(name);
        }

        public override string ZeroIfNullFunctionProlog { get { return "IFNULL("; } }
        public override string EmptyColumnName { get { return "0"; } }
        public override string ConcatOperator { get { return "||"; } }
        public override bool SupportsXor { get { return false; } }

        public override LimitPosition LimitPlacement { get { return LimitPosition.LimitFirstAtEnd; } }
        public override string LimitProlog { get { return " LIMIT "; } }
        public override string LimitEpilog { get { return string.Empty; } }
        public override string OffsetProlog { get { return " OFFSET "; } }
        public override string OffsetEpilog { get { return string.Empty; } }

        public override string IfsDateTimeAddYearsFunction { get { return DateTimeAddYears.FunctionName; } }
        public override string IfsDateTimeAddMonthsFunction { get { return DateTimeAddMonths.FunctionName; } }
        public override string IfsDateTimeAddDaysFunction { get { return DateTimeAddDays.FunctionName; } }
        public override string IfsDateTimeAddHoursFunction { get { return DateTimeAddHours.FunctionName; } }
        public override string IfsDateTimeAddMinutesFunction { get { return DateTimeAddMinutes.FunctionName; } }
        public override string IfsDateTimeAddSecondsFunction { get { return DateTimeAddSeconds.FunctionName; } }
        public override string IfsDateTimeDateFunction { get { return DateTimeDate.FunctionName; } }
        public override string IfsDateTimeTimeFunction { get { return DateTimeTime.FunctionName; } }
        public override string IfsDateTimeFormatFunction { get { return DateTimeFormat.FunctionName; } }

        // Conversion from unixSecs to .NET ticks
        // unixEpochTicks = 621355968000000000
        // ticksPerScond = 10000000
        // ticks = unixEpochTicks + (unixSecs * ticksPerScond)
        public override string IfsDateTimeNow => "(CAST((621355968000000000 + (strftime('%s','now', 'localtime') * 10000000)) AS INTEGER))";
        public override string IfsDateTimeUtcNow => "(CAST((621355968000000000 + (strftime('%s','now') * 10000000)) AS INTEGER))";
        public override string IfsDateTimeNowDate => "(CAST((621355968000000000 + (strftime('%s','now', 'localtime', 'start of day') * 10000000)) AS INTEGER))";
        
        protected override string PragmaTableInfo { get { return "PRAGMA table_info "; } }

        public override string ScopeIdentityCommand(IInsertBase spec)
        {
            return "SELECT last_insert_rowid()";
        }

        protected override string StatementTerminator { get { return string.Empty; } }

        public override string ScopeIdentityFuncName { get { return "last_insert_rowid()"; } }
        public override string RowsAffectedFuncName { get { return "last_rows_affected()"; } }

        public override bool HandleDeleteTop(IDeleteSpec spec, StringBuilder sb, IStatementInfo info)
        {
            bool result = false;
            if (spec != null && spec.Top > 0)
            {
                // use the special "rowid" column and a nested select to limit the rows to delete
                IDeleteSpec newSpec = SqlSpec.CreateDelete(spec.TableName, new IWhereElement[]
                    {
                        WhereElement.CreateNNested(EOperand.None, RowIdName, EComparisonMethod.Equals, 
                            SqlSpec.CreateSelect(RowIdSelectColumn, TableSpec.Create(spec.TableName), null,
                                spec.Where, null, OrderBy.Create(RowIdSortColumn, 0, spec.Top), false))
                    }, 0); // now Top is 0
                if (newSpec != null)
                {
                    newSpec.WriteSql(sb, info, this, SqlWriteMode.Statement);
                    result = true;
                }
            }
            return result;
        }

        private const string RowIdName = "rowid";
        private static readonly IEnumerable<ISelectColumnSpec> RowIdSelectColumn = new ISelectColumnSpec[] { ColumnSpec.CreateSelect(RowIdName) };
        private static readonly IEnumerable<ISortColumnSpec> RowIdSortColumn = new ISortColumnSpec[] { ColumnSpec.Create(RowIdName, ESortOrder.Ascending) };

        protected override string DropTableClause { get { return "DROP TABLE IF EXISTS "; } }
        protected override string DropViewClause { get { return "DROP VIEW IF EXISTS "; } }

        protected override void WriteColumnDefinition(StringBuilder sb, IMetaDataMember member)
        {
            base.WriteColumnDefinition(sb, member);
            if (sb != null && member != null)
            {
                if (member.AutoIncrement)
                {
                    sb.Append(" INTEGER PRIMARY KEY AUTOINCREMENT");
                }
                else
                {
                    switch (TypeHelper.ValidateDbType(TypeHelper.GetNonNullableType(member.ColumnType)))
                    {
                        case TypeCode.Boolean:
                        case TypeCode.DateTime:
                        case TypeCode.Int16:
                        case TypeCode.Int32:
                        case TypeCode.Int64:
                            sb.Append(" INTEGER");
                            break;
                        case TypeCode.ByteArray:
                            sb.Append(" BLOB");
                            break;
                        case TypeCode.Decimal:
                        case TypeCode.Double:
                            sb.Append(" DOUBLE");
                            break;
                        case TypeCode.Enumeration:
                        case TypeCode.Guid:
                        case TypeCode.String:
                            sb.Append(" TEXT");
                            break;
                        default:
                            throw new NotSupportedException(member.ColumnType.FullName + " not supported");
                    }
                    if (member.Mandatory)
                    {
                        sb.Append(" NOT NULL");
                    }
                }
            }
        }

        protected override void WriteTableConstraints(StringBuilder sb, IMetaTable table)
        {
            if (sb != null && table != null)
            {
                IMetaDataMember[] pk = table.PrimaryKey.ToArray();
                if (pk.Length > 1 || (pk.Length > 0 && !pk[0].AutoIncrement))
                {
                    sb.Append(",\r\n    PRIMARY KEY(");
                    this.WriteCommaSeparatedList(sb, null, SqlWriteMode.Column, pk);
                    sb.Append(")");
                }
            }
        }
    }
}
