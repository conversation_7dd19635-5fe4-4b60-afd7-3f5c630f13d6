using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Lists;
using Ifs.Uma.Utility;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.Framework.UI.Lookups
{
    public class LovService : ILovService
    {
        private readonly IMetadata _metadata;
        private readonly IDataHandler _data;
        private readonly ILookupService _lookupService;
        private readonly IRoamingProfile _roamingProfile;
        private readonly ILogger _logger;
        private readonly ICardDefCreator _cardDefCreator;
        private IExpressionRunner _expressionRunner;
        private ViewData _viewData;

        public LovService(IMetadata metadata, IDataHandler data, ILookupService lookupService, IRoamingProfile roamingProfile,
           ILogger logger, ICardDefCreator cardDefCreator, IExpressionRunner expressionRunner)
        {
            _metadata = metadata;
            _data = data;
            _lookupService = lookupService;
            _roamingProfile = roamingProfile;
            _logger = logger;
            _cardDefCreator = cardDefCreator;
            _expressionRunner = expressionRunner;
        }

        public async Task<bool> OpenLovAsync(LookupField field, CpiField cpifield, ViewData viewData)
        {
            if (field == null) throw new ArgumentNullException(nameof(field));
            if (cpifield == null) throw new ArgumentNullException(nameof(cpifield));
            if (cpifield.Lov == null) throw new ArgumentException($"{nameof(cpifield)}.{nameof(cpifield.Lov)} must have a value", nameof(cpifield.Lov));
            if (viewData == null) throw new ArgumentNullException(nameof(viewData));
            if (viewData.Record == null) throw new ArgumentException($"{nameof(viewData)}.{nameof(viewData.Record)} must have a value", nameof(viewData.Record));

            CpiSelector selector = _metadata.FindSelector(viewData.Record.ProjectionName, cpifield.Lov.Selector);
            if (selector == null) throw new InvalidMetadataException($"Unable to find the selector '{cpifield.Lov.Selector}' for LOV field '{cpifield.Label}'");

            EntityDataSource rds = await GetDataSource(cpifield, viewData.Record);
            if (rds == null) throw new InvalidMetadataException($"Unable to find the datasource for LOV field '{cpifield.Label}'");
            if (rds.Table == null) throw new InvalidMetadataException($"Unable to find a local table for LOV field '{cpifield.Label}' that refers to '{rds.EntityName}'");

            _viewData = viewData;
            HashSet<string> searchAttributes = GetSearchAttributes(cpifield, rds);

            EntityQuery query = new EntityQuery(rds);

            AddLovFilter(query, cpifield, viewData);

            LookupRequest<LovItem> request = PrepareLookupRequest(field.Name, query, cpifield, selector, searchAttributes);
            request.AllowUnknown = _expressionRunner.RunCheck(cpifield.OfflineFreeInput ?? cpifield.FreeInput, viewData, false);
            request.AllowNull = !field.IsRequired;
            request.IsOnDemandDownloadEnabled = cpifield.Lov.SearchOnline && _metadata.IsEntityOnDemandEnabled(rds.EntityName);
            request.EntitySetName = rds.EntitySetName;
            request.SelectedItemPredicate = GetSelectedItemPredicate(viewData.Record, cpifield.Update?.Item);

            LookupResult<LovItem> result = await _lookupService.OpenLookup(request);

            if (result.Canceled)
            {
                return false;
            }

            if (result.IsUnknown)
            {
                ApplyUnknownSelection(cpifield, viewData.Record, result.UnknownItem, cpifield.FreeInputField);
                return true;
            }

            if (request.IsOnDemandDownloadEnabled)
            {
                await Task.Delay(1000);

                if (DeviceInfo.OperatingSystem == OperatingSystem.iOS)
                {
                    await Task.Delay(2000);
                }
            }

            LovItem item = result.Item;
            ApplySelection(cpifield, viewData.Record, item);

            return true;
        }

        private Predicate<LovItem> GetSelectedItemPredicate(RecordData data, string refName)
        {
            CpiReference reference = refName == null ? null : _metadata.FindReference(data.ProjectionName, data.EntityName, refName);
            if (reference == null)
            {
                return _ => false;
            }

            ObjKey selectedKey = data.GetReferenceKey(reference, false);
            if (selectedKey == null)
            {
                // Only select None item
                return x => x == null;
            }

            return x =>
            {
                if (x == null)
                {
                    // None item always deselected
                    return false;
                }

                ObjKey thisKey = x.Record.GetReferenceKey(reference, true);
                return selectedKey.Equals(thisKey);
            };
        }

        private void AddLovFilter(EntityQuery query, CpiField cpiField, ViewData viewData)
        {
            if (cpiField.Lov.Filter != null)
            {
                CpiFilter[] filter = cpiField.Lov.Filter;

                foreach (CpiFilter currentFilter in filter)
                {
                    bool enabledValue = currentFilter.Enabled.QuickCheck(false);

                    // The first filter that is enabled and evaluates to true will be used others will be ignored
                    if (enabledValue && _expressionRunner.RunCheck(currentFilter.Case, viewData, false))
                    {
                        query.FilterExpression = OdataToAttributeExpressionConverter.GetFilterExpression(currentFilter.Value);
                        break;
                    }
                }
            }
        }

        private async Task<EntityDataSource> GetDataSource(CpiField cpifield, RecordData recordData)
        {
            if (!string.IsNullOrEmpty(cpifield.Lov.DatasourceFunction))
            {
                Dictionary<string, object> parameterValues = new Dictionary<string, object>();
                if (recordData != null )
                {
                    await recordData?.ExtractFunctionParametersAsync(cpifield?.Lov.DatasourceFunctionParams, parameterValues);
                }
                return FunctionDataSource.Create(_metadata, cpifield.Lov.DatasourceProjection, cpifield.Lov.DatasourceFunction, parameterValues);
            }

            if (!string.IsNullOrEmpty(cpifield.Lov.Datasource) && cpifield.Lov.Datasource.Contains(".svc/"))
            {
                string[] projectionAndDataSet = cpifield.Lov.Datasource.Split(new string[] { ".svc/" }, StringSplitOptions.None);
                return EntityDataSource.FromEntitySet(_metadata, projectionAndDataSet[0], projectionAndDataSet[1]);
            }

            return EntityDataSource.FromEntitySet(_metadata, cpifield.Lov.DatasourceProjection, cpifield.Lov.DatasourceEntitySet);
        }

        private HashSet<string> GetSearchAttributes(CpiField cpifield, EntityDataSource rds)
        {
            HashSet<string> searchAttributes = new HashSet<string>();
            if (cpifield.Lov.Search != null)
            {
                foreach (string attributeName in cpifield.Lov.Search)
                {
                    searchAttributes.Add(attributeName);
                }
            }

            CpiExpression hideKeyExpression = cpifield.View?.OfflineHideKey ?? cpifield.View?.HideKey;
            bool hideKey = hideKeyExpression?.QuickCheck() ?? false;
            if (!hideKey)
            {
                // Add the key that is updated by the field
                if (cpifield.Update?.Copy != null &&
                    cpifield.Update.Copy.TryGetValue(cpifield.Attribute, out string keyAttributeName) &&
                    _metadata.FindAttribute(rds.ProjectionName, rds.EntityName, keyAttributeName) != null)
                {
                    searchAttributes.Add(keyAttributeName);
                }
            }

            if (cpifield.View?.Description != null)
            {
                string desc = cpifield.View?.Description;
                string start = "${" + cpifield.Update?.Item + ".";
                if (!string.IsNullOrEmpty(desc) && desc.StartsWith(start) && desc.EndsWith("}"))
                {
                    string attributeName = desc.Substring(start.Length, desc.Length - start.Length - 1);
                    if (_metadata.FindAttribute(rds.ProjectionName, rds.EntityName, attributeName) != null)
                    {
                        searchAttributes.Add(attributeName);
                    }
                }
            }

            return searchAttributes;
        }

        private static void ApplySelection(CpiField cpifield, RecordData record, LovItem row)
        {
            if (cpifield.Update?.Copy != null)
            {
                foreach (KeyValuePair<string, string> kvp in cpifield.Update.Copy)
                {
                    record.Assign(kvp.Key, row?[kvp.Value]);
                }
            }
        }

        private static void ApplyUnknownSelection(CpiField cpifield, RecordData record, string unknownData, string freeInputField)
        {
            if (freeInputField != null)
            {
                record.Assign(freeInputField, unknownData);
            }
            else
            {
                if (cpifield.Update?.Copy != null)
                {
                    foreach (KeyValuePair<string, string> kvp in cpifield.Update.Copy)
                    {
                        record.Assign(kvp.Key, unknownData);
                    }
                }
            }
        }

        public async Task<ObjPrimaryKey> OpenLovAsync(EntityQuery entityQuery, CpiSelector selector, string title, ObjPrimaryKey selectedRecord)
        {
            if (entityQuery == null) throw new ArgumentNullException(nameof(entityQuery));
            if (selector == null) throw new ArgumentNullException(nameof(selector));

            LookupRequest<LovItem> request = PrepareLookupRequest(title, entityQuery, null, selector, null);

            request.SelectedItemPredicate = CreatedSelectedItemPredicate(selectedRecord);

            var result = await _lookupService.OpenLookup(request);

            return result?.Item?.Record?.ToPrimaryKey();
        }

        private static Predicate<LovItem> CreatedSelectedItemPredicate(ObjPrimaryKey selectedRecord)
        {
            return x =>
            {
                if (x?.Record == null)
                {
                    return selectedRecord == null;
                }

                if (selectedRecord == null)
                {
                    return x == null;
                }

                ObjPrimaryKey thisRecord = x?.Record.ToPrimaryKey();
                return thisRecord.Equals(selectedRecord);
            };
        }

        public async Task<bool> OpenLovAsync(LookupField field, CpiReference reference, RecordData data)
        {
            if (field == null) throw new ArgumentNullException(nameof(field));
            if (reference == null) throw new ArgumentNullException(nameof(reference));

            CpiSelector selector = _metadata.FindSelector(data.ProjectionName, "Reference" + reference.Target + "Selector");
            if (selector == null) throw new ArgumentException("Unable to find selector for reference", nameof(reference));

            EntityDataSource rds = EntityDataSource.FromEntity(_metadata, data.ProjectionName, reference.Target);
            EntityQuery query = new EntityQuery(rds);

            LookupRequest<LovItem> request = PrepareLookupRequest(field.Name, query, null, selector, null);
            request.AllowNull = !field.IsRequired;

            request.SelectedItemPredicate = new Predicate<LovItem>(x =>
            {
                if (field.BackingField.Value == null)
                {
                    return x == null;
                }
                else
                {
                    bool isSelected = false;
                    if (x != null)
                    {
                        object xValue = x?[reference.Mapping.Values.FirstOrDefault()];
                        isSelected = xValue?.Equals(field.BackingField.Value) == true;
                    }
                    return isSelected;
                }
            });

            LookupResult<LovItem> result = await _lookupService.OpenLookup(request);

            if (result.Canceled)
            {
                return false;
            }

            field.BackingField.Value = result.Item?[reference.Mapping.Values.FirstOrDefault()];
            return true;
        }

        private LookupRequest<LovItem> PrepareLookupRequest(string title, EntityQuery query, CpiField cpiField, CpiSelector selector, IEnumerable<string> customSearchAttributes)
        {
            EntityQuery baseQuery = query.Clone();
            HashSet<string> selectAttributes = new HashSet<string>();
            selectAttributes.Add(nameof(RemoteRow.ObjKey));
            AttributeFinder.FindInSelector(selectAttributes, _metadata, baseQuery.DataSource.ProjectionName, selector);

            // When "copy" definitions exist in the reference given in the projection, we must include them in the select attributes as well
            if (cpiField?.Update?.Copy != null)
            {
                foreach (KeyValuePair<string, string> kvp in cpiField.Update.Copy.Where(x => x.Value != null))
                {
                    selectAttributes.Add(kvp.Value);
                }
            }

            baseQuery.SelectAttributes = selectAttributes.ToArray();

            if (selector.OrderBy != null)
            {
                baseQuery.AddSorts(selector.OrderBy);
            }

            LookupRequest<LovItem> request = new LookupRequest<LovItem>(title);
            request.Settings = _roamingProfile.GetSettings("Lookups", selector.Name);
            request.CardDef = _cardDefCreator.CreateCardDef(baseQuery.DataSource.ProjectionName, selector, ValueProvider);
            LovDataProvider dataProvider = GetDataProvider(baseQuery, customSearchAttributes ?? selector.SelectAttributes, cpiField);
            request.DataProvider = dataProvider.RequestDataProvider;
            request.ItemRetriever = dataProvider.RequestItemRetriever;
            request.ItemIdRetriever = dataProvider.RequestItemIdRetriever;
            request.IsOnDemandSearchEnabled = cpiField != null && cpiField.Lov != null && cpiField.Lov.SearchOnline;
            request.SearchModeChanged = request.IsOnDemandSearchEnabled ? GetOnlineCommand((LovDataProviderDecorator)dataProvider) : null;
            request.SortOptions = GetSortOptions(selector);
            request.QuerySearchOnly = true;
            request.QueryOnSearch = true;
            request.QuickItemSettings = QuickItemSettings.Create(QuickItemOptions.FavoritesAndRecentlyUsed);
            return request;
        }

        private Command GetOnlineCommand(LovDataProviderDecorator lovDataProviderDecorator)
        {
            return Command.FromMethod(lovDataProviderDecorator.LovSearchModeChanged);
        }

        private object ValueProvider(AttributePath path, object item)
        {
            if (item is LovItem lovItem && lovItem.Record != null)
            {
                return lovItem.Record[path];
            }

            return null;
        }

        private LovDataProvider GetDataProvider(EntityQuery baseQuery, IEnumerable<string> searchAttributes, CpiField cpiField)
        {
            if (cpiField != null && cpiField.Lov != null && cpiField.Lov.SearchOnline)
            {
                if (baseQuery.DataSource is FunctionDataSource)
                {
                    Dictionary<string, object> parameterValues = new Dictionary<string, object>();
                    if (_viewData != null)
                    {
                        _viewData.Record?.ExtractFunctionParametersAsync(cpiField.Lov.OnlineDatasourceFunctionParams, parameterValues);
                    }

                    FunctionDataSource dataSource = FunctionDataSource.Create(_metadata, cpiField.Lov.DatasourceProjection, cpiField.Lov.OnlineDataSourceFunction, parameterValues);
                    return new FunctionDataProviderDecorator(_logger, _metadata, _data, baseQuery, searchAttributes, new FunctionDataProvider(_logger, _metadata, _data, baseQuery, searchAttributes), dataSource);
                }
                else
                {
                    return new EntityDataProviderDecorator(_logger, _metadata, _data, baseQuery, searchAttributes, new EntityDataProvider(_logger, _metadata, _data, baseQuery, searchAttributes));
                }
            }
            else
            {
                if (baseQuery.DataSource is FunctionDataSource)
                {
                    return new FunctionDataProvider(_logger, _metadata, _data, baseQuery, searchAttributes);
                }
                else
                {
                    return new EntityDataProvider(_logger, _metadata, _data, baseQuery, searchAttributes);
                }
            }
        }

        private static IEnumerable<ListSortOption> GetSortOptions(CpiSelector cpiSelector)
        {
            if (cpiSelector != null)
            {
                List<ListSortOption> sortOptions = new List<ListSortOption>();
                IEnumerable<CpiFieldContent> contentsWithLabels = cpiSelector.Content.Where(c => !string.IsNullOrWhiteSpace(c.Field.Label));

                // Add "None" option
                sortOptions.Add(ListSortOption.None);

                foreach (CpiFieldContent cpiFieldContent in contentsWithLabels)
                {
                    sortOptions.Add(new ListSortOption(cpiFieldContent.Field.Attribute, cpiFieldContent.Field.Label));
                }

                return sortOptions;
            }

            return null;
        }
    }
}
