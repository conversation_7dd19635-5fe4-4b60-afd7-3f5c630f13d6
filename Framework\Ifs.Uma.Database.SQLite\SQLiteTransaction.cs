﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database.SQLite
{
    internal class SQLiteTransaction : DbTransaction
    {
        internal SQLiteTransaction(SQLiteConnection parent, ILogger logger, bool traceFlag)
            : base(logger, traceFlag)
        {
            if (parent == null) throw new ArgumentNullException("parent");
            m_parent = parent;
        }

        private SQLiteConnection m_parent;
        private bool m_gotLock;

        public override DbConnection Connection { get { return m_parent; } }

        protected override void DoStart()
        {
            m_parent.EnterWriteLock();
            m_gotLock = true;
            using (DbCommand command = m_parent.CreateCommand(this))
            {
                // lock the database for write now
                command.CommandText = "BEGIN EXCLUSIVE TRANSACTION";
                command.ExecuteNonQuery();
            }
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
            if (disposing)
            {
                SQLiteConnection parent = Interlocked.Exchange(ref m_parent, null);
                if (parent != null)
                {
                    if (m_gotLock)
                    {
                        parent.ExitWriteLock();
                    }
                    parent.DisposeTransaction(this);
                }
            }
        }

        protected override void DoCommit()
        {
            using (DbCommand command = m_parent.CreateCommand(this))
            {
                command.CommandText = "COMMIT";
                command.ExecuteNonQuery();
            }
        }

        protected override void DoRollback()
        {
            using (DbCommand command = m_parent.CreateCommand(this))
            {
                command.CommandText = "ROLLBACK";
                command.ExecuteNonQuery();
            }
        }
    }
}
