﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Ifs.Uma.Utility;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Database
{
    public interface IStatementInfo
    {
        /// <summary>
        /// Gets Now with centisecond accuracy and Unspecified Kind
        /// The value can survive a database roundtrip
        /// All date times are assumed local time when stored in the database 
        /// so comparisons should be done against local time not UTC
        /// </summary>
        DateTime StatementNow { get; }
        IEnumerable<DbParameter> Parameters { get; }
        void WriteParameter(StringBuilder sb, Type parameterType, object value, int valueIndex);
    }

    public enum SqlWriteMode
    {
        Column,
        SelectColumn,
        SortColumn,
        ColumnValue,
        ColumnAssign,
        WhereElement,
        Statement
    }

    public enum LimitPosition
    {
        LimitOnlyAtStart,
        LimitFirstAtEnd,
        OffsetFirstAtEnd
    }

    public interface IWriteSql
    {
        void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode);
    }

    [Flags]
    public enum ParameterUsage
    {
        None = 0,
        Create = 1,
        Clear = 2,
        CreateNew = Create | Clear
    }

    [Flags]
    public enum DdlChoice
    {
        None = 0,
        DropTable = 1,
        CreateTable = 2,
        PrimaryIndex = 4,
        InsertData = 8,
        OtherIndex = 16,
        CreateScript = DropTable | CreateTable | PrimaryIndex | InsertData | OtherIndex
    }

    /// <summary>
    /// Although this class is abstract it is trivially wrapped and used to write "Generic" SQL statements
    /// for debug purposes.  So all strings need to be given a default of some flavour so that the
    /// debug string has more meaning.
    /// </summary>
    public abstract class SqlBuilder
    {
        /// <summary>
        /// Builds a SQL statement according to the provided spec and parameter flags
        /// </summary>
        /// <param name="command">Command to hold the statement and parameters</param>
        /// <param name="source">SQL statement definition (DBMS-independent)</param>
        /// <param name="usage">How parameters are to be treated</param>
        /// <returns>Parameters created during this build</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1011:ConsiderPassingBaseTypesAsParameters",
            Justification = "IWriteSql does not write a complete SQL statement")]
        public IEnumerable<DbParameter> BuildSql(DbCommand command, ISqlSpec source, ParameterUsage usage)
        {
            if (command == null) throw new ArgumentNullException("command");
            if (source == null) throw new ArgumentNullException("source");
            if (usage.IsSet(ParameterUsage.Clear))
            {
                command.Parameters.Clear();
            }
            command.CommandText = string.Empty;
            StatementInfo info = new StatementInfo(command, usage.IsSet(ParameterUsage.Create));
            StringBuilder sb = new StringBuilder();
            source.WriteSql(sb, info, this, SqlWriteMode.Statement);
            // change the command.
            command.CommandText = sb.ToString();
            // add the additional parameters
            foreach (DbParameter parameter in info.Parameters)
            {
                command.Parameters.Add(parameter);
            }
            return info.Parameters;
        }

        public IEnumerable<string> BuildDdl(IMetaModel metaModel, DdlChoice choice, MetaTableClass classification)
        {
            IEnumerable<IMetaTable> allTables = metaModel != null ? metaModel.GetTables() : Enumerable.Empty<IMetaTable>();
            IEnumerable<IMetaTable> tables = GetTables(classification, allTables);
            return BuildDdl(tables, choice);
        }

        public IEnumerable<string> BuildDdl(IEnumerable<IMetaTable> tables, DdlChoice choice)
        {
            List<string> result = new List<string>();
            if (tables != null)
            {
                StringBuilder sb = new StringBuilder();
                if (choice.IsSet(DdlChoice.DropTable))
                {
                    foreach (IMetaTable table in tables)
                    {
                        if (table.TableImplementation == TableImplementation.View)
                        {
                            WriteDropView(sb, table);
                            AddDdl(result, sb);
                        }
                        else
                        {
                            // Will also drop structures from < 10.5 installs
                            WriteDropTable(sb, table);
                            AddDdl(result, sb);
                        }
                    }
                }
                if (choice.IsSet(DdlChoice.CreateTable))
                {
                    foreach (IMetaTable table in tables)
                    {
                        if (table.TableImplementation == TableImplementation.View)
                        {
                            WriteCreateView(sb, table);
                            AddDdl(result, sb);
                        }
                        else if (table.TableImplementation == TableImplementation.Table)
                        {
                            WriteCreateTable(sb, table);
                            AddDdl(result, sb);
                        }
                    }
                }
                if (choice.IsSet(DdlChoice.PrimaryIndex))
                {
                    foreach (IMetaTable table in tables)
                    {
                        if (table.TableImplementation == TableImplementation.Table)
                        {
                            WritePrimaryIndex(sb, table);
                            AddDdl(result, sb);
                        }
                    }
                }
                if (choice.IsSet(DdlChoice.OtherIndex))
                {
                    foreach (IMetaTable table in tables)
                    {
                        if (table.TableImplementation == TableImplementation.Table)
                        {
                            result.AddRange(WriteTableIndexes(sb, table));
                        }
                    }
                }
            }
            return result;
        }

        public string BuildTableInfoDdl(IMetaTable table)
        {
            StringBuilder sb = new StringBuilder();
            WritePragmaTableInfo(sb, table);
            sb.Append(StatementTerminator);
            return sb.ToString();
        }

        public IEnumerable<string> BuildAddColumnDdl(IMetaTable table, IMetaDataMember member)
        {
            List<string> result = new List<string>();
            StringBuilder sb = new StringBuilder();
            WriteAddColumn(sb, table, member);
            AddDdl(result, sb);
            return result;
        }

        public static string BuildDebugGenericSql(IWriteSql writeSql)
        {
            StringBuilder sb = new StringBuilder();
            GenericSqlBuilder sqlBuilder = new GenericSqlBuilder();
            GenericStatementInfo si = new GenericStatementInfo();
            writeSql.WriteSql(sb, si, sqlBuilder, SqlWriteMode.Statement);

            string par = si.ParametersString;
            if (par.Length > 0)
            {
                sb.Append(" " + par);
            }

            return sb.ToString();
        }

        private sealed class GenericSqlBuilder : SqlBuilder
        {
        }

        private sealed class GenericStatementInfo : IStatementInfo
        {
            public DateTime StatementNow { get; } = new DateTime(2000, 1, 1, 12, 0, 0);
            public IEnumerable<DbParameter> Parameters => Enumerable.Empty<DbParameter>();

            private int _paramIndex;
            private readonly StringBuilder _params = new StringBuilder();

            public string ParametersString => _params.ToString();

            public void WriteParameter(StringBuilder sb, Type parameterType, object value, int valueIndex)
            {
                string param = "@p" + _paramIndex;
                string str = ObjectConverter.ToString(value);

                if (_params.Length > 0)
                {
                    _params.Append(", ");
                }

                _params.Append(param);
                _params.Append("=");
                _params.Append(str == null ? "null" : "'" + str + "'");

                sb.Append(param);

                _paramIndex++;
            }
        }

        #region SQL Building Blocks

        public virtual bool SupportsUpsert { get { return false; } }
        public virtual bool SupportsXor { get { { return true; } } }

        public virtual string DecorateTableName(string name) { return name; }
        public virtual string DecorateName(string name) { return name; }
        public virtual bool ValueIsNull(Type valueType, object value) { return value == null; }

        public virtual string ParameterProlog { get { return "@"; } }

        // JVB: I know a lot of these strings are very unlikely to change.
        // However, a const is accessed through its class name whereas a virtual
        // is accessed through an object - so in the interests of minimising
        // the change required when I need to switch from "const" to "virtual",
        // I just use virtual read-only strings.

        public virtual string SelectClause { get { return "SELECT "; } }
        public virtual string SelectDistinctClause { get { return "SELECT DISTINCT "; } }
        public virtual string InsertIntoClause { get { return "INSERT INTO "; } }
        public virtual string UpsertIntoClause { get { return "REPLACE INTO "; } }
        public virtual string DeleteFromClause { get { return "DELETE FROM "; } }
        public virtual string ColumnsProlog { get { return " ("; } }
        public virtual string ColumnsEpilog { get { return ")"; } }
        public virtual string ValuesProlog { get { return " VALUES ("; } }
        public virtual string ValuesEpilog { get { return ")"; } }
        public virtual string UpdateProlog { get { return "UPDATE "; } }
        public virtual string UpdateEpilog { get { return " SET "; } }

        public virtual string CommaSeparator { get { return ", "; } }
        public virtual string NameQualifier { get { return "."; } }

        public virtual string AscendingOrder { get { return " ASC"; } }
        public virtual string DescendingOrder { get { return " DESC"; } }

        public virtual string GroupByClause { get { return " GROUP BY "; } }
        public virtual string OrderByClause { get { return " ORDER BY "; } }

        public virtual string MaxFunctionProlog { get { return "MAX("; } }
        public virtual string MaxFunctionEpilog { get { return ")"; } }

        public virtual string MinFunctionProlog { get { return "MIN("; } }
        public virtual string MinFunctionEpilog { get { return ")"; } }

        public virtual string CountFunctionProlog { get { return "COUNT("; } }
        public virtual string CountFunctionEpilog { get { return ")"; } }

        public virtual string SumFunctionProlog { get { return "SUM("; } }
        public virtual string SumFunctionEpilog { get { return ")"; } }

        public virtual string AvgFunctionProlog { get { return "AVG("; } }
        public virtual string AvgFunctionEpilog { get { return ")"; } }

        public virtual string ZeroIfNullFunctionProlog { get { return "ISNULL("; } }
        public virtual string ZeroIfNullFunctionEpilog { get { return ",0)"; } }

        public virtual string IfsDateTimeAddYearsFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeAddMonthsFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeAddDaysFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeAddHoursFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeAddMinutesFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeAddSecondsFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeDateFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeTimeFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeFormatFunction => throw new NotImplementedException();
        public virtual string IfsDateTimeNow => throw new NotImplementedException();
        public virtual string IfsDateTimeUtcNow => throw new NotImplementedException();
        public virtual string IfsDateTimeNowDate => throw new NotImplementedException();
        
        public virtual string FunctionProlog { get { return "("; } }
        public virtual string FunctionEpilog { get { return ")"; } }

        public virtual string AsClause { get { return " AS "; } }

        public virtual string AssignOperator { get { return "="; } }

        public virtual string WhereClause { get { return " WHERE "; } }

        public virtual string LikeClause { get { return " LIKE "; } }

        public virtual string AndOperand { get { return " AND "; } }
        public virtual string OrOperand { get { return " OR "; } }
        public virtual string NoOperand { get { return " "; } }

        public virtual string OpenBracket { get { return "("; } }
        public virtual string CloseBracket { get { return ")"; } }

        public virtual string EqualOperator { get { return "="; } }
        public virtual string NotEqualOperator { get { return "<>"; } }
        public virtual string LessThanOperator { get { return "<"; } }
        public virtual string NotLessThanOperator { get { return ">="; } }
        public virtual string GreaterThanOperator { get { return ">"; } }
        public virtual string NotGreaterThanOperator { get { return "<="; } }
        public virtual string InOperator { get { return " IN "; } }
        public virtual string NotInOperator { get { return " NOT IN "; } }
        public virtual string IsNullOperator { get { return " IS NULL"; } }
        public virtual string IsNotNullOperator { get { return " IS NOT NULL"; } }
        public virtual string ExistsOperator { get { return " EXISTS "; } }
        public virtual string BetweenOperator { get { return " BETWEEN "; } }

        public virtual string AggregateDistinct { get { return "DISTINCT "; } }
        public virtual string AggregateAsterisk { get { return "*"; } }

        // Binary Operators
        public virtual string BitwiseAndOperator { get { return "&"; } }
        public virtual string BitwiseOrOperator { get { return "|"; } }
        public virtual string AddOperator { get { return "+"; } }
        public virtual string SubtractOperator { get { return "-"; } }
        public virtual string MultiplyOperator { get { return "*"; } }
        public virtual string DivideOperator { get { return "/"; } }
        public virtual string ModuloOperator { get { return "%"; } }
        public virtual string ExclusiveOrOperator { get { return "^"; } }
        public virtual string LeftShiftOperator { get { return "<<"; } }
        public virtual string RightShiftOperator { get { return ">>"; } }
        public virtual string ConcatOperator { get { return "+"; } } // sometimes "||"

        // Unary Operators
        public virtual string NegateOperator { get { return "-"; } }
        public virtual string PlusOperator { get { return "+"; } }
        public virtual string NotOperator { get { return " NOT "; } }
        public virtual string BitwiseNotOperator { get { return "~"; } }

        public virtual char MultipleWildCharacter { get { return '%'; } }
        public virtual char SingleWildCharacter { get { return '_'; } }
        public virtual string TrueLiteral { get { return "1"; } }
        public virtual string FalseLiteral { get { return "0"; } }
        public virtual string NullLiteral { get { return "NULL"; } }
        public virtual string EmptyColumnName { get { return "NULL"; } }

        public virtual string AliasSeparator { get { return " "; } } // sometimes " AS "

        public virtual string FromClause { get { return " FROM "; } }

        public virtual string UnionAll { get { return " UNION ALL "; } }

        public virtual string InnerJoinClause { get { return " INNER JOIN "; } }
        public virtual string LeftOuterJoinClause { get { return " LEFT OUTER JOIN "; } }
        public virtual string RightOuterJoinClause { get { return " RIGHT OUTER JOIN "; } }
        public virtual string FullOuterJoinClause { get { return " FULL OUTER JOIN "; } }

        public virtual string OnClause { get { return " ON "; } }

        public virtual LimitPosition LimitPlacement { get { return LimitPosition.OffsetFirstAtEnd; } }

        public virtual string OffsetProlog { get { return " OFFSET "; } }
        public virtual string OffsetEpilog { get { return " ROWS"; } }

        public virtual string LimitProlog { get { return " FETCH FIRST "; } }
        public virtual string LimitEpilog { get { return " ROWS ONLY"; } }

        public virtual string ScopeIdentityEpilog { get { return string.Empty; } }
        public virtual string ScopeIdentityCommand(IInsertBase spec) { return string.Empty; }
        public virtual string ScopeIdentityFuncName { get { return string.Empty; } }
        public virtual string RowsAffectedFuncName { get { return string.Empty; } }

        public virtual string DeleteProlog(int top)
        {
            return "DELETE FROM ";
        }

        public virtual string DeleteEpilog(int top)
        {
            return string.Empty;
        }

        public virtual bool HandleDeleteTop(IDeleteSpec spec, StringBuilder sb, IStatementInfo info) { return false; }

        #endregion

        #region DDL Building Blocks

        protected virtual string StatementTerminator { get { return ";\r\n\r\n"; } }
        protected virtual string DropTableClause { get { return "DROP TABLE "; } }
        protected virtual string DropViewClause { get { return "DROP VIEW "; } }
        protected virtual string CreateTableClause { get { return "CREATE TABLE "; } }
        protected virtual string CreateViewClause { get { return "CREATE VIEW "; } }
        protected virtual string AlterTableClause { get { return "ALTER TABLE "; } }
        protected virtual string AddColumnClause { get { return " ADD COLUMN "; } }
        protected virtual string CreateColumnsProlog { get { return " (\r\n    "; } }
        protected virtual string ColumnSeparator { get { return ",\r\n    "; } }
        protected virtual string CreateColumnsEpilog { get { return ")"; } }
        protected virtual string CreateIndexClause { get { return "CREATE INDEX "; } }
        protected virtual string CreateUniqueIndexClause { get { return "CREATE UNIQUE INDEX "; } }
        protected virtual string IndexOnClause { get { return " ON "; } }
        protected virtual string IndexColumnsProlog { get { return "\r\n    ("; } }
        protected virtual string IndexColumnsEpilog { get { return ")"; } }
        protected virtual string BeginTransactionClause { get { return string.Empty; } }
        protected virtual string CommitTransactionClause { get { return "COMMIT"; } }
        protected virtual string InsertColumnsEpilog { get { return ")\r\n"; } }
        protected virtual string PragmaTableInfo { get { return null; } }

        protected virtual void WriteColumnDefinition(StringBuilder sb, IMetaDataMember member)
        {
            if (sb != null && member != null)
            {
                sb.Append(DecorateName(member.ColumnName));
            }
        }

        protected virtual void WriteTableConstraints(StringBuilder sb, IMetaTable table)
        {
        }

        protected virtual void WritePrimaryIndex(StringBuilder sb, IMetaTable table) { }

        public virtual string DecorateCharValue(char value)
        {
            return DecorateStringValue(value.ToString());
        }

        protected virtual string DecorateStringValue(string value)
        {
            // double all apostrophe characters
            return value == null ? "NULL" : "'" + value.Replace("'", "''") + "'";
        }

        protected virtual string DecorateBooleanValue(bool value)
        {
            return value ? "1" : "0";
        }

        /// <summary>
        /// Be warned that writing string constants could potentially be unsafe depending
        /// on the SQL implementation
        /// </summary>
        protected virtual string EscapeString(string value)
        {
            return "'" + value.Replace("'", "''") + "'";
        }
        
        #endregion

        #region Implementation

        protected SqlBuilder()
        {
        }

        #region DDL Helpers

        protected void WriteDropTable(StringBuilder sb, IMetaTable table)
        {
            if (sb != null && table != null)
            {
                sb.Append(DropTableClause);
                sb.Append(DecorateTableName(table.TableName));
                sb.Append(StatementTerminator);
            }
        }

        protected void WriteDropView(StringBuilder sb, IMetaTable table)
        {
            if (sb != null && table != null)
            {
                sb.Append(DropViewClause);
                sb.Append(DecorateTableName(table.TableName));
                sb.Append(StatementTerminator);
            }
        }

        protected void WriteCreateTable(StringBuilder sb, IMetaTable table)
        {
            if (sb != null && table != null &&
                table.DataMembers != null && table.DataMembers.Any(x => x != null))
            {
                sb.Append(CreateTableClause);
                sb.Append(DecorateTableName(table.TableName));
                sb.Append(CreateColumnsProlog);
                bool first = true;
                foreach (IMetaDataMember member in table.DataMembers.Where(x => x != null))
                {
                    if (first)
                    {
                        first = false;
                    }
                    else
                    {
                        sb.Append(ColumnSeparator);
                    }
                    WriteColumnDefinition(sb, member);
                }
                WriteTableConstraints(sb, table);
                sb.Append(CreateColumnsEpilog);
                sb.Append(StatementTerminator);
            }
        }

        protected void WriteCreateView(StringBuilder sb, IMetaTable table)
        {
            sb.Append(CreateViewClause);
            sb.Append(DecorateTableName(table.TableName));
            sb.Append(AsClause);

            ISelectSpec selectSpec = table.CreateViewSelectSpec();

            if (selectSpec == null)
            {
                throw new InvalidOperationException($"Unable to create view for '{table.TableName}'");
            }

            BuildDdlStatementInfo info = new BuildDdlStatementInfo(this);
            selectSpec.WriteSql(sb, info, this, SqlWriteMode.Statement);

            sb.Append(StatementTerminator);
        }

        protected void WritePragmaTableInfo(StringBuilder sb, IMetaTable table)
        {
            if (sb != null && table != null)
            {
                sb.Append(PragmaTableInfo);
                sb.Append(OpenBracket);
                sb.Append(DecorateTableName(table.TableName));
                sb.Append(CloseBracket);
            }
        }

        protected void WriteAddColumn(StringBuilder sb, IMetaTable table, IMetaDataMember member)
        {
            if (sb != null && table != null && member != null)
            {
                sb.Append(AlterTableClause);
                sb.Append(DecorateTableName(table.TableName));
                sb.Append(AddColumnClause);
                WriteColumnDefinition(sb, member);
                sb.Append(StatementTerminator);
            }
        }

        protected IEnumerable<string> WriteTableIndexes(StringBuilder sb, IMetaTable table)
        {
            ICollection<string> result = new List<string>();
            if (sb != null && table != null && table.Indexes != null)
            {
                string tableName = DecorateTableName(table.TableName);
                foreach (IMetaIndex index in table.Indexes
                    .Where(x => x != null && x.Columns != null && x.Columns.Any(y => y != null)))
                {
                    sb.Append(index.Unique ? CreateUniqueIndexClause : CreateIndexClause);
                    sb.Append(DecorateName(index.IndexName));
                    sb.Append(IndexOnClause);
                    sb.Append(tableName);
                    sb.Append(IndexColumnsProlog);
                    this.WriteCommaSeparatedList(sb, null, SqlWriteMode.Column, index.Columns);
                    sb.Append(IndexColumnsEpilog);
                    sb.Append(StatementTerminator);
                    AddDdl(result, sb);
                }
            }
            return result;
        }

        private static IEnumerable<IMetaTable> GetTables(MetaTableClass classification, IEnumerable<IMetaTable> allTables)
        {
            IEnumerable<IMetaTable> tables = Enumerable.Empty<IMetaTable>();
            if (classification.IsSet(MetaTableClass.App))
            {
                tables = tables.Concat(allTables.Where(x => x.Classification.IsSet(MetaTableClass.App)));
            }
            if (classification.IsSet(MetaTableClass.System))
            {
                tables = tables.Concat(allTables.Where(x => x.Classification.IsSet(MetaTableClass.System)));
            }
            if (classification.IsSet(MetaTableClass.Remote))
            {
                tables = tables.Concat(allTables.Where(x => x.Classification.IsSet(MetaTableClass.Remote)));
            }
            tables = tables.Distinct().ToArray();
            return tables;
        }

        private static void AddDdl(ICollection<string> result, StringBuilder sb)
        {
            if (sb.Length > 0)
            {
                result.Add(sb.ToString());
                sb.Clear();
            }
        }

        #endregion

        private class BuildDdlStatementInfo : IStatementInfo
        {
            private readonly SqlBuilder _sqlBuilder;

            public DateTime StatementNow { get; }

            public IEnumerable<DbParameter> Parameters { get; }

            public BuildDdlStatementInfo(SqlBuilder sqlBuilder)
            {
                Parameters = new DbParameter[0];
                StatementNow = DbInternal.Now;
                _sqlBuilder = sqlBuilder;
            }

            public void WriteParameter(StringBuilder sb, Type parameterType, object value, int valueIndex)
            {
                // Parameters are not supported in DDL so we will write the constant

                if (value == null || _sqlBuilder.ValueIsNull(value.GetType(), value))
                {
                    sb.Append(_sqlBuilder.NullLiteral);
                    return;
                }

                switch (TypeHelper.GetTypeCode(value.GetType()))
                {
                    case TypeCode.Boolean:
                        sb.Append((bool)value ? _sqlBuilder.TrueLiteral : _sqlBuilder.FalseLiteral);
                        break;
                    case TypeCode.Int16:
                        sb.Append(ObjectConverter.ToString((short)value));
                        break;
                    case TypeCode.Int32:
                        sb.Append(ObjectConverter.ToString((int)value));
                        break;
                    case TypeCode.Int64:
                        sb.Append(ObjectConverter.ToString((long)value));
                        break;
                    case TypeCode.Double:
                        string str = ObjectConverter.ToString((double)value);
                        sb.Append(str);
                        if (!str.Contains("."))
                        {
                            sb.Append(".0");
                        }
                        break;
                    case TypeCode.Decimal:
                        string strDec = ObjectConverter.ToString((decimal)value);
                        sb.Append(strDec);
                        if (!strDec.Contains("."))
                        {
                            sb.Append(".0");
                        }
                        break;
                    case TypeCode.DateTime:
                        long ticks = ((DateTime)value).Ticks;
                        sb.Append(ObjectConverter.ToString((long)value));
                        break;
                    case TypeCode.String:
                        string strS = _sqlBuilder.EscapeString((string)value);
                        sb.Append(strS);
                        break;
                    case TypeCode.Guid:
                        string strGuid = ObjectConverter.ToString((Guid)value);
                        strGuid = _sqlBuilder.EscapeString(strGuid);
                        sb.Append(strGuid);
                        break;
                    default:
                        throw new NotSupportedException("Unsupported constant: " + value.ToString());
                }
            }
        }

        private class StatementInfo : IStatementInfo
        {
            public DateTime StatementNow { get; }

            public StatementInfo(DbCommand command, bool create)
            {
                if (command == null) throw new ArgumentNullException("command");
                _command = command;
                _create = create;
                _parameters = new List<DbParameter>();
                StatementNow = DbInternal.Now;
            }

            public IEnumerable<DbParameter> Parameters { get { return _parameters.AsEnumerable(); } }

            public void WriteParameter(StringBuilder sb, Type parameterType, object value, int valueIndex)
            {
                if (parameterType == null) throw new ArgumentNullException("parameterType");
                DbParameter parameter = null;
                lock (_parameters)
                {
                    if (valueIndex >= 0)
                    {
                        // check existing parameters
                        parameter = _command.Parameters.FirstOrDefault(x => x.ValueIndex == valueIndex);
                        if (parameter == null)
                        {
                            // check additional parameters
                            parameter = _parameters.FirstOrDefault(x => x.ValueIndex == valueIndex);
                        }
                    }
                    if (parameter == null && !_create) throw new InvalidOperationException("Parameter creation disabled");
                    if (parameter == null)
                    {
                        int parameterIndex = _command.Parameters.Count + _parameters.Count;
                        string parameterName = "P" + ObjectConverter.ToString(parameterIndex);
                        parameter = _command.CreateParameter(parameterName, parameterType, valueIndex);
                        _parameters.Add(parameter);
                    }
                    parameter.Value = value;
                }
                if (sb != null && parameter != null)
                {
                    sb.Append(parameter.Name);
                }
            }

            private readonly DbCommand _command;
            private readonly ICollection<DbParameter> _parameters;
            private readonly bool _create;
        }

        #endregion
    }

    public static class SqlBuilderExtensions
    {
        public static bool IsSet(this ParameterUsage source, ParameterUsage flag)
        {
            return ((source & flag) == flag);
        }

        public static IEnumerable<DbParameter> BuildSql(this SqlBuilder builder, DbCommand command, ISqlSpec source)
        {
            if (builder == null) throw new ArgumentNullException("builder");
            return builder.BuildSql(command, source, ParameterUsage.CreateNew);
        }

        public static bool IsSet(this MetaTableClass source, MetaTableClass flag)
        {
            return ((source & flag) == flag);
        }

        public static bool IsSet(this DdlChoice source, DdlChoice flag)
        {
            return ((source & flag) == flag);
        }

        public static IEnumerable<string> BuildDdl(this SqlBuilder builder, IMetaModel model)
        {
            if (builder == null) throw new ArgumentNullException("builder");
            return builder.BuildDdl(model, DdlChoice.CreateScript, MetaTableClass.All);
        }

       public static void WriteColumn(this SqlBuilder builder, StringBuilder sb, string tableAlias, string columnName)
        {
            if (builder != null && sb != null)
            {
                if (!string.IsNullOrEmpty(tableAlias))
                {
                    sb.Append(builder.DecorateTableName(tableAlias));
                    sb.Append(builder.NameQualifier);
                }
                sb.Append(builder.DecorateName(columnName));
            }
        }

        public static void WriteCommaSeparatedList<T>(this SqlBuilder builder, StringBuilder sb, IStatementInfo info, SqlWriteMode mode, IEnumerable<T> values) where T : IWriteSql
        {
            if (sb != null && values != null && builder != null)
            {
                bool first = true;
                foreach (T value in values)
                {
                    if (value != null)
                    {
                        if (first)
                        {
                            first = false;
                        }
                        else
                        {
                            sb.Append(builder.CommaSeparator);
                        }
                        value.WriteSql(sb, info, builder, mode);
                    }
                }
            }
        }

        public static void WriteWhere(this SqlBuilder builder, StringBuilder sb, IStatementInfo info, IEnumerable<IWhereElement> elements)
        {
            if (builder != null && sb != null && info != null && elements != null && elements.Any(x => x != null))
            {
                bool wantOperand = false;
                sb.Append(builder.WhereClause);
                foreach (IWhereElement element in elements)
                {
                    if (element != null)
                    {
                        if (wantOperand)
                        {
                            switch (element.Operand)
                            {
                                case EOperand.And:
                                    sb.Append(builder.AndOperand);
                                    break;
                                case EOperand.Or:
                                    sb.Append(builder.OrOperand);
                                    break;
                                default:
                                    sb.Append(builder.NoOperand);
                                    break;
                            }
                        }
                        wantOperand = element.Kind != EWhereElement.PushBracket;
                        element.WriteSql(sb, info, builder, SqlWriteMode.WhereElement);
                    }
                }
            }
        }

        public static string ComparisonText(this SqlBuilder builder, EComparisonMethod comparer)
        {
            string result = string.Empty;
            if (builder != null)
            {
                switch (comparer)
                {
                    case EComparisonMethod.Equals:
                        result = builder.EqualOperator;
                        break;
                    case EComparisonMethod.NotEqual:
                        result = builder.NotEqualOperator;
                        break;
                    case EComparisonMethod.LessThan:
                        result = builder.LessThanOperator;
                        break;
                    case EComparisonMethod.NotLessThan:
                        result = builder.NotLessThanOperator;
                        break;
                    case EComparisonMethod.GreaterThan:
                        result = builder.GreaterThanOperator;
                        break;
                    case EComparisonMethod.NotGreaterThan:
                        result = builder.NotGreaterThanOperator;
                        break;
                }
            }
            return result;
        }

        public static string MultiComparisonText(this SqlBuilder builder, EComparisonMethod comparer)
        {
            string result = string.Empty;
            if (builder != null)
            {
                switch (comparer)
                {
                    case EComparisonMethod.Equals:
                        result = builder.InOperator;
                        break;
                    case EComparisonMethod.NotEqual:
                        result = builder.NotInOperator;
                        break;
                }
            }
            return result;
        }

        public static string NullComparisonText(this SqlBuilder builder, EComparisonMethod comparer)
        {
            string result = string.Empty;
            if (builder != null)
            {
                switch (comparer)
                {
                    case EComparisonMethod.Equals:
                        result = builder.IsNullOperator;
                        break;
                    case EComparisonMethod.NotEqual:
                        result = builder.IsNotNullOperator;
                        break;
                }
            }
            return result;
        }

        public static string JoinText(this SqlBuilder builder, EJoinType join)
        {
            string result = string.Empty;
            if (builder != null)
            {
                switch (join)
                {
                    case EJoinType.Inner:
                        result = builder.InnerJoinClause;
                        break;
                    case EJoinType.LeftOuter:
                        result = builder.LeftOuterJoinClause;
                        break;
                    case EJoinType.RightOuter:
                        result = builder.RightOuterJoinClause;
                        break;
                    case EJoinType.FullOuter:
                        result = builder.FullOuterJoinClause;
                        break;
                }
            }
            return result;
        }

        public static void WriteFunction(this SqlBuilder builder, StringBuilder sb, EColumnFunction func, IAliasedColumnSpec column)
        {
            if (builder != null && sb != null && column != null)
            {
                switch (func)
                {
                    case EColumnFunction.None:
                        builder.WriteColumn(sb, column.TableAlias, column.ColumnName);
                        break;
                    case EColumnFunction.MaxFunction:
                        sb.Append(builder.MaxFunctionProlog);
                        builder.WriteColumn(sb, column.TableAlias, column.ColumnName);
                        sb.Append(builder.MaxFunctionEpilog);
                        break;
                    case EColumnFunction.MinFunction:
                        sb.Append(builder.MinFunctionProlog);
                        builder.WriteColumn(sb, column.TableAlias, column.ColumnName);
                        sb.Append(builder.MinFunctionEpilog);
                        break;
                    case EColumnFunction.CountFunction:
                        sb.Append(builder.CountFunctionProlog);
                        builder.WriteColumn(sb, column.TableAlias, column.ColumnName);
                        sb.Append(builder.CountFunctionEpilog);
                        break;
                    case EColumnFunction.SumFunction:
                        sb.Append(builder.SumFunctionProlog);
                        builder.WriteColumn(sb, column.TableAlias, column.ColumnName);
                        sb.Append(builder.SumFunctionEpilog);
                        break;
                    case EColumnFunction.AvgFunction:
                        sb.Append(builder.AvgFunctionProlog);
                        builder.WriteColumn(sb, column.TableAlias, column.ColumnName);
                        sb.Append(builder.AvgFunctionEpilog);
                        break;
                }
            }
        }

        public static void WriteAggregateFunction(this SqlBuilder builder, StringBuilder sb, IStatementInfo info, EColumnFunction func, IWriteSql aggregateExpression)
        {
            if (builder != null && sb != null)
            {
                switch (func)
                {
                    case EColumnFunction.MaxFunction:
                        sb.Append(builder.MaxFunctionProlog);
                        aggregateExpression?.WriteSql(sb, info, builder, SqlWriteMode.Statement);
                        sb.Append(builder.MaxFunctionEpilog);
                        break;
                    case EColumnFunction.MinFunction:
                        sb.Append(builder.MinFunctionProlog);
                        aggregateExpression?.WriteSql(sb, info, builder, SqlWriteMode.Statement);
                        sb.Append(builder.MinFunctionEpilog);
                        break;
                    case EColumnFunction.CountFunction:
                        sb.Append(builder.CountFunctionProlog);
                        aggregateExpression?.WriteSql(sb, info, builder, SqlWriteMode.Statement);
                        sb.Append(builder.CountFunctionEpilog);
                        break;
                    case EColumnFunction.SumFunction:
                        sb.Append(builder.SumFunctionProlog);
                        aggregateExpression?.WriteSql(sb, info, builder, SqlWriteMode.Statement);
                        sb.Append(builder.SumFunctionEpilog);
                        break;
                    case EColumnFunction.AvgFunction:
                        sb.Append(builder.AvgFunctionProlog);
                        aggregateExpression?.WriteSql(sb, info, builder, SqlWriteMode.Statement);
                        sb.Append(builder.AvgFunctionEpilog);
                        break;
                }
            }
        }

        public static void WriteOffset(this SqlBuilder builder, StringBuilder sb, int offset)
        {
            if (builder != null && sb != null && offset > 0)
            {
                sb.Append(builder.OffsetProlog);
                sb.Append(ObjectConverter.ToString(offset));
                sb.Append(builder.OffsetEpilog);
            }
        }

        public static void WriteLimit(this SqlBuilder builder, StringBuilder sb, int limit)
        {
            if (builder != null && sb != null && limit > 0)
            {
                sb.Append(builder.LimitProlog);
                sb.Append(ObjectConverter.ToString(limit));
                sb.Append(builder.LimitEpilog);
            }
        }
    }
}
