﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.Data;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Navigation
{
    public interface IHomePageItems
    {
        ViewableCollection<AppMenuItem> DashboardItems { get; }

        ViewableCollection<AppMenuItem> AppMenuItems { get; }

        Task UpdateDynamicItems(DataChangeSet changeSet = null);

#if REMOTE_ASSISTANCE
        Task CheckRemoteAssistanceEnabledAsync();
#endif

        void CancelDynamicItemUpdate();

        event EventHandler<EventArgs> Loaded;

        event EventHandler<EventArgs> CountersUpdated;

        event EventHandler<EventArgs> StatusIndicatorChanged;
    }
}
