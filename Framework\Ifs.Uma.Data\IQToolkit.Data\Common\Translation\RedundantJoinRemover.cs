﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Removes joins expressions that are identical to joins that already exist
    /// </summary>
    internal class RedundantJoinRemover : DbExpressionVisitor
    {
        Dictionary<TableAlias, TableAlias> map;

        private RedundantJoinRemover()
        {
            this.map = new Dictionary<TableAlias, TableAlias>();
        }

        public static Expression Remove(Expression expression)
        {
            return new RedundantJoinRemover().Visit(expression);
        }

        protected override Expression VisitJoin(JoinExpression node)
        {
            Expression result = base.VisitJoin(node);
            node = result as JoinExpression;
            if (node != null)
            {
                AliasedExpression right = node.Right as AliasedExpression;
                if (right != null)
                {
                    AliasedExpression similarRight = (AliasedExpression)this.FindSimilarRight(node.Left as JoinExpression, node);
                    if (similarRight != null)
                    {
                        this.map.Add(right.Alias, similarRight.Alias);
                        return node.Left;
                    }
                }
            }
            return result;
        }

        private Expression FindSimilarRight(JoinExpression join, JoinExpression compareTo)
        {
            if (join == null)
                return null;
            if (join.Join == compareTo.Join)
            {
                if (join.Right.NodeType == compareTo.Right.NodeType
                    && DbExpressionComparer.AreEqual(join.Right, compareTo.Right))
                {
                    if (join.On == compareTo.On)
                        return join.Right;
                    var scope = new ScopedDictionary<TableAlias, TableAlias>(null);
                    scope.Add(((AliasedExpression)join.Right).Alias, ((AliasedExpression)compareTo.Right).Alias);
                    if (DbExpressionComparer.AreEqual(null, scope, join.On, compareTo.On))
                        return join.Right;
                }
            }
            Expression result = FindSimilarRight(join.Left as JoinExpression, compareTo);
            if (result == null)
            {
                result = FindSimilarRight(join.Right as JoinExpression, compareTo);
            }
            return result;
        }

        protected override Expression VisitColumn(ColumnExpression node)
        {
            if (node == null) return null;
            TableAlias mapped;
            if (this.map.TryGetValue(node.Alias, out mapped))
            {
                return new ColumnExpression(node.Type, mapped, node.Name);
            }
            return node;
        }
    }
}
