﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml;
using Windows.ApplicationModel.Background;
using Windows.Devices.Geolocation;
using Windows.Foundation;
using Windows.Storage;
using Windows.Storage.FileProperties;

namespace Ifs.Uma.BackgroundTasks
{
    public sealed class GpsTrackingBackgroundTask : IBackgroundTask
    {
        private const string TaskName = "GpsTrackingBackgroundTask";
        private const string TaskEntryPoint = "Ifs.Uma.BackgroundTasks.GpsTrackingBackgroundTask";
        private const string TrackingFileName = "GpsTracking.txt";

        private const string UmaSettingGroup = "Uma";
        private const string GpsTrackingSettingGroup = "GpsTracking";
        private const string DesiredAccuracySettingName = "DesiredAccuracy";

        private CancellationTokenSource _cancellationTokenSource = null;

        async void IBackgroundTask.Run(IBackgroundTaskInstance taskInstance)
        {
            BackgroundTaskDeferral deferral = taskInstance.GetDeferral();

            try
            {
                taskInstance.Canceled += new BackgroundTaskCanceledEventHandler(OnCanceled);

                if (_cancellationTokenSource == null)
                {
                    _cancellationTokenSource = new CancellationTokenSource();
                }

                CancellationToken token = _cancellationTokenSource.Token;

                Geolocator geolocator = new Geolocator();
                geolocator.DesiredAccuracy = GetDesiredAccuracy();
                Geoposition pos = await geolocator.GetGeopositionAsync().AsTask(token);

                await SavePositionAsync(pos);
            }
            catch (Exception)
            { 
            }
            finally
            {
                _cancellationTokenSource = null;
                deferral.Complete();
            }
        }

        private async Task SavePositionAsync(Geoposition pos)
        {
            DateTimeOffset time = DateTimeOffset.UtcNow;
            double latitude = pos.Coordinate.Point.Position.Latitude;
            double longitude = pos.Coordinate.Point.Position.Longitude;
            string line = string.Join("^",
                    new[] 
                    { 
                        XmlConvert.ToString(time), 
                        XmlConvert.ToString(latitude), 
                        XmlConvert.ToString(longitude)
                    }) 
                + Environment.NewLine;

            StorageFile file = await ApplicationData.Current.LocalFolder.CreateFileAsync(TrackingFileName, CreationCollisionOption.OpenIfExists);
            
            await FileIO.AppendTextAsync(file, line);

            BasicProperties prop = await file.GetBasicPropertiesAsync();
            if (prop.Size > 7500)
            {
                // 7500 = Each line is about 50 bytes * 150 lines to give us some expansion room 
                await ShrinkFileLines(file, 100);
            }
        }

        private static async Task ShrinkFileLines(StorageFile file, int lineCount)
        {
            IList<string> lines = await FileIO.ReadLinesAsync(file);

            while (lines.Count > lineCount)
            {
                lines.RemoveAt(0);
            }

            await FileIO.WriteLinesAsync(file, lines);
        }

        private void OnCanceled(IBackgroundTaskInstance sender, BackgroundTaskCancellationReason reason)
        {
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
                _cancellationTokenSource = null;
            }
        }

        public static IAsyncOperation<IEnumerable<GpsTrackingPosition>> GetTrackedPositionsAsync()
        {
            return GetTrackedPositions().AsAsyncOperation();
        }

        private static async Task<IEnumerable<GpsTrackingPosition>> GetTrackedPositions()
        {
            List<GpsTrackingPosition> positions = new List<GpsTrackingPosition>();

            StorageFile file = await ApplicationData.Current.LocalFolder.CreateFileAsync(TrackingFileName, CreationCollisionOption.OpenIfExists);
            IList<string> lines = await FileIO.ReadLinesAsync(file);

            foreach (string line in lines)
            {
                string[] parts = line.Split('^');
                DateTimeOffset time = XmlConvert.ToDateTimeOffset(parts[0]).ToLocalTime();
                double latitude = XmlConvert.ToDouble(parts[1]);
                double longitude = XmlConvert.ToDouble(parts[2]);
                positions.Add(new GpsTrackingPosition(time, latitude, longitude));
            }

            return positions;
        }

        public static IAsyncAction ClearTrackingDataAsync()
        {
            return ClearTrackingData().AsAsyncAction();
        }

        private static async Task ClearTrackingData()
        {
            StorageFile file = await ApplicationData.Current.LocalFolder.GetFileAsync(TrackingFileName);
            if (file != null)
            {
                await file.DeleteAsync();
            }
        }

        public static IAsyncOperation<IBackgroundTaskRegistration> RegisterAsync(TimeSpan period)
        {
            return RegisterAsync(period, PositionAccuracy.Default);
        }

        public static IAsyncOperation<IBackgroundTaskRegistration> RegisterAsync(TimeSpan period, PositionAccuracy accuracy)
        {
            return Register(period, accuracy).AsAsyncOperation();
        }

        private static async Task<IBackgroundTaskRegistration> Register(TimeSpan period, PositionAccuracy accuracy)
        {
            try
            {
                BackgroundAccessStatus accessStatus = BackgroundExecutionManager.GetAccessStatus();

                if (accessStatus == BackgroundAccessStatus.Unspecified)
                {
                    accessStatus = await BackgroundExecutionManager.RequestAccessAsync();
                }

                if (accessStatus == BackgroundAccessStatus.Unspecified ||
                    accessStatus == BackgroundAccessStatus.DeniedByUser ||
                    accessStatus == BackgroundAccessStatus.DeniedBySystemPolicy)
                {
                    return null;
                }

                // Cannot just reuse existing task because it may have a different timer period
                IBackgroundTaskRegistration existingTask = GetRegisteredTask();
                if (existingTask != null)
                {
                    existingTask.Unregister(true);
                }

                SetDesiredAccuracy(accuracy);

                int minutes = Math.Max(15, (int)Math.Ceiling(period.TotalMinutes));
                TimeTrigger timeTrigger = new TimeTrigger((uint)minutes, false);

                BackgroundTaskBuilder builder = new BackgroundTaskBuilder();
                builder.Name = TaskName;
                builder.TaskEntryPoint = TaskEntryPoint;
                builder.SetTrigger(timeTrigger);
                return builder.Register();
            }
            catch (Exception)
            {
                return null;
            }
        }

        private static void SetDesiredAccuracy(PositionAccuracy accuracy)
        {
            ApplicationDataContainer container = GetGpsTrackingDataContainer();
            container.Values[DesiredAccuracySettingName] = accuracy.ToString();
        }

        private static PositionAccuracy GetDesiredAccuracy()
        {
            ApplicationDataContainer container = GetGpsTrackingDataContainer();

            object value;
            if (container.Values.TryGetValue(DesiredAccuracySettingName, out value) && value is string)
            {
                PositionAccuracy result;
                if (Enum.TryParse((string)value, out result))
                {
                    return result;
                }
            }

            return PositionAccuracy.Default;
        }

        private static ApplicationDataContainer GetGpsTrackingDataContainer()
        {
            return ApplicationData.Current.LocalSettings
               .CreateContainer(UmaSettingGroup, ApplicationDataCreateDisposition.Always)
               .CreateContainer(GpsTrackingSettingGroup, ApplicationDataCreateDisposition.Always);
        }

        public static IBackgroundTaskRegistration GetRegisteredTask()
        {
            foreach (var task in BackgroundTaskRegistration.AllTasks)
            {
                if (task.Value.Name == TaskName)
                {
                    return (IBackgroundTaskRegistration)task.Value;
                }
            }

            return null;
        }
    }

    public sealed class GpsTrackingPosition
    {
        public DateTimeOffset DateTime { get; private set; }
        public double Latitude { get; private set; }
        public double Longitude { get; private set; }

        public GpsTrackingPosition(DateTimeOffset dateTime, double latitude, double longitude)
        {
            DateTime = dateTime;
            Latitude = latitude;
            Longitude = longitude;
        }

        public override string ToString()
        {
            return string.Format("{0}: {1}, {2}", DateTime, Latitude, Longitude);
        }
    }
}
