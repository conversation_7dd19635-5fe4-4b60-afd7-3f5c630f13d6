﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Services;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Metadata.Navigation;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Unity;
using Unity.Exceptions;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.Framework.App
{
    public class SessionController : IResolver
    {
        private const string ServerVersionSetting = "ServerVersion";
        public Session CurrentSession { get; private set; }
        public ILogger Logger => LoggerManager.Logger;
        public ILoggerManager LoggerManager { get; }
        public IUnityContainer AppUnityContainer { get; }
        protected SynchronizationContext UiContext { get; }
        public AppRegistration Registration { get; }

        private readonly SemaphoreSlim _dataReadyLock = new SemaphoreSlim(1);
        private IAppParameters _appParameters;
        private readonly ISettings _settings;
        private string _launchUri;

        public SessionController(IUnityContainer appUnityContainer, ILoggerManager loggerManager, AppRegistration appReg, ISettings settings)
        {
            UiContext = SynchronizationContext.Current;

            appUnityContainer.RegisterInstance<IResolver>(this);

            AppUnityContainer = appUnityContainer;
            LoggerManager = loggerManager;
            Registration = appReg;
            _settings = settings;

            Dictionary<string, object> properties = new Dictionary<string, object>();
            properties["Version"] = Registration.DisplayVersion;
            properties["OS"] = DeviceInfo.OperatingSystem.ToString();
            properties["OSVersion"] = DeviceInfo.OperatingSystemVersion;
            LoggerManager.Insights.TrackEvent("ApplicationStarted", Insights.SystemEventType, properties);
        }

        #region Session Handling

        protected async Task StartSessionAsync(string username, IIfsConnection connection, TouchAppAccount account)
        {
            if (CurrentSession != null)
            {
                throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
            }

            Session session = new Session(AppUnityContainer, username, account);

            CurrentSession = session;

            LoggerManager.Insights.TrackEvent("SessionStarted", Insights.SystemEventType);

            await session.StartAsync(connection);

            if (session.TryResolve(out ITransactionSyncService transactionSyncService))
            {
                transactionSyncService.InitializeStatusChanged += SyncService_InitializeStatusChanged;
            }

            if (IsDataReady())
            {
                await session.SetDataReadyAsync();
            }

            await NavigateToApp();
        }

        private bool IsDataReady()
        {
            if (CurrentSession == null)
            {
                return false;
            }

            if (CurrentSession.TryResolve(out ITransactionSyncService transactionSyncService))
            {
                InitializeStatus initStatus = transactionSyncService.InitializeStatus;
                return initStatus >= InitializeStatus.Initialized ||
                       initStatus == InitializeStatus.Required_AfterMessagesSent ||
                       initStatus == InitializeStatus.UpdateMetadata_AfterMessagesSent;
            }
            else
            {
                return true;
            }
        }

        public async Task NavigateToApp()
        {
            ISystemNavigator systemNavigator = this.Resolve<ISystemNavigator>();
            INavigator navigator = this.Resolve<INavigator>();

            if (CurrentSession == null)
            {
                await systemNavigator.NavigateToLoginAsync();
            }
            else if (!CurrentSession.TryResolve(out ITransactionSyncService transactionSyncService) || !transactionSyncService.IsInitializing)
            {
                IHomePageItems homePageItems = this.Resolve<IHomePageItems>();
                if (DeviceInfo.OperatingSystem != OperatingSystem.Windows) // Windows handles this within AppMenuViewModel
                {
                    await homePageItems.UpdateDynamicItems();
                }

                NavigationParameter launchUriNavParam = ResolveLaunchArguments();
                if (DeviceInfo.OperatingSystem != OperatingSystem.Windows && launchUriNavParam != null)
                {
                    await navigator.NavigateToRootAsync(FrameworkLocations.MetadataPage, launchUriNavParam);
                    _launchUri = null;
                }
                else if (DeviceInfo.OperatingSystem != OperatingSystem.Windows && homePageItems.DashboardItems.Count == 1)
                {
                    IMetadata metadata = this.Resolve<IMetadata>();
                    MenuItemData item = metadata.NavigationData.MenuItems.First();
                    MetadataPageNavParam navParam = new MetadataPageNavParam(item.ProjectionName, item.Target);
                    await navigator.NavigateToRootAsync(FrameworkLocations.MetadataPage, navParam);

                    if (DeviceInfo.OperatingSystem == OperatingSystem.Android)
                    {
                        await systemNavigator.NavigateToComplete();
                    }
                }
                else
                {
                    await systemNavigator.NavigateToAppHomeAsync();
                }
            }
            else
            {
                await systemNavigator.NavigateToSyncMonitorAsync();
            }
        }

        private void SyncService_InitializeStatusChanged(object sender, EventArgs e)
        {
            // Post the sync event to the UI thread
            UiContext.Post(async _ => await OnInitializeStatusChanged(), null);
        }

        private async Task OnInitializeStatusChanged()
        {
            await _dataReadyLock.WaitAsync();
            try
            {
                if (CurrentSession != null)
                {
                    bool expectedIsDataReady = IsDataReady();
                    if (expectedIsDataReady != CurrentSession.IsDataReady)
                    {
                        if (expectedIsDataReady)
                        {
                            await CurrentSession.SetDataReadyAsync();

                            if (TryResolve(out IAppParameters appParameters))
                            {
                                _appParameters = appParameters;
                            }
                            ISettings pinSettings = _settings?.GetSubGroup(OfflinePinCodeConfigurations.PinCodeSettingsKey);
                            string tries = _appParameters?.GetPinCodeNoOfTries();
                            string failBehavior = _appParameters?.GetPinCodeFailBehavior();
                            string lockTime = _appParameters?.GetPinCodeLockAppTime();
                            pinSettings?.Set(OfflinePinCodeConfigurations.PinCodeAttemptsKey, tries);
                            pinSettings?.Set(OfflinePinCodeConfigurations.PinCodeFailBehaviorKey, failBehavior);
                            pinSettings?.Set(OfflinePinCodeConfigurations.PinCodeLockDurationKey, lockTime);
                                                        
                            if (TryResolve(out ITouchApp account) && account?.Accounts != null && account.Accounts.Any())
                            {
                                _settings.Set(ServerVersionSetting, account?.Accounts?.FirstOrDefault()?.ServerVersion);
                            }

                            if (TryResolve(out SynchronizationEndedEvent syncEndedEvent))
                            {
                                syncEndedEvent.NotifyInitializationCompleted();
                            }

                            await NavigateToApp();
                        }
                        else
                        {
                            await CurrentSession.SetDataUnavailableAsync();
                        }
                    }
                }
            }
            finally
            {
                _dataReadyLock.Release();
            }
        }

        protected async Task EndSessionAsync()
        {
            Session session = CurrentSession;
            if (session != null)
            {
                IDialogService dialogService = this.Resolve<IDialogService>();
                using (dialogService.ShowLoadingDialog(Strings.LoggingOutEllipsis, false))
                {
                    await _dataReadyLock.WaitAsync();
                    try
                    {
                        await session.EndAsync();
                        CurrentSession = null;
                    }
                    finally
                    {
                        _dataReadyLock.Release();
                    }

                    LoggerManager.Insights.TrackEvent("SessionEnded", Insights.SystemEventType);
                }

                ISystemNavigator systemNavigator = this.Resolve<ISystemNavigator>();
                await systemNavigator.NavigateToLoginAsync();
            }
        }

        #endregion

        #region IResolver

        public bool TryResolve<T>(out T result) where T : class
        {
            try
            {
                Session session = CurrentSession;

                if (session != null && session.TryResolve(out result))
                {
                    return true;
                }

                if (AppUnityContainer.IsRegistered<T>())
                {
                    result = AppUnityContainer.Resolve<T>();
                    return true;
                }

                result = null;
                return false;
            }
            catch (ResolutionFailedException)
            {
                result = null;
                return false;
            }
        }

        public object Resolve(Type type)
        {
            if (CurrentSession != null)
            {
                return CurrentSession.Resolve(type);
            }

            return AppUnityContainer.Resolve(type);
        }

        public object BuildUp(object obj)
        {
            if (CurrentSession != null)
            {
                return CurrentSession.BuildUp(obj);
            }
          
            Type type = obj.GetType();

            return AppUnityContainer.BuildUp(type, obj);
        }

        #endregion
        
        public void ProcessLaunchArguments(string launchUri)
        {
            Uri uri = new Uri(launchUri);
            if (string.IsNullOrEmpty(uri.PathAndQuery)) return;
            _launchUri = launchUri;
            if (CurrentSession == null) return;
            
            INavigator navigator = this.Resolve<INavigator>();
            NavigationParameter launchUriNavParam = ResolveLaunchArguments();
            if (launchUriNavParam != null)
            {
                navigator.NavigateToAsync(FrameworkLocations.MetadataPage, launchUriNavParam);
            }
        }

        public NavigationParameter ResolveLaunchArguments()
        {
            if (_launchUri == null) return null;
            Uri uri = new Uri(_launchUri);
            _launchUri = null;

            // Expect the path to be the page name and only the page name
            string page = uri.LocalPath.Substring(1);
                
            // Since projection is mandatory in MetadataPageNavParam we try to get it as the host part of the uri
            string projection = uri.Host;
            // However, since System.Uri lowercases the host part, and the projection is case sensitive, we need to get it from the original string
            if (!string.IsNullOrEmpty(projection))
            {
                GroupCollection groups = Regex.Match(uri.OriginalString, "[a-z][a-z0-9+\\-.]*://([a-zA-Z0-9\\-._~%]+)*.").Groups;
                if (groups.Count == 2)
                {
                    projection = groups[1].Value;
                }
            }
            // If no projection was supplied in the URI, or if the projection is not valid, we use the first client projection
            IMetadata metadata = this.Resolve<IMetadata>();
            if (string.IsNullOrEmpty(projection) || metadata?.CpiMetaData?.GetClientMetadata(projection) == null)
            {
                projection = metadata?.CpiMetaData?.Clients?.FirstOrDefault()?.Projection.Name;
            }

            // Validate that there is a corresponding page
            CpiPage metaDataPage;
            if ((metaDataPage = metadata?.FindPage(projection, page)) == null) return null;

            // Convert any query parameters into PageValues
            PageValues filter = null;
            if (!string.IsNullOrEmpty(uri.Query))
            {
                NameValueCollection nvc = HttpUtility.ParseQueryString(uri.Query.Substring(1));
                Dictionary<string, object> queryAttributes = nvc.AllKeys.ToDictionary(k => k, k => (object)nvc[k]);
                queryAttributes = queryAttributes
                    .Where(attribute => metadata.FindAttribute(projection, metaDataPage.Entity, attribute.Key) != null)
                    .ToDictionary(k => k.Key, k => k.Value);
                
                filter = PageValues.FromAttributeValues(queryAttributes);
            }
            return new MetadataPageNavParam(projection, page, filter);
        }
    }
}
