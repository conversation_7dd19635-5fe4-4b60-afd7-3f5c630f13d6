{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<BasicProc>": {"name": "BasicProc", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"value": "TestProcValue"}}, "assign": "Var1"}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<SubProcOuter>": {"name": "SubProcOuter", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "proc", "args": {"name": "SubProcInner"}}, "assign": "Var1"}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "SubProcInner": {"name": "SubProcInner", "type": "Utility", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"value": "TestProcValue2"}}, "assign": "Var1"}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<ParamsOuter>": {"name": "ParamsOuter", "type": "Function", "params": [{"name": "TestParam1"}], "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "proc", "args": {"name": "ParamsInner", "params": {"TestParam2": "${TestParam1}"}}}, "assign": "Var1"}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "ParamsInner": {"name": "ParamsInner", "type": "Utility", "params": [{"name": "TestParam2"}], "layers": [{"execute": [{"call": {"method": "return", "args": {"name": "TestParam2"}}}]}]}, "Function<GetPlatformTime>": {"name": "GetPlatformTime", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "proc", "args": {"name": "DateTime", "namespace": "System"}}, "assign": "Var1"}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<ScopeOuter>": {"name": "ScopeOuter", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Var1"}, {"call": {"method": "set", "args": {"value": "ScopeOuterValue"}}, "assign": "Var1.CustomerName"}, {"call": {"method": "proc", "args": {"name": "ScopeInner", "params": {"Var1": "${Var1}"}}}}, {"call": {"method": "return", "args": {"name": "Var1.CustomerName"}}}]}]}, "ScopeInner": {"name": "ScopeInner", "type": "Utility", "params": [{"name": "Var1"}], "layers": [{"execute": [{"call": {"method": "set", "args": {"value": "ScopeInnerValue"}}, "assign": "Var1.CustomerName"}]}]}, "Function<NoReturnValue>": {"name": "NoReturnValue", "type": "Function", "params": [{"name": "Var1"}], "layers": [{"execute": [{"call": {"method": "set", "args": {"value": "TestValue"}}, "assign": "Var1"}]}]}, "Function<ReturnInResult>": {"name": "ReturnInResult", "type": "Function", "layers": [{"execute": [{"call": {"method": "if", "args": {"offlineExpression": true}}, "result": {"TRUE": [{"call": {"method": "return", "args": {"value": "ReturnVal1"}}}]}}, {"call": {"method": "return", "args": {"value": "ReturnVal2"}}}]}]}}}}