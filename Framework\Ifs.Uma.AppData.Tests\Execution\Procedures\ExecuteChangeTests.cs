﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;
using NUnit.Framework;
using Prism.Events;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteChangeTests : ProcedureTest
    {
        private const string TstCustomerEntityName = "TstCustomer";
        private static readonly string TstCustomerTableName = RemoteNaming.ToTableName(TstCustomerEntityName);
        private const string TstCustomerOfflineQueryTableName = "tst_customer_offline_query";
        private const string CustomerNoAttributeName = "CustomerNo";
        private const string CustomerNameAttributeName = "CustomerName";

        [Test]
        public async Task InsertLocal()
        {
            RemoteRow row = new RemoteRow(TstCustomerTableName);
            row[CustomerNoAttributeName] = "500";

            await CallFunction("DoSaveLocal", row);

            RemoteRow insertedRow = GetRow();
            Assert.IsNotNull(insertedRow);
            Assert.AreEqual("500", insertedRow[CustomerNoAttributeName]);

            ValidateWrittenTransactionTable(null);
        }

        [Test]
        public async Task InsertAndSend()
        {
            RemoteRow row = new RemoteRow(TstCustomerTableName);
            row[CustomerNoAttributeName] = "500";

            await CallFunction("DoSaveAndSend", row);

            RemoteRow insertedRow = GetRow();
            Assert.IsNotNull(insertedRow);
            Assert.AreEqual("500", insertedRow[CustomerNoAttributeName]);

            ValidateWrittenTransactionTable(TstCustomerTableName);
            ValidateWrittenTransactionGroup("C: 500");
        }
        
        [Test]
        public async Task UpdateLocal()
        {
            RemoteRow row = InsertTestRow();
            row[CustomerNameAttributeName] = "TestName";

            await CallFunction("DoSaveLocal", row);
            
            RemoteRow updatedRow = GetRow();
            Assert.IsNotNull(updatedRow);
            Assert.AreEqual("500", updatedRow[CustomerNoAttributeName]);
            Assert.AreEqual("TestName", updatedRow[CustomerNameAttributeName]);

            ValidateWrittenTransactionTable(null);
        }

        [Test]
        public async Task UpdateAndSend()
        {
            RemoteRow row = InsertTestRow();
            row[CustomerNameAttributeName] = "TestName";

            await CallFunction("DoSaveAndSend", row);

            RemoteRow updatedRow = GetRow();
            Assert.IsNotNull(updatedRow);
            Assert.AreEqual("500", updatedRow[CustomerNoAttributeName]);
            Assert.AreEqual("TestName", updatedRow[CustomerNameAttributeName]);

            ValidateWrittenTransactionTable(TstCustomerTableName);
            ValidateWrittenTransactionGroup("C: 500");
        }
        
        [Test]
        public async Task DeleteLocal()
        {
            RemoteRow row = InsertTestRow();

            await CallFunction("DoDeleteLocal", row);

            RemoteRow updatedRow = GetRow();
            Assert.IsNull(updatedRow);

            ValidateWrittenTransactionTable(null);
        }

        [Test]
        public async Task DeleteAndSend()
        {
            RemoteRow row = InsertTestRow();

            await CallFunction("DoDeleteAndSend", row);

            RemoteRow updatedRow = GetRow();
            Assert.IsNull(updatedRow);

            ValidateWrittenTransactionTable(TstCustomerTableName);
            ValidateWrittenTransactionGroup("C: 500");
        }

        [Test]
        public async Task FailSaveOfflineQuery()
        {
            RemoteRow row = new RemoteRow(TstCustomerOfflineQueryTableName);
            row[CustomerNoAttributeName] = "500";

            ExecuteResult result = await CallFunction("DoSaveLocal", row, true);
            ProcedureException ex = result.Exception as ProcedureException;
            Assert.IsNotNull(ex);
            Assert.AreEqual("Function<DoSaveLocal>: Save or delete cannot be executed against offline query 'FndTstOffline.TstCustomerOfflineQuery' from a procedure", ex.Message);
        }

        [Test]
        public async Task FailDeleteOfflineQuery()
        {
            RemoteRow row = new RemoteRow(TstCustomerOfflineQueryTableName);
            row[CustomerNoAttributeName] = "500";

            ExecuteResult result = await CallFunction("DoDeleteLocal", row, true);
            ProcedureException ex = result.Exception as ProcedureException;
            Assert.IsNotNull(ex);
            Assert.AreEqual("Function<DoDeleteLocal>: Save or delete cannot be executed against offline query 'FndTstOffline.TstCustomerOfflineQuery' from a procedure", ex.Message);
        }

        [Test]
        public async Task PerformAction()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["TestParam"] = "MyTestValue";

            ExecuteResult result = await executor.CallActionAsync(TestOfflineProjection, "DoPerformAction", parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            
            ValidateWrittenTransactionTable("DoPerformAction");
            ValidateWrittenTransactionGroup("A: MyTestValue");
        }

        [Test]
        public async Task PerformBoundAction()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            
            RemoteRow row = InsertTestRow();

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["TestParam"] = "MyTestValue";

            ExecuteResult result = await executor.CallBoundActionAsync(TestOfflineProjection, row, "DoPerformBoundAction", parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            ValidateWrittenTransactionTable("TstCustomer.DoPerformBoundAction");
            ValidateWrittenTransactionValue("customer_no", "500");
            ValidateWrittenTransactionValue("test_param", "MyTestValue");
            ValidateWrittenTransactionGroup("BA: MyTestValue, 500");
        }

        [Test]
        public async Task PerformBoundActionOnOfflineQuery()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            InsertTestRow();

            RemoteRow row = new RemoteRow(TstCustomerOfflineQueryTableName);
            row[CustomerNoAttributeName] = "500";

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["TestParam"] = "MyTestValue";

            ExecuteResult result = await executor.CallBoundActionAsync(TestOfflineProjection, row, "DoPerformBoundAction", parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            ValidateWrittenTransactionTable("TstCustomerOfflineQuery.DoPerformBoundAction");
            ValidateWrittenTransactionValue("customer_no", "500");
            ValidateWrittenTransactionValue("test_param", "MyTestValue");
            ValidateWrittenTransactionGroup("C: 500");
        }

        [Test]
        public async Task PerformBoundFunction()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow row = InsertTestRow();

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["TestParam"] = "MyTestValue";

            ExecuteResult result = await executor.CallBoundFunctionAsync(TestOfflineProjection, row, "DoPerformBoundFunction", parameters, CancellationToken.None);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("MyTestValue|500|end", result.Value);
        }

        [Test]
        public async Task PerformBoundFunctionOnOfflineQuery()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            InsertTestRow();

            RemoteRow row = new RemoteRow(TstCustomerOfflineQueryTableName);
            row[CustomerNoAttributeName] = "500";

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["TestParam"] = "MyTestValue2";

            ExecuteResult result = await executor.CallBoundFunctionAsync(TestOfflineProjection, row, "DoPerformBoundFunction", parameters, CancellationToken.None);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("MyTestValue2|500|end", result.Value);
        }

        protected override void OnErrorLogged(string message)
        {
            if (!message.Contains("ProcedureException"))
            {
                base.OnErrorLogged(message);
            }
        }

        private async Task<ExecuteResult> CallFunction(string functionName, RemoteRow row, bool expectFail = false)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            IEventAggregator events = Resolve<IEventAggregator>();

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Record"] = row;

            DataChangeSet changes = null;
            Action<DataChangedEventArgs> onDataChanged = args => changes = args.ChangeSet;

            events.GetEvent<DataChangedEvent>().Subscribe(onDataChanged, ThreadOption.PublisherThread);

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, functionName, parameters);

            events.GetEvent<DataChangedEvent>().Unsubscribe(onDataChanged);

            Assert.IsNotNull(result);

            if (expectFail)
            {
                Assert.True(result.Failed);
                Assert.IsNull(changes);
            }
            else
            {
                Assert.IsFalse(result.Failed);
                Assert.IsNotNull(changes);
                Assert.IsTrue(changes.EffectedTables.Select(x => x.TableName).Contains(row.TableName));
            }

            return result;
        }

        private RemoteRow InsertTestRow()
        {
            RemoteRow row = new RemoteRow(TstCustomerTableName);
            row[CustomerNoAttributeName] = "500";

            IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
            FwDataContext ctx = (FwDataContext)dataContextProvider.CreateDataContext();
            IMetaTable metaTable = ctx.Model.GetTable(TstCustomerTableName);
            ITable<RemoteRow> table = (ITable<RemoteRow>)ctx.GetTable(metaTable);
            table.InsertOnSubmit(row);
            ctx.SubmitChanges(false);
            return row;
        }

        private RemoteRow GetRow()
        {
            IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
            FwDataContext ctx = (FwDataContext)dataContextProvider.CreateDataContext();
            IMetaTable metaTable = ctx.Model.GetTable(TstCustomerTableName);
            return ((ITable<RemoteRow>)ctx.GetTable(metaTable)).SingleOrDefault();
        }

        private void ValidateWrittenTransactionTable(string tableName)
        {
            IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
            FwDataContext ctx = (FwDataContext)dataContextProvider.CreateDataContext();
            string writtenTableName = ctx.TransitionRows.SingleOrDefault()?.TableName;
            Assert.AreEqual(tableName, writtenTableName);
        }

        private void ValidateWrittenTransactionGroup(string transactionGroup)
        {
            IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
            FwDataContext ctx = (FwDataContext)dataContextProvider.CreateDataContext();
            string writtenTransactionGroup = ctx.TransitionRows.SingleOrDefault()?.TransactionGroup;
            Assert.AreEqual(transactionGroup, writtenTransactionGroup);
        }

        private void ValidateWrittenTransactionValue(string field, object value)
        {
            IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
            FwDataContext ctx = (FwDataContext)dataContextProvider.CreateDataContext();
            TransitionRowField row = ctx.TransitionChanges.SingleOrDefault(x => x.FieldName == field);
            Assert.IsNotNull(row, $"No change found for field {field}");
            object storedValue = BinarySerializerHelper.ByteArrayToObject(row.NewValue);
            Assert.AreEqual(value, storedValue, $"Invalid value found for field {field}");
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteChangeSchema", null);
        }
    }
}
