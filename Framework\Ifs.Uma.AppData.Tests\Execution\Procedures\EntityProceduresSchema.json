{"name": "FndTstOffline", "version": "1706901162:1948287535", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}, "CustomersServerGen": {"name": "CustomersServerGen", "entity": "TstCustomerServerGen", "array": true}, "CustomersWithProcs": {"name": "CustomersWithProcs", "entity": "TstCustomerWithProcs", "array": true}}, "enumerations": {}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}, "references": {}, "arrays": {}, "actions": {}, "functions": {}}, "TstCustomerServerGen": {"name": "TstCustomerServerGen", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomerServerGen", "ludependencies": ["TstCustomerServerGen"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "Server"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}, "references": {}, "arrays": {}, "actions": {}, "functions": {}}, "TstCustomerWithProcs": {"name": "TstCustomerWithProcs", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomerWithProcs", "ludependencies": ["TstCustomerWithProcs"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}, "references": {}, "arrays": {}, "actions": {}, "functions": {}}}, "structures": {}, "actions": {}, "functions": {}, "procedures": {"EntityPrepare<TstCustomerWithProcs>": {"name": "TstCustomerWithProcs", "type": "EntityPrepare", "params": [{"name": "Record", "dataType": "Structure", "subType": "TstCustomerWithProcs"}], "layers": [{"execute": [{"call": {"method": "set", "args": {"value": "TestPrepareValue"}}, "assign": "Record.CustomerName"}, {"call": {"method": "return", "args": {"name": "Record"}}}]}]}, "EntityInsert<TstCustomerWithProcs>": {"name": "TstCustomerWithProcs", "type": "EntityInsert", "params": [{"name": "Record", "dataType": "Structure", "subType": "TstCustomerWithProcs"}], "layers": [{"execute": [{"call": {"method": "set", "args": {"value": "TestInsertValue"}}, "assign": "Record.CustomerName"}, {"call": {"method": "saveLocal", "args": {"name": "Record"}}}]}]}, "EntityUpdate<TstCustomerWithProcs>": {"name": "TstCustomerWithProcs", "type": "EntityUpdate", "params": [{"name": "Record", "dataType": "Structure", "subType": "TstCustomerWithProcs"}], "layers": [{"execute": [{"call": {"method": "set", "args": {"value": "TestUpdateValue"}}, "assign": "Record.CustomerName"}, {"call": {"method": "saveLocal", "args": {"name": "Record"}}}]}]}, "EntityDelete<TstCustomerWithProcs>": {"name": "TstCustomerWithProcs", "type": "EntityDelete", "params": [{"name": "Record", "dataType": "Structure", "subType": "TstCustomerWithProcs"}], "layers": [{"execute": [{"call": {"method": "deleteLocal", "args": {"name": "Record"}}}]}]}, "Event<SynchronizationEnded>": {"name": "SynchronizationEnded", "type": "Event", "params": [], "layers": [{"execute": [{"call": {"method": "return", "args": {"value": "SynchronizationEndedEventValue"}}}]}]}}}, "component": "FNDTST", "layout": {"lists": {}, "cards": {}, "selectors": {}, "pages": {}, "groups": {}, "menus": {}, "commands": {}}}