﻿using Ifs.Cloud.Client.Interfaces;
using Ifs.Uma.AppData;

namespace Ifs.Uma.Comm.TouchApps
{
    internal class FunctionResource : CallMethodResource, IQueryStringProvider
    {
        public EntityQuery Query { get; set; }

        protected override string ResourceSection => "function";

        public string GetQueryString()
        {
            return Query != null ? ResourceUtils.GetQueryString(Query) : null;
        }

        public override object DeserializeJsonString(string jsonString)
        {
            if (Query != null)
            {
                return ResourceUtils.DeserializeJsonString(Query, ClientKeysMapper, jsonString);
            }

            return base.DeserializeJsonString(jsonString);
        }
    }
}
