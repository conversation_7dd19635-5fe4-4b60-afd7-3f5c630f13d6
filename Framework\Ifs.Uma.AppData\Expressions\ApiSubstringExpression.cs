using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class ApiSubstringExpression : IfsApiExpression
    {
        public override IfsApiMethodName ApiMethodName => IfsApiMethodName.Contains;

        public override IfsApiMethodHandleType ApiMethodHandleType => IfsApiMethodHandleType.String;

        protected override MethodInfo LogicMethodInfo => typeof(ApiSubstringExpression).GetTypeInfo().GetDeclaredMethod(nameof(Substring));

        public ApiSubstringExpression(List<Expression> expressions)
        {
            Parameters = expressions;
        }

        private static string Substring(List<DynamicValue> parameters)
        {
            int startIndex = (int)(parameters[1].ToNumber());
            int index = (int)(parameters[2].ToNumber());
            string value = parameters[0].GetCleanString().Substring(startIndex, index);
            return value;
        }
    }
}
