﻿using System.Collections.Generic;
using System.Linq.Expressions;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Expressions
{
    public partial class IfsExpression
    {
        public static QueryExpression FromCpiQuery(CpiQuery query)
        {
            return query == null ? null : CpiQueryConverter.Convert(query);
        }

        private static class CpiQueryConverter
        {
            public static QueryExpression Convert(CpiQuery query)
            {
                if (query.Unions != null)
                {
                    List<UnionSelectExpression> union = CreateSelectForUnion(query.Unions, query.Select.Columns);
                    return Query(union);
                }
                else
                {
                    FromExpression from = QueryFrom(query.From.Entity, query.From.Alias);
                    List<JoinExpression> joins = ConvertJoins(query.Joins);
                    Expression where = query.Where == null ? null : FromJsonLogic(query.Where.JsonLogic);
                    List<ResultColumnExpression> resultColumns = ConvertResultColumns(query.Select.Columns);
                    return Query(from, joins, where, null, query.Select.Distinct, resultColumns);
                }
            }

            private static List<JoinExpression> ConvertJoins(CpiQueryJoin[] joins)
            {
                if (joins == null || joins.Length == 0)
                {
                    return null;
                }

                List<JoinExpression> exps = new List<JoinExpression>();
                foreach (CpiQueryJoin join in joins)
                {
                    Expression on = FromJsonLogic(join.On.JsonLogic);
                    JoinExpression exp = QueryJoin(ConvertJoinType(join.Type), join.Entity, join.Alias, on);
                    exps.Add(exp);
                }

                return exps;
            }

            private static EJoinType ConvertJoinType(CpiJoinType joinType)
            {
                switch (joinType)
                {
                    case CpiJoinType.Left:
                        return EJoinType.LeftOuter;
                    default:
                        return EJoinType.Inner;
                }
            }

            private static List<ResultColumnExpression> ConvertResultColumns(CpiQueryResultColumn[] columns)
            {
                List<ResultColumnExpression> exps = new List<ResultColumnExpression>();
                foreach (CpiQueryResultColumn column in columns)
                {
                    ResultColumnExpression exp = QueryResultColumn(column.Name, column.As);
                    exps.Add(exp);
                }
                return exps;
            }

            private static List<UnionSelectExpression> CreateSelectForUnion(CpiQueryFrom[] unions, CpiQueryResultColumn[] columns)
            {
                List<UnionSelectExpression> store = new List<UnionSelectExpression>();

                foreach (CpiQueryFrom union in unions)
                {
                    string entityName = union.Entity;
                    string alias = union.Alias;

                    if (string.IsNullOrEmpty(entityName) || string.IsNullOrEmpty(alias))
                    {
                        throw new InvalidMetadataException("EntityName and alias should have a value");
                    }

                    List<ResultColumnExpression> resultColumnExpressions = new List<ResultColumnExpression>();
                    foreach (CpiQueryResultColumn col in columns)
                    {
                        string[] colname = col.Name.Split('.');

                        if (colname.Length != 2)
                        {
                            throw new InvalidMetadataException("Column should have an alias");
                        }

                        ResultColumnExpression exp = QueryResultColumn(colname[1], alias == colname[0]);
                        resultColumnExpressions.Add(exp);
                    }
                    
                    store.Add(UnionSelect(entityName, resultColumnExpressions));
                }
                return store;
            }
        }
    }
}
