# Earlier powershell script fails to correctly download the signed exe (artifact) file from the signing server.
# Hence this python script was Written by LINILK to reliably download the file from the signing server.

import requests
from requests.auth import HTTPBasicAuth
import os
import argparse
import sys
import shutil

parser = argparse.ArgumentParser()
parser.add_argument('--signusername', required=True)
parser.add_argument('--signpassword', required=True)
parser.add_argument('--buildNumber', required=True)
parser.add_argument('--signedDeliverableName', required=True)
parser.add_argument('--packageDir', required=True)
parser.add_argument('--deliverablesDir', required=True)
parser.add_argument('--deliverableName', required=True)
parser.add_argument('--appVersion', required=True)

args = parser.parse_args()

# Sign username conversion
if 'corpnet\\' in args.signusername:
    args.signusername = args.signusername.replace('corpnet\\', '')

# Sign server SSL Cert
cert_path = 'cmbpde460_.corpnet.ifsworld.crt'

# Construct the URL for the artifact
artifact_url = f'https://cmbpde460.corpnet.ifsworld.com/job/Sign_EXE_MSI/{args.buildNumber}/artifact/{args.signedDeliverableName}'

# Path to download the artifact
output_file = f'{args.packageDir}' + f'{args.signedDeliverableName}'

# Path to put signed file for publishing
publish_file = f'{args.deliverablesDir}' + f'{args.deliverableName}' + '_' + f'{args.appVersion}' + '.exe'

# Create packageDir if doesn't exist
if not(os.path.exists(args.packageDir)):
    os.makedirs(args.packageDir)
    print(f'Created directory: {args.packageDir}')

# Create deliverablesDir if doesn't exist
if not(os.path.exists(args.deliverablesDir)):
    os.makedirs(args.deliverablesDir)
    print(f'Created directory: {args.deliverablesDir}')

print (f'Signed Artifact URL: {artifact_url}')

try:
    # Authenticate and send a HEAD request to get the artifact size
    response = requests.head(artifact_url, auth=HTTPBasicAuth(args.signusername, args.signpassword), verify=cert_path)
    if response.status_code == 200:
        total_size = int(response.headers.get('Content-Length', 0))
        print (f'Artifact found. File size: {total_size} bytes')
    else:
        print(f'Failed to retrieve the artifact file. Status code: {response.status_code}, Message: {response.text}')
        sys.exit(1)

    # Authenticate and download the artifact
    with requests.get(artifact_url, auth=HTTPBasicAuth(args.signusername, args.signpassword), stream=True, verify=cert_path) as response:
        # Check if the request was successful
        if response.status_code == 200:
            downloaded_size = 0
            download_output_incr = 1024*1024 # This is used show output after each 1MB. Reason for this is due to jenkins console output do not show the output in the same line even I have specified it in the script
            checkpoint = download_output_incr
            print ('Downloading the artifact...')
            with open(output_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        f.write(chunk)
                        downloaded_size = downloaded_size + len(chunk)
                        percent_complete = int(downloaded_size / total_size * 100)
                        if downloaded_size >= checkpoint:
                            checkpoint = checkpoint + download_output_incr
                            print(f'Downloaded {downloaded_size}/{total_size} bytes ({percent_complete}%)', end='\r')
                        
            # Check if the artifact has been downloaded correctly
            if downloaded_size == total_size:
                print(f'Downloaded {downloaded_size}/{total_size} bytes ({percent_complete}%)', end='\r')
                print(f'\nArtifact downloaded successfully: {output_file}')
                shutil.copy(output_file, publish_file)
                print(f'Artifact renamed and copied: {publish_file}')
                sys.exit(0)
            else:
                print('Artifact has been downloaded incorrectly.')
                sys.exit(1)
        else:
            print(f'Failed to download artifact. Status code: {response.status_code}, Message: {response.text}')
            sys.exit(1)
except Exception as e:
    print(f'An error occurred: {e}')
    sys.exit(1)