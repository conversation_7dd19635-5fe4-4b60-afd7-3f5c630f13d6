﻿using System;
using System.Collections.Generic;
using System.Reflection;

namespace Ifs.Uma.Database
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Naming", "CA1717:OnlyFlagsEnumsShouldHavePluralNames",
        Justification = "Fits in with IFS Apps enumeration names")]
    public enum TextFormats
    {
        None,
        Uppercase,
        Lowercase
    }

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Naming", "CA1717:OnlyFlagsEnumsShouldHavePluralNames",
        Justification = "Fits in with IFS Apps enumeration names")]
    public enum DateFormats
    {
        Timestamp,
        Date,
        TimestampUtc,
        Time
    }

    public enum NumberFormat
    {
        Unformatted,
        Decimal,
        Percentage,
        Currency
    }

    public enum SyncRule
    {
        Modified,
        Never,
        Always
    }

    public interface IMetaDataMember : INullableTypedColumnSpec
        // ColumnName: custom: AttributeName -> Lower Case with Underscore
        //             standard: either supplied in ColumnAttribute
        //                           or Property Name -> Lower Case with Underscore
        // ColumnType: custom: derived from DataType (and Mandatory)
        //             standard: Property Type
        // Mandatory:  custom: supplied
        //             standard: either derived from Property Type (if not class)
        //                           or supplied in ColumnAttribute
    {
        // custom: PropertyName is AttributeName -> Title Case
        // standard: Property.Name
        string PropertyName { get; }
        // Name of the data member when displaying to the user
        string DisplayName { get; }
        // Max Length of string or binary or precision of number (number of decimal digits)
        // custom: derived from DataLength
        // standard: supplied in ColumnAttribute
        int MaxLength { get; }
        // Scale of number (number of decimal places)
        // custom: derived from DataLength
        // standard: supplied in ColumnAttribute
        int Scale { get; }
        // custom: derived from Format
        // standard: supplied in ColumnAttribute
        TextFormats TextFormat { get; }
        // custom: derived from Format
        // standard: supplied in ColumnAttribute
        DateFormats DateFormat { get; }
        // custom: derived from MobileDataType
        // standard: supplied in ColumnAttribute
        NumberFormat NumberFormat { get; }
        // custom: derived from UiObject
        // standard: Default
        bool Insertable { get; }
        //JVB: I personally think this should be called "Mutable"
        // but the name came from an IFS Apps specification so I'll use it.
        bool Updateable { get; }
        bool PrimaryKey { get; }
        // Column is primary key on the server
        bool ServerPrimaryKey { get; }
        bool AutoIncrement { get; }
        // if Storage != null then
        //    use Storage to access the data
        // else if Property != null
        //    use Property to access the data
        // else
        //    use PropertyName and StringIndexer to access data
        PropertyInfo Property { get; }
        FieldInfo Storage { get; }
        IMetaEnumeration Enumeration { get; }
        // Index is non-negative and unique within the parent MetaTable.
        // The ordering may not be the same as the DataMembers enumerable in the MetaTable.
        int Index { get; }
        // Index of the column in the server
        int? ServerIndex { get; }
        // If Property and Storage are null then use StringIndexer and PropertyName to access data
        PropertyInfo StringIndexer { get; }
        // Storage ?? Property  the method used to access data (null if CustomField)
        MemberInfo MemberInfo { get; }
        SyncRule Sync { get; }
        bool IsBinary { get; }
        bool IsLongText { get; }
        // This is used to indicate that this attribute refers to a key in another entity through a reference
        bool IsReferenceSource { get; }
    }

    public interface IMetaEnumValue
    {
        string ServerValue { get; }
        string DisplayName { get; }
        object LocalValue { get; }
        bool ClientOnly { get; }
    }

    public interface IMetaEnumeration
    {
        string Name { get; }
        Type EnumType { get; }
        IEnumerable<IMetaEnumValue> Values { get; }
    }

    public interface IMetaIndex
    {
        string IndexName { get; }
        IEnumerable<IMetaDataMember> Columns { get; }
        bool Unique { get; }
    }

    [Flags]
    public enum MetaTableClass
    {
        None = 0,
        App = 1,        // Table is for app data and will be cleared on initialization
        System = 2,     // Table is required by the framework
        Remote = 4,     // Table data comes from the remote server
        AppRemote = App | Remote,
        All = System | App | Remote
    }

    public interface IMetaTable
    {
        string TableName { get; set; }
        string DisplayName { get; }
        string TransactionGroup { get; }
        MetaTableClass Classification { get; }
        TableImplementation TableImplementation { get; }
        IMetaModel Model { get; }
        Type RowType { get; }
        IEnumerable<IMetaDataMember> DataMembers { get; }
        IEnumerable<IMetaDataMember> PrimaryKey { get; }
        IEnumerable<IMetaIndex> Indexes { get; } // not including the primary key
        ISelectSpec CreateViewSelectSpec();
    }

    public enum RelationType
    {
        Reference,
        ObjectConnection
    }

    public enum TableImplementation
    {
        None,
        Table,
        View,
    }

    public interface IMetaRelation
    {
        string ReferenceName { get; }
        RelationType RelationType { get; }
        IMetaTable Table { get; }
        IEnumerable<IMetaDataMember> Columns { get; }
        IMetaTable ReferencedTable { get; }
        IEnumerable<IMetaDataMember> ReferencedColumns { get; }
    }

    public interface IMetaModel
    {
        Type ContextType { get; }
        MappingSource Source { get; }
        string GetTableName(Type rowType);
        IMetaTable GetTable(Type rowType);
        IMetaTable GetTable(string tableName);
        IEnumerable<IMetaTable> GetTables();
        IEnumerable<string> GetTableNames();
        IEnumerable<IMetaRelation> GetRelations(IMetaTable table);
    }
}
