﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public class DocumentDetailPage : PageBase
    {
        public const string PageName = "DocumentDetailPage";

        public DocumentFormData FormData { get; }

        public DocumentDetailPage(ILookupService lookupController, IDocumentHandler documentHandler, IDialogService dialogService, IToastService toastService,
            IMetadata metadata, IFileService fileService, IAppParameters appParams, IEventAggregator eventAggregator, INavigator navigator)
            : base(eventAggregator, dialogService)
        {
            Name = PageName;
            Classification = PageClassification.DocumentDetail;
            FormData = new DocumentFormData(lookupController, documentHandler, dialogService, toastService, metadata, fileService, appParams, eventAggregator, navigator);
            FormData.PropertyChanged += FormData_PropertyChanged;
        }

        private void FormData_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(FormData.FormTitleText))
            {
                Title = FormData.FormTitleText;
            }
            else if (e.PropertyName == nameof(FormData.HasChanges))
            {
                HasChanges = FormData.HasChanges;
            }
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter parameter)
        {
            AttachmentNavParam attachmentNavParam = parameter as AttachmentNavParam;
            DocumentNavParam navParam = new DocumentNavParam(attachmentNavParam.EntityName, attachmentNavParam.KeyRef);
            await FormData.Load(navParam);
            Title = FormData.FormTitleText;
            return true;
        }
    }
}
