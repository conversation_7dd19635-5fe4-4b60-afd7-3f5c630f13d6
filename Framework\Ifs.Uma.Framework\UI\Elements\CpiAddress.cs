﻿using System;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;

namespace Ifs.Uma.Framework.UI.Elements
{
    public class CpiAddress : Address
    {
        private CpiFieldAddress _cpiFieldAddress;

        private AddressPresentation[] _addressPresentations;

        public CpiAddress(CpiFieldAddress cpiFieldAddress)
        {
            _cpiFieldAddress = cpiFieldAddress;
        }

        public override string FormattedAddress
        {
            get
            {
                string formattedAddress = string.Empty;
                if (_addressPresentations != null)
                {
                    AddressPresentation addressPresentation;
                    if (string.IsNullOrEmpty(CountryCode))
                    {
                        addressPresentation = _addressPresentations.Where(x => x.DefaultDisplayLayout == FndBoolean.True).FirstOrDefault();
                    }
                    else
                    {
                        addressPresentation = _addressPresentations.Where(x => x.CountryCode == CountryCode).FirstOrDefault();
                    }

                    if (addressPresentation != null)
                    {
                        formattedAddress = addressPresentation.DisplayLayout;
                    }
                }

                if (string.IsNullOrEmpty(formattedAddress))
                {
                    if (!string.IsNullOrEmpty(Address1))
                        formattedAddress += Address1 + Environment.NewLine;
                    if (!string.IsNullOrEmpty(Address2))
                        formattedAddress += Address2 + Environment.NewLine;
                    if (!string.IsNullOrEmpty(Address3))
                        formattedAddress += Address3 + Environment.NewLine;
                    if (!string.IsNullOrEmpty(Address4))
                        formattedAddress += Address4 + Environment.NewLine;
                    if (!string.IsNullOrEmpty(Address5))
                        formattedAddress += Address5 + Environment.NewLine;
                    if (!string.IsNullOrEmpty(Address6))
                        formattedAddress += Address6 + Environment.NewLine;
                    if (!string.IsNullOrEmpty(City))
                        formattedAddress += City + Environment.NewLine;
                    if (!string.IsNullOrEmpty(State))
                        formattedAddress += State + Environment.NewLine;
                    if (!string.IsNullOrEmpty(County))
                        formattedAddress += County + Environment.NewLine;
                    if (!string.IsNullOrEmpty(Country))
                        formattedAddress += Country + Environment.NewLine;
                    if (!string.IsNullOrEmpty(ZipCode))
                        formattedAddress += ZipCode;
                }
                else
                {
                    formattedAddress = formattedAddress.Replace("&ADDRESS1", Address1);
                    formattedAddress = formattedAddress.Replace("&ADDRESS2", Address2);
                    formattedAddress = formattedAddress.Replace("&ADDRESS3", Address3);
                    formattedAddress = formattedAddress.Replace("&ADDRESS4", Address4);
                    formattedAddress = formattedAddress.Replace("&ADDRESS5", Address5);
                    formattedAddress = formattedAddress.Replace("&ADDRESS6", Address6);
                    formattedAddress = formattedAddress.Replace("&ZIP_CODE", ZipCode);
                    formattedAddress = formattedAddress.Replace("&CITY", City);
                    formattedAddress = formattedAddress.Replace("&COUNTY", County);
                    formattedAddress = formattedAddress.Replace("&COUNTRY_CODE", CountryCode);
                    formattedAddress = formattedAddress.Replace("&COUNTRY", Country);
                    formattedAddress = formattedAddress.Replace("&STATE", State);
                }

                string[] addressSplitByNewLine = formattedAddress?.Split(new string[] { "\r\n" }, StringSplitOptions.None);

                formattedAddress = string.Empty;

                if (addressSplitByNewLine != null)
                {
                    StringBuilder formattedAddressStringBuilder = new StringBuilder();
                    formattedAddressStringBuilder.Append(formattedAddress);

                    foreach (string str in addressSplitByNewLine)
                    {
                        if (string.IsNullOrEmpty(str))
                        {
                            continue;
                        }

                        Match match = Regex.Match(str, @"^[-\s]+$", RegexOptions.None, TimeSpan.FromSeconds(10));
                        if (!match.Success)
                        {
                            formattedAddressStringBuilder.Append(str);
                            formattedAddressStringBuilder.Append(Environment.NewLine);
                        }
                    }

                    formattedAddress = formattedAddressStringBuilder.ToString();
                }
                
                formattedAddress = formattedAddress.TrimEnd();

                return formattedAddress;
            }
        }

        public void Update(ViewData viewData, AddressPresentation[] addressPresentations)
        {
            _addressPresentations = addressPresentations;

            if (viewData != null)
            {
                object address1;
                viewData.TryGetValue(_cpiFieldAddress.Address1, out address1);
                object address2;
                viewData.TryGetValue(_cpiFieldAddress.Address2, out address2);
                object address3;
                viewData.TryGetValue(_cpiFieldAddress.Address3, out address3);
                object address4;
                viewData.TryGetValue(_cpiFieldAddress.Address4, out address4);
                object address5;
                viewData.TryGetValue(_cpiFieldAddress.Address5, out address5);
                object address6;
                viewData.TryGetValue(_cpiFieldAddress.Address6, out address6);
                object city;
                viewData.TryGetValue(_cpiFieldAddress.City, out city);
                object state;
                viewData.TryGetValue(_cpiFieldAddress.State, out state);
                object country;
                viewData.TryGetValue(_cpiFieldAddress.Country, out country);
                object zipcode;
                viewData.TryGetValue(_cpiFieldAddress.ZipCode, out zipcode);
                object county;
                viewData.TryGetValue(_cpiFieldAddress.County, out county);
                object countryCode;
                viewData.TryGetValue(_cpiFieldAddress.CountryCode, out countryCode);

                Address1 = address1?.ToString();
                Address2 = address2?.ToString();
                Address3 = address3?.ToString();
                Address4 = address4?.ToString();
                Address5 = address5?.ToString();
                Address6 = address6?.ToString();
                City = city?.ToString();
                County = county?.ToString();
                Country = country?.ToString();
                CountryCode = countryCode?.ToString();
                ZipCode = zipcode?.ToString();
                State = state?.ToString();

                OnPropertyChanged(nameof(FormattedAddress));
            }
        }
    }
}
