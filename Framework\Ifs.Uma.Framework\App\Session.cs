﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Services;
using Unity;
using Unity.Exceptions;

namespace Ifs.Uma.Framework.App
{
    public sealed class Session : IResolver
    {
        private bool Started { get; set; }
        private bool Ended { get; set; }

        public string UserName { get; }
        public bool IsDataReady => _dataReadyContainer != null;
        
        private IUnityContainer _sessionContainer;
        private IUnityContainer _dataReadyContainer;

        public TouchAppAccount Account { get; }

        public bool IsTryMeMode => Account == null;

        internal Session(IUnityContainer appUnityContiner, string userName, TouchAppAccount account)
        {
            if (appUnityContiner == null) throw new ArgumentNullException(nameof(appUnityContiner));

            UserName = userName;
            Account = account;

            _sessionContainer = appUnityContiner.CreateChildContainer();
        }
        
        internal async Task StartAsync(IIfsConnection connection)
        {
            if (Started)
            {
                throw new InvalidOperationException();
            }

            Started = true;

            IUnityContainer sessionContainer = _sessionContainer;

            if (connection != null)
            {
                sessionContainer.RegisterInstance(connection);
            }

            ServiceManager sm = sessionContainer.Resolve<ServiceManager>();
            sessionContainer.RegisterInstance<IServiceManager>(sm);

            IRegistrar registrar = sessionContainer.Resolve<IRegistrar>();
            registrar.ApplySessionRegistrations(sessionContainer);

            await sm.SwitchServiceLayerAsync(ServiceLayer.Session);
        }

        internal async Task SetDataReadyAsync()
        {
            if (Started && !Ended && _dataReadyContainer == null)
            {
                _dataReadyContainer = _sessionContainer.CreateChildContainer();
                
                IUnityContainer dataReadyContainer = _dataReadyContainer;

                IRegistrar registrar = dataReadyContainer.Resolve<IRegistrar>();
                registrar.ApplyInitializedRegistrations(dataReadyContainer);

                AppRegistration appReg = this.Resolve<AppRegistration>();
                appReg.ApplyRegistrations(dataReadyContainer);

                IServiceManager sm = dataReadyContainer.Resolve<IServiceManager>();
                await sm.SwitchServiceLayerAsync(ServiceLayer.DataReady);
            }
        }

        internal async Task SetDataUnavailableAsync()
        {
            if (Started && !Ended && _dataReadyContainer != null)
            {
                IServiceManager sm = this.Resolve<IServiceManager>();
                await sm.SwitchServiceLayerAsync(ServiceLayer.Session);

                _dataReadyContainer.Dispose();
                _dataReadyContainer = null;
            }
        }

        internal async Task EndAsync()
        {
            if (!Started)
            {
                throw new InvalidOperationException();
            }

            if (!Ended)
            {
                Ended = true;
                
                IServiceManager sm = this.Resolve<IServiceManager>();
                await sm.SwitchServiceLayerAsync(ServiceLayer.None);

                if (_dataReadyContainer != null)
                {
                    _dataReadyContainer.Dispose();
                    _dataReadyContainer = null;
                }

                if (_sessionContainer != null)
                {
                    _sessionContainer.Dispose();
                    _sessionContainer = null;
                }
            }
        }

        public bool TryResolve<T>(out T result) where T : class
        {
            try
            {
                if (_dataReadyContainer != null && _dataReadyContainer.IsRegistered<T>())
                {
                    result = _dataReadyContainer.Resolve<T>();
                    return true;
                }

                if (_sessionContainer != null && _sessionContainer.IsRegistered<T>())
                {
                    result = _sessionContainer.Resolve<T>();
                    return true;
                }

                result = null;
                return false;
            }
            catch (ResolutionFailedException)
            {
                result = null;
                return false;
            }
        }

        public object Resolve(Type type)
        {
            if (_dataReadyContainer != null)
            {
                return _dataReadyContainer.Resolve(type);
            }

            return _sessionContainer.Resolve(type);
        }

        public object BuildUp(object obj)
        {
            Type type = obj.GetType();

            if (_dataReadyContainer != null)
            {
                return _dataReadyContainer.BuildUp(type, obj);
            }

            return _sessionContainer.BuildUp(type, obj);
        }
    }
}
