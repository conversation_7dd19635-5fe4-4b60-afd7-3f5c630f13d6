﻿using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using System;
using System.Reflection;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Localization;

namespace Ifs.Uma.Framework.Data
{
    public class TransitionData : DataAccessor<FwDataContext>
    {
        public TransitionRow TransitionRow { get; private set; }

        private Transition _transition;
        private TransitionRowField[] _transitionRowFields;
        private IDataContextProvider _db;
        private ILogger _logger;

        private TransitionData(IDataContextProvider db, ILogger logger)
            : base(db, logger, null)
        {
            _db = db;
            _logger = logger;
        }

        public static async Task<TransitionData> FromTransitionRowId(IDataContextProvider db, ILogger logger, long transitionRowId)
        {
            TransitionData ft = new TransitionData(db, logger);
            try
            {
                if (await ft.LoadTransitionRow(transitionRowId))
                {
                    return ft;
                }
            }
            catch (Exception ex) 
            {
                logger.HandleException(ExceptionType.Recoverable, ex);
            }

            return null;
        }

        private async Task<bool> LoadTransitionRow(long transitionRowId)
        {
            return await WithDataContextAsync((ctx) =>
            {
                TransitionRow = ctx.TransitionRows
                    .FirstOrDefault(x => x.RowId == transitionRowId);

                if (TransitionRow == null)
                {
                    return false;
                }

                _transition = ctx.Transitions.FirstOrDefault(x => x.TransitionId == TransitionRow.TransitionId);
                _transitionRowFields = ctx.TransitionChanges.Where(x => x.TransitionRowId == TransitionRow.RowId).ToArray();

                return true;
            });
        }

        public async Task Resend()
        {
            await WithDataContextAsync((ctx) =>
            {
                ctx.TransitionRows.Attach(TransitionRow);
                TransitionRow.PrepareForResend();
                ctx.SubmitChanges(false);
            });
        }

        public async Task Resend(IEnumerable<TransitionChange> changes)
        {
            await WithDataContextAsync((ctx) =>
            {
                ctx.TransitionChanges.DeleteAllOnSubmit(_transitionRowFields);

                List<TransitionRowField> newFields = new List<TransitionRowField>();
                foreach (TransitionChange change in changes)
                {
                    TransitionRowField field = new TransitionRowField();
                    field.TransitionRowId = TransitionRow.RowId;
                    field.FieldName = change.FieldName;
                    field.NewValue = change.ByteData;
                    newFields.Add(field);
                }
                ctx.TransitionChanges.InsertAllOnSubmit(newFields);

                UpdateFuturePrimaryKeys(ctx, changes);

                ctx.TransitionRows.Attach(TransitionRow);
                TransitionRow.PrepareForResend();

                UpdateDataRow(ctx, changes);

                ctx.SubmitChanges(false);

                _transitionRowFields = newFields.ToArray();
            });
        }

        private void UpdateFuturePrimaryKeys(DataContext ctx, IEnumerable<TransitionChange> changes)
        {
            // We must update future transition changes if we change the primary key on an insert
            if (TransitionRow.Operation != OperationType.Insert)
            {
                return;
            }

            try
            {
                IMetaModel metaModel = _db.GetMetaModel();
                IMetaTable metaTable = metaModel.GetTable(TransitionRow.TableName);
                if (metaTable != null)
                {
                    foreach (TransitionChange change in changes)
                    {
                        IMetaDataMember member = change.Member ?? metaTable.DataMembers.FirstOrDefault(x => x.ColumnName == change.FieldName);
                        bool changingPrimary = member != null && member.ServerPrimaryKey;

                        if (changingPrimary)
                        {
                            IEnumerable<TransitionRowField> futureChanges = GetFutureChanges(ctx, change.FieldName);
                            foreach (TransitionRowField futureChange in futureChanges)
                            {
                                ctx.TransitionChanges.Attach(futureChange);
                                futureChange.NewValue = change.ByteData;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private IEnumerable<TransitionRowField> GetFutureChanges(DataContext ctx, string fieldName)
        {
            return (from tc in ctx.TransitionChanges
                    join tr in ctx.TransitionRows on tc.TransitionRowId equals tr.RowId
                    where tr.TableName == TransitionRow.TableName &&
                            tr.ModifiedRowId == TransitionRow.ModifiedRowId &&
                            tr.RowId > TransitionRow.RowId
                    where tc.FieldName == fieldName
                    select tc).ToArray();
        }

        private void UpdateDataRow(DataContext ctx, IEnumerable<TransitionChange> changes)
        {
            // We must update the data row with the new transition values if they haven't 
            // been changed by a later transition

            try
            {
                IMetaModel metaModel = _db.GetMetaModel();
                IMetaTable metaTable = metaModel.GetTable(TransitionRow.TableName);

                if (metaTable != null)
                {
                    string[] laterChangedFields = (from tc in ctx.TransitionChanges
                                                   join tr in ctx.TransitionRows on tc.TransitionRowId equals tr.RowId
                                                   where tr.TableName == TransitionRow.TableName && 
                                                         tr.ModifiedRowId == TransitionRow.ModifiedRowId && 
                                                         tr.RowId > TransitionRow.RowId
                                                   select tc.FieldName).Distinct().ToArray();

                    TransitionChange[] changesToApply = changes.Where(x => !laterChangedFields.Contains(x.FieldName)).ToArray();

                    if (changesToApply.Any())
                    {
                        RowBase row = GetAndAttachDataRow(ctx, metaTable);
                        if (row != null)
                        {
                            foreach (TransitionChange change in changesToApply)
                            {
                                IMetaDataMember member = change.Member;

                                if (member == null)
                                {
                                    member = metaTable.DataMembers.FirstOrDefault(x => x.ColumnName == change.FieldName);
                                }

                                if (member != null)
                                {
                                    row[member.PropertyName] = change.Value;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private RowBase GetAndAttachDataRow(DataContext ctx, IMetaTable metaTable)
        {
            ITable table = ctx.GetTable(metaTable);
            IQueryable<RowBase> queryable = table as IQueryable<RowBase>;
            if (queryable != null)
            {
                RowBase row = queryable.FirstOrDefault(x => x.RowId == TransitionRow.ModifiedRowId);
                if (row != null)
                {
                    table.Attach(row);
                    return row;
                }
            }

            return null;
        }

        public async Task Delete()
        {
            await WithDataContextAsync((ctx) =>
            {
                ctx.TransitionRows.DeleteOnSubmit(TransitionRow);
                ctx.TransitionChanges.DeleteAllOnSubmit(_transitionRowFields);

                if (!ctx.TransitionRows.Where(x => 
                    x.TransitionId == TransitionRow.TransitionId && 
                    x.SyncState != SyncState.SentAcknowledged &&
                    x.RowId != TransitionRow.RowId).Any())
                {
                    ctx.Transitions.DeleteOnSubmit(_transition);
                }

                ctx.SubmitChanges(false);
            });
        }

        public IEnumerable<TransitionChange> GetTransitionChanges()
        {
            IMetaModel metaModel = _db.GetMetaModel();
            IMetaTable table = metaModel.GetTable(TransitionRow.TableName);

            List<TransitionChange> values = new List<TransitionChange>();
            foreach (TransitionRowField field in _transitionRowFields)
            {
                TransitionChange change;
                IMetaDataMember member = table == null ? null : table.DataMembers.FirstOrDefault(x => x.ColumnName == field.FieldName);
                if (member == null)
                {
                    change = new TransitionChange(field.FieldName);
                }
                else
                {
                    change = new TransitionChange(member); 
                }
                change.ByteData = field.NewValue;

                values.Add(change);
            }

            return values;
        }

        public string GetTransitionName(IMetaModel metaModel)
        {
            return GetTransitionName(TransitionRow, metaModel);
        }

        public static string GetTransitionName(TransitionRow transitionRow, IMetaModel metaModel)
        {
            return GetOperationName(transitionRow) + " " + GetTableDisplayName(metaModel, transitionRow);
        }

        private static string GetOperationName(TransitionRow transitionRow)
        {
            switch (transitionRow.Operation)
            {
                case OperationType.Insert:
                    return Strings.Add;
                case OperationType.Delete:
                    return Strings.Delete;
                default:
                    return Strings.Update;
            }
        }

        private static string GetTableDisplayName(IMetaModel metaModel, TransitionRow transitionRow)
        {
            IMetaTable table = metaModel.GetTable(transitionRow.TableName);
            return table == null ? transitionRow.TableName : (string.IsNullOrEmpty(table.DisplayName) ? string.Format("[{0}]", table.TableName.ToTitleCaseSpace()) : table.DisplayName);
        }
    }

    public class TransitionChange : ObservableBase
    {
        public IMetaDataMember Member { get; private set; }
        public string FieldName { get; private set; }

        public TransitionChange(IMetaDataMember member)
        {
            Member = member;
            FieldName = Member.ColumnName;
        }

        public TransitionChange(string fieldName)
        {
            FieldName = fieldName;
        }

        private object _value = null;
        public object Value
        {
            get { return _value; }
            set
            {
                if (_value != value)
                {
                    _value = value;
                    OnPropertyChanged(() => Value);
                    OnPropertyChanged(() => ByteData);
                }
            }
        }

        public byte[] ByteData
        {
            // Convert an Enumeration to its ServerValue string it will serialise much shorter
            // and we won't need to convert it when we send the transition

            get
            {
                object value = Value;

                if (value != null)
                {
                    Type valueType = value.GetType();
                    if (valueType.GetTypeInfo().IsEnum && Member != null && Member.Enumeration != null)
                    {
                        value = Member.Enumeration.ServerValue(value);
                    }
                }

                return BinarySerializerHelper.ObjectToByteArray(value);
            }
            set
            {
                object objValue = BinarySerializerHelper.ByteArrayToObject(value);

                if (Member != null && Member.Enumeration != null && objValue is string)
                {
                    objValue = Member.Enumeration.LocalValue((string)objValue);
                }

                Value = objValue;
            }
        }
    }
}
