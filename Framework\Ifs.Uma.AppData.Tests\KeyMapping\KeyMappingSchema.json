{"name": "FndTstOffline", "component": "FNDTST", "version": "1706901162:1948287535", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["Customer"], "attributes": {"Customer": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}, "CompanyNo": {"datatype": "Text", "keygeneration": "User"}}, "references": {"CompanyRef": {"target": "TstCompany", "mapping": {"CompanyNo": "Company"}}}}, "TstCompany": {"name": "TstCompany", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCompany", "ludependencies": ["TstCompany"], "keys": ["Company"], "attributes": {"Company": {"datatype": "Text", "keygeneration": "User"}, "CompanyName": {"datatype": "Text", "keygeneration": "User"}}, "arrays": {"SitesArray": {"target": "TstSite", "mapping": {"Company": "CompanyNo"}}}}, "TstSite": {"name": "TstSite", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstSite", "ludependencies": ["TstSite"], "keys": ["Site"], "attributes": {"Site": {"datatype": "Text", "keygeneration": "User"}, "SiteName": {"datatype": "Text", "keygeneration": "User"}, "CompanyNo": {"datatype": "Text", "keygeneration": "User"}}}}}}