﻿using System.Runtime.Serialization;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Attachments
{
    [DataContract]
    public sealed class AttachmentNavParam : NavigationParameter
    {
        [DataMember]
        public string Page { get; private set; }

        [DataMember]
        public string EntityName { get; private set; }

        [DataMember]
        public string KeyRef { get; private set; }

        [DataMember]
        public bool RevisionEnabled { get; private set; }

        public AttachmentNavParam(string page, string entityName, string keyRef, bool revisionEnabled)
        {
            Page = page;
            EntityName = entityName;
            KeyRef = keyRef;
            RevisionEnabled = revisionEnabled;
        }
    }
}
