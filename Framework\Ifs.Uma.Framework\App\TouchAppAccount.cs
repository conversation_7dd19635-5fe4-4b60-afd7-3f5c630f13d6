﻿using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.App
{
    public class TouchAppAccount : ITouchAppsAccount
    {
        private const string IdSettingsName = "Id";
        private const string ServiceUrlSettingsName = "ServiceUrl";
        private const string SystemIdSettingsName = "SystemId";
        private const string UserNameSettingsName = "UserName";
        private const string UserDisplayNameSettingsName = "UserDisplayName";
        private const string AppNameSettingsName = "AppName";
        private const string DeviceIdSettingsName = "DeviceId";
        private const string PinAuthenticationSettingsName = "PinAuthentication";
        private const string ScopeIdSettingName = "ScopeId";
        private const string BrandingCodeName = "BrandingCode";
        private const string ServerVersionSettings = "ServerVersion";

        public int AccountId { get; private set; }
        public string ServiceUrl { get; private set; }
        public string SystemId { get; private set; }
        public string AppName { get; private set; }

        private IdentityProvider _identityProvider;
        public IdentityProvider IdentityProvider
        {
            get { return _identityProvider; }
            set
            {
                if (_identityProvider != value)
                {
                    _identityProvider = value;
                }
            }
        }

        private string _userName;
        public string UserName
        {
            get { return _userName; }
            internal set
            {
                if (_userName != value)
                {
                    _userName = value;

                    if (_settings != null)
                    {
                        _settings.Set(UserNameSettingsName, _userName);
                    }
                }
            }
        }

        private string _userDisplayName;

        public string UserDisplayName
        {
            get { return _userDisplayName; }
            set
            {
                if (_userDisplayName != value)
                {
                    _userDisplayName = value;

                    if (_settings != null)
                    {
                        _settings.Set(UserDisplayNameSettingsName, _userDisplayName);
                    }
                }
            }
        }

        private string _serverVersion;

        public string ServerVersion
        {
            get { return _serverVersion; }
            set
            {
                if (_serverVersion != value)
                {
                    _serverVersion = value;

                    if (_settings != null)
                    {
                        _settings.Set(ServerVersionSettings, _serverVersion);
                    }
                }
            }
        }

        public const string SystemIdForDevScaffold = "#devscaffold#";

        public bool DevScaffold => SystemId == SystemIdForDevScaffold;

        internal int DatabaseId => AccountId;
        internal bool DatabaseExists { get; set; }

        private int? _deviceId;
        public int? DeviceId
        {
            get { return _deviceId; }
            internal set
            {
                if (_deviceId != value)
                {
                    _deviceId = value;
                    _settings?.Set(DeviceIdSettingsName, DeviceId);
                }
            }
        }

        int? ITouchAppsAccount.DeviceId
        {
            get { return DeviceId; }
            set { DeviceId = value; }
        }

        private bool _pinAuthentication;
        public bool PinAuthentication
        {
            get { return _pinAuthentication; }
            set
            {
                _pinAuthentication = value;
                _settings.Set(PinAuthenticationSettingsName, _pinAuthentication);
            }
        }

        private string _scopeId;
        public string ScopeId
        {
            get { return _scopeId; }
            set
            {
                _scopeId = value;
                _settings.Set(ScopeIdSettingName, _scopeId);
            }
        }

        public bool IsActivated => DatabaseExists && DeviceId.HasValue;

        private string _brandingCode;
        public string BrandingCode
        {
            get { return _brandingCode; }
            set
            {
                if (_brandingCode != value)
                {
                    _brandingCode = value;

                    if (_settings != null)
                    {
                        _settings.Set(BrandingCodeName, _brandingCode);
                    }
                }
            }
        }

        private ISettings _settings;

        internal TouchAppAccount(int accountId, string serviceUrl, string systemId, string appName)
        {
            AccountId = accountId;
            ServiceUrl = serviceUrl;
            SystemId = systemId;
            AppName = appName;
        }

        internal TouchAppAccount(bool databaseExists, ISettings settings)
        {
            DatabaseExists = databaseExists;
            _settings = settings;
            Reload();
        }

        private void Reload()
        {
            if (_settings != null)
            {
                AccountId = _settings.GetInt32(IdSettingsName, -1);
                ServiceUrl = _settings.Get(ServiceUrlSettingsName);
                SystemId = _settings.Get(SystemIdSettingsName);
                AppName = _settings.Get(AppNameSettingsName);
                UserName = _settings.Get(UserNameSettingsName);
                UserDisplayName = _settings.Get(UserDisplayNameSettingsName);
                DeviceId = _settings.GetInt32(DeviceIdSettingsName, -1);
                BrandingCode = _settings.Get(BrandingCodeName);
                ServerVersion = _settings.Get(ServerVersionSettings);

                _pinAuthentication = _settings.GetBoolean(PinAuthenticationSettingsName, true);
                _scopeId = _settings.Get(ScopeIdSettingName);

                if (DeviceId == -1)
                {
                    DeviceId = 0;
                }
            }
        }

        internal void SetSettings(ISettings settings)
        {
            _settings = settings;
            _settings.Set(IdSettingsName, AccountId);
            _settings.Set(ServiceUrlSettingsName, ServiceUrl);
            _settings.Set(SystemIdSettingsName, SystemId);
            _settings.Set(UserNameSettingsName, UserName);
            _settings.Set(UserDisplayNameSettingsName, UserDisplayName);
            _settings.Set(AppNameSettingsName, AppName);
            _settings.Set(DeviceIdSettingsName, DeviceId);
            _settings.Set(PinAuthenticationSettingsName, PinAuthentication);
            _settings.Set(ScopeIdSettingName, ScopeId);
            _settings.Set(BrandingCodeName, BrandingCodeName);
            _settings.Set(ServerVersionSettings, ServerVersion);
        }

        internal void Delete()
        {
            _settings.Set(IdSettingsName, null);
            _settings.Set(ServiceUrlSettingsName, null);
            _settings.Set(SystemIdSettingsName, null);
            _settings.Set(UserNameSettingsName, null);
            _settings.Set(UserDisplayNameSettingsName, null);
            _settings.Set(AppNameSettingsName, null);
            _settings.Set(DeviceIdSettingsName, null);
            _settings.Set(PinAuthenticationSettingsName, null);
            _settings.Set(ScopeIdSettingName, null);
            _settings.Set(BrandingCodeName, null);
            _settings.Set(ServerVersionSettings, null);
        }
    }
}
