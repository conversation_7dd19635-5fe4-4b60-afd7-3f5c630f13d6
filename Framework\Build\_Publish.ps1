Write-Output "============ Publish"

$fullAppVersion = $deliverableName + "_" + $appVersion
$appPublishDir = $publishDir + $fullAppVersion

# App

# Delete .aab and .appxupload files for Dev builds
# Since they're not uploaded to stores and take up space in server
if (!($env:APP_NAME))
{
	Remove-Item -Force "$($deliverablesDir)*" -Include *.aab, *.appxupload -ErrorAction SilentlyContinue
}

New-Item -ItemType Directory -Force -Path $appPublishDir -ErrorAction SilentlyContinue | Out-Null
Copy-Item -Force -Recurse "$($deliverablesDir)*" $appPublishDir -ErrorAction SilentlyContinue

$dateTime = Get-Date -format "d/M/yyyy hh:mm:ss tt"

$latestFilePath = $publishDir + "\latest.txt"
$versionsFilePath = $publishDir + "\versions.txt"

if (!(Test-Path -Path $latestFilePath -PathType Leaf))
{
    New-Item -Path $publishDir -Name "latest.txt" -ItemType "file" -ErrorAction SilentlyContinue
}

if (!(Test-Path -Path $versionsFilePath -PathType Leaf))
{
    New-Item -Path $publishDir -Name "versions.txt" -ItemType "file" -ErrorAction SilentlyContinue
}

Set-Content -Path ($latestFilePath) -Value ($fullAppVersion) -Force -ErrorAction SilentlyContinue
$oldVersions = Get-Content -Path ($versionsFilePath) -ErrorAction SilentlyContinue
Set-Content -Path ($versionsFilePath) -Value ($fullAppVersion + "," + $dateTime) -Force -ErrorAction SilentlyContinue
Add-Content -Path ($versionsFilePath) -Value ($oldVersions) -Force -ErrorAction SilentlyContinue

# Documentation

#if (![string]::IsNullOrEmpty($publishDocumentationDir))
#{
#    Remove-Item -Recurse -Path $publishDocumentationDir -ErrorAction Ignore
#    Copy-Item -Force -Recurse -Path $documentationBuildDir -Destination $publishDocumentationDir
#}

# Delete the network drive once the publishing process is finished
net use * /del /yes

Write-Output "============ End - Publish"
