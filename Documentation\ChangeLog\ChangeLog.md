# Client Change Log

## 25.5.1471.0
#### Windows
[MOBOFF-20223](https://ifsdev.atlassian.net/browse/MOBOFF-20223): Update the test signing certificate for Windows app  

## 25.99.1468.0
#### Shared
[MOBOFF-20198](https://ifsdev.atlassian.net/browse/MOBOFF-20198): Add backward compatibility for image survey questions  

## 25.99.1459.0
#### Shared
[MOBOFF-19300](https://ifsdev.atlassian.net/browse/MOBOFF-19300): Increase the timeout to fix the MWO issue of missing objects for all platforms/environments.  
[MOBOFF-19581](https://ifsdev.atlassian.net/browse/MOBOFF-19581):  Fix Norwegian App Error that caused Pool work list empty  

## 25.99.1458.0
#### Shared
[MOBOFF-17909](https://ifsdev.atlassian.net/browse/MOBOFF-17909): Storing image step data to medialibrary instead of base64 string in database  

## 25.99.1457.0
#### Shared
[MOBOFF-19300](https://ifsdev.atlassian.net/browse/MOBOFF-19300): Fix the MWO issue of missing Object in Cloud  

## 25.99.1441.0
#### Shared
[MOBOFF-19138](https://ifsdev.atlassian.net/browse/MOBOFF-19138): Fix for navigation crash on Assistant dialogs loaded on single step assistant  
[MOBOFF-19518](https://ifsdev.atlassian.net/browse/MOBOFF-19518): Lov save can be done multiple times  

## 25.99.1439.0
#### Android
[MOBOFF-19121](https://ifsdev.atlassian.net/browse/MOBOFF-19121): Fix Android WIFI connection status issue  

## 25.99.1438.0
#### Shared
[MOBOFF-18951](https://ifsdev.atlassian.net/browse/MOBOFF-18951): Resolve the issue of failing to receive parts from the transport task  
[MOBOFF-19135](https://ifsdev.atlassian.net/browse/MOBOFF-19135): Fix for crash on disposed list items on back navigation  

## 25.99.1435.0
#### Shared
[MOBOFF-18659](https://ifsdev.atlassian.net/browse/MOBOFF-18659): Changes to improve performance issues on Windows  
[MOBOFF-18682](https://ifsdev.atlassian.net/browse/MOBOFF-18682): Detect Custom Assistants and handle Finish Command being null and Final step possibly being null  

## 25.99.1432.0
#### Shared
[MOBOFF-16285](https://ifsdev.atlassian.net/browse/MOBOFF-16285): Exclude server only FW attributes like F1ModifiedColumns from out message  

#### Android
[MOBOFF-18084](https://ifsdev.atlassian.net/browse/MOBOFF-18084): Enable symbol characters for survey statement answer on Android device  

## 25.99.1425.0
#### Shared
[MOBOFF-18549](https://ifsdev.atlassian.net/browse/MOBOFF-18549): Mobile does not support updated CSV offset syntax  
[MOBOFF-18865](https://ifsdev.atlassian.net/browse/MOBOFF-18865): added support for navigation back when having page name is defined  

## 25.99.1418.0
#### Shared
[MOBOFF-18678](https://ifsdev.atlassian.net/browse/MOBOFF-18678): Enhance performance by preventing the repeated execution of the same update function in a short time  

## 25.99.1412.0
#### Shared
[MOBOFF-18164](https://ifsdev.atlassian.net/browse/MOBOFF-18164): Fix for null value error on esig  

#### Android
[MOBOFF-17503](https://ifsdev.atlassian.net/browse/MOBOFF-17503): Preload bundled Noto fonts to ensure fonts with a wide character set are available for PDF report generation on Android  

## 25.99.1411.0
#### Android
[MOBOFF-18470](https://ifsdev.atlassian.net/browse/MOBOFF-18470): *********-mWO App crashes several times in Android  

## 25.99.1410.0
#### Shared
[MOBOFF-18381](https://ifsdev.atlassian.net/browse/MOBOFF-18381): Disable SITE_TIME_ZONE_AWARENESS_ENABLED check  
[MOBOFF-7475](https://ifsdev.atlassian.net/browse/MOBOFF-7475):  added tests to Get Native Executo rVersion and Native Notification Version  

#### Android
[MOBOFF-17503](https://ifsdev.atlassian.net/browse/MOBOFF-17503): Preload bundled Noto fonts to ensure fonts with a wide character set are available for PDF report generation on Android  

#### Windows
[MOBOFF-17725](https://ifsdev.atlassian.net/browse/MOBOFF-17725): Fix for focus in currency and percentage double fields  

## 25.99.1403.0
#### Android
[MOBOFF-17983](https://ifsdev.atlassian.net/browse/MOBOFF-17983): Cache network status to improve performance of function execution for Android  

## 24.99.1400.0
#### iOS
[MOBOFF-17979](https://ifsdev.atlassian.net/browse/MOBOFF-17979): Cache network status to improve function execution performance for iOS  

## 24.99.1397.0
#### Shared
[MOBOFF-17213](https://ifsdev.atlassian.net/browse/MOBOFF-17213): Fix eform object reference error  

#### Windows
[MOBOFF-17977](https://ifsdev.atlassian.net/browse/MOBOFF-17977): Fixed navigation lowness issue on Windows  
[MOBOFF-17977](https://ifsdev.atlassian.net/browse/MOBOFF-17977): Improved log file writing and fix for log file rotation  

## 24.99.1396.0
#### iOS
[MOBOFF-13883](https://ifsdev.atlassian.net/browse/MOBOFF-13883): Fix the iOS issue of login progress stuck  

## 24.99.1381.0
#### Windows
[MOBOFF-16455](https://ifsdev.atlassian.net/browse/MOBOFF-16455): Changed the visibility of the location icon on the address field to collapse when the language of the current device is not English.  

## 24.99.1380.0
#### Shared
[MOBOFF-16433](https://ifsdev.atlassian.net/browse/MOBOFF-16433): added time_zone_aware_request_test files and added required actions  
[MOBOFF-17303](https://ifsdev.atlassian.net/browse/MOBOFF-17303): Fixed form label font sizes in repeating sections element  
[MOBOFF-17330](https://ifsdev.atlassian.net/browse/MOBOFF-17330): An overlay has been added to facilitate command execution on the list.  

## 24.99.1361.0
#### Shared
[MOBOFF-14164](https://ifsdev.atlassian.net/browse/MOBOFF-14164): Fixed quality loss of logo in pdf reports  

## 24.99.1359.0
#### Shared
[MOBOFF-16999](https://ifsdev.atlassian.net/browse/MOBOFF-16999): Default value fix for static time values  

## 24.99.1358.0
#### Shared
[MOBOFF-16084](https://ifsdev.atlassian.net/browse/MOBOFF-16084): fixed field always shows the default value instead of the newly inserted value issue  

#### iOS
[MOBOFF-16284](https://ifsdev.atlassian.net/browse/MOBOFF-16284): Fix for crash when accessing Task Events offline  

## 24.99.1355.0
#### Shared
[MOBOFF-16433](https://ifsdev.atlassian.net/browse/MOBOFF-16433): added time_zone_aware_request_test files and added required actions  
[MOBOFF-16521](https://ifsdev.atlassian.net/browse/MOBOFF-16521): changes to support csv dates with offset  
[MOBOFF-8689](https://ifsdev.atlassian.net/browse/MOBOFF-8689): Fix for the corrupted Windows EXE produced after signing  

#### Android
[MOBOFF-16447](https://ifsdev.atlassian.net/browse/MOBOFF-16447): Fix Android time zone badge display issue when no time data  

## 24.99.1354.0
#### Shared
[MOBOFF-16520](https://ifsdev.atlassian.net/browse/MOBOFF-16520): Remove field visibility check to allow default values to be set on hidden fields  

#### Android
[MOBOFF-16337](https://ifsdev.atlassian.net/browse/MOBOFF-16337): Fix long running events date display issue  

#### iOS
[MOBOFF-16100](https://ifsdev.atlassian.net/browse/MOBOFF-16100): Resolve iOS card content unresponsive to clicks issue  
[MOBOFF-16361](https://ifsdev.atlassian.net/browse/MOBOFF-16361): Fix iOS timezone badge display issue when no data  

## 24.99.1352.0
#### Windows
[MOBOFF-16286](https://ifsdev.atlassian.net/browse/MOBOFF-16286): resolved the api.match() with multiline validation is not working issue on Windows  

## 24.99.1341.0
#### Shared
[MOBOFF-14164](https://ifsdev.atlassian.net/browse/MOBOFF-14164): Resize and compress report logo when downloading from server  
[MOBOFF-15914](https://ifsdev.atlassian.net/browse/MOBOFF-15914): Introduce new test transaction retry mechanism  
[MOBOFF-16344](https://ifsdev.atlassian.net/browse/MOBOFF-16344): Change the name of Close button to Ok in eForm  

## 24.99.1332.0
#### Shared
[MOBOFF-16019](https://ifsdev.atlassian.net/browse/MOBOFF-16019): Revert Site Timezone Aware related changes  

#### Android
[MOBOFF-14686](https://ifsdev.atlassian.net/browse/MOBOFF-14686): SuperNotCalledException more logs added related to the session  

## 24.99.1331.0
#### Android
[MOBOFF-14686](https://ifsdev.atlassian.net/browse/MOBOFF-14686): SuperNotCalledException more logs added related to the session  

#### iOS
[MOBOFF-15978](https://ifsdev.atlassian.net/browse/MOBOFF-15978): Appointment should sort in ascending order not descending order.  

## 24.99.1328.0
#### Shared
[MOBOFF-15929](https://ifsdev.atlassian.net/browse/MOBOFF-15929): resolved the api.substring() not working issue on the conditional field  

#### Android
[MOBOFF-14686](https://ifsdev.atlassian.net/browse/MOBOFF-14686): SuperNotCalledException more logs added related to the session  

## 24.99.1327.0
#### Shared
[MOBOFF-13446](https://ifsdev.atlassian.net/browse/MOBOFF-13446): Included PushNotificationTest TO flow test csproj file  
[MOBOFF-15933](https://ifsdev.atlassian.net/browse/MOBOFF-15933): Changes to support resolvecsv on mobile framework  
[MOBOFF-16250](https://ifsdev.atlassian.net/browse/MOBOFF-16250): Added timezone awareness support to calender shift times  

#### Android
[MOBOFF-14686](https://ifsdev.atlassian.net/browse/MOBOFF-14686): SuperNotCalledException more logs added related to the session  
[MOBOFF-15939](https://ifsdev.atlassian.net/browse/MOBOFF-15939): APP crash when connecting to Remote Assistance call in Android 14  
[MOBOFF-16257](https://ifsdev.atlassian.net/browse/MOBOFF-16257): Resolve Calendar Time display issue for Norwegian on Android  

#### iOS
[MOBOFF-16255](https://ifsdev.atlassian.net/browse/MOBOFF-16255): Fix today icon not working on different language settings  

## 24.99.1324.0
#### Shared
[MOBOFF-16019](https://ifsdev.atlassian.net/browse/MOBOFF-16019): Disable Site Time Zone Func when site timezone aware false  

## 24.99.1321.0
#### Shared
[MOBOFF-11568](https://ifsdev.atlassian.net/browse/MOBOFF-11568): Change iOS build script by specifying app type  
[MOBOFF-15978](https://ifsdev.atlassian.net/browse/MOBOFF-15978): Fix appointments not sorted in ascending order issue  

#### Windows
[MOBOFF-16018](https://ifsdev.atlassian.net/browse/MOBOFF-16018): Fix for focus not set on the next button in FilePickerFields on DynamicAssistant pages  

## 24.99.1320.0
#### Shared
[MOBOFF-13631](https://ifsdev.atlassian.net/browse/MOBOFF-13631): Changed to handle regex patterns surrounded by slashes from Page Designer.  

#### Android
[MOBOFF-16052](https://ifsdev.atlassian.net/browse/MOBOFF-16052): Fixed Remote Assistance is not working issue in Android 13 due to BT permission  

## 24.99.1319.0
#### Shared
[MOBOFF-16061](https://ifsdev.atlassian.net/browse/MOBOFF-16061): Added TZ support for interpolated strings in field & card labels  

#### Android
[MOBOFF-14686](https://ifsdev.atlassian.net/browse/MOBOFF-14686): Possible fix for SuperNotCalledException with more logs added  
[MOBOFF-16052](https://ifsdev.atlassian.net/browse/MOBOFF-16052): Fixed Remote Assistance is not working issue in Android 13 due to BT permission  

## 24.99.1317.0
#### Shared
[MOBOFF-16024](https://ifsdev.atlassian.net/browse/MOBOFF-16024): Get the record keyref using the transition change set for a transition row  

## 24.99.1316.0
#### Shared
[MOBOFF-15846](https://ifsdev.atlassian.net/browse/MOBOFF-15846): Added fix to display Address in the given format in MyWork Page  

## 24.99.1315.0
#### Shared
[MOBOFF-15579](https://ifsdev.atlassian.net/browse/MOBOFF-15579): change the date time parse TryParse to handle unexpected date formats.  
[MOBOFF-15902](https://ifsdev.atlassian.net/browse/MOBOFF-15902): Convert utc timestamp to local time on Calendar events  
[MOBOFF-15992](https://ifsdev.atlassian.net/browse/MOBOFF-15992): Fix for UI issues on Fields  
[MOBOFF-16010](https://ifsdev.atlassian.net/browse/MOBOFF-16010): Upgrade System.Formats.Asn1 Nuget package to address vulnerability  

## 24.99.1312.0
#### Shared
[MOBOFF-12250](https://ifsdev.atlassian.net/browse/MOBOFF-12250): Configure navigation with parameters to a core or custom screen from a Homescreen tile so that end users can see the right information  

## 24.99.1308.0
#### Shared
[MOBOFF-13631](https://ifsdev.atlassian.net/browse/MOBOFF-13631): Implementation for Api Match function  
[MOBOFF-15723](https://ifsdev.atlassian.net/browse/MOBOFF-15723): Add new http header for site timezone aware actions  

#### Windows
[MOBOFF-15656](https://ifsdev.atlassian.net/browse/MOBOFF-15656): Fixed the timestamp field being not visible when size is small  

## 24.99.1306.0
#### Shared
[MOBOFF-14453](https://ifsdev.atlassian.net/browse/MOBOFF-14453): implementation for contains API function  
[MOBOFF-15839](https://ifsdev.atlassian.net/browse/MOBOFF-15839): Fix Czech translation for sync monitor  

#### Android
[MOBOFF-15620](https://ifsdev.atlassian.net/browse/MOBOFF-15620): Change color of the none option in the optional enum for gray  

#### iOS
[MOBOFF-14423](https://ifsdev.atlassian.net/browse/MOBOFF-14423): When user clicks on hyperlink it should let user choose to open in app or browser  

#### Windows
[MOBOFF-15645](https://ifsdev.atlassian.net/browse/MOBOFF-15645): Add translation support for Norwegian Bokmal and Nynorsk  

## 24.99.1301.0
#### Shared
[MOBOFF-14866](https://ifsdev.atlassian.net/browse/MOBOFF-14866): Site TZ feature toggle  
[MOBOFF-13446](https://ifsdev.atlassian.net/browse/MOBOFF-13446): Added unit test for push notification  

## 24.99.1296.0
#### Shared
[MOBOFF-13622](https://ifsdev.atlassian.net/browse/MOBOFF-13622): System for validate clear site cache  

#### Android
[MOBOFF-14686](https://ifsdev.atlassian.net/browse/MOBOFF-14686): Add enahanced log messages to root cause SuperNotCalledException  

#### iOS
[MOBOFF-15704](https://ifsdev.atlassian.net/browse/MOBOFF-15704): Updated iOS SDK for Help Lightning to 17.1  

## 24.99.1295.0
#### Shared
[MOBOFF-13040](https://ifsdev.atlassian.net/browse/MOBOFF-13040): Implement the Time Zone info update when time zone is changed  
[MOBOFF-15427](https://ifsdev.atlassian.net/browse/MOBOFF-15427): Upgrade HelpLightning SDK to 17.0  

#### Android
[MOBOFF-15399](https://ifsdev.atlassian.net/browse/MOBOFF-15399): Fixed the issue where MWO app launches only for the first time after Omnibyte callback  
[MOBOFF-15672](https://ifsdev.atlassian.net/browse/MOBOFF-15672): Merge the changes related to A14 RegisterReceiver  
[MOBOFF-15633](https://ifsdev.atlassian.net/browse/MOBOFF-15633): Getting only an empty notification when the RA call receives while the app is in background  
[MOBOFF-14686](https://ifsdev.atlassian.net/browse/MOBOFF-14686): Add enahanced log messages to root cause SuperNotCalledException  

## 24.99.1293.0
#### Shared
[MOBOFF-10438](https://ifsdev.atlassian.net/browse/MOBOFF-10438): Add Default value capabilities  
[MOBOFF-14862](https://ifsdev.atlassian.net/browse/MOBOFF-14862): Fix the issue with opening downloaded video  
[MOBOFF-15583](https://ifsdev.atlassian.net/browse/MOBOFF-15583): Fixed Objsite null check error in transition row  

#### Android
[MOBOFF-14747](https://ifsdev.atlassian.net/browse/MOBOFF-14747): Update API level to 34  

## 24.99.1286.0
#### Shared
[MOBOFF-13260](https://ifsdev.atlassian.net/browse/MOBOFF-13260): framework support for Union keyword in offline queries.  

#### Windows
[MOBOFF-14353](https://ifsdev.atlassian.net/browse/MOBOFF-14353): Implement Workflow Attachment Access for Window App  

## 24.99.1285.0
#### Shared
[MOBOFF-14904](https://ifsdev.atlassian.net/browse/MOBOFF-14904): Add Enumeration Filter support for Custom Enumerations  

## 24.99.1283.0
#### Shared
[MOBOFF-13626](https://ifsdev.atlassian.net/browse/MOBOFF-13626): Added Client-side validation capability.  

## 24.99.1276.0
#### Windows
[MOBOFF-15433](https://ifsdev.atlassian.net/browse/MOBOFF-15433): Fix the translation regression issue  

## 24.99.1274.0
#### Shared
[MOBOFF-12435](https://ifsdev.atlassian.net/browse/MOBOFF-12435): Fix sort order reflect as none issue  
[MOBOFF-14909](https://ifsdev.atlassian.net/browse/MOBOFF-14909): Fix to call FindEntity method for bound actions correctly  

#### iOS
[MOBOFF-14764](https://ifsdev.atlassian.net/browse/MOBOFF-14764): Change UINavigationBar appearance to see cancel button  

## 24.99.1273.0
#### Shared
[MOBOFF-14343](https://ifsdev.atlassian.net/browse/MOBOFF-14343): Stop showing low-level errors as dialogs  
[MOBOFF-14717](https://ifsdev.atlassian.net/browse/MOBOFF-14717): Include references when checking entity timezone awareness  
[MOBOFF-14895](https://ifsdev.atlassian.net/browse/MOBOFF-14895): Fix mobile app menu item alignment issue  

## 24.99.1272.0
#### Shared
[MOBOFF-11698](https://ifsdev.atlassian.net/browse/MOBOFF-11698): Support for compound odata expressions is added  

#### Android
[MOBOFF-14474](https://ifsdev.atlassian.net/browse/MOBOFF-14474): Made it possible to use filter while using intents.  

#### iOS
[MOBOFF-14857](https://ifsdev.atlassian.net/browse/MOBOFF-14857): Fix a crash issue of video preview  

## 24.99.1271.0
#### Android
[MOBOFF-14390](https://ifsdev.atlassian.net/browse/MOBOFF-14390): Receiving empty notification when initiating RemoteAssistance call fix  
[MOBOFF-14771](https://ifsdev.atlassian.net/browse/MOBOFF-14771): Fix app crashes issue when download the online entity records in LOV  

## 24.99.1269.0
#### Shared
[MOBOFF-10930](https://ifsdev.atlassian.net/browse/MOBOFF-10930): Resolved inconsistent status for media after init  
[MOBOFF-14700](https://ifsdev.atlassian.net/browse/MOBOFF-14700): Fixed failing EntityPrepare system test  

## 24.99.1268.0
#### Shared
[MOBOFF-14395](https://ifsdev.atlassian.net/browse/MOBOFF-14395): Added capabilities to Custom Entities  

#### Android
[MOBOFF-14368](https://ifsdev.atlassian.net/browse/MOBOFF-14368): Time zone popup wont show up when the event triggered intemitently fix  
[MOBOFF-14375](https://ifsdev.atlassian.net/browse/MOBOFF-14375): Fix layout to handle reisizable timezone information button  
[MOBOFF-14682](https://ifsdev.atlassian.net/browse/MOBOFF-14682): Fixed PopFromBreadcrumbStack navigation crash  

#### iOS
[MOBOFF-13937](https://ifsdev.atlassian.net/browse/MOBOFF-13937): UI cut off fixed in Syncfusion Schedule Date Picker  

## 24.99.1267.0
#### Android
[MOBOFF-14661](https://ifsdev.atlassian.net/browse/MOBOFF-14661): Fix the app crash issue when selecting country or language  

#### Windows
[MOBOFF-14674](https://ifsdev.atlassian.net/browse/MOBOFF-14674): Add record upload functionality for windows  

## 24.99.1261.0
#### Shared
[MOBOFF-13514](https://ifsdev.atlassian.net/browse/MOBOFF-13514): Added Site timezone awareness support  
[MOBOFF-14487](https://ifsdev.atlassian.net/browse/MOBOFF-14487): Use dynamic redirect Uri instead of hardcoded Uri  
[MOBOFF-14651](https://ifsdev.atlassian.net/browse/MOBOFF-14651): Adjust translation while adding login PIN czech language  

## 24.99.1253.0
#### Shared
[MOBOFF-14405](https://ifsdev.atlassian.net/browse/MOBOFF-14405): Fix the issue of duplicate events appearing across the three platforms in calendar views  

#### Android
[MOBOFF-14502](https://ifsdev.atlassian.net/browse/MOBOFF-14502): Fix the cross button disappear regression issue from MOBOFF-13854  

## 24.99.1252.0
#### Shared
[MOBOFF-12949](https://ifsdev.atlassian.net/browse/MOBOFF-12949): Fix the calendar event duplicate issue after changing status  
[MOBOFF-14300](https://ifsdev.atlassian.net/browse/MOBOFF-14300): Fine turn query take values to improve CachedEntity download performance  
[MOBOFF-14384](https://ifsdev.atlassian.net/browse/MOBOFF-14384): Allow video files being shown only on gallery when setting is available  

#### Android
[MOBOFF-14568](https://ifsdev.atlassian.net/browse/MOBOFF-14568): Fixed Zebra VC8300 devices not supported for external microphone  

## 24.99.1251.0
#### Shared
[MOBOFF-14087](https://ifsdev.atlassian.net/browse/MOBOFF-14087): Read ActionTZRef for unbound actions that are timezone aware  
[MOBOFF-13862](https://ifsdev.atlassian.net/browse/MOBOFF-13862): DB lock issue fix in system test.  

## 24.99.1250.0
#### Android
[MOBOFF-13770](https://ifsdev.atlassian.net/browse/MOBOFF-13770): Fix Ad-HOC-Purchasing Menu Issue By Hiding Keybaord First Then Display Hidden Menu  
[MOBOFF-13854](https://ifsdev.atlassian.net/browse/MOBOFF-13854): Site name is missing from Title in the Android Scan It App when using the Portrait mode  

## 24.99.1249.0
#### Shared
[MOBOFF-14435](https://ifsdev.atlassian.net/browse/MOBOFF-14435): provide permission dialog for the home screen with metadata page  

#### Android
[MOBOFF-14113](https://ifsdev.atlassian.net/browse/MOBOFF-14113): Fix the heading title loading logic to address new work heading issue also New Step heading not showing issue as well  

## 24.99.1248.0
#### Shared
[MOBOFF-12435](https://ifsdev.atlassian.net/browse/MOBOFF-12435): Fix sort order reflect as none when sort order set by page designer  

#### Windows
[MOBOFF-12142](https://ifsdev.atlassian.net/browse/MOBOFF-12142): Term Events wont translate in the SF calendar fix  

## 24.99.1247.0
#### Shared
[MOBOFF-13944](https://ifsdev.atlassian.net/browse/MOBOFF-13944): Fix the incorrect date format display, such as the French date format, for long-running events.  

#### Android
[MOBOFF-9145](https://ifsdev.atlassian.net/browse/MOBOFF-9145): Person Name/Description is not shown in Person Lov in CRM Companion in Android  

## 24.99.1233.0
#### Shared
[MOBOFF-10502](https://ifsdev.atlassian.net/browse/MOBOFF-10502): Navigation with parameters from page designers is enabled  
[MOBOFF-14011](https://ifsdev.atlassian.net/browse/MOBOFF-14011): Missing null checks added to the syncmonitor classes in the respective platforms  

#### Android
[MOBOFF-11125](https://ifsdev.atlassian.net/browse/MOBOFF-11125): List performance enhancement and removal of push notifications and FCM nugets in Scan It  
[MOBOFF-14067](https://ifsdev.atlassian.net/browse/MOBOFF-14067): Improve the robustness of the OnMessageReceived method for Android push notification  

## 24.99.1232.0
#### Shared
[MOBOFF-13207](https://ifsdev.atlassian.net/browse/MOBOFF-13207): The downloaded image is displayed in the image tile  

#### iOS
[MOBOFF-13872](https://ifsdev.atlassian.net/browse/MOBOFF-13872): PrivacyInfo.xcprivacy file added for iOS  

## 24.99.1221.0
#### Android
[MOBOFF-14050](https://ifsdev.atlassian.net/browse/MOBOFF-14050): Fix crash due to fcm payload mismatch  

#### Windows
[MOBOFF-13692](https://ifsdev.atlassian.net/browse/MOBOFF-13692): Update development certificate for Windows  

## 24.99.1216.0
#### Shared
[MOBOFF-12383](https://ifsdev.atlassian.net/browse/MOBOFF-12383): Incorrect Ordering of Long Running Events in Calendar Agenda Mode - iOS, Android  
[MOBOFF-13693](https://ifsdev.atlassian.net/browse/MOBOFF-13693): The progress bar will be removed if the loading of the next step is terminated  

#### Android
[MOBOFF-13124](https://ifsdev.atlassian.net/browse/MOBOFF-13124): Display Long running event icon for Windows Syncfusion calendar views and adjust icon position for Android Syncfusion calendar views  

#### iOS
[MOBOFF-13787](https://ifsdev.atlassian.net/browse/MOBOFF-13787): Long running event icon is missing in Syncfusion calendar Day, Week, Workweek, Month in IOS.  
[MOBOFF-13872](https://ifsdev.atlassian.net/browse/MOBOFF-13872): PrivacyInfo.xcprivacy file added for iOS  

## 24.5.1205.0
#### Android
[MOBOFF-13191](https://ifsdev.atlassian.net/browse/MOBOFF-13191): Added Fcm V1 message handling for Remote Assistant  

## 24.99.1201.0
#### Shared
[MOBOFF-13670](https://ifsdev.atlassian.net/browse/MOBOFF-13670): The client FW string handling for server errors is flawed  

## 24.99.1200.0
#### Shared
[MOBOFF-12579](https://ifsdev.atlassian.net/browse/MOBOFF-12579): Added changes to support Boolean field required property on IOS and UWP  

#### Android
[MOBOFF-13757](https://ifsdev.atlassian.net/browse/MOBOFF-13757): Fix for crash on hidden menu item  

## 24.99.1199.0
#### Android
[MOBOFF-12606](https://ifsdev.atlassian.net/browse/MOBOFF-12606): Fix the status update issue occurring after modifying the status from the details page and then returning to the agenda view  
[MOBOFF-13519](https://ifsdev.atlassian.net/browse/MOBOFF-13519): Fix for hidden menu options not accessible on Ad hoc options  

#### Windows
[MOBOFF-13134](https://ifsdev.atlassian.net/browse/MOBOFF-13134): Agenda view should display correct viewp when changing the status of the appoinment  

## 24.99.1198.0
#### Shared
[MOBOFF-12994](https://ifsdev.atlassian.net/browse/MOBOFF-12994): Added support for columnvisible attribute in the fw  
[MOBOFF-13194](https://ifsdev.atlassian.net/browse/MOBOFF-13194): Time Zone Badge is shown longer in generated pdf report  
[MOBOFF-13529](https://ifsdev.atlassian.net/browse/MOBOFF-13529): The last question answer of a temp survey is editable when reopening the survey again.  
[MOBOFF-13624](https://ifsdev.atlassian.net/browse/MOBOFF-13624): Added regex validation to determine which labels are translatable.  

#### Windows
[MOBOFF-13134](https://ifsdev.atlassian.net/browse/MOBOFF-13134): Agenda view should display correct viewp when changing the status of the appoinment  

## 24.99.1196.0
#### Shared
[MOBOFF-13140](https://ifsdev.atlassian.net/browse/MOBOFF-13140): enable clearing value on non required enum fields.  
[MOBOFF-13370](https://ifsdev.atlassian.net/browse/MOBOFF-13370): added a null check for the trransitionManager date format method.  
[MOBOFF-13529](https://ifsdev.atlassian.net/browse/MOBOFF-13529): The last question answer of a temp survey is editable when reopening the survey again.  

#### Android
[MOBOFF-13378](https://ifsdev.atlassian.net/browse/MOBOFF-13378): A duplicated title can be seen in the Android Scan It App when using the Landscape mode  

## 24.99.1190.0
#### Shared
[MOBOFF-13015](https://ifsdev.atlassian.net/browse/MOBOFF-13015): Set onclose result to false when dismissing pdf preview using swipe gesture  

## 24.99.1186.0
#### Shared
[MOBOFF-13382](https://ifsdev.atlassian.net/browse/MOBOFF-13382): Added support for loop occurrence in dynamic assistance.  

## 24.99.1185.0
#### Shared
[MOBOFF-13382](https://ifsdev.atlassian.net/browse/MOBOFF-13382): Added support for loop occurrence in dynamic assistance.  

#### iOS
[MOBOFF-10067](https://ifsdev.atlassian.net/browse/MOBOFF-10067): Support for switching notification hub without re-activating device  

## 24.99.1184.0
#### Shared
[MOBOFF-13213](https://ifsdev.atlassian.net/browse/MOBOFF-13213): Custom enum in CF wont show the client value instead shows the db value fix  

## 24.99.1180.0
#### Shared
[MOBOFF-13382](https://ifsdev.atlassian.net/browse/MOBOFF-13382): Added support for loop occurrence in dynamic assistance.  

## 24.99.1179.0
#### Shared
[MOBOFF-13212](https://ifsdev.atlassian.net/browse/MOBOFF-13212): State Parked was mapped to light gray color which will not be null anymore  

## 24.99.1165.0
#### Shared
[MOBOFF-12867](https://ifsdev.atlassian.net/browse/MOBOFF-12867): set IdentityProvider.ClientId in TouchApp.LoadIdpAndAuthInfo() to ensure succesful access token renewal downstream  
[MOBOFF-13138](https://ifsdev.atlassian.net/browse/MOBOFF-13138): Resolved issue of missing toggle to confirm  

## 24.99.1161.0
#### Shared
[MOBOFF-13203](https://ifsdev.atlassian.net/browse/MOBOFF-13203): Added finding attributes for page and assistant command items  

#### Android
[MOBOFF-11125](https://ifsdev.atlassian.net/browse/MOBOFF-11125): Improvement to online list performance  

#### iOS
[MOBOFF-12417](https://ifsdev.atlassian.net/browse/MOBOFF-12417): Start time of the Long Running Event is incorrect in Calendar Agenda Mode IOS  
[MOBOFF-13158](https://ifsdev.atlassian.net/browse/MOBOFF-13158): Added clear option to clear date field values  

## 24.99.1156.0
#### Android
[MOBOFF-13146](https://ifsdev.atlassian.net/browse/MOBOFF-13146): Added null check on _activityStack to handle Google Play and Android Vitals warning  
[MOBOFF-13154](https://ifsdev.atlassian.net/browse/MOBOFF-13154): Added null check to address Android Vitals crash report  

## 24.99.1155.0
#### Shared
[MOBOFF-12923](https://ifsdev.atlassian.net/browse/MOBOFF-12923): Entity that has a custom field created and added to a card wont search online fix  

## 24.99.1153.0
#### Shared
[MOBOFF-13007](https://ifsdev.atlassian.net/browse/MOBOFF-13007): App crashes when attempting to mark a document class as a favorite and unfavorite of Android and Windows  
[MOBOFF-13079](https://ifsdev.atlassian.net/browse/MOBOFF-13079): Fixed Search not working on list views  
[MOBOFF-13088](https://ifsdev.atlassian.net/browse/MOBOFF-13088): Added loading animation for dynamic assistance step in UWP.  

## 24.99.1150.0
#### Shared
[MOBOFF-13035](https://ifsdev.atlassian.net/browse/MOBOFF-13035): MOBOFF-13035 Extract attributes from label for header Id  

#### Android
[MOBOFF-13108](https://ifsdev.atlassian.net/browse/MOBOFF-13108): Fixed App crash  
[MOBOFF-13108](https://ifsdev.atlassian.net/browse/MOBOFF-13108): Fixed App crash  

## 24.99.1148.0
#### Shared
[MOBOFF-12957](https://ifsdev.atlassian.net/browse/MOBOFF-12957): Fixed list content of material quick issue stock not shown with request task  

## 24.99.1147.0
#### Android
[MOBOFF-12684](https://ifsdev.atlassian.net/browse/MOBOFF-12684): Android text overlap issue fix.  

## 24.99.1146.0
#### Shared
[MOBOFF-13006](https://ifsdev.atlassian.net/browse/MOBOFF-13006): Revert EDF changes done under MOBOFF-12046  

## 24.99.1145.0
#### Shared
[MOBOFF-11334](https://ifsdev.atlassian.net/browse/MOBOFF-11334): Updating Nuget packages and fixing deprecated libraries  
[MOBOFF-11704](https://ifsdev.atlassian.net/browse/MOBOFF-11704): Allow deep linking into apps  
[MOBOFF-12686](https://ifsdev.atlassian.net/browse/MOBOFF-12686): Merged MOBZFW-1535 and MOBZFW-1641 to IFS Cloud with andriod and ios changes  

#### Windows
[MOBOFF-11981](https://ifsdev.atlassian.net/browse/MOBOFF-11981): Adjust Agenda mode long running event display by showing 2 lines and setting correct row span according to MOBOFF-11887 design.  

## 24.99.1144.0
#### iOS
[MOBOFF-12574](https://ifsdev.atlassian.net/browse/MOBOFF-12574): Screen auto scrolling to the bottom when select the long description field- Landscape view  

## 24.99.1143.0
#### Shared
[MOBOFF-12981](https://ifsdev.atlassian.net/browse/MOBOFF-12981): Offline functions sending timestamp parameters in Zulu format  

## 24.2.1142.0
#### Shared
[MOBOFF-12838](https://ifsdev.atlassian.net/browse/MOBOFF-12838): Added time zone support for assistant  
[MOBOFF-12945](https://ifsdev.atlassian.net/browse/MOBOFF-12945): Strip off translatable constant from Dynamic Assistant step description  

## 24.2.1140.0
#### Shared
[MOBOFF-12705](https://ifsdev.atlassian.net/browse/MOBOFF-12705): add support for structures  
[MOBOFF-12773](https://ifsdev.atlassian.net/browse/MOBOFF-12773): adding support for the displaytimezones property.  
[MOBOFF-12834](https://ifsdev.atlassian.net/browse/MOBOFF-12834): Made the time zone icon on cards clickable  
[MOBOFF-12934](https://ifsdev.atlassian.net/browse/MOBOFF-12934): The Time-Zone-Aware Request flag is added to the header of known time zone bound actions  

#### iOS
[MOBOFF-12857](https://ifsdev.atlassian.net/browse/MOBOFF-12857): The calendar icon is visible for UTC fields.  

## 24.99.1138.0
#### Shared
[MOBOFF-12596](https://ifsdev.atlassian.net/browse/MOBOFF-12596): Get entity default media and add to the HTML/PDF report as a logo  

#### Android
[MOBOFF-12684](https://ifsdev.atlassian.net/browse/MOBOFF-12684): Android text overlap issue fix.  

#### iOS
[MOBOFF-12875](https://ifsdev.atlassian.net/browse/MOBOFF-12875): File name label of the uploaded images are not fully visible  

#### Windows
[MOBOFF-11981](https://ifsdev.atlassian.net/browse/MOBOFF-11981): Fix Agenda long run display issue by MOBOFF-11887 and improve multiple year display  
[MOBOFF-12924](https://ifsdev.atlassian.net/browse/MOBOFF-12924): Fix UWP build issue caused by iText7 and MSTeams  

## 24.99.1130.0
#### Shared
[MOBOFF-12906](https://ifsdev.atlassian.net/browse/MOBOFF-12906): Resolved missing attributes from navigate execute call args  

## 24.99.1129.0
#### Shared
[MOBOFF-12809](https://ifsdev.atlassian.net/browse/MOBOFF-12809): Convert html Service Report to pdf format  

## 24.99.1128.0
#### Shared
[MOBOFF-12648](https://ifsdev.atlassian.net/browse/MOBOFF-12922): New System test step in the prepare data setup to enable Custom fields FW if it's not yet set 

## 24.99.1128.0
#### Shared
[MOBOFF-12648](https://ifsdev.atlassian.net/browse/MOBOFF-12648): Timezone System Tests 

## 24.99.1128.0
#### Shared
[MOBOFF-12520](https://ifsdev.atlassian.net/browse/MOBOFF-12520): Fix Duplicate search result using hashset  
[MOBOFF-12742](https://ifsdev.atlassian.net/browse/MOBOFF-12742): Fixed Enum filter exception on iOS and Android  

## 24.99.1110.0
#### Shared
[MOBOFF-12525](https://ifsdev.atlassian.net/browse/MOBOFF-12525): Fixed Time Zone icon placement in all platforms  
[MOBOFF-1760](https://ifsdev.atlassian.net/browse/MOBOFF-1760): The process cannot access the file database_1.db because it is being used by another process.  
[MOBOFF-4043](https://ifsdev.atlassian.net/browse/MOBOFF-4043): Generate select attributes from client  

#### Android
[MOBOFF-12425](https://ifsdev.atlassian.net/browse/MOBOFF-12425): Fixed an extra card is visible when loading and moving back.  

## 24.99.1107.0
#### Shared
[MOBOFF-12710](https://ifsdev.atlassian.net/browse/MOBOFF-12710): Datetime parameters related to the OData function format fix  

## 24.99.1105.0
#### Shared
[MOBOFF-11981](https://ifsdev.atlassian.net/browse/MOBOFF-11981): Start Time is Neglected in Syncfusion Modes for Start Dates of Long-Running Events- Calendar Windows  
[MOBOFF-12440](https://ifsdev.atlassian.net/browse/MOBOFF-12440): Sending an ISO time zone ID instead of GMT.  
[MOBOFF-1760](https://ifsdev.atlassian.net/browse/MOBOFF-1760): The process cannot access the file database_1.db because it is being used by another process.  
[MOBOFF-12046](https://ifsdev.atlassian.net/browse/MOBOFF-12046): New logic introducing HasSurveyChanged in FndDynamicNextStep  

#### Windows
[MOBOFF-11981](https://ifsdev.atlassian.net/browse/MOBOFF-11981): Display the correct long term event end time for Day, Week, Month etc mode.  
[MOBOFF-11981](https://ifsdev.atlassian.net/browse/MOBOFF-11981): Fix long term events display according to design for agenda view and display start time and end time along with date for other modes (Month, Week, WorkWeek, Day)  
[MOBOFF-11981](https://ifsdev.atlassian.net/browse/MOBOFF-11981): Fixe build error by removing the semicolon typo which passed local build somehow.  
[MOBOFF-11981](https://ifsdev.atlassian.net/browse/MOBOFF-11981): Readjust implementation and add change explanation  
[MOBOFF-12669](https://ifsdev.atlassian.net/browse/MOBOFF-12669): Show progress ring when navigating to survey in win  

## 24.99.1102.0
#### Shared
[MOBOFF-12674](https://ifsdev.atlassian.net/browse/MOBOFF-12674): added a new viewstate to read user interaction.  
[MOBOFF-12680](https://ifsdev.atlassian.net/browse/MOBOFF-12680): Making mobile client backward compatible.  

## 24.1.1109.0
#### Shared
[MOBOFF-12070](https://ifsdev.atlassian.net/browse/MOBOFF-12070): Reading new metadata and adding utility methods to use that metadata.  
[MOBOFF-12075](https://ifsdev.atlassian.net/browse/MOBOFF-12075): Adding the new request header for known time zone servicers.  
[MOBOFF-12080](https://ifsdev.atlassian.net/browse/MOBOFF-12080): Data sync changes for known time zone.  
[MOBOFF-12095](https://ifsdev.atlassian.net/browse/MOBOFF-12095): Define the behavior for timestamp fields that are in a known time zone entity.  

#### Android
[MOBOFF-12100](https://ifsdev.atlassian.net/browse/MOBOFF-12100): Implement UI controllers in Android for known time zone.  

#### iOS
[MOBOFF-12110](https://ifsdev.atlassian.net/browse/MOBOFF-12110): UI for known time zone  

#### Windows
[MOBOFF-12105](https://ifsdev.atlassian.net/browse/MOBOFF-12105): Implemented UI controlls in Windows for known time zone.  

## 24.99.1101.0
#### Shared
[MOBOFF-11981](https://ifsdev.atlassian.net/browse/MOBOFF-11981): Start Time is Neglected in Syncfusion Modes for Start Dates of Long-Running Events- Calendar Windows  
[MOBOFF-1760](https://ifsdev.atlassian.net/browse/MOBOFF-1760): The process cannot access the file database_1.db because it is being used by another process.  

#### Windows
[MOBOFF-11887](https://ifsdev.atlassian.net/browse/MOBOFF-11887): Fixed issues with events display in Calendar  
[MOBOFF-12003](https://ifsdev.atlassian.net/browse/MOBOFF-12003): Fixed Calendar[Windows] - Status change is not working for search event in Agenda View  
[MOBOFF-12361](https://ifsdev.atlassian.net/browse/MOBOFF-12361): Fixed Calendar [windows] - Filter is not working on agenda view (Regression)  

## 24.99.1083.0
#### Shared
[MOBOFF-12046](https://ifsdev.atlassian.net/browse/MOBOFF-12046): Fix to allow changing the step sequence in dynamic assistants  

#### Android
[MOBOFF-12378](https://ifsdev.atlassian.net/browse/MOBOFF-12378): Changed uses-features for camera to avoid the item being merged by 3rd party library  

#### iOS
[MOBOFF-11999](https://ifsdev.atlassian.net/browse/MOBOFF-11999): Fixed Image field getting hidden after uplaoding an image.  
[MOBOFF-12444](https://ifsdev.atlassian.net/browse/MOBOFF-12444): Fixed text field disappearing issue while typing  

## 23.99.1079.0
#### Shared
[MOBOFF-11125](https://ifsdev.atlassian.net/browse/MOBOFF-11125): App crash on field focus fixed  

#### Android
[MOBOFF-12379](https://ifsdev.atlassian.net/browse/MOBOFF-12379): Fixed null reference exception in MWO Maintenance (OpenIdAuthenticator)  
[MOBOFF-12380](https://ifsdev.atlassian.net/browse/MOBOFF-12380): Fixed null reference exception in MWO Maintanance (DialogService)  

## 23.99.1075.0
#### Shared
[MOBOFF-10860](https://ifsdev.atlassian.net/browse/MOBOFF-10860): Added globe icon UI support to Record Fluid Details page  
[MOBOFF-12046](https://ifsdev.atlassian.net/browse/MOBOFF-12046): Fix to allow changing the step sequence in dynamic assistants  
[MOBOFF-12400](https://ifsdev.atlassian.net/browse/MOBOFF-12400): Remove userid when calling authentication url  
[MOBOFF-1760](https://ifsdev.atlassian.net/browse/MOBOFF-1760): The process cannot access the file database_1.db' because it is being used by another process.  
[MOBOFF-3353](https://ifsdev.atlassian.net/browse/MOBOFF-3353): Retrieve company logo from server and add to the HTML/PDF reports  

#### Android
[MOBOFF-12019](https://ifsdev.atlassian.net/browse/MOBOFF-12019): Calendar Control [Android] - Current day button is not clear the search results and not execute to the current date on the agenda view  
[MOBOFF-12360](https://ifsdev.atlassian.net/browse/MOBOFF-12360): Fixed Null reference exception in MWO Maintenance.  
[MOBOFF-12413](https://ifsdev.atlassian.net/browse/MOBOFF-12413): Fixed No field to add New Measurements in Report All page, Measurements.  

## 23.99.1066.0
#### Shared
[MOBOFF-12124](https://ifsdev.atlassian.net/browse/MOBOFF-12124): App crash when timezone icon clicked in Bogota timezone fix  

#### Android
[MOBOFF-11490](https://ifsdev.atlassian.net/browse/MOBOFF-11490): Time indicator not placed correctly when the long running event starts on the current day  
[MOBOFF-11721](https://ifsdev.atlassian.net/browse/MOBOFF-11721): Fixed Android app shows extra space in the ScanIt process list  
[MOBOFF-11833](https://ifsdev.atlassian.net/browse/MOBOFF-11833): Save button is not visible on editable page in Android 6  

## 23.99.1059.0
#### Shared
[MOBOFF-12015](https://ifsdev.atlassian.net/browse/MOBOFF-12015): Changed API call sequence when returning signature documents  

#### Windows
[MOBOFF-11779](https://ifsdev.atlassian.net/browse/MOBOFF-11779): Fix Calendar Control [Windows] - Random dates are displayed in blue text in Agenda view.  

## 23.99.1048.0
#### Windows
[MOBOFF-11792](https://ifsdev.atlassian.net/browse/MOBOFF-11792): Fixed Cannot access time zone info popup on list view  

## 23.99.1047.0
#### iOS
[MOBOFF-11968](https://ifsdev.atlassian.net/browse/MOBOFF-11968): [23R2][GA][Validation Test][IOS] - Calendar events are on incorrect time slots  
[MOBOFF-11972](https://ifsdev.atlassian.net/browse/MOBOFF-11972): App Crashes on Status Change in Calendar Events - iOS  
[MOBOFF-11976](https://ifsdev.atlassian.net/browse/MOBOFF-11976): Calendar Agenda Mode- Incorrect Time Line Display  

#### Windows
[MOBOFF-12003](https://ifsdev.atlassian.net/browse/MOBOFF-12003): Calendar[Windows] - Status change is not working for search event in Agenda View  

## 23.99.1032.0
#### Shared
[MOBOFF-11490](https://ifsdev.atlassian.net/browse/MOBOFF-11490): Time indicator not placed correctly when the long running event starts on the current day  
[MOBOFF-11632](https://ifsdev.atlassian.net/browse/MOBOFF-11632): Fixed the work details page always open with Save/Cancel after adding custom boolean fields.  
[MOBOFF-11751](https://ifsdev.atlassian.net/browse/MOBOFF-11751): Fixed ScanIt -When press Enter key does not trigger Save command  

#### Android
[MOBOFF-11865](https://ifsdev.atlassian.net/browse/MOBOFF-11865): Fixed Calendar Card Panels Not Opening Properly After Searching  

## 23.99.1023.0
#### Android
[MOBOFF-11665](https://ifsdev.atlassian.net/browse/MOBOFF-11665): Cancel button is not visible on editable page  

#### iOS
[MOBOFF-11590](https://ifsdev.atlassian.net/browse/MOBOFF-11590): Fix for crash on ElementListView navigation  
[MOBOFF-11938](https://ifsdev.atlassian.net/browse/MOBOFF-11938): Calendar Control [iOS] - Status change is not updated in Agenda View Calendar Event  

#### Windows
[MOBOFF-11857](https://ifsdev.atlassian.net/browse/MOBOFF-11857): Fixed unwanted editor option when adding a camera image in Windows  
[MOBOFF-11883](https://ifsdev.atlassian.net/browse/MOBOFF-11883): Fixed Inconsistent Highlighting of Filter Options in Calendar- Windows  

## 23.99.1021.0
#### iOS
[MOBOFF-11735](https://ifsdev.atlassian.net/browse/MOBOFF-11735): Html text will be shown aligned to the app font.  
[MOBOFF-11878](https://ifsdev.atlassian.net/browse/MOBOFF-11878): Calendar displays all events after searching and opening the FAB menu- iOS  
[MOBOFF-7163](https://ifsdev.atlassian.net/browse/MOBOFF-7163): IOS navigation back multiple times issue fix.  

#### Windows
[MOBOFF-11631](https://ifsdev.atlassian.net/browse/MOBOFF-11631): Fixed Picture size in MWO survey answer causes sync process failing  

## 23.99.1020.0
#### iOS
[MOBOFF-11804](https://ifsdev.atlassian.net/browse/MOBOFF-11804): Calendar - App crashes while searching in iOS  

## 23.99.1018.0
#### Shared
[MOBOFF-11697](https://ifsdev.atlassian.net/browse/MOBOFF-11697): added recently used item filter for LOV filtering  

#### Windows
[MOBOFF-11631](https://ifsdev.atlassian.net/browse/MOBOFF-11631): Fixed Picture size in MWO survey answer causes sync process failing  
[MOBOFF-11653](https://ifsdev.atlassian.net/browse/MOBOFF-11653): Fix for app crash when navigating back from detailed page view of first event  

## 23.99.1017.0
#### Shared
[MOBOFF-11712](https://ifsdev.atlassian.net/browse/MOBOFF-11712): Calendar Event Status Badges Not Updating Properly- Android (Regression)  

## 23.99.1016.0
#### Shared
[MOBOFF-10403](https://ifsdev.atlassian.net/browse/MOBOFF-10403): Update syncfusion packages to 23.1.42  

#### Windows
[MOBOFF-11309](https://ifsdev.atlassian.net/browse/MOBOFF-11309): Fixed issue of LOV field with UTC timezone not selectable on Windows App  
[MOBOFF-11570](https://ifsdev.atlassian.net/browse/MOBOFF-11570): Fixed Inconsistent Dark Mode Colors in Calendar Control  

## 23.99.1015.0
#### iOS
[MOBOFF-2694](https://ifsdev.atlassian.net/browse/MOBOFF-2694): iOS-Field Focus issue Find Part/Ad-hoc  

## 23.99.1013.0
#### Shared
[MOBOFF-10815](https://ifsdev.atlassian.net/browse/MOBOFF-10815): Fields with hasHtml font correction  
[MOBOFF-11522](https://ifsdev.atlassian.net/browse/MOBOFF-11522): Command position change  
[MOBOFF-11600](https://ifsdev.atlassian.net/browse/MOBOFF-11600): Update to HERE maps V3 API  

#### iOS
[MOBOFF-11699](https://ifsdev.atlassian.net/browse/MOBOFF-11699): QLPreviewController dismiss on swipe down fix.  
[MOBOFF-2694](https://ifsdev.atlassian.net/browse/MOBOFF-2694): iOS-Field Focus issue Find Part/Ad-hoc  
[MOBOFF-11335](https://ifsdev.atlassian.net/browse/MOBOFF-11335): Fix timeline display for iOS with 12-hour formatting  

## 23.99.1011.0
#### iOS
[MOBOFF-11649](https://ifsdev.atlassian.net/browse/MOBOFF-11649): Fixed Search Bar Persistence in Syncfusion Modes  

## 23.99.1010.0
#### Shared
[MOBOFF-11641](https://ifsdev.atlassian.net/browse/MOBOFF-11641): Fixed opening the time zone information on list view gives an Error-Android  

#### Android
[MOBOFF-11287](https://ifsdev.atlassian.net/browse/MOBOFF-11287): Fixed Current Time Indicator is missing when there is only a long-running activity scheduled for the day(Agenda Mode)- Android  
[MOBOFF-10377](https://ifsdev.atlassian.net/browse/MOBOFF-10377): Fixed IFS mobile card view and list view have unnecessary extra padding.  

#### iOS
[MOBOFF-10696](https://ifsdev.atlassian.net/browse/MOBOFF-10696): Removed asterisk for boolean required field as a boolean always has a value  
[MOBOFF-11469](https://ifsdev.atlassian.net/browse/MOBOFF-11469): Incorrect Current Time Indicator in Calendar Agenda Mode - iOS  
[MOBOFF-11497](https://ifsdev.atlassian.net/browse/MOBOFF-11497): Ordering Agenda events in the Calendar fix  
[MOBOFF-8225](https://ifsdev.atlassian.net/browse/MOBOFF-8225): Fixed wrong color shown on stage indicator of process viewer  

#### Windows
[MOBOFF-11633](https://ifsdev.atlassian.net/browse/MOBOFF-11633): Fixed Click Add button on Process viewer page caused Windows app crashed  

## 23.99.1009.0
#### Shared
[MOBOFF-11110](https://ifsdev.atlassian.net/browse/MOBOFF-11110): calendar - Agenda view is scroll to the top when changing the status of the appoinment  
[MOBOFF-11325](https://ifsdev.atlassian.net/browse/MOBOFF-11325): Handling unexpected server versions.  
[MOBOFF-10638](https://ifsdev.atlassian.net/browse/MOBOFF-10638): Fixed query issues with multiple map pins  
[MOBOFF-11560](https://ifsdev.atlassian.net/browse/MOBOFF-11560): Fixed Incorrect 24-Hour Time Formatting in Calendar.  
[MOBOFF-9531](https://ifsdev.atlassian.net/browse/MOBOFF-9531): Retry uploading sig items with non validation errors  

#### Android
[MOBOFF-11366](https://ifsdev.atlassian.net/browse/MOBOFF-11366): Fixed Device logs cannot be shared on android 13 due storage permission issue  

#### iOS
[MOBOFF-10471](https://ifsdev.atlassian.net/browse/MOBOFF-10471): Request to enable elements to capture in iOS DOM and assign �Accessibility Ids� for those  
[MOBOFF-11081](https://ifsdev.atlassian.net/browse/MOBOFF-11081): Replace GUID with filenameprefix in signature file names  
[MOBOFF-11579](https://ifsdev.atlassian.net/browse/MOBOFF-11579): Fixed FAB menu is not displayed with the search results  

#### Windows
[MOBOFF-11612](https://ifsdev.atlassian.net/browse/MOBOFF-11612): Fixed Title is not getting prepopulated with file name when adding a media  

## 23.99.1005.0
#### Shared
[MOBOFF-10294](https://ifsdev.atlassian.net/browse/MOBOFF-10294): Utc Icon is updated dynamically  

#### Android
[MOBOFF-10170](https://ifsdev.atlassian.net/browse/MOBOFF-10170): IFS ScanIt Error - "IFS Scan It closed because this app has a bug"  

## 23.99.1004.0
#### Shared
[MOBOFF-11374](https://ifsdev.atlassian.net/browse/MOBOFF-11374): Calendar [Android, iOS] - Filtering not working in agenda view after status change (Regression)  
[MOBOFF-11564](https://ifsdev.atlassian.net/browse/MOBOFF-11564): Prevent login required dialog showing again soon after user logs in  

#### Android
[MOBOFF-11331](https://ifsdev.atlassian.net/browse/MOBOFF-11331): Fixed Behavior mismatch for Sort Direction field in android app (Icon and List view)  

#### iOS
[MOBOFF-11272](https://ifsdev.atlassian.net/browse/MOBOFF-11272): iOS, Home button doesn't recognize todays events in the Agenda  

## 23.99.1002.0
#### Shared
[MOBOFF-10906](https://ifsdev.atlassian.net/browse/MOBOFF-10906): Dynamic labels are enabled  
[MOBOFF-10906](https://ifsdev.atlassian.net/browse/MOBOFF-10906): Dynamic lables on card is enabled  

#### iOS
[MOBOFF-10399](https://ifsdev.atlassian.net/browse/MOBOFF-10399): Remove additional local alert message when app in background  

#### Windows
[MOBOFF-11488](https://ifsdev.atlassian.net/browse/MOBOFF-11488): unnecessary focus removing issue is fixed.  

## 23.99.1001.0
#### Shared
[MOBOFF-11098](https://ifsdev.atlassian.net/browse/MOBOFF-11098): Menu items will be update accoding to page designer  
[MOBOFF-11362](https://ifsdev.atlassian.net/browse/MOBOFF-11362): Fix for app crash when timezone dialog pops with no information  
[MOBOFF-9725](https://ifsdev.atlassian.net/browse/MOBOFF-9725): Nuget packages source change  
[MOBOFF-11477](https://ifsdev.atlassian.net/browse/MOBOFF-11477): Pause transaction sync service if there are pending digital signature transactions  

#### Android
[MOBOFF-10651](https://ifsdev.atlassian.net/browse/MOBOFF-10651): Resolve call ended crashing in remote assistant function  
[MOBOFF-11282](https://ifsdev.atlassian.net/browse/MOBOFF-11282): ScanIt Cloud Application Not Responding for Large data volume  
[MOBOFF-11378](https://ifsdev.atlassian.net/browse/MOBOFF-11378): Calendar app freezing after closing the e-form- Android  

#### iOS
[MOBOFF-10471](https://ifsdev.atlassian.net/browse/MOBOFF-10471): Request to enable elements to capture in iOS DOM and assign �Accessibility Ids� for those  
[MOBOFF-11317](https://ifsdev.atlassian.net/browse/MOBOFF-11317): Fix for search icon is disappeared when coming back from details screen or "Show in Map" command  
[MOBOFF-8576](https://ifsdev.atlassian.net/browse/MOBOFF-8576): Difference in available options while viewing documents - iOS16  

#### Windows
[MOBOFF-10846](https://ifsdev.atlassian.net/browse/MOBOFF-10846): Fixed Copy paste using mouse issue  
[MOBOFF-10986](https://ifsdev.atlassian.net/browse/MOBOFF-10986): Fixed Description and Long Description fields border highlight issue  
[MOBOFF-11396](https://ifsdev.atlassian.net/browse/MOBOFF-11396): Fixed Contact Widget- Poor visibility of customer details in dark mode- Windows  

## 23.99.987.0
#### Windows
[MOBOFF-11276](https://ifsdev.atlassian.net/browse/MOBOFF-11276): Fixed Calendar - "All events" option is not highlighted when the filter option is removed.  

## 23.99.986.0
#### iOS
[MOBOFF-8417](https://ifsdev.atlassian.net/browse/MOBOFF-8417): Overlapping text on Badge in Object List  

## 23.99.983.0
#### Android
[MOBOFF-11223](https://ifsdev.atlassian.net/browse/MOBOFF-11223): Fixed Tab Title Disappears After Opening Media/Document and Returning in Android.  

## 23.99.980.0
#### Shared
[MOBOFF-11018](https://ifsdev.atlassian.net/browse/MOBOFF-11018): Added support for missing granite icons  

#### iOS
[MOBOFF-11280](https://ifsdev.atlassian.net/browse/MOBOFF-11280): Fix for custom command buttons not showing above keyboard popup  

## 23.99.974.0
#### iOS
[MOBOFF-10914](https://ifsdev.atlassian.net/browse/MOBOFF-10914): Fix for html field not populating when navigating back and forth  

## 23.9.972.0
#### Shared
[MOBOFF-10965](https://ifsdev.atlassian.net/browse/MOBOFF-10965): Support signing online flows  

#### Android
[MOBOFF-10313](https://ifsdev.atlassian.net/browse/MOBOFF-10313): Push Notifications not working on android 13  

## 23.99.969.0
#### Shared
[MOBOFF-11146](https://ifsdev.atlassian.net/browse/MOBOFF-11146): Incorrect Current Time Indicator in Agenda Mode for Long Running Activities - iOS  

#### iOS
[MOBOFF-11182](https://ifsdev.atlassian.net/browse/MOBOFF-11182): Fix for iOS new color tokens wont show  

## 23.99.968.0
#### Shared
[MOBOFF-10660](https://ifsdev.atlassian.net/browse/MOBOFF-10660): Removing the changes.  
[MOBOFF-10965](https://ifsdev.atlassian.net/browse/MOBOFF-10965): Close pdf preview dialog after upload of signature records is complete  
[MOBOFF-11110](https://ifsdev.atlassian.net/browse/MOBOFF-11110): fixed  scroll to the top when changing the status of the appoinment  
[MOBOFF-5973](https://ifsdev.atlassian.net/browse/MOBOFF-5973): Send positive client keys if server keys are not available  

#### iOS
[MOBOFF-10914](https://ifsdev.atlassian.net/browse/MOBOFF-10914): Fix for html field not populating when navigating back and forth  
[MOBOFF-11211](https://ifsdev.atlassian.net/browse/MOBOFF-11211): Resolve agenda crashing on calendar view  

## 23.99.966.0
#### iOS
[MOBOFF-10705](https://ifsdev.atlassian.net/browse/MOBOFF-10705): Remove extra blank on rows  

## 23.99.965.0
#### Shared
[MOBOFF-10660](https://ifsdev.atlassian.net/browse/MOBOFF-10660): Commands with navigate will shown on the related page list.  
[MOBOFF-10723](https://ifsdev.atlassian.net/browse/MOBOFF-10723): In native List view there is no Globe icon in front of the time stamp(Local) to view the UTC time-reopened  

#### iOS
[MOBOFF-10743](https://ifsdev.atlassian.net/browse/MOBOFF-10743): Calendar Control [iOS] - Current timeline should be hidden in search results  

## 23.99.963.0
#### Shared
[MOBOFF-11089](https://ifsdev.atlassian.net/browse/MOBOFF-11089): Calendar - Tap on calendar event is not working after using search option  
[MOBOFF-9993](https://ifsdev.atlassian.net/browse/MOBOFF-9993): Removed unnecessary label from login page when showing error message.  

#### Android
[MOBOFF-10809](https://ifsdev.atlassian.net/browse/MOBOFF-10809): selected tab title is visible  

#### iOS
[MOBOFF-10747](https://ifsdev.atlassian.net/browse/MOBOFF-10747): Calendar Control [iOS] - Current day button is not clear the search results and not execute to the current date on the agenda view  

## 23.99.962.0
#### Shared
[MOBOFF-2933](https://ifsdev.atlassian.net/browse/MOBOFF-2933): Time not displayed according to device time settings fix  

#### iOS
[MOBOFF-10780](https://ifsdev.atlassian.net/browse/MOBOFF-10780): List is not populated correctly when navigate back and select a different list in IOS  

## 23.99.961.0
#### Shared
[MOBOFF-9711](https://ifsdev.atlassian.net/browse/MOBOFF-9711): Added color tokens to align cloud with IFS Design System colors  

## 23.99.960.0
#### Shared
[MOBOFF-10522](https://ifsdev.atlassian.net/browse/MOBOFF-10522): Measurement Inheritance - Shared code implementation for all 3 platforms  
[MOBOFF-10723](https://ifsdev.atlassian.net/browse/MOBOFF-10723): In native List view there is no Globe icon in front of the time stamp(Local) to view the UTC time.  

#### Android
[MOBOFF-10944](https://ifsdev.atlassian.net/browse/MOBOFF-10944): Android, the Agenda view in the calendar crashes the app  

#### Windows
[MOBOFF-10990](https://ifsdev.atlassian.net/browse/MOBOFF-10990): Time zone information not visible on pop up when on dark mode - Windows  

## 23.99.958.0
#### Shared
[MOBOFF-5973](https://ifsdev.atlassian.net/browse/MOBOFF-5973): Send null KeyRef if server keys are not available during signed document upload  
[MOBOFF-10403](https://ifsdev.atlassian.net/browse/MOBOFF-10403): Update syncfusion packages to latest version  
[MOBOFF-9851](https://ifsdev.atlassian.net/browse/MOBOFF-9851): Mobile Maintenance - Rebranding  

#### Android
[MOBOFF-5151](https://ifsdev.atlassian.net/browse/MOBOFF-5151): Login issue after credentials expired - ScanIt Android client  

## 23.99.957.0
#### Shared
[MOBOFF-2933](https://ifsdev.atlassian.net/browse/MOBOFF-2933): Time not displayed according to device time settings fix  
[MOBOFF-3010](https://ifsdev.atlassian.net/browse/MOBOFF-3010): Use Granite.Tokens and Granite.Icons NuGet packages  
[MOBOFF-10522](https://ifsdev.atlassian.net/browse/MOBOFF-10522): Measurement inheritance - Shared and Windows implementation  
[MOBOFF-10881](https://ifsdev.atlassian.net/browse/MOBOFF-10881): Make eSig feature backwards compatible with 23r1 and below  

## 23.99.956.0
#### Shared
[MOBOFF-9857](https://ifsdev.atlassian.net/browse/MOBOFF-9857): Users can visit any type of page that added via aurena web.  

## 23.99.955.0
#### Shared
[MOBOFF-10781](https://ifsdev.atlassian.net/browse/MOBOFF-10781): Fixes to json meta content generation  

## 23.99.954.0
#### Shared
[MOBOFF-9535](https://ifsdev.atlassian.net/browse/MOBOFF-9535): Clear error messages when conditional field value changes  

## 23.99.950.0
#### iOS
[MOBOFF-10797](https://ifsdev.atlassian.net/browse/MOBOFF-10797): Fix crashing issue if user clicks search button  
[MOBOFF-10856](https://ifsdev.atlassian.net/browse/MOBOFF-10856): Fix for dialog response issues on iPad OS below 16.3  

## 23.99.949.0
#### iOS
[MOBOFF-6692](https://ifsdev.atlassian.net/browse/MOBOFF-6692): "Show in Map" command invisibility fix when search view enables  

## 23.99.948.0
#### Android
[MOBOFF-10763](https://ifsdev.atlassian.net/browse/MOBOFF-10763): Fix for invisible icons and text in status bar  

## 23.99.947.0
#### Shared
[MOBOFF-10781](https://ifsdev.atlassian.net/browse/MOBOFF-10781): Fixed json content to match webclient generated json  

## 23.99.946.0
#### Shared
[MOBOFF-10759](https://ifsdev.atlassian.net/browse/MOBOFF-10759): Resolved clients publish error  

## 23.99.945.0
#### Android
[MOBOFF-10318](https://ifsdev.atlassian.net/browse/MOBOFF-10318): Removed the fixed height on cards.  
[MOBOFF-10498](https://ifsdev.atlassian.net/browse/MOBOFF-10498): Fixed request read_external_storage for Android 12 and below  

## 23.99.942.0
#### Shared
[MOBOFF-10342](https://ifsdev.atlassian.net/browse/MOBOFF-10342): Fix SonarCloud warning by replacing an extraneous Union of Hashset with parameterized initialization  
[MOBOFF-10665](https://ifsdev.atlassian.net/browse/MOBOFF-10665): Include file ext when returning pdf and json files in signature  
[MOBOFF-9541](https://ifsdev.atlassian.net/browse/MOBOFF-9541): Odata expressions will be supported on the LOV  
[MOBOFF-9546](https://ifsdev.atlassian.net/browse/MOBOFF-9546): Enum filter is enabled.  
[MOBOFF-3119](https://ifsdev.atlassian.net/browse/MOBOFF-3119): UI changes  
[MOBOFF-10537](https://ifsdev.atlassian.net/browse/MOBOFF-10537): Change label for online switch  

#### Android
[MOBOFF-5151](https://ifsdev.atlassian.net/browse/MOBOFF-5151): Login issue after credentials expired - ScanIt Android client  

#### iOS
[MOBOFF-6692](https://ifsdev.atlassian.net/browse/MOBOFF-6692): "Show in Map" command invisibility fix when search view enables  

#### Windows
[MOBOFF-10494](https://ifsdev.atlassian.net/browse/MOBOFF-10494): Fixed how we check for amount of available free storage on Win  

## 23.99.941.0
#### iOS
[MOBOFF-2933](https://ifsdev.atlassian.net/browse/MOBOFF-2933): Time not displayed according to device time settings fix  

## 23.99.940.0
#### Shared
[MOBOFF-10682](https://ifsdev.atlassian.net/browse/MOBOFF-10682): Fix for bound actions.  

#### Android
[MOBOFF-10498](https://ifsdev.atlassian.net/browse/MOBOFF-10498): Fix to check read and write permissions correctly according to API level  

## 23.99.935.0
#### Shared
[MOBOFF-10528](https://ifsdev.atlassian.net/browse/MOBOFF-10528): Online actions with date parameter issue is fixed  

## 23.99.934.0
#### iOS
[MOBOFF-10381](https://ifsdev.atlassian.net/browse/MOBOFF-10381): Wrong day is displayed when returning to Day view [iOS]  
[MOBOFF-5220](https://ifsdev.atlassian.net/browse/MOBOFF-5220): When iPad orientation is changing, card details are overlapping  

#### Windows
[MOBOFF-4494](https://ifsdev.atlassian.net/browse/MOBOFF-4494): Current day button is hidden when windows responsiveness changed fix  

## 23.99.932.0
#### Shared
[MOBOFF-5609](https://ifsdev.atlassian.net/browse/MOBOFF-5609): Fixed zoom issues of Show Map function with multiple markers  
[MOBOFF-9741](https://ifsdev.atlassian.net/browse/MOBOFF-9741): Apply security permission settings to dynamic menu items  

#### iOS
[MOBOFF-10478](https://ifsdev.atlassian.net/browse/MOBOFF-10478): Fix For unnecessary action on syncfusion dialogs  

## 23.99.933.0
#### Shared
[MOBOFF-5821](https://ifsdev.atlassian.net/browse/MOBOFF-5821): RELEASE TEST_EA Calendar Control [Android, iOS] - Save current changes in the calendar page is not working  
[MOBOFF-8897](https://ifsdev.atlassian.net/browse/MOBOFF-8897): Changed return type of sign document to a structure containg State and Guid  
[MOBOFF-3120](https://ifsdev.atlassian.net/browse/MOBOFF-3120): IFS defined UI color changes for groups  
[MOBOFF-5485](https://ifsdev.atlassian.net/browse/MOBOFF-5485): Added require download for video media file  

#### iOS
[MOBOFF-5651](https://ifsdev.atlassian.net/browse/MOBOFF-5651): Internal sporage space checker when uploading media files  

## 23.99.931.0
#### Shared
[MOBOFF-5554](https://ifsdev.atlassian.net/browse/MOBOFF-5554): Event Highlighting Disappearing - Calendar Control  
[MOBOFF-7923](https://ifsdev.atlassian.net/browse/MOBOFF-7923): filtering from reference attributes in an online screen is enabled.  

## 23.99.930.0
#### Shared
[MOBOFF-9535](https://ifsdev.atlassian.net/browse/MOBOFF-9535): Display required indicator for fields conditionally  

#### Windows
[MOBOFF-10425](https://ifsdev.atlassian.net/browse/MOBOFF-10425): Work task status is not shown in day view on Windows - Calendar Control  

## 23.99.929.0
#### Shared
[MOBOFF-10296](https://ifsdev.atlassian.net/browse/MOBOFF-10296): Create sync_rule system entity  
[MOBOFF-9465](https://ifsdev.atlassian.net/browse/MOBOFF-9465): Download for ondemand is done  

#### Android
[MOBOFF-9485](https://ifsdev.atlassian.net/browse/MOBOFF-9485): Added LOV on-demand download for Android  

#### iOS
[MOBOFF-9475](https://ifsdev.atlassian.net/browse/MOBOFF-9475): Added support of downloading OnDemand entity for iOS  

## 23.99.928.0
#### Shared
[MOBOFF-10073](https://ifsdev.atlassian.net/browse/MOBOFF-10073): Calendar Control [Windows] - Status change is not updated in Agenda View Calendar Event  

#### Android
[MOBOFF-5821](https://ifsdev.atlassian.net/browse/MOBOFF-5821): RELEASE TEST_EA Calendar Control [Android, iOS] - Save current changes in the calendar page is not working  

## 23.99.927.0
#### Shared
[MOBOFF-9646](https://ifsdev.atlassian.net/browse/MOBOFF-9646): Support for offline command TimestampUtc  

#### iOS
[MOBOFF-10263](https://ifsdev.atlassian.net/browse/MOBOFF-10263): Implement Syncfusion dialog on iPads version 16.3  

## 23.99.922.0
#### Shared
[MOBOFF-8892](https://ifsdev.atlassian.net/browse/MOBOFF-8892): Use provided filename prefix in XAdES sig and return json using SaveMeta action  

#### Windows
[MOBOFF-10093](https://ifsdev.atlassian.net/browse/MOBOFF-10093): Calendar Control [Windows] - Selected long running event is not highlighted in agenda view  

## 23.99.921.0
#### Shared
[MOBOFF-10350](https://ifsdev.atlassian.net/browse/MOBOFF-10350): All the events are not showing in agenda view of the current date (Windows) - Calendar Control  

## 23.99.916.0
#### Shared
[MOBOFF-10150](https://ifsdev.atlassian.net/browse/MOBOFF-10150): Long running event is not displaying at the top of the event list on android & ios( Calendar Control)  

## 23.99.919.0
#### Shared
[MOBOFF-10272](https://ifsdev.atlassian.net/browse/MOBOFF-10272): Honor the primary time zone setting for DateTimeUtc fields  

## 23.99.918.0
#### Shared
[MOBOFF-10258](https://ifsdev.atlassian.net/browse/MOBOFF-10258): Force clearing of time zone cache when OS changes.  
[MOBOFF-3597](https://ifsdev.atlassian.net/browse/MOBOFF-3597): Anonymous delegates should not be used to unsubscribe from Events  

## 23.99.917.0
#### Shared
[MOBOFF-10254](https://ifsdev.atlassian.net/browse/MOBOFF-10254): Failing to normalize windows time zone name to IANA time zone name  

#### Android
[MOBOFF-10250](https://ifsdev.atlassian.net/browse/MOBOFF-10250): Separate time zone change check from notification.  

## 23.99.915.0

#### Android

[MOBOFF-9441](https://ifsdev.atlassian.net/browse/MOBOFF-9441): Cannot go back to the main menu of the ScanIt app using device back button when a folder structure (with 3 or more folders) created from WaDaCo Data Collection Manu is enabled. App exits..

## 23.99.908.0

#### Shared

[MOBOFF-8882](https://ifsdev.atlassian.net/browse/MOBOFF-8882): Create json copy of signed pdf and include it in XAdES signature

## 23.99.905.0

#### Android

[MOBOFF-7860](https://ifsdev.atlassian.net/browse/MOBOFF-7860): Changed android dev app display name

#### iOS

[MOBOFF-7860](https://ifsdev.atlassian.net/browse/MOBOFF-7860): Changed iOS dev app display name  
[MOBOFF-5730](https://ifsdev.atlassian.net/browse/MOBOFF-5730): iOS-Cards not refreshing smoothly when clicking rapidly on the chevron  
[MOBOFF-6696](https://ifsdev.atlassian.net/browse/MOBOFF-6696): Options in FAB menu are not correctly aligned - iOS

#### Windows

[MOBOFF-10097](https://ifsdev.atlassian.net/browse/MOBOFF-10097): Calendar Control [Windows] - User is able to select other event types with all events button  
[MOBOFF-7860](https://ifsdev.atlassian.net/browse/MOBOFF-7860): Changed windows dev app display name

## 23.99.904.0

#### Android

[MOBOFF-9145](https://ifsdev.atlassian.net/browse/MOBOFF-9145): Person Name/Description is not shown in Person Lov in CRM Companion

#### iOS

[MOBOFF-2694](https://ifsdev.atlassian.net/browse/MOBOFF-2694): iOS-Field Focus issue Find Part/Ad-hoc

## 23.99.903.0

#### Shared

[MOBOFF-8100](https://ifsdev.atlassian.net/browse/MOBOFF-8100): Child nodes will be shown for online entities

## 23.99.902.0

#### iOS

[MOBOFF-9359](https://ifsdev.atlassian.net/browse/MOBOFF-9359): E-signature workflows are crashing in the Dev app version 23.99.781.0 and above

## 23.99.900.0

#### Shared

[MOBOFF-10144](https://ifsdev.atlassian.net/browse/MOBOFF-10144): C# null pointers should not be dereferenced

## 23.99.899.0

#### Shared

[MOBOFF-5651](https://ifsdev.atlassian.net/browse/MOBOFF-5651): Display warning message when device storage is full

#### iOS

[MOBOFF-6700](https://ifsdev.atlassian.net/browse/MOBOFF-6700): Some UI elements are not visible when UI changes from landscape view to portrait view - iPad

## 23.99.898.0

#### Shared

[MOBOFF-10049](https://ifsdev.atlassian.net/browse/MOBOFF-10049): Online functions with date issue is fixed  
[MOBOFF-9319](https://ifsdev.atlassian.net/browse/MOBOFF-9319): Fixed Blocks should be synchronized on read only fields

## 23.99.896.0

#### Android

[MOBOFF-6929](https://ifsdev.atlassian.net/browse/MOBOFF-6929): All the dates for the long running event are highlighted in blue text when the starting date is today itself

## 23.99.895.0

#### Android

[MOBOFF-9145](https://ifsdev.atlassian.net/browse/MOBOFF-9145): Person Name/Description is not shown in Person Lov in CRM Companion

## 23.99.892.0

#### Shared

[MOBOFF-9730](https://ifsdev.atlassian.net/browse/MOBOFF-9730): time zone awareness in cards

#### Windows

[MOBOFF-9837](https://ifsdev.atlassian.net/browse/MOBOFF-9837): Calendar Control [Windows] - Past date is displayed in blue text in Agenda view

## 23.99.891.0

#### iOS

[MOBOFF-6708](https://ifsdev.atlassian.net/browse/MOBOFF-6708): Orientation fix in iOS 16 and up  
[MOBOFF-9879](https://ifsdev.atlassian.net/browse/MOBOFF-9879): Missing Finish button in Assistant in Norwegian language fix

## 23.99.889.0

#### iOS

[MOBOFF-9721](https://ifsdev.atlassian.net/browse/MOBOFF-9721): IOS screen collapse fix

## 23.99.887.0

#### Shared

[MOBOFF-9229](https://ifsdev.atlassian.net/browse/MOBOFF-9229): Fix to support dynamic field labels in cards

## 23.99.881.0

#### Shared

[MOBOFF-9229](https://ifsdev.atlassian.net/browse/MOBOFF-9229): Add support for dynamic field labels in cards  
[MOBOFF-2055](https://ifsdev.atlassian.net/browse/MOBOFF-2055): Switch user flow change  
[MOBOFF-2698](https://ifsdev.atlassian.net/browse/MOBOFF-2698): Calendar events are not highlighted on Android and Windows  
[MOBOFF-2798](https://ifsdev.atlassian.net/browse/MOBOFF-2798): Asynchronize dalay used instead of Xamarine.Essentials to time applock  
[MOBOFF-3881](https://ifsdev.atlassian.net/browse/MOBOFF-3881): Find Part map cards are duplicated

## 23.99.878.0

#### Shared

[MOBOFF-2962](https://ifsdev.atlassian.net/browse/MOBOFF-2962): Identifying rooted devices and logging them to the server  
[MOBOFF-4334](https://ifsdev.atlassian.net/browse/MOBOFF-4334): Show progress when downloading media items from list page  
[MOBOFF-6620](https://ifsdev.atlassian.net/browse/MOBOFF-6620): Media list not getting updated when deleting a media item

#### Android

[MOBOFF-9823](https://ifsdev.atlassian.net/browse/MOBOFF-9823): Fixed video files larger than Max Limit can be uploaded issue

#### iOS

[MOBOFF-9737](https://ifsdev.atlassian.net/browse/MOBOFF-9737): Fixed issue of iOS crash if scan a text string on numeric field

#### Windows

[MOBOFF-9813](https://ifsdev.atlassian.net/browse/MOBOFF-9813): Override the default culture using data obtained from OS directly and not use UWP default culture settings as this is based only on current language.

## 23.99.877.0

#### Shared

[MOBOFF-4727](https://ifsdev.atlassian.net/browse/MOBOFF-4727): Retry button showing on required upload issue is fixed.

#### Windows

[MOBOFF-9833](https://ifsdev.atlassian.net/browse/MOBOFF-9833): App crash occurred when changing the appointment status is fixed

## 23.99.859.0

#### Shared

[MOBOFF-2055](https://ifsdev.atlassian.net/browse/MOBOFF-2055): Switch user flow change  
[MOBOFF-9793](https://ifsdev.atlassian.net/browse/MOBOFF-9793): Strip off translatable constant from Dynamic Menu

#### Android

[MOBOFF-9733](https://ifsdev.atlassian.net/browse/MOBOFF-9733): Clear card panel when changing calendar modes  
[MOBOFF-9395](https://ifsdev.atlassian.net/browse/MOBOFF-9395): INC0531906-Maps in MWO not loading

#### iOS

[MOBOFF-6924](https://ifsdev.atlassian.net/browse/MOBOFF-6924): Fixed the disappearance of the search icon in the agenda view when coming back from detail screens and Show In Map screen

#### Windows

[MOBOFF-4305](https://ifsdev.atlassian.net/browse/MOBOFF-4305): Fab menu dark mode is enabled  
[MOBOFF-5098](https://ifsdev.atlassian.net/browse/MOBOFF-5098): Time indicator issue is fixed  
[MOBOFF-5132](https://ifsdev.atlassian.net/browse/MOBOFF-5132): Implemented a fixed size for popup screens across all resolutions, eliminating variable margins based on window size.  
[MOBOFF-6655](https://ifsdev.atlassian.net/browse/MOBOFF-6655): "No appointment" toast message displayed without click on "Today" button fixed.

## 23.99.858.0

#### Shared

[MOBOFF-2733](https://ifsdev.atlassian.net/browse/MOBOFF-2733): Removed parameter from Pincode string since its not used anywhere  
[MOBOFF-2797](https://ifsdev.atlassian.net/browse/MOBOFF-2797): Added unsupported pincode complex characters.  
[MOBOFF-5306](https://ifsdev.atlassian.net/browse/MOBOFF-5306): Added media download command and status badge to media details  
[MOBOFF-6602](https://ifsdev.atlassian.net/browse/MOBOFF-6602): Adding media will hide the media options  
[MOBOFF-9757](https://ifsdev.atlassian.net/browse/MOBOFF-9757): Update to latest HL SDK

#### Android

[MOBOFF-9395](https://ifsdev.atlassian.net/browse/MOBOFF-9395): Maps in MWO not loading

#### iOS

[MOBOFF-9419](https://ifsdev.atlassian.net/browse/MOBOFF-9419): Encrypt stored app settings to prevent information being easily available if accessed from file system.  
[MOBOFF-5897](https://ifsdev.atlassian.net/browse/MOBOFF-5897): Show image editor when image is selected from File manager  
[MOBOFF-7467](https://ifsdev.atlassian.net/browse/MOBOFF-7467): Trip Tracker - LOV for Activities can be called twice

#### Windows

[MOBOFF-2589](https://ifsdev.atlassian.net/browse/MOBOFF-2589): Pincode lock error message is not obstructed anymore after adding Max-width property  
[MOBOFF-5098](https://ifsdev.atlassian.net/browse/MOBOFF-5098): Time indicator issue is fixed  
[MOBOFF-9075](https://ifsdev.atlassian.net/browse/MOBOFF-9075): INC0498643-MWO Windows application does not fully use the language of the device

## 23.99.857.0

#### Shared

[MOBOFF-3972](https://ifsdev.atlassian.net/browse/MOBOFF-3972): removed the client FW logic that handles the slicing of notification message from the point of a colon

#### iOS

[MOBOFF-6830](https://ifsdev.atlassian.net/browse/MOBOFF-6830): Display the timeline indicator when clicking on the today button after a search

#### Windows

[MOBOFF-4494](https://ifsdev.atlassian.net/browse/MOBOFF-4494): Fix Current Day/Today shortcut button is hidden if window is too small  
[MOBOFF-5098](https://ifsdev.atlassian.net/browse/MOBOFF-5098): Time indicator issue is fixed  
[MOBOFF-6746](https://ifsdev.atlassian.net/browse/MOBOFF-6746): Fix "No Items" label displayed even though there are appointments in agenda view

## 23.99.860.0

#### Shared

[MOBOFF-6616](https://ifsdev.atlassian.net/browse/MOBOFF-6616): After selecting a file, a user will have the option to go to previous screen.

#### Android

[MOBOFF-9519](https://ifsdev.atlassian.net/browse/MOBOFF-9519): Fixed receiving an empty notification when initiating remote assistance calls on Android

#### iOS

[MOBOFF-9524](https://ifsdev.atlassian.net/browse/MOBOFF-9524): Mobile App is not consistent

#### Windows

[MOBOFF-4206](https://ifsdev.atlassian.net/browse/MOBOFF-4206): Unselecting selected event in the agenda view when changing the schedule type.  
[MOBOFF-4434](https://ifsdev.atlassian.net/browse/MOBOFF-4434): Fix event highlight is not removed when click anywhere on the calendar view  
[MOBOFF-6292](https://ifsdev.atlassian.net/browse/MOBOFF-6292): Fix Day view of the past events displaying incorrectly  
[MOBOFF-6423](https://ifsdev.atlassian.net/browse/MOBOFF-6423): Fixed "All events" button is not displayed with blue background by default

## 23.99.853.0

#### Shared

[MOBOFF-2451](https://ifsdev.atlassian.net/browse/MOBOFF-2451): Users are enabled to return back from assistant.

## 23.99.852.0

#### Shared

[MOBOFF-8748](https://ifsdev.atlassian.net/browse/MOBOFF-8748): Change to get the RA sessionId from notification  
[MOBOFF-8748](https://ifsdev.atlassian.net/browse/MOBOFF-8748): Change to get the RA sessionId from notification

#### iOS

[MOBOFF-8748](https://ifsdev.atlassian.net/browse/MOBOFF-8748): iOS changes to get the RA sessionId from notification  
[MOBOFF-8748](https://ifsdev.atlassian.net/browse/MOBOFF-8748): Additional iOS changes to get the RA sessionId from notification  
[MOBOFF-8748](https://ifsdev.atlassian.net/browse/MOBOFF-8748): iOS changes to get the RA sessionId from notification

## 23.99.849.0

#### Shared

[MOBOFF-8748](https://ifsdev.atlassian.net/browse/MOBOFF-8748): Change to get the RA sessionId from notification

#### Android

[MOBOFF-9391](https://ifsdev.atlassian.net/browse/MOBOFF-9391): Check that previous time zone setting is not null as well as if it has changed before showing time zone changed alert.

#### iOS

[MOBOFF-9304](https://ifsdev.atlassian.net/browse/MOBOFF-9304): INC0530056-User Interface Issue - Aurena Native Calendar  
[MOBOFF-9364](https://ifsdev.atlassian.net/browse/MOBOFF-9364): Issue with Service Report Forward icon

## 23.99.845.0

#### Android

[MOBOFF-6809](https://ifsdev.atlassian.net/browse/MOBOFF-6809): Cards will now close upon initiating a search.

#### iOS

[MOBOFF-9166](https://ifsdev.atlassian.net/browse/MOBOFF-9166): Fix ios datetime conversion issue for online calendar

## 23.99.844.0

#### Shared

[MOBOFF-9447](https://ifsdev.atlassian.net/browse/MOBOFF-9447): Change the method used to get the current TimeZoneInfo to use TimeZoneInfo.Local  
[MOBOFF-8748](https://ifsdev.atlassian.net/browse/MOBOFF-8748): Backwards compatibility fix for RA to send datacenter param when starting a call  
[MOBOFF-7689](https://ifsdev.atlassian.net/browse/MOBOFF-7689): The progress bar and no items problem has been fixed.

## 23.99.839.0

#### Shared

[MOBOFF-5460](https://ifsdev.atlassian.net/browse/MOBOFF-5460): Fixed inaccessible newly created steps (ListData)

## 23.99.837.0

#### Shared

[MOBOFF-2952](https://ifsdev.atlassian.net/browse/MOBOFF-2952): Added sort order for repeating section steps  
[MOBOFF-7951](https://ifsdev.atlassian.net/browse/MOBOFF-7951): Fix issue of unable to find Part due to language and region settings on device  
[MOBOFF-8613](https://ifsdev.atlassian.net/browse/MOBOFF-8613): 23R1 GA and EA Translations

## 23.99.836.0

#### Shared

[MOBOFF-1738](https://ifsdev.atlassian.net/browse/MOBOFF-1738): Use the Installed Application Details/Device Log Level to control output of stack trace in log files. Only include stack trace when value is set to Trace.  
[MOBOFF-9187](https://ifsdev.atlassian.net/browse/MOBOFF-9187): INC0525323-MWO Maintenance - Default image set for work task get overridden when user uploads new media item from MWO app for work task

## 23.99.830.0

#### Shared

[MOBOFF-5604](https://ifsdev.atlassian.net/browse/MOBOFF-5604): The no items label won't be displayed until data is displayed.  
[MOBOFF-8165](https://ifsdev.atlassian.net/browse/MOBOFF-8165): Device will re-register for push notification when the hub details change  
[MOBOFF-9206](https://ifsdev.atlassian.net/browse/MOBOFF-9206): Allow adding video from gallery/file in documents

#### Android

[MOBOFF-5682](https://ifsdev.atlassian.net/browse/MOBOFF-5682): Add and implement encryption support Android to AppDataSettings so that information stored in Ifs.Uma.AppData.xml shared preferences file.

#### iOS

[MOBOFF-7989](https://ifsdev.atlassian.net/browse/MOBOFF-7989): Fixed video title getting populated with a random name.

## 23.99.829.0

#### Shared

[MOBOFF-9194](https://ifsdev.atlassian.net/browse/MOBOFF-9194): Save button is not visible after retry logic unless retake the picture  
[MOBOFF-5777](https://ifsdev.atlassian.net/browse/MOBOFF-5777): Fixed orderby clause call request sent from client  
[MOBOFF-8998](https://ifsdev.atlassian.net/browse/MOBOFF-8998): Can not upload picture in MWO app after save changes on media screen

#### iOS

[MOBOFF-5141](https://ifsdev.atlassian.net/browse/MOBOFF-5141): Fixed Camera option to only allow taking photos

## 23.99.828.0

#### Shared

[MOBZFW-1147](https://ifsdev.atlassian.net/browse/MOBZFW-1147): Persisting Resource Favorites when there are linked LOVs  
[MOBZFW-1310](https://ifsdev.atlassian.net/browse/MOBZFW-1310): Further changes to JSON MaxDepth handling  
[MOBZFW-871](https://ifsdev.atlassian.net/browse/MOBZFW-871): Log error for sync failing doesn't describe precise cause  
[MOBZFW-898](http://jira/browse/MOBZFW-898): (and MOBZFW-961): Make the Tree load only one level of children automatically

#### Android

[MOBOFF-9001](https://ifsdev.atlassian.net/browse/MOBOFF-9001): App crash at the first login after a fresh installation  
[MOBZFW-1126](https://ifsdev.atlassian.net/browse/MOBZFW-1126): Fixed Notification Type Badge Not Visible issue  
[MOBZFW-1252](https://ifsdev.atlassian.net/browse/MOBZFW-1252): ********: WaDaCo in the IFS Aurena Scan It app - Loss of focus during the Receive shop order process  
[MOBZFW-874](https://ifsdev.atlassian.net/browse/MOBZFW-874): Fixed cannot access a disposed object error

#### iOS

[MOBZFW-1022](https://ifsdev.atlassian.net/browse/MOBZFW-1022): Fix for transparent UmaTabBarController on iOS 15  
[MOBZFW-951](https://ifsdev.atlassian.net/browse/MOBZFW-951): Fix for crash on UITableView

#### Windows

[MOBZFW-1140](https://ifsdev.atlassian.net/browse/MOBZFW-1140): G2293471 - 400: Error when scanning GS1 barcode using IFS Aurena Scan It 10 on Windows scanner

## 23.99.825.0

#### iOS

[MOBOFF-3358](https://ifsdev.atlassian.net/browse/MOBOFF-3358): Support HTML content in Step description in Task steps

## 23.99.817.0

#### Shared

[MOBOFF-9198](https://ifsdev.atlassian.net/browse/MOBOFF-9198): Fixed database upgrade error

## 23.99.815.0

#### Android

[MOBOFF-7292](https://ifsdev.atlassian.net/browse/MOBOFF-7292): Solution for android app crash when changing location permission for android 10 and above

#### iOS

[MOBOFF-8825](https://ifsdev.atlassian.net/browse/MOBOFF-8825): fix iOS crash issue when the process is scanned through WaDaCo

## 23.99.810.0

#### Android

[MOBOFF-8720](https://ifsdev.atlassian.net/browse/MOBOFF-8720): Set LegacyExternalStorage to true to fix android 10 crash

## 23.99.809.0

#### Shared

[MOBOFF-8811](https://ifsdev.atlassian.net/browse/MOBOFF-8811): Contacts string translation fixed in German, Italian and Spanish languages  
[MOBOFF-8811](https://ifsdev.atlassian.net/browse/MOBOFF-8811): Incorrect translations corrected

#### Android

[MOBOFF-7292](https://ifsdev.atlassian.net/browse/MOBOFF-7292): Solution for android app crash when changing location permission for android 10 and above

#### iOS

[MOBOFF-7981](https://ifsdev.atlassian.net/browse/MOBOFF-7981): Fix for error message disappearing quickly when vid exceeds size limit

## 23.99.807.0

#### iOS

[MOBOFF-7875](https://ifsdev.atlassian.net/browse/MOBOFF-7875): improve location update performance for iOS

#### Windows

[MOBOFF-8292](https://ifsdev.atlassian.net/browse/MOBOFF-8292): Ok button glitch is fixed.

## 23.99.806.0

#### Android

[MOBOFF-7292](https://ifsdev.atlassian.net/browse/MOBOFF-7292): Solution for android app crash when changing location permission for android 10 and above

#### Windows

[MOBOFF-8663](https://ifsdev.atlassian.net/browse/MOBOFF-8663): Utc field will convert the time to utc.

## 23.99.804.0

#### Shared

[MOBOFF-8815](https://ifsdev.atlassian.net/browse/MOBOFF-8815): Updated the CopyRight information

#### Android

[MOBOFF-8760](https://ifsdev.atlassian.net/browse/MOBOFF-8760): Reduce location performance lag by introducing polling location with extra providers and last known location

## 23.99.805.0

#### Shared

[MOBOFF-3032](https://ifsdev.atlassian.net/browse/MOBOFF-3032): get request for new navigation end point  
[MOBOFF-8165](https://ifsdev.atlassian.net/browse/MOBOFF-8165): Device will re-register for push notification when the hub details change  
[MOBOFF-8764](https://ifsdev.atlassian.net/browse/MOBOFF-8764): The user should be prompted with an alert message to proceed with the image upload if the GPS location cannot be retrieved  
[MOBOFF-8923](https://ifsdev.atlassian.net/browse/MOBOFF-8923): Logging out or deactivating will unregister the device from push notifications.

#### Android

[MOBOFF-8544](https://ifsdev.atlassian.net/browse/MOBOFF-8544): Ensure the dialog "Log in credentials expired" is not lost when navigating from the log in screen

#### iOS

[MOBOFF-8760](https://ifsdev.atlassian.net/browse/MOBOFF-8760): Reduce location performance lag by timeout timer and last known location on iOS  
[MOBOFF-7601](https://ifsdev.atlassian.net/browse/MOBOFF-7601): Upgrade to latest HL SDK for iOS  
[MOBOFF-7601](https://ifsdev.atlassian.net/browse/MOBOFF-7601): Fix build errors caused by HL SDK upgrade for iOS

## 23.99.803.0

#### Shared

[MOBOFF-8250](https://ifsdev.atlassian.net/browse/MOBOFF-8250): Client FW changes to reading the new ignoreOfflineCheck attribute in Validate Commands  
[MOBOFF-8519](https://ifsdev.atlassian.net/browse/MOBOFF-8519): Add server version check for TimeZone  
[MOBOFF-8811](https://ifsdev.atlassian.net/browse/MOBOFF-8811): Incorrect translations corrected

#### Android

[MOBOFF-7601](https://ifsdev.atlassian.net/browse/MOBOFF-7601): Upgrade to latest HL SDK for android  
[MOBOFF-7292](https://ifsdev.atlassian.net/browse/MOBOFF-7292): Solution for android app crash when changing location permission

#### iOS

[MOBOFF-8270](https://ifsdev.atlassian.net/browse/MOBOFF-8270): Fix iOS layout issue when multiple primary views exist in single step assistants

## 23.99.797.0

#### Android

[MOBOFF-8720](https://ifsdev.atlassian.net/browse/MOBOFF-8720): Fixed app crash when the permission is not given to save to device storage

## 23.99.792.0

#### Shared

[MOBOFF-3290](https://ifsdev.atlassian.net/browse/MOBOFF-3290): Fix conditional compilation for signature service in some places in the code

## 23.99.783.0

#### iOS

[MOBOFF-8744](https://ifsdev.atlassian.net/browse/MOBOFF-8744): Fixed app crash when the permission is not given to save to gallery

## 23.99.780.0

#### iOS

[MOBOFF-8652](https://ifsdev.atlassian.net/browse/MOBOFF-8652): Correctly use RSA/ECDSA256/ECDSA384 algo for eSignature

## 23.99.779.0

#### Android

[MOBOFF-5798](https://ifsdev.atlassian.net/browse/MOBOFF-5798): calendar events not getting ordered properly is fixed

## 23.99.777.0

#### Shared

[MOBOFF-8140](https://ifsdev.atlassian.net/browse/MOBOFF-8140): Fixed android crash when selecting large media file

#### Android

[MOBOFF-8635](https://ifsdev.atlassian.net/browse/MOBOFF-8635): Video files cannot be uploaded from the Storage on Android

#### iOS

[MOBOFF-8104](https://ifsdev.atlassian.net/browse/MOBOFF-8104): Fix for search bar disappearing  
[MOBOFF-8279](https://ifsdev.atlassian.net/browse/MOBOFF-8279): Add device has changed locale check

## 23.99.769.0

#### Shared

[MOBOFF-8588](https://ifsdev.atlassian.net/browse/MOBOFF-8588): Changing Microsoft identity client package version

## 23.99.767.0

#### Shared

[MOBOFF-2081](https://ifsdev.atlassian.net/browse/MOBOFF-2081): MS authentication part added for Windows and Android  
[MOBOFF-2084](https://ifsdev.atlassian.net/browse/MOBOFF-2084): Initiating calls and chats to MS Teams feature added in Windows and Android clients  
[MOBOFF-6639](https://ifsdev.atlassian.net/browse/MOBOFF-6639): Retrieve contact's status from MSTeams in shared code  
[MOBOFF-6875](https://ifsdev.atlassian.net/browse/MOBOFF-6875): MS Teams function will be shown only if contacts email exists and only if it is enabled from server side by the app parameter  
[MOBOFF-7361](https://ifsdev.atlassian.net/browse/MOBOFF-7361): Users can log into MS account by clicking log in option in the contact widget and will be signed out once the user is switched or disconnected from the app  
[MOBOFF-7401](https://ifsdev.atlassian.net/browse/MOBOFF-7401): Contact button and contact widget icons updated in Android and Windows clients  
[MOBOFF-8017](https://ifsdev.atlassian.net/browse/MOBOFF-8017): Using system parameter values for clientId  
[MOBOFF-8497](https://ifsdev.atlassian.net/browse/MOBOFF-8497): Correct the redirect uri format to be dynamic per app  
[MOBOFF-8521](https://ifsdev.atlassian.net/browse/MOBOFF-8521): If the clientId is not configured in System Parameters MS Teams section will not be shown  
[MOBOFF-8572](https://ifsdev.atlassian.net/browse/MOBOFF-8572): Fix server version check for locales other than English

#### Android

[MOBOFF-6624](https://ifsdev.atlassian.net/browse/MOBOFF-6624): Android client contact widget UI is updated with MS Teams information  
[MOBOFF-7106](https://ifsdev.atlassian.net/browse/MOBOFF-7106): Uses can see the presence icon in the Android client contact widget after logging in to the Microsoft account  
[MOBOFF-7703](https://ifsdev.atlassian.net/browse/MOBOFF-7703): Corrected the usage of icons from png to svg in the android client contact widget

#### iOS

[MOBOFF-6634](https://ifsdev.atlassian.net/browse/MOBOFF-6634): Contact Widget Control UI  
[MOBOFF-6644](https://ifsdev.atlassian.net/browse/MOBOFF-6644): ms teams user status  
[MOBOFF-7310](https://ifsdev.atlassian.net/browse/MOBOFF-7310): MSTeams LogIn  
[MOBOFF-7393](https://ifsdev.atlassian.net/browse/MOBOFF-7393): Initiate MS Teams calls and chats  
[MOBOFF-7730](https://ifsdev.atlassian.net/browse/MOBOFF-7730): Hide MSTeams information

#### Windows

[MOBOFF-6629](https://ifsdev.atlassian.net/browse/MOBOFF-6629): Windows client contact widget UI is updated with MS Teams information  
[MOBOFF-6649](https://ifsdev.atlassian.net/browse/MOBOFF-6649): Uses can see the presence icon in the Windows client contact widget after logging in to the Microsoft account

## 23.99.766.0

#### Shared

[MOBOFF-2295](https://ifsdev.atlassian.net/browse/MOBOFF-2295): Looking for a new system method to substring a text to given length

## 23.99.768.0

#### Shared

[MOBOFF-7968](https://ifsdev.atlassian.net/browse/MOBOFF-7968): Save video to gallery if recording exceeds max upload size

#### Windows

[MOBOFF-8118](https://ifsdev.atlassian.net/browse/MOBOFF-8118): Further changes to camera picked files on Windows

## 23.99.765.0

#### Android

[MOBOFF-8118](https://ifsdev.atlassian.net/browse/MOBOFF-8118): Further changes to camera picked files on Android

#### iOS

[MOBOFF-3358](https://ifsdev.atlassian.net/browse/MOBOFF-3358): Support HTML content in Step description in Task steps  
[MOBOFF-8270](https://ifsdev.atlassian.net/browse/MOBOFF-8270): Fix for content view crash

## 23.99.762.0

#### Shared

[MOBOFF-3032](https://ifsdev.atlassian.net/browse/MOBOFF-3032): get request for new navigation end point  
[MOBOFF-8328](https://ifsdev.atlassian.net/browse/MOBOFF-8328): Documents are not uploaded when the document class contains illegal characters

#### Windows

[MOBOFF-8300](https://ifsdev.atlassian.net/browse/MOBOFF-8300): UTC Icon on Date and Time Field displaying Date Time picker fixed to show UTC alert box only.  
[MOBOFF-8304](https://ifsdev.atlassian.net/browse/MOBOFF-8304): Timezone alert message not showing change in timezone appropriately fix.

## 23.99.761.0

#### Shared

[MOBOFF-1895](https://ifsdev.atlassian.net/browse/MOBOFF-1895): A new get call to get the server version is completed  
[MOBOFF-7850](https://ifsdev.atlassian.net/browse/MOBOFF-7850): Remove mentions of the word Aurena in the comments and some code

#### iOS

[MOBOFF-7553](https://ifsdev.atlassian.net/browse/MOBOFF-7553): Change the document viewer so that share option is always available

## 23.99.759.0

#### Shared

[MOBOFF-8118](https://ifsdev.atlassian.net/browse/MOBOFF-8118): Enforce video file size limit in assistants and multi file pickers

## 23.99.758.0

#### Windows

[MOBOFF-7173](https://ifsdev.atlassian.net/browse/MOBOFF-7173): Glitch after adding materials to Work Task is fixed

## 22.99.756.0

#### Android

[MOBOFF-6432](https://ifsdev.atlassian.net/browse/MOBOFF-6432): Android - UI control changes for mobile FW components

## 22.99.755.0

#### Shared

[MOBOFF-8242](https://ifsdev.atlassian.net/browse/MOBOFF-8242): Fix SonarCloud hotspot around RegEx DoS attacks

#### iOS

[MOBOFF-6899](https://ifsdev.atlassian.net/browse/MOBOFF-6899): Fix sort options button not working on some lists

## 22.99.754.0

#### Shared

[MOBOFF-1466](https://ifsdev.atlassian.net/browse/MOBOFF-1466): component.ListName.Selection will give access to key values  
[MOBOFF-4688](https://ifsdev.atlassian.net/browse/MOBOFF-4688): Timestamp utc fields now stored and sent to server in UTC  
[MOBOFF-7328](https://ifsdev.atlassian.net/browse/MOBOFF-7328): Date update on online entity attribute fix  
[MOBOFF-7855](https://ifsdev.atlassian.net/browse/MOBOFF-7855): Rename: Rename dev app UI texts  
[MOBOFF-3290](https://ifsdev.atlassian.net/browse/MOBOFF-3290): Improve Transaction Sessions support in the sync service

#### Android

[MOBOFF-6467](https://ifsdev.atlassian.net/browse/MOBOFF-6467): System alerts when the device locations moves from one time zone to another

#### iOS

[MOBOFF-3969](https://ifsdev.atlassian.net/browse/MOBOFF-3969): Updated the Add Media & Media Detail page UI buttons  
[MOBOFF-6427](https://ifsdev.atlassian.net/browse/MOBOFF-6427): Added icon to UTC date time fields  
[MOBOFF-6476](https://ifsdev.atlassian.net/browse/MOBOFF-6476): Check location change on iOS  
[MOBOFF-7985](https://ifsdev.atlassian.net/browse/MOBOFF-7985): Prevent vids larger than max limit from uploading

#### Windows

[MOBOFF-4682](https://ifsdev.atlassian.net/browse/MOBOFF-4682): Timestamp Utc field UI changes

## 22.99.751.0

#### Shared

[MOBOFF-7840](https://ifsdev.atlassian.net/browse/MOBOFF-7840): Provide a system method to get the current device ID

## 22.99.750.0

#### Windows

[MOBOFF-6373](https://ifsdev.atlassian.net/browse/MOBOFF-6373): The user will be notified when the time zone changes

## 22.99.745.0

#### iOS

[MOBOFF-7584](https://ifsdev.atlassian.net/browse/MOBOFF-7584): Fix for file not found error when trying to import a file from a cloud file provider like OneDrive

## 22.99.734.0

#### Shared

[MOBOFF-7435](https://ifsdev.atlassian.net/browse/MOBOFF-7435): On demand modify not updating the record

#### Android

[MOBOFF-5106](https://ifsdev.atlassian.net/browse/MOBOFF-5106): Fix for self signed certificates failing on Android

## 22.99.732.0

#### Shared

[MOBOFF-7073](https://ifsdev.atlassian.net/browse/MOBOFF-7073): Parameterize disabling screenshots

#### iOS

[MOBOFF-7415](https://ifsdev.atlassian.net/browse/MOBOFF-7415): Fix for request quotation crash

## 22.99.723.0

#### Shared

[MOBOFF-5860](https://ifsdev.atlassian.net/browse/MOBOFF-5860): US3: Video File Size Restriction  
[MOBOFF-7125](https://ifsdev.atlassian.net/browse/MOBOFF-7125): Added a new function SystemTimeZone to return client's timezone to marble

#### Android

[MOBOFF-2962](https://ifsdev.atlassian.net/browse/MOBOFF-2962): Identifying rooted devices and logging them to the server

## 22.99.721.0

#### Shared

[MOBOFF-7564](https://ifsdev.atlassian.net/browse/MOBOFF-7564): Fix push disabling when logged out on iOS (due to Android changes) and added push handle debug to settings

## 22.99.718.0

#### iOS

[MOBOFF-7564](https://ifsdev.atlassian.net/browse/MOBOFF-7564): Fix push notifications being disabled by default

## 22.99.716.0

#### Shared

[MOBOFF-4672](https://ifsdev.atlassian.net/browse/MOBOFF-4672): Retrieving additional time zones from the server  
[MOBOFF-4677](https://ifsdev.atlassian.net/browse/MOBOFF-4677): Convert Timezone UTC to device Timezone  
[MOBOFF-7068](https://ifsdev.atlassian.net/browse/MOBOFF-7068): Fix for tree structure online query  
[MOBOFF-7410](https://ifsdev.atlassian.net/browse/MOBOFF-7410): Related Page List item visibility now updated from enable function defined from marble

## 22.99.713.0

#### Shared

[MOBOFF-2739](https://ifsdev.atlassian.net/browse/MOBOFF-2739): Confusing mandatory indicators (\*) shows in Trip tracker for non-mandatory fields  
[MOBOFF-4677](https://ifsdev.atlassian.net/browse/MOBOFF-4677): Convert Timezone UTC to device Timezone

#### iOS

[MOBOFF-5057](https://ifsdev.atlassian.net/browse/MOBOFF-5057): Changed the Digital signature preview dialog's title to 'Digital Signature Preview'

## 22.99.712.0

#### Shared

[MOBOFF-7073](https://ifsdev.atlassian.net/browse/MOBOFF-7073): Parameterize disabling screenshots

## 22.99.709.0

#### Shared

[MOBOFF-3300](https://ifsdev.atlassian.net/browse/MOBOFF-3300): Finalize open transaction sessions when app is closed unexpectedly

## 22.99.704.0

#### Shared

[MOBOFF-3468](https://ifsdev.atlassian.net/browse/MOBOFF-3468): Sonarcloud hotspot warning supressed

## 22.99.703.0

#### Shared

[MOBOFF-7414](https://ifsdev.atlassian.net/browse/MOBOFF-7414): Fix for sonar cloud null pointer checks

## 22.99.702.0

#### Shared

[MOBOFF-3601](https://ifsdev.atlassian.net/browse/MOBOFF-3601): [SonarCloud] (HTML) "<html>" element should have a language attribute

## 22.99.701.0

#### Shared

[MOBOFF-3290](https://ifsdev.atlassian.net/browse/MOBOFF-3290): Added Transaction Sessions support to the sync service

## 22.99.700.0

#### Shared

[MOBOFF-6966](https://ifsdev.atlassian.net/browse/MOBOFF-6966): Fix for quotation online entity

#### Windows

[MOBOFF-7381](https://ifsdev.atlassian.net/browse/MOBOFF-7381): Fix to update text field value when new field comes into focus

## 22.99.690.0

#### Android

[MOBOFF-6972](https://ifsdev.atlassian.net/browse/MOBOFF-6972): Search bar is enabled in calendar when coming from details screen - Android

## 22.99.689.0

#### Android

[MOBOFF-3076](https://ifsdev.atlassian.net/browse/MOBOFF-3076): Implement validations in the process viewer control for Android

## 22.99.686.0

#### Shared

[MOBOFF-3224](https://ifsdev.atlassian.net/browse/MOBOFF-3224): REVERT Block the capturing of screenshots  
[MOBOFF-3599](https://ifsdev.atlassian.net/browse/MOBOFF-3599): [SonarCloud] Empty nullable value should not be accessed  
[MOBOFF-6817](https://ifsdev.atlassian.net/browse/MOBOFF-6817): Removed condition that always evaluated to 'true'  
[MOBOFF-6849](https://ifsdev.atlassian.net/browse/MOBOFF-6849): Adding missing translations

#### Windows

[MOBOFF-3974](https://ifsdev.atlassian.net/browse/MOBOFF-3974): US 1: Update the Add Media & Media Detail page UI - Windows  
[MOBOFF-5764](https://ifsdev.atlassian.net/browse/MOBOFF-5764): Fix for agenda view-Calendar events get disappeared randomly when changing WT status

## 22.99.685.0

#### Shared

[MOBOFF-3285](https://ifsdev.atlassian.net/browse/MOBOFF-3285): Added procedures needed for Transaction Session grouping  
[MOBOFF-3468](https://ifsdev.atlassian.net/browse/MOBOFF-3468): Sonarcloud hotspot warning supressed

#### Android

[MOBOFF-6728](https://ifsdev.atlassian.net/browse/MOBOFF-6728): Calendar Control [Android] - Event status badge is not updated when we change the status from details screen

#### iOS

[MOBOFF-7079](https://ifsdev.atlassian.net/browse/MOBOFF-7079): Scanit scan iOS camera improvement fix

## 22.99.679.0

#### Shared

[MOBOFF-6767](https://ifsdev.atlassian.net/browse/MOBOFF-6767): message ID now sent for media upload

## 22.99.678.0

#### Shared

[MOBOFF-3280](https://ifsdev.atlassian.net/browse/MOBOFF-3280): DB changes to support transaction session grouping

## 22.99.677.0

#### Shared

[MOBOFF-3592](https://ifsdev.atlassian.net/browse/MOBOFF-3592): SonarCloud "string.ToCharArray()" is not called redundantly now.  
[MOBOFF-3593](https://ifsdev.atlassian.net/browse/MOBOFF-3593): SonarCloud - Refactored foreach loop into if statement.

## 22.99.676.0

#### Shared

[MOBOFF-3475](https://ifsdev.atlassian.net/browse/MOBOFF-3475): SonarCloud recursion fix.  
[MOBOFF-3595](https://ifsdev.atlassian.net/browse/MOBOFF-3595): SonarCloud - "ToString()" method now returns empty string instead of null.  
[MOBOFF-3596](https://ifsdev.atlassian.net/browse/MOBOFF-3596): SonarCloud switch conditional structure removed and simplified with initialized variable.

#### Android

[MOBOFF-3075](https://ifsdev.atlassian.net/browse/MOBOFF-3075): Implement stage indicator in process viewer on Android

#### Windows

[MOBOFF-6750](https://ifsdev.atlassian.net/browse/MOBOFF-6750): Calendar Control [Windows] - Filter and Search is not working when coming back from details screen

## 22.99.675.0

#### Android

[MOBOFF-6485](https://ifsdev.atlassian.net/browse/MOBOFF-6485): Backward Compatibility-Two Button sets available on Documents Details Page for Older IFS cloud Versions

## 22.99.674.0

#### Shared

[MOBOFF-3224](https://ifsdev.atlassian.net/browse/MOBOFF-3224): Block the capturing of Recent app details and Sensitive Data Captured in Screenshots  
[MOBOFF-3592](https://ifsdev.atlassian.net/browse/MOBOFF-3592): SonarCloud "string.ToCharArray()" is not called redundantly now.  
[MOBOFF-3594](https://ifsdev.atlassian.net/browse/MOBOFF-3594): SonarCloud- "<!DOCTYPE>" declaration inserted.

## 22.99.673.0

#### Shared

[MOBOFF-3474](https://ifsdev.atlassian.net/browse/MOBOFF-3474): different comparison operator ("==") used for value type.

## 22.99.672.0

#### Windows

[MOBOFF-6803](https://ifsdev.atlassian.net/browse/MOBOFF-6803): Searched events is now cleared when the search field clear button is pressed.

## 22.99.667.0

#### Shared

[MOBOFF-6397](https://ifsdev.atlassian.net/browse/MOBOFF-6397): Calendar Control [Android, Window] - Current timeline should be hidden in search results

#### Android

[MOBOFF-6414](https://ifsdev.atlassian.net/browse/MOBOFF-6414): When the user closes the fab menu, they will return to the previous state

#### iOS

[MOBOFF-6834](https://ifsdev.atlassian.net/browse/MOBOFF-6834): Current date navigation issue  
[MOBOFF-6857](https://ifsdev.atlassian.net/browse/MOBOFF-6857): Today button now clear search results while keeping filters

#### Windows

[MOBOFF-6861](https://ifsdev.atlassian.net/browse/MOBOFF-6861): Now filters are applied when clicked on the filter button  
[MOBOFF-6826](https://ifsdev.atlassian.net/browse/MOBOFF-6826): [Windows] The status badge isn't updated in the search list when changing status

## 22.99.666.0

#### iOS

[MOBOFF-6405](https://ifsdev.atlassian.net/browse/MOBOFF-6405): Show search button in agenda and navigate to top when pressed  
[MOBOFF-6688](https://ifsdev.atlassian.net/browse/MOBOFF-6688): FAB icon position change  
[MOBOFF-6818](https://ifsdev.atlassian.net/browse/MOBOFF-6818): Clear search text when current day button is clicked

## 22.99.660.0

#### Android

[MOBOFF-6732](https://ifsdev.atlassian.net/browse/MOBOFF-6732): Cancelling the search will take the user back to the current date.

#### iOS

[MOBOFF-6287](https://ifsdev.atlassian.net/browse/MOBOFF-6287): Dismiss keyboard to show the card panel when the search bar is active

#### Windows

[MOBOFF-6525](https://ifsdev.atlassian.net/browse/MOBOFF-6525): When switched to different calendar modes the search value is removed.

## 22.99.653.0

#### Shared

[MOBOFF-4516](https://ifsdev.atlassian.net/browse/MOBOFF-4516): Added word wrap in htmlrenderer to make column header text visible

#### Android

[MOBOFF-6362](https://ifsdev.atlassian.net/browse/MOBOFF-6362): IFS MWO Maintenance crashing in the Android operating system  
[MOBOFF-6608](https://ifsdev.atlassian.net/browse/MOBOFF-6608): Agenda view has been opened when changing the WO status / come back from Map view in Syncfusion calendar  
[MOBOFF-6679](https://ifsdev.atlassian.net/browse/MOBOFF-6679): fix switching the calendar mode is not working after using search

#### iOS

[MOBOFF-5274](https://ifsdev.atlassian.net/browse/MOBOFF-5274): Fixed field value not updating after barcode or lidar scan  
[MOBOFF-6504](https://ifsdev.atlassian.net/browse/MOBOFF-6504): Fix filters not working when coming back from details screen  
[MOBOFF-6663](https://ifsdev.atlassian.net/browse/MOBOFF-6663): Fix for filtered and searched events not showing when changing status  
[MOBOFF-6667](https://ifsdev.atlassian.net/browse/MOBOFF-6667): Search bar inactive when calendar modes are changed

#### Windows

[MOBOFF-6305](https://ifsdev.atlassian.net/browse/MOBOFF-6305): The current day button will clear the search field and advance the user to the current date.

## 22.99.652.0

#### Shared

[MOBOFF-6768](https://ifsdev.atlassian.net/browse/MOBOFF-6768): Fix for events disappearing when clicking on search icon in Calendar

#### iOS

[MOBOFF-6667](https://ifsdev.atlassian.net/browse/MOBOFF-6667): Search bar inactive when calendar modes are changed

## 22.99.651.0

#### Shared

[MOBOFF-6409](https://ifsdev.atlassian.net/browse/MOBOFF-6409): Documents cannot be viewed in mobile client when the repository filename extension differs from edm file type.  
[MOBOFF-6704](https://ifsdev.atlassian.net/browse/MOBOFF-6704): Card by card navigation is now working for the search results

#### Android

[MOBOFF-6414](https://ifsdev.atlassian.net/browse/MOBOFF-6414): Fix filetring issue on top of search results  
[MOBOFF-6737](https://ifsdev.atlassian.net/browse/MOBOFF-6737): Fix for GetSearchedCalendarEvents null string crash

#### iOS

[MOBOFF-6301](https://ifsdev.atlassian.net/browse/MOBOFF-6301): Clear search when navigating to current date  
[MOBOFF-6671](https://ifsdev.atlassian.net/browse/MOBOFF-6671): Fix for filtering not working on searched results on iOS  
[MOBOFF-6684](https://ifsdev.atlassian.net/browse/MOBOFF-6684): Changes in search bar on edit started method

#### Windows

[MOBOFF-6563](https://ifsdev.atlassian.net/browse/MOBOFF-6563): Events that have a status badge will display the badge

## 22.99.650.0

#### Android

[MOBOFF-6393](https://ifsdev.atlassian.net/browse/MOBOFF-6393): Clear search results when current day button is clicked  
[MOBOFF-6446](https://ifsdev.atlassian.net/browse/MOBOFF-6446): Search text is now considered every time agenda is reloaded  
[MOBOFF-6583](https://ifsdev.atlassian.net/browse/MOBOFF-6583): Calendar Control [Android] - When the filters are applied in syncfusion calendar, agenda view has been opened  
[MOBOFF-6659](https://ifsdev.atlassian.net/browse/MOBOFF-6659): Added PendingIntent Flags for new target API level  
[MOBOFF-6513](https://ifsdev.atlassian.net/browse/MOBOFF-6513): After searching, switching apps won't clear the search. The search user will return back to the same screen

#### Windows

[MOBOFF-6567](https://ifsdev.atlassian.net/browse/MOBOFF-6567): Agenda view is now set to open in the current date by default  
[MOBOFF-6410](https://ifsdev.atlassian.net/browse/MOBOFF-6410): Filters now work on top of search results

## 22.99.649.0

#### Shared

[MOBOFF-5722](https://ifsdev.atlassian.net/browse/MOBOFF-5722): Update Syncfusion packages to version 20.3.0.47

#### Android

[MOBOFF-6441](https://ifsdev.atlassian.net/browse/MOBOFF-6441): Fix switching calendar modes is not clear out the search

#### iOS

[MOBOFF-6319](https://ifsdev.atlassian.net/browse/MOBOFF-6319): Search results dismiss error fixed

#### Windows

[MOBOFF-6508](https://ifsdev.atlassian.net/browse/MOBOFF-6508): Previously selected events will be set as not selected if the user clicks the event again.

## 22.99.648.0

#### Shared

[MOBOFF-5973](https://ifsdev.atlassian.net/browse/MOBOFF-5973): Use server keys when available during signed document upload

#### iOS

[MOBOFF-6131](https://ifsdev.atlassian.net/browse/MOBOFF-6131): Fixed text and background colour of image and doc picker navigation bar

#### Windows

[MOBOFF-6521](https://ifsdev.atlassian.net/browse/MOBOFF-6521): The Fab menu getting auto-closed after selecting calendar mode is fixed

## 22.99.647.0

#### Shared

[MOBOFF-4516](https://ifsdev.atlassian.net/browse/MOBOFF-4516): Added word wrap in htmlrenderer to make column text visible  
[MOBOFF-6158](https://ifsdev.atlassian.net/browse/MOBOFF-6158): Added multiline attribute to UI Fields

#### iOS

[MOBOFF-2927](https://ifsdev.atlassian.net/browse/MOBOFF-2927): Correct Missing Export Compliance warning on iOS submisisons

#### Windows

[MOBOFF-6305](https://ifsdev.atlassian.net/browse/MOBOFF-6305): Cleared the search field when Today button is pressed and redirected to today's event.

## 22.99.646.0

#### Shared

[MOBOFF-5901](https://ifsdev.atlassian.net/browse/MOBOFF-5901): Include new Translated strings to the Aurena Native

#### Android

[MOBOFF-6401](https://ifsdev.atlassian.net/browse/MOBOFF-6401): Fix keyboard not disappear when click search key issue

#### Windows

[MOBOFF-4406](https://ifsdev.atlassian.net/browse/MOBOFF-4406): Removed the status badge for events that are less than 30 minutes.

## 22.99.645.0

#### Shared

[MOBOFF-3150](https://ifsdev.atlassian.net/browse/MOBOFF-3150): FAB icon changes when filters are active in calendar

## 22.99.644.0

#### Android

[MOBOFF-3137](https://ifsdev.atlassian.net/browse/MOBOFF-3137): Calendar Control Search in Android

#### iOS

[MOBOFF-6327](https://ifsdev.atlassian.net/browse/MOBOFF-6327): Enabled doc selection from file browser for document attachment

## 22.99.643.0

#### Android

[MOBOFF-6095](https://ifsdev.atlassian.net/browse/MOBOFF-6095): Display filter options in FAB menu in Android

#### Windows

[MOBOFF-4583](https://ifsdev.atlassian.net/browse/MOBOFF-4583): Group Collapse/UnCollapse button not working

## 22.99.639.0

#### Shared

[MOBOFF-6297](https://ifsdev.atlassian.net/browse/MOBOFF-6297): Fix DB upgrade error due to IsSigningError column not being added

## 22.99.632.0

#### Shared

[MOBOFF-4614](https://ifsdev.atlassian.net/browse/MOBOFF-4614): Add Media screen stuck when keyboard closed

## 22.99.631.0

#### iOS

[MOBOFF-5713](https://ifsdev.atlassian.net/browse/MOBOFF-5713): Fixed datetime picker frame size issue

## 22.99.630.0

#### Shared

[MOBOFF-5528](https://ifsdev.atlassian.net/browse/MOBOFF-5528): Fixed keyref string builder for object connections

#### iOS

[MOBOFF-5233](https://ifsdev.atlassian.net/browse/MOBOFF-5233): Find translations from region if specific language is not supported

## 22.99.629.0

#### iOS

[MOBOFF-3187](https://ifsdev.atlassian.net/browse/MOBOFF-3187): Display filter options in FAB menu in iOS

#### Windows

[MOBOFF-6096](https://ifsdev.atlassian.net/browse/MOBOFF-6096): Filters enabled in Calendar Control

## 22.99.628.0

#### Android

[MOBOFF-2903](https://ifsdev.atlassian.net/browse/MOBOFF-2903): Randomly appearing Hamburger menu item was removed.

## 22.99.627.0

#### Shared

[MOBOFF-5480](https://ifsdev.atlassian.net/browse/MOBOFF-5480): Fixed upload cancel command

#### iOS

[MOBOFF-3122](https://ifsdev.atlassian.net/browse/MOBOFF-3122): Add search to agenda in calendar control iOS

#### Windows

[MOBOFF-3138](https://ifsdev.atlassian.net/browse/MOBOFF-3138): Calendar Control Search in Windows

## 22.99.626.0

#### Android

[MOBOFF-2945](https://ifsdev.atlassian.net/browse/MOBOFF-2945): Fix for closing the dynamic assistant dialog not working - Android

## 22.99.624.0

#### Shared

[MOBOFF-2925](https://ifsdev.atlassian.net/browse/MOBOFF-2925): Fix document attachments not downloading for records with temp client keys  
[MOBOFF-5839](https://ifsdev.atlassian.net/browse/MOBOFF-5839): Adding Not Set Label for Boolean Fields

#### Android

[MOBOFF-5735](https://ifsdev.atlassian.net/browse/MOBOFF-5735): Removed the states badge for events without a state

## 22.99.623.0

#### Shared

[MOBOFF-3140](https://ifsdev.atlassian.net/browse/MOBOFF-3140): As a user I want to search through the appointments in the Calendar Control

## 22.99.622.0

#### Windows

[MOBOFF-4792](https://ifsdev.atlassian.net/browse/MOBOFF-4792): Removed the border in agendalist  
[MOBOFF-5139](https://ifsdev.atlassian.net/browse/MOBOFF-5139): Fix for margin in calendar control popup menu

## 22.99.621.0

#### Shared

[MOBOFF-3125](https://ifsdev.atlassian.net/browse/MOBOFF-3125): Adding logic to filter events

## 22.99.618.0

#### Shared

[MOBOFF-5407](https://ifsdev.atlassian.net/browse/MOBOFF-5407): Added a new SecondaryLoggingEnabled flag for Traceing request/response content

## 22.99.617.0

#### Android

[MOBOFF-2706](https://ifsdev.atlassian.net/browse/MOBOFF-2706): 'No items' ovelaping field was removed - Android  
[MOBOFF-4438](https://ifsdev.atlassian.net/browse/MOBOFF-4438): "No Items" Label visible even after closing the search (Android)  
[MOBOFF-5647](https://ifsdev.atlassian.net/browse/MOBOFF-5647): Fixed app crash when try to upload a file from device when the Device Storage is full-Android

## 22.99.615.0

#### Android

[MOBOFF-5858](https://ifsdev.atlassian.net/browse/MOBOFF-5858): Fix video files not showing in Gallery on some Android devices

## 22.99.601.0

#### Android

[MOBOFF-5726](https://ifsdev.atlassian.net/browse/MOBOFF-5726): Fix for agenda list order in android  
[MOBOFF-5794](https://ifsdev.atlassian.net/browse/MOBOFF-5794): Fixed crash when clicking on Work Status in main menu

#### iOS

[MOBOFF-4954](https://ifsdev.atlassian.net/browse/MOBOFF-4954): Limit landscape mode only for iPads in Calendar Control

## 22.99.600.0

#### Android

[MOBOFF-5500](https://ifsdev.atlassian.net/browse/MOBOFF-5500): Fixed app crash when connecting RA calls  
[MOBOFF-5571](https://ifsdev.atlassian.net/browse/MOBOFF-5571): Fixed app crash when navigating back with back button  
[MOBOFF-5672](https://ifsdev.atlassian.net/browse/MOBOFF-5672): Trip Tracker - Save/Cancel Buttons missing when adding a New Destination in 21R2

## 22.99.599.0

#### Shared

[MOBOFF-5156](https://ifsdev.atlassian.net/browse/MOBOFF-5156): Enable Replace Media functionality for older IFS cloud versions  
[MOBOFF-5750](https://ifsdev.atlassian.net/browse/MOBOFF-5750): Crash caused by enumeration for status badge fixed

## 22.99.598.0

#### Shared

[MOBOFF-5698](https://ifsdev.atlassian.net/browse/MOBOFF-5698): Changes in creating appointment logic in agenda view

#### Android

[MOBOFF-5746](https://ifsdev.atlassian.net/browse/MOBOFF-5746): Fix text fields losing focus after each keypress

#### Windows

[MOBOFF-5756](https://ifsdev.atlassian.net/browse/MOBOFF-5756): Fix for app crash when switching to week view from work week view  
[MOBOFF-5760](https://ifsdev.atlassian.net/browse/MOBOFF-5760): Null check in reload appointment method

## 22.99.597.0

#### Android

[MOBOFF-5588](https://ifsdev.atlassian.net/browse/MOBOFF-5588): Fix for android-Random scroll on the agenda view during the real time reload

#### iOS

[MOBOFF-5112](https://ifsdev.atlassian.net/browse/MOBOFF-5112): A "Document Sign" is shown when the option "Enable Camera" is used in Register Arrival Process - ScanIt iOS client RiverTASAurenaNative_22.99.523.0

#### Windows

[MOBOFF-5261](https://ifsdev.atlassian.net/browse/MOBOFF-5261): Fix for Status change is not updated in Agenda View Calendar Event

## 22.99.596.0

#### Shared

[MOBOFF-5698](https://ifsdev.atlassian.net/browse/MOBOFF-5698): Changes in creating appointment logic in agenda view

#### Android

[MOBOFF-4541](https://ifsdev.atlassian.net/browse/MOBOFF-4541): Android - Update mandatory field asterisk when form changes  
[MOBOFF-5406](https://ifsdev.atlassian.net/browse/MOBOFF-5406): Fix for Android, Agenda view duplicates todays date and splits the appointments

#### iOS

[MOBOFF-4541](https://ifsdev.atlassian.net/browse/MOBOFF-4541): Update mandatory field asterisk when form changes  
[MOBOFF-5563](https://ifsdev.atlassian.net/browse/MOBOFF-5563): MOBOFF-5563 Flxed app crash issue when clicking rapidly on chevron

#### Windows

[MOBOFF-5183](https://ifsdev.atlassian.net/browse/MOBOFF-5183): Fix time indicator not visible after today button  
[MOBOFF-5436](https://ifsdev.atlassian.net/browse/MOBOFF-5436): Time indicator logic enahancement  
[MOBOFF-5668](https://ifsdev.atlassian.net/browse/MOBOFF-5668): Calendar Control [Windows] - Time duration is incorrectly displayed for overnight events

## 22.99.595.0

#### Shared

[MOBOFF-5484](https://ifsdev.atlassian.net/browse/MOBOFF-5484): Status badge now shows label value instead of DB value

#### Android

[MOBOFF-3925](https://ifsdev.atlassian.net/browse/MOBOFF-3925): Corrected the text in background location disclosure message  
[MOBOFF-5413](https://ifsdev.atlassian.net/browse/MOBOFF-5413): Long running activity extension issue  
[MOBOFF-5575](https://ifsdev.atlassian.net/browse/MOBOFF-5575): remove the last shedule type select code line and replace into set defaultview method

#### iOS

[MOBOFF-3869](https://ifsdev.atlassian.net/browse/MOBOFF-3869): Fixed duplicated calendar events  
[MOBOFF-5394](https://ifsdev.atlassian.net/browse/MOBOFF-5394): Fixed time line indicator issue for long running events in Agenda View  
[MOBOFF-5413](https://ifsdev.atlassian.net/browse/MOBOFF-5413): Long running activity extension issue  
[MOBOFF-5510](https://ifsdev.atlassian.net/browse/MOBOFF-5510): Today button logic enhancement

#### Windows

[MOBOFF-5183](https://ifsdev.atlassian.net/browse/MOBOFF-5183): check whether its currnt date time before load the shedule view and set visbility of time indicator according to that  
[MOBOFF-5293](https://ifsdev.atlassian.net/browse/MOBOFF-5293): Status indicator displayed for Break when its coming as a parallel event in Day view  
[MOBOFF-5413](https://ifsdev.atlassian.net/browse/MOBOFF-5413): Long running activity extension issue

## 22.99.594.0

#### Android

[MOBOFF-5559](https://ifsdev.atlassian.net/browse/MOBOFF-5559): Floating button is not clickable when select the same calendar mode, the user is already in

## 22.99.593.0

#### Shared

[MOBOFF-2025](https://ifsdev.atlassian.net/browse/MOBOFF-2025): Aurena - Manage Media gives an error when description with max length added to media file in Auren Native

#### Android

[MOBOFF-5582](https://ifsdev.atlassian.net/browse/MOBOFF-5582): App crash fix in agenda view

#### Windows

[MOBOFF-2800](https://ifsdev.atlassian.net/browse/MOBOFF-2800): Fix translation issues in Calendar control  
[MOBOFF-4366](https://ifsdev.atlassian.net/browse/MOBOFF-4366): Calendar Control [Windows] - Status indicator in appointments not displayed correctly as per design for Work Week, Week and Agenda views.  
[MOBOFF-5224](https://ifsdev.atlassian.net/browse/MOBOFF-5224): removed unnecessary rowspan attribute from shedulegrid

## 22.99.592.0

#### Android

[MOBOFF-5546](https://ifsdev.atlassian.net/browse/MOBOFF-5546): App crash fix

#### iOS

[MOBOFF-4506](https://ifsdev.atlassian.net/browse/MOBOFF-4506): Collapse inline view when today button is clicked  
[MOBOFF-5398](https://ifsdev.atlassian.net/browse/MOBOFF-5398): iOS app crash related to time indicator implementation  
[MOBOFF-5538](https://ifsdev.atlassian.net/browse/MOBOFF-5538): Work event status indicator issue fix

#### Windows

[MOBOFF-4463](https://ifsdev.atlassian.net/browse/MOBOFF-4463): Status indicator enhancements in agenda view  
[MOBOFF-5127](https://ifsdev.atlassian.net/browse/MOBOFF-5127): Added CurrentTimeIndicatorTemplateDark according to application dark theme  
[MOBOFF-5455](https://ifsdev.atlassian.net/browse/MOBOFF-5455): Time indicator is displayed for past date in Agenda view

## 22.99.589.0

#### Android

[MOBOFF-5278](https://ifsdev.atlassian.net/browse/MOBOFF-5278): call dismiss popup methode inside the if block which checking if is it same shedule type  
[MOBOFF-5523](https://ifsdev.atlassian.net/browse/MOBOFF-5523): Missing status icons on task

#### Windows

[MOBOFF-5523](https://ifsdev.atlassian.net/browse/MOBOFF-5523): Missing staus icons on tasks  
[MOBOFF-5542](https://ifsdev.atlassian.net/browse/MOBOFF-5542): App crash occurred when go to the calendar view

## 22.99.588.0

#### Android

[MOBOFF-5413](https://ifsdev.atlassian.net/browse/MOBOFF-5413): Long running activity extension issue  
[MOBOFF-5441](https://ifsdev.atlassian.net/browse/MOBOFF-5441): Logical improvement for agenda timeline indicator

#### iOS

[MOBOFF-4451](https://ifsdev.atlassian.net/browse/MOBOFF-4451): Update status in Agenda view calendar event  
[MOBOFF-5413](https://ifsdev.atlassian.net/browse/MOBOFF-5413): Long running activity extension issue

#### Windows

[MOBOFF-5413](https://ifsdev.atlassian.net/browse/MOBOFF-5413): Long running activity extension issue  
[MOBOFF-5417](https://ifsdev.atlassian.net/browse/MOBOFF-5417): Month header text got cropped in Agenda view

## 22.99.587.0

#### iOS

[MOBOFF-5361](https://ifsdev.atlassian.net/browse/MOBOFF-5361): Fixed issue with event is not getting highlighted properly  
[MOBOFF-5370](https://ifsdev.atlassian.net/browse/MOBOFF-5370): Fixed issue in current time indicator

#### Windows

[MOBOFF-3522](https://ifsdev.atlassian.net/browse/MOBOFF-3522): Agenda timeline indicator - Change-in-Logic  
[MOBOFF-5357](https://ifsdev.atlassian.net/browse/MOBOFF-5357): Agenda view toast message logic correction

## 22.99.585.0

#### Shared

[MOBOFF-3755](https://ifsdev.atlassian.net/browse/MOBOFF-3755): Updated privacy policy url  
[MOBOFF-4257](https://ifsdev.atlassian.net/browse/MOBOFF-4257): Prevent audio uploads in attach media

#### Android

[MOBOFF-3090](https://ifsdev.atlassian.net/browse/MOBOFF-3090): Current time indicator for android agenda view added  
[MOBOFF-4553](https://ifsdev.atlassian.net/browse/MOBOFF-4553): Agenda view always starts with today's appointments  
[MOBOFF-5058](https://ifsdev.atlassian.net/browse/MOBOFF-5058): Fixed app crash when uploading mpeg files  
[MOBOFF-5348](https://ifsdev.atlassian.net/browse/MOBOFF-5348): App crash in agenda view fixed  
[MOBOFF-5353](https://ifsdev.atlassian.net/browse/MOBOFF-5353): Fix app crash when selecting calendar events and switching calendar mode

#### iOS

[MOBOFF-5353](https://ifsdev.atlassian.net/browse/MOBOFF-5353): Fix app crash when selecting events in the Syncfusion calendar

## 22.99.584.0

#### Shared

[MOBOFF-4358](https://ifsdev.atlassian.net/browse/MOBOFF-4358): Fix for status badges not showing in agenda view

#### Android

[MOBOFF-2336](https://ifsdev.atlassian.net/browse/MOBOFF-2336): Fix wrong title in standard and dynamic dialog assistants  
[MOBOFF-4796](https://ifsdev.atlassian.net/browse/MOBOFF-4796): Added check for breadcrumb label when removing view from stack  
[MOBOFF-5037](https://ifsdev.atlassian.net/browse/MOBOFF-5037): Deselect event in calendar on card panel back/forward or close  
[MOBOFF-5216](https://ifsdev.atlassian.net/browse/MOBOFF-5216): Fixed app crash in Android when reopening calendar agenda view  
[MOBOFF-5270](https://ifsdev.atlassian.net/browse/MOBOFF-5270): Today button funtionality correction

#### iOS

[MOBOFF-5037](https://ifsdev.atlassian.net/browse/MOBOFF-5037): Deselect event in calendar on card panel back/forward or close

#### Windows

[MOBOFF-4557](https://ifsdev.atlassian.net/browse/MOBOFF-4557): Agenda view now always start on todays appointments  
[MOBOFF-5270](https://ifsdev.atlassian.net/browse/MOBOFF-5270): Possible locale issue fix related to today button in agenda view

## 22.99.583.0

#### iOS

[MOBOFF-4226](https://ifsdev.atlassian.net/browse/MOBOFF-4226): Draggable FAB gets stuck at times

## 22.99.582.0

#### Shared

[MOBOFF-3246](https://ifsdev.atlassian.net/browse/MOBOFF-3246): Added long running activities in the agenda view  
[MOBOFF-1574](https://ifsdev.atlassian.net/browse/MOBOFF-1574): Stop showing location toast when uploading a camera media item and GPS is disabled on device

#### Android

[MOBOFF-3854](https://ifsdev.atlassian.net/browse/MOBOFF-3854): Wrap long text in related pages sidepanel for Android

#### iOS

[MOBOFF-3517](https://ifsdev.atlassian.net/browse/MOBOFF-3517): Add current time indicator in Agenda View  
[MOBOFF-3854](https://ifsdev.atlassian.net/browse/MOBOFF-3854): Wrap long text in related pages sidepanel

## 22.99.581.0

#### Windows

[MOBOFF-3522](https://ifsdev.atlassian.net/browse/MOBOFF-3522): Current Time Indicator in Agenda View

## 22.99.580.0

#### Shared

[MOBOFF-5291](https://ifsdev.atlassian.net/browse/MOBOFF-5291): Support navigating to dialog assistants in other client files

#### Windows

[MOBOFF-4463](https://ifsdev.atlassian.net/browse/MOBOFF-4463): Status indicators for parallel activities removed

## 22.99.579.0

#### Windows

[MOBOFF-5010](https://ifsdev.atlassian.net/browse/MOBOFF-5010): Fix duplicate status badges appearing in Agenda view  
[MOBOFF-5175](https://ifsdev.atlassian.net/browse/MOBOFF-5175): Fix app crash when switching Calendar from Agenda to other modes

## 22.99.578.0

#### Shared

[MOBOFF-4371](https://ifsdev.atlassian.net/browse/MOBOFF-4371): Reading the status badge from calendar view metatada, not form card data

#### Android

[MOBOFF-4325](https://ifsdev.atlassian.net/browse/MOBOFF-4325): Long running activity fix to stop moving to the initial date when clicked  
[MOBOFF-4342](https://ifsdev.atlassian.net/browse/MOBOFF-4342): Android: Status indicator on calendar events are incorrect for events without status  
[MOBOFF-5170](https://ifsdev.atlassian.net/browse/MOBOFF-5170): Fixing the toast for no events in agenda view to be displayed only when the todays button is clicked

#### iOS

[MOBOFF-4325](https://ifsdev.atlassian.net/browse/MOBOFF-4325): Long running activity fix to stop moving to the initial date when clicked  
[MOBOFF-4402](https://ifsdev.atlassian.net/browse/MOBOFF-4402): Event start time fix in long running activites  
[MOBOFF-4418](https://ifsdev.atlassian.net/browse/MOBOFF-4418): Agenda scroll view enhancement to handle current date with no events  
[MOBOFF-5010](https://ifsdev.atlassian.net/browse/MOBOFF-5010): Fix for status indicator not displaying in parallel events in month view

#### Windows

[MOBOFF-5170](https://ifsdev.atlassian.net/browse/MOBOFF-5170): Toast logic for agenda view fixed

## 22.99.577.0

#### Android

[MOBOFF-4414](https://ifsdev.atlassian.net/browse/MOBOFF-4414): Agendaview scroll to enhancement when there are no events for current date

## 22.99.576.0

#### Shared

[MOBOFF-4606](https://ifsdev.atlassian.net/browse/MOBOFF-4606): Added a system procedure to compute different hashes

#### Android

[MOBOFF-4317](https://ifsdev.atlassian.net/browse/MOBOFF-4317): Cell tap and header tap implementations to close panels  
[MOBOFF-4717](https://ifsdev.atlassian.net/browse/MOBOFF-4717): Android-Auto-close FAB after Calendar mode is selected

#### Windows

[MOBOFF-4410](https://ifsdev.atlassian.net/browse/MOBOFF-4410): When event card is open in the month view, time & status indicator text got cropped

## 22.99.575.0

#### Android

[MOBOFF-4193](https://ifsdev.atlassian.net/browse/MOBOFF-4193): popup window focusable property set as true.

#### Windows

[MOBOFF-4425](https://ifsdev.atlassian.net/browse/MOBOFF-4425): Fix to enable the agenda scroll to show the closest next date when current date has no events  
[MOBOFF-4570](https://ifsdev.atlassian.net/browse/MOBOFF-4570): Display current date header in blue text in agenda view

## 22.99.574.0

#### Shared

[MOBOFF-3854](https://ifsdev.atlassian.net/browse/MOBOFF-3854): UI Changes for Side Panel iPad/tablet view

## 22.99.573.0

#### Android

[MOBOFF-3108](https://ifsdev.atlassian.net/browse/MOBOFF-3108): Limit Landscape mode only for Tabs device  
[MOBOFF-5000](https://ifsdev.atlassian.net/browse/MOBOFF-5000): Commented code that sets event border in selection

#### iOS

[MOBOFF-5000](https://ifsdev.atlassian.net/browse/MOBOFF-5000): Commented code that sets event border in selection iOS

#### Windows

[MOBOFF-5000](https://ifsdev.atlassian.net/browse/MOBOFF-5000): Reverted code change from syncfusion update

## 22.99.572.0

#### Shared

[MOBOFF-4484](https://ifsdev.atlassian.net/browse/MOBOFF-4484): Update Syncfusion NuGet packages to latest to stabilize Android and Windows

#### Android

[MOBOFF-4712](https://ifsdev.atlassian.net/browse/MOBOFF-4712): Fix for Agenda view not refreshing when navigating back

#### Windows

[MOBOFF-4366](https://ifsdev.atlassian.net/browse/MOBOFF-4366): Calendar Control-Status indicator in appointments not displayed correctly as per design for Work Week, Week and Agenda views.

## 22.99.571.0

#### Shared

[MOBOFF-4622](https://ifsdev.atlassian.net/browse/MOBOFF-4622): Add Media in Assistant screen (New Work) - Video option is not available

#### Android

[MOBOFF-4663](https://ifsdev.atlassian.net/browse/MOBOFF-4663): Fix calendar badge views crashing on Android 9 and improve the area

#### iOS

[MOBOFF-4353](https://ifsdev.atlassian.net/browse/MOBOFF-4353): Show Today button after navigating back from maps screen  
[MOBOFF-4459](https://ifsdev.atlassian.net/browse/MOBOFF-4459): Removal of status indicator in parallel events after an event update

#### Windows

[MOBOFF-4463](https://ifsdev.atlassian.net/browse/MOBOFF-4463): Status indicators for parallel activities removed

## 22.99.568.0

#### Windows

[MOBOFF-4537](https://ifsdev.atlassian.net/browse/MOBOFF-4537): Event card is not opened when coming back from appointment details screen in Agenda View

## 22.99.567.0

#### Shared

[MOBOFF-4885](https://ifsdev.atlassian.net/browse/MOBOFF-4885): Fix bound action URL format to support ODP

#### Android

[MOBOFF-2860](https://ifsdev.atlassian.net/browse/MOBOFF-2860): Not getting the thumbnail after uploading a picture  
[MOBOFF-4549](https://ifsdev.atlassian.net/browse/MOBOFF-4549): Today button click in agenda fix

#### iOS

[MOBOFF-2860](https://ifsdev.atlassian.net/browse/MOBOFF-2860): Not getting the thumbnail after uploading a picture-iOS  
[MOBOFF-4805](https://ifsdev.atlassian.net/browse/MOBOFF-4805): Add gallery option back in single and multiple file pickers

#### Windows

[MOBOFF-4498](https://ifsdev.atlassian.net/browse/MOBOFF-4498): Toast message in windows fixed

## 22.99.566.0

#### Shared

[MOBOFF-4626](https://ifsdev.atlassian.net/browse/MOBOFF-4626): Use generic label for delete button in media details  
[MOBOFF-4717](https://ifsdev.atlassian.net/browse/MOBOFF-4717): Auto-close FAB after Calendar mode is selected

#### Android

[MOBOFF-4222](https://ifsdev.atlassian.net/browse/MOBOFF-4222): Set android calendar font size to default size  
[MOBOFF-4553](https://ifsdev.atlassian.net/browse/MOBOFF-4553): Agenda view starts with current date

#### Windows

[MOBOFF-2750](https://ifsdev.atlassian.net/browse/MOBOFF-2750): Fixed calendar control show label issue  
[MOBOFF-4185](https://ifsdev.atlassian.net/browse/MOBOFF-4185): not possible to close when window is too narrow

## 22.99.565.0

#### Android

[MOBOFF-4253](https://ifsdev.atlassian.net/browse/MOBOFF-4253): Set download button in media list to be not focusable so that list taps are registered  
[MOBOFF-4334](https://ifsdev.atlassian.net/browse/MOBOFF-4334): Immediately update the UI when downloading media items from list page

#### Windows

[MOBOFF-4799](https://ifsdev.atlassian.net/browse/MOBOFF-4799): Correctly show media picker options based on capabilities

## 22.99.564.0

#### Windows

[MOBOFF-4263](https://ifsdev.atlassian.net/browse/MOBOFF-4263): FAB & Agenda Design adjustments

## 22.99.563.0

#### Shared

[MOBOFF-3867](https://ifsdev.atlassian.net/browse/MOBOFF-3867): Added a new FW function to create and connect binary media

## 22.99.561.0

#### Shared

[MOBOFF-4088](https://ifsdev.atlassian.net/browse/MOBOFF-4088): Remove 'Replace media' section in the UI  
[MOBOFF-4618](https://ifsdev.atlassian.net/browse/MOBOFF-4618): Revert file size limitation for media uploads (limit removed)  
[MOBOFF-3470](https://ifsdev.atlassian.net/browse/MOBOFF-3470): [SonarCloud] "async" methods should not return "void"

#### iOS

[MOBOFF-3638](https://ifsdev.atlassian.net/browse/MOBOFF-3638): Enable Lidar button in number field

#### Windows

[MOBOFF-4557](https://ifsdev.atlassian.net/browse/MOBOFF-4557): Agenda view starts with current date

## 22.99.559.0

#### Shared

[MOBOFF-4181](https://ifsdev.atlassian.net/browse/MOBOFF-4181): Upload Failing when try to upload a Media without a Description

#### iOS

[MOBOFF-3949](https://ifsdev.atlassian.net/browse/MOBOFF-3949): Fix for Agenda iOS not refreshing automatically with new events  
[MOBOFF-4277](https://ifsdev.atlassian.net/browse/MOBOFF-4277): Attempt to fix app crashing when navigating to and from list view in MWO

## 22.99.558.0

#### iOS

[MOBOFF-2660](https://ifsdev.atlassian.net/browse/MOBOFF-2660): Not possible to add a photo (Regression)

#### Windows

[MOBOFF-4520](https://ifsdev.atlassian.net/browse/MOBOFF-4520): Sync fusion null exception workaround added

## 22.99.557.0

#### Shared

[MOBOFF-4088](https://ifsdev.atlassian.net/browse/MOBOFF-4088): Remove 'Replace media' section in the UI for 22R2 Release  
[MOBOFF-4489](https://ifsdev.atlassian.net/browse/MOBOFF-4489): Fix app crash due to null reference when setting accessibility identifiers

#### iOS

[MOBOFF-2661](https://ifsdev.atlassian.net/browse/MOBOFF-2661): Make iOS multi file picker options to be similar to Android  
[MOBOFF-4429](https://ifsdev.atlassian.net/browse/MOBOFF-4429): Fix app crash when going to settings/media details due to null command color

## 22.99.556.0

#### Shared

[MOBOFF-2357](https://ifsdev.atlassian.net/browse/MOBOFF-2357): US 7: Error-handling for uploading large media files

#### Android

[MOBOFF-3089](https://ifsdev.atlassian.net/browse/MOBOFF-3089): Current time indicator for android

#### iOS

[MOBOFF-3089](https://ifsdev.atlassian.net/browse/MOBOFF-3089): Current time indicatior for iOS

#### Windows

[MOBOFF-3089](https://ifsdev.atlassian.net/browse/MOBOFF-3089): Current time indicator for windows

## 22.99.555.0

#### iOS

[MOBOFF-1985](https://ifsdev.atlassian.net/browse/MOBOFF-1985): Fix app crash when picking files in multi file picker  
[MOBOFF-4310](https://ifsdev.atlassian.net/browse/MOBOFF-4310): Add functionality for today button in agenda view iOS

#### Windows

[MOBOFF-2775](https://ifsdev.atlassian.net/browse/MOBOFF-2775): Hide video capture option in file selector when device has no camera

## 22.99.554.0

#### Android

[MOBOFF-2753](https://ifsdev.atlassian.net/browse/MOBOFF-2753): Fix for long work week text format in Android

#### iOS

[MOBOFF-2617](https://ifsdev.atlassian.net/browse/MOBOFF-2617): US 4: As a user I want to record a video in iOS so that I can sync it to the server database

## 22.99.553.0

#### Shared

[MOBOFF-2301](https://ifsdev.atlassian.net/browse/MOBOFF-2301): As a user I want to remove an uploaded media  
[MOBOFF-3471](https://ifsdev.atlassian.net/browse/MOBOFF-3471): [SonarCloud] Getters and setters should access the expected fields  
[MOBOFF-3965](https://ifsdev.atlassian.net/browse/MOBOFF-3965): Correctly show media picker options based on the IFS Cloud version

#### Android

[MOBOFF-3092](https://ifsdev.atlassian.net/browse/MOBOFF-3092): Today button click for Agenda view android  
[MOBOFF-3897](https://ifsdev.atlassian.net/browse/MOBOFF-3897): Fixed expand media unable to upload mp4 videos

#### iOS

[MOBOFF-3932](https://ifsdev.atlassian.net/browse/MOBOFF-3932): Custom appointments for calendar control in iOS  
[MOBOFF-3990](https://ifsdev.atlassian.net/browse/MOBOFF-3990): Fix for wrong section date format  
[MOBOFF-4151](https://ifsdev.atlassian.net/browse/MOBOFF-4151): Improve the keyboard accessory view behavior

#### Windows

[MOBOFF-3092](https://ifsdev.atlassian.net/browse/MOBOFF-3092): Today button click for agenda in windows  
[MOBOFF-3953](https://ifsdev.atlassian.net/browse/MOBOFF-3953): Real time reload for agenda

## 22.99.552.0

#### iOS

[MOBOFF-4239](https://ifsdev.atlassian.net/browse/MOBOFF-4239): Syncfusion Today button added to iOS

## 22.99.551.0

#### Shared

[MOBOFF-4128](https://ifsdev.atlassian.net/browse/MOBOFF-4128): add PerfCounterCollector and DependencyCollector, upgrade libraries and tweak what we track for better insights

#### Android

[MOBOFF-3074](https://ifsdev.atlassian.net/browse/MOBOFF-3074): Implement process viewer in aurena native  
[MOBOFF-3936](https://ifsdev.atlassian.net/browse/MOBOFF-3936): Status indicator for custom appointments  
[MOBOFF-3091](https://ifsdev.atlassian.net/browse/MOBOFF-3091): Today button for calendar in android

#### Windows

[MOBOFF-3074](https://ifsdev.atlassian.net/browse/MOBOFF-3074): Implement process viewer in aurena native  
[MOBOFF-3091](https://ifsdev.atlassian.net/browse/MOBOFF-3091): Today button for calendar in windows

## 22.99.550.0

#### Shared

[MOBOFF-3472](https://ifsdev.atlassian.net/browse/MOBOFF-3472): [SonarCloud] Conditionally executed code should be reachable

## 22.99.549.0

#### Shared

[MOBOFF-2612](https://ifsdev.atlassian.net/browse/MOBOFF-2612): US 9: As a user I want to see title, description and the status of the media file on the media list page

#### Windows

[MOBOFF-4213](https://ifsdev.atlassian.net/browse/MOBOFF-4213): Inline events click handled

## 22.99.548.0

#### Shared

[MOBOFF-2238](https://ifsdev.atlassian.net/browse/MOBOFF-2238): On-demand download for video media items  
[MOBOFF-4156](https://ifsdev.atlassian.net/browse/MOBOFF-4156): Update icon library - June 2022

#### iOS

[MOBOFF-4147](https://ifsdev.atlassian.net/browse/MOBOFF-4147): Fix crash due to data type mismatch in signature field handler

#### Windows

[MOBOFF-3093](https://ifsdev.atlassian.net/browse/MOBOFF-3093): Windows agenda view

## 22.99.547.0

#### Shared

[MOBOFF-3469](https://ifsdev.atlassian.net/browse/MOBOFF-3469): Null pointer fixes from sonar cloud  
[MOBOFF-4137](https://ifsdev.atlassian.net/browse/MOBOFF-4137): Removed map pin label if the label is null

#### Windows

[MOBOFF-3937](https://ifsdev.atlassian.net/browse/MOBOFF-3937): Status Indicator added for custom view in calendar

## 22.99.546.0

#### Android

[MOBOFF-3938](https://ifsdev.atlassian.net/browse/MOBOFF-3938): Real time reload for android agenda

#### Windows

[MOBOFF-3934](https://ifsdev.atlassian.net/browse/MOBOFF-3934): Custom view for calendar events

## 22.99.545.0

#### Shared

[MOBOFF-3740](https://ifsdev.atlassian.net/browse/MOBOFF-3740): Handled index out of bound exception in CurrentCalendarIndex

## 22.99.544.0

#### Shared

[MOBOFF-3885](https://ifsdev.atlassian.net/browse/MOBOFF-3885): Fix for app crash and errors when branding elements are missing

## 22.99.540.0

#### Shared

[MOBOFF-2612](https://ifsdev.atlassian.net/browse/MOBOFF-2612): US 9: As a user I want to see title, description and the status of the media file on the media list page  
[MOBOFF-2689](https://ifsdev.atlassian.net/browse/MOBOFF-2689): As a user I should be able to capture an image / video  
[MOBOFF-3083](https://ifsdev.atlassian.net/browse/MOBOFF-3083): US 9: As a user I want to change the title and/or description of a media file  
[MOBOFF-3391](https://ifsdev.atlassian.net/browse/MOBOFF-3391): Part search - Sort locations using max hits value

#### Android

[MOBOFF-3933](https://ifsdev.atlassian.net/browse/MOBOFF-3933): Custom view for android calendar

#### iOS

[MOBOFF-2694](https://ifsdev.atlassian.net/browse/MOBOFF-2694): Improve scrollview behaviour in assistants when soft keyboard appears

#### Windows

[MOBOFF-2350](https://ifsdev.atlassian.net/browse/MOBOFF-2350): Floating Action Button for windows + Pop up window

## 22.99.535.0

#### Shared

[MOBOFF-3695](https://ifsdev.atlassian.net/browse/MOBOFF-3695): Assign "Accessibility Ids" for elements - [1] For automation

## 22.99.534.0

#### Shared

[MOBOFF-3391](https://ifsdev.atlassian.net/browse/MOBOFF-3391): Part search - Sort locations using max hits value

#### Android

[MOBOFF-3096](https://ifsdev.atlassian.net/browse/MOBOFF-3096): Sticky header for agenda view

#### iOS

[MOBOFF-3769](https://ifsdev.atlassian.net/browse/MOBOFF-3769): Fix for crash when tapping event on iOS  
[MOBOFF-3094](https://ifsdev.atlassian.net/browse/MOBOFF-3094): Add the agenda view to the calendar control in iOS

#### Windows

[MOBOFF-2350](https://ifsdev.atlassian.net/browse/MOBOFF-2350): Floating Action Button for windows + Pop up window

## 22.99.533.0

#### iOS

[MOBOFF-3778](https://ifsdev.atlassian.net/browse/MOBOFF-3778): Improve field visibility when keyboard is shown in split-view assistants

## 22.99.532.0

#### iOS

[MOBOFF-3418](https://ifsdev.atlassian.net/browse/MOBOFF-3418): Switch calendar view from FAB menu for iOS  
[MOBOFF-3850](https://ifsdev.atlassian.net/browse/MOBOFF-3850): Fix for crash on empty calendar on iOS

## 22.99.531.0

#### Shared

[MOBOFF-3695](https://ifsdev.atlassian.net/browse/MOBOFF-3695): Assign "Accessibility Ids" for elements - [1] For automation

## 22.99.530.0

#### Android

[MOBOFF-3095](https://ifsdev.atlassian.net/browse/MOBOFF-3095): Agenda view for android

## 22.99.529.0

#### Shared

[MOBOFF-3850](https://ifsdev.atlassian.net/browse/MOBOFF-3850): Null check added to prevent crash

## 22.99.528.0

#### Shared

[MOBOFF-3608](https://ifsdev.atlassian.net/browse/MOBOFF-3608): Implement map pinstyle with icon and emphasis

#### Android

[MOBOFF-3413](https://ifsdev.atlassian.net/browse/MOBOFF-3413): Switch calendar views using FAB menu

## 22.99.525.0

#### Shared

[MOBOFF-1881](https://ifsdev.atlassian.net/browse/MOBOFF-1881): Display a confirm alert for close button when removing a saved image.

## 22.99.524.0

#### Shared

[MOBOFF-2670](https://ifsdev.atlassian.net/browse/MOBOFF-2670): Improved logic to find distance between two coordinates

#### Android

[MOBOFF-3760](https://ifsdev.atlassian.net/browse/MOBOFF-3760): Translating assistant previous and next button labels

#### iOS

[MOBOFF-2457](https://ifsdev.atlassian.net/browse/MOBOFF-2457): Fix for extra space in cards in MapElementView on iOS  
[MOBOFF-3769](https://ifsdev.atlassian.net/browse/MOBOFF-3769): Fix for calendar control crash on navigation in iOS

## 22.99.523.0

#### iOS

[MOBOFF-3571](https://ifsdev.atlassian.net/browse/MOBOFF-3571): Prevent list view UI being refreshed when inactive to avoid UIViewControllerHierarchyInconsistencyException

## 22.99.518.0

#### Shared

[MOBOFF-1329](https://ifsdev.atlassian.net/browse/MOBOFF-1329): Support orderby in selectors  
[MOBOFF-3348](https://ifsdev.atlassian.net/browse/MOBOFF-3348): Hide unwanted menu options for failed signature transactions

#### Android

[MOBOFF-3103](https://ifsdev.atlassian.net/browse/MOBOFF-3103): Correcting Disclosure Dialog for Background Location Tracking

## 22.99.517.0

#### Shared

[MOBOFF-3015](https://ifsdev.atlassian.net/browse/MOBOFF-3015): Eform pops up again & again (looping)  
[MOBOFF-3199](https://ifsdev.atlassian.net/browse/MOBOFF-3199): Removed Audio upload from windows and android

## 22.99.515.0

#### Shared

[MOBOFF-3063](https://ifsdev.atlassian.net/browse/MOBOFF-3063): Enable Expand Media Item

## 22.99.513.0

#### Shared

[MOBOFF-2842](https://ifsdev.atlassian.net/browse/MOBOFF-2842): Revert reloading the record when resetting changes to fix data not populating issue

## 22.99.510.0

#### Shared

[MOBOFF-3343](https://ifsdev.atlassian.net/browse/MOBOFF-3343): Improved information displayed in failed signature transactions list

## 22.99.507.0

#### Shared

[MOBOFF-3123](https://ifsdev.atlassian.net/browse/MOBOFF-3123): Fix for show in map doesn't populate pins

## 22.99.506.0

#### Shared

[MOBOFF-2614](https://ifsdev.atlassian.net/browse/MOBOFF-2614): Added new FW function DateTime.ToFormattedString  
[MOBOFF-3015](https://ifsdev.atlassian.net/browse/MOBOFF-3015): Eform pops up again & again (looping)

#### iOS

[MOBOFF-3155](https://ifsdev.atlassian.net/browse/MOBOFF-3155): Set the HttpClientHandler to NSUrlSessionHandler

## 22.99.492.0

#### Shared

[MOBOFF-2372](https://ifsdev.atlassian.net/browse/MOBOFF-2372): Wait until references are loaded before populating data in a LOV  
[MOBOFF-2764](https://ifsdev.atlassian.net/browse/MOBOFF-2764): Calendar is not refreshed when the assistant is closed and navigated back to calendar

#### iOS

[MOBOFF-2795](https://ifsdev.atlassian.net/browse/MOBOFF-2795): Revert changes done in media and documents picker

## 22.99.491.0

#### Shared

[MOBOFF-2931](https://ifsdev.atlassian.net/browse/MOBOFF-2931): Fix for Edit Button not working as expected (Assistant is not getting opened)

## 22.99.490.0

#### iOS

[MOBOFF-2923](https://ifsdev.atlassian.net/browse/MOBOFF-2923): Fix for E-signature list Last column label disappear in PDF

## 22.99.488.0

#### Shared

[MOBOFF-2764](https://ifsdev.atlassian.net/browse/MOBOFF-2764): Calendar is not refreshed when the assistant is closed and navigated back to calendar  
[MOBOFF-2924](https://ifsdev.atlassian.net/browse/MOBOFF-2924): New procedure added to get server key from client key

#### iOS

[MOBOFF-2936](https://ifsdev.atlassian.net/browse/MOBOFF-2936): Fix for crash on home screen navigation due to status indicator disposed exception

## 22.1.487.0

#### Shared

[MOBOFF-2741](https://ifsdev.atlassian.net/browse/MOBOFF-2741): Fix for all columns not showing in List in Single-step assistant

## 22.1.486.0

#### iOS

[MOBOFF-2795](https://ifsdev.atlassian.net/browse/MOBOFF-2795): Check for expand Media support to add files

## 22.1.485.0

#### iOS

[MOBOFF-2793](https://ifsdev.atlassian.net/browse/MOBOFF-2793): Fix for no direct access to gallery in attachment

## 22.1.483.0

#### Shared

[MOBOFF-2725](https://ifsdev.atlassian.net/browse/MOBOFF-2725): Aurena Native Calendar : property "size" in calendar card fields isn't reflected in client.

## 22.1.481.0

#### Shared

[MOBOFF-2781](https://ifsdev.atlassian.net/browse/MOBOFF-2781): Duration on calendar items on Month view is not correct for short allocations

## 22.1.480.0

#### Android

[MOBOFF-2895](https://ifsdev.atlassian.net/browse/MOBOFF-2895): Display disclosure dialog for location tracking

## 22.1.477.0

#### iOS

[MOBOFF-2768](https://ifsdev.atlassian.net/browse/MOBOFF-2768): Refresh card on navigation back in iOS

## 22.1.476.0

#### Shared

[MOBOFF-2056](https://ifsdev.atlassian.net/browse/MOBOFF-2056): Change PIN code security improvement  
[MOBOFF-2063](https://ifsdev.atlassian.net/browse/MOBOFF-2063): Fix duplicate dashboard items randomly appearing  
[MOBOFF-2703](https://ifsdev.atlassian.net/browse/MOBOFF-2703): Changed files' document type to be always original  
[MOBOFF-2769](https://ifsdev.atlassian.net/browse/MOBOFF-2769): Refresh card commands in calendar after execution  
[MOBOFF-2792](https://ifsdev.atlassian.net/browse/MOBOFF-2792): Pin Code Max length validation removed

#### Android

[MOBOFF-2485](https://ifsdev.atlassian.net/browse/MOBOFF-2485): Cloud Android app warning on User Security certificates

## 22.1.465.0

#### Shared

[MOBOFF-2783](https://ifsdev.atlassian.net/browse/MOBOFF-2783): Fix select attributes in LOVs connected to online function calls

## 22.1.464.0

#### Shared

[MOBOFF-1977](https://ifsdev.atlassian.net/browse/MOBOFF-1977): Change copyright year

#### Android

[MOBOFF-2107](https://ifsdev.atlassian.net/browse/MOBOFF-2107): Fix for client crash when switching fragments

## 22.1.463.0

#### Shared

[MOBOFF-2275](https://ifsdev.atlassian.net/browse/MOBOFF-2275): Showing breadcrumb icon when there are more than one item in breadcrumbstack  
[MOBOFF-2363](https://ifsdev.atlassian.net/browse/MOBOFF-2363): Added Locale to SyncFusion calendar  
[MOBOFF-2730](https://ifsdev.atlassian.net/browse/MOBOFF-2730): Calendar Control - Timeline hours view is not consistent for all three platforms  
[MOBOFF-2740](https://ifsdev.atlassian.net/browse/MOBOFF-2740): Simple fix done for null error when opening HTML page assistant containing an invisible list  
[MOBOFF-2746](https://ifsdev.atlassian.net/browse/MOBOFF-2746): Move schedule to initial date when set  
[MOBOFF-2760](https://ifsdev.atlassian.net/browse/MOBOFF-2760): Fix for Minimum time in appointments

#### iOS

[MOBOFF-2454](https://ifsdev.atlassian.net/browse/MOBOFF-2454): Aurena Native - iOS - List gets invisible upon tapping on the editable field in the list in Single step assistant and cannot scroll the header

## 22.1.462.0

#### Shared

[MOBOFF-2054](https://ifsdev.atlassian.net/browse/MOBOFF-2054): Improve LOV performance for online/client cache entities

#### iOS

[MOBOFF-2709](https://ifsdev.atlassian.net/browse/MOBOFF-2709): Improve esignature process on iOS

## 22.1.461.0

#### Shared

[MOBOFF-2341](https://ifsdev.atlassian.net/browse/MOBOFF-2341): PIN Code allows a minimum character of 1  
[MOBOFF-2711](https://ifsdev.atlassian.net/browse/MOBOFF-2711): Reverted expand media item changes

#### Android

[MOBOFF-2728](https://ifsdev.atlassian.net/browse/MOBOFF-2728): Set a minimum height for calendar items to avoid text alignment issues

#### Windows

[MOBOFF-2482](https://ifsdev.atlassian.net/browse/MOBOFF-2482): Windows, the text in the calendar items doesn't show all the text  
[MOBOFF-2731](https://ifsdev.atlassian.net/browse/MOBOFF-2731): Windows - short calendar events are not visible

## 22.1.460.0

#### Shared

[MOBOFF-2054](https://ifsdev.atlassian.net/browse/MOBOFF-2054): Pagination support for online lists based on functions  
[MOBOFF-2616](https://ifsdev.atlassian.net/browse/MOBOFF-2616): Support luname and keyref keywords on pages and signdocument command  
[MOBOFF-2625](https://ifsdev.atlassian.net/browse/MOBOFF-2625): Aurena Native application has to be initialized to see a document detachment done in Aurena Web using DOCMAN  
[MOBOFF-2709](https://ifsdev.atlassian.net/browse/MOBOFF-2709): Fixed signdocument command not returning the result correctly  
[MOBOFF-2712](https://ifsdev.atlassian.net/browse/MOBOFF-2712): Aurena Native Calendar : ShowLabel = false in fields of events doesn't reflect in client

#### Android

[MOBOFF-2726](https://ifsdev.atlassian.net/browse/MOBOFF-2726): Disabled dragging events in calendar

#### iOS

[MOBOFF-2459](https://ifsdev.atlassian.net/browse/MOBOFF-2459): Fixed Month Inline view not working in calendar element  
[MOBOFF-2693](https://ifsdev.atlassian.net/browse/MOBOFF-2693): Aurena Native - iOS - List does not populate

## 22.1.459.0

#### Shared

[MOBOFF-2500](https://ifsdev.atlassian.net/browse/MOBOFF-2500): Fix validation to check metadata modified error and retry  
[MOBOFF-2673](https://ifsdev.atlassian.net/browse/MOBOFF-2673): Data for WorkDayStart and WorkDayEnd not mapping properly  
[MOBOFF-2697](https://ifsdev.atlassian.net/browse/MOBOFF-2697): Fix for navigation in the calendar cards is not in chronological order  
[MOBOFF-2717](https://ifsdev.atlassian.net/browse/MOBOFF-2717): Calendar refresh issue, pushed work isn't displayed

#### Android

[MOBOFF-2447](https://ifsdev.atlassian.net/browse/MOBOFF-2447): Month view has issues when changing orientation  
[MOBOFF-2510](https://ifsdev.atlassian.net/browse/MOBOFF-2510): Calendar items are displayed incorrectly  
[MOBOFF-2698](https://ifsdev.atlassian.net/browse/MOBOFF-2698): Added selection border for appointments android and windows

#### iOS

[MOBOFF-2478](https://ifsdev.atlassian.net/browse/MOBOFF-2478): Remove segments from calendar views before adding new ones

#### Windows

[MOBOFF-2594](https://ifsdev.atlassian.net/browse/MOBOFF-2594): Card is not closed when switching calendar view  
[MOBOFF-2683](https://ifsdev.atlassian.net/browse/MOBOFF-2683): Inconsistent representation of long time events

## 22.1.457.0

#### Shared

[MOBOFF-2285](https://ifsdev.atlassian.net/browse/MOBOFF-2285): Calendar control visual corrections  
[MOBOFF-2330](https://ifsdev.atlassian.net/browse/MOBOFF-2330): Aurena Native Calendar : Db column name shown instead of defined label in Calendar view  
[MOBOFF-2428](https://ifsdev.atlassian.net/browse/MOBOFF-2428): Refresh calendar control card when data has changed  
[MOBOFF-2656](https://ifsdev.atlassian.net/browse/MOBOFF-2656): Agenda View should be hidden on Calendar Control

#### Android

[MOBOFF-2269](https://ifsdev.atlassian.net/browse/MOBOFF-2269): Disable fields rearranging on orientation changes-Android  
[MOBOFF-2659](https://ifsdev.atlassian.net/browse/MOBOFF-2659): Provided package visibility to record audio

#### iOS

[MOBOFF-2269](https://ifsdev.atlassian.net/browse/MOBOFF-2269): Disable fields rearranging on orientation changes-iOS

#### Windows

[MOBOFF-2464](https://ifsdev.atlassian.net/browse/MOBOFF-2464): Fixed Windows calendar lag and crash

## 22.1.0.455

#### Shared

[MOBOFF-2596](https://ifsdev.atlassian.net/browse/MOBOFF-2596): Allow function data sources without params to work without needing a record to be loaded  
[MOBOFF-2638](https://ifsdev.atlassian.net/browse/MOBOFF-2638): Fix issues with the loading dialog on map element

## 22.1.0.454

#### Shared

[MOBOFF-2655](https://ifsdev.atlassian.net/browse/MOBOFF-2655): Enable retry if media upload fails  
[MOBZFW-729](https://ifsdev.atlassian.net/browse/MOBZFW-729): Menu Tiles changed in batch sync (merged from Apps 10)  
[MOBZFW-733](https://ifsdev.atlassian.net/browse/MOBZFW-733): Fixed crash when cancelling list loading (merged from Apps 10)  
[MOBZFW-849](https://ifsdev.atlassian.net/browse/MOBZFW-849): Fix to disable multiclicking of list items (merged from Apps 10)

## 22.1.0.451

#### Shared

[MOBOFF-2255](https://ifsdev.atlassian.net/browse/MOBOFF-2255): Bigger icon size for currently active pin  
[MOBOFF-2640](https://ifsdev.atlassian.net/browse/MOBOFF-2640): Command fix to use primary key to filter lists in repeating section

#### Android

[MOBOFF-2463](https://ifsdev.atlassian.net/browse/MOBOFF-2463): Fix for Android app crash on navigating back from calendar element  
[MOBOFF-2654](https://ifsdev.atlassian.net/browse/MOBOFF-2654): Android - app crashes when taking photo

#### iOS

[MOBOFF-2645](https://ifsdev.atlassian.net/browse/MOBOFF-2645): Fix iOS crashing with Linked Away error when generating XAdES signature

## 22.1.0.450

#### Shared

[MOBOFF-2198](https://ifsdev.atlassian.net/browse/MOBOFF-2198): Added eSignature shared services  
[MOBOFF-2225](https://ifsdev.atlassian.net/browse/MOBOFF-2225): Audio and Video media support  
[MOBOFF-2226](https://ifsdev.atlassian.net/browse/MOBOFF-2226): Record audio/video and upload it to the server  
[MOBOFF-2309](https://ifsdev.atlassian.net/browse/MOBOFF-2309): Make the Tree load only one level of children automatically  
[MOBOFF-2362](https://ifsdev.atlassian.net/browse/MOBOFF-2362): Radius Limit Circle and the correct values are not visible when we set an attribute or a variable to "RadiusLimit" keyword  
[MOBOFF-2487](https://ifsdev.atlassian.net/browse/MOBOFF-2487): Fix to use CommandContext in sign command  
[MOBOFF-2624](https://ifsdev.atlassian.net/browse/MOBOFF-2624): Fixed crash when loading global commands in HTML report page  
[MOBOFF-2637](https://ifsdev.atlassian.net/browse/MOBOFF-2637): Fix assistant page not refreshing for commands executed from within lists  
[MOBOFF-2408](https://ifsdev.atlassian.net/browse/MOBOFF-2408): Fix for Calendar event duplicates  
[MOBOFF-2226](https://ifsdev.atlassian.net/browse/MOBOFF-2226): Disabled the capability to record audio and video in imagefields  
[MOBOFF-2425](https://ifsdev.atlassian.net/browse/MOBOFF-2425): Can not see a difference in the size of the "Range Circle" based on the UOM (KM,MILES)

#### iOS

[MOBOFF-2198](https://ifsdev.atlassian.net/browse/MOBOFF-2198): Added eSignature related features and uploads monitor in sync page  
[MOBOFF-2444](https://ifsdev.atlassian.net/browse/MOBOFF-2444): Save current changes in the Calendar page in the user profile

## 22.1.0.449

#### Shared

[MOBOFF-2221](https://ifsdev.atlassian.net/browse/MOBOFF-2221): Dynamic support for allowed media types  
[MOBOFF-2329](https://ifsdev.atlassian.net/browse/MOBOFF-2329): Change the calendar non working time color to define shifts  
[MOBOFF-2598](https://ifsdev.atlassian.net/browse/MOBOFF-2598): Singleton not getting a record when it binds with map  
[MOBOFF-2562](https://ifsdev.atlassian.net/browse/MOBOFF-2562): Hid resource buttons and appointments border

## 22.1.0.448

#### Shared

[MOBOFF-2581](https://ifsdev.atlassian.net/browse/MOBOFF-2581): Change to using listElement viewdata in global list command execution

#### iOS

[MOBOFF-2268](https://ifsdev.atlassian.net/browse/MOBOFF-2268): Improve UI layouts on iPad

## 22.1.0.445

#### Shared

[MOBOFF-2215](https://ifsdev.atlassian.net/browse/MOBOFF-2215): Update Help Lightning SDK to 14.6.0  
[MOBOFF-2270](https://ifsdev.atlassian.net/browse/MOBOFF-2270): UI improvements on Labels  
[MOBOFF-2275](https://ifsdev.atlassian.net/browse/MOBOFF-2275): Ensure Breadcrumbs feature behaves consistently across platforms  
[MOBOFF-2561](https://ifsdev.atlassian.net/browse/MOBOFF-2561): Uplift Syncfusion libraries to *********  
[MOBOFF-2254](https://ifsdev.atlassian.net/browse/MOBOFF-2254): Search indicator while searching in map  
[MOBOFF-2300](https://ifsdev.atlassian.net/browse/MOBOFF-2300): GetGPSState function added to get clients GPS State

#### iOS

[MOBOFF-2579](https://ifsdev.atlassian.net/browse/MOBOFF-2579): Selector Header Background is Black

## 22.1.0.443

#### Shared

[MOBOFF-2253](https://ifsdev.atlassian.net/browse/MOBOFF-2253): added support to use group element as a search on map element  
[MOBOFF-2274](https://ifsdev.atlassian.net/browse/MOBOFF-2274): UI improvemented on Group Headers on Pages  
[MOBOFF-2276](https://ifsdev.atlassian.net/browse/MOBOFF-2276): Add more meaningful Breadcrumb icon

#### iOS

[MOBOFF-2476](https://ifsdev.atlassian.net/browse/MOBOFF-2476): UI improvements on Required Fields

## 22.1.0.442

#### Shared

[MOBOFF-2273](https://ifsdev.atlassian.net/browse/MOBOFF-2273): Fixed IFS branding color hard code issue for AurenaPageSubHeader and AurenaPageSubHeaderText

## 22.1.0.441

#### Shared

[MOBOFF-2492](https://ifsdev.atlassian.net/browse/MOBOFF-2492): Fixed AsisstantPages' Global Command Execution issue and ReportGenerator ListElement data not refresh issue

## 22.1.0.440

#### Shared

[MOBOFF-2273](https://ifsdev.atlassian.net/browse/MOBOFF-2273): Added UI improvements for Selector  
[MOBOFF-2423](https://ifsdev.atlassian.net/browse/MOBOFF-2423): Granite color scheme added  
[MOBOFF-2442](https://ifsdev.atlassian.net/browse/MOBOFF-2442): Error " Cannot create URL without specifying entity set name " when clicking commands in calendar event cards

## 22.1.0.439

#### Shared

[MOBOFF-2495](https://ifsdev.atlassian.net/browse/MOBOFF-2495): Fixed SingleRecord type command's ViewData Overriding issue

#### iOS

[MOBOFF-1147](https://ifsdev.atlassian.net/browse/MOBOFF-1147): Hyperlinks and hyperlink buttons (Email/URL/Phone) should not be IFS purple  
[MOBOFF-2337](https://ifsdev.atlassian.net/browse/MOBOFF-2337): fix for error popup on ListElementView

## 22.1.0.438

#### Shared

[MOBOFF-2453](https://ifsdev.atlassian.net/browse/MOBOFF-2453): Fixed app crash on calendar element

## 22.1.0.437

#### Shared

[MOBOFF-2271](https://ifsdev.atlassian.net/browse/MOBOFF-2271): UI improvements on Required Fields  
[MOBOFF-2378](https://ifsdev.atlassian.net/browse/MOBOFF-2378): Calendar events not showing in Windows and IOS client when using multiple entity support  
[MOBOFF-2389](https://ifsdev.atlassian.net/browse/MOBOFF-2389): Aurena Native Calendar: Defined colors not shown in events  
[MOBZFW-960](https://ifsdev.atlassian.net/browse/MOBZFW-960): Issues with Sync Process when using in different culture settings

## 22.1.0.436

#### Shared

[MOBOFF-2272](https://ifsdev.atlassian.net/browse/MOBOFF-2272): Allow fields to easily clear incorrect input  
[MOBOFF-2290](https://ifsdev.atlassian.net/browse/MOBOFF-2290): Save current changes in the Calendar page in the user profile  
[MOBOFF-2388](https://ifsdev.atlassian.net/browse/MOBOFF-2388): Fixed Aurena Native Calendar : EntitySet doesn't support in events in calendar

## 22.1.0.427

#### Shared

[MOBOFF-2184](https://ifsdev.atlassian.net/browse/MOBOFF-2184): Fixed Add button not displaying in the list page

## 22.1.0.426

#### Shared

[MOBOFF-1660](https://ifsdev.atlassian.net/browse/MOBOFF-1660): show/hide Data Sources on the Calendar based on defined Resources  
[MOBOFF-2102](https://ifsdev.atlassian.net/browse/MOBOFF-2102): Fetch and store the user's certificate info in the client  
[MOBOFF-2242](https://ifsdev.atlassian.net/browse/MOBOFF-2242): Can not pass parameters to a map page created based on a function (OnlineOnly)  
[MOBOFF-2294](https://ifsdev.atlassian.net/browse/MOBOFF-2294): Added Refresh ability to the Calendar View on data background refresh  
[MOBOFF-2331](https://ifsdev.atlassian.net/browse/MOBOFF-2331): Aurena Native Calendar : Elements defined in Card not visible  
[MOBZFW-950](https://ifsdev.atlassian.net/browse/MOBZFW-950): Documents and Media handling : Value for "enabled" not resolved from variables unless it's set directly in fileselector is fixed

#### Android

[MOBOFF-2308](https://ifsdev.atlassian.net/browse/MOBOFF-2308): Application is required to restart once Find Part command is performed

#### iOS

[MOBOFF-2037](https://ifsdev.atlassian.net/browse/MOBOFF-2037): Fix for black background on Navigationbar

## 22.1.0.425

#### Shared

[MOBOFF-2222](https://ifsdev.atlassian.net/browse/MOBOFF-2222): Mobile app crashed when navigating to a single step assistant which has a command button in a list  
[MOBOFF-2278](https://ifsdev.atlassian.net/browse/MOBOFF-2278): Fixed query based commands disabled issue  
[MOBOFF-2293](https://ifsdev.atlassian.net/browse/MOBOFF-2293): Fixed Calendar element selected Events border color not clearly visible issue  
[MOBOFF-2312](https://ifsdev.atlassian.net/browse/MOBOFF-2312): Null check and backward compatibility changes  
[MOBOFF-2323](https://ifsdev.atlassian.net/browse/MOBOFF-2323): Min pincode validation fixed when pincode is complex  
[MOBOFF-2332](https://ifsdev.atlassian.net/browse/MOBOFF-2332): Make all Syncfusion NuGet versions to be the same

#### Android

[MOBOFF-2244](https://ifsdev.atlassian.net/browse/MOBOFF-2244): Contact widget not working properly for Android  
[MOBOFF-2339](https://ifsdev.atlassian.net/browse/MOBOFF-2339): [Android] Phone contact widget not working properly for Android

## 22.1.0.424

#### Shared

[MOBOFF-1573](https://ifsdev.atlassian.net/browse/MOBOFF-1573): Support for multiple datasource in calendar events  
[MOBOFF-1955](https://ifsdev.atlassian.net/browse/MOBOFF-1955): Pin Code Enhancement  
[MOBZFW-883](https://ifsdev.atlassian.net/browse/MOBZFW-883): Added two fw functions to send media and document permission grant

## 22.1.0.423

#### Shared

[MOBOFF-1894](https://ifsdev.atlassian.net/browse/MOBOFF-1894): Implemented a way to clear client device logs  
[MOBOFF-2247](https://ifsdev.atlassian.net/browse/MOBOFF-2247): Reintroduced Agenda view to calendar control

#### Android

[MOBOFF-2091](https://ifsdev.atlassian.net/browse/MOBOFF-2091): Add GC configuration options to Android  
[MOBOFF-2118](https://ifsdev.atlassian.net/browse/MOBOFF-2118): UI Implementation added for calendar element card view

#### iOS

[MOBOFF-2117](https://ifsdev.atlassian.net/browse/MOBOFF-2117): Show a card and execute commands on a calendar item in iOS

## 22.1.0.413

#### Shared

[MOBOFF-1659](https://ifsdev.atlassian.net/browse/MOBOFF-1659): Shared code added to support card on calendar element. Windows UI implementation added

## 22.1.0.412

#### Shared

[MOBOFF-1525](https://ifsdev.atlassian.net/browse/MOBOFF-1525): commands were made invisible according to the result of the enabledfunction  
[MOBOFF-1550](https://ifsdev.atlassian.net/browse/MOBOFF-1550): Add translations for Aurena Native IFS Cloud for 21R2

#### Android

[MOBOFF-1980](https://ifsdev.atlassian.net/browse/MOBOFF-1980): Fixed DynamicAssistantPageFragment toolbar null issue

## 22.1.0.403

#### Android

[MOBOFF-1793](https://ifsdev.atlassian.net/browse/MOBOFF-1793): Lag on the latest apps on Android  
[MOBOFF-1877](https://ifsdev.atlassian.net/browse/MOBOFF-1877): Fixed Android notification onClick does nothing issue

## 22.1.0.402

#### Shared

[MOBOFF-1805](https://ifsdev.atlassian.net/browse/MOBOFF-1805): Cannot redirect to main dashboard with navigate (Trip Tracker)

#### Android

[MOBOFF-1308](https://ifsdev.atlassian.net/browse/MOBOFF-1308): Integrate Android broadcast intent launching changes done by Labs

#### Windows

[MOBOFF-1525](https://ifsdev.atlassian.net/browse/MOBOFF-1525): Fixed Windows ListElement Command's enabled/disabled render issue

## 22.1.0.401

#### Shared

[MOBOFF-1513](https://ifsdev.atlassian.net/browse/MOBOFF-1513): Issue with Map Zoom In  
[MOBOFF-1525](https://ifsdev.atlassian.net/browse/MOBOFF-1525): Added enablefunction support for command  
[MOBOFF-942](https://ifsdev.atlassian.net/browse/MOBOFF-942): Branding > Removed properties not updated correctly

#### Android

[MOBOFF-1861](https://ifsdev.atlassian.net/browse/MOBOFF-1861): Remove connection service to fix Help Lightning Sound errors in Android.

## 22.1.0.400

#### Shared

[MOBOFF-1972](https://ifsdev.atlassian.net/browse/MOBOFF-1972): Fixed issues with reference type custom fields when syncing

#### Android

[MOBOFF-1937](https://ifsdev.atlassian.net/browse/MOBOFF-1937): Fixed font style on appbar tabs and Details/Actions tabs  
[MOBOFF-931](https://ifsdev.atlassian.net/browse/MOBOFF-931): RA recent call log list not updated correctly

## 22.1.0.399

#### Shared

[MOBOFF-1670](https://ifsdev.atlassian.net/browse/MOBOFF-1670): Reverting merged changes

#### Windows

[MOBOFF-1083](https://ifsdev.atlassian.net/browse/MOBOFF-1083): The SlidableItemView used for LOV in Windows is deprecated

## 22.1.0.398

#### Shared

[MOBOFF-1892](https://ifsdev.atlassian.net/browse/MOBOFF-1892): Revising a document only limits to the same file extension as the one already attached

#### Android

[MOBOFF-1938](https://ifsdev.atlassian.net/browse/MOBOFF-1938): "No items" label randomly comes on screens with data.

## 22.1.0.397

#### Shared

[MOBOFF-1667](https://ifsdev.atlassian.net/browse/MOBOFF-1667): Wrong Number of Parameters Passed Error when load page with singleton

#### Android

[MOBOFF-1790](https://ifsdev.atlassian.net/browse/MOBOFF-1790): Edit failed transaction send button is in black color (Not visible)  
[MOBOFF-1929](https://ifsdev.atlassian.net/browse/MOBOFF-1929): Android - Wrong text colour on Details/Actions tabs in android 6

#### iOS

[MOBOFF-1078](https://ifsdev.atlassian.net/browse/MOBOFF-1078): Remove version checks in the iOS framework code

## 22.1.0.396

#### Shared

[MOBOFF-1154](https://ifsdev.atlassian.net/browse/MOBOFF-1154): When you open a screen with no data, it's not obvious if there's no data or nothing has loaded  
[MOBOFF-1485](https://ifsdev.atlassian.net/browse/MOBOFF-1485): Https validation for service url  
[MOBOFF-1524](https://ifsdev.atlassian.net/browse/MOBOFF-1524): Wrong end user error message when mobile security is missing  
[MOBOFF-1878](https://ifsdev.atlassian.net/browse/MOBOFF-1878): Image description doesn't get cleared when image is cleared  
[MOBOFF-553](https://ifsdev.atlassian.net/browse/MOBOFF-553): Work Order Map Radial Search - Pins are Visible outside of the circle radius

#### Android

[MOBOFF-1854](https://ifsdev.atlassian.net/browse/MOBOFF-1854): Wadaco - Taking picture using camera option is unavailable in Android(version 11) Aurena Native Cloud App for Register Arrival process  
[MOBOFF-1879](https://ifsdev.atlassian.net/browse/MOBOFF-1879): Fix for installed (google maps) package visibility on Android 11  
[MOBOFF-1907](https://ifsdev.atlassian.net/browse/MOBOFF-1907): Android crash on loading repeating section fixed

## 22.1.0.395

#### Shared

[MOBOFF-1755](https://ifsdev.atlassian.net/browse/MOBOFF-1755): Edit transactions page - missing attribute labels  
[MOBOFF-1780](https://ifsdev.atlassian.net/browse/MOBOFF-1780): Hid agenda in calendar

#### Android

[MOBOFF-1652](https://ifsdev.atlassian.net/browse/MOBOFF-1652): Fixed blank notification banner

## 22.1.0.394

#### Shared

[MOBOFF-1424](https://ifsdev.atlassian.net/browse/MOBOFF-1424): Navigator Menu Disabled when navigate to page from command in single step assistant  
[MOBOFF-1783](https://ifsdev.atlassian.net/browse/MOBOFF-1783): Calendar data items do not show proper text  
[MOBOFF-1789](https://ifsdev.atlassian.net/browse/MOBOFF-1789): Fixed Image editor does not show up if file extension is in UPPERCASE issue  
[MOBOFF-993](https://ifsdev.atlassian.net/browse/MOBOFF-993): E signature issues- Showing badges inside HTML

#### Android

[MOBOFF-1135](https://ifsdev.atlassian.net/browse/MOBOFF-1135): Added Background Location Access permission  
[MOBOFF-1171](https://ifsdev.atlassian.net/browse/MOBOFF-1171): Show icon in Android ButtonElementView (match Windows behavior)  
[MOBOFF-1730](https://ifsdev.atlassian.net/browse/MOBOFF-1730): (Remote Assistance) Workaround to fix the Android audio muting issue

#### Windows

[MOBOFF-1781](https://ifsdev.atlassian.net/browse/MOBOFF-1781): Unable to identify selected calendar mode, and fix Workweek text  
[MOBOFF-1782](https://ifsdev.atlassian.net/browse/MOBOFF-1782): Calendar pie menu usefulness

## 22.1.0.386

#### Shared

[MOBOFF-1775](https://ifsdev.atlassian.net/browse/MOBOFF-1775): "None" option is not visible enough in LOV  
[MOBOFF-1797](https://ifsdev.atlassian.net/browse/MOBOFF-1797): Hidden fields still takes the space from the page  
[MOBOFF-1840](https://ifsdev.atlassian.net/browse/MOBOFF-1840): Create is hidden when CrudActions are null

#### Android

[MOBOFF-1453](https://ifsdev.atlassian.net/browse/MOBOFF-1453): Fix Font inconsistency on List Views for Android  
[MOBOFF-1665](https://ifsdev.atlassian.net/browse/MOBOFF-1665): Fix for Details-Action component is not clearly visible in Android  
[MOBOFF-1772](https://ifsdev.atlassian.net/browse/MOBOFF-1772): [Android] Syncfusion Calendar - Selected button not highlighted

#### iOS

[MOBOFF-1883](https://ifsdev.atlassian.net/browse/MOBOFF-1883): Breadcrumb Navigation Crashes the Mobile App

#### Windows

[MOBOFF-1774](https://ifsdev.atlassian.net/browse/MOBOFF-1774): Fixed unnecessary space when status indicator is not shown  
[MOBOFF-1779](https://ifsdev.atlassian.net/browse/MOBOFF-1779): Fixed when no items in calendar, the modes show class names instead of actual text  
[MOBOFF-1791](https://ifsdev.atlassian.net/browse/MOBOFF-1791): Usability - No way to cancel image editor  
[MOBOFF-1843](https://ifsdev.atlassian.net/browse/MOBOFF-1843): (Windows) Hyperlink buttons (Email/URL/Phone) are not being enabled

## 22.1.0.378

#### Shared

[MOBOFF-1670](https://ifsdev.atlassian.net/browse/MOBOFF-1670): Refresh custom commands when assistant data changes

#### Android

[MOBOFF-1148](https://ifsdev.atlassian.net/browse/MOBOFF-1148): (Android) Hyperlinks and hyperlink buttons (Email/URL/Phone) should not be IFS purple  
[MOBOFF-1807](https://ifsdev.atlassian.net/browse/MOBOFF-1807): Fix for action buttons in assistant pages are not shown along with the keys in Android

#### Windows

[MOBOFF-1773](https://ifsdev.atlassian.net/browse/MOBOFF-1773): Unreadable text on file selector controls with Windows dark mode

## 21.2.0.374

#### Shared

[MOBOFF-1768](https://ifsdev.atlassian.net/browse/MOBOFF-1768): Update the CopyRight information  
[MOBOFF-1801](https://ifsdev.atlassian.net/browse/MOBOFF-1801): Fix for exceptions in calendar control  
[MOBOFF-1820](https://ifsdev.atlassian.net/browse/MOBOFF-1820): Date/Time picker has an issue with the hour  
[MOBOFF-1835](https://ifsdev.atlassian.net/browse/MOBOFF-1835): Authentication error connecting to local native odp container in dev mode  
[MOBOFF-1819](https://ifsdev.atlassian.net/browse/MOBOFF-1819): Cannot report Time between 00:00 - 00:59 in the new Date time picker

#### Android

[MOBOFF-1836](https://ifsdev.atlassian.net/browse/MOBOFF-1836): Fix crash when allowing or denying the initial location permission request  
[MOBOFF-936](https://ifsdev.atlassian.net/browse/MOBOFF-936): Wadaco - Android- hidefilter keyword not working

## 21.2.0.373

#### Shared

[MOBOFF-1767](https://ifsdev.atlassian.net/browse/MOBOFF-1767): Further improve reference and array handling key mapping  
[MOBOFF-1814](https://ifsdev.atlassian.net/browse/MOBOFF-1814): Fix replace media functionality in cloud  
[MOBOFF-1817](https://ifsdev.atlassian.net/browse/MOBOFF-1817): Client framework fix for not sending updated fields to server when new value is NULL

#### Android

[MOBOFF-1702](https://ifsdev.atlassian.net/browse/MOBOFF-1702): Fix status indicator not showing the correct color  
[MOBOFF-1811](https://ifsdev.atlassian.net/browse/MOBOFF-1811): Fix crash when selecting barcode scanner options

## 21.2.0.366

#### Shared

[MOBOFF-1770](https://ifsdev.atlassian.net/browse/MOBOFF-1770): Add the clear button to the Syncfusion picker  
[MOBOFF-1803](https://ifsdev.atlassian.net/browse/MOBOFF-1803): Status icon fix for Assistants

#### Android

[MOBOFF-1800](https://ifsdev.atlassian.net/browse/MOBOFF-1800): Android version 6 - Aurena native apps are crashing on actions  
[MOBOFF-1804](https://ifsdev.atlassian.net/browse/MOBOFF-1804): Fix inability to pick document file types for attachments  
[MOBOFF-949](https://ifsdev.atlassian.net/browse/MOBOFF-949): Secondary fix for cursor issue when the device orientation is changed  
[MOBOFF-949](https://ifsdev.atlassian.net/browse/MOBOFF-949): Wadaco- Cursor focus lost when the device orientation is changed

#### iOS

[MOBOFF-1711](https://ifsdev.atlassian.net/browse/MOBOFF-1711): Status Indicator iOS issues

## 21.2.0.365

#### Shared

[MOBOFF-1700](https://ifsdev.atlassian.net/browse/MOBOFF-1700): Fix for navigation lag issue  
[MOBOFF-1707](https://ifsdev.atlassian.net/browse/MOBOFF-1707): Added support for global commands in lists

#### Android

[MOBOFF-1709](https://ifsdev.atlassian.net/browse/MOBOFF-1709): Lag on the latest apps on Android & iOS v350

#### iOS

[MOBOFF-1670](https://ifsdev.atlassian.net/browse/MOBOFF-1670): Single Page assistant Back button disappears and bottoms are not available in iPad device

## 21.2.0.353

#### Shared

[MOBOFF-1661](https://ifsdev.atlassian.net/browse/MOBOFF-1661): Add unit tests for the calendar control  
[MOBOFF-1734](https://ifsdev.atlassian.net/browse/MOBOFF-1734): Improve reference handling logic on data sync

#### iOS

[MOBOFF-1709](https://ifsdev.atlassian.net/browse/MOBOFF-1709): Lag on the latest apps on Android & iOS v350

## 21.2.0.352

#### Shared

[MOBOFF-1398](https://ifsdev.atlassian.net/browse/MOBOFF-1398): Provide rowids of changed records after a sync and a way to get PK from those  
[MOBOFF-1577](https://ifsdev.atlassian.net/browse/MOBOFF-1577): Fix client side error handling and TransactionFailed event  
[MOBOFF-1592](https://ifsdev.atlassian.net/browse/MOBOFF-1592): Fix failing System Flow test for Create KeyEntity  
[MOBOFF-1669](https://ifsdev.atlassian.net/browse/MOBOFF-1669): Translation key removed from server errors  
[MOBOFF-1691](https://ifsdev.atlassian.net/browse/MOBOFF-1691): Add SyncFusion picker for iOS and Android  
[MOBOFF-1712](https://ifsdev.atlassian.net/browse/MOBOFF-1712): Status Icon Added for Assistants for Windows and Android  
[MOBOFF-1717](https://ifsdev.atlassian.net/browse/MOBOFF-1717): Fix for calendar not navigating to selected date on edit  
[MOBOFF-1735](https://ifsdev.atlassian.net/browse/MOBOFF-1735): Fix URL key formatting broken  
[MOBOFF-956](https://ifsdev.atlassian.net/browse/MOBOFF-956): Repeating section assistant "Object reference" error when selecting a list command

#### Android

[MOBOFF-1587](https://ifsdev.atlassian.net/browse/MOBOFF-1587): Search & Barcode icons appear black and barely visible  
[MOBOFF-1588](https://ifsdev.atlassian.net/browse/MOBOFF-1588): Android > Barcode icon is not visible inside dialogs  
[MOBOFF-1706](https://ifsdev.atlassian.net/browse/MOBOFF-1706): Status icon size and placement changed

#### iOS

[MOBOFF-1711](https://ifsdev.atlassian.net/browse/MOBOFF-1711): Status Indicator iOS issues

#### Windows

[MOBOFF-1571](https://ifsdev.atlassian.net/browse/MOBOFF-1571): Alignment changed in windows app to reflect android/iOS app  
[MOBOFF-1685](https://ifsdev.atlassian.net/browse/MOBOFF-1685): Unnecessary icon underneath Home screen elements

## 21.2.0.351

#### Shared

[MOBOFF-1531](https://ifsdev.atlassian.net/browse/MOBOFF-1531): URI is malformed(Multiple Issues)  
[MOBOFF-1690](https://ifsdev.atlassian.net/browse/MOBOFF-1690): Fix for statusindicator color not getting updated after command action  
[MOBOFF-1105](https://ifsdev.atlassian.net/browse/MOBOFF-1105): Derived System Test device identifier from the local pc (only for Dev app)

#### Android

[MOBOFF-1361](https://ifsdev.atlassian.net/browse/MOBOFF-1361): Usability - Task Steps - New icon hinders access to additional options  
[MOBOFF-1674](https://ifsdev.atlassian.net/browse/MOBOFF-1674): Add SyncFusion Calendar switch control Android  
[MOBOFF-1702](https://ifsdev.atlassian.net/browse/MOBOFF-1702): Fix for status icon not showing up in home screen

#### iOS

[MOBOFF-1675](https://ifsdev.atlassian.net/browse/MOBOFF-1675): Add SyncFusion Calendar switch control iOS

#### Windows

[MOBOFF-1673](https://ifsdev.atlassian.net/browse/MOBOFF-1673): Add SyncFusion Calendar switch control Windows

## 21.2.0.350

#### Shared

[MOBOFF-1471](https://ifsdev.atlassian.net/browse/MOBOFF-1471): Add New Pin Icons for Remote Assistance
[MOBOFF-1654](https://ifsdev.atlassian.net/browse/MOBOFF-1654): Changed the redirect URI for the DEV app  
[MOBOFF-1686](https://ifsdev.atlassian.net/browse/MOBOFF-1686): Changing EntityUpdateAsync to handle null values during Native Inline Field edit empty value.

## 21.2.0.349

#### Shared

[MOBOFF-1590](https://ifsdev.atlassian.net/browse/MOBOFF-1590): Improve list IndexOf and Remove system procedures  
[MOBOFF-1609](https://ifsdev.atlassian.net/browse/MOBOFF-1609): Fixed reference source columns not updating with incoming messages  
[MOBOFF-1614](https://ifsdev.atlassian.net/browse/MOBOFF-1614): Online Search filters changed for aurena native

## 21.2.0.348

#### Android

[MOBOFF-1682](https://ifsdev.atlassian.net/browse/MOBOFF-1682): Client error in TA Builds Java.Lang.RuntimeException

## 21.2.0.347

#### Android

[MOBOFF-1607](https://ifsdev.atlassian.net/browse/MOBOFF-1607): Fix Android permissions for apps using Remote Assistance

## 21.2.0.346

#### Shared

[MOBOFF-1602](https://ifsdev.atlassian.net/browse/MOBOFF-1602): Entity Update String handled  
[MOBOFF-1677](https://ifsdev.atlassian.net/browse/MOBOFF-1677): TripTracker app is crashing when using with the client version RiverTASAurenaNative_21.2.0.345  
[MOBOFF-1681](https://ifsdev.atlassian.net/browse/MOBOFF-1681): Fetching IPI string from DB now

#### Android

[MOBOFF-1023](https://ifsdev.atlassian.net/browse/MOBOFF-1023): Integrate Sync Fusion Calendar Android

#### iOS

[MOBOFF-1017](https://ifsdev.atlassian.net/browse/MOBOFF-1017): Integrate SyncFusion Calendar iOS  
[MOBOFF-1275](https://ifsdev.atlassian.net/browse/MOBOFF-1275): Investigate what we can use for pdf generation for eSignature-iOS

#### Windows

[MOBOFF-1027](https://ifsdev.atlassian.net/browse/MOBOFF-1027): Integrate SyncFusion Calendar Windows

## 21.2.0.345

#### Shared

[MOBOFF-1664](https://ifsdev.atlassian.net/browse/MOBOFF-1664): HelpLightning SDK uplifting from 13.10.2 to 14.2.0 and added zebra sdk for Android  
[MOBOFF-1621](https://ifsdev.atlassian.net/browse/MOBOFF-1621): ShowIn Attribute introduced for DynamicMenuItem to hide and show the dynamic nav entry  
[MOBOFF-1458](https://ifsdev.atlassian.net/browse/MOBOFF-1458): Mapped Marble Code features for Calendar Control  
[MOBOFF-1063](https://ifsdev.atlassian.net/browse/MOBOFF-1063): Fixed failing unit tests  
[MOBOFF-1264](https://ifsdev.atlassian.net/browse/MOBOFF-1264): Add key params to URL when updating document reference  
[MOBZFW-350](https://ifsdev.atlassian.net/browse/MOBZFW-350): Sync process fail when attaching documents containing a task template  
[MOBZFW-409](https://ifsdev.atlassian.net/browse/MOBZFW-409): Completed WO tile UI issue in IFS MWO Maint Eng  
[MOBZFW-416](https://ifsdev.atlassian.net/browse/MOBZFW-416): Call back command when device back button is pressed  
[MOBZFW-554](https://ifsdev.atlassian.net/browse/MOBZFW-554): Fetch datasource for LOV from additional definitions in the metadata  
[MOBZFW-598](https://ifsdev.atlassian.net/browse/MOBZFW-598): Collapse nodes on trees on initial load to improve loading times  
[MOBZFW-598](https://ifsdev.atlassian.net/browse/MOBZFW-598): Collapse nodes on trees to improve loading times  
[MOBZFW-598](https://ifsdev.atlassian.net/browse/MOBZFW-598): Set nodes to collapse on LoadNodesAndPageWithFilter  
[MOBZFW-608](https://ifsdev.atlassian.net/browse/MOBZFW-608): Further improvements for SPIE issues  
[MOBZFW-608](https://ifsdev.atlassian.net/browse/MOBZFW-608): Terminate counters and list loading when navigated away  
[MOBZFW-616](https://ifsdev.atlassian.net/browse/MOBZFW-616): Fix list counters based on functions showing zero when null is returned  
[MOBZFW-678](https://ifsdev.atlassian.net/browse/MOBZFW-678): Prevent transient errors being shown to the user  
[MOBZFW-703](https://ifsdev.atlassian.net/browse/MOBZFW-703): Fixed boolean custom field mandatory error  
[MOBZFW-722](https://ifsdev.atlassian.net/browse/MOBZFW-722): Added support for use of degrees symbol in text fields.  
[MOBZFW-722](https://ifsdev.atlassian.net/browse/MOBZFW-722): Fixed support for use of degrees symbol in text fields  
[MOBZFW-733](https://ifsdev.atlassian.net/browse/MOBZFW-733): Added null check to prevent crash when cancelling list loading

#### Android

[MOBZFW-255](https://ifsdev.atlassian.net/browse/MOBZFW-255): Fix App crash when attaching a picture with GPS turned off  
[MOBZFW-377](https://ifsdev.atlassian.net/browse/MOBZFW-377): Fix page scroll up every time you navigate back to notifications page  
[MOBZFW-378](https://ifsdev.atlassian.net/browse/MOBZFW-378): Fix webviews not loading on Android 10  
[MOBZFW-379](https://ifsdev.atlassian.net/browse/MOBZFW-379): Notify Me > 3 dot menu disappears for some notifications  
[MOBZFW-405](https://ifsdev.atlassian.net/browse/MOBZFW-405): Added white background to the signature image.

#### iOS

[MOBZFW-252](https://ifsdev.atlassian.net/browse/MOBZFW-252): Fix breadcrumb navigation on iOS for apps without dashboard  
[MOBZFW-253](https://ifsdev.atlassian.net/browse/MOBZFW-253): Fix cell separator overlapping command buttons in card lists (NotifyMe)  
[MOBZFW-332](https://ifsdev.atlassian.net/browse/MOBZFW-332): Fixed back button appearing on the dashboard screen  
[MOBZFW-382](https://ifsdev.atlassian.net/browse/MOBZFW-382): Notify Me > iOS rotation issues  
[MOBZFW-413](https://ifsdev.atlassian.net/browse/MOBZFW-413): Fix odd card list behaviour in Scan It iOS app  
[MOBZFW-552](https://ifsdev.atlassian.net/browse/MOBZFW-552): Fixed cannot incomplete/complete work task issue on iOS12  
[MOBZFW-564](https://ifsdev.atlassian.net/browse/MOBZFW-564): Fix field visibility issues when keyboard is shown  
[MOBZFW-572](https://ifsdev.atlassian.net/browse/MOBZFW-572): Fix crash in lookup (LOV) dialogs  
[MOBZFW-574](https://ifsdev.atlassian.net/browse/MOBZFW-574): Prevent modal dynamic assistants from closing when swiped down or tapped outside  
[MOBZFW-778](https://ifsdev.atlassian.net/browse/MOBZFW-778): Added BundleIconName to plist file

## 11.1.339.0

#### Shared

[MOBOFF-1476](https://ifsdev.atlassian.net/browse/MOBOFF-1476): Send correct date and time formats in URL parameters

## 11.1.338.0

#### Shared

[MOBOFF-1007](https://ifsdev.atlassian.net/browse/MOBOFF-1007): Prevent reference source columns from being updated during sync  
[MOBOFF-1187](https://ifsdev.atlassian.net/browse/MOBOFF-1187): Stops downloading messages when SyncNow returns false

## 11.1.337.0

#### Shared

[MOBOFF-1111](https://ifsdev.atlassian.net/browse/MOBOFF-1111): Upgrading nuget packages

## 11.1.336.0

#### Shared

[MOBOFF-1134](https://ifsdev.atlassian.net/browse/MOBOFF-1134): Added a method while offline to return current user and the functionality for offlineQuery to be queried by the activated user id.  
[MOBOFF-1465](https://ifsdev.atlassian.net/browse/MOBOFF-1465): Fixed support for use of degrees symbol in text fields.

#### iOS

[MOBOFF-1415](https://ifsdev.atlassian.net/browse/MOBOFF-1415): Better error handling of HL sdk errors

## 11.1.333.0

#### Shared

[MOBOFF-1465](https://ifsdev.atlassian.net/browse/MOBOFF-1465): Added support for use of degrees symbol in text fields.  
[MOBOFF-191](https://ifsdev.atlassian.net/browse/MOBOFF-191): Fixed support for 'when' keyword in tree navigation

#### Android

[MOBOFF-1383](https://ifsdev.atlassian.net/browse/MOBOFF-1383): Android - Various Errors from Remote Assistant Testing at Pre Sales Environments

## 11.1.332.0

#### Shared

[MOBOFF-191](https://ifsdev.atlassian.net/browse/MOBOFF-191): Added support for 'when' keyword in tree navigation

## 11.1.331.0

#### Shared

[MOBOFF-1364](https://ifsdev.atlassian.net/browse/MOBOFF-1364): Change online bound action and function calls to use underscore instead of dot  
[MOBOFF-1381](https://ifsdev.atlassian.net/browse/MOBOFF-1381): Allow calling bound actions in parent entity from derived entities

## 11.1.329.0

#### Shared

[MOBOFF-1018](https://ifsdev.atlassian.net/browse/MOBOFF-1018): Use server capabilities flags to enable Syncfusion for 21R2 and upwards  
[MOBOFF-1025](https://ifsdev.atlassian.net/browse/MOBOFF-1025): Embed sync fusion license key  
[MOBOFF-1390](https://ifsdev.atlassian.net/browse/MOBOFF-1390): Added a Method while offline to detect current device state  
[MOBOFF-433](https://ifsdev.atlassian.net/browse/MOBOFF-433): Use commandgroups from the metadata instead of commands

#### Android

[MOBOFF-937](https://ifsdev.atlassian.net/browse/MOBOFF-937): Search view branding issues

## 11.1.328.0

#### Shared

[MOBOFF-620](https://ifsdev.atlassian.net/browse/MOBOFF-620): Call back command when device back button is pressed

## 11.1.325.0

#### Shared

[MOBOFF-1277](https://ifsdev.atlassian.net/browse/MOBOFF-1277): Fetch datasource for LOV from additional definitions in the metadata

## 11.1.323.0

#### Shared

[MOBOFF-1021](https://ifsdev.atlassian.net/browse/MOBOFF-1021): Re enable Sync fusion for 21R2  
[MOBZFW-598](https://ifsdev.atlassian.net/browse/MOBZFW-598): Collapse nodes on trees to improve loading times

#### Android

[MOBOFF-986](https://ifsdev.atlassian.net/browse/MOBOFF-986): Client should clear out or update existing notifications  
[MOBOFF-987](https://ifsdev.atlassian.net/browse/MOBOFF-987): AN to AN call logs has missing entries

#### iOS

[MOBOFF-557](https://ifsdev.atlassian.net/browse/MOBOFF-557): Fixed ApplicationId not saving in KeyChain.

#### Windows

[MOBOFF-557](https://ifsdev.atlassian.net/browse/MOBOFF-557): Changes made for the Unique Id of the device to be taken from GetSystemIdForPublisher()

## 11.1.315.0

#### Windows

[MOBOFF-955](https://ifsdev.atlassian.net/browse/MOBOFF-955): Prevent crashes when scrolling the notifications list in Notify Me

## 11.1.314.0

#### Shared

[MOBOFF-1004](https://ifsdev.atlassian.net/browse/MOBOFF-1004): RemoteAssistance navigateswitch to more than one page according to a condition  
[MOBOFF-758](https://ifsdev.atlassian.net/browse/MOBOFF-758): Handle call cancellation when multiple users are calling  
[TECY-156](http://jira/browse/TECY-156): iOS alignment and android decline button text change

## 11.1.304.0

#### Android

[MOBOFF-879](https://ifsdev.atlassian.net/browse/MOBOFF-879): Search Icon Set  
[MOBOFF-884](https://ifsdev.atlassian.net/browse/MOBOFF-884): Search text set to branding color  
[MOBOFF-933](https://ifsdev.atlassian.net/browse/MOBOFF-933): Fixed toolbar icon colors for search, barcode and done buttons

#### iOS

[MOBOFF-833](https://ifsdev.atlassian.net/browse/MOBOFF-833): (iOS) Workflow button on Header, is not branded  
[MOBOFF-934](https://ifsdev.atlassian.net/browse/MOBOFF-934): fix for error when calling a user already in a call in iOS

## 11.0.297.0

#### Shared

[MOBOFF-928](https://ifsdev.atlassian.net/browse/MOBOFF-928): Fixed Remote Assistance throwing error repeatedly in Recent Logs

#### iOS

[MOBOFF-918](https://ifsdev.atlassian.net/browse/MOBOFF-918): Set branding to EditableViewController UIBarButtonItems

## 11.0.296.0

#### Shared

[TECY-112](http://jira/browse/TECY-112): Add usercount for timeout when calling group form recents tab

#### Android

[MOBOFF-806](https://ifsdev.atlassian.net/browse/MOBOFF-806): Added a circular app icon for pixel devices

#### iOS

[MOBOFF-912](https://ifsdev.atlassian.net/browse/MOBOFF-912): Fix card panel alignment issues  
[MOBOFF-916](https://ifsdev.atlassian.net/browse/MOBOFF-916): Set minimum supported version to iOS 14

#### Windows

[MOBOFF-894](https://ifsdev.atlassian.net/browse/MOBOFF-894): (Win) Links are not visible in Dark Mode  
[MOBOFF-913](https://ifsdev.atlassian.net/browse/MOBOFF-913): (Win) Hyperlink buttons (Email/URL/Phone) are not visible in Dark Mode

## 11.0.295.0

#### Shared

[MOBOFF-807](https://ifsdev.atlassian.net/browse/MOBOFF-807): Wadaco - Error message shows list as title and starts with constant  
[MOBOFF-887](https://ifsdev.atlassian.net/browse/MOBOFF-887): Cancelled Login on Auth page should not allow app to activate

#### iOS

[MOBOFF-870](https://ifsdev.atlassian.net/browse/MOBOFF-870): Update iOS app to IFS Purple  
[MOBOFF-889](https://ifsdev.atlassian.net/browse/MOBOFF-889): Fixed crash when entering signature in Scan It app

#### Windows

[MOBOFF-883](https://ifsdev.atlassian.net/browse/MOBOFF-883): Fixed branding in multiselector dialog

## 11.0.294.0

#### Shared

[MOBOFF-100](https://ifsdev.atlassian.net/browse/MOBOFF-100): Assistant is not refreshing after CRUD operations  
[MOBOFF-100](https://ifsdev.atlassian.net/browse/MOBOFF-100): Fixed Assistant not refreshing after removing list item from the list  
[MOBOFF-130](https://ifsdev.atlassian.net/browse/MOBOFF-130): Map location does not work when decimal separator symbol is comma  
[MOBOFF-136](https://ifsdev.atlassian.net/browse/MOBOFF-136): Cannot refresh a card of a list in an assistant  
[MOBOFF-151](https://ifsdev.atlassian.net/browse/MOBOFF-151): Fix for Sorting when no dynamic menu items are present (TEOFF-4921)  
[MOBOFF-157](https://ifsdev.atlassian.net/browse/MOBOFF-157): Fixed commands being disabled for query-based card lists  
[MOBOFF-165](https://ifsdev.atlassian.net/browse/MOBOFF-165): Fixed crashed occuring due to unsaved app meta  
[MOBOFF-181](https://ifsdev.atlassian.net/browse/MOBOFF-181): minimalistic dashboard will be shown only if DashboardIconColor property is set  
[MOBOFF-185](https://ifsdev.atlassian.net/browse/MOBOFF-185): Remove hardcoded Context Value on ActivateResource.cs  
[MOBOFF-198](https://ifsdev.atlassian.net/browse/MOBOFF-198): Remove the error message display area in embedded maps  
[MOBOFF-212](https://ifsdev.atlassian.net/browse/MOBOFF-212): Editable Lists > Object reference error occurs  
[MOBOFF-213](https://ifsdev.atlassian.net/browse/MOBOFF-213): MOBOFF-213 : Stop breadcrumb from extending unnecessarily  
[MOBOFF-214](https://ifsdev.atlassian.net/browse/MOBOFF-214): Remove SyncFusion Code and PhotoEditor for preview release  
[MOBOFF-216](https://ifsdev.atlassian.net/browse/MOBOFF-216): Fixed Favorites and Recently used items not getting saved in LOVs  
[MOBOFF-217](https://ifsdev.atlassian.net/browse/MOBOFF-217): (MOBZFW-386 Update 10) Ignore custom fields when creating local database views  
[MOBOFF-22](https://ifsdev.atlassian.net/browse/MOBOFF-22): SyncFusion Image Editor Implementation for Android / UWP  
[MOBOFF-248](https://ifsdev.atlassian.net/browse/MOBOFF-248): Fix URL error when calling bound actions in assistant finish  
[MOBOFF-255](https://ifsdev.atlassian.net/browse/MOBOFF-255): Custom Branding - Dark Theme is not being applied on iOS / Windows  
[MOBOFF-258](https://ifsdev.atlassian.net/browse/MOBOFF-258): Custom Branding > Theme doesn't return to default if user does not have branding applied  
[MOBOFF-268](https://ifsdev.atlassian.net/browse/MOBOFF-268): Changed Dashboard tile background color to IfsWhiteLight  
[MOBOFF-268](https://ifsdev.atlassian.net/browse/MOBOFF-268): Made dashboard UI update correctly for light and dark mode  
[MOBOFF-284](https://ifsdev.atlassian.net/browse/MOBOFF-284): Client to use updated branding property codes  
[MOBOFF-325](https://ifsdev.atlassian.net/browse/MOBOFF-325): Fix date and time datatypes not sent in correct format for actions  
[MOBOFF-328](https://ifsdev.atlassian.net/browse/MOBOFF-328): Fix pagination for refresh cache server calls  
[MOBOFF-338](https://ifsdev.atlassian.net/browse/MOBOFF-338): Fix unnecessary auto focus on ios and android  
[MOBOFF-339](https://ifsdev.atlassian.net/browse/MOBOFF-339): Validation of hex color codes for Aurena Native client  
[MOBOFF-35](https://ifsdev.atlassian.net/browse/MOBOFF-35): User Story 2: Able to apply branding on the device  
[MOBOFF-351](https://ifsdev.atlassian.net/browse/MOBOFF-351): Branding context not resolved correctly for client  
[MOBOFF-355](https://ifsdev.atlassian.net/browse/MOBOFF-355): Branding > Field Titles are still in IFS purple though branding is applied  
[MOBOFF-36](https://ifsdev.atlassian.net/browse/MOBOFF-36): Map customer branding element values with Aurena Native elements  
[MOBOFF-361](https://ifsdev.atlassian.net/browse/MOBOFF-361): Android > Branding > Theming behavior should be consistent across all platforms  
[MOBOFF-372](https://ifsdev.atlassian.net/browse/MOBOFF-372): Fixed null entity set name when using array data source  
[MOBOFF-375](https://ifsdev.atlassian.net/browse/MOBOFF-375): Errors with new client 211  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-231 Update 9 Hotfix) Disable login screen buttons when loading and improve home screen counters  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-240 Update 10) Fixed attachment preview not showing issue  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-242 Update 10) Use enumeration identifiers correctly in dynamic assistants  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-244 Update 10) Added new translations  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-249 Update 10) Fixed WorkOrder displaying incorrect Image attachment, when cancelling an update  
[MOBOFF-386](https://ifsdev.atlassian.net/browse/MOBOFF-386): Error when sync media with long desc  
[MOBOFF-390](https://ifsdev.atlassian.net/browse/MOBOFF-390): Load alternate image in cards when the default image is faulty  
[MOBOFF-396](https://ifsdev.atlassian.net/browse/MOBOFF-396): Fixed EditableList issues (MOBOFF-396, MOBOFF-392) reverted changes in MOBOFF-100  
[MOBOFF-401](https://ifsdev.atlassian.net/browse/MOBOFF-401): Returning of signed document to server  
[MOBOFF-435](https://ifsdev.atlassian.net/browse/MOBOFF-435): Can't process a Work assignment in client version 215  
[MOBOFF-441](https://ifsdev.atlassian.net/browse/MOBOFF-441): Evaluate when-conditions for list conditional formatting  
[MOBOFF-453](https://ifsdev.atlassian.net/browse/MOBOFF-453): Highlight modifications made in esig report  
[MOBOFF-457](https://ifsdev.atlassian.net/browse/MOBOFF-457): Fix transition row field creation logic  
[MOBOFF-501](https://ifsdev.atlassian.net/browse/MOBOFF-501): Setting initial focus to CurrentStep Elements on Cancel  
[MOBOFF-52](https://ifsdev.atlassian.net/browse/MOBOFF-52): SyncFusion Image Editor Implementation for IOS and Changes For Camera File Pick  
[MOBOFF-546](https://ifsdev.atlassian.net/browse/MOBOFF-546): Work Order Maps > Work Order card does not display when pin is selected  
[MOBOFF-564](https://ifsdev.atlassian.net/browse/MOBOFF-564): Fixed hide filter bar functionality  
[MOBOFF-609](https://ifsdev.atlassian.net/browse/MOBOFF-609): Cannot switch Users on client once token has expired  
[MOBOFF-638](https://ifsdev.atlassian.net/browse/MOBOFF-638): Change the report look and feel to align with the Aurena online version  
[MOBOFF-647](https://ifsdev.atlassian.net/browse/MOBOFF-647): Implemented HTML Element  
[MOBOFF-669](https://ifsdev.atlassian.net/browse/MOBOFF-669): FndMotOffline taking a long time to load and showing message to end user about projection optimization  
[MOBOFF-68](https://ifsdev.atlassian.net/browse/MOBOFF-68): Image Editor dialog is being displayed for Document Upload  
[MOBOFF-681](https://ifsdev.atlassian.net/browse/MOBOFF-681): Update IFS purple within the apps to the new colour  
[MOBOFF-723](https://ifsdev.atlassian.net/browse/MOBOFF-723): Refresh repeating sections element  
[MOBOFF-729](https://ifsdev.atlassian.net/browse/MOBOFF-729): When adding new Task Step you get an error  
[MOBOFF-738](https://ifsdev.atlassian.net/browse/MOBOFF-738): Fix document download status not showing after initialization  
[MOBOFF-76](https://ifsdev.atlassian.net/browse/MOBOFF-76): Update the elements based on collapsed condition  
[MOBOFF-799](https://ifsdev.atlassian.net/browse/MOBOFF-799): Arrange element support in HTML report  
[MOBOFF-802](https://ifsdev.atlassian.net/browse/MOBOFF-802): Customer Branding not always applied  
[MOBOFF-820](https://ifsdev.atlassian.net/browse/MOBOFF-820): Fix dynamic assistants not opening due to invalid type cast error  
[MOBOFF-828](https://ifsdev.atlassian.net/browse/MOBOFF-828): iOS & Android > Actions Menu  
[MOBOFF-831](https://ifsdev.atlassian.net/browse/MOBOFF-831): Fix entity set not being set properly when using editable lists  
[MOBOFF-832](https://ifsdev.atlassian.net/browse/MOBOFF-832): (iOS & Android) Sync Monitor Icons should match branding theme
[MOBOFF-838](https://ifsdev.atlassian.net/browse/MOBOFF-838): eForms can crash or throw exception on iOS  
[MOBOFF-842](https://ifsdev.atlassian.net/browse/MOBOFF-842): Fixed wrong server call url when sending null value of number datatype.  
[MOBOFF-843](https://ifsdev.atlassian.net/browse/MOBOFF-843): Remove assistant commands in the HTML view  
[MOBOFF-853](https://ifsdev.atlassian.net/browse/MOBOFF-853): Prevent reloading the record after performing a command in an assistant  
[MOBOFF-856](https://ifsdev.atlassian.net/browse/MOBOFF-856): Prevent assistant record being cleared when resetting changes  
[MOBOFF-862](https://ifsdev.atlassian.net/browse/MOBOFF-862): Add translations for Aurena Native IFS Cloud for 21R1  
[MOBOFF-863](https://ifsdev.atlassian.net/browse/MOBOFF-863): Safely load existing records in assistants without breaking existing functionality  
[MOBOFF-93](https://ifsdev.atlassian.net/browse/MOBOFF-93): Lists in assistants are getting freeze  
[MOBOFF-93](https://ifsdev.atlassian.net/browse/MOBOFF-93): MOBOFF-93: Expand single list in an assistant step  
[TECY-102](http://jira/browse/TECY-102): Use projection name for remote assistance services  
[TECY-106](http://jira/browse/TECY-106): Disconnected call hangs before throwing errors  
[TECY-108](http://jira/browse/TECY-108): Nullpointer error when calling group form recents tab  
[TECY-109](http://jira/browse/TECY-109): Handle refresh token expiry in a better way  
[TECY-124](http://jira/browse/TECY-124): Fixed string interpolation of url filter attributes  
[TECY-130](http://jira/browse/TECY-130): Add new icons to HL UI  
[TECY-140](http://jira/browse/TECY-140): Call Recents not retrieving latest call logs, if records exceed 50  
[TECY-146](http://jira/browse/TECY-146): Fixing alert message consistency  
[TECY-147](http://jira/browse/TECY-147): Fix for HL Call missing User Name as call starts  
[TECY-148](http://jira/browse/TECY-148): Prevent calls made from non initialized WOs in Remote Assistance  
[TECY-156](http://jira/browse/TECY-156): Improving calling UI on iOS and Android  
[TECY-22](http://jira/browse/TECY-22): Remote assistant - Sharing Task details with online Help Giver  
[TECY-53](http://jira/browse/TECY-53): Change notification service to accept correct remote-assistant format  
[TECY-55](http://jira/browse/TECY-55): Non HL-users should not be able to see Remote Assistance features  
[TECY-62](http://jira/browse/TECY-62): Send IsTimeout flag when a call is Cancelled  
[TECY-65](http://jira/browse/TECY-65): Fix to decline call automatically if user is already on call  
[TECY-71](http://jira/browse/TECY-71): Update call button UI and interface  
[TECY-72](http://jira/browse/TECY-72): Call status error messages should be better formatted & user friendly  
[TECY-73](http://jira/browse/TECY-73): Recents screen (Call History) have better icons  
[TECY-74](http://jira/browse/TECY-74): Update timeout for Group Calls  
[TECY-80](http://jira/browse/TECY-80): Remote assistance call pages UI improvements  
[TECY-81](http://jira/browse/TECY-81): Connecting to Call UI should be corrected  
[TECY-82](http://jira/browse/TECY-82): 'Cannot load history' error message should be corrected  
[TECY-91](http://jira/browse/TECY-91): Fixed issues around calling DND user  
[TECY-98](http://jira/browse/TECY-98): Displaying DND users on Contact list as Busy  
[TERT-1034](http://jira/browse/TERT-1034): Duplicated sort by in Pool task, Objects and Measurements tiles in Aurena native client 150  
[TERT-1094](http://jira/browse/TERT-1094): Android app crash at launch due to FirebaseInitProvider  
[TERT-1157](http://jira/browse/TERT-1157): Issue with clob where execute by signature does not get updated  
[TERT-1183](http://jira/browse/TERT-1183): Encoded URL parameter values containing '^' and '=' character  
[TERT-1206](http://jira/browse/TERT-1206): Synch error in mobile client when using CF defined as Enumeration  
[TERT-852](http://jira/browse/TERT-852): Use the first found entity set in the projection when fetching ClientCache records  
[TERT-964](http://jira/browse/TERT-964): Fix URL format for online array calls

#### Android

[MOBOFF-159](https://ifsdev.atlassian.net/browse/MOBOFF-159): Update Fonts - Android Impl  
[MOBOFF-162](https://ifsdev.atlassian.net/browse/MOBOFF-162): Implemented branding colors for dashboard  
[MOBOFF-166](https://ifsdev.atlassian.net/browse/MOBOFF-166): Fix for embedded maps not loading on Android  
[MOBOFF-209](https://ifsdev.atlassian.net/browse/MOBOFF-209): Missing Font Changes  
[MOBOFF-211](https://ifsdev.atlassian.net/browse/MOBOFF-211): Fixed save button not clearly visible  
[MOBOFF-229](https://ifsdev.atlassian.net/browse/MOBOFF-229): Fixed app not hiding characters in PIN code field  
[MOBOFF-261](https://ifsdev.atlassian.net/browse/MOBOFF-261): Removed gradient from dashboard tile background  
[MOBOFF-278](https://ifsdev.atlassian.net/browse/MOBOFF-278): Fixed icons in breadcrumb not visible  
[MOBOFF-333](https://ifsdev.atlassian.net/browse/MOBOFF-333): Added armeabi-v7a architecture as a supported architecture.  
[MOBOFF-357](https://ifsdev.atlassian.net/browse/MOBOFF-357): Android > Pin code field does not get focus  
[MOBOFF-364](https://ifsdev.atlassian.net/browse/MOBOFF-364): Fix for Workflow bar separator visibility  
[MOBOFF-374](https://ifsdev.atlassian.net/browse/MOBOFF-374): Fixed client crashing in OS6 on some screens  
[MOBOFF-382](https://ifsdev.atlassian.net/browse/MOBOFF-382): Reload map cards when orientation is changed.  
[MOBOFF-385](https://ifsdev.atlassian.net/browse/MOBOFF-385): Android > Pin code field is in different font.  
[MOBOFF-393](https://ifsdev.atlassian.net/browse/MOBOFF-393): Fixed Editable List Done button not displaying in white  
[MOBOFF-405](https://ifsdev.atlassian.net/browse/MOBOFF-405): Commands > Show Dialog > Multiple barcodes display after selecting to add a bar code  
[MOBOFF-420](https://ifsdev.atlassian.net/browse/MOBOFF-420): Android > Finish Wo > Fonts are not updated  
[MOBOFF-421](https://ifsdev.atlassian.net/browse/MOBOFF-421): Android 10 > Pressing back does not return the user to the last state  
[MOBOFF-424](https://ifsdev.atlassian.net/browse/MOBOFF-424): Fix editable list info not saving when device is in horizontal view  
[MOBOFF-424](https://ifsdev.atlassian.net/browse/MOBOFF-424): Fixed text field not updating on selecting the enter key in Editable List  
[MOBOFF-424](https://ifsdev.atlassian.net/browse/MOBOFF-424): Text field updates on pressing the Done Key(Soft keyboard) and reverted updating on Enter key press.  
[MOBOFF-426](https://ifsdev.atlassian.net/browse/MOBOFF-426): Set default menu font color to black on Android  
[MOBOFF-445](https://ifsdev.atlassian.net/browse/MOBOFF-445): Fix markdown webview not loading on Android 10  
[MOBOFF-447](https://ifsdev.atlassian.net/browse/MOBOFF-447): Android > Notify Me > 3 dot menu disappears for some notifications  
[MOBOFF-461](https://ifsdev.atlassian.net/browse/MOBOFF-461): Android > Notify Me > Page scrolls up every time you navigate back to the notifications page  
[MOBOFF-475](https://ifsdev.atlassian.net/browse/MOBOFF-475): Fixed Screenshot mode crashing the app.  
[MOBOFF-511](https://ifsdev.atlassian.net/browse/MOBOFF-511): List sorting options, cropped off and not visible on Android  
[MOBOFF-613](https://ifsdev.atlassian.net/browse/MOBOFF-613): (Android) Branding > Section Titles are still in IFS purple  
[MOBOFF-614](https://ifsdev.atlassian.net/browse/MOBOFF-614): (Android) Branding > Header bar when adding a new LoV value, is purple.  
[MOBOFF-759](https://ifsdev.atlassian.net/browse/MOBOFF-759): Prevent incoming call screen to show multiple times on Android  
[MOBOFF-821](https://ifsdev.atlassian.net/browse/MOBOFF-821): Placeholder set to default icon till attachment finishes the download  
[MOBOFF-823](https://ifsdev.atlassian.net/browse/MOBOFF-823): (Android) Branding > "Add new" on Free input LOV is in IFS purple  
[MOBOFF-835](https://ifsdev.atlassian.net/browse/MOBOFF-835): (Android) Login > App Name on Header still in branding after user logs out  
[MOBOFF-84](https://ifsdev.atlassian.net/browse/MOBOFF-84): UI issues with Android app  
[MOBOFF-844](https://ifsdev.atlassian.net/browse/MOBOFF-844): Loading the html content to the assistant when the orientation is switched  
[MOBOFF-859](https://ifsdev.atlassian.net/browse/MOBOFF-859): Text color in search field set to appbar text color  
[MOBOFF-869](https://ifsdev.atlassian.net/browse/MOBOFF-869): (Android) Update app to IFS Purple  
[MOBOFF-885](https://ifsdev.atlassian.net/browse/MOBOFF-885): Fix slow scrolling due to property change events in menu items  
[TECY-118](http://jira/browse/TECY-118): Remote assistant is not working in Samsung S6 Edge  
[TECY-121](http://jira/browse/TECY-121): Fixed group call timeout falling back to client timeout  
[TECY-131](http://jira/browse/TECY-131): App crash when Android declines call and out of network  
[TECY-138](http://jira/browse/TECY-138): Incorrect client message on Android client  
[TECY-151](http://jira/browse/TECY-151): Added VideoResourceOn/Off icons for Remote Assistance  
[TECY-23](http://jira/browse/TECY-23): Integrate Help Lightning Nuget package - Android  
[TECY-29](http://jira/browse/TECY-29): Client app call fails gracefully for loss of connection  
[TECY-5](http://jira/browse/TECY-5): Migrate to AndroidX libraries in Aurena Native Xamarin FW  
[TECY-52](http://jira/browse/TECY-52): notification sent from installed/app devices does not display on device  
[TECY-54](http://jira/browse/TECY-54): Android-client times out and fails to join call  
[TECY-56](http://jira/browse/TECY-56): Fix for Adapters duplicating items  
[TECY-58](http://jira/browse/TECY-58): Loading progress dialog is not dismissed when call ends  
[TECY-59](http://jira/browse/TECY-59): Android crashes if you make/receive call when refresh token is expired  
[TECY-60](http://jira/browse/TECY-60): Android crashes when called a second time  
[TECY-63](http://jira/browse/TECY-63): Upload screen captures to Media Library  
[TECY-66](http://jira/browse/TECY-66): Update HL Android SDK  
[TECY-75](http://jira/browse/TECY-75): Add ring tone/dial tone for Android  
[TECY-84](http://jira/browse/TECY-84): Android > App should request permissions before initiating Remote Assistance call  
[TECY-95](http://jira/browse/TECY-95): Android > Unformatted Error messages  
[TECY-96](http://jira/browse/TECY-96): Android > Phone log permissions request  
[TECY-99](http://jira/browse/TECY-99): Update HL Android SDK  
[TERT-734](http://jira/browse/TERT-734): Remove deprecated DialogFragment in Android that causes build errors  
[TERT-839](http://jira/browse/TERT-839): Fix android build issues

#### iOS

[MOBOFF-174](https://ifsdev.atlassian.net/browse/MOBOFF-174): Implemented branding colors for iOS dashboard  
[MOBOFF-176](https://ifsdev.atlassian.net/browse/MOBOFF-176): Update iOS fonts  
[MOBOFF-179](https://ifsdev.atlassian.net/browse/MOBOFF-179): Fix crash when adding image as media item  
[MOBOFF-186](https://ifsdev.atlassian.net/browse/MOBOFF-186): Able to apply branding on the device - IOS  
[MOBOFF-238](https://ifsdev.atlassian.net/browse/MOBOFF-238): Fix dialog assistants not closing after another dialog opens on top  
[MOBOFF-252](https://ifsdev.atlassian.net/browse/MOBOFF-252): Forced Light Mode in the info.plist file  
[MOBOFF-260](https://ifsdev.atlassian.net/browse/MOBOFF-260): Fix for branding UI issues  
[MOBOFF-293](https://ifsdev.atlassian.net/browse/MOBOFF-293): Remove branding from DetailRelatedPage label  
[MOBOFF-301](https://ifsdev.atlassian.net/browse/MOBOFF-301): Fix crash due to setting alert action font  
[MOBOFF-308](https://ifsdev.atlassian.net/browse/MOBOFF-308): Fix font related errors on iOS  
[MOBOFF-317](https://ifsdev.atlassian.net/browse/MOBOFF-317): Fix dark mode issues in LOV dialogs  
[MOBOFF-335](https://ifsdev.atlassian.net/browse/MOBOFF-335): Fix for label not formatted on breadcrumb stack  
[MOBOFF-345](https://ifsdev.atlassian.net/browse/MOBOFF-345): Fix for MenuPopOver transparency issues  
[MOBOFF-346](https://ifsdev.atlassian.net/browse/MOBOFF-346): Add fonts to different UIbarButtonItem states  
[MOBOFF-347](https://ifsdev.atlassian.net/browse/MOBOFF-347): Add new font for MarkdownViews  
[MOBOFF-348](https://ifsdev.atlassian.net/browse/MOBOFF-348): Set new font to UmaTableViewController title  
[MOBOFF-363](https://ifsdev.atlassian.net/browse/MOBOFF-363): Remove branding on MenuTable items  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-250 Update 10) Fix wrong header button being shown for commands page in a workflow  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-252 Update 10) Fix breadcrumb navigation on iOS for apps without dashboard  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-253 Update 10) Fix cell separator overlapping command buttons in card lists (NotifyMe)  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-372 Update 10) Fix iOS dialog assistants not closing  
[MOBOFF-411](https://ifsdev.atlassian.net/browse/MOBOFF-411): Fix for dim view not resizing on rotation  
[MOBOFF-414](https://ifsdev.atlassian.net/browse/MOBOFF-414): Fixing ios app alignment issues  
[MOBOFF-440](https://ifsdev.atlassian.net/browse/MOBOFF-440): Inline Report Edit control iOS  
[MOBOFF-442](https://ifsdev.atlassian.net/browse/MOBOFF-442): Generate a PDF format of the report - iOS  
[MOBOFF-458](https://ifsdev.atlassian.net/browse/MOBOFF-458): Make iOS report screen async  
[MOBOFF-555](https://ifsdev.atlassian.net/browse/MOBOFF-555): Fix issue with loading images in card list on iOS  
[MOBOFF-565](https://ifsdev.atlassian.net/browse/MOBOFF-565): Change field label color to black and sort active indicator to blue  
[MOBOFF-636](https://ifsdev.atlassian.net/browse/MOBOFF-636): Remove prefix from notifications on iOS  
[MOBOFF-640](https://ifsdev.atlassian.net/browse/MOBOFF-640): Fix for alignment issues in ScanIt app iOS  
[MOBOFF-721](https://ifsdev.atlassian.net/browse/MOBOFF-721): iOS not applying app bar text color  
[MOBOFF-834](https://ifsdev.atlassian.net/browse/MOBOFF-834): (iOS) Login > "Connect" button still in branding after user logs out  
[MOBOFF-846](https://ifsdev.atlassian.net/browse/MOBOFF-846): Remove usages of OverrideUserInterfaceStyle to prevent crash in BrowserStack  
[MOBOFF-851](https://ifsdev.atlassian.net/browse/MOBOFF-851): Fix for font inconsistency on cards in iOS  
[MOBOFF-857](https://ifsdev.atlassian.net/browse/MOBOFF-857): Fix more app crashes on iOS 12  
[MOBOFF-871](https://ifsdev.atlassian.net/browse/MOBOFF-871): Use old wheel-style date time pickers on iOS 14 as well  
[MOBOFF-873](https://ifsdev.atlassian.net/browse/MOBOFF-873): Fix field visibility issues when keyboard is shown  
[TECY-104](http://jira/browse/TECY-104): iOS Recent Calls & Contacts icons should be black  
[TECY-105](http://jira/browse/TECY-105): Set branding to progress indicator on iOS  
[TECY-113](http://jira/browse/TECY-113): Fix for group unnecesary call connection on iOS  
[TECY-115](http://jira/browse/TECY-115): Upload screen captures on iOS for HelpLighting  
[TECY-116](http://jira/browse/TECY-116): Improve Remote Assistance call cancellation on iOS  
[TECY-119](http://jira/browse/TECY-119): Prevent device rotation on call screens  
[TECY-126](http://jira/browse/TECY-126): Drop call when connection is lost or timed out on iOS  
[TECY-128](http://jira/browse/TECY-128): Dismiss incoming call UI with buttons when connection lost on iOS  
[TECY-137](http://jira/browse/TECY-137): Fix for Ongoing call dismissed on connection in iOS  
[TECY-142](http://jira/browse/TECY-142): iOS call connecting screen UI improvements  
[TECY-143](http://jira/browse/TECY-143): Decline call to DND user on iOS  
[TECY-155](http://jira/browse/TECY-155): Prevent unnecesarily error messages on iOS in Remote Assistance  
[TECY-157](http://jira/browse/TECY-157): Prevent double navigation into incoming call on remote Assistant in iOS  
[TECY-158](http://jira/browse/TECY-158): Prevent timed out alert on outgoing calls on iOS  
[TECY-49](http://jira/browse/TECY-49): Fix for call history TableView not refreshing on scroll  
[TECY-57](http://jira/browse/TECY-57): Added tone and ringtone to remote assistant  
[TECY-65](http://jira/browse/TECY-65): Better handle when the user being called is on another call  
[TECY-7](http://jira/browse/TECY-7): Integrate Help Lightning Nuget package for iOS  
[TECY-85](http://jira/browse/TECY-85): Change Loading overlay background color and text on iOS  
[TECY-87](http://jira/browse/TECY-87): Check for video permission on Remote Assistant in iOS

#### Windows

[MOBOFF-177](https://ifsdev.atlassian.net/browse/MOBOFF-177): Update Fonts Windows  
[MOBOFF-181](https://ifsdev.atlassian.net/browse/MOBOFF-181): Implementing foreground color to match branding colors  
[MOBOFF-188](https://ifsdev.atlassian.net/browse/MOBOFF-188): Letters are not readable  
[MOBOFF-189](https://ifsdev.atlassian.net/browse/MOBOFF-189): MOBOFF-189: Added a ScrollViewer to the login page.  
[MOBOFF-200](https://ifsdev.atlassian.net/browse/MOBOFF-200): Set About button background to transparent on Windows  
[MOBOFF-206](https://ifsdev.atlassian.net/browse/MOBOFF-206): Align about dialog in the login page to center  
[MOBOFF-225](https://ifsdev.atlassian.net/browse/MOBOFF-225): Actions go blank when the Screen is minimized  
[MOBOFF-257](https://ifsdev.atlassian.net/browse/MOBOFF-257): Fixed text is missing from breadcrumb menu button  
[MOBOFF-290](https://ifsdev.atlassian.net/browse/MOBOFF-290): eForm Page Background color fix  
[MOBOFF-296](https://ifsdev.atlassian.net/browse/MOBOFF-296): Fix for branding not applied on workflow and subheader texts  
[MOBOFF-297](https://ifsdev.atlassian.net/browse/MOBOFF-297): Align disconnect button with text fields  
[MOBOFF-344](https://ifsdev.atlassian.net/browse/MOBOFF-344): Fix for app bar losing color when not focused  
[MOBOFF-353](https://ifsdev.atlassian.net/browse/MOBOFF-353): Fix for branding not being applied to Sync bar  
[MOBOFF-354](https://ifsdev.atlassian.net/browse/MOBOFF-354): Win > Branding > Dialog UI not updated correctly  
[MOBOFF-356](https://ifsdev.atlassian.net/browse/MOBOFF-356): Win > Branding > UI is not updated correctly  
[MOBOFF-358](https://ifsdev.atlassian.net/browse/MOBOFF-358): Android > Deactivating and logging in with different user throws error.  
[MOBOFF-360](https://ifsdev.atlassian.net/browse/MOBOFF-360): Set the top bar to normal IFS purple only in the login page  
[MOBOFF-362](https://ifsdev.atlassian.net/browse/MOBOFF-362): Change buttons text to normal weight on Windows  
[MOBOFF-379](https://ifsdev.atlassian.net/browse/MOBOFF-379): (MOBZFW-251 Update 10) Added attachments button to Company screen  
[MOBOFF-395](https://ifsdev.atlassian.net/browse/MOBOFF-395): Fixed text field not updating on selecting the enter key in Editable List  
[MOBOFF-423](https://ifsdev.atlassian.net/browse/MOBOFF-423): Fix for Windows login screen wrong background color  
[MOBOFF-428](https://ifsdev.atlassian.net/browse/MOBOFF-428): Add Branding to dialogs in Windows  
[MOBOFF-429](https://ifsdev.atlassian.net/browse/MOBOFF-429): Fixed Dialog title font size being too big.  
[MOBOFF-465](https://ifsdev.atlassian.net/browse/MOBOFF-465): Custom Branding > Win > Branding context cleared on app reopening  
[MOBOFF-497](https://ifsdev.atlassian.net/browse/MOBOFF-497): Add a null check for EntitySetName in ValidateAndSaveImple  
[MOBOFF-515](https://ifsdev.atlassian.net/browse/MOBOFF-515): Fixed company details not displaying.  
[MOBOFF-519](https://ifsdev.atlassian.net/browse/MOBOFF-519): Removed the prefix with the colon in push notifications  
[MOBOFF-616](https://ifsdev.atlassian.net/browse/MOBOFF-616): Changed Hover-over and onPressed color of buttons in the title bar to a neutral color.  
[MOBOFF-822](https://ifsdev.atlassian.net/browse/MOBOFF-822): Record received push notification data in the log  
[MOBOFF-868](https://ifsdev.atlassian.net/browse/MOBOFF-868): (Win) Update app to IFS Purple  
[MOBOFF-874](https://ifsdev.atlassian.net/browse/MOBOFF-874): Clear button fix

## 10.9.1327.0

#### iOS

[TEOFF-5301](http://jira/browse/TEOFF-5301): Fix for wrong layouts on cards in CardListViewController  
[TEOFF-5304](http://jira/browse/TEOFF-5304): Handle assistant dialog closing with swipe on iOS 13

## 10.9.1325.0

#### Shared

[TEOFF-5264](http://jira/browse/TEOFF-5264): Fix missing side menu entry when there is only one item  
[TEOFF-5273](http://jira/browse/TEOFF-5273): Fix for counters are missing/disappearing in Home Screen

#### Android

[TEOFF-5077](http://jira/browse/TEOFF-5077): Changed OnBackPressed method to execute cancel command if available.  
[TEOFF-5265](http://jira/browse/TEOFF-5265): Make the apps compatible on devices without a camera

#### iOS

[TEOFF-5302](http://jira/browse/TEOFF-5302): Fix search not working after navigating from a list and coming back

## 10.9.1324.0

#### iOS

[TEOFF-5241](http://jira/browse/TEOFF-5241): Fix wrong markdown text updates in card list  
[TEOFF-5262](http://jira/browse/TEOFF-5262): Make element dialogs scrollable

#### Windows

[TEOFF-5260](http://jira/browse/TEOFF-5260): Fix the only page not loading by default

## 10.9.1322.0

#### Shared

[TEOFF-4921](http://jira/browse/TEOFF-4921): Order Home screen Elements Splitted App (Split related)

## 10.9.1321.0

#### iOS

[MOBZFW-203](https://ifsdev.atlassian.net/browse/MOBZFW-203): Handle dialogs being dismissed by swipe

## 10.9.1319.0

#### Shared

[TEOFF-5229](http://jira/browse/TEOFF-5229): Prevent automatically focusing on the first field

## 10.9.1318.0

#### iOS

[TEOFF-4827](http://jira/browse/TEOFF-4827): Efficient use of space on a small screen

## 10.9.1317.0

#### Shared

[TEOFF-5209](http://jira/browse/TEOFF-5209): Ability to show/hide navigator entries using a function call

#### iOS

[TEOFF-4317](http://jira/browse/TEOFF-4317): Force light theme on iOS

## 10.9.1316.0

#### Shared

[TEOFF-5203](http://jira/browse/TEOFF-5203): Prevent copying null values when calling copyCustomFields

## 10.9.1315.0

#### Android

[TEOFF-5182](http://jira/browse/TEOFF-5182): Editable List item not editable under Add button

## 10.9.1313.0

#### Shared

[TEOFF-4935](http://jira/browse/TEOFF-4935): Remove the field limit on cards in lists

#### Android

[TEOFF-4964](http://jira/browse/TEOFF-4964): Wadaco Native - Load circle is covered by the headers  
[TEOFF-5035](http://jira/browse/TEOFF-5035): Fix wrong icon for last step of workflow  
[TEOFF-5129](http://jira/browse/TEOFF-5129): Work Order cards are not displaying correctly

#### iOS

[TEOFF-4737](http://jira/browse/TEOFF-4737): Fix search and sort options not working in lists inside assistants  
[TEOFF-5062](http://jira/browse/TEOFF-5062): Fix greyed out text in the description of an assistant inside a workflow  
[TEOFF-5096](http://jira/browse/TEOFF-5096): Prevent the detail page tabs becoming greyed out when editing

## 10.9.1312.0

#### Android

[TEOFF-4603](http://jira/browse/TEOFF-4603): Work Orders Map - When rotating from portrait to landscape mode the card does not remain displayed

#### iOS

[TEOFF-3384](http://jira/browse/TEOFF-3384): Renamed Done to Cancel in search bar for all iOS versions  
[TEOFF-4692](http://jira/browse/TEOFF-4692): Center align text in delete command  
[TEOFF-5215](http://jira/browse/TEOFF-5215): Fixed crash in home screen on iOS 12 due to background colour change

## 10.9.1311.0

#### Shared

[TEOFF-4014](http://jira/browse/TEOFF-4014): Amended GoToPreviousStep dialog string in dynamic assistants

#### Android

[TEOFF-5018](http://jira/browse/TEOFF-5018): Android > Single Step > Cursor does not remain focused on field when device is rotated

## 10.9.1308.0

#### Shared

[TEOFF-4795](http://jira/browse/TEOFF-4795): Parameterize input masks to turn on and off accented characters  
[TEOFF-4926](http://jira/browse/TEOFF-4926): Media item is not saved when location is set to off  
[TEOFF-5204](http://jira/browse/TEOFF-5204): Ability to change the Assistant Finish Command Label

#### iOS

[TEOFF-4722](http://jira/browse/TEOFF-4722): Disable phone number highlighting in large text edits

#### Windows

[TEOFF-5166](http://jira/browse/TEOFF-5166): Dashboard icon labels are not centrally aligned

## 10.9.1307.0

#### iOS

[TEOFF-4676](http://jira/browse/TEOFF-4676): Fixed spacing between dashboard icons in landscape mode  
[TEOFF-4746](http://jira/browse/TEOFF-4746): Fix app crash when using editable list command buttons

## 10.9.1306.0

#### Shared

[TEOFF-4141](http://jira/browse/TEOFF-4141): Added ability to control 'Document Revisions' in Work Flow Configuration  
[TEOFF-4847](http://jira/browse/TEOFF-4847): Hide or remove the filter input when not needed

#### iOS

[TEOFF-5186](http://jira/browse/TEOFF-5186): Fix for input field label not visible on initialFocus

#### Windows

[TEOFF-5151](http://jira/browse/TEOFF-5151): When navigate back, commands get disabled and page information is not shown.

## 10.9.1305.0

#### iOS

[TEOFF-5152](http://jira/browse/TEOFF-5152): Fix for initial focus not being set  
[TEOFF-5174](http://jira/browse/TEOFF-5174): Missing Accessibility ID for Actions Menu Items - IOS

## 10.9.1304.0

#### Shared

[TEOFF-4823](http://jira/browse/TEOFF-4823): Add/fix missing Accessibility ID:s in NextGen Offline framework

#### iOS

[TEOFF-5083](http://jira/browse/TEOFF-5083): Fix for wrong header in UmaTableViewController

#### Windows

[TEOFF-5094](http://jira/browse/TEOFF-5094): Editable List items are not displaying correctly

## 10.9.1303.0

#### Shared

[TEOFF-4792](http://jira/browse/TEOFF-4792): Copy structure suppport  
[TEOFF-4890](http://jira/browse/TEOFF-4890): Allow an icon and item order to be defined for dynamic menu items

## 10.9.1302.0

#### Shared

[TEOFF-4918](http://jira/browse/TEOFF-4918): Ability to change the Assistant Finish Command Label

#### Android

[MOBZFW-143](https://ifsdev.atlassian.net/browse/MOBZFW-143): Fix for error pop up on multiple measurements

## 10.9.1301.0

#### Shared

[TEOFF-4932](http://jira/browse/TEOFF-4932): Size property in imagefield does not working

#### iOS

[TEOFF-5098](http://jira/browse/TEOFF-5098): Fixed failure to navigate forward after viewing wo report before customer signature

#### Windows

[MOBZFW-2](https://ifsdev.atlassian.net/browse/MOBZFW-2): Suppress exception popups when user has logged out  
[TEOFF-5025](http://jira/browse/TEOFF-5025): Fix for focus not being set on enter key on Windows

## 10.9.1300.0

#### Shared

[TEOFF-5020](http://jira/browse/TEOFF-5020): Fix for text in badges not being wrapped on Windows and iOS

#### iOS

[TEOFF-3133](http://jira/browse/TEOFF-3133): Add barcode scan button to entry fields in iOS  
[TEOFF-5022](http://jira/browse/TEOFF-5022): Removed white spacing when step description is null

#### Windows

[TEOFF-5021](http://jira/browse/TEOFF-5021): Fix for sound effects not playing sometimes on Windows

## 10.9.1299.0

#### Shared

[MOBZFW-1](https://ifsdev.atlassian.net/browse/MOBZFW-1): Work order report not coming after customer signature

#### Windows

[TEOFF-3890](http://jira/browse/TEOFF-3890): Performance improvements to Windows client

## 10.9.1298.0

#### Shared

[TEOFF-4058](http://jira/browse/TEOFF-4058): Add location pin in card lists

#### iOS

[TEOFF-4886](http://jira/browse/TEOFF-4886): Check and fix deprecated UIWebView warnings

#### Windows

[TEOFF-4794](http://jira/browse/TEOFF-4794): Fixed start position on masked input text helper

## 10.8.1297.0

#### Windows

[TEOFF-5027](http://jira/browse/TEOFF-5027): Revert initialfocus changes to Windows client

## 10.8.1296.0

#### Windows

[TEOFF-4622](http://jira/browse/TEOFF-4622): Fix layout cycle crash on Windows in card lists

## 10.8.1295.0

#### iOS

[TEOFF-5013](http://jira/browse/TEOFF-5013): Fix assistant custom command height issue in landscape

## 10.8.1294.0

#### iOS

[TEOFF-4735](http://jira/browse/TEOFF-4735): Prevent duplicate back button appearing in assistants  
[TEOFF-4985](http://jira/browse/TEOFF-4985): Improve keyboard behaviour on iOS  
[TEOFF-4990](http://jira/browse/TEOFF-4990): Fixed not being able to open keyboard

## 10.8.1293.0

#### Shared

[TEOFF-4895](http://jira/browse/TEOFF-4895): Improve element display in single step assistants

#### iOS

[TEOFF-4896](http://jira/browse/TEOFF-4896): Move Stock - Outgoing Stock does not open  
[TEOFF-4989](http://jira/browse/TEOFF-4989): Fix crash when using NavigationController in assistants

## 10.8.1292.0

#### Shared

[TEOFF-4826](http://jira/browse/TEOFF-4826): Initial focus on fields in AssistantPage and ElementPage  
[TEOFF-4898](http://jira/browse/TEOFF-4898): Trigger sync for all types of notifications  
[TEOFF-4959](http://jira/browse/TEOFF-4959): Prevent keyboard from appearing automatically on initial focus

#### iOS

[TEOFF-4955](http://jira/browse/TEOFF-4955): Removed hyperlink for URL on the About page  
[TEOFF-4965](http://jira/browse/TEOFF-4965): Provide button to dismiss keyboard  
[TEOFF-4972](http://jira/browse/TEOFF-4972): Improve stability when navigating back in assistant finish command

## 10.8.1290.0

#### Shared

[TEOFF-4919](http://jira/browse/TEOFF-4919): No warning of unsaved changes when Next from New Picture / New Document

#### iOS

[TEOFF-4958](http://jira/browse/TEOFF-4958): Fix for crash when tapping complete or incomplete button on WO

## 10.8.1289.0

#### Shared

[TEOFF-4812](http://jira/browse/TEOFF-4812): Input Mask, scanner issues(android and ios)  
[TEOFF-4826](http://jira/browse/TEOFF-4826): Initial focus on fields in AssistantPage and ElementPage  
[TEOFF-4859](http://jira/browse/TEOFF-4859): Added url for privacy policy  
[TEOFF-4951](http://jira/browse/TEOFF-4951): Improve camera detection

#### Android

[TEOFF-4945](http://jira/browse/TEOFF-4945): Android > Text Fields > Emoji can be added to fields

#### iOS

[TEOFF-4641](http://jira/browse/TEOFF-4641): Survey answer options displayed on device cut words in half  
[TEOFF-4900](http://jira/browse/TEOFF-4900): Fixed iOS crash when creating New Work from Task

## 10.8.1288.0

#### Shared

[TEOFF-4825](http://jira/browse/TEOFF-4825): Fixed next command validation for action flow configurations  
[TEOFF-4826](http://jira/browse/TEOFF-4826): Initial focus on fields in AssistantPage and ElementPage  
[TEOFF-4920](http://jira/browse/TEOFF-4920): Fix menu item order in multi projection apps

## 10.8.1287.0

#### Shared

[TEOFF-4806](http://jira/browse/TEOFF-4806): Parameterize proceeding to next field or executing command on scanning with CR

#### Android

[TEOFF-4870](http://jira/browse/TEOFF-4870): Fixed list in Android to show all list items

#### iOS

[TEOFF-4917](http://jira/browse/TEOFF-4917): Fixed iOS app crashing when entering Main Menu in wadaco

#### Windows

[TEOFF-4831](http://jira/browse/TEOFF-4831): Fix field type being shown as label when label is set to blank

## 10.8.1286.0

#### Shared

[TEOFF-4796](http://jira/browse/TEOFF-4796): eForms: The mask for Confirm Answer is missing  
[TEOFF-4865](http://jira/browse/TEOFF-4865): Markdown support for comma in table data  
[TEOFF-4849](http://jira/browse/TEOFF-4849): Fixed crash when dynamic menu items method returns null

## 10.8.1285.0

#### Shared

[TEOFF-4795](http://jira/browse/TEOFF-4795): Input mask in eForms: When defining a mask for Letters it doesn't work for non ASCII letters

#### iOS

[TEOFF-4856](http://jira/browse/TEOFF-4856): Fix push notification handle for iOS 13

## 10.8.1284.0

#### Android

[TEOFF-4527](http://jira/browse/TEOFF-4527): Android integration with third party app  
[TEOFF-4638](http://jira/browse/TEOFF-4638): Fixed Android client crash when logging in

#### iOS

[TEOFF-4856](http://jira/browse/TEOFF-4856): Fix push notification handle for iOS 13

## 10.8.1283.0

#### Shared

[TEOFF-4746](http://jira/browse/TEOFF-4746): Editable Lists > Check buttons > App crashes when selecting options  
[TEOFF-4806](http://jira/browse/TEOFF-4806): Parameterize proceeding to next field or executing command on scanning with CR  
[TEOFF-4841](http://jira/browse/TEOFF-4841): Unable to save a picture in Media Attachment in Aurena Native  
[TEOFF-4850](http://jira/browse/TEOFF-4850): Enabling/Disabling of fileselector works opposite way.

## 10.8.1281.0

#### Shared

[TEOFF-4790](http://jira/browse/TEOFF-4790): Add command for map position in media details  
[TEOFF-4799](http://jira/browse/TEOFF-4799): Add copyCustomFields procedure to support copying custom fields in virtuals  
[TEOFF-4863](http://jira/browse/TEOFF-4863): Fix for page not navigating back after adding media/documents

#### iOS

[TEOFF-4861](http://jira/browse/TEOFF-4861): Fix for add button not showing on attachment list on iOS

## 10.8.1275.0

#### Shared

[TEOFF-4723](http://jira/browse/TEOFF-4723): Alert sounds in Aurena Native client  
[TEOFF-4762](http://jira/browse/TEOFF-4762): Editing a field using a smiley stops the sync  
[TEOFF-4798](http://jira/browse/TEOFF-4798): Close dialog assistants when custom commands return a value

## 10.8.1273.0

#### Shared

[TEOFF-4298](http://jira/browse/TEOFF-4298): View and add documents and media from workflow

## 10.8.1265.0

#### Android

[TEOFF-4800](http://jira/browse/TEOFF-4800): Removed Unused header layout file

## 10.8.1262.0

#### Shared

[TEOFF-4750](http://jira/browse/TEOFF-4750): Improve error logging in client framework

## 10.8.1261.0

#### iOS

[TEOFF-4633](http://jira/browse/TEOFF-4633): Double up the signature dialog size for tablet screens

#### Android

[TEOFF-4780](http://jira/browse/TEOFF-4780): Aurena native android client does not connect to back end when build with VS 2019.

## 10.8.1260.0

#### Shared

[TEOFF-4398](http://jira/browse/TEOFF-4398): Add Location to media items added with camera

## 10.8.1254.0

#### Shared

[TEOFF-4435](http://jira/browse/TEOFF-4435): Input mask support for text fields

#### iOS

[TEOFF-4624](http://jira/browse/TEOFF-4624): G2096752 - 100: mWO client not allowing Step Remark to be easily updated

## 10.8.1252.0

#### Shared

[TEOFF-4364](http://jira/browse/TEOFF-4364): Fetch select attributes from workflow setup method  
[TEOFF-4565](http://jira/browse/TEOFF-4565): Fix for loading initial values to element selector  
[TEOFF-4713](http://jira/browse/TEOFF-4713): App crashes when logging in to NotifyMe on Android and iOS  
[TEOFF-4719](http://jira/browse/TEOFF-4719): Save assistant step when field editing is done  
[TEOFF-4728](http://jira/browse/TEOFF-4728): Always navigate to workflow using the primary key of the record

#### Android

[TEOFF-4689](http://jira/browse/TEOFF-4689): Fix for Fields hidden by custom commands bar

#### iOS

[TEOFF-4688](http://jira/browse/TEOFF-4688): Fix for focus lost on field when rotating

## 10.8.1236.0

#### Shared

[TEOFF-4721](http://jira/browse/TEOFF-4721): Reload record in workflow when data change is detected

## 10.8.1235.0

#### Shared

[TEOFF-4642](http://jira/browse/TEOFF-4642): Navigation not enabled after closing Custom Commands Assistant  
[TEOFF-4712](http://jira/browse/TEOFF-4712): Custom commands disappear at loading

## 10.8.1234.0

#### Shared

[TEOFF-4090](http://jira/browse/TEOFF-4090): Wadaco: List support in Single Step Assistant  
[TEOFF-4710](http://jira/browse/TEOFF-4710): Show Previous/Next text when workflow step label is empty

## 10.8.1231.0

#### Shared

[TEOFF-4694](http://jira/browse/TEOFF-4694): Fix broken unbound actions/functions in multi projection meta

## 10.8.1227.0

#### Shared

[TEOFF-4116](http://jira/browse/TEOFF-4116): Fix for label not displaying interpolated strings on iOS and Android

## 10.8.1226.0

#### Shared

[TEOFF-4589](http://jira/browse/TEOFF-4589): Extract select attributes from workflow commands  
[TEOFF-4681](http://jira/browse/TEOFF-4681): Improve navigating to workflow by sequence number

#### iOS

[TEOFF-4674](http://jira/browse/TEOFF-4674): Customer Data - Link to make a phone call fails in iOS

#### Windows

[TEOFF-4643](http://jira/browse/TEOFF-4643): Move the workflow header below the page header

## 10.8.1223.0

#### Shared

[TEOFF-4136](http://jira/browse/TEOFF-4136): Allow navigating to a workflow step by sequence number  
[TEOFF-4646](http://jira/browse/TEOFF-4646): Refresh pages in workflow when navigating back

## 10.8.1217.0

#### iOS

[TEOFF-4623](http://jira/browse/TEOFF-4623): Fix command page not loading inside workflow

## 10.8.1216.0

#### Shared

[TEOFF-4474](http://jira/browse/TEOFF-4474): Change focus to next field when using scanner

## 10.8.1212.0

#### Shared

[TEOFF-4595](http://jira/browse/TEOFF-4595): Reload elements when navigated back to a page

## 10.8.1201.0

#### iOS

[TEOFF-4611](http://jira/browse/TEOFF-4611): Fixed app crash when workflow finish command icon is not defined

## 10.8.1195.0

#### Shared

[TEOFF-4089](http://jira/browse/TEOFF-4089): Wadaco custom commands in single step assistant  
[TEOFF-4115](http://jira/browse/TEOFF-4115): Fix for NOT operator failing

## 10.8.1187.0

#### iOS

[TEOFF-4368](http://jira/browse/TEOFF-4368): Hide placeholder images on cards when image data is not available

## 10.8.1186.0

#### Shared

[TEOFF-4116](http://jira/browse/TEOFF-4116): Fix for String interpolation not working in field labels contained in groups

## 10.8.1183.0

#### iOS

[TEOFF-4579](http://jira/browse/TEOFF-4579): Fix current date not being set on pickers introduced by TEOFF-4214

## 10.8.1178.0

#### Shared

[TEOFF-4153](http://jira/browse/TEOFF-4153): Remove the CfEnum prefix from custom enum values

## 10.8.1176.0

#### iOS

[TEOFF-3976](http://jira/browse/TEOFF-3976): Clear app badge without clearing notifications  
[TEOFF-4384](http://jira/browse/TEOFF-4384): Split Screen UI layout alignment issues

## 10.8.1175.0

#### iOS

[TEOFF-4270](http://jira/browse/TEOFF-4270): Fix for CommandPageView loading twice on back navigation

## 10.8.1174.0

#### Shared

[TEOFF-4494](http://jira/browse/TEOFF-4494): Don't show previews for incompatible media types (Video/Text/Audio)

## 10.8.1170.0

#### Shared

[TEOFF-4563](http://jira/browse/TEOFF-4563): Fix null pointer exception when executing validate command in cards

#### iOS

[TEOFF-4122](http://jira/browse/TEOFF-4122): unchecked radio buttons shows larger circle in the mobile

## 10.8.1161.0

#### Shared

[TEOFF-3597](http://jira/browse/TEOFF-3597): Make the card in embedded maps scrollable  
[TEOFF-4509](http://jira/browse/TEOFF-4509): Fix the warnings about F1KeyRef when initializing

#### Android

[TEOFF-4284](http://jira/browse/TEOFF-4284): Monospace fonts used in login screen on Android

#### iOS

[TEOFF-4214](http://jira/browse/TEOFF-4214): Set value in date time picker dialog after opening

## 10.8.1160.0

#### Shared

[TEOFF-4450](http://jira/browse/TEOFF-4450): Clicking on an Address Field type in a card does not allow navigation

## 10.8.1159.0

#### Windows

[TEOFF-4367](http://jira/browse/TEOFF-4367): Show correct filename when saving bug report in Windows  
[TEOFF-4397](http://jira/browse/TEOFF-4397): Fix For signatures not showing on completed task

## 10.8.1156.0

#### Shared

[TEOFF-3936](http://jira/browse/TEOFF-3936): Make collapsible group headers look consistent across platforms

## 10.8.1155.0

#### Shared

[TEOFF-4319](http://jira/browse/TEOFF-4319): Refresh lists after performing the detail command  
[TEOFF-4475](http://jira/browse/TEOFF-4475): Editable fields in cards are saved when having a validate command

#### Windows

[TEOFF-4423](http://jira/browse/TEOFF-4423): Improve weak event handling in ListDataControlBase

## 10.8.1153.0

#### Shared

[TEOFF-4126](http://jira/browse/TEOFF-4126): Correct attribute names are fetched when loading workflows

#### Android

[TEOFF-4517](http://jira/browse/TEOFF-4517): Disable Refresh Cache option when session has expired

#### iOS

[TEOFF-4516](http://jira/browse/TEOFF-4516): Disable the initialize option when the cache is refreshing

## 10.8.1152.0

#### Shared

[TEOFF-3517](http://jira/browse/TEOFF-3517): Added Refresh Cache Data to Sync Monitor

## 10.7.1148.0

#### Shared

[TEOFF-3521](http://jira/browse/TEOFF-3521): Mobile integration support  
[TEOFF-4402](http://jira/browse/TEOFF-4402): Added missing translations

## 10.7.1147.0

#### Shared

[TEOFF-4062](http://jira/browse/TEOFF-4062): Replace media item attachments

## 10.7.1146.0

#### Shared

[TEOFF-4472](http://jira/browse/TEOFF-4472): Support generic JSONLogic expressions

## 10.7.1143.0

#### Shared

[TEOFF-4470](http://jira/browse/TEOFF-4470): Fixed parameter validation method to accept null values

## 10.7.1142.0

#### Shared

[TEOFF-3602](http://jira/browse/TEOFF-3602): Connect media and documents

## 10.7.1138.0

#### Shared

[TEOFF-4461](http://jira/browse/TEOFF-4461): Log errors when function parameters mismatch

## 10.7.1137.0

#### Shared

[TEOFF-4447](http://jira/browse/TEOFF-4447): Improved support for multiple maps on the same page

## 10.7.1133.0

#### Shared

[TEOFF-4420](http://jira/browse/TEOFF-4420): Fix tree pages not loading

## 10.7.1131.0

#### Android

[TEOFF-4399](http://jira/browse/TEOFF-4399): Fix Android app crashes due to disposed card list items

## 10.7.1130.0

#### Shared

[TEOFF-4406](http://jira/browse/TEOFF-4406): Highlight editable field on a card after saving changes

## 10.7.1128.0

#### Shared

[TEOFF-4404](http://jira/browse/TEOFF-4404): Reload card panel items after record reloaded  
[TEOFF-4407](http://jira/browse/TEOFF-4407): Procedure errors are shown for editable lists

#### iOS

[TEOFF-4360](http://jira/browse/TEOFF-4360): Fixed crash when saving image to Photos  
[TEOFF-4425](http://jira/browse/TEOFF-4425): Show the border only around the editable field and not the label

## 10.7.1124.0

#### Shared

[TEOFF-3974](http://jira/browse/TEOFF-3974): Made selector on online entity openable if query returns more than one record  
[TEOFF-4326](http://jira/browse/TEOFF-4326): Fixed multiple sync monitor pages created in Windows

## 10.7.1121.0

#### Shared

[TEOFF-4387](http://jira/browse/TEOFF-4387): Fixed blank page being shown when a workflow is opened

## 10.7.1118.0

#### Shared

[TEOFF-4316](http://jira/browse/TEOFF-4316): TEOFF-4316: Fix for assistants finish button been able to be clicked multiple times

## 10.7.1117.0

#### Shared

[TEOFF-4155](http://jira/browse/TEOFF-4155): Provide a finish command for workflow  
[TEOFF-4164](http://jira/browse/TEOFF-4164): Provide support for workflow previous command  
[TEOFF-4297](http://jira/browse/TEOFF-4297): Support selector and singleton within the commands page

## 10.7.1115.0

#### Windows

[TEOFF-4052](http://jira/browse/TEOFF-4052): Updated LocationButtonStyle for map icon button

## 10.7.1114.0

#### Shared

[TEOFF-4150](http://jira/browse/TEOFF-4150): Workflow steps are not added when dynamic function is not given or returns nothing

## 10.7.1088.0

#### iOS

[TEOFF-4315](http://jira/browse/TEOFF-4315): Show map pin when opening coordinates

## 10.7.1084.0

#### Android

[TEOFF-4137](http://jira/browse/TEOFF-4137): Added layout margin at top of recycler listview in lists

## 10.7.1082.0

#### iOS

[TEOFF-4234](http://jira/browse/TEOFF-4234): Fix crashes on iOS 13

## 10.7.1080.0

#### Windows

[TEOFF-4147](http://jira/browse/TEOFF-4147): Fix breadcrumb issue on windows when viewing WO

## 10.7.1076.0

#### iOS

[TEOFF-4095](http://jira/browse/TEOFF-4095): Fix some fields in map cards not being shown

## 10.7.1070.0

#### Shared

[TEOFF-4291](http://jira/browse/TEOFF-4291): Ignore custom fields when validating offlinequery and query

## 10.7.1068.0

#### Shared

[TEOFF-4151](http://jira/browse/TEOFF-4151): Zoom embedded map to all pins shown  
[TEOFF-4283](http://jira/browse/TEOFF-4283): Fix grey map on startup and after screen rotation (on Android)

#### Android

[TEOFF-3964](http://jira/browse/TEOFF-3964): Fix crashing in Work Order Maps page

## 10.7.1066.0

#### iOS

[TEOFF-4207](http://jira/browse/TEOFF-4207): Add OnAttachEvents to SelectionFieldHandler to avoid OnClick failure

## 10.7.1064.0

#### Shared

[TEOFF-4280](http://jira/browse/TEOFF-4280): Fix attachment downloads for online-only entities

#### Windows

[TEOFF-4020](http://jira/browse/TEOFF-4020): Give Space to clear button on dynamic assistants

## 10.7.1062.0

#### Shared

[TEOFF-4248](http://jira/browse/TEOFF-4248): HTML encode text on map pins

## 10.6.1040.0

#### Shared

[TEOFF-4188](http://jira/browse/TEOFF-4188): Add support for custom icons, label and messages on finish button in assistants

## 10.6.1038.0

#### Android

[TEOFF-4048](http://jira/browse/TEOFF-4048): Fix breadcrumb crash on workflows

## 10.6.1036.0

#### Shared

[TEOFF-3000](http://jira/browse/TEOFF-3000): Ensure card panel items remain in the same location regardless of their visibility  
[TEOFF-4198](http://jira/browse/TEOFF-4198): Save the current dynamic assistant step upon finish command  
[TEOFF-4209](http://jira/browse/TEOFF-4209): Support new input/output format for modal assistants

#### Android

[TEOFF-4235](http://jira/browse/TEOFF-4235): Fixed URLs not opening on Android 9 due to activity context not being passed in

#### iOS

[TEOFF-4161](http://jira/browse/TEOFF-4161): Background app refresh for iOS

## 10.6.1035.0

#### Shared

[TEOFF-4138](http://jira/browse/TEOFF-4138): Add video type to enumeration  
[TEOFF-4195](http://jira/browse/TEOFF-4195): Ignore locale number formatting when setting lat/long values for map pins  
[TEOFF-4206](http://jira/browse/TEOFF-4206): Consider beyond visible bounds when determining nearby markers on the map

## 10.6.1032.0

#### Shared

[TEOFF-3807](http://jira/browse/TEOFF-3807): Fixed documents/media connections to overriden entities

## 10.6.1029.0

#### Shared

[TEOFF-4042](http://jira/browse/TEOFF-4042): Remove developer-specific values in code for Release builds

## 10.6.1028.0

#### Shared

[TEOFF-4041](http://jira/browse/TEOFF-4041): HTML encode the text fields rendered in the report  
[TEOFF-4200](http://jira/browse/TEOFF-4200): Fix app crash introduced by the correction for TEOFF-4126

## 10.6.1026.0

#### Shared

[TEOFF-4126](http://jira/browse/TEOFF-4126): Force-load references in the workflow to get the values when navigating between steps

## 10.6.1025.0

#### Shared

[TEOFF-4166](http://jira/browse/TEOFF-4166): Fixed lists not loading inside the workflow because of column values not being passed in

## 10.6.1023.0

#### Shared

[TEOFF-4121](http://jira/browse/TEOFF-4121): Set the bind attribute name for dynamic step field when the name is not available  
[TEOFF-4146](http://jira/browse/TEOFF-4146): Fix workflow navigation bug

## 10.6.1022.0

#### Shared

[TEOFF-4084](http://jira/browse/TEOFF-4084): Fix for require steps showing error dialog on auto complete dynamic assistants

## 10.6.1021.0

#### Shared

[TEOFF-4035](http://jira/browse/TEOFF-4035): Fix workflow navigation error

#### Android

[TEOFF-4125](http://jira/browse/TEOFF-4125): Fixed app crash when dismissing keyboard on Android 9

## 10.6.1015.0

#### Shared

[TEOFF-4100](http://jira/browse/TEOFF-4100): Added exception when map has failed to load query

## 10.6.1012.0

#### Shared

[TEOFF-4130](http://jira/browse/TEOFF-4130): Removed unneeded hiding of attachment button

#### iOS

[TEOFF-4026](http://jira/browse/TEOFF-4026): Fixed reports showing in iOS assistants properly

## 10.6.1009.0

#### Shared

[TEOFF-3541](http://jira/browse/TEOFF-3541): Commands now localize when setting variable  
[TO-202](http://jira/browse/TO-202): Fix wrong encoding in cloud error messages

## 10.6.1006.0

#### Shared

[TEOFF-4125](http://jira/browse/TEOFF-4125): Allow the Finish button to be enabled/disabled in dynamic assistants  
[TO-237](http://jira/browse/TO-237): Disable option to revise documents when they are read only

#### iOS

[TO-230](http://jira/browse/TO-230): Badgeview supports multiple lines of text with wrapping

## 10.6.1003.0

#### Shared

[TEOFF-3747](http://jira/browse/TEOFF-3747): Added checks and error logging when loading data to a list from a function that requires parameters  
[TEOFF-4082](http://jira/browse/TEOFF-4082): Group visibility set inside element is now considered

## 10.6.1000.0

#### Shared

[TEOFF-4096](http://jira/browse/TEOFF-4096): Add visible property to workflows

## 10.6.994.0

#### Shared

[TEOFF-3560](http://jira/browse/TEOFF-3560): Add support to be able to hide workflow commands from navigation menu  
[TEOFF-3847](http://jira/browse/TEOFF-3847): Null contract widget elements are now hidden  
[TEOFF-4086](http://jira/browse/TEOFF-4086): Fixed "no such table" on logging in after an app update

## 10.6.988.0

#### Shared

[TEOFF-3975](http://jira/browse/TEOFF-3975): Handle timeout cloud exception in client

## 10.6.985.0

#### Shared

[TEOFF-3558](http://jira/browse/TEOFF-3558): Add progress bar for workflows  
[TEOFF-3819](http://jira/browse/TEOFF-3819): Improve multi select dialog answers on Android and iOS

## 10.6.982.0

#### Android

[TEOFF-3868](http://jira/browse/TEOFF-3868): Update Android client target version to API 28 (Pie)

#### Windows

[TEOFF-3933](http://jira/browse/TEOFF-3933): Removed colon from field labels

## 10.6.979.0

#### Shared

[TEOFF-3934](http://jira/browse/TEOFF-3934): Changed carets on element headers to be consistent with Aurena online  
[TO-199](http://jira/browse/TO-199): Support emphasis and make fonts consistent in markdown webviews

#### iOS

[TEOFF-3935](http://jira/browse/TEOFF-3935): Increased related pages item height from 44 -> 50

## 10.6.972.0

#### Windows

[TO-188](http://jira/browse/TO-188): Horizontal Scroll for Table View

## 10.6.966.0

#### Shared

[TO-186](http://jira/browse/TO-186): Improved behaviour on markdown tables in iOS and Android

## 10.6.954.0

#### Windows

[TEOFF-3819](http://jira/browse/TEOFF-3819): Expand select buttons and make them scrollable

## 10.6.951.0

#### iOS

[TO-187](http://jira/browse/TO-187): Fix for MarkdownView cropped when device is rotated on iOS.

## 10.6.948.0

#### iOS

[TO-122](http://jira/browse/TO-122): Push notification command support for iOS

#### Windows

[TEOFF-3969](http://jira/browse/TEOFF-3969): Ensure calendars open on selected dates

## 10.6.945.0

#### Shared

[TO-192](http://jira/browse/TO-192): Support LU name in queries for document/media connections

#### Android

[TO-184](http://jira/browse/TO-184): Stopped contact widget from opening twise

## 10.6.939.0

#### Windows

[TO-123](http://jira/browse/TO-123): Push notification on click support for Windows

## 10.6.933.0

#### Shared

[TEOFF-3561](http://jira/browse/TEOFF-3561): Allow navigation to a workflow step from a command

## 10.6.930.0

#### Shared

[TEOFF-3290](http://jira/browse/TEOFF-3290): Text split on About screens

#### Android

[TO-121](http://jira/browse/TO-121): Implementation for commands in push notification

## 10.6.921.0

#### Shared

[TEOFF-3240](http://jira/browse/TEOFF-3240): Added support for conditions in FreeInput

## 10.6.918.0

#### iOS

[TEOFF-4006](http://jira/browse/TEOFF-4006): Add support for alternate language calendars

## 10.6.913.0

#### Android

[TEOFF-4005](http://jira/browse/TEOFF-4005): Fix media attachment error

## 10.6.909.0

#### Shared

[TEOFF-3985](http://jira/browse/TEOFF-3985): Fix assistants/trees not being navigated to when toplevel entries are not used

#### iOS

[TEOFF-3986](http://jira/browse/TEOFF-3986): Fix list click being disabled even when there's a detail command

## 10.6.903.0

#### Shared

[TEOFF-3711](http://jira/browse/TEOFF-3711): Map pins now get the card data in the query

#### Android

[TEOFF-3724](http://jira/browse/TEOFF-3724): Fixed crash in contact control

## 10.6.900.0

#### Android

[TEOFF-3702](http://jira/browse/TEOFF-3702): Elipsize menu options if the text is too long

## 10.6.897.0

#### Shared

[TEOFF-3130](http://jira/browse/TEOFF-3130): Disabled list item click effect when click is not allowed

#### Android

[TO-117](http://jira/browse/TO-117): Fix app crash when screen rotated with contact widget open, Fix image scaling in contact widget

## 10.6.891.0

#### Android

[TEOFF-3724](http://jira/browse/TEOFF-3724): Fixed android not loading cards correctly in map

#### Windows

[TEOFF-3695](http://jira/browse/TEOFF-3695): Fixed incorrect date showing when reopening a previous steps date or datetime field

## 10.6.888.0

#### Shared

[TEOFF-3850](http://jira/browse/TEOFF-3850): Fix for being unable to navigate back when an error is shown in assistants

#### Android

[TEOFF-3698](http://jira/browse/TEOFF-3698): Degraded zxing library on Android to fix issue with camera rotation

## 10.6.885.0

#### Shared

[TEOFF-3878](http://jira/browse/TEOFF-3878): Fixed command errors not showing custom label correctly

#### Windows

[TEOFF-3656](http://jira/browse/TEOFF-3656): Fix for combo boxes not showing a required placeholder text in Windows

## 10.5.882.0

#### Shared

[TEOFF-3751](http://jira/browse/TEOFF-3751): Documents and Media are now made read-only when downloading and uploading  
[TEOFF-3881](http://jira/browse/TEOFF-3881): Fixed issues with field layouts

#### Android

[TEOFF-3684](http://jira/browse/TEOFF-3684): Accessibility IDs are on fields rather than containers

#### iOS

[TEOFF-3288](http://jira/browse/TEOFF-3288): Set editing to false on rotation to make swipeable item reset

## 10.5.880.0

#### Shared

[TEOFF-3885](http://jira/browse/TEOFF-3885): Improve error messages when invalid metadata is found  
[TEOFF-3914](http://jira/browse/TEOFF-3914): Added check on comms broken reason to fix exception

#### Android

[TEOFF-3895](http://jira/browse/TEOFF-3895): Fixed actions not using all the available space

## 10.5.878.0

#### Shared

[TEOFF-3171](http://jira/browse/TEOFF-3171): Wrap text in readonly fields that are not URLs, phone numbers or emails

#### Android

[TEOFF-3683](http://jira/browse/TEOFF-3683): Make Automation identifiers language independent  
[TO-168](http://jira/browse/TO-168): Fix app crash when Settings page is opened from a metadata root page

## 10.5.876.0

#### iOS

[TEOFF-3880](http://jira/browse/TEOFF-3880): Fixed badge not truncating when it does not have a label  
[TEOFF-3905](http://jira/browse/TEOFF-3905): Fixed issue with related pages crashing when reloading data

## 10.5.874.0

#### Shared

[TEOFF-3794](http://jira/browse/TEOFF-3794): Fixed navigating to a screen with the same name

#### iOS

[TEOFF-3879](http://jira/browse/TEOFF-3879): Changed tree structure item selection to be consistent with Android

## 10.5.866.0

#### Shared

[TEOFF-3882](http://jira/browse/TEOFF-3882): Fixed lookups crashing the app rather than showing a message when there is a problem with the metadata

## 10.5.864.0

#### Shared

[TEOFF-3891](http://jira/browse/TEOFF-3891): Fixed incorrect ludependencies table checked on data changes

## 10.5.863.0

#### Shared

[TEOFF-3886](http://jira/browse/TEOFF-3886): Fixed a crash when using a command that removes an item from a list

## 10.5.861.0

#### Windows

[TEOFF-3759](http://jira/browse/TEOFF-3759): Fixed a crash when resizing the window after logging out

## 10.5.857.0

#### Windows

[TEOFF-3808](http://jira/browse/TEOFF-3808): Time picker popups will no longer be opened too large

## 10.5.856.0

#### Shared

[TEOFF-3869](http://jira/browse/TEOFF-3869): Fixed a crash when loading metadata which has an entity inside a structure

## 10.5.854.0

#### iOS

[TEOFF-3697](http://jira/browse/TEOFF-3697): If dynamic assistant step is cleared, don't move on to the next step automatically

## 10.5.852.0

#### Windows

[TEOFF-3799](http://jira/browse/TEOFF-3799): Prevent dialogs from trying to open in dialogs that are currently closing

## 10.5.851.0

#### Shared

[TEOFF-3753](http://jira/browse/TEOFF-3753): Support searching on date time fields

#### Android

[TEOFF-3820](http://jira/browse/TEOFF-3820): Add Scrollbars to card list views

## 10.5.849.0

#### iOS

[TO-105](http://jira/browse/TO-105): Table support for markdowntext implemented in iOS

## 10.5.847.0

#### Windows

[TEOFF-3848](http://jira/browse/TEOFF-3848): Add Tooltip to app bar buttons and stop words going over two lines

## 10.5.845.0

#### Windows

[TEOFF-3495](http://jira/browse/TEOFF-3495): Change System ID not recognised to translatable string  
[TEOFF-3720](http://jira/browse/TEOFF-3720): Fixed text fields sometimes not responding

## 10.5.843.0

#### Shared

[TEOFF-3308](http://jira/browse/TEOFF-3308): Add logs around re-entering login details  
[TEOFF-3840](http://jira/browse/TEOFF-3840): Stop showing page create command as an action

#### Android

[TEOFF-3485](http://jira/browse/TEOFF-3485): Remove auto backup

#### iOS

[TEOFF-3782](http://jira/browse/TEOFF-3782): Fixed an issue where editable list fields did not update

## 10.5.841.0

#### Shared

[TEOFF-3717](http://jira/browse/TEOFF-3717): Fixed bug with map reloading after updating the state of a pin

#### Windows

[TEOFF-3632](http://jira/browse/TEOFF-3632): Vertical alignment of address fields added

## 10.5.839.0

#### Shared

[TEOFF-3706](http://jira/browse/TEOFF-3706): Fixed iOS map overlapping the zoom buttons at max zoom

#### iOS

[TEOFF-3312](http://jira/browse/TEOFF-3312): Support right to left languages

## 10.5.837.0

#### Shared

[TEOFF-3793](http://jira/browse/TEOFF-3793): Fix for needing to double click when expanding sections

#### Android

[TEOFF-3707](http://jira/browse/TEOFF-3707): Fixed android not selecting default option which caused multiple options to be selected

#### Windows

[TEOFF-3814](http://jira/browse/TEOFF-3814): Fixed details and actions tabs sometimes not responding

## 10.5.835.0

#### Shared

[TEOFF-3782](http://jira/browse/TEOFF-3782): Fixed editable field not updating when the field type changes

#### Windows

[TEOFF-3727](http://jira/browse/TEOFF-3727): Fixed an error when searching in an LOV

## 10.5.834.0

#### Shared

[TEOFF-2945](http://jira/browse/TEOFF-2945): Support before and after crudaction commands

#### iOS

[TEOFF-3714](http://jira/browse/TEOFF-3714): Fixed crash in iOS when updating data in card arranger view

## 10.5.832.0

#### Windows

[TO-118](http://jira/browse/TO-118): Fix for SyncMonitorPage not navigating to the TransactionsPivotPage

## 10.5.830.0

#### Shared

[TEOFF-2892](http://jira/browse/TEOFF-2892): Added custom cancellation message to assistants  
[TEOFF-3574](http://jira/browse/TEOFF-3574): Added functionality to autocomplete dynamic assistant fields

#### A i W

[TEOFF-3201](http://jira/browse/TEOFF-3201): Use a white background for the signature control

#### Android

[TEOFF-3792](http://jira/browse/TEOFF-3792): Fixed bug where uncollapsing a section on misc page would crash

## 10.5.828.0

#### Shared

[TEOFF-3253](http://jira/browse/TEOFF-3253): Added support for saving state for collapsed sections  
[TEOFF-3538](http://jira/browse/TEOFF-3538): Changed version number to 10.5

#### iOS

[TEOFF-3540](http://jira/browse/TEOFF-3540): Change Application Id to use a Guid rather than IdenfitierForVendor

## 10.5.826.0

#### Shared

[TEOFF-2944](http://jira/browse/TEOFF-2944): Added the ability to get the users location from a command  
[TEOFF-3534](http://jira/browse/TEOFF-3534): Updated workflow to accept all forms of navigation to pages and assistants  
[TEOFF-3600](http://jira/browse/TEOFF-3600): Added the ability to show an attachments button on list items  
[TO-119](http://jira/browse/TO-119): Change failed transaction dialog cancel button to Dismiss

#### iOS

[TEOFF-3741](http://jira/browse/TEOFF-3741): Degraded auth library from version 0-92-0 to 0-7-0 as there was an issue with Microsoft authentication

## 10.5.821.0

#### Shared

[TO-91](http://jira/browse/TO-91): Shared Code implementation of TransactionFailed event

## 10.5.819.0

#### Shared

[TEOFF-2956](http://jira/browse/TEOFF-2956): Support a single editable field in cards

## 10.5.817.0

#### Android

[TEOFF-3708](http://jira/browse/TEOFF-3708): Fixed command overflow not showing correctly

## 10.5.815.0

#### Shared

[TEOFF-3550](http://jira/browse/TEOFF-3550): Add support for custom create commands

## 10.5.807.0

#### Android

[TEOFF-3710](http://jira/browse/TEOFF-3710): Fixed a crash when moving away from a screen with a file picker

## 10.5.805.0

#### Shared

[TEOFF-3562](http://jira/browse/TEOFF-3562): Added string tokenize method to split a string with a delimiter into a list  
[TEOFF-3671](http://jira/browse/TEOFF-3671): Fixed map not showing when there are no valid entities  
[TO-73](http://jira/browse/TO-73): Support Readonly flag for DocMan and Media Library

## 10.5.803.0

#### Windows

[TEOFF-3701](http://jira/browse/TEOFF-3701): Fix for crash when returning from actions list to a card list

## 10.5.799.0

#### Windows

[TEOFF-3696](http://jira/browse/TEOFF-3696): Cleaned up LOV look on Windows

## 10.5.797.0

#### Shared

[TEOFF-3627](http://jira/browse/TEOFF-3627): Fix for validation message not being shown to user

## 10.5.791.0

#### Android

[TO-104](http://jira/browse/TO-104): Tables defined in markdown text is now populated as tables in the android client.

## 10.5.788.0

#### Shared

[TEOFF-3592](http://jira/browse/TEOFF-3592): Improved the performance when opening a tree page  
[TEOFF-3637](http://jira/browse/TEOFF-3637): Fixed a crash when an app has no menu items

#### AI

[TEOFF-3472](http://jira/browse/TEOFF-3472): Make Dynamic LOVs Required property work and show placeholder text for Required

#### iOS

[TEOFF-3281](http://jira/browse/TEOFF-3281): Changed tree view items to be larger

#### Windows

[TEOFF-3632](http://jira/browse/TEOFF-3632): Address field font size corrected

## 10.5.784.0

#### Shared

[TEOFF-3085](http://jira/browse/TEOFF-3085): Added support for navigating back to a specific page  
[TEOFF-3596](http://jira/browse/TEOFF-3596): Download files using a temporary file so they cannot be opened when half downloaded

#### Android

[TEOFF-3506](http://jira/browse/TEOFF-3506): Fixed files being selected that were the incorrect file type

#### iOS

[TEOFF-3484](http://jira/browse/TEOFF-3484): fix address presentation not getting picked up in detail screen  
[TEOFF-3623](http://jira/browse/TEOFF-3623): Fixed a crash when clicking the download attachment button

## 10.5.782.0

#### Shared

[TEOFF-3512](http://jira/browse/TEOFF-3512): Fixed LOV sometimes not checking items and added check to windows  
[TEOFF-3520](http://jira/browse/TEOFF-3520): Make badges appear as badges in reports  
[TEOFF-3527](http://jira/browse/TEOFF-3527): Fixed database export and import not working

#### Android

[TEOFF-3539](http://jira/browse/TEOFF-3539): Fixed text fields sometimes entering multiple characters  
[TEOFF-3569](http://jira/browse/TEOFF-3569): Fixed a crash when opening the app after Android closed the app on the sync monitor

## 10.5.780.0

#### Shared

[TEOFF-3519](http://jira/browse/TEOFF-3519): All columns available in generated reports  
[TEOFF-3588](http://jira/browse/TEOFF-3588): Use string interpolation for urls

## 10.5.776.0

#### All

[TO-18](http://jira/browse/TO-18): Support for Contact Widget

## 10.5.768.0

#### Shared

[TEOFF-2942](http://jira/browse/TEOFF-2942): Embedded Map Element

## 10.5.756.0

#### Shared

[TO-42](http://jira/browse/TO-42): Show dialog automatically if triggered failed transaction is the only one in the list

## 10.5.747.0

#### Shared

[TEOFF-2956](http://jira/browse/TEOFF-2956): Improve card layouts  
[TEOFF-3529](http://jira/browse/TEOFF-3529): Support the new app metadata format

## 10.5.744.0

#### All

[TO-26](http://jira/browse/TO-26): Navigate directly to a page if the navigator only contains one item

## 10.5.740.0

#### Shared

[TEOFF-2894](http://jira/browse/TEOFF-2894): Fixed a crash when dropping offline query views  
[TEOFF-3383](http://jira/browse/TEOFF-3383): Fixed incorrect lov items being checked in the lov dialog  
[TO-41](http://jira/browse/TO-41): Improved dialog on failed transaction  
[TO-44](http://jira/browse/TO-44): Initialize app when last failed transaction has been deleted

## 10.5.736.0

#### Android

[TEOFF-3336](http://jira/browse/TEOFF-3336): Fix field hints and default to Paragraph  
[TEOFF-3426](http://jira/browse/TEOFF-3426): Fixed documents picker showing the image picker button

#### iOS

[TEOFF-3497](http://jira/browse/TEOFF-3497): Remove unneeded location usage description

## 10.5.734.0

#### iOS

[TEOFF-3418](http://jira/browse/TEOFF-3418): Fixed responsiveness of LOVs with a large number of items

## 10.5.732.0

#### Shared

[TEOFF-3313](http://jira/browse/TEOFF-3313): Fix LOV search not working on references

## 10.5.730.0

#### Shared

[TEOFF-3448](http://jira/browse/TEOFF-3448): Fixed a crash when adding a document if no default doc class is set  
[TEOFF-3464](http://jira/browse/TEOFF-3464): Support reading select attributes from fields  
[TEOFF-2246](http://jira/browse/TEOFF-2246): Implement address fields

#### iOS

[TEOFF-3051](http://jira/browse/TEOFF-3051): Fixed ios card label align to the top always when the content is too big

## 10.5.727.0

#### Shared

[TEOFF-3114](http://jira/browse/TEOFF-3114): Lists and counts are now updated when data in the client changes in the background

### Additional changes from 10.4

#### Android

[TEOFF-3402](http://jira/browse/TEOFF-3402): Fixed an issue where after selecting a document the entered document details are lost
[TEOFF-3422](http://jira/browse/TEOFF-3422): Fix for crash on Android list elements

#### iOS

[TEOFF-3419](http://jira/browse/TEOFF-3419): Fixed a crash when navigating between assistant pages
[TEOFF-3420](http://jira/browse/TEOFF-3420): Fixed a gray bar appearing at the bottom of list screens on tablets

## 10.5.699.0

#### Shared

[TEOFF-3170](http://jira/browse/TEOFF-3170): Fixed issues with generated Android icons and use of app icon in Android and iOS login page and menu

## 10.5.697.0

#### Android

[TEOFF-3042](http://jira/browse/TEOFF-3042): Add delete button to bottom of detail page

## 10.5.695.0

#### Android

[TEOFF-3367](http://jira/browse/TEOFF-3367): Fixed a crash caused by the user being able to navigate away while a command is running

## 10.5.693.0

#### Shared

[TEOFF-2781](http://jira/browse/TEOFF-2781): Send location tracking permission denied messages to server  
[TEOFF-3230](http://jira/browse/TEOFF-3230): Failed transactions are now retried when the user does a manual sync

#### Windows

[TEOFF-3272](http://jira/browse/TEOFF-3272): Add required to windows required fields

## 10.5.691.0

#### Shared

[TEOFF-3302](http://jira/browse/TEOFF-3302): Made the dynamic assistants less clicky

### Additional changes from 10.4

#### Shared

[TEOFF-3109](http://jira/browse/TEOFF-3109): Changed Android and iOS LOV's to be with constistent with Windows

#### Android

[TEOFF-3285](http://jira/browse/TEOFF-3285): Android now updates the node tree node state every time it is readded to a view  
[TEOFF-3291](http://jira/browse/TEOFF-3291): Fixed Android loading dialog getting stuck when closing open ID login screen
[TEOFF-3329](http://jira/browse/TEOFF-3329): Changed minimum supported version of Android to 6 (API 23)

#### Windows

[TEOFF-3233](http://jira/browse/TEOFF-3233): Move action buttons to left instead of right

#### iOS

[TEOFF-3268](http://jira/browse/TEOFF-3268): Ensure iOS related pages list re-enables after disabling due to command run

## 10.5.682.0

#### Android

[TEOFF-3307](http://jira/browse/TEOFF-3307): Add support for right to left languages

## 10.5.681.0

#### Android

[TEOFF-3309](http://jira/browse/TEOFF-3309): Fix ancestor tree crash on Samsung

## 10.5.679.0

#### Windows

[TEOFF-3284](http://jira/browse/TEOFF-3284): Focus on next step on Dynamic Assistants

## 10.5.677.0

#### Android

[TEOFF-3280](http://jira/browse/TEOFF-3280): Improved loading command buttons for better performance  
[TEOFF-3291](http://jira/browse/TEOFF-3291): Changed dialog hide to dismiss to fix login loading dialog

#### iOS

[TEOFF-3193](http://jira/browse/TEOFF-3193): Change notification from dialog to banner

#### Windows

[TEOFF-3299](http://jira/browse/TEOFF-3299): Fix for Windows tree dialog ancestors not appearing correctly

## 10.5.674.0

#### Android

[TEOFF-3090](http://jira/browse/TEOFF-3090): Fix possibility to open multiple tree dialogs on multiple click

### Additional changes from 10.4

#### Shared

[TEOFF-2844](http://jira/browse/TEOFF-2844): Add Spanish language translations  
[TEOFF-3179](http://jira/browse/TEOFF-3179): Pin icon appears on trees when it's not in use  
[TEOFF-3164](http://jira/browse/TEOFF-3164): Fix for references not updating when reloading record  
[TEOFF-3276](http://jira/browse/TEOFF-3276): Show document class and name in displayed field in document form  
[TEOFF-2844](http://jira/browse/TEOFF-2844): Added application language translations  
[TEOFF-3245](http://jira/browse/TEOFF-3245): When searching in a list, only search on attributes shown in cards and not all attributes of an entity  
[TEOFF-3197](http://jira/browse/TEOFF-3197): Added default icons for documents and media if the file doesn't have an image  
[TEOFF-3220](http://jira/browse/TEOFF-3220): Added previous versions file to nightly builds page  
[TEOFF-3123](http://jira/browse/TEOFF-3123): Show "Internal Test Use Only" on login page if in developer mode

#### Android

[TEOFF-3255](http://jira/browse/TEOFF-3255): Fix android DateTimePickerDialog clear button missing when device rotated  
[TEOFF-3267](http://jira/browse/TEOFF-3267): Fix command labels not updating in lists  
[TEOFF-3256](http://jira/browse/TEOFF-3256): Fix app navigating back from root activity when a breadcrumb item selected  
[TEOFF-3011](http://jira/browse/TEOFF-3011): Add barcode scanner to entry fields rather than as a toolbar, saving screen space  
[TEOFF-3211](http://jira/browse/TEOFF-3211): Fixed an issue where user could end up on the login screen when they were already logged in  
[TEOFF-3241](http://jira/browse/TEOFF-3241): Fix for crashes using radio group fields on Android 5  
[TEOFF-3076](http://jira/browse/TEOFF-3076): Allow barcode and search view to both be visible in an LoV Dialog  
[TEOFF-3080](http://jira/browse/TEOFF-3080): fix Android Command Button Padding  
[TEOFF-3237](http://jira/browse/TEOFF-3237): Fix crash when setting the color of menu items  
[TEOFF-3239](http://jira/browse/TEOFF-3239): Fixed a crash when navigating away from pages  
[TEOFF-3229](http://jira/browse/TEOFF-3229): Fixed a crash when navigating back to an activity Android previously destroyed  
[TEOFF-3073](http://jira/browse/TEOFF-3073): Show toasts sequentially like iOS  
[TEOFF-3096](http://jira/browse/TEOFF-3096): Scale QR Code with device screen size  
[TEOFF-3218](http://jira/browse/TEOFF-3218): Fixed a crash when navigating around the app

#### iOS

[TEOFF-3271](http://jira/browse/TEOFF-3271): Fixed a crash when navigating back from a list  
[TEOFF-3252](http://jira/browse/TEOFF-3252): Fix crash due to empty menu table cell  
[TEOFF-3173](http://jira/browse/TEOFF-3173): fix badgeCarditem measurement when the badge has a label  
[TEOFF-3224](http://jira/browse/TEOFF-3224): Added missing interface file to iOS framework project  
[TEOFF-3225](http://jira/browse/TEOFF-3225): Fix app icon being resized incorrectly on Login page when region set to Swedish  
[TEOFF-3231](http://jira/browse/TEOFF-3231): Fix crash on startup when region set to Netherlands

#### Windows

[TEOFF-3190](http://jira/browse/TEOFF-3190): fix Document Details padding  
[TEOFF-3228](http://jira/browse/TEOFF-3228): Allow for different formatting region and language  
[TEOFF-3270](http://jira/browse/TEOFF-3270): Fix for Windows menu not updating selected item correctly  
[TEOFF-3213](http://jira/browse/TEOFF-3213): Fixed a memory leak causing the application to get slower  
[TEOFF-3227](http://jira/browse/TEOFF-3227): Make windows sync monitor button translatable  
[TEOFF-3213](http://jira/browse/TEOFF-3213): Fixed a memory leak  
[TEOFF-3191](http://jira/browse/TEOFF-3191): Hide back button when a lookup dialog is open  
[TEOFF-3214](http://jira/browse/TEOFF-3214): Add all Windows app icon sizes  
[TEOFF-3182](http://jira/browse/TEOFF-3182): Can no longer deselect a date by clicking on it  
[TEOFF-3232](http://jira/browse/TEOFF-3232): Fixed a crash when using the bar code scanner on a Surface device  
[TEOFF-3125](http://jira/browse/TEOFF-3125): Fixed card panel in LOV showing text incorrectly on certain resolutions

## 10.5.652.0

#### Android

[TEOFF-3080](http://jira/browse/TEOFF-3080): Added Android Command Button Padding  
[TEOFF-3096](http://jira/browse/TEOFF-3096): Scale QR Code with device screen size  
[TEOFF-3218](http://jira/browse/TEOFF-3218): Fixed a crash when navigating around the app

#### iOS

[TEOFF-3224](http://jira/browse/TEOFF-3224): Added missing interface file to iOS framework project

## 10.5.623.0

#### Shared

[TEOFF-2877](http://jira/browse/TEOFF-2877): Show entire tree ancestry popover UI on all platforms

## 10.5.615.0

#### Windows

[TEOFF-3054](http://jira/browse/TEOFF-3054): Support Actions / Details tabs for Windows when the window is small

#### Android

[TEOFF-3082](http://jira/browse/TEOFF-3082): Fixed a crash when rotating the screen at the same time as the device ends initialization  
[TEOFF-3091](http://jira/browse/TEOFF-3091): Fixed Android large edit texts being opened when they should not

## 10.4.647.0

#### Shared

[TEOFF-3206](http://jira/browse/TEOFF-3206): Fixed a crash when processing messages takes longer than the db transaction timeout

#### Android

[TEOFF-3208](http://jira/browse/TEOFF-3208): Fixed a crash when closing the bug reporter dialog

#### iOS

[TEOFF-3126](http://jira/browse/TEOFF-3126): Fix for tree page title view broken on first load on iOS >10  
[TEOFF-3195](http://jira/browse/TEOFF-3195): Can now send device logs on initilization

## 10.4.639.0

#### Shared

[TEOFF-3150](http://jira/browse/TEOFF-3150): Fixed the Next button being unclickable in dynamic assistant statement questions

#### Android

[TEOFF-3165](http://jira/browse/TEOFF-3165): Made notification id unique so multiple notifications can display  
[TEOFF-3183](http://jira/browse/TEOFF-3183): Fixed a crash when opening the app after Android closed the app while on attachments

## 10.4.636.0

#### Shared

[TEOFF-3089](http://jira/browse/TEOFF-3089): Change navigation to always navigate from the home page so you can now navigate back from a page to the home page  
[TEOFF-3155](http://jira/browse/TEOFF-3155): Fix for maps not opening with correct location in non-English languages

#### iOS

[TEOFF-3128](http://jira/browse/TEOFF-3128): Fixed navigator menu overlap on iOS versions 10 and below  
[TEOFF-3135](http://jira/browse/TEOFF-3135): stop card list from slightly scrolling from the original position. when navigating back from details page opened from a command in the list item

## 10.4.632.0

#### Shared

[TEOFF-3168](http://jira/browse/TEOFF-3168): Fixed sending logs with data not working

#### Android

[TEOFF-3081](http://jira/browse/TEOFF-3081): Make validation bar and message the same colour  
[TEOFF-3181](http://jira/browse/TEOFF-3181): Fixed a crash when returning to a list page

#### Windows

[TEOFF-3166](http://jira/browse/TEOFF-3166): Fixed a crash when opening a push notification while the app is suspended

## 10.4.628.0

#### Windows

[TEOFF-3151](http://jira/browse/TEOFF-3151): Fixed date time picker accept and cancel buttons sometimes not showing

## 10.4.622.0

#### Shared

[TEOFF-3151](http://jira/browse/TEOFF-3151): General application performance improvements

#### Android

[TEOFF-3111](http://jira/browse/TEOFF-3111): Removed incorrect section header from LOVs when adding free input

#### iOS

[TEOFF-3146](http://jira/browse/TEOFF-3146): Fixed a performance issue when opening an action when a page has lots of actions

## 10.4.618.0

#### Shared

[TEOFF-3140](http://jira/browse/TEOFF-3140): Fixed function parameters for LOVs and lists not being passed correctly

#### Android

[TEOFF-3148](http://jira/browse/TEOFF-3148): Fixed a getPackageName error when opening an assistant from a command

## 10.4.617.0

#### Shared

[TEOFF-2696](http://jira/browse/TEOFF-2696): Better error detection in build  
[TEOFF-2962](http://jira/browse/TEOFF-2962): Fixed uploading of EdmFiles with DocType of VIEW

#### Android

[TEOFF-3079](http://jira/browse/TEOFF-3079): Fixed LOV section headers being displayed incorrectly  
[TEOFF-3087](http://jira/browse/TEOFF-3087): Improved the usability of LOV swipe actions  
[TEOFF-3139](http://jira/browse/TEOFF-3139): Fixed a CustomTabManager error when logging in  
[TEOFF-3144](http://jira/browse/TEOFF-3144): Fixed a crash when navigating between screens with an LOV

#### iOS

[TEOFF-3084](http://jira/browse/TEOFF-3084): Wrong menu icon in breadcrumbs popup

## 10.4.605.0

#### Shared

[TEOFF-2744](http://jira/browse/TEOFF-2744): Added support for parameters in list count functions

#### Android

[TEOFF-3082](http://jira/browse/TEOFF-3082): Fixed a crash when rotating the screen at the same time as the device ends initialization  
[TEOFF-3091](http://jira/browse/TEOFF-3091): Fixed Android large edit texts being opened when they should not

## 10.4.596.0

#### Shared

[TEOFF-3136](http://jira/browse/TEOFF-3136): Fixed sort and search not working on LOVs

## 10.4.593.0

#### Android

[TEOFF-3094](http://jira/browse/TEOFF-3094): Fixed large text fields not restricting text to the attributes maximum length  
[TEOFF-3097](http://jira/browse/TEOFF-3097): Fixed message dialogs not showing when opened after a navigate

#### Windows

[TEOFF-3122](http://jira/browse/TEOFF-3122): Fixed a crash when loading radio buttons

## 10.4.579.0

#### Android

[TEOFF-3088](http://jira/browse/TEOFF-3088): Fixed the date time picker being the incorrect size on small Android 5 devices

## 10.4.574.0

#### Shared

[TEOFF-2704](http://jira/browse/TEOFF-2704): Added the change log file

