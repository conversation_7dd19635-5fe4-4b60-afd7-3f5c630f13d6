{"name": "FndTstOffline", "component": "FNDTST", "version": "1706901162:1948287535", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}, "CustomersWithoutJohn": {"name": "CustomersWithoutJohn", "entity": "TstCustomer", "array": true, "offlinefilter": {"!=": [{"var": "CustomerName"}, "<PERSON>"]}}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}, "CustomerType": {"datatype": "Text", "keygeneration": "User"}, "CustomerTypeDesc": {"datatype": "Text", "keygeneration": "User"}}, "references": {"CustomerTypeRef": {"target": "TstCustomerType", "mapping": {"CustomerType": "TypeId"}, "prefetch": {"CustomerTypeDesc": "TypeDescription"}}}, "arrays": {"ExpandsArray": {"target": "TstExpand", "mapping": {"CustomerNo": "ColA"}}, "ExpandsFilteredArray": {"target": "TstExpand", "mapping": {"CustomerNo": "ColA"}, "offlinefilter": {"==": [{"var": "Id"}, "<PERSON>"]}}}}, "TstCustomerType": {"name": "TstCustomerType", "hasETag": true, "CRUD": "Read", "luname": "TstCustomerType", "ludependencies": ["TstCustomerType"], "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text", "keygeneration": "User"}, "TypeDescription": {"datatype": "Text", "keygeneration": "User"}}}}, "functions": {"GetCustomers": {"name": "GetCustomers", "returnType": {"dataType": "Structure", "subType": "TstCustomer", "collection": true}}}, "procedures": {"Function<GetCustomers>": {"name": "GetCustomers", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subType": "TstCustomer", "collection": true}, {"name": "Customer", "dataType": "Structure", "subType": "TstCustomer", "collection": false}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CA"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "set", "args": {"value": "TYPE_A"}}, "assign": "Customer.CustomerType"}, {"call": {"method": "proc", "args": {"namespace": "List", "name": "Add", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Customer"}, {"call": {"method": "set", "args": {"value": "CB"}}, "assign": "Customer.CustomerNo"}, {"call": {"method": "proc", "args": {"namespace": "List", "name": "Add", "paramsArray": ["${Result}", "${Customer}"]}}}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}