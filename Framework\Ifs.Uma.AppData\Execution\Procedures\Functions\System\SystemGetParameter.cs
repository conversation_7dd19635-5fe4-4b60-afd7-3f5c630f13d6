﻿using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemGetParameter : SystemFunction
    {
        public const string FunctionName = "GetParameter";

        public SystemGetParameter()
            : base(FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string name = parameters[0].GetString();
            if (string.IsNullOrEmpty(name))
            {
                return null;
            }

            MobileClientParam param = context.DbDataContext.AppParameters.FirstOrDefault(x => x.Parameter == name);

            if (param?.Value == null)
            {
                return null;
            }

            return ConvertParamValueToType(param.Value, param.ValueType);
        }

        private static object ConvertParamValueToType(string paramValue, string valueType)
        {
            switch (valueType)
            {
                case "BOOLEAN":
                    string paramUpper = paramValue.ToUpperInvariant();
                    return paramUpper == "Y" || paramUpper == "TRUE";
                case "NUMBER":
                    return ObjectConverter.TryParse(paramValue, out double value) ? value : (double?)null;
                default:
                    return paramValue;
            }
        }
    }
}
