﻿namespace Ifs.Cloud.Client
{
    /// <summary>
    /// Contains constants and enumerations used to create and analyze the http headers that are used 
    /// in the communication between different clients and IFS Cloud.
    /// </summary>
    public static class IfsCloudHttpHeaderFields
    {
        /// <summary>
        /// This header is sent in a request from the client used for authorization.
        /// 
        /// Ex:
        /// "X-IFSCloud-Authorization: authenticate:SystemID^alain^qZ3cery7eFD6cFk1XTRj8qsdabHGUEEVxN43B40WgnVdJDwdLY/dgEyTbqmC/mB/ImtGObgT9mzrL0iqGf+Ern/hsl6tZC+os3N/TWRZKKG8u6I5jj6Hhln9UnZL2keBX8xYxy4QrF1SMAIP+iUbkTsGO2D+TW69Itm5KrDhqXE="
        /// "X-IFSCloud-Authorization: session:ad84715d-7e40-4797-a9c9-fd88c9e49d52"
        /// </summary>
        public const string AuthorizationHeader = "Authorization";

        /// <summary>
        /// This header is sent in a request from the client used for open id authentication.
        /// 
        /// Ex:
        /// "X-IFSCloud-AuthToken: authenticate:qZ3cery7eFD6cFk1XTRj8qsdabHGUEEVxN43B40WgnVdJDwdLY/dgEyTbqmC/mB/ImtGObgT9mzrL0iqGf+Ern/hsl6tZC+os3N/TWRZKKG8u6I5jj6Hhln9UnZL2keBX8xYxy4QrF1SMAIP+iUbkTsGO2D+TW69Itm5KrDhqXE="
        /// "X-IFSCloud-Authorization: session:ad84715d-7e40-4797-a9c9-fd88c9e49d52"
        /// </summary>
        public const string AuthorizationHeaderOpenId = "X-IFSCloud-AuthToken";

        /// <summary>
        /// Used when requesting a SessionID.
        /// </summary>
        public const string Authenticate = "authenticate:";
        
        /// <summary>
        /// Used for resource requests.
        /// </summary>
        public const string Session = "session:";

        /// <summary>
        ///  This header is sent in a request from the client used to identify what locale, cloud resource and version should be ues.
        ///  
        ///  <para>The format of the data is:
        ///  "AppName^ApplicationID^OS^OSVersion^AppVersion"
        ///  </para>
        ///  <para>
        ///  AppName: The name of the Touch App.
        ///  ApplicationID : An unique identifier for the app on the client device.
        ///  OS: Client Operating System.
        ///  AppVersion: The client version of the Touch App.
        ///  Ex: 
        ///  "X-IFSCloud-App: My Resource^X0123456789^Android^4.1.0^1.2.3"
        ///  </para>
        /// </summary>
        public const string AppInfoHeader = "X-IFS-Native-App-Name";

        /// <summary>
        /// This header is sent in a request from the client to identify the vendor of the calling application.
        /// The information is only required if the client application vendor is different from the cloud resource vendor
        /// 
        /// Ex:
        /// "X-IFSCloud-Vendor:IFS"
        /// </summary>
        public const string ClientAppVendorHeader = "X-IFSCloud-Vendor";

        /// <summary>
        /// This header is sent in a request from the client to identify the device ID.
        /// 
        /// Ex:
        /// "X-IFS-Native-Device-ID"
        /// </summary>
        public const string DeviceIdHeader = "X-IFS-Native-Device-ID";

        /// <summary>
        /// This header is sent in a request from the client to identify the message ID.
        /// 
        /// Ex:
        /// "X-IFS-Native-Message-ID"
        /// </summary>
        public const string MessageIdHeader = "X-IFS-Native-Message-ID";
        
        /// <summary>
        /// This header is sent in a request from the client to identify the e-Tag.
        /// 
        /// Ex:
        /// "If-Match"
        /// </summary>
        public const string ETagHeader = "If-Match";
        
        /// <summary>
        /// This header is sent in a request from the client to identify if compression is needed.
        /// 
        /// Ex:
        /// "usecompression"
        /// </summary>
        public const string UseCompression = "usecompression";
        
        /// <summary>
        /// This header is sent in a request from the client containing locale information.
        /// 
        /// The format is the same as <see cref="System.Globalization.CultureInfo"/>.
        /// 
        /// Ex:
        /// "Accept-Language:en-US"
        /// "Accept-Language:sv-SE"
        /// </summary>
        public const string LocaleHeader = "Accept-Language";

        /// <summary>
        /// This header is added to response when an authorization error has occured.
        /// 
        /// When this header is added the response code is set to: 401 Unauthorized.
        /// </summary>
        public const string CloudAuthorizationErrorHeader = "X-IFSCloud-AuthorizationError";

        /// <summary>
        /// This header is added to the response when there is an App configuration related error. 
        /// 
        /// When this header is added the response code is set to: 404, Not Found.
        /// </summary>
        public const string AppErrorHeader = "X-IFSCloud-AppError";

        /// <summary>
        /// This header is added to the response when a Resource error has occured.
        ///
        /// When this header is added the response code is set to: 500 Internal Server Error.
        /// </summary>
        public const string CloudResourceErrorHeader = "X-IFSCloud-ResourceError";

        /// <summary>
        /// This header is added to the response when an unknown error in the cloud has occured.
        /// 
        /// When this header is added the response code is set to: 500 Internal Server Error.
        /// </summary>
        public const string CloudErrorHeader = "X-IFSCloud-CloudError";

        /// <summary>
        /// This header is used to enable Gzip compression on downloads.
        /// 
        /// When this heading is added and set to "gzip, deflate", if configured the server will send data compressed.
        /// </summary>
        public const string AcceptEncoding = "Accept-Encoding";
        
        /// <summary>
        /// This header is used to indicate the media type of the resource.
        /// 
        /// When this header is added, the type of body is mentioned
        /// </summary>
        public const string ContentType = "Content-Type";

        /// <summary>
        /// Used to split concatenated data.
        /// Ex: To split "x^y^z" to the array {"x", "y", "z"}.
        /// </summary>
        public static string[] TokenSplitter { get; } = new string[] { "^" };

        /// <summary>
        /// Specifies messages should be sent as Base64 encoded compressed data rather
        /// that text to improve server performance
        /// </summary>
        public const string BasicAuthorization = "Authorization";

        /// <summary>
        /// Specifies the content disposition header.
        /// Because HTTP requests doesn't have a standard Content-Disposition header.
        /// </summary>
        public const string ContentDisposition = "X-IFS-Content-Disposition";

        /// <summary>
        ///  This header is used to indicate that the client wants to use timezone aware Resource in server
        ///  If this is not set to true the server wont activate the known time zone functionality.
        ///  This should be only added if a request is based on a known time zone service.
        ///  Otherwise ODP will throw an error
        ///  
        /// EX: X-IFS-Time-Zone-Aware-Request: true
        /// </summary>
        public const string KnownTimeZoneEnabled = "X-IFS-Time-Zone-Aware-Request";

        /// <summary>
        ///  This header is used to indicate that the client wants to use site timezone aware Resource in server
        ///  If this is not set to true the server wont activate the site time zone functionality.
        ///  This should be only added for offline Bound/Unbound action requests that are site timezone aware.
        ///  Otherwise ODP will throw an error
        /// </summary>
        public const string ObjSiteTimeZoneResource = "X-IFS-Native-Objsite";
    }
}
