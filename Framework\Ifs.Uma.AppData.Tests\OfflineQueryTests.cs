﻿using System.Linq;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class OfflineQueryTests : FrameworkTest
    {
        [Test]
        public void InUseCustomerTypes()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "InUseCustomerTypesSet");
            EntityQuery query = new EntityQuery(source);

            CheckResults(query, "TypeId", "TYPE_A", "TYPE_B");
        }

        [Test]
        public void InUseCustomerTypesSql()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "InUseCustomerTypesSet");
            EntityQuery query = new EntityQuery(source);

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT t0.type_id FROM in_use_customer_types t0 ORDER BY t0.type_id ASC");
        }

        [Test]
        public void CustomersByName()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "CustomersByNameSet");
            EntityQuery query = new EntityQuery(source);

            CheckResults(query, "CustomerNo", "2", "3");
        }

        [Test]
        public void QueryWithWhereTimestamp()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "ExpiredCustomersSet");
            EntityQuery query = new EntityQuery(source);

            CheckResults(query, "CustomerNo", "2", "4");
        }

        [Test]
        public void QueryFromQuery()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "OuterQuerySet");
            EntityQuery query = new EntityQuery(source);

            CheckResults(query, "CustomerNo", "1", "4");
        }

        [Test]
        public void QueryFromQuerySql()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "OuterQuerySet");
            EntityQuery query = new EntityQuery(source);

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT t0.customer_no FROM outer_query t0 ORDER BY t0.customer_no ASC");
        }

        [Test]
        public void FailLoopingQuery()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "LoopQueryASet");
            EntityQuery query = new EntityQuery(source);

            try
            {
                CheckResults(query, "CustomerNo", "1");

                Assert.Fail("Looping query did not fail");
            }
            catch (DbException ex)
            {
                Assert.AreEqual("view loop_query_a is circularly defined", ex.Message);
            }
        }

        protected override void OnErrorLogged(string message)
        {
            if (!message.Contains("loop_query_a"))
            {
                base.OnErrorLogged(message);
            }
        }

        private void CheckResults(EntityQuery query, string attribute, params string[] values)
        {
            FwDataContext ctx = CreateDataContext();
            var rows = ctx.Query(query).ToArray();
            string[] actualIds = rows.Select(x => (string)x.Row[attribute]).ToArray();
            Assert.That(actualIds, Is.EquivalentTo(values));
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("OfflineQuerySchema", "OfflineQueryData");
        }

        private static string GetSql(EntityQuery query)
        {
            PreparedEntityQuery preparedQuery = query.Prepare();
            return SqlBuilder.BuildDebugGenericSql(preparedQuery.SelectSpec);
        }
    }
}
