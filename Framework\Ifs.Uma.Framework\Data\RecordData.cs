﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.AppData.StringExpressions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;
using Ifs.Uma.Utility.Formatters;

namespace Ifs.Uma.Framework.Data
{
    public class RecordData : ObservableBase, IStringExpressionValueProvider, IDisposable
    {
        public event EventHandler DataChanged;
        public event EventHandler RecordLoaded;

        private readonly ILogger _logger;
        private readonly IDataHandler _data;
        private readonly string[] _restrictedAttributeNames = new string[] { "EntitySetName", "ArraySourceName", "ETag", "PrimaryKeyString" };

        public IMetadata Metadata { get; }

        private bool _hasChanges;
        public bool HasChanges
        {
            get => _hasChanges;
            protected set => SetProperty(ref _hasChanges, value);
        }

        public HashSet<string> ChangedMembers { get; } = new HashSet<string>();

        private string _projectionName;
        public string ProjectionName
        {
            get => _projectionName;
            private set
            {
                SetProperty(ref _projectionName, value);
            }
        }

        private string _entityName;
        public string EntityName
        {
            get => _entityName;
            private set
            {
                if (SetProperty(ref _entityName, value))
                {
                    RecordType = Metadata.GetRecordType(_projectionName, _entityName);
                    EntityCrudType = _data.GetCrudType(_projectionName, _entityName);
                }
            }
        }

        private RecordType _recordType;
        public RecordType RecordType
        {
            get => _recordType;
            private set => SetProperty(ref _recordType, value);
        }

        private CpiCrudType _entityCrudType;
        public CpiCrudType EntityCrudType
        {
            get => _entityCrudType;
            private set => SetProperty(ref _entityCrudType, value);
        }

        public TaskTracker BackgroundTasks { get; } = new TaskTracker();

        private readonly Dictionary<string, RecordRef> _references = new Dictionary<string, RecordRef>();

        private bool _createIfNeeded;
        private RemoteRow _row;

        public RecordData(ILogger logger, IMetadata metadata, IDataHandler data)
        {
            _logger = logger;
            Metadata = metadata;
            _data = data;
        }

        public bool IsNew() => _row != null && !IsExisting();

        public bool IsUsingOnlineSyncPolicy()
        {
            if (_row == null)
            {
                return false;
            }

            CpiEntity entityFromAppMeta = null;
            Metadata.CpiMetaData.App?.SyncEntities?.Entities?.TryGetValue(EntityName, out entityFromAppMeta);
            return entityFromAppMeta?.SyncPolicy?.Type == EntitySyncPolicy.OnlineOnly;
        }

        public bool HasObjId() => _row?.ObjId != null;

        public bool IsExisting()
        {
            if (_row == null)
            {
                return false;
            }

            if (_row.RowId > 0 || _row.ETag != null)
            {
                return true;
            }

            CpiEntity entityFromAppMeta = null;
            Metadata.CpiMetaData.App?.SyncEntities?.Entities?.TryGetValue(EntityName, out entityFromAppMeta);
            CpiEntity entity = Metadata.FindEntity(ProjectionName, EntityName);
            return entityFromAppMeta?.IsView() == true || entity?.IsView() == true;
        }

        public bool IsEmpty() => _row == null;

        public static bool HasLoadedRecord(RecordData recordData)
        {
            return recordData?._row != null;
        }

        public static bool HasLoadedExistingRecord(RecordData recordData)
        {
            return recordData != null && recordData.IsExisting();
        }

        public static bool HasLoadedNewRecord(RecordData recordData)
        {
            return recordData != null && recordData.IsNew();
        }

        public async Task<ExecuteResult> LoadRecordAsync(EntityQuery query, bool createOnNone, bool checkForSingleRecord = false)
        {
            if (query == null) throw new ArgumentNullException(nameof(query));

            query = query.Clone();
            query.Expand = query.Expand?.Concat(_references.Keys).ToArray() ?? _references.Keys.ToArray();

            // If we want to explicitly check if the query returns one record, the checkForSingleRecord flag will be true (for example, singleton elements)
            // In such a scenario, fetch two records to validate if the query results in one record or more than one, and show a warning message
            query.Take = checkForSingleRecord ? 2 : 1;

            await BackgroundTasks.WaitForCompletion();

            ProjectionName = query.DataSource.ProjectionName;
            EntityName = query.DataSource.EntityName;
            _createIfNeeded = createOnNone;

            EntityRecord record;

            try
            {
                EntityQueryResult queryResult = await _data.GetRecordsAsync(query, CancellationToken.None);

                if (checkForSingleRecord && queryResult.Records.Count > 1)
                {
                    Logger.Current.Warning($"The query returned more than one record when expecting only one. Projection: {ProjectionName}, Entity set: {query.DataSource.EntitySetName}, Entity: {EntityName}.");
                }

                record = queryResult.Records.FirstOrDefault();
                if (record?.Row != null)
                {
                    record.Row.EntitySetName = query.DataSource.EntitySetName;
                }
            }
            catch
            {
                LoadRecord(null, null);
                throw;
            }

            if (record == null && createOnNone)
            {
                return await LoadNewRecordAsync(ProjectionName, EntityName, query.DataSource.EntitySetName);
            }

            LoadRecord(ProjectionName, record);
            await BackgroundTasks.WaitForCompletion();
            return ExecuteResult.None;
        }

        public async Task<ExecuteResult> LoadNewRecordAsync(string projectionName, string entityName, string entitySetName = "")
        {
            if (projectionName == null) throw new ArgumentNullException(nameof(projectionName));
            if (entityName == null) throw new ArgumentNullException(nameof(entityName));

            await BackgroundTasks.WaitForCompletion();

            ProjectionName = projectionName;
            EntityName = entityName;
            _createIfNeeded = true;

            try
            {
                ExecuteResult result = await _data.EntityPrepareAsync(projectionName, entityName, entitySetName: entitySetName);

                EntityRecord record = null;
                if (!result.Failed && result.Value is RemoteRow row)
                {
                    row.EntitySetName = entitySetName;
                    record = new EntityRecord(row, null);
                }

                LoadRecord(ProjectionName, record);
                await BackgroundTasks.WaitForCompletion();
                return result;
            }
            catch
            {
                LoadRecord(null, null);
                throw;
            }
        }

        public async Task<ExecuteResult> LoadBlankRecordAsync()
        {
            await BackgroundTasks.WaitForCompletion();

            ProjectionName = null;
            EntityName = null;
            _createIfNeeded = false;

            LoadRecord(null, null);

            await BackgroundTasks.WaitForCompletion();

            return ExecuteResult.None;
        }

        public async Task<ExecuteResult> SaveRecordAsync()
        {
            if (_row == null)
            {
                return ExecuteResult.None;
            }

            await BackgroundTasks.WaitForCompletion();

            ExecuteResult result;
            if (IsNew())
            {
                result = await _data.EntityInsertAsync(ProjectionName, _row);
            }
            else
            {
                result = await _data.EntityUpdateAsync(ProjectionName, _row, ChangedMembers);
            }

            if (!result.Failed)
            {
                RemoteRow updated = result.Value as RemoteRow;
                updated.EntitySetName = _row.EntitySetName;
                LoadRecord(ProjectionName, new EntityRecord(updated, null));
            }

            return result;
        }

        public async Task<ExecuteResult> DeleteRecordAsync()
        {
            if (_row == null)
            {
                return ExecuteResult.None;
            }

            await BackgroundTasks.WaitForCompletion();

            ExecuteResult result = await _data.EntityDeleteAsync(ProjectionName, _row);

            if (!result.Failed)
            {
                if (_createIfNeeded && EntityName != null)
                {
                    result = await LoadNewRecordAsync(ProjectionName, EntityName, _row.EntitySetName);
                }
                else
                {
                    LoadRecord(null, null);
                }
            }

            return result;
        }

        public async Task<ExecuteResult> ReloadRecordAsync()
        {
            if (!IsEmpty() && !IsNew())
            {
                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(Metadata.MetaModel, _row);

                if (key != null)
                {
                    EntityDataSource dataSource = EntityDataSource.FromEntity(Metadata, ProjectionName, EntityName);
                    EntityQuery query = new EntityQuery(dataSource);
                    query.EntitySetName = string.IsNullOrEmpty(query.EntitySetName) ? _row.EntitySetName : query.EntitySetName;
                    query.SetFilter(key);

                    return await LoadRecordAsync(query, _createIfNeeded);
                }
            }

            return ExecuteResult.None;
        }

        public async Task<Stream> GetBinaryDataAsync(string attribute, CancellationToken cancelToken)
        {
            if (TryGetRecordValue(attribute, out object value) &&
                value is byte[] data &&
                data != null)
            {
                return new MemoryStream(data);
            }

            if (_row == null)
            {
                return null;
            }

            ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(Metadata.MetaModel, _row);

            if (key == null)
            {
                return null;
            }

            ExecuteResult result = await _data.GetBinaryDataAsync(ProjectionName, key, attribute, cancelToken);

            if (result.Value is Stream streamData)
            {
                return streamData;
            }

            return null;
        }

        public void LoadRecord(string projectionName, EntityRecord record)
        {
            if (_row != null)
            {
                _row.PropertyChanged -= Data_PropertyChanged;
            }

            ChangedMembers.Clear();

            foreach (RecordRef recordRef in _references.Values)
            {
                recordRef.PauseUpdates = true;
            }

            ProjectionName = projectionName;
            EntityName = record == null ? null : RemoteNaming.ToEntityName(record.Row.TableName);

            _row = record?.Row;
            HasChanges = false;

            if (record?.References != null)
            {
                foreach (KeyValuePair<string, RemoteRow> kvp in record.References)
                {
                    if (!_references.TryGetValue(kvp.Key, out RecordRef recordRef))
                    {
                        recordRef = new RecordRef(_logger, this, _data, ProjectionName, kvp.Key, true);
                        _references[kvp.Key] = recordRef;

                        recordRef.RecordLoaded += RecordRef_RecordLoaded;
                    }

                    recordRef.LoadRecord(kvp.Value);
                }
            }

            foreach (RecordRef recordRef in _references.Values)
            {
                recordRef.PauseUpdates = false;
                if (record?.References?.ContainsKey(recordRef.RefName) != true)
                {
                    recordRef.ReloadRecord();
                }
            }

            if (_row != null)
            {
                _row.PropertyChanged += Data_PropertyChanged;
            }

            RecordLoaded?.Invoke(this, EventArgs.Empty);
            DataChanged?.Invoke(this, EventArgs.Empty);
        }

        private void RecordRef_RecordLoaded(object sender, EventArgs e)
        {
            DataChanged?.Invoke(this, EventArgs.Empty);
        }

        public void LoadRecordForTest(RemoteRow row)
        {
            LoadRecord(ProjectionName, row == null ? null : new EntityRecord(row, null));
        }

        public void ResetHasChanges()
        {
            ChangedMembers.Clear();
            HasChanges = false;
            DataChanged?.Invoke(this, EventArgs.Empty);
        }

        public bool Assign(string name, object value)
        {
            if (Equals(value, string.Empty))
            {
                value = null;
            }

            RemoteRow row = _row;
            IMetaTable table = row == null ? null : Metadata.MetaModel.GetTable(row.TableName);
            IMetaDataMember member = table?.FindMemberByPropertyName(name);
            if (member != null)
            {
                row[member.PropertyName] = member.ConvertValue(value);
                return true;
            }
            else if (row != null)
            {
                _logger.Warning(nameof(RecordData) + ".Assign Failed: Member '{0}' does not exist on '{1}'", name, EntityName);
            }

            return false;
        }

        private void Data_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (!_restrictedAttributeNames.Contains(e.PropertyName))
            {
                ChangedMembers.Add(e.PropertyName);
                HasChanges = true;
                DataChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        public bool TryGetRecordValue(string propertyName, out object value)
        {
            if (_row != null)
            {
                IMetaTable table = Metadata.MetaModel.GetTable(_row.TableName);
                IMetaDataMember member = table.FindMemberByPropertyName(propertyName);
                if (member != null)
                {
                    value = _row[member.PropertyName];
                    return true;
                }

                CpiReference cpiRef = Metadata.FindReference(ProjectionName, EntityName, propertyName);
                if (cpiRef != null)
                {
                    value = GetReference(propertyName)?.Row;
                    return true;
                }

                AttributePath attributePath = AttributePath.Create(propertyName);
                return TryGetRecordValue(attributePath, out value);
            }

            value = null;
            return false;
        }

        public bool TryGetRecordValue(AttributePath attributePath, out object value)
        {
            if (attributePath != null && _row != null)
            {
                if (attributePath.RefName != null)
                {
                    CpiReference cpiRef = Metadata.FindReference(ProjectionName, RemoteNaming.ToEntityName(_row.TableName), attributePath.RefName);
                    if (cpiRef != null)
                    {
                        IMetaTable refTable = Metadata.GetTableForEntityName(cpiRef.Target);
                        IMetaDataMember refMember = refTable?.FindMemberByPropertyName(attributePath.AttributeName);
                        if (refMember != null)
                        {
                            RecordRef recordRef = GetReference(attributePath.RefName);
                            value = recordRef?.Row?[refMember.PropertyName];
                            return true;
                        }
                    }
                }
                else
                {
                    IMetaTable table = Metadata.MetaModel.GetTable(_row.TableName);
                    IMetaDataMember member = table.FindMemberByPropertyName(attributePath.AttributeName);
                    if (member != null)
                    {
                        value = _row[member.PropertyName];
                        return true;
                    }

                    if (string.Equals(attributePath.AttributeName, "etag", StringComparison.OrdinalIgnoreCase))
                    {
                        bool hasEtag = !string.IsNullOrEmpty(_row.ObjId) || !string.IsNullOrEmpty(_row.ObjVersion);
                        if (hasEtag)
                        {
                            value = _row.ObjId + ":" + _row.ObjVersion;
                        }
                        else
                        {
                            value = _row.RowId == 0 ? null : "#" + _row.RowId;
                        }
                        return true;
                    }
                }
            }

            value = null;
            return false;
        }

        public RecordData CreateNew()
        {
            return new RecordData(_logger, Metadata, _data);
        }

        public RecordRef GetReference(string refName)
        {
            if (string.IsNullOrEmpty(refName))
            {
                return null;
            }

            if (!_references.TryGetValue(refName, out RecordRef reference))
            {
                reference = new RecordRef(_logger, this, _data, ProjectionName, refName, false);
                _references[refName] = reference;

                reference.RecordLoaded += RecordRef_RecordLoaded;
            }

            return reference;
        }

        public RemoteRow GetRemoteRow()
        {
            return _row;
        }

        public ObjPrimaryKey ToPrimaryKey()
        {
            if (IsEmpty())
            {
                return null;
            }

            return ObjPrimaryKey.FromPrimaryKey(Metadata.MetaModel, _row);
        }

        public ObjKey GetReferenceKey(CpiReference reference, bool recordIsTarget)
        {
            if (reference == null) throw new ArgumentNullException(nameof(reference));

            if (_row == null)
            {
                return null;
            }

            return Metadata.GetReferenceKey(_row, reference, recordIsTarget);
        }

        public EntityDataSource GetArrayDataSource(string arrayName)
        {
            if (IsEmpty())
            {
                return null;
            }

            EntityDataSource dataSource = EntityDataSource.FromEntity(Metadata, ProjectionName, EntityName);
            return dataSource.SelectArray(_row, arrayName);
        }

        public void ExtractFunctionParameters(IReadOnlyDictionary<string, string> mapping, Dictionary<string, object> into)
        {
            if (mapping != null)
            {
                foreach (KeyValuePair<string, string> item in mapping)
                {
                    if (TryGetRecordValue(item.Value, out object value))
                    {
                        into[item.Key] = value;
                    }
                }
            }
        }

        /// <summary>
        /// Loads referenced records asynchronously before extracting parameter values
        /// </summary>
        public async Task ExtractFunctionParametersAsync(IReadOnlyDictionary<string, string> mapping, Dictionary<string, object> into)
        {
            if (mapping != null)
            {
                foreach (KeyValuePair<string, string> item in mapping)
                {
                    string possibleRefName = item.Value;

                    // If it's a referenced record, then we must wait until the row is loaded and then return it
                    CpiReference cpiRef = Metadata.FindReference(ProjectionName, EntityName, possibleRefName);

                    // If it's an attribute of a referenced record, still we must wait for the record to be loaded before extracting the value
                    AttributePath attributePath = AttributePath.Create(item.Value);

                    if (attributePath.RefName != null)
                    {
                        possibleRefName = attributePath.RefName;
                        cpiRef = Metadata.FindReference(ProjectionName, EntityName, possibleRefName);
                    }

                    if (cpiRef != null)
                    {
                        _ = GetReference(possibleRefName); // No use for the return value because we just want to load the record
                        await BackgroundTasks.WaitForCompletion();
                    }

                    if (TryGetRecordValue(item.Value, out object value))
                    {
                        into[item.Key] = value;
                    }
                }
            }
        }

        public object this[string name]
        {
            get => TryGetRecordValue(name, out object value) ? value : null;
            set => Assign(name, value);
        }

        public object this[AttributePath attributePath]
        {
            get => TryGetRecordValue(attributePath, out object value) ? value : null;
            set => Assign(attributePath.Path, value);
        }

        public object ReadParamValue(string str)
        {
            // Attempts to read '${Name}' as an object value
            // Otherwise replaces params as a string value
            if (str == null) return null;

            if ((str.StartsWith("${") && str.EndsWith("}")) ||
                (str.StartsWith("$[") && str.EndsWith("]")) ||
                (str.StartsWith("$(") && str.EndsWith(")")))
            {
                string propertyName = str.Substring(2, str.Length - 3);

                if (TryGetRecordValue(propertyName, out object value))
                {
                    return value;
                }
            }

            string ValueProvider(string attributeName)
            {
                if (TryGetRecordValue(attributeName, out object paramValue))
                {
                    return ObjectConverter.ToString(paramValue) ?? string.Empty;
                }

                return string.Empty;
            }

            string ret = str.ReplaceParams(ValueProvider);
            return ret == string.Empty ? null : ret;
        }

        public void AssignParams(string paramsList)
        {
            if (paramsList == null) return;

            Dictionary<string, string> parameters = paramsList.SplitToDictionary();
            foreach (KeyValuePair<string, string> item in parameters)
            {
                Assign(item.Key, item.Value);
            }
        }

        public bool TryGetFormattedValue(string propertyName, out string value)
        {
            value = string.Empty;

            if (TryGetRecordValue(propertyName, out object paramValue))
            {
                AttributePathInfo attribute = AttributePathInfo.Get(Metadata, ProjectionName, EntityName, propertyName);
                if (attribute == null)
                {
                    value = AttributeFormatter.FormatValue(paramValue);
                }
                else
                {
                    IValueFormatter formatter = AttributeFormatter.For(attribute.Member);
                    value = formatter.Format(paramValue);
                }

                return true;
            }

            return false;
        }

        public bool TryCallExpressionMethod(string methodName, object[] parameters, out object result)
        {
            result = null;

            switch (methodName)
            {
                case "isNew":
                    result = IsNew();
                    return true;
            }

            return false;
        }

        bool IStringExpressionValueProvider.TryGetValue(string propertyName, out object value)
        {
            value = null;
            return TryGetRecordValue(propertyName, out value);
        }

        bool IStringExpressionValueProvider.TryGetFormattedValue(string propertyName, out string value)
        {
            value = null;
            return TryGetFormattedValue(propertyName, out value);
        }

        #region IDisposable Support

        private bool _disposed = false;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                // Unsubscribe from events
                if (_row != null)
                {
                    _row.PropertyChanged -= Data_PropertyChanged;

                    foreach (RecordRef recordRef in _references.Values)
                    {
                        recordRef.Dispose();
                    }

                    _row = null;
                }

                foreach (RecordRef recordRef in _references.Values)
                {
                    recordRef.RecordLoaded -= RecordRef_RecordLoaded;
                    recordRef.Dispose();
                }

                // Dispose of disposable objects
                BackgroundTasks?.Dispose();
            }

            _disposed = true;
        }

        // Ensure that Dispose is called when the object is finalized
        ~RecordData()
        {
            Dispose();
        }

        #endregion
    }
}
