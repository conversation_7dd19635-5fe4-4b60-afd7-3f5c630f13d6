﻿using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;
using Ifs.Uma.UI.Model;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Extensions;
using Ifs.Uma.UI.Services;
using Ifs.Uma.UI;
using Ifs.Uma.Localization;
using Ifs.Uma.AppData;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Framework.UI.Forms
{
    public class FormBuilder<T>
    {
        private readonly IMetadata _metadata;
        private readonly Form _form;
        private readonly T _dataContext;
        private readonly Dictionary<string, Type> _overriddenFieldTypes = new Dictionary<string, Type>();
        private readonly IDataHandler _dataHandler;

        public Form Form => _form;
        public T DataContext => _dataContext;

        public FormBuilder(IMetadata metadata, T dataContext, IDataHandler dataHandler = null)
        {
            _form = new Form { LayoutVariant = Form.LayoutVariants.Regular };
            _metadata = metadata;
            _dataContext = dataContext;
            _dataHandler = dataHandler;
        }

        #region Metadata Field Generation

        public Field AddField(int id, string projectionName, CpiField fieldDef, AttributeValue attributeValue)
        {
            Field field = _metadata.CreateFieldType(projectionName, fieldDef, id);
            field.Name = fieldDef.Label;

            CpiExpression visible = fieldDef.OfflineVisible ?? (fieldDef.Visible != null ? fieldDef.Visible : fieldDef.ColumnVisible);
            field.IsVisible = visible == null || visible.QuickCheck(false);

            field.ShowLabel = fieldDef.ShowLabel.HasValue ? fieldDef.ShowLabel.Value : true;

            field.SizeHint = fieldDef.ControlSize.ToSizeHint();
            field.Multiline = fieldDef.Multiline;

            SetFieldEditability(projectionName, field, fieldDef, attributeValue);

            LovField lookupField = field as LovField;
            if (lookupField == null)
            {
                field.SetBinding(attributeValue, nameof(attributeValue.Value));
            }

            Form.AllFields.Add(field);

            return field;
        }

        public void SetFieldEditability(string projectionName, Field field, CpiField fieldDef, AttributeValue attributeValue)
        {
            if (attributeValue == null || 
                attributeValue.AttributePath.RefName != null ||
                fieldDef.Control == CpiControlType.ComputedField)
            {
                field.Editability = FieldEditability.Never;
                return;
            }

            CpiEntity entity = _metadata.FindEntity(projectionName, fieldDef.Entity);

            if (entity != null)
            {
                CpiCrudType crudType = entity.GetCrudType();

                if (_dataHandler != null)
                {
                    crudType = _dataHandler.GetCrudType(projectionName, entity.Name);
                }

                bool isPrimaryKey = entity.Keys != null && entity.Keys.Contains(fieldDef.Attribute);
                field.SetFieldEditability(crudType, isPrimaryKey);
            }
        }

        #endregion

        #region Property Field Generation

        public Field AddField<TProp>(Expression<Func<T, TProp>> propertyExpression)
        {
            string propertyPath = propertyExpression.Path();
            Queue<string> parts = new Queue<string>(propertyPath.Split('.'));

            Type currentType = typeof(T);
            while (parts.Count > 0)
            {
                string currentPath = parts.Dequeue();

                PropertyInfo property = currentType.GetRuntimeProperty(currentPath);

                if (property == null)
                {
                    break;
                }

                if (parts.Count == 0)
                {
                    return GetOrCreateFieldForProperty(propertyPath, property);
                }

                currentType = property.PropertyType;
            }

            return null;
        }

        private Field GetOrCreateFieldForProperty(string path, PropertyInfo property)
        {
            Field existingField = _form.FindField(path);

            if (existingField != null)
            {
                return existingField;
            }

            Field field = CreateField(path, property.PropertyType, DateFormats.Timestamp, false, null, null);
            field.Id = path;
            field.SetBinding(_dataContext, path);

            SetupFieldAttributes(field, property);

            _form.AllFields.Add(field);

            return field;
        }

        private static void SetupFieldAttributes(Field field, PropertyInfo property)
        {
            field.Name = MakeName(property.Name);
            field.IsRequired = !TypeHelper.IsNullAssignable(property.PropertyType);
            field.Editability = FieldEditability.InsertingOrUpdating;
        }

        private static string MakeName(string path)
        {
            StringBuilder sb = new StringBuilder();
            foreach (char c in path)
            {
                if (c != '.')
                {
                    if (sb.Length > 0 && char.IsUpper(c))
                    {
                        sb.Append(" ");
                        sb.Append(c);
                    }
                    else
                    {
                        sb.Append(c);
                    }
                }
            }
            return sb.ToString();
        }

        #endregion

        #region Field Utils

        public string Path<TProp>(Expression<Func<T, TProp>> propertyExpression)
        {
            if (propertyExpression == null) throw new ArgumentNullException(nameof(propertyExpression));
            return propertyExpression.Path();
        }

        public Field GetField(string propertyPath)
        {
            return _form.FindField(propertyPath);
        }

        public void SetFieldEditability(string propertyPath, FieldEditability state)
        {
            Field field = _form.FindField(propertyPath);
            if (field != null)
            {
                field.Editability = state;
            }
        }

        public void AddValueChangeListener(string propertyPath, EventHandler<ValueChangedEventArgs> handler)
        {
            Field field = _form.FindField(propertyPath);
            if (field != null)
            {
                field.ValueChanged += handler;
            }
        }

        public void OverrideFieldType(string propertyPath, Type fieldType)
        {
            _overriddenFieldTypes[propertyPath] = fieldType;
        }

        public void AutoSetDefaultLayouts()
        {
            foreach (string layoutVariant in new[] { Form.LayoutVariants.Compact, Form.LayoutVariants.Regular })
            {
                FormLayoutData layout = Form.GetLayout(layoutVariant);
                layout.SetDefaultLayout();
            }
        }

        public void SetLayout(string[][] columnIds)
        {
            SetCompactLayout(columnIds);
            SetRegularLayout(columnIds);
        }

        public void SetCompactLayout(string[][] columnIds)
        {
            FormLayoutData layout = _form.GetLayout(Form.LayoutVariants.Compact);
            layout.SetDefaultLayout(columnIds);
        }

        public void SetRegularLayout(string[][] columnIds)
        {
            FormLayoutData layout = _form.GetLayout(Form.LayoutVariants.Regular);
            layout.SetDefaultLayout(columnIds);
        }

        private Field CreateField(string propertyPath, Type type, DateFormats dateFormat, bool largeText, IEnumerable<SelectableItem<object>> enumeration, ContentType? contentType)
        {
            if (_overriddenFieldTypes.TryGetValue(propertyPath, out Type overriddenFieldType))
            {
                return (Field)Activator.CreateInstance(overriddenFieldType);
            }
            else
            {
                return FormBuilder.CreateField(type, dateFormat, largeText, enumeration, contentType);
            }
        }

        public HeaderField AddHeaderField(string id, string header)
        {
            HeaderField field = new HeaderField
            {
                Id = id,
                Name = string.Format(CultureInfo.CurrentCulture, Strings.HeaderName, header),
                Value = header
            };
            Form.AllFields.Add(field);
            return field;
        }

        public BadgeField AddBadgeField(string id)
        {
            BadgeField field = new BadgeField
            {
                Id = id
            };
            Form.AllFields.Add(field);
            return field;
        }

        public CommandField AddCommandField(string id, string text, Command command)
        {
            CommandField field = new CommandField();
            field.Id = id;
            field.Name = text;
            field.Value = text;
            field.Command = command;
            field.IsVisible = false;
            Form.AllFields.Add(field);
            return field;
        }

        public LookupField AddLookupField<TP>(string name, Expression<Func<T, TP>> propertyExpression)
        {
            string path = propertyExpression.Path();
            LookupField field = new LookupField();
            field.Id = FormBuilder.CreateLookupFieldId(path);
            field.Name = name;
            field.BackingField = GetField(path);
            Form.AllFields.Add(field);
            return field;
        }

        #endregion
    }

    public static class FormBuilder
    {
        public static Field CreateFieldForMetaDataMember(IMetaDataMember metaDataMember)
        {
            if (metaDataMember == null) throw new ArgumentNullException(nameof(metaDataMember));

            List<SelectableItem<object>> enumeration = null;
            if (metaDataMember.Enumeration != null)
            {
                enumeration = metaDataMember.Enumeration.Values.Where(k => !k.ClientOnly).Select(x => new SelectableItem<object>(
                    string.IsNullOrEmpty(x.DisplayName) ? string.Format(CultureInfo.InvariantCulture, "[{0}]", x.LocalValue) : x.DisplayName,
                    x.LocalValue)).ToList();
            }

            Field field = CreateField(metaDataMember.ColumnType, metaDataMember.DateFormat, metaDataMember.MaxLength >= 1000, enumeration, ContentType.Text);
            SetupFieldAttributes(field, metaDataMember, enumeration);
            return field;
        }

        internal static Field CreateField(Type type, DateFormats dateFormat, bool largeText, IEnumerable<SelectableItem<object>> enumeration, ContentType? contentType)
        {
            if (enumeration != null)
            {
                if (contentType.HasValue && contentType.Value == ContentType.Boolean)
                {
                    // Special case for IFS FndBoolean here 
                    return new BoolField();
                }

                if (contentType.HasValue && contentType.Value == ContentType.MultiEnumeration)
                {
                    return new ComboMultiSelectField();
                }

                return new ComboField();
            }

            if (type == typeof(PickedFile))
            {
                return new FilePickerField();
            }

            if (type == typeof(byte[]))
            {
                FilePickerField field = new FilePickerField();
                field.ValueType = FilePickerValueType.ByteArray;
                return field;
            }

            Type nonNullType = TypeHelper.GetNonNullableType(type);
            switch (TypeHelper.GetTypeCode(nonNullType))
            {
                case TypeCode.Boolean:
                    return new BoolField();

                case TypeCode.Int16:
                case TypeCode.UInt16:
                case TypeCode.Int32:
                case TypeCode.UInt32:
                    return new IntField();

                case TypeCode.Int64:
                case TypeCode.UInt64:
                    return new LongField();

                case TypeCode.Decimal:
                case TypeCode.Double:
                    return new DoubleField();

                case TypeCode.DateTimeOffset:
                case TypeCode.TimeSpan:
                case TypeCode.DateTime:
                    {
                        switch (dateFormat)
                        {
                            case DateFormats.Date:
                                return new DateField();
                            case DateFormats.Time:
                                return new TimeField();
                            case DateFormats.TimestampUtc:
                                return new DateTimeFieldUtc();
                            default:
                                return new DateTimeField();
                        }
                    }

                case TypeCode.String:
                    {
                        TextField textField = largeText ? new LargeTextEdit() : new TextField();
                        if (contentType.HasValue)
                        {
                            textField.ContentType = contentType.Value;
                        }
                        return textField;
                    }
                case TypeCode.Enumeration:
                    return new ComboField();

                default:
                    {
                        TextField textField = new TextField();
                        if (contentType.HasValue)
                        {
                            textField.ContentType = contentType.Value;
                        }
                        return textField;
                    }
            }
        }

        private static void SetupFieldAttributes(Field field, IMetaDataMember metaDataMember, List<SelectableItem<object>> enumeration)
        {
            field.Name = metaDataMember.DisplayName ?? metaDataMember.PropertyName;

            field.IsRequired = metaDataMember.Mandatory;

            FieldEditability editablity = FieldEditability.InsertingOrUpdating;

            if (!metaDataMember.Insertable && !metaDataMember.Updateable)
            {
                editablity = FieldEditability.Never;
            }
            else if (!metaDataMember.Updateable)
            {
                editablity = FieldEditability.Inserting;
            }

            field.Editability = editablity;

            if (field is TextField textField)
            {
                textField.MaxLength = metaDataMember.MaxLength <= 0 ? int.MaxValue : metaDataMember.MaxLength;
                textField.TextFormat = metaDataMember.TextFormat;
            }

            if (field is SelectionField selectionField && metaDataMember.Enumeration != null)
            {
                List<SelectableItem<object>> items = enumeration.ToList();

                NaturalSortComparer sorter = new NaturalSortComparer();
                items.Sort((x, y) => sorter.Compare(x.Label, y.Label));
                if (!field.IsRequired && !(field is ComboMultiSelectField))
                {
                    items.Insert(0, SelectableItem<object>.Create<object>(string.Empty, null));
                }

                selectionField.ItemsSource = items;
            }

            if (field is BoolField boolField && enumeration != null)
            {
                // First value of enumeration is false
                // Second value is true
                SelectableItem<object> falseValue = enumeration.ElementAtOrDefault(0);
                if (falseValue != null)
                {
                    boolField.FalseLabel = falseValue.Label;
                    boolField.FalseValue = falseValue.Value;
                }

                SelectableItem<object> trueValue = enumeration.ElementAtOrDefault(1);
                if (trueValue != null)
                {
                    boolField.TrueLabel = trueValue.Label;
                    boolField.TrueValue = trueValue.Value;
                }
            }

            if (field is DoubleField doubleField)
            {
                if (metaDataMember.Scale >= 0)
                {
                    doubleField.DecimalPlaces = metaDataMember.Scale;
                }

                doubleField.NumberFormat = metaDataMember.NumberFormat;

                if (!doubleField.DecimalPlaces.HasValue)
                {
                    switch (doubleField.NumberFormat)
                    {
                        case NumberFormat.Decimal:
                        case NumberFormat.Percentage:
                        case NumberFormat.Currency:
                            doubleField.DecimalPlaces = 2;
                            break;
                    }
                }
            }
        }

        public static string CreateLookupFieldId(string backingFieldId)
        {
            return backingFieldId + "Lookup";
        }
    }
}
