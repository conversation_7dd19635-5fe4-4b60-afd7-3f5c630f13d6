using Ifs.Cloud.Client;
using Ifs.Cloud.Client.Types;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Utility;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace Ifs.Tools.HttpClient
{
    public class AuthenticatedHttpClient : IDisposable
    {
        private readonly System.Net.Http.HttpClient _httpClient;
        private readonly Options _options;
        private bool _disposed = false;

        public AuthenticatedHttpClient(Options options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _httpClient = new System.Net.Http.HttpClient();

            // Set up default headers
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "IFS-HttpClient-Tool/1.0");
        }

        public async Task<HttpResponseMessage> GetAsync(string url)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, url);

            // Add authentication headers if credentials are provided
            await AddAuthenticationHeaders(request);

            if (_options.Verbose)
            {
                Console.WriteLine("Request Headers:");
                foreach (var header in request.Headers)
                {
                    Console.WriteLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                }
                Console.WriteLine();
            }

            return await _httpClient.SendAsync(request);
        }

        private async Task AddAuthenticationHeaders(HttpRequestMessage request)
        {
            try
            {
                // If we have a direct access token, use it
                if (!string.IsNullOrEmpty(_options.AccessToken))
                {
                    request.Headers.Add(IfsCloudHttpHeaderFields.AuthorizationHeader, "Bearer " + _options.AccessToken);
                    if (_options.Verbose)
                    {
                        Console.WriteLine("Using direct access token for authentication");
                    }
                    return;
                }

                // If we have username and password, use TOKEN_DIRECT authentication
                if (!string.IsNullOrEmpty(_options.Username) && !string.IsNullOrEmpty(_options.Password))
                {
                    await AddTokenDirectAuthentication(request);
                    return;
                }

                if (_options.Verbose)
                {
                    Console.WriteLine("No authentication credentials provided - making unauthenticated request");
                }
            }
            catch (Exception ex)
            {
                if (_options.Verbose)
                {
                    Console.WriteLine($"Warning: Failed to add authentication headers: {ex.Message}");
                }
                // Continue without authentication rather than failing
            }
        }

        private async Task AddTokenDirectAuthentication(HttpRequestMessage request)
        {
            try
            {
                if (_options.Verbose)
                {
                    Console.WriteLine("Using TOKEN_DIRECT authentication");
                }

                // Use the TouchAppsAuthenticator for TOKEN_DIRECT authentication
                var authenticator = TouchAppsAuthenticator.Instance.Authenticator;
                var systemId = _options.SystemId ?? string.Empty;

                // Get authentication token using TOKEN_DIRECT method
                var authToken = authenticator.GetAuthenticationToken(systemId, _options.Username!, _options.Password!, string.Empty);

                if (!string.IsNullOrEmpty(authToken))
                {
                    // Add the authentication header in the format expected by IFS Cloud
                    request.Headers.Add(IfsCloudHttpHeaderFields.AuthorizationHeader, "Basic " + authToken);

                    if (_options.Verbose)
                    {
                        Console.WriteLine($"Added TOKEN_DIRECT authentication header for user: {_options.Username}");
                    }
                }
                else
                {
                    if (_options.Verbose)
                    {
                        Console.WriteLine("Warning: Failed to generate authentication token");
                    }
                }
            }
            catch (Exception ex)
            {
                if (_options.Verbose)
                {
                    Console.WriteLine($"Warning: TOKEN_DIRECT authentication failed: {ex.Message}");
                }
                throw;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _httpClient?.Dispose();
                }
                _disposed = true;
            }
        }
    }
}
