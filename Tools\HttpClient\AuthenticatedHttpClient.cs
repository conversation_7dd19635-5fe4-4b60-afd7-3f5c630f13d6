using Ifs.Tools.HttpClient.Authentication;
using Ifs.Tools.HttpClient.Models;
using Ifs.Tools.HttpClient.Services;

namespace Ifs.Tools.HttpClient
{
    public class AuthenticatedHttpClient : IDisposable
    {
        private readonly System.Net.Http.HttpClient _httpClient;
        private readonly Options _options;
        private bool _disposed = false;

        // IFS Cloud HTTP header constants
        private const string AuthorizationHeader = "Authorization";
        private const string NativeAppNameHeader = "X-IFS-Native-App-Name";

        public AuthenticatedHttpClient(Options options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _httpClient = new System.Net.Http.HttpClient();

            // Set up default headers
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "IFS-HttpClient-Tool/1.0");
            _httpClient.DefaultRequestHeaders.Add(NativeAppNameHeader, "IFS_aurena_native");
        }

        public async Task<HttpResponseMessage> GetAsync(string url)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, url);

            // Add authentication headers if credentials are provided
            await AddAuthenticationHeaders(request);

            if (_options.Verbose)
            {
                Console.WriteLine("Request Headers:");
                foreach (var header in request.Headers)
                {
                    Console.WriteLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                }
                Console.WriteLine();
            }

            return await _httpClient.SendAsync(request);
        }

        private async Task AddAuthenticationHeaders(HttpRequestMessage request)
        {
            try
            {
                // If we have a direct access token, use it
                if (!string.IsNullOrEmpty(_options.AccessToken))
                {
                    var authHeader = OAuthAuthenticator.GetAuthorizationHeader(_options.AccessToken);
                    request.Headers.Add(AuthorizationHeader, authHeader);

                    if (_options.Verbose)
                    {
                        Console.WriteLine("Using direct access token for authentication");
                    }
                    return;
                }

                // If we have username and password, try OAuth first, then fall back to TOKEN_DIRECT
                if (!string.IsNullOrEmpty(_options.Username) && !string.IsNullOrEmpty(_options.Password))
                {
                    // Try OAuth authentication if server URL is provided
                    if (!string.IsNullOrEmpty(_options.ServerUrl))
                    {
                        var oauthSuccess = await TryOAuthAuthentication(request);
                        if (oauthSuccess) return;
                    }

                    // Fall back to TOKEN_DIRECT authentication
                    AddTokenDirectAuthentication(request);
                    return;
                }

                if (_options.Verbose)
                {
                    Console.WriteLine("No authentication credentials provided - making unauthenticated request");
                }
            }
            catch (Exception ex)
            {
                if (_options.Verbose)
                {
                    Console.WriteLine($"Warning: Failed to add authentication headers: {ex.Message}");
                }
                // Continue without authentication rather than failing
            }
        }

        private async Task<bool> TryOAuthAuthentication(HttpRequestMessage request)
        {
            try
            {
                if (_options.Verbose)
                {
                    Console.WriteLine("Attempting OAuth authentication...");
                }

                // Discover identity provider
                var discovery = new IdentityProviderDiscovery(_httpClient);
                var identityProvider = await discovery.GetIdentityProviderInformation(_options.ServerUrl!);

                if (identityProvider == null)
                {
                    if (_options.Verbose)
                    {
                        Console.WriteLine("OAuth discovery failed - no identity provider found");
                    }
                    return false;
                }

                if (_options.Verbose)
                {
                    Console.WriteLine($"Found OAuth token endpoint: {identityProvider.TokenEndpoint}");
                }

                // Get access token using OAuth
                var oauthAuth = new OAuthAuthenticator(_httpClient);
                var tokenResponse = await oauthAuth.GetDirectAccessToken(identityProvider, _options.Username!, _options.Password!);

                if (tokenResponse != null && !tokenResponse.IsEmpty())
                {
                    var authHeader = OAuthAuthenticator.GetAuthorizationHeader(tokenResponse.AccessToken!);
                    request.Headers.Add(AuthorizationHeader, authHeader);

                    if (_options.Verbose)
                    {
                        Console.WriteLine($"OAuth authentication successful for user: {_options.Username}");
                    }
                    return true;
                }

                if (_options.Verbose)
                {
                    Console.WriteLine("OAuth authentication failed - no access token received");
                }
                return false;
            }
            catch (Exception ex)
            {
                if (_options.Verbose)
                {
                    Console.WriteLine($"OAuth authentication failed: {ex.Message}");
                }
                return false;
            }
        }

        private void AddTokenDirectAuthentication(HttpRequestMessage request)
        {
            try
            {
                if (_options.Verbose)
                {
                    Console.WriteLine("Using TOKEN_DIRECT authentication");
                }

                var systemId = _options.SystemId ?? string.Empty;
                var authHeader = TokenDirectAuthenticator.GetAuthorizationHeader(systemId, _options.Username!, _options.Password!);

                request.Headers.Add(AuthorizationHeader, authHeader);

                if (_options.Verbose)
                {
                    Console.WriteLine($"Added TOKEN_DIRECT authentication header for user: {_options.Username}");
                }
            }
            catch (Exception ex)
            {
                if (_options.Verbose)
                {
                    Console.WriteLine($"Warning: TOKEN_DIRECT authentication failed: {ex.Message}");
                }
                throw;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _httpClient?.Dispose();
                }
                _disposed = true;
            }
        }
    }
}
