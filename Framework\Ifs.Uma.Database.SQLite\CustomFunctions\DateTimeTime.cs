﻿using System;
using Ifs.Uma.Utility;
using SQLitePCL;

namespace Ifs.Uma.Database.SQLite.CustomFunctions
{
    internal sealed class DateTimeTime : SQLiteCustomFunction
    {
        public const string FunctionName = "ifs_datetime_time";

        public DateTimeTime() 
            : base(FunctionName, true, 1)
        {
        }

        protected override void OnExecute(sqlite3_context ctx, sqlite3_value[] args)
        {
            if (raw.sqlite3_value_type(args[0]) == raw.SQLITE_NULL)
            {
                raw.sqlite3_result_null(ctx);
                return;
            }

            long dateTicks = raw.sqlite3_value_int64(args[0]);
            DateTime dateTime = new DateTime(dateTicks, DateTimeKind.Unspecified);
            dateTime = ObjectConverter.ToTime(dateTime);

            raw.sqlite3_result_int64(ctx, dateTime.Ticks);
        }
    }
}
