﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements
{
    public abstract class ElementBase : ObservableBase, IDisposable
    {
        private const string SettingsIsCollapsed = "IsCollapsed";

        public ArrangeLocation Location { get; private set; } = new ArrangeLocation();
        public bool HasArrange { get; set; } = false;
        public bool InProcessViewer { get; set; } = false;
        public bool InRepeatingSection { get; set; } = false;
        public CpiElementContent Content { get; private set; }

        private string _projectionName;
        public string ProjectionName
        {
            get => _projectionName;
            private set
            {
                SetProperty(ref _projectionName, value);
            }
        }

        private PageData _pageData;
        public PageData PageData
        {
            get => _pageData;
            set
            {
                if (SetProperty(ref _pageData, value))
                {
                    PageData oldValue = _pageData;
                    OnPageDataChanged(oldValue, _pageData);
                }
            }
        }

        private ViewData _viewData;
        public ViewData ViewData
        {
            get => _viewData;
            private set
            {
                ViewData oldValue = _viewData;
                if (SetProperty(ref _viewData, value))
                {
                    OnViewDataChanged(oldValue, _viewData);
                    OnPropertyChanged(nameof(Record));
                }
            }
        }

        public RecordData Record => _viewData?.Record;

        private EntityDataSource _dataSource;
        public EntityDataSource DataSource
        {
            get => _dataSource;
            private set => SetProperty(ref _dataSource, value);
        }

        private string _header;
        public string Header
        {
            get => _header;
            private set
            {
                SetProperty(ref _header, value);
                OnPropertyChanged(nameof(HasHeader));
            }
        }

        private bool _hasHeader = true;
        public bool HasHeader
        {
            get => _hasHeader && !string.IsNullOrEmpty(Header);
            set => SetProperty(ref _hasHeader, value);
        }

        private string _label;
        protected string Label
        {
            get => _label;
            set
            {
                if (SetProperty(ref _label, value))
                {
                    UpdateHeader();
                }
            }
        }

        private bool _isVisible;
        public bool IsVisible
        {
            get => _isVisible;
            private set
            {
                if (_isVisible != value)
                {
                    _isVisible = value;
                    OnPropertyChanged(nameof(IsVisible));
                }
            }
        }

        private bool _isSystemModifyingCollapse = false;
        private bool _isCollapsed = true;
        private bool _userIsExpanding = false;
        public bool IsCollapsed
        {
            get => _isCollapsed;
            set
            {
                _userIsExpanding = _isSystemModifyingCollapse ? false : !value;

                if (SetProperty(ref _isCollapsed, value))
                {
                    // need to pass _isSystemModifyingCollapse value, as this could change later in the thread before this line is called
                    OnIsCollapsedChanged(_isSystemModifyingCollapse);
                }
                _userIsExpanding = false;
            }
        }

        private bool _hasLoaded;
        public bool HasLoaded
        {
            get => _hasLoaded;
            private set => SetProperty(ref _hasLoaded, value);
        }

        private ElementDisplayState _displayState;

        /// <summary>
        /// Certain elements can be shown in different states. For example, a ListElement can be
        /// shown as a List Button (Normal), a full list which shares the screen with other
        /// elements like a standard list screen (Expanded), or a full list shown on its own
        /// after its List Button has been clicked (FullScreen)
        /// </summary>
        public ElementDisplayState DisplayState
        {
            get => _displayState;
            set
            {
                if (SetProperty(ref _displayState, value))
                {
                    OnDisplayStateChanged();
                }
            }
        }

        private bool _alwaysLoad;
        public bool AlwaysLoad
        {
            get => _alwaysLoad;
            protected set
            {
                if (SetProperty(ref _alwaysLoad, value) && PageData != null)
                {
                    Load();
                }
            }
        }

        private bool _loadsWhenHidden;
        public bool LoadsWhenHidden
        {
            get => _loadsWhenHidden;
            set => SetProperty(ref _loadsWhenHidden, value);
        }

        public bool IsDefaultViewData
        {
            get
            {
                return ViewData == PageData.DefaultViewData;
            }
        }

        protected abstract BindingType BindingPropertyType { get; }

        protected enum BindingType
        {
            None,
            Array,
            Reference
        }

        protected IExpressionRunner ExpressionRunner { get; private set; }
        protected ICommandExecutor CommandExecutor { get; private set; }

        public ElementList Elements { get; internal set; }
        public UpdatingState UpdatingState { get; } = new UpdatingState();

        private IMetadata _metadata;
        private ILogger _logger;
        private IDialogService _dialogService;

        private readonly Dictionary<string, AttributeValue> _attributes = new Dictionary<string, AttributeValue>();

        private bool _disposed = false;

        public bool Initialize(IMetadata metadata, IExpressionRunner expressionRunner, ICommandExecutor commandExecutor, ILogger logger, IDialogService dialogService, string projectionName, CpiElementContent content)
        {
            _metadata = metadata;
            ExpressionRunner = expressionRunner;
            CommandExecutor = commandExecutor;
            _logger = logger;
            _dialogService = dialogService;
            ProjectionName = projectionName;
            Content = content;
            return OnInitialize();
        }

        protected virtual bool OnInitialize()
        {
            return true;
        }

        protected virtual void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            if (oldValue?.ViewState != null)
            {
                oldValue.ViewState.ViewStateChanged -= OnViewStateChanged;
                oldValue.PropertyChanged -= PageData_PropertyChanged;
            }

            if (newValue?.ViewState != null)
            {
                newValue.ViewState.ViewStateChanged += OnViewStateChanged;
                newValue.PropertyChanged += PageData_PropertyChanged;
            }

            if (newValue == null)
            {
                ViewData = null;
            }
            else
            {
                ViewData viewData = newValue.DefaultViewData;

                if (Content?.Binding?.BindName != null && !InProcessViewer)
                {
                    viewData = newValue.GetViewData(Content.Binding.BindName);
                }

                if (BindingPropertyType == BindingType.Reference && Content?.Binding?.Property != null)
                {
                    viewData = viewData.GetRefViewData(Content.Binding.Property);
                }

                ViewData = viewData;
            }

            OnPropertyChanged(nameof(IsDefaultViewData));

            if (newValue != null && AlwaysLoad)
            {
                Load();
            }
        }

        private void PageData_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PageData.EditingViewData))
            {
                DataChanged();
            }
        }

        private void OnViewStateChanged(object sender, EventArgs e)
        {
            DataChanged();
        }

        protected virtual void OnViewDataChanged(ViewData oldValue, ViewData newValue)
        {
            if (oldValue?.Record != null)
            {
                oldValue.Record.RecordLoaded -= Record_RecordLoaded;
                oldValue.Record.DataChanged -= Record_DataChanged;
            }

            if (newValue?.Record != null)
            {
                newValue.Record.DataChanged += Record_DataChanged;
                newValue.Record.RecordLoaded += Record_RecordLoaded;
            }

            foreach (var attribute in _attributes)
            {
                attribute.Value.Record = newValue?.Record;
            }

            RecordLoaded();
        }

        private void Record_RecordLoaded(object sender, EventArgs e)
        {
            RecordLoaded();
        }

        private void RecordLoaded()
        {
            if (HasLoaded)
            {
                UpdateDataSource();
                OnRecordLoaded();
            }

            DataChanged();
        }

        protected virtual void OnRecordLoaded()
        {
        }

        private void Record_DataChanged(object sender, EventArgs e)
        {
            RecordDataChanged();
        }

        private void RecordDataChanged()
        {
            if (HasLoaded)
            {
                UpdateDataSource();
                OnRecordDataChanged();
            }

            DataChanged();
        }

        protected virtual void OnRecordDataChanged()
        {
        }

        protected virtual void OnIsCollapsedChanged(bool isSystemCall)
        {
            if ((AlwaysLoad || !IsCollapsed) && (IsVisible || LoadsWhenHidden) && !HasLoaded)
            {
                Load();
            }

            // We only want to save the state if IsCollapsed change by UI, not a system process
            if (!isSystemCall)
            {
                GetSettings().Set(SettingsIsCollapsed, IsCollapsed);
            }
        }

        protected virtual void OnDisplayStateChanged()
        {
        }

        private void Load()
        {
            if (!HasLoaded)
            {
                HasLoaded = OnLoad();

                if (HasLoaded)
                {
                    RecordLoaded();
                }
            }
        }

        protected abstract bool OnLoad();

        public void ReloadData()
        {
            OnReloadData();
        }

        protected virtual void OnReloadData()
        {
        }

        protected void DataChanged()
        {
            IsVisible = CalculateIsVisible();

            if (IsVisible)
            {
                bool oldHasLoaded = HasLoaded;
                if (!_userIsExpanding)
                {
                    string settingsIsCollapsed = GetSettings().Get(SettingsIsCollapsed);

                    _isSystemModifyingCollapse = true;
                    if (Content?.Override?.Collapsed != null)
                    {
                        IsCollapsed = RunOverrideCheck(Content.Override.Collapsed, false);
                    }
                    else if (!string.IsNullOrWhiteSpace(settingsIsCollapsed) && bool.TryParse(settingsIsCollapsed, out bool result))
                    {
                        IsCollapsed = result;
                    }
                    else
                    {
                        IsCollapsed = false;
                    }
                    _isSystemModifyingCollapse = false;
                }

                // States will have already been updated if we loaded
                // while changing IsCollapsed
                bool didLoad = oldHasLoaded != HasLoaded;
                if (HasLoaded && !didLoad)
                {
                    OnDataChanged();
                }
            }

            UpdateHeader();
        }

        private void UpdateHeader()
        {
            string label = Content?.Override?.Label ?? Label;
            Header = InterpolateString(label, ViewData?.Record);
        }

        protected virtual bool CalculateIsVisible()
        {
            return RunOverrideCheck(Content?.Override?.OfflineVisible ?? Content?.Override?.Visible, true);
        }

        private void UpdateDataSource()
        {
            if (!HasLoaded) return;

            EntityDataSource dataSource = GetDataSource();
            if (!EntityDataSource.IsTheSame(dataSource, DataSource))
            {
                DataSource = dataSource;
                OnDataSourceChanged();
            }
        }

        protected virtual void OnDataSourceChanged()
        {
        }

        private EntityDataSource GetDataSource()
        {
            EntityDataSource dataSource = PageData?.DataSource;

            if (!string.IsNullOrEmpty(Content?.DatasourceFunction))
            {
                Dictionary<string, object> parameterValues = new Dictionary<string, object>();

                if (Record?.IsEmpty() == false)
                {
                    Record.ExtractFunctionParameters(Content.DatasourceFunctionParams, parameterValues);

                    if (RecordData.HasLoadedRecord(Record) && parameterValues.Count != Content.DatasourceFunctionParams.Count)
                    {
                        _logger.Error("Could not read parameters for function '{0}' properly from record of type '{1}'", Content.DatasourceFunction, Record?.EntityName);
                    }
                }

                return FunctionDataSource.Create(_metadata, Content.DatasourceProjection ?? ProjectionName, Content.DatasourceFunction, parameterValues);
            }

            if (!string.IsNullOrEmpty(Content?.DatasourceEntitySet))
            {
                dataSource = EntityDataSource.FromEntitySet(_metadata, Content.DatasourceProjection ?? ProjectionName, Content.DatasourceEntitySet);
            }

            if (BindingPropertyType == BindingType.Array && Content?.Binding?.Property != null)
            {
                dataSource = Record?.GetArrayDataSource(Content.Binding.Property);

                if (dataSource != null && dataSource.EntitySetName == null)
                {
                    dataSource.EntitySetName = PageData?.DataSource?.EntitySetName;
                }
            }

            return dataSource;
        }

        protected virtual void OnDataChanged()
        {
        }

        internal void NotifyStoredDataChanged(DataChangeSet changeSet)
        {
            if (HasLoaded)
            {
                OnStoredDataChanged(changeSet);
            }
        }

        protected virtual void OnStoredDataChanged(DataChangeSet changeSet)
        {
        }

        protected bool CanEdit()
        {
            if (!RecordData.HasLoadedRecord(ViewData?.Record))
            {
                return false;
            }

            if (!ViewData.CanEdit)
            {
                return false;
            }

            if (PageData?.EditingViewData != null && PageData.EditingViewData != ViewData)
            {
                return false;
            }

            if (!IsDefaultViewData && RecordData.HasLoadedNewRecord(ViewData.Record))
            {
                return false;
            }

            CpiExpression pageDataEnabled = PageData?.CrudActions?.Edit?.OfflineEnabled ?? PageData?.CrudActions?.Edit?.Enabled;
            if (pageDataEnabled != null && !ExpressionRunner.RunCheck(pageDataEnabled, PageData.DefaultViewData, true))
            {
                return false;
            }

            CpiExpression viewDataEnabled = ViewData?.CrudActions?.Edit?.OfflineEnabled ?? ViewData?.CrudActions?.Edit?.Enabled;
            if (viewDataEnabled != null && !ExpressionRunner.RunCheck(viewDataEnabled, ViewData, true))
            {
                return false;
            }

            return true;
        }

        private bool RunOverrideCheck(CpiExpression check, bool nullValue)
        {
            return ExpressionRunner.RunCheck(check, ViewData?.Parent ?? ViewData, nullValue);
        }

        protected bool RunCheck(CpiExpression check, bool nullValue)
        {
            return ExpressionRunner.RunCheck(check, ViewData, nullValue);
        }

        protected string InterpolateString(string str)
        {
            return InterpolateString(str, Record);
        }

        protected string InterpolateString(string str, RecordData record)
        {
            if (str == null)
            {
                return null;
            }

            return ExpressionRunner.InterpolateString(str, record);
        }

        protected void ExecuteBackgroundCommand(CpiCommand command)
        {
            CommandExecutor.GetStates(ProjectionName, ViewData, command, true, out bool isVisible, out bool isEnabled);

            if (isVisible && isEnabled)
            {
                Task commandTask = CommandExecutor.ExecuteAsync(ProjectionName, ViewData, command);
                ExecuteBackgroundTask(commandTask);
            }
        }

        protected void ExecuteBackgroundTask(Task task)
        {
            PageData?.BackgroundTasks.Add(task);
        }

        public async Task<bool> ValidateAsync()
        {
            if (!IsVisible || !HasLoaded)
            {
                return true;
            }

            ClearValidations();

            bool result = await OnValidateAsync();

            if (!result)
            {
                //Open element if it contains validation errors
                _isSystemModifyingCollapse = true;
                IsCollapsed = false;
                _isSystemModifyingCollapse = false;
            }

            return result;
        }

        protected virtual Task<bool> OnValidateAsync()
        {
            return Task.FromResult(OnValidate());
        }

        protected virtual bool OnValidate()
        {
            return true;
        }

        public void ClearValidations()
        {
            if (HasLoaded)
            {
                OnClearValidations();
            }
        }

        protected virtual void OnClearValidations()
        {
        }

        public virtual bool NotifyFieldInvalid(string fieldName, string message)
        {
            if (HasLoaded)
            {
                return OnNotifyFieldInvalid(fieldName, message);
            }

            return false;
        }

        protected virtual bool OnNotifyFieldInvalid(string fieldName, string message)
        {
            return false;
        }

        public AttributeValue GetAttribute(string attributePath)
        {
            if (attributePath == null) throw new ArgumentNullException(nameof(attributePath));

            AttributeValue attrib;
            if (!_attributes.TryGetValue(attributePath, out attrib))
            {
                AttributePath path = AttributePath.Create(attributePath);
                attrib = path == null ? null : new AttributeValue(path, Record);
                _attributes[attributePath] = attrib;
            }

            return attrib;
        }

        public virtual void GetSelectAttributes(ICollection<string> attributes)
        {
            if (Content?.Override != null)
            {
                AttributeFinder.FindInExpression(attributes, ProjectionName, Content.Override.OfflineVisible ?? Content.Override.Visible);
                AttributeFinder.FindInExpression(attributes, ProjectionName, Content.Override.Collapsed);
            }

            if (BindingPropertyType == BindingType.Reference && Content?.Binding?.Property != null)
            {
                HashSet<string> subAttributes = new HashSet<string>();

                AttributeFinder.FindInLabel(subAttributes, Content?.Override?.Label);
                OnGetSelectAttributes(subAttributes);

                foreach (string attribute in subAttributes)
                {
                    attributes.Add(Content.Binding.Property + "." + attribute);
                }
            }
            else
            {
                AttributeFinder.FindInLabel(attributes, Content?.Override?.Label);
                OnGetSelectAttributes(attributes);
            }
        }

        [Obsolete("This method should never be used. Only the ElementBase itself should be modifying the IsCollapsed property.")]
        protected void SetIsCollapsed(bool isCollapsed)
        {
            _isSystemModifyingCollapse = true;
            IsCollapsed = isCollapsed;
            _isSystemModifyingCollapse = false;
        }

        protected virtual void OnGetSelectAttributes(ICollection<string> attributes)
        {
        }

        internal async Task HandleException(Exception ex)
        {
            _logger.HandleException(ExceptionType.Unexpected, ex);
            await _dialogService.ShowException(ex, title: Header);
        }

        internal async Task<bool> CheckExecuteResult(ExecuteResult result, bool notifyOffline = true)
        {
            if (result.Failed)
            {
                if (result.IsOffline)
                {
                    if (notifyOffline)
                    {
                        await _dialogService.Alert(string.Empty, Strings.YouMustBeOnline);
                    }
                }
                else if (result.Exception?.Message != null)
                {
                    await _dialogService.ShowException(result.Exception, title: Header);
                }

                return false;
            }

            return true;
        }

        public ISettings GetSettings()
        {
            if (PageData?.PageSettings != null && Content?.Id != null)
            {
                return PageData.PageSettings.GetSubGroup(Content.Id);
            }

            return new InMemorySettings();
        }

        #region IDisposable Support

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
            {
                return;
            }

            if (disposing)
            {
                // Unsubscribe from events
                if (_viewData?.Record != null)
                {
                    _viewData.Record.RecordLoaded -= Record_RecordLoaded;
                    _viewData.Record.DataChanged -= Record_DataChanged;
                }

                if (_pageData?.ViewState != null)
                {
                    _pageData.ViewState.ViewStateChanged -= OnViewStateChanged;
                    _pageData.PropertyChanged -= PageData_PropertyChanged;
                }
            }

            _disposed = true;
        }

        // Ensure that Dispose is called when the object is finalized
        ~ElementBase()
        {
            Dispose();
        }

        #endregion
    }

    public enum ElementDisplayState
    {
        Normal,
        Expanded,
        FullScreen
    }

    public class ArrangeLocation
    {
        public int Row { get; set; }
        public int Column { get; set; }
        public bool FullWidth { get; set; } = true;
    }
}
