& .\_UpdateChangeLog.ps1
if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ CommitAndPushChangeLog"

$gitStatus = (git status "$documentationDir" --porcelain) | Out-String

if (![String]::IsNullOrWhiteSpace($gitStatus)) {
    git add -A "$documentationDir"
    git commit -m ("Update Change Log " + $appVersion)
    git push origin HEAD
} else {
    Write-Output "Nothing to commit"
}

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End -  CommitAndPushChangeLog"