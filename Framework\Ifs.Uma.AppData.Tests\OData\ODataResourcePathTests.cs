﻿using Ifs.Uma.Metadata.OData;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.OData
{
    [TestFixture]
    public class ODataResourcePathTests
    {
        [Test]
        public void MultiSegment()
        {
            ODataResourcePath path = new ODataResourcePath("OData.svc/Company('COM1')/Products(ProdId=55)/Image");

            Assert.AreEqual(4, path.Segments.Count);

            ODataResourcePathSegment segment = path.Segments[0];
            Assert.AreEqual("OData.svc", segment.ResourceName);
            Assert.AreEqual(0, segment.ResourceKey.Count);

            segment = path.Segments[1];
            Assert.AreEqual("Company", segment.ResourceName);
            Assert.AreEqual(1, segment.ResourceKey.Count);
            Assert.AreEqual(null, segment.ResourceKey[0].Key);
            Assert.AreEqual("COM1", segment.ResourceKey[0].Value);

            segment = path.Segments[2];
            Assert.AreEqual("Products", segment.ResourceName);
            Assert.AreEqual(1, segment.ResourceKey.Count);
            Assert.AreEqual("ProdId", segment.ResourceKey[0].Key);
            Assert.AreEqual(55, segment.ResourceKey[0].Value);

            segment = path.Segments[3];
            Assert.AreEqual("Image", segment.ResourceName);
            Assert.AreEqual(0, segment.ResourceKey.Count);
        }

        [Test]
        public void NoKey()
        {
            ODataResourcePath path = new ODataResourcePath("ResourceA");
            Assert.AreEqual(1, path.Segments.Count);
            Assert.AreEqual("ResourceA", path.Segments[0].ResourceName);
            Assert.AreEqual(0, path.Segments[0].ResourceKey.Count);
        }

        [Test]
        public void KeyMulti()
        {
            ODataResourcePath path = new ODataResourcePath("ResourceA(K1='test',K2=45)");
            Assert.AreEqual(1, path.Segments.Count);
            Assert.AreEqual("ResourceA", path.Segments[0].ResourceName);
            Assert.AreEqual(2, path.Segments[0].ResourceKey.Count);
            Assert.AreEqual("K1", path.Segments[0].ResourceKey[0].Key);
            Assert.AreEqual("test", path.Segments[0].ResourceKey[0].Value);
            Assert.AreEqual("K2", path.Segments[0].ResourceKey[1].Key);
            Assert.AreEqual(45, path.Segments[0].ResourceKey[1].Value);
        }

        [Test]
        public void NoKeyName()
        {
            ODataResourcePath path = new ODataResourcePath("ResourceA('test', 55)");
            Assert.AreEqual(1, path.Segments.Count);
            Assert.AreEqual("ResourceA", path.Segments[0].ResourceName);
            Assert.AreEqual(2, path.Segments[0].ResourceKey.Count);
            Assert.AreEqual(null, path.Segments[0].ResourceKey[0].Key);
            Assert.AreEqual("test", path.Segments[0].ResourceKey[0].Value);
            Assert.AreEqual(null, path.Segments[0].ResourceKey[1].Key);
            Assert.AreEqual(55, path.Segments[0].ResourceKey[1].Value);
        }

        [Test]
        public void NoKeyNameEqualsInString()
        {
            ODataResourcePath path = new ODataResourcePath("ResourceA('sda=dsda')");
            Assert.AreEqual(1, path.Segments.Count);
            Assert.AreEqual("ResourceA", path.Segments[0].ResourceName);
            Assert.AreEqual(1, path.Segments[0].ResourceKey.Count);
            Assert.AreEqual(null, path.Segments[0].ResourceKey[0].Key);
            Assert.AreEqual("sda=dsda", path.Segments[0].ResourceKey[0].Value);
        }

        [Test]
        public void EscapedString()
        {
            ODataResourcePath path = new ODataResourcePath("ResourceA(P1='abc''def')");
            Assert.AreEqual(1, path.Segments.Count);
            Assert.AreEqual("ResourceA", path.Segments[0].ResourceName);
            Assert.AreEqual(1, path.Segments[0].ResourceKey.Count);
            Assert.AreEqual("P1", path.Segments[0].ResourceKey[0].Key);
            Assert.AreEqual("abc'def", path.Segments[0].ResourceKey[0].Value);
        }
    }
}
