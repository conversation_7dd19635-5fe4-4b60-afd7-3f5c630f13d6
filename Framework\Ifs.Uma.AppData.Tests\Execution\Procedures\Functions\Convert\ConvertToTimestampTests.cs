﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.Convert
{
    [TestFixture]
    public class ConvertToTimestampTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.Convert.ConvertToTimestampTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("2018-07-12 11:21:00", ExpectedResult = null)]
        [TestCase("2018/07/12 11:21:00", ExpectedResult = null)]
        [TestCase(null, ExpectedResult = null)]
        public async Task<DateTime?> Convert_ToTimestamp_IncorrectFormat(string dateTimeInput)
        {
            _params["TextInput"] = dateTimeInput;
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToTimestamp", _params);
            CheckResults(result);
            return result.Value as DateTime?;
        }

        [Test]
        public async Task Convert_ToTimestamp_CorrectFormat()
        {
            ExecuteResult result;

            DateTime expectedValue = new DateTime(2018, 07, 12, 11, 21, 0, DateTimeKind.Unspecified);

            _params["TextInput"] = "2018-07-12T11:21:00";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToTimestamp", _params);
            CheckResults(result);

            Assert.AreEqual(result.Value, expectedValue);

            _params["TextInput"] = "2018-07-12T11:21:00.0000000";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToTimestamp", _params);
            CheckResults(result);

            Assert.AreEqual(result.Value, expectedValue);
        }

        [Test]
        public async Task Convert_ToTimestampBySet()
        {
            ExecuteResult result;

            DateTime expectedValue = new DateTime(2018, 07, 12, 11, 21, 0, DateTimeKind.Unspecified);

            _params["TextInput"] = "2018-07-12T11:21:00";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToTimestampBySet", _params);
            CheckResults(result);

            Assert.AreEqual(result.Value, expectedValue);

            _params["TextInput"] = "2018-07-12T11:21:00.0000000";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToTimestampBySet", _params);
            CheckResults(result);

            Assert.AreEqual(result.Value, expectedValue);
        }

        private static void CheckResults(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
