﻿using Ifs.Uma.Database;
using NUnit.Framework;
using System.Linq;

namespace Ifs.Uma.Data.Tests
{
    [TestFixture]
    public class DbRowHandlerTests : DataContextTest<TestDataContext>
    {
        [Test]
        public void Insert()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.MyId = "id_A";
                row.Name = "A";
                rowHandler.InsertRow(row);
            });

            SyncedRow savedRow = dc.SyncedRows.SingleOrDefault();
            Assert.IsNotNull(savedRow, "Row not inserted");
            Assert.AreEqual("id_A", savedRow.MyId);
            Assert.AreEqual("A", savedRow.Name);
        }

        [Test]
        public void InsertMisingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.Name = "A";
                
                CheckKeyValueNotFound(() => rowHandler.InsertRow(row));
            });
        }

        [Test]
        public void InsertSecondMisingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow first = new SyncedRow();
                first.MyId = "id_First";
                first.Name = "First";
                rowHandler.InsertRow(first);

                SyncedRow second = new SyncedRow();
                second.Name = "Second";

                CheckKeyValueNotFound(() => rowHandler.InsertRow(second));
            });
        }

        [Test]
        public void Update()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.MyId = "id_A";
                row.Name = "A";
                rowHandler.InsertRow(row);

                row.Name = "B";
                rowHandler.UpdateRow(row);
            });

            SyncedRow savedRow = dc.SyncedRows.SingleOrDefault();
            Assert.IsNotNull(savedRow, "Row not inserted");
            Assert.AreEqual("B", savedRow.Name);
        }

        [Test]
        public void UpdatesMissingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.Name = "A";

                CheckKeyValueNotFound(() => rowHandler.UpdateRow(row));
            });
        }
        
        [Test]
        public void UpdateSecondMisingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow first = new SyncedRow();
                first.MyId = "id_First";
                first.Name = "First";
                rowHandler.InsertRow(first);
                
                first.Name = "First Up2";
                rowHandler.UpdateRow(first);

                SyncedRow second = new SyncedRow();
                second.Name = "Second";

                CheckKeyValueNotFound(() => rowHandler.UpdateRow(second));
            });
        }

        [Test]
        public void Upsert()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.MyId = "id_A";
                row.Name = "A";
                rowHandler.UpsertRow(row);

                SyncedRow savedRow = dc.SyncedRows.SingleOrDefault();
                Assert.IsNotNull(savedRow, "Row not inserted");
                Assert.AreEqual("A", savedRow.Name);

                row.Name = "B";
                rowHandler.UpsertRow(row);

                savedRow = dc.SyncedRows.Single();
                Assert.AreEqual("B", savedRow.Name, "Row not updated");
            });
        }

        [Test]
        public void UpsertMissingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.Name = "A";

                CheckKeyValueNotFound(() => rowHandler.UpsertRow(row));
            });
        }

        [Test]
        public void UpsertSecondMisingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow first = new SyncedRow();
                first.MyId = "id_First";
                first.Name = "First";
                rowHandler.UpsertRow(first);
                
                SyncedRow second = new SyncedRow();
                second.Name = "Second";

                CheckKeyValueNotFound(() => rowHandler.UpsertRow(second));
            });
        }

        [Test]
        public void Delete()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.MyId = "id_A";
                row.Name = "A";
                rowHandler.InsertRow(row);

                SyncedRow savedRow = dc.SyncedRows.SingleOrDefault();
                Assert.IsNotNull(savedRow, "Row not inserted");

                rowHandler.DeleteRow(savedRow);

                savedRow = dc.SyncedRows.SingleOrDefault();
                Assert.IsNull(savedRow, "Row not deleted");
            });
        }

        [Test]
        public void DeleteMissingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.Name = "A";

                CheckKeyValueNotFound(() => rowHandler.DeleteRow(row));
            });
        }

        [Test]
        public void DeleteSecondMisingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow first = new SyncedRow();
                first.MyId = "id_First";
                first.Name = "First";
                rowHandler.InsertRow(first);

                rowHandler.DeleteRow(first);

                SyncedRow second = new SyncedRow();
                second.Name = "Second";

                CheckKeyValueNotFound(() => rowHandler.DeleteRow(second));
            });
        }

        [Test]
        public void Exists()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.MyId = "id_A";
                row.Name = "A";

                bool exists = rowHandler.Exists(row);
                Assert.IsFalse(exists, "Row has not yes been inserted");

                rowHandler.InsertRow(row);

                exists = rowHandler.Exists(row);
                Assert.IsTrue(exists, "Row should exist");

                exists = rowHandler.Exists(row);
                Assert.IsTrue(exists, "Row should exist second");
            });
        }

        [Test]
        public void ExistsMissingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow row = new SyncedRow();
                row.Name = "A";

                CheckKeyValueNotFound(() => rowHandler.Exists(row));
            });
        }

        [Test]
        public void ExistsSecondMisingKey()
        {
            TestDataContext dc = CreateDataContext();

            dc.ExecuteInTransaction((cmd) =>
            {
                IDbRowHandler<SyncedRow> rowHandler = dc.CreateDbRowHandler<SyncedRow>(cmd);

                SyncedRow first = new SyncedRow();
                first.MyId = "id_First";
                first.Name = "First";
                rowHandler.Exists(first);
                
                SyncedRow second = new SyncedRow();
                second.Name = "Second";

                CheckKeyValueNotFound(() => rowHandler.Exists(second));
            });
        }

        private static void CheckKeyValueNotFound(TestDelegate code)
        {
            DbException exception = Assert.Throws<DbException>(code);
            Assert.AreEqual(DbExceptionType.KeyValueNotFound, exception.ExceptionType);
            Assert.AreEqual("Key 'synced.my_id' requires a value", exception.Message);
        }
    }
}
