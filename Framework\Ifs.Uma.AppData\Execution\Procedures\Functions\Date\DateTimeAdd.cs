﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Date
{
    internal abstract class DateTimeAddInteger : DateTimeFunction
    {
        public DateTimeAddInteger(string name) 
            : base(name, 2)
        {
        }

        protected sealed override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime? ts = parameters[0].GetTimestamp();

            if (!ts.HasValue)
            {
                return null;
            }

            long? value = parameters[1].GetInteger();

            if (!value.HasValue)
            {
                return ts.Value;
            }

            return Add(ts.Value, (int)value.Value);
        }

        protected abstract object Add(DateTime ts, int value);
    }

    internal abstract class DateTimeAddNumber : DateTimeFunction
    {
        public DateTimeAddNumber(string name)
            : base(name, 2)
        {
        }

        protected sealed override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime? ts = parameters[0].GetTimestamp();

            if (!ts.HasValue)
            {
                return null;
            }

            double? value = parameters[1].GetNumber();

            if (!value.HasValue)
            {
                return ts.Value;
            }

            return Add(ts.Value, value.Value);
        }

        protected abstract object Add(DateTime ts, double value);
    }

    internal sealed class DateTimeAddYears : DateTimeAddInteger
    {
        public const string FunctionName = "AddYears";

        public DateTimeAddYears()
            : base(FunctionName)
        {
        }

        protected override object Add(DateTime ts, int value)
        {
            return ts.AddYears(value);
        }
    }

    internal sealed class DateTimeAddMonths : DateTimeAddInteger
    {
        public const string FunctionName = "AddMonths";

        public DateTimeAddMonths()
            : base(FunctionName)
        {
        }

        protected override object Add(DateTime ts, int value)
        {
            return ts.AddMonths(value);
        }
    }

    internal sealed class DateTimeAddDays : DateTimeAddNumber
    {
        public const string FunctionName = "AddDays";

        public DateTimeAddDays()
            : base(FunctionName)
        {
        }

        protected override object Add(DateTime ts, double value)
        {
            return ts.AddDays(value);
        }
    }

    internal sealed class DateTimeAddHours : DateTimeAddNumber
    {
        public const string FunctionName = "AddHours";

        public DateTimeAddHours()
            : base(FunctionName)
        {
        }

        protected override object Add(DateTime ts, double value)
        {
            return ts.AddHours(value);
        }
    }

    internal sealed class DateTimeAddMinutes : DateTimeAddNumber
    {
        public const string FunctionName = "AddMinutes";

        public DateTimeAddMinutes()
            : base(FunctionName)
        {
        }

        protected override object Add(DateTime ts, double value)
        {
            return ts.AddMinutes(value);
        }
    }

    internal sealed class DateTimeAddSeconds : DateTimeAddInteger
    {
        public const string FunctionName = "AddSeconds";

        public DateTimeAddSeconds()
            : base(FunctionName)
        {
        }

        protected override object Add(DateTime ts, int value)
        {
            return ts.AddSeconds(value);
        }
    }
}
