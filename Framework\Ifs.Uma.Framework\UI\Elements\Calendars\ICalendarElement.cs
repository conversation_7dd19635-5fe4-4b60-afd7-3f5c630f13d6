﻿using System.Collections.Generic;
using Ifs.Uma.Framework.UI.Cards;

namespace Ifs.Uma.Framework.UI.Elements.Calendars
{
    public interface ICalendarElement
    {
        bool CardPanelIsOpen { get; }
        bool CalendarIsLoaded { get; set; }
        void ShowCalendar(List<CalendarEvent> events);
        void AddResourceSegments(List<CalendarResource> resources);
        void ShowCardPanel();
        void CloseCardPanel();
        void FocusCalendarEvent(CalendarEvent calendarEvent);
        void ReloadAppointments(List<CalendarEvent> events, bool allAppointments = false, bool applyFilter = false);
        void RefreshCardData(CardDef cardDef);
    }
}
