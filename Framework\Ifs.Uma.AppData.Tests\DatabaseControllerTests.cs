﻿using System;
using System.Linq;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Tests.TestClasses;
using Ifs.Uma.Utility;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class DatabaseControllerTests
    {
        [SetUp]
        protected virtual void BeforeTest()
        {
            PlatformServicesTest.Initialize();
        }

        [Test]
        public void RecreateAfterBadCredentials()
        {
            IDatabaseController db = new DatabaseController<FwDataContext>(new TestLogger(OnErrorLogged));
            try
            {
                db.CreateDatabaseForUser(1, "USER1", "USER1", null);
                db.Disconnect();

                bool didLogin = db.Login(1, "USER1", "BADPASS");
                Assert.IsFalse(didLogin);
                Assert.AreEqual(-1, db.ConnectedDatabaseId);

                db.DeleteDatabase(1);
                Assert.AreEqual(-1, db.ConnectedDatabaseId);

                db.CreateDatabaseForUser(1, "USER2", "USER2", null);
                db.Disconnect();
                Assert.AreEqual(-1, db.ConnectedDatabaseId);

                didLogin = db.Login(1, "USER2", "USER2");
                Assert.IsTrue(didLogin);
                Assert.AreEqual(1, db.ConnectedDatabaseId);
            }
            finally
            {
                CleanupDatabaseController(db);
            }
        }

        [Test]
        public void CheckPassword()
        {
            IDatabaseController db = new DatabaseController<FwDataContext>(new TestLogger(OnErrorLogged));
            try
            {
                db.CreateDatabaseForUser(1, "USER1", "USER1", null);
                db.Disconnect();

                bool didLogin = db.Login(1, "USER1", "BADPASS");
                Assert.IsFalse(didLogin);
                Assert.AreEqual(-1, db.ConnectedDatabaseId);

                didLogin = db.Login(1, "USER1", "USER1");
                Assert.IsTrue(didLogin);
                Assert.AreEqual(1, db.ConnectedDatabaseId);

                FwDataContext ctx = db.CreateDataContext();
                Assert.IsNotNull(ctx, "Failed to create data context");

                DatabaseInfo dbInfo = ctx.DatabaseInfos.FirstOrDefault();
                Assert.IsNotNull(dbInfo, "Failed to get dbInfo");
            }
            finally
            {
                CleanupDatabaseController(db);
            }
        }

        private static void CleanupDatabaseController(IDatabaseController db)
        {
            try
            {
                db.Disconnect();
                db.DeleteDatabase(1);
            }
            catch
            {
            }
        }

        private void OnErrorLogged(string message)
        {
            if (!message.Contains("file is not a database"))
            {
                Assert.Fail(message);
            }
        }
    }
}
