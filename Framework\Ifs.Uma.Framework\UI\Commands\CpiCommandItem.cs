using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Commands
{
    public class CpiCommandItem : FwCommandItem
    {
        private bool _visibleWhenDisabled = true;
        public bool VisibleWhenDisabled
        {
            get => _visibleWhenDisabled;
            set => SetProperty(ref _visibleWhenDisabled, value);
        }

        private readonly ICommandExecutor _commandExecutor;
        private readonly IExpressionRunner _expressionRunner;
        private readonly CpiCommand _command;
        private readonly IDataHandler _dataHandler;
        private readonly IMetadata _metadata;

        public string Name { get => _command.Name; }

        public CpiCommandItem(ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, string projectionName, CpiCommand command)
        {
            if (projectionName == null) throw new ArgumentNullException(nameof(projectionName));
            if (commandExecutor == null) throw new ArgumentNullException(nameof(commandExecutor));
            if (command == null) throw new ArgumentNullException(nameof(command));

            ProjectionName = projectionName;
            _commandExecutor = commandExecutor;
            _expressionRunner = expressionRunner;
            _command = command;
            _dataHandler = Resolver.Resolve<IDataHandler>();
            _metadata = Resolver.Resolve<IMetadata>();

            BindingName = command.Binding?.BindName;
            Text = _command.Label;
            Icon = command.Icon == null ? null : IconUtils.Load(command.Icon);

            UpdatePrimaryCommand();

            if (_command.Style.HasValue)
            {
                ShowImage = _command.Style.Value == CpiFieldStyle.IconAndText || _command.Style.Value == CpiFieldStyle.IconOnly;
                ShowText = _command.Style.Value == CpiFieldStyle.IconAndText || _command.Style.Value == CpiFieldStyle.TextOnly
                    || (_command.Style == CpiFieldStyle.IconOnly && string.IsNullOrEmpty(_command.Icon)); // Show text if the style is IconOnly but no icon is set
            }
        }

        public CpiCommand GetCpiCommand()
        {
            return _command;
        }

        private void UpdatePrimaryCommand()
        {
            switch (_command.ShowAsAction)
            {
                case CpiCommandPriority.Never:
                    ShowAsAction = Uma.UI.Controls.CommandPriority.Never;
                    break;
                case CpiCommandPriority.IfRoom:
                default:
                    ShowAsAction = Uma.UI.Controls.CommandPriority.IfRoom;
                    break;
            }
        }

        protected override async Task OnExecuteAsync()
        {
            if (_commandExecutor != null)
            {
                await _commandExecutor.ExecuteAsync(ProjectionName, ViewData, _command);
            }
        }

        protected override void OnUpdateStates(ViewData viewData, bool isUpdating)
        {
            bool enabledOnEmpty = IsEnabledOnEmpty(viewData);
            bool enabledOnHasChanges = viewData?.CommandsEnabledOnHasChanges == true;
            bool isVisible, isEnabled;
            _commandExecutor.GetStates(ProjectionName, viewData, _command, enabledOnEmpty, out isVisible, out isEnabled);

            bool isNew = viewData?.Record?.IsNew() == true;
            bool isEmpty = viewData?.Record == null || isNew;
            bool hasChanges = viewData?.Record?.HasChanges == true || PageData?.HasChanges == true;

            if (!isEnabled && !VisibleWhenDisabled)
            {
                isVisible = false;
            }

            if (isUpdating
                || (hasChanges && !enabledOnHasChanges)
                || (isEmpty && !enabledOnEmpty))
            {
                isEnabled = viewData?.Record?.IsUsingOnlineSyncPolicy() == true;
            }

            IsVisible = isVisible;
            IsEnabled = _command?.OfflineName == null && isEnabled;

            string emphasis = _expressionRunner.GetEmphasis(_command?.OfflineEmphasis ?? _command?.Emphasis, viewData);
            EmphasisColor = UmaColor.FromEmphasis(emphasis);
            Text = _expressionRunner.InterpolateString(_command.Label, viewData?.Record);

            if (_command?.OfflineName != null && viewData != null)
            {
                _ = SetEnabledWithEnabledFunction(_command, isEnabled, isVisible, viewData);
            }
        }

        private async Task SetEnabledWithEnabledFunction(CpiCommand command, bool isEnabled, bool isVisible, ViewData viewData)
        {
            bool isEnabledFromFunction = await GetEnabledFunctionResult(command, viewData);
            IsEnabled = isEnabled && isEnabledFromFunction;
            IsVisible = isVisible && IsEnabled;
        }

        private async Task<bool> GetEnabledFunctionResult(CpiCommand cpiCommand, ViewData viewData)
        {
            Dictionary<string, object> offlineParams = cpiCommand.OfflineParams?.ToDictionary(kv => kv.Key,
                kv => (object)_expressionRunner.InterpolateString(kv.Value?.ToString(), viewData?.Record));
            ExecuteResult enabledFunctionResult = await _dataHandler.PerformFunctionAsync(ProjectionName, cpiCommand.OfflineName, offlineParams);

            if (enabledFunctionResult.Value is bool result)
            {
                return result;
            }
            else
            {
                Logger.Current.Warning("{0} Enabled Function must return a Boolean in Command {1}.", cpiCommand.OfflineName, cpiCommand.Name);
                return false;
            }
        }

        private bool IsEnabledOnEmpty(ViewData viewData)
        {
            if (_command.Selection == CpiCommandSelection.Default)
            {
                return viewData?.CommandsEnabledOnEmpty == true;
            }

            return _command.Selection == CpiCommandSelection.Global;
        }

        public override void GetSelectAttributes(ICollection<string> attributes)
        {
            base.GetSelectAttributes(attributes);

            AttributeFinder.FindInCommand(attributes, _metadata, ProjectionName, _command);
        }
    }
}
