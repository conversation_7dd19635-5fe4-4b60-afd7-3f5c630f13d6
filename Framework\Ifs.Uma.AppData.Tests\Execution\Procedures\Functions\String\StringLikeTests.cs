﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringLikeTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringLikeTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("12345", "12_45", ExpectedResult = true)]
        [TestCase("02345", "12_45", ExpectedResult = false)]
        [TestCase("023456", "%45", ExpectedResult = true)]
        [TestCase("12345", null, ExpectedResult = false)]
        [TestCase("12345", "", ExpectedResult = false)]
        [TestCase("cat house dog", "%house%", ExpectedResult = true)]
        [TestCase("cat HOUSE dog", "%house%", ExpectedResult = true)]
        [TestCase(1, "1", ExpectedResult = true)]
        [TestCase(1.1, "1.1", ExpectedResult = true)]
        [TestCase(true, "true", ExpectedResult = true)]
        [TestCase(null, null, ExpectedResult = false)]
        public async Task<bool?> String_Like(object input, object likePattern)
        {
            _params["TextInput"] = input;
            _params["LikePattern"] = likePattern;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Like", _params);
            CheckResult(result);

            return result?.Value as bool?;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
