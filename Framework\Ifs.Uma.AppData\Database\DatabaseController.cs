﻿using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Database;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Database
{
    public class DatabaseController<T> : IDatabaseController where T : FwDataContext
    {
        private const string DllPath = "Ifs.Uma.Database.SQLite, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null";
        private const string DbFileName = "database_{0}.db";
        private const string DbConnectionString = "Transaction Timeout=15;data source=" + DbFileName;

        private readonly ILogger _logger;

        private DbProviderFactory _factory;
        private DbInternal _dbInternal;
        private IMetadata _metadata;

        public DatabaseController(ILogger logger)
        {
            _logger = logger;
            ConnectedDatabaseId = -1;
        }

        #region IDatabaseController

        public int ConnectedDatabaseId { get; set; }

        public bool DoesDatabaseExist(int id)
        {
            if (id < 0)
                throw new ArgumentOutOfRangeException("id");

            string dbFileName = string.Format(CultureInfo.InvariantCulture, DbFileName, id);
            return DataSourceHelper.Instance.FileExists(dbFileName);
        }

        public void CreateDatabaseForUser(int id, string userName, string password, MetadataBlob metadataBlob = null)
        {
            if (id < 0)
                throw new ArgumentOutOfRangeException(nameof(id));

            DeleteDatabase(id);

            string dbConnectionString = string.Format(CultureInfo.InvariantCulture, DbConnectionString, id);
            DbInternal dbInternal = CreateDbInternal(dbConnectionString, metadataBlob, password);

            dbInternal.CreateDatabase(dbInternal.MappingSource.GetModel(typeof(T)));

            FwDataContext ctx = new FwDataContext(dbInternal);
            DatabaseInfo dbInfo = new DatabaseInfo();
            dbInfo.ActivatedUser = userName;
            dbInfo.InitializeStatus = InitializeStatus.Required;
            dbInfo.Creation = DateTime.Now;
            dbInfo.PasswordHash = HashPassword(password);
            dbInfo.Version = FwDataContext.SchemaVersion;
            dbInfo.AppVersion = GetAppVersion();
            dbInfo.MetadataBlob = metadataBlob?.ToByteArray();
            ctx.DatabaseInfos.InsertOnSubmit(dbInfo);
            ctx.SubmitChanges(false);
        }

        public void CreateAndLoginDatabaseFromData(int id, string exportData, out string userName)
        {
            if (id < 0)
                throw new ArgumentOutOfRangeException(nameof(id));

            Disconnect();
            DeleteDatabase(id);

            string dbConnectionString = string.Format(CultureInfo.InvariantCulture, DbConnectionString, id);
            DbInternal dbInternal = CreateDbInternal(dbConnectionString, (CpiMetadata)null, null);

            dbInternal.CreateDatabase(dbInternal.MappingSource.GetModel(typeof(T)));

            // Load meta data
            T context = OnCreateDataContext(dbInternal);
            ImportData(context, exportData, x => TypeHelper.CanCast(x, typeof(DatabaseInfo)));

            MetadataBlob metadataBlob = context.DatabaseInfos.Select(x => MetadataBlob.FromByteArray(x.MetadataBlob)).First();

            if (metadataBlob.Cpi == null)
            {
                throw new InvalidDataException("Failed to create TryMe database. Data does not contain cpi or svc metadata");
            }

            // Load non meta data
            dbInternal = CreateDbInternal(dbConnectionString, metadataBlob, null);
            context = OnCreateDataContext(dbInternal);
            context.RecreateAppsTables();
            ImportData(context, exportData, x => !TypeHelper.CanCast(x, typeof(DatabaseInfo)));

            Connect(id, dbInternal);

            context = CreateDataContext();
            var dbInfo = context.DatabaseInfos.First();

            //Change data schema version if not right
            if (dbInfo.Version != FwDataContext.SchemaVersion)
            {
                _logger.Warning(Strings.ImportedSchemaVersionDoesntMatch, dbInfo.Version.ToString(), FwDataContext.SchemaVersion.ToString());

                context.DatabaseInfos.Attach(dbInfo);
                dbInfo.Version = FwDataContext.SchemaVersion;
                context.SubmitChanges(false);
            }

            userName = dbInfo.ActivatedUser;
        }

        public bool Login(int id, string userName, string password)
        {
            if (id < 0)
                throw new ArgumentOutOfRangeException(nameof(id));

            Disconnect();

            string dbConnectionString = string.Format(CultureInfo.InvariantCulture, DbConnectionString, id);
            DbInternal dbInternal = CreateDbInternal(dbConnectionString, (CpiMetadata)null, password);

            if (dbInternal.GetStatus() == DbStatus.Unauthorized)
            {
                Disconnect();
                return false;
            }

            T ctx = OnCreateDataContext(dbInternal);

            var dbInfo = ctx.DatabaseInfos
                .Where(x => x.ActivatedUser == userName)
                .Select(x => new
                {
                    x.Version,
                    x.PasswordHash,
                    MetadataBlob = x.MetadataBlob == null ? null : MetadataBlob.FromByteArray(x.MetadataBlob)
                })
                .FirstOrDefault();

            if (dbInfo == null || dbInfo.PasswordHash != HashPassword(password))
            {
                return false;
            }

            if (dbInfo.Version != FwDataContext.SchemaVersion)
            {
                UpdateDatabase(dbConnectionString, password, dbInfo.Version);
                return Login(id, userName, password);
            }

            var dbExtraInfo = ctx.DatabaseInfos
                .Where(x => x.ActivatedUser == userName)
                .Select(x => new
                {
                    NavigatorEntriesBlob = x.NavigatorEntriesBlob == null ? null : MetadataBlob.FromByteArray(x.NavigatorEntriesBlob)
                })
                .FirstOrDefault();

            Disconnect();

            if (dbExtraInfo != null && dbExtraInfo.NavigatorEntriesBlob != null)
            {
                dbInternal = CreateDbInternal(dbConnectionString, dbInfo.MetadataBlob, dbExtraInfo.NavigatorEntriesBlob, password);
            }
            else
            {
                dbInternal = CreateDbInternal(dbConnectionString, dbInfo.MetadataBlob, password);
            }

            return Connect(id, dbInternal);
        }

        private void UpdateDatabase(string dbConnectionString, string password, int oldVersion)
        {
            DbInternal dbInternal = CreateDbInternal(dbConnectionString, (CpiMetadata)null, password);
            FwDataContext ctx = new FwDataContext(dbInternal);
            ctx.UpdateDatabase();
            Disconnect();
        }

        public void DeleteDatabase(int id)
        {
            if (id < 0)
                throw new ArgumentOutOfRangeException("id");

            if (ConnectedDatabaseId == id)
            {
                Disconnect();
            }

            try
            {
                string dbFileName = string.Format(CultureInfo.InvariantCulture, DbFileName, id);
                DataSourceHelper.Instance.DeleteFileAsync(dbFileName).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private bool _traceFlag;
        public bool TraceFlag
        {
            get { return _traceFlag; }
            set
            {
                _traceFlag = value;
                if (_factory != null)
                {
                    _factory.TraceFlag = _traceFlag;
                }
            }
        }

        public void ChangePassword(string newPassword)
        {
            if (ConnectedDatabaseId < 0)
            {
                throw new InvalidOperationException();
            }

            ChangeDatabasePassword(_dbInternal, newPassword);

            T ctx = CreateDataContext();
            DatabaseInfo dbInfo = ctx.DatabaseInfos.FirstOrDefault();
            ctx.DatabaseInfos.Attach(dbInfo);

            if (dbInfo != null)
            {
                dbInfo.PasswordHash = HashPassword(newPassword);
                ctx.SubmitChanges(false);
            }
        }

        public bool ValidatePassword(string password)
        {
            if (ConnectedDatabaseId < 0)
            {
                throw new InvalidOperationException();
            }

            T ctx = CreateDataContext();

            string code = PlatformServices.SaltPassword(password);

            var dbPassword = ctx.DatabaseInfos
                .Select(x => x.PasswordHash)
                .FirstOrDefault();
            return (HashPassword(code) == dbPassword);
        }

        public void Disconnect()
        {
            ConnectedDatabaseId = -1;
            _dbInternal = null;
            _metadata = null;

            UsefulExtensions.ThreadSafeDispose(ref _factory);

            QueryCache.Clear();
        }

        public async Task<MetadataDifference> UpdateMetadata(MetadataBlob metadataBlob, Func<string, Task<MetadataBlob>> getClientMetaData, MetadataBlob navEntries)
        {
            if (_dbInternal == null)
                throw new InvalidOperationException("Metadata cannot be updated with no active database login");

            IMetadata oldMetadata = _metadata;
            IMetadata metadata = navEntries == null ? CreateMetadata(metadataBlob) : CreateMetadata(metadataBlob, navEntries);
            string metadataBlobSet = CpiClientMetadata.BinaryToText(metadataBlob.Cpi) + CpiMetadata.BlobSeparator; // App meta comes first

            foreach (CpiClientMetadata client in metadata.CpiMetaData.Clients)
            {
                if (client.Layout == null)
                {
                    MetadataBlob clientMetadataBlob = await getClientMetaData(client.Name);
                    CpiClientMetadata clientMetadata = CreateClientMetadata(clientMetadataBlob);
                    metadataBlobSet += client.Name + CpiMetadata.BlobNameSeparator + CpiClientMetadata.BinaryToText(clientMetadataBlob.Cpi) + CpiMetadata.BlobSeparator;
                    client.Layout = clientMetadata.Layout;
                    client.Projection = clientMetadata.Projection;
                    client.Version = clientMetadata.Version;
                }
            }

            MetadataDifference diff = ClientMetadata.Compare(oldMetadata, metadata);

            T ctx = CreateDataContext();

            if (diff == MetadataDifference.ProjectionChanges)
            {
                ctx.DropAppsTables();
            }

            DatabaseInfo dbInfo = ctx.DatabaseInfos.First();
            ctx.DatabaseInfos.Attach(dbInfo);

            byte[] metadataBlobArray;
            byte[] cpi = Encoding.UTF8.GetBytes(metadataBlobSet);
            int size = sizeof(int) + cpi.Length;

            using (MemoryStream ms = new MemoryStream(size))
            using (BinaryWriter writer = new BinaryWriter(ms))
            {
                writer.Write(cpi.Length);
                writer.Write(cpi);

                metadataBlobArray = ms.ToArray();
            }

            if (navEntries != null)
            {
                dbInfo.NavigatorEntriesBlob = navEntries.ToByteArray();
            }

            dbInfo.MetadataBlob = metadataBlobArray;
            ctx.SubmitChanges(false);

            DbInternal dbInternal = CreateDbInternal(_dbInternal.ConnectionString, metadata.CpiMetaData, null, navEntries);
            _dbInternal = dbInternal;

            if (dbInternal.MappingSource is MetadataMappingSource metadataMappingSource)
            {
                _metadata = metadataMappingSource.GetMetadata(typeof(T));

                // Do a call here to create all the meta tables (entities and structures)
                // this will validate the meta model and throw an exception if invalid
                _metadata.MetaModel.GetTables();
            }
            else
            {
                _metadata = null;
            }

            if (diff == MetadataDifference.ProjectionChanges)
            {
                ctx = CreateDataContext();
                ctx.RecreateAppsTables();
                QueryCache.Clear();
            }

            return diff;
        }

        public IMetadata GetMetadata()
        {
            return _metadata;
        }

        public void ExportDataToStream(Stream stream)
        {
            if (ConnectedDatabaseId < 0)
            {
                throw new InvalidOperationException();
            }

            T ctx = CreateDataContext();

            using (XmlWriter writer = XmlWriter.Create(stream, new XmlWriterSettings { Indent = true, Encoding = Encoding.UTF8 }))
            {
                ctx.ExportData(writer);
                writer.Flush(); //JVB: Flush should not be necessary
            }
        }

        FwDataContext IDataContextProvider.CreateDataContext()
        {
            return CreateDataContext();
        }

        public IMetaModel GetMetaModel()
        {
            return _dbInternal?.MappingSource.GetModel(typeof(T));
        }

        #endregion

        public T CreateDataContext()
        {
            if (_dbInternal == null)
            {
                return null;
            }

            return OnCreateDataContext(_dbInternal);
        }

        private DbInternal CreateDbInternal(string connectionString, MetadataBlob metadataBlob, string password)
        {
            IMetadata metadata = CreateMetadata(metadataBlob);
            return CreateDbInternal(connectionString, metadata?.CpiMetaData, password);
        }

        private DbInternal CreateDbInternal(string connectionString, MetadataBlob metadataBlob, MetadataBlob navMetaDataBlob, string password)
        {
            IMetadata metadata = CreateMetadata(metadataBlob, navMetaDataBlob);
            return CreateDbInternal(connectionString, metadata?.CpiMetaData, password, navMetaDataBlob);
        }

        private DbInternal CreateDbInternal(string connectionString, CpiMetadata metadata, string password, MetadataBlob navMetaDatablob = null)
        {
            if (_factory == null)
            {
                _factory = DbProviderFactory.Create(DllPath, _logger);

                if (_factory == null)
                {
                    throw new InvalidOperationException($"Unable to load dll {DllPath}");
                }

                _factory.TraceFlag = TraceFlag;
            }

            Type enumAttributeClassType = GetEnumAttributeClassType();
            MappingSource mappingSource = new AttributeMappingSource(null, _logger, enumAttributeClassType);

            if (metadata != null)
            {
                mappingSource = new MetadataMappingSource(_logger, (AttributeMappingSource)mappingSource, metadata, QuerySelectSpec.CreateForView, navMetaDatablob);
            }

            DbConnectionStringBuilder connectionStringBuilder = _factory.CreateConnectionStringBuilder();
            connectionStringBuilder.ConnectionString = connectionString;
            if (password != null)
            {
                connectionStringBuilder.Password = password;
            }

            return new DbInternal(_factory, connectionStringBuilder.ConnectionString, mappingSource, _logger);
        }

        private bool Connect(int dbId, DbInternal dbInternal)
        {
            ConnectedDatabaseId = dbId;
            _dbInternal = dbInternal;

            if (dbInternal.MappingSource is MetadataMappingSource metadataMappingSource)
            {
                _metadata = metadataMappingSource.GetMetadata(typeof(T));
            }
            else
            {
                _metadata = null;
            }

            return dbInternal.GetStatus() == DbStatus.Valid;
        }

        private void ChangeDatabasePassword(DbInternal db, string newPassword)
        {
            string cleanPass = newPassword?.Replace("'", "''");

            string connectionString = db.ConnectionString;
            int dbId = ConnectedDatabaseId;
            IMetadata metadata = _metadata;

            // Disconnect everything so no connection is using the db with the old password
            Disconnect();

            // Connect and change password
            DbInternal db2 = CreateDbInternal(connectionString, metadata.CpiMetaData, null);
            db2.CommandA(cmd =>
            {
                cmd.CommandText = $"PRAGMA rekey = '{cleanPass}';";
                cmd.ExecuteNonQuery();
            });
            Disconnect();

            // Connect with new password
            DbInternal dbInternal = CreateDbInternal(connectionString, metadata.CpiMetaData, newPassword);
            Connect(dbId, dbInternal);
        }

        private static void ImportData(DataContextBase ctx, string xmlData, Predicate<Type> tableFilter)
        {
            StringReader stringReader = null;
            try
            {
                stringReader = new StringReader(xmlData);
                using (XmlReader reader = XmlReader.Create(stringReader))
                {
                    stringReader = null;
                    ctx.ImportData(reader, tableFilter);
                }
            }
            finally
            {
                stringReader?.Dispose();
            }
            ctx.SubmitChanges(false);
        }

        #region Abstract / Overridable Methods

        protected virtual Type GetEnumAttributeClassType()
        {
            return typeof(T);
        }

        protected virtual T OnCreateDataContext(DbInternal dbInternal)
        {
            return (T)Activator.CreateInstance(typeof(T), dbInternal);
        }

        protected virtual string HashPassword(string password)
        {
            return PlatformServices.HashPassword(password);
        }

        protected virtual string GetAppVersion()
        {
            return ApplicationInfo.FrameworkVersion.ToString();
        }

        protected virtual IMetadata CreateMetadata(MetadataBlob metadataBlob)
        {
            return metadataBlob == null ? null : ClientMetadata.Create(_logger, metadataBlob);
        }

        protected virtual IMetadata CreateMetadata(MetadataBlob metadataBlob, MetadataBlob navMetaDataBlob)
        {
            return metadataBlob == null ? null : ClientMetadata.Create(_logger, metadataBlob, navMetaDataBlob);
        }

        protected virtual CpiClientMetadata CreateClientMetadata(MetadataBlob metadataBlob)
        {
            return metadataBlob == null ? null : ClientMetadata.CreateClientMetaData(_logger, metadataBlob);
        }
        #endregion

    }
}
