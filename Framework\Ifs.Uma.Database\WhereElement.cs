﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public enum EWhereElement
    {
        Where, WhereN, PushBracket, PopBracket, WhereNNested, WhereAge, WhereEndDate, WhereBool, WhereInt, WhereLong
    }

    public interface IWhereElement : IWriteSql
    {
        EWhereElement Kind { get; }
        EOperand Operand { get; }
    }

    public interface IWhereBase : IWhereElement, IAliasedColumnSpec
    {
        EComparisonMethod Comparer { get; }
    }

    public interface IWhereBaseTyped : IWhereBase, ITypedColumnSpec
    {
    }

    public interface IWhereBool : IWhereBaseTyped
    {
        bool Value { get; }
    }

    public interface IWhereInt : IWhereBaseTyped
    {
        int Value { get; }
    }

    public interface IWhereLong : IWhereBaseTyped
    {
        long Value { get; }
    }

    public interface IWhereDetail : IWhereBaseTyped
    {
        object Value { get; }
        int ValueIndex { get; }
    }

    public interface IWhereNDetail : IWhereBaseTyped
    {
        IEnumerable Values { get; }
        int StartValueIndex { get; }
    }

    public interface IWhereNNested : IWhereBase
    {
        ISelectSpec NestedSelect { get; }
    }

    public abstract class WhereElement : IWhereElement
    {
        #region IWhereElement factories

        public static IWhereDetail Create<T>(EOperand operand, ITypedColumnSpec column, EComparisonMethod comparison,
            T value, int valueIndex, string tableAlias)
        {
            return column != null ?
                new WhereDetail(operand, column.ColumnName, column.ColumnType, comparison, value, valueIndex, tableAlias) : null;
        }

        public static IWhereDetail Create<T>(EOperand operand, ITypedColumnSpec column, EComparisonMethod comparison,
            T value, int valueIndex)
        {
            return Create(operand, column, comparison, value, valueIndex, null);
        }

        public static IWhereDetail Create<T>(EOperand operand, ITypedColumnSpec column, EComparisonMethod comparison,
            T value)
        {
            return Create(operand, column, comparison, value, -1, null);
        }

        public static IWhereBaseTyped CreateN<T>(EOperand operand, ITypedColumnSpec column, EComparisonMethod comparison,
            IEnumerable<T> values, int startValueIndex, string tableAlias)
        {
            IWhereBaseTyped result = null;
            if (column != null)
            {
                int count = 0;
                if (values != null)
                {
                    count = values.Count();
                }
                if (count > 1)
                {
                    result = new WhereNDetail(operand, column.ColumnName, column.ColumnType, comparison, values, startValueIndex, tableAlias);
                }
                else if (count == 1)
                {
                    result = new WhereDetail(operand, column.ColumnName, column.ColumnType, comparison, values.Single(), startValueIndex, tableAlias);
                }
            }
            return result;
        }

        public static IWhereBaseTyped CreateN<T>(EOperand operand, ITypedColumnSpec column, EComparisonMethod comparison,
            IEnumerable<T> values, int startValueIndex)
        {
            return CreateN(operand, column, comparison, values, startValueIndex, null);
        }

        public static IWhereBaseTyped CreateN<T>(EOperand operand, ITypedColumnSpec column, EComparisonMethod comparison,
            IEnumerable<T> values)
        {
            return CreateN(operand, column, comparison, values, -1, null);
        }
        public static IWhereElement Create(EOperand operand, ISqlExpression expression)
        {
            return expression == null ? null : new WhereExpression(operand, expression);
        }

        public static IWhereElement CreatePushBracket(EOperand operand)
        {
            return new WherePushBracket(operand);
        }

        public static IWhereElement CreatePopBracket()
        {
            return new WherePopBracket();
        }

        public static IWhereNNested CreateNNested(EOperand operand, string columnName, EComparisonMethod comparison,
            ISelectSpec nestedSelect, string tableAlias)
        {
            return !string.IsNullOrEmpty(columnName) && nestedSelect != null &&
                nestedSelect.Columns != null && nestedSelect.Columns.Count(x => x != null) == 1 ?
                new WhereNNested(operand, columnName, comparison, nestedSelect, tableAlias) : null;
        }

        public static IWhereNNested CreateNNested(EOperand operand, string columnName, EComparisonMethod comparison,
            ISelectSpec nestedSelect)
        {
            return CreateNNested(operand, columnName, comparison, nestedSelect, null);
        }

        //public static IWhereAge CreateAge(EOperand operand, string columnName, EComparisonMethod comparison,
        //    TimeSpan timeSpan, string tableAlias)
        //{
        //    return !string.IsNullOrEmpty(columnName) ? 
        //        new WhereAge(operand, columnName, comparison, timeSpan, tableAlias) : null;
        //}

        //public static IWhereAge CreateAge(EOperand operand, string columnName, EComparisonMethod comparison,
        //    TimeSpan timeSpan)
        //{
        //    return CreateAge(operand, columnName, comparison, timeSpan, null);
        //}

        //public static IWhereEndDate CreateEndDate(EOperand operand, string startDateColumnName, string durationColumnName,
        //    EComparisonMethod comparison, DateTime endDate, string tableAlias)
        //{
        //    return !string.IsNullOrEmpty(startDateColumnName) && !string.IsNullOrEmpty(durationColumnName) ?
        //        new WhereEndDate(operand, startDateColumnName, durationColumnName, comparison, endDate, tableAlias) : null;
        //}

        //public static IWhereEndDate CreateEndDate(EOperand operand, string startDateColumnName, string durationColumnName,
        //    EComparisonMethod comparison, DateTime endDate)
        //{
        //    return CreateEndDate(operand, startDateColumnName, durationColumnName, comparison, endDate, null);
        //}

        public static IWhereBool CreateBool(EOperand operand, string columnName, EComparisonMethod comparison,
            bool value, string tableAlias)
        {
            return !string.IsNullOrEmpty(columnName) ?
                new WhereBool(operand, columnName, comparison, value, tableAlias) : null;
        }

        public static IWhereBool CreateBool(EOperand operand, string columnName, EComparisonMethod comparison,
            bool value)
        {
            return CreateBool(operand, columnName, comparison, value, null);
        }

        public static IWhereInt CreateInt(EOperand operand, string columnName, EComparisonMethod comparison,
            int value, string tableAlias)
        {
            return !string.IsNullOrEmpty(columnName) ?
                new WhereInt(operand, columnName, comparison, value, tableAlias) : null;
        }

        public static IWhereInt CreateInt(EOperand operand, string columnName, EComparisonMethod comparison,
            int value)
        {
            return CreateInt(operand, columnName, comparison, value, null);
        }

        public static IWhereLong CreateLong(EOperand operand, string columnName, EComparisonMethod comparison,
            long value, string tableAlias)
        {
            return !string.IsNullOrEmpty(columnName) ?
                new WhereLong(operand, columnName, comparison, value, tableAlias) : null;
        }

        public static IWhereLong CreateLong(EOperand operand, string columnName, EComparisonMethod comparison,
            long value)
        {
            return CreateLong(operand, columnName, comparison, value, null);
        }

        #endregion

        #region IWhereElement Members

        public abstract EWhereElement Kind { get; }
        public abstract EOperand Operand { get; }

        #endregion

        #region IWriteSql Members

        public abstract void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode);

        #endregion

        #region Implementation

        protected WhereElement()
        {
        }

        protected class WherePopBracket : WhereElement
        {
            public WherePopBracket()
            {
            }
            public override EWhereElement Kind { get { return EWhereElement.PopBracket; } }
            public override EOperand Operand { get { return EOperand.None; } }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (sb != null && builder != null)
                {
                    sb.Append(builder.CloseBracket);
                }
            }
        }

        protected class WherePushBracket : WhereElement
        {
            public WherePushBracket(EOperand operand)
            {
                m_operand = operand;
            }
            public override EOperand Operand { get { return m_operand; } }
            public override EWhereElement Kind { get { return EWhereElement.PushBracket; } }
            private EOperand m_operand;

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (sb != null && builder != null)
                {
                    sb.Append(builder.OpenBracket);
                }
            }
        }

        protected abstract class WhereBase : WherePushBracket, IWhereBase
        {
            protected WhereBase(EOperand operand, string columnName, EComparisonMethod comparison, string tableAlias)
                : base(operand)
            {
                ColumnName = columnName;
                Comparer = comparison;
                TableAlias = tableAlias;
            }

            #region IWhereBase Members

            public EComparisonMethod Comparer { get; private set; }

            #endregion

            #region IColumnSpec Members

            public string ColumnName { get; private set; }

            #endregion

            #region IAliasedColumnSpec Members

            public string TableAlias { get; private set; }

            #endregion

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (mode != SqlWriteMode.WhereElement)
                {
                    if (builder != null)
                    {
                        builder.WriteColumn(sb, TableAlias, ColumnName);
                    }
                }
                else
                {
                    WriteSqlWhere(sb, info, builder);
                }
            }

            protected void WriteColumn(StringBuilder sb, SqlBuilder builder)
            {
                if (builder != null)
                {
                    builder.WriteColumn(sb, TableAlias, ColumnName);
                }
            }
            protected void WriteComparison(StringBuilder sb, SqlBuilder builder)
            {
                if (sb != null && builder != null)
                {
                    sb.Append(builder.ComparisonText(Comparer));
                }
            }

            protected abstract void WriteSqlWhere(StringBuilder sb, IStatementInfo info, SqlBuilder builder);
        }

        protected abstract class WhereBaseTyped : WhereBase, IWhereBaseTyped
        {
            protected WhereBaseTyped(EOperand operand, string columnName, Type columnType, EComparisonMethod comparison, string tableAlias)
                : base(operand, columnName, comparison, tableAlias)
            {
                ColumnType = columnType;
            }

            #region ITypedColumnSpec Members

            public Type ColumnType { get; private set; }

            #endregion
        }

        protected class WhereBool : WhereBaseTyped, IWhereBool
        {
            public WhereBool(EOperand operand, string columnName, EComparisonMethod comparison, bool value, string tableAlias)
                : base(operand, columnName, typeof(bool), comparison, tableAlias)
            {
                Value = value;
            }
            public bool Value { get; private set; }
            public override EWhereElement Kind { get { return EWhereElement.WhereBool; } }

            protected override void WriteSqlWhere(StringBuilder sb, IStatementInfo info, SqlBuilder builder)
            {
                WriteColumn(sb, builder);
                WriteComparison(sb, builder);
                if (sb != null && builder != null)
                {
                    sb.Append(Value ? builder.TrueLiteral : builder.FalseLiteral);
                }
            }
        }

        protected class WhereInt : WhereBaseTyped, IWhereInt
        {
            public WhereInt(EOperand operand, string columnName, EComparisonMethod comparison, int value, string tableAlias)
                : base(operand, columnName, typeof(int), comparison, tableAlias)
            {
                Value = value;
            }
            public int Value { get; private set; }
            public override EWhereElement Kind { get { return EWhereElement.WhereInt; } }

            protected override void WriteSqlWhere(StringBuilder sb, IStatementInfo info, SqlBuilder builder)
            {
                WriteColumn(sb, builder);
                WriteComparison(sb, builder);
                if (sb != null)
                {
                    sb.Append(ObjectConverter.ToString(Value));
                }
            }
        }

        protected class WhereLong : WhereBaseTyped, IWhereLong
        {
            public WhereLong(EOperand operand, string columnName, EComparisonMethod comparison, long value, string tableAlias)
                : base(operand, columnName, typeof(int), comparison, tableAlias)
            {
                Value = value;
            }
            public long Value { get; private set; }
            public override EWhereElement Kind { get { return EWhereElement.WhereLong; } }

            protected override void WriteSqlWhere(StringBuilder sb, IStatementInfo info, SqlBuilder builder)
            {
                WriteColumn(sb, builder);
                WriteComparison(sb, builder);
                if (sb != null)
                {
                    sb.Append(ObjectConverter.ToString(Value));
                }
            }
        }

        protected class WhereDetail : WhereBaseTyped, IWhereDetail
        {
            public WhereDetail(EOperand operand, string columnName, Type columnType, EComparisonMethod comparison,
                object value, int valueIndex, string tableAlias)
                : base(operand, columnName, columnType, comparison, tableAlias)
            {
                Value = value;
                ValueIndex = valueIndex;
            }

            #region IWhereDetail Members

            public object Value { get; private set; }
            public int ValueIndex { get; private set; }

            #endregion

            public override EWhereElement Kind { get { return EWhereElement.Where; } }

            protected override void WriteSqlWhere(StringBuilder sb, IStatementInfo info, SqlBuilder builder)
            {
                WriteColumn(sb, builder);
                if (sb != null && builder != null && info != null)
                {
                    if (builder.ValueIsNull(ColumnType, Value))
                    {
                        sb.Append(builder.NullComparisonText(Comparer));
                    }
                    else
                    {
                        WriteComparison(sb, builder);
                        info.WriteParameter(sb, ColumnType, Value, ValueIndex);
                    }
                }
            }

            public override string ToString()
            {
                // Improve debugging detail
                return string.Format(CultureInfo.InvariantCulture, "{0} {1} {2} {3}", Operand, ColumnName, Comparer, Value);
            }
        }

        protected class WhereNDetail : WhereBaseTyped, IWhereNDetail
        {
            public WhereNDetail(EOperand operand, string columnName, Type columnType, EComparisonMethod comparison,
                IEnumerable values, int startValueIndex, string tableAlias)
                : base(operand, columnName, columnType, comparison, tableAlias)
            {
                Values = values;
                StartValueIndex = startValueIndex;
            }

            #region IWhereNDetail Members

            public IEnumerable Values { get; private set; }
            public int StartValueIndex { get; private set; }

            #endregion

            public override EWhereElement Kind { get { return EWhereElement.WhereN; } }

            protected override void WriteSqlWhere(StringBuilder sb, IStatementInfo info, SqlBuilder builder)
            {
                WriteColumn(sb, builder);
                if (sb != null && info != null && builder != null)
                {
                    sb.Append(builder.MultiComparisonText(Comparer));
                    sb.Append(builder.OpenBracket);
                    bool first = true;
                    int valueIndex = StartValueIndex;
                    foreach (object value in Values)
                    {
                        if (first)
                        {
                            first = false;
                        }
                        else
                        {
                            sb.Append(builder.CommaSeparator);
                        }
                        info.WriteParameter(sb, ColumnType, value, valueIndex);
                        if (StartValueIndex >= 0)
                        {
                            valueIndex++;
                        }
                    }
                    sb.Append(builder.CloseBracket);
                }
            }
        }

        protected class WhereNNested : WhereBase, IWhereNNested
        {
            public WhereNNested(EOperand operand, string columnName, EComparisonMethod comparison,
                ISelectSpec nestedSelect, string tableAlias)
                : base(operand, columnName, comparison, tableAlias)
            {
                NestedSelect = nestedSelect;
            }
            public ISelectSpec NestedSelect { get; private set; }
            public override EWhereElement Kind { get { return EWhereElement.WhereNNested; } }

            protected override void WriteSqlWhere(StringBuilder sb, IStatementInfo info, SqlBuilder builder)
            {
                WriteColumn(sb, builder);
                if (sb != null && builder != null)
                {
                    sb.Append(builder.MultiComparisonText(Comparer));
                    sb.Append(builder.OpenBracket);
                    NestedSelect.WriteSql(sb, info, builder, SqlWriteMode.Statement);
                    sb.Append(builder.CloseBracket);
                }
            }
        }

        internal sealed class WhereExpression : IWhereElement
        {
            public EWhereElement Kind { get; } = EWhereElement.Where;
            public EOperand Operand { get; }

            public ISqlExpression Expression { get; }

            internal WhereExpression(EOperand operand, ISqlExpression expression)
            {
                Operand = operand;
                Expression = expression;
            }

            public void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                Expression.WriteSql(sb, info, builder, mode);
            }
        }

        #endregion
    }
}
