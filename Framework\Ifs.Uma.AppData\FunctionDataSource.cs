﻿using System;
using System.Collections.Generic;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData
{
    public sealed class FunctionDataSource : EntityDataSource
    {
        public string FunctionName { get; }
        public IReadOnlyDictionary<string, object> ParameterValues { get; }

        private FunctionDataSource(IMetadata metadata, RecordType recordType, string projectionName, string functionName, 
            IReadOnlyDictionary<string, object> parameterValues)
            : base(metadata, projectionName, recordType)
        {
            if (projectionName == null) throw new ArgumentNullException(nameof(projectionName));
            if (functionName == null) throw new ArgumentNullException(nameof(functionName));
            if (parameterValues == null) throw new ArgumentNullException(nameof(parameterValues));

            FunctionName = functionName;
            ParameterValues = parameterValues;
        }

        public static FunctionDataSource Create(IMetadata metadata, string projectionName, string functionName, IReadOnlyDictionary<string, object> parameterValues)
        {
            if (metadata == null) throw new ArgumentNullException(nameof(metadata));
            if (functionName == null) throw new ArgumentNullException(nameof(functionName));

            CpiFunction function = metadata.FindFunction(projectionName, functionName);

            if (function?.ReturnType?.DataType != CpiDataType.Structure && function?.ReturnType?.DataType != CpiDataType.Entity)
            {
                return null;
            }

            string entityName = function.ReturnType.SubType;

            if (entityName == null)
            {
                return null;
            }

            RecordType recordType = metadata.GetRecordType(projectionName, function.ReturnType.SubType);

            if (recordType == null)
            {
                return null;
            }

            return new FunctionDataSource(metadata, recordType, projectionName, functionName, parameterValues ?? new Dictionary<string, object>());
        }

        public override bool IsEffectedByChangeSet(DataChangeSet changeSet)
        {
            if (changeSet.HasChanges(Table))
            {
                return true;
            }

            FunctionInfo function = FunctionInfo.Get(Metadata, ProjectionName, FunctionName);
            
            if (function == null)
            {
                return false;
            }
            
            return function.AreResultsEffectedByChangeSet(changeSet);
        }

        public override bool IsTheSameAs(EntityDataSource dataSource)
        {
            FunctionDataSource funcDataSource = dataSource as FunctionDataSource;
            if (funcDataSource == null)
                return false;
            if (funcDataSource.FunctionName != FunctionName)
                return false;
            if (funcDataSource.ParameterValues.Count != ParameterValues.Count)
                return false;

            foreach (var item in ParameterValues)
            {
                if (!funcDataSource.ParameterValues.TryGetValue(item.Key, out object value) ||
                    !object.Equals(item.Value, value))
                {
                    return false;
                }
            }

            return true;
        }
    }
}
