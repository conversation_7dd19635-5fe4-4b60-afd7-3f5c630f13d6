﻿using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Types;
using Ifs.Cloud.Client.Utils;
using Ifs.Uma.Services.Attachments;

namespace Ifs.Uma.Comm.TouchApps
{
    public sealed class AttachmentComms : IAttachmentComms
    {
        private readonly TouchAppsComms _comms;
        private long _messageId;

        public AttachmentComms(IIfsConnection connection)
        {
            _comms = connection.TouchAppsComms;
            _messageId = 0;
        }

        public bool IsOnline => _comms.IsAvailable();

        public int DeviceId => _comms.DeviceId;

        public async Task<ReponseStream> DownloadFile(string resourceName, Dictionary<string, object> queryParams)
        {
            string queryParamsString = Formatter.ToParamString(queryParams);
            CallResponseStream response = await _comms.GetStream(resourceName, queryParamsString);
            return new ReponseStream() { Stream = response.Stream };
        }

        public async Task UploadFile(string resourceName, Dictionary<string, object> queryParams, string binaryColumn, Stream stream, string eTag, bool offline = true, string fileName = null)
        {
            _messageId++;
            string queryParamsString = Formatter.ToParamString(queryParams);

            // Message ID is not madatory from 22 track for BLOB upload. Sending the message ID here for backward compatibility support. 
            await _comms.PatchStream(resourceName, queryParamsString, binaryColumn, stream, DataFormat.Binary, eTag, _messageId, offline, fileName);
        }
    }
}
