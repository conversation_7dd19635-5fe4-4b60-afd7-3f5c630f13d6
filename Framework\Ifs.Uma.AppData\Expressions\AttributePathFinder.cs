﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    internal sealed class AttributePathFinder : IfsExpressionVisitor
    {
        private readonly HashSet<string> _found = new HashSet<string>();

        public static string[] Find(Expression expression)
        {
            AttributePathFinder visitor = new AttributePathFinder();
            visitor.Visit(expression);
            return visitor._found.ToArray();
        }

        private AttributePathFinder()
        {
        }

        protected internal override Expression VisitAttributeAccessExpression(AttributeAccessExpression exp)
        {
            _found.Add(exp.Attribute.Path);
            return base.VisitAttributeAccessExpression(exp);
        }
    }
}
