﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Framework.UI.Images;
using Ifs.Uma.Reporting.Report;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.Reporting.Generator
{
    public sealed class ImageViewerToItemsConverter
    {
        private ImageViewerElement _imageViewerElement;
        private readonly ILogger _logger;

        public ImageViewerToItemsConverter(ILogger logger)
        {
            _logger = logger;
        }

        public async Task<bool> GenerateForItemsAsync(ReportDoc report, CancellationToken cancellationToken, ImageViewerElement imageViewerElement)
        {
            _imageViewerElement = imageViewerElement;

            ReportImageViewerItem reportImageItem = new ReportImageViewerItem();
            reportImageItem.Header = imageViewerElement.Header;

            if (imageViewerElement.IsVisible)
            {
                if (imageViewerElement.Items != null)
                {
                    foreach (ImageViewerItem imageViewerItem in imageViewerElement.Items)
                    {
                        if (imageViewerItem is DocumentItem documentItem)
                        {
                            reportImageItem.Documents.Add(documentItem.PickedFile.FileName);
                        }
                        else if (imageViewerItem is MediaItem mediaItem)
                        {
                            byte[] data = null;
                            string base64Data = null;
                            try
                            {
                                using (MemoryStream ms = new MemoryStream())
                                using (Stream stream = await mediaItem.PickedFile.ReadAsync())
                                {
                                    await stream.CopyToAsync(ms);
                                    data = ms.ToArray();
                                    if (data != null)
                                    {
                                        base64Data = Convert.ToBase64String(data);
                                    }
                                }

                                ReportImage field = new ReportImage
                                {
                                    Name = imageViewerItem.FileName,
                                    Data = base64Data,
                                    Size = (FieldSize)imageViewerElement.Height
                                };

                                reportImageItem.Media.Add(field);
                            }
                            catch (Exception e)
                            {
                                _logger.HandleException(ExceptionType.Recoverable, e);
                            }
                        }
                    }
                }

                report.Items.Add(reportImageItem);
            }

            return true;
        }
    }
}
