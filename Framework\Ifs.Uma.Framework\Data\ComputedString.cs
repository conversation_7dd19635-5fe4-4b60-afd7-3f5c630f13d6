﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.AppData.StringExpressions;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.Data
{
    public sealed class ComputedString : ObservableBase, IStringExpressionValueProvider
    {
        public event EventHandler ValueChanged;

        private string _value;
        public string Value
        {
            get => _value;
            set => SetProperty(ref _value, value, OnValueChanged);
        }
        
        private readonly IDataHandler _dataHandler;
        private readonly ILogger _logger;
        private readonly TaskTracker _backgroundTasks;
        private readonly CancellingUpdater _updater;
        private readonly InterpolatedString _interpolatedString;
        private readonly IReadOnlyDictionary<string, AggregateInfo> _aggregates;

        private RecordData _record;

        public ComputedString(ILogger logger, IDataHandler dataHandler, TaskTracker backgroundTasks, string stringExpression)
        {
            _dataHandler = dataHandler;
            _logger = logger;
            _backgroundTasks = backgroundTasks;
            _updater = new CancellingUpdater(UpdateAggregatesAsync);

            AggregateReplacer aggregateReplacer = new AggregateReplacer();
            _interpolatedString = new InterpolatedString(logger, stringExpression, aggregateReplacer.Replace);
            _aggregates = aggregateReplacer.Aggregates;
        }

        private void OnValueChanged(string oldValue, string newValue)
        {
            ValueChanged?.Invoke(this, EventArgs.Empty);
        }

        public void Update(RecordData record)
        {
            _record = record;
            Value = null;

            Task task = _updater.UpdateAsync();
            _backgroundTasks?.Add(task);
        }

        public void UpdateString()
        {
            if (_record == null || _record.IsEmpty())
            {
                Value = null;
            }
            else
            {
                Value = _interpolatedString.GetString(this, true, false);
            }
        }
        
        private async Task UpdateAggregatesAsync(CancellationToken cancelToken)
        {
            foreach (KeyValuePair<string, AggregateInfo> aggregate in _aggregates)
            {
                if (_record == null)
                {
                    aggregate.Value.Value = null;
                }
                else
                {
                    await UpdateAggregateAsync(aggregate.Value);
                }

                if (cancelToken.IsCancellationRequested)
                {
                    break;
                }
            }

            UpdateString();
        }

        private async Task UpdateAggregateAsync(AggregateInfo aggregate)
        {
            try
            {
                aggregate.Value = null;
                
                EntityDataSource dataSource = _record.GetArrayDataSource(aggregate.ArrayName);
                if (dataSource != null)
                {
                    AggregateQuery query = new AggregateQuery(dataSource, aggregate.AggregateType, aggregate.Expression);

                    ExecuteResult result = await _dataHandler.GetAggregateAsync(query);
                    result.CheckFailure();

                    aggregate.Value = result.Value;
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
                aggregate.Value = null;
            }
        }

        bool IStringExpressionValueProvider.TryGetValue(string propertyName, out object value)
        {
            if (((IStringExpressionValueProvider)_record).TryGetValue(propertyName, out value))
            {
                return true;
            }

            if (_aggregates.TryGetValue(propertyName, out AggregateInfo info))
            {
                value = info.Value;
                return true;
            }

            value = null;
            return false;
        }

        bool IStringExpressionValueProvider.TryGetFormattedValue(string propertyName, out string value)
        {
            if (((IStringExpressionValueProvider)_record).TryGetFormattedValue(propertyName, out value))
            {
                return true;
            }

            if (_aggregates.TryGetValue(propertyName, out AggregateInfo info))
            {
                value = AttributeFormatter.FormatValue(info.Value);
                return true;
            }

            value = null;
            return false;
        }

        private sealed class AggregateInfo
        {
            public string ArrayName { get; }
            public AggregateType AggregateType { get; }
            public Expression Expression { get; }
            public object Value { get; set; }

            public AggregateInfo(string arrayName, AggregateType aggregateType, Expression expression)
            {
                ArrayName = arrayName;
                AggregateType = aggregateType;
                Expression = expression;
            }
        }

        private sealed class AggregateReplacer : IfsExpressionVisitor
        {
            private const string AggregateVariablePrefix = "_Aggregate";

            public Dictionary<string, AggregateInfo> Aggregates { get; } = new Dictionary<string, AggregateInfo>();

            public Expression Replace(Expression expression)
            {
                return Visit(expression);
            }

            protected override Expression VisitMethod(MethodExpression exp)
            {
                string[] parts = exp.MethodName.Split('.');
                if (parts.Length == 2 &&
                    (exp.Arguments.Count == 0 || exp.Arguments.Count == 1) &&
                    TryConvertAggregate(parts[1], out AggregateType aggregateType))
                {
                    AggregateInfo info = new AggregateInfo(parts[0], aggregateType, exp.Arguments.Count == 0 ? null : exp.Arguments[0]);
                    string name = AggregateVariablePrefix + Aggregates.Count;
                    Aggregates[name] = info;
                    return IfsExpression.VarAccess(name);
                }

                return base.VisitMethod(exp);
            }

            private bool TryConvertAggregate(string str, out AggregateType aggregate)
            {
                switch (str)
                {
                    case "sum":
                        aggregate = AggregateType.Sum;
                        return true;
                    case "avg":
                        aggregate = AggregateType.Average;
                        return true;
                    case "max":
                        aggregate = AggregateType.Maximum;
                        return true;
                    case "min":
                        aggregate = AggregateType.Minimum;
                        return true;
                    case "count":
                        aggregate = AggregateType.Count;
                        return true;
                    default:
                        aggregate = AggregateType.Count;
                        return false;
                }
            }
        }
    }
}
