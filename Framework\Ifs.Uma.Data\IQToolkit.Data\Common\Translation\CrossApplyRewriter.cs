﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Attempts to rewrite cross-apply and outer-apply joins as inner and left-outer joins
    /// </summary>
    internal class CrossApplyRewriter : DbExpressionVisitor
    {
        private CrossApplyRewriter()
        {
        }

        public static Expression Rewrite(Expression expression)
        {
            return new CrossApplyRewriter().Visit(expression);
        }

        protected override Expression VisitJoin(JoinExpression node)
        {
            node = (JoinExpression)base.VisitJoin(node);

            if (node.Join == JoinType.CrossApply || node.Join == JoinType.OuterApply)
            {
                if (node.Right is TableExpression)
                {
                    return new JoinExpression(JoinType.CrossJoin, node.Left, node.Right, null);
                }
                else
                {
                    SelectExpression select = node.Right as SelectExpression;
                    // Only consider rewriting cross apply if 
                    //   1) right side is a select
                    //   2) other than in the where clause in the right-side select, no left-side declared aliases are referenced
                    //   3) and has no behavior that would change semantics if the where clause is removed (like groups, aggregates, take, skip, etc).
                    // Note: it is best to attempt this after redundant subqueries have been removed.
                    if (select != null
                        && select.Take == null
                        && select.Skip == null
                        && !AggregateChecker.HasAggregates(select)
                        && (select.GroupBy == null || select.GroupBy.Count == 0))
                    {
                        Expression where = select.Where;
                        SelectExpression selectWithoutWhere = select.SetWhere(null);
                        HashSet<TableAlias> referencedAliases = ReferencedAliasGatherer.Gather(selectWithoutWhere);
                        HashSet<TableAlias> whereAliases = ReferencedAliasGatherer.Gather(where);
                        HashSet<TableAlias> declaredAliases = DeclaredAliasGatherer.Gather(node.Left);
                        referencedAliases.IntersectWith(declaredAliases);
                        whereAliases.IntersectWith(declaredAliases);
                        // the where clause must include an alias from the left of the join
                        if (referencedAliases.Count == 0 && whereAliases.Count > 0)
                        {
                            select = selectWithoutWhere;
                            var pc = ColumnProjector.ProjectColumns(where, select.Columns, select.Alias, DeclaredAliasGatherer.Gather(select.From));
                            select = select.SetColumns(pc.Columns);
                            where = pc.Projector;
                            JoinType jt = (where == null) ? JoinType.CrossJoin : (node.Join == JoinType.CrossApply ? JoinType.InnerJoin : JoinType.LeftOuter);
                            return new JoinExpression(jt, node.Left, select, where);
                        }
                    }
                }
            }

            return node;
        }

        //private static bool CanBeColumn(Expression expr)
        //{
        //    return expr != null && expr.NodeType == (ExpressionType)DbExpressionType.Column;
        //}
    }
}