{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<DateTime_AddYears>": {"name": "DateTime_AddYears", "type": "Function", "params": [{"name": "MyDate", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "AddYears", "paramsArray": ["${MyDate}", 2]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_AddMonths>": {"name": "DateTime_AddMonths", "type": "Function", "params": [{"name": "MyDate", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "AddMonths", "paramsArray": ["${MyDate}", 3]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_AddDays>": {"name": "DateTime_AddDays", "type": "Function", "params": [{"name": "MyDate", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "AddDays", "paramsArray": ["${MyDate}", 1.5]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_AddHours>": {"name": "DateTime_AddHours", "type": "Function", "params": [{"name": "MyDate", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "AddHours", "paramsArray": ["${MyDate}", 1.5]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_AddMinutes>": {"name": "DateTime_AddMinutes", "type": "Function", "params": [{"name": "MyDate", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "AddMinutes", "paramsArray": ["${MyDate}", 1.5]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_AddSeconds>": {"name": "DateTime_AddSeconds", "type": "Function", "params": [{"name": "MyDate", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "AddSeconds", "paramsArray": ["${MyDate}", 65]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}