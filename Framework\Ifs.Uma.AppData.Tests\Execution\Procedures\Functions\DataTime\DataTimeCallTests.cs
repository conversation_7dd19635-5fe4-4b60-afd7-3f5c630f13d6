﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.DataTime
{
    [TestFixture]
    public class DataTimeCallTests : ProcedureTest
    {
        [Test]
        public async Task DateTime_Timestamp0()
        {
            //procedure Function<DateTime_Timestamp0> Timestamp {
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.Timestamp() into Result;
            //        return Result;
            //    }
            //}
            
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            DateTime now = DateTime.Now;
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_Timestamp0", null);
            result.CheckFailure();
                        
            Assert.IsTrue(result.Value is DateTime);
            DateTime dt = (DateTime)result.Value;
            Assert.IsTrue(dt > now.AddSeconds(-5) && dt < now.AddSeconds(10));
        }

        [Test]
        public async Task DateTime_TimestampUtc0()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            DateTime utcNow = DateTime.UtcNow;
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_TimestampUtc0", null);
            result.CheckFailure();

            Assert.IsTrue(result.Value is DateTime);
            DateTime dt = (DateTime)result.Value;
            Assert.IsTrue(dt > utcNow.AddSeconds(-5) && dt < utcNow.AddSeconds(10));
        }

        [Test]
        public async Task DateTime_Timestamp6()
        {
            //procedure Function<DateTime_Timestamp6> Timestamp {
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.Timestamp(2000, 7, 9, "12", 34, 56) into Result;
            //        return Result;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_Timestamp6", null);
            CheckResults(result, new DateTime(2000, 7, 9, 12, 34, 56));
        }

        [Test]
        public async Task DateTime_Date0()
        {
            //procedure Function<DateTime_Date0> Timestamp {
            //    variable Result Date;
            //    execute {
            //        call DateTime.Date() into Result;
            //        return Result;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_Date0", null);
            CheckResults(result, DateTime.Now.Date);
        }

        [Test]
        public async Task DateTime_Date1()
        {
            //procedure Function<DateTime_Date1> Timestamp {
            //    parameter MyDate Timestamp;
            //    variable Result Date;
            //    execute {
            //        call DateTime.Date(MyDate) into Result;
            //        return Result;
            //    }
            //}

            DateTime dt = DateTime.Now;
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDate"] = dt;

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_Date1", param);
            CheckResults(result, dt.Date);
        }

        [Test]
        public async Task DateTime_Date3()
        {
            //procedure Function<DateTime_Date3> Timestamp {
            //    parameter Years Number;
            //    parameter Months Number;
            //    variable Result Date;
            //    execute {
            //        call DateTime.Date(Years, Months, 2) into Result;
            //        return Result;
            //    }
            //}
            
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["Years"] = 2015;
            param["Months"] = 4;

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_Date3", param);
            CheckResults(result, new DateTime(2015, 4, 2));
        }

        [Test]
        public async Task DateTime_Time1()
        {
            //procedure Function<DateTime_Time1> Timestamp {
            //    parameter MyDate Timestamp;
            //    variable Result Time;
            //    execute {
            //        call DateTime.Date(MyDate) into Result;
            //        return Result;
            //    }
            //}

            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDate"] = dt;

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_Time1", param);
            CheckResults(result, new DateTime(1, 1, 1, 12, 34, 56));
        }
        
        [Test]
        public async Task DateTime_Time3()
        {
            //procedure Function<DateTime_Time3> Timestamp {
            //    parameter Hours Number;
            //    parameter Mins Number;
            //    variable Result Time;
            //    execute {
            //        call DateTime.Date(Hours, Mins, 2) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["Hours"] = 8;
            param["Mins"] = 4;

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_Time3", param);
            CheckResults(result, new DateTime(1, 1, 1, 8, 4, 2));
        }

        [Test]
        [TestCase("dd-MMM-yyyy HH:mm", "03-Apr-2022 13:14")]
        [TestCase("dd/MM/yy", "03/04/22")]
        [TestCase("hh:mm:ss", "01:14:15")]
        [TestCase("dd-MMMM-yyyy", "03-April-2022")]
        public async Task DateTime_ToFormattedString(string format, string expected)
        {
            //procedure Function<DateTime_ToFormattedString> Text {
            //    parameter Format Text;
            //    variable Input Timestamp;
            //    variable Result Text;
            //    execute {
            //        call DateTime.Timestamp(2022, 4, 3, 13, 14, 15) into Input;
            //        call DateTime.ToFormattedString(Input, Format) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>
            {
                ["Format"] = format
            };

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_ToFormattedString", param);
            CheckResults(result, expected);
        }

        [Test]
        [TestCase("YYYY", "2022")]
        [TestCase("YY", "22")]
        [TestCase("MON", "Apr")]
        [TestCase("MONTH", "April")]
        [TestCase("DY", "Sun")]
        [TestCase("DD", "03")]
        [TestCase("HH24", "13")]
        [TestCase("HH12", "01")]
        [TestCase("HH", "01")]
        [TestCase("MI", "14")]
        [TestCase("SS", "15")]
        [TestCase("SS", "15")]
        [TestCase("dd-MMM-YY HH24:mm", "03-Apr-22 13:14")]
        public async Task DateTime_ToOracleFormat(string format, string expected)
        {
            //procedure Function<DateTime_ToOracleFormat> Text {
            //    parameter Format Text;
            //    variable Input Timestamp;
            //    variable Result Text;
            //    execute {
            //        call DateTime.Timestamp(2022, 4, 3, 13, 14, 15) into Input;
            //        call DateTime.ToOracleFormat(Input, Format) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>
            {
                ["Format"] = format
            };

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_ToOracleFormat", param);
            CheckResults(result, expected);
        }

        private static void CheckResults(ExecuteResult result, object value)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
            Assert.AreEqual(value, result.Value);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.DataTime.DataTimeCallTestsSchema", null);
        }
    }
}
