﻿using System;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Localization;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Services
{
    internal sealed class MetadataRefreshRequiredNotifier : InitializeStatusWatcher
    {
        private readonly IDialogService _dialogService;

        public MetadataRefreshRequiredNotifier(ILogger logger, ITransactionSyncService transactionSyncService, IDialogService dialogService)
            : base(logger, transactionSyncService, ThreadOption.UIThread)
        {
            _dialogService = dialogService;
        }

        protected override bool OnMatchEventFilter(InitializeStatus status)
        {
            return status == InitializeStatus.Initialized_WithMetaRefreshRequired && !Service.IsInitializing;
        }

        protected override async void OnEvent(InitializeStatus status)
        {
            try
            {
                await _dialogService.WaitForDialogsToClose(ServiceCancelToken);

                if (IsRunning && !Service.IsInitializing && Service.InitializeStatus == InitializeStatus.Initialized_WithMetaRefreshRequired)
                {
                    CustomButtonsResult result = await _dialogService.CustomButtons(Strings.UpdateMetadata, Strings.AppConfigurationChangedMessage, Strings.Update, Strings.Later);
                    if (result == CustomButtonsResult.Positive)
                    {
                        Service.RequestMetadataUpdate();
                    }
                }
            }
            catch (Exception ex)
            {
                LogException(ex);
            }
        }
    }
}
