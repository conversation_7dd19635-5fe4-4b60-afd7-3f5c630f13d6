﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Elements.Groups;
using Ifs.Uma.Framework.UI.Elements.HtmlText;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Ifs.Uma.Framework.UI.Elements.Markdown;
using Ifs.Uma.Framework.UI.Images;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Reporting.Report;
#if SIGNATURE_SERVICE
using Ifs.Uma.Signing;
#endif
using Ifs.Uma.UI.Fields;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.Reporting.Generator
{
    public sealed class ReportGenerator
    {
        private readonly IPageCreator _pageCreator;
        private readonly GroupToFormConverter _groupConverter = new GroupToFormConverter();
        private readonly ListToTableConverter _listConverter = new ListToTableConverter();
        private readonly ImageViewerToItemsConverter _imageViewerToItemsConverter;

        public ReportGenerator(IPageCreator pageCreator, ILogger logger)
        {
            _pageCreator = pageCreator;
            _imageViewerToItemsConverter = new ImageViewerToItemsConverter(logger);
        }
        
        public async Task<ReportDoc> GeneratePageReportAsync(string projectionName, string pageName, PageValues filter, CancellationToken cancelToken)
        {
            MetadataPageNavParam navParam = new MetadataPageNavParam(projectionName, pageName, filter);
            ElementPage page = _pageCreator.CreatePage(navParam) as ElementPage;

            if (page == null)
            {
                throw new ReportingException($"Invalid page used for print '{projectionName}.{pageName}'");
            }

            page.PageData.IsReport = true;
            await page.LoadPageAsync(navParam);

            cancelToken.ThrowIfCancellationRequested();

            return await AddReportItems(page.Title, page.Elements, cancelToken);
        }

        public async Task<ReportDoc> AddReportItems(string title, ElementList elements, CancellationToken cancelToken)
        {
            ReportDoc report = new ReportDoc();
            report.Title = title;

            bool arangeStarted = false;
            int count = 0;

            foreach (ElementBase element in elements.Elements)
            {
                if (element.HasLoaded && element.IsVisible && !element.IsCollapsed)
                {
                    if (element.HasArrange && !arangeStarted)
                    {
                        ReportArrange reportItem = new ReportArrange();
                        report.Items.Add(reportItem);
                        arangeStarted = true;
                    }
                    else if (!element.HasArrange && arangeStarted)
                    {
                        ReportArrangeEnd reportItem = new ReportArrangeEnd();
                        report.Items.Add(reportItem);
                        arangeStarted = false;
                    }
                    else if (element.HasArrange && arangeStarted)
                    {
                        count++;
                        if (count == 2)
                        {
                            count = 0;
                            ReportArrangeBreak reportItem = new ReportArrangeBreak();
                            report.Items.Add(reportItem);
                        }
                    }

                    await AddReportItemsForElementAsync(report, cancelToken, element);

                    cancelToken.ThrowIfCancellationRequested();
                }
            }

            return report;
        }

        private async Task AddReportItemsForElementAsync(ReportDoc report, CancellationToken cancelToken, ElementBase element)
        {
            if (element is GroupElement groupElement)
            {
                await _groupConverter.GenerateForGroupAsync(report, cancelToken, groupElement);
            }
            else if (element is ListElement listElement)
            {
                await _listConverter.GenerateForListAsync(report, cancelToken, listElement);
            }
            else if (element is ImageViewerElement imageViewerElement)
            {
                await _imageViewerToItemsConverter.GenerateForItemsAsync(report, cancelToken, imageViewerElement);
            }
            else if (element is MarkdownElement markdownElement)
            {
                GenerateMarkdown(report, markdownElement);
            }
            else if (element is HtmlTextElement htmlTextElement)
            {
                ReportHtmlItem reportItem = new ReportHtmlItem();
                reportItem.Html = htmlTextElement.Html;
                report.Items.Add(reportItem);
            }
        }

        private void GenerateMarkdown(ReportDoc report, MarkdownElement element)
        {
            ReportMarkdownItem reportItem = new ReportMarkdownItem
            {
                Header = element.Header,
                MarkdownText = element.Markdown.Text,
                TextColor = element.Markdown.TextColor,
                BackgroundColor = element.Markdown.BackgroundColor
            };

            report.Items.Add(reportItem);
        }

#if SIGNATURE_SERVICE
#pragma warning disable
        public object AddJsonReportItems(string assistantName, ElementList elements, CancellationToken cancelToken)
        {
            dynamic signedData = new ExpandoObject();
            signedData.signedData = new List<object>();

            foreach (ElementBase element in elements.Elements)
            {
                dynamic signedDataItem = new ExpandoObject();
                if (element.IsVisible)
                {
                    if (element is ListElement listElement && !listElement.InRepeatingSection)
                    {
                        signedDataItem.id = assistantName + "-" + listElement.ListName;
                        signedDataItem.type = "list";
                        signedDataItem.label = listElement.ListLabel;
                        signedDataItem.luName = listElement.GetSignatureEntity();

                        signedData.signedData.Add(GetListContent(signedDataItem, listElement));
                    }
                    else if (element is GroupElement groupElement)
                    {
                        signedDataItem.id = assistantName + "-" + groupElement.Name;
                        signedDataItem.type = "group";
                        signedDataItem.label = groupElement.Header;
                        signedDataItem.luName = groupElement.GetSignatureEntity();

                        dynamic dataItemContent = new ExpandoObject();
                        dataItemContent.contents = new Dictionary<string, string>();

                        List<Field> hashFields = new List<Field>();
                        Dictionary<string, string>[] dataItemJson = new Dictionary<string, string>[1];

                        foreach (Field groupField in groupElement.Form.Fields)
                        {
                            if (groupField.IsVisible)
                            {
                                if (groupField.FieldType.Contains("Date") && groupField.Value != null)
                                {
                                    object value = groupField.Value;

                                    DateTime date = ObjectConverter.ToDateTime(value);

                                    if (date != null)
                                    {
                                        dataItemContent.contents.Add(groupField.Attribute, string.IsNullOrWhiteSpace(date.ToString(Constants.TimestampFormat)) ? null : date.ToString(Constants.TimestampFormat));
                                    }
                                }
                                else
                                {
                                    dataItemContent.contents.Add(groupField.Attribute, string.IsNullOrWhiteSpace(groupField.Value?.ToString()) ? null : groupField.Value?.ToString());
                                }

                                if (!groupField.ExcludeFromSignatureMapping)
                                {
                                    hashFields.Add(groupField);
                                }
                            }
                        }

                        bool isSigned = false;
                        isSigned = groupElement.HasSignatureChanges();

                        if (isSigned)
                        {
                            dataItemContent.contents.Add("hashContent", "true");
                        }

                        Dictionary<string, string>[] hashOrderItems = new Dictionary<string, string>[hashFields.Count];
                        int hashCount = 0;
                        foreach (Field hashfield in hashFields)
                        {
                            dynamic data = new ExpandoObject();
                            data.contents = new Dictionary<string, string>();

                            data.contents.Add("field", hashfield.Attribute);

                            if (!string.IsNullOrWhiteSpace(hashfield.SignatureMappingAttribute))
                            {
                                data.contents.Add("attribute", hashfield.SignatureMappingAttribute);
                            }

                            hashOrderItems[hashCount] = data.contents;
                            hashCount++;
                        }

                        if (groupElement.GetSignatureEntity() != null)
                        {
                            signedDataItem.hashOrderMapping = hashOrderItems.ToArray();
                            dataItemJson[0] = dataItemContent.contents;
                            signedDataItem.contents = dataItemJson.ToArray();
                        }
                        else
                        {
                            // Remove luname in the json if there is no signature entity defined
                            ((IDictionary<string, object>)signedDataItem).Remove("luName");
                            signedDataItem.contents = dataItemContent.contents;
                        }

                        if (signedDataItem.contents != null)
                        {
                            signedData.signedData.Add(signedDataItem);
                        }
                    }
                    else if (element is RepeatingSectionElement repeatingElement)
                    {
                        signedData = GetRepeatingSections(assistantName, signedData, repeatingElement);
                    }
                }                
            }

            object[] jsonData = new object[signedData.signedData.Count];
            int count = 0;
            foreach (var item in signedData.signedData)
            {
                jsonData[count] = item;
                count++;
            }

            return jsonData;
        }
#pragma warning restore

        private dynamic GetRepeatingSections(string assistantName, dynamic signedData, RepeatingSectionElement repeatingElement)
        {
            int sectionCount = 1;

            foreach (ElementBase elementBase in repeatingElement.Elements.Elements)
            {
                if (elementBase.IsVisible && elementBase.InRepeatingSection)
                {
                    if (elementBase is MarkdownElement markdown)
                    {
                        dynamic repeatingDataItem = new ExpandoObject();
                        repeatingDataItem.id = assistantName + "-" + "StepSection-" + sectionCount.ToString() + "-MarkdownText";
                        repeatingDataItem.type = "markdowntext";
                        repeatingDataItem.content = MarkdownElement.GetHtmlText(markdown.Markdown.Text);

                        signedData.signedData.Add(repeatingDataItem);
                    }
                    else if (elementBase is HtmlTextElement htmlText)
                    {
                        dynamic repeatingDataItem = new ExpandoObject();
                        repeatingDataItem.id = assistantName + "-" + "StepSection-" + sectionCount.ToString() + "-HtmlText";
                        repeatingDataItem.type = "htmltext";
                        repeatingDataItem.content = htmlText.Html;

                        signedData.signedData.Add(repeatingDataItem);
                    }
                    else if (elementBase is ListElement sectionListElement)
                    {
                        dynamic listDataItem = new ExpandoObject();
                        listDataItem.id = assistantName + "-" + "StepSection-" + sectionCount.ToString() + "-" + sectionListElement.ListName;
                        listDataItem.type = "list";
                        listDataItem.label = sectionListElement.ListLabel;
                        listDataItem.luName = sectionListElement.GetSignatureEntity();

                        signedData.signedData.Add(GetListContent(listDataItem, sectionListElement));
                        sectionCount++;
                    }
                }
            }

            return signedData;
        }

#pragma warning disable
        private dynamic GetListContent(dynamic listDataItem, ListElement listElement)
        {
            int signedDataCount = 0;
            Dictionary<string, string>[] signedDataContent = new Dictionary<string, string>[(listElement.ListData.Items.Count > 0 ? listElement.ListData.Items.Count : 0)];
            List<CpiField> hashFields = new List<CpiField>();

            if (listElement.ListData.Items.Any())
            {
                foreach (ListElementItem listItem in listElement.ListData.Items)
                {
                    bool isSigned = false;
                    hashFields.Clear();

                    isSigned = listElement.HasSignatureChanges(listItem);

                    CardDefItem[] cardDefItems = listElement.ListData.ItemCardDef.Items.ToArray();

                    dynamic dataItemContent = new ExpandoObject();
                    dataItemContent.contents = new Dictionary<string, string>();

                    foreach (CardDefItem cardDefItem in cardDefItems)
                    {
                        if (cardDefItem.ControlType == CardDefItem.ControlTypes.HeaderLabel)
                        {
                            string substr = cardDefItem.ConverterParameter.ToString().Substring(cardDefItem.ConverterParameter.ToString().IndexOf("{") + 1);
                            string label = substr.TrimEnd('}');
                            dataItemContent.contents.Add(label, string.IsNullOrWhiteSpace(listElement.ListData.ItemCardDef.GetHeaderText(listItem)) ? null : listElement.ListData.ItemCardDef.GetHeaderText(listItem));
                        }
                        else if (listItem.Record[cardDefItem.Field.Attribute] != null && (cardDefItem.Field.Datatype == CpiDataType.Timestamp || cardDefItem.Field.Datatype == CpiDataType.Date))
                        {
                            DateTime date = ObjectConverter.ToDateTime(listItem.Record[cardDefItem.Field.Attribute]);
                            dataItemContent.contents.Add(cardDefItem.Field.Attribute, string.IsNullOrWhiteSpace(date.ToString(Constants.TimestampFormat)) ? null : date.ToString(Constants.TimestampFormat));
                        }
                        else
                        {
                            dataItemContent.contents.Add(cardDefItem.Field.Attribute, string.IsNullOrWhiteSpace(cardDefItem.GetDisplayValue(listItem)) ? null : cardDefItem.GetDisplayValue(listItem));
                        }
                    }

                    if (isSigned)
                    {
                        dataItemContent.contents.Add("hashContent", "true");
                    }

                    signedDataContent[signedDataCount] = dataItemContent.contents;
                    signedDataCount++;
                }
            }
            
            if (listElement.ListData.ItemCardDef.Items.Any())
            {
                foreach (var cardDefItem in listElement.ListData.ItemCardDef.Items.Select(cardDefItem => cardDefItem.Field))
                {
                    if (cardDefItem != null && !cardDefItem.ExcludeFromSignatureMapping.HasValue)
                    {
                        hashFields.Add(cardDefItem);
                    }
                }
            }

            Dictionary<string, string>[] hashOrderItems = new Dictionary<string, string>[hashFields.Count];
            int hashCount = 0;

            foreach (CpiField hashfield in hashFields)
            {
                dynamic data = new ExpandoObject();
                data.contents = new Dictionary<string, string>();

                data.contents.Add("field", hashfield.Attribute);

                if (!string.IsNullOrWhiteSpace(hashfield.SignatureMappingAttribute))
                {
                    data.contents.Add("attribute", hashfield.SignatureMappingAttribute);
                }
                
                hashOrderItems[hashCount] = data.contents;
                hashCount++;
            }

            if (listElement.GetSignatureEntity() != null)
            {
                listDataItem.hashOrderMapping = hashOrderItems.ToArray();
                listDataItem.contents = signedDataContent.ToArray();
            }
            else
            {
                // Remove luname in the json if there is no signature entity defined
                ((IDictionary<string, object>)listDataItem).Remove("luName");
                listDataItem.contents = signedDataContent;
            }

            return listDataItem;
        }
#pragma warning restore
#endif
    }
}
