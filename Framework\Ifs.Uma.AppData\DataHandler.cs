﻿using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.AppData.Permissions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData
{
    public interface IDataHandler
    {
        Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken);
        Task<EntityQueryResult> GetOnlineRecordsWithReferencesAsync(EntityQuery query, CancellationToken cancelToken);
        Task<ExecuteResult> GetBinaryDataAsync(string projectionName, ObjPrimaryKey primaryKey, string attribute, CancellationToken cancelToken);
        Task<ExecuteResult> SetBinaryDataAsync(string projectionName, ObjPrimaryKey primaryKey, string attribute, Stream dataStream, CancellationToken cancelToken);
        Task<ExecuteResult> GetAggregateAsync(AggregateQuery query);

        Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken = default(CancellationToken), string entitySetName = "");
        Task<ExecuteResult> EntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> EntityUpdateAsync(string projectionName, RemoteRow row, IEnumerable<string> changedMembers, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> EntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> PerformActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> PerformBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> PerformFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> PerformBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken));

        CpiCrudType GetCrudType(string projectionName, string entityName);
    }

    public sealed class DataHandler : DataAccessor<FwDataContext>, IDataHandler
    {
        private static readonly TimeSpan DefaultWaitForTransactionsTimeout = TimeSpan.FromSeconds(20);
        private const int DefaultOfflineTake = 1000;

        private readonly IMetadata _metadata;
        private readonly IProcedureExecutor _procExecutor;
        private readonly ICachePreparer _cache;
        private readonly IOnlineDataHandler _onlineData;
        private readonly ITransactionWaiter _transactionWaiter;
        private readonly IAppPermissions _permissions;
        private readonly ConcurrentDictionary<string, CpiCrudType> _entityCrudTypes = new ConcurrentDictionary<string, CpiCrudType>();

        public DataHandler(IDataContextProvider db, ILogger logger, IPerfLogger perfLogger, IMetadata metadata, 
            IProcedureExecutor procExecutor, ICachePreparer cache, IOnlineDataHandler onlineData, ITransactionWaiter transactionWaiter, IAppPermissions permissions)
            : base(db, logger, perfLogger)
        {
            _metadata = metadata;
            _procExecutor = procExecutor;
            _cache = cache;
            _onlineData = onlineData;
            _transactionWaiter = transactionWaiter;
            _permissions = permissions;
        }

        #region Data Retrieval

        public async Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken)
        {
            if (query.DataSource is FunctionDataSource funcDataSource)
            {
                return await GetFunctionRecordsAsync(query, funcDataSource, cancelToken);
            }

            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(query.DataSource.EntityName);

            if (syncPolicy == EntitySyncPolicy.OnlineOnly)
            {
                EntityQueryResult onlineResult = await _onlineData.GetRecordsAsync(query, cancelToken);

                if (onlineResult.Records != null)
                {
                    await SlowLoadReferences(query, onlineResult.Records);
                }

                return onlineResult;
            }
            
            PreparedEntityQuery preparedQuery = query.Prepare(DefaultOfflineTake);
            query = preparedQuery.Query;

            if (syncPolicy == EntitySyncPolicy.ClientCache)
            {
                // If this is a request for specific records, then we don't update the entire cache for performance reasons as this is quicker
                // For example, if an LOV requests just one record, there's no need to fetch the entire data set and update the cache
                // Just need to get the specific set of records from the server and persist in the local table
                if (query.Filters.Any())
                {
                    EntityQueryResult localRecords = await GetLocalRecords(query, preparedQuery, cancelToken);

                    if (!localRecords.Records.Any())
                    {
                        EntityQuery onlineQuery = query.Clone();

                        // Special case to set entity set name when performing an online server call for a cache type entity
                        query.DataSource.EntitySetName = _metadata.GetFirstEntitySet(query.DataSource.ProjectionName, query.DataSource.EntityName);

                        EntityQueryResult onlineRecords = await _onlineData.GetRecordsAsync(onlineQuery, cancelToken);

                        RemoteRow[] rows = onlineRecords?.Records?.Select(x => x.Row)?.ToArray();
                        if (rows != null)
                        {
                            await WithDataContextAsync(ctx =>
                            {
                                ctx.UpsertCacheTable(onlineQuery.DataSource.Table, rows);
                            });
                        }
                    }
                }
                else if (await _cache.PrepareCacheAsync(preparedQuery, cancelToken) == PrepareCacheResult.Offline)
                {
                    return EntityQueryResult.Offline;
                }
            }

            EntityQueryResult result = await GetLocalRecords(query, preparedQuery, cancelToken);

            if (result.Records.Count == 0 &&
                syncPolicy == EntitySyncPolicy.ClientCache && 
                query.Take.HasValue && query.Take.Value == 1)
            {
                // If we are doing a query for one record from the cache and it does not exist then 
                // it is likely a new record came to the device referencing a record not 
                // in the cache so we should try add it

                if (await _cache.AddToCacheAsync(query, cancelToken) == PrepareCacheResult.Ready)
                {
                    result = await GetLocalRecords(query, preparedQuery, cancelToken);
                }
            }

            return result;
        }

        public async Task<EntityQueryResult> GetOnlineRecordsWithReferencesAsync(EntityQuery query, CancellationToken cancelToken)
        {
            EntityQueryResult onlineResult = await _onlineData.GetRecordsAsync(query, cancelToken);

            if (onlineResult.Records != null)
            {
                await SlowLoadReferences(query, onlineResult.Records);
            }

            return onlineResult;
        }

        private async Task<EntityQueryResult> GetLocalRecords(EntityQuery query, PreparedEntityQuery preparedQuery, CancellationToken token)
        {
            string name = "GetRecords<" + (query.DataSource.EntitySetName ?? query.DataSource.EntityName) + ">";
            return await WithDataContextAsync(ctx =>
            {
                IReadOnlyList<EntityRecord> records = ctx.Query(preparedQuery).ToArray();

                bool hasMoreResults = false;
                if (query.Take.HasValue && records.Count >= query.Take.Value)
                {
                    EntityQuery countQuery = query.Clone();
                    countQuery.Skip = countQuery.Skip.GetValueOrDefault(0) + records.Count;
                    countQuery.Take = 1;
                    hasMoreResults = ctx.Count(countQuery) > 0;
                }

                return new EntityQueryResult(query, records, hasMoreResults);
            }, name);
        }

        public async Task<EntityQueryResult> GetFunctionRecordsAsync(EntityQuery query, FunctionDataSource funcDataSource, CancellationToken cancelToken)
        {
            query = query.Clone();

            // Online function calls that return a collection of records should be handled differently, to allow pagination
            ProcSyncPolicy syncPolicy = _metadata.GetFunctionSyncPolicy(funcDataSource.ProjectionName, funcDataSource.FunctionName);
            if (syncPolicy == ProcSyncPolicy.Online)
            {
                EntityQueryResult onlineResult = await _onlineData.GetFunctionRecordsAsync(query, funcDataSource.ParameterValues, cancelToken);
                
                if (onlineResult.Records != null)
                {
                    await SlowLoadReferences(query, onlineResult.Records);
                }

                return onlineResult;
            }

            ExecuteResult result = await PerformFunctionAsync(funcDataSource.ProjectionName, funcDataSource.FunctionName, funcDataSource.ParameterValues, cancelToken).ConfigureAwait(false);

            if (result.IsOffline)
            {
                return EntityQueryResult.Offline;
            }

            result.CheckFailure();

            List<EntityRecord> records = new List<EntityRecord>();

            IEnumerable rows = result.Value as IEnumerable;
            if (rows != null)
            {
                foreach (var row in rows.OfType<RemoteRow>())
                {
                    if (row != null)
                    {
                        records.Add(new EntityRecord(row, null));
                    }
                }
            }
            else
            {
                RemoteRow row = result.Value as RemoteRow;
                if (row != null)
                {
                    records.Add(new EntityRecord(row, null));
                }
            }

            await SlowLoadReferences(query, records);

            return query.ApplyTo(records);
        }

        private async Task SlowLoadReferences(EntityQuery query, IEnumerable<EntityRecord> records)
        {
            IReadOnlyList<string> requiredExpands = query.GetRequiredExpands();

            if (requiredExpands.Count > 0)
            {
                await WithDataContextAsync(ctx =>
                {
                    foreach (string refName in requiredExpands)
                    {
                        CpiReference reference = _metadata.FindReference(query.DataSource.ProjectionName, query.DataSource.EntityName, refName);
                        if (reference != null)
                        {
                            SlowLoadReference(ctx, records, refName, reference);
                        }
                    }
                });
            }
        }
        
        private void SlowLoadReference(FwDataContext ctx, IEnumerable<EntityRecord> records, string refName, CpiReference reference)
        {
            Dictionary<ObjKey, List<EntityRecord>> refsToUpdate = new Dictionary<ObjKey, List<EntityRecord>>();

            foreach (EntityRecord record in records)
            {
                if (record.References != null && record.References.ContainsKey(refName))
                {
                    // Reference already loaded
                    continue;
                }

                ObjKey refKey = _metadata.GetReferenceKey(record.Row, reference, false);
                if (refKey == null)
                {
                    record.UpdateReference(refName, null);
                }
                else
                {
                    List<EntityRecord> keyRecords;
                    if (!refsToUpdate.TryGetValue(refKey, out keyRecords))
                    {
                        keyRecords = new List<EntityRecord>();
                        refsToUpdate[refKey] = keyRecords;
                    }

                    keyRecords.Add(record);
                }
            }

            if (refsToUpdate.Count > 0)
            {
                // Ideally we would only select the columns that are needed but there is curently no easy way 
                // to find out all columns that would be needed to complete the query.ApplyTo(records) step
                IReadOnlyDictionary<ObjKey, object> loadedRefRows = ctx.FindAll(refsToUpdate.Keys.ToArray());
                foreach (KeyValuePair<ObjKey, List<EntityRecord>> kvp in refsToUpdate)
                {
                    RemoteRow row = null;
                    if (loadedRefRows.TryGetValue(kvp.Key, out object loadedRow))
                    {
                        row = loadedRow as RemoteRow;
                    }

                    foreach (EntityRecord record in kvp.Value)
                    {
                        record.UpdateReference(refName, row);
                    }
                }
            }
        }

        public async Task<ExecuteResult> GetBinaryDataAsync(string projectionName, ObjPrimaryKey primaryKey, string attribute, CancellationToken cancelToken)
        {
            if (primaryKey == null) throw new ArgumentNullException(nameof(primaryKey));
            if (attribute == null) throw new ArgumentNullException(nameof(attribute));

            string entityName = RemoteNaming.ToEntityName(primaryKey.Table.TableName);
            EntityDataSource dataSource = EntityDataSource.FromEntity(_metadata, projectionName, entityName);
            AttributePathInfo attributeInfo = AttributePathInfo.Get(_metadata, projectionName, entityName, attribute);

            if (dataSource == null || attributeInfo == null)
            {
                return ExecuteResult.None;
            }

            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(entityName);

            if (syncPolicy == EntitySyncPolicy.OnlineOnly)
            {
                return await GetBinaryDataOnline(dataSource, primaryKey, attributeInfo, cancelToken);
            }

            if (await _cache.PrepareCacheAsync(dataSource, cancelToken) == PrepareCacheResult.Offline)
            {
                return ExecuteResult.Offline;
            }

            return await GetBinaryDataLocal(dataSource, primaryKey, attributeInfo);
        }
        
        private async Task<ExecuteResult> GetBinaryDataOnline(EntityDataSource dataSource, ObjPrimaryKey primaryKey, AttributePathInfo attributeInfo, CancellationToken cancelToken)
        {
            EntityQuery query = new EntityQuery(dataSource);
            query.Select(attributeInfo.Path);
            query.SetFilter(primaryKey);
            query.Take = 1;

            EntityQueryResult result = await _onlineData.GetRecordsAsync(query, cancelToken);
            EntityRecord record = result.Records.FirstOrDefault();

            if (record != null && record[attributeInfo] is byte[] bytes)
            {
                return new ExecuteResult(new MemoryStream(bytes));
            }

            return ExecuteResult.None;
        }

        private async Task<ExecuteResult> GetBinaryDataLocal(EntityDataSource dataSource, ObjPrimaryKey primaryKey, AttributePathInfo attributeInfo)
        {
            EntityQuery query = new EntityQuery(dataSource);
            query.Select(attributeInfo.Path);
            query.SetFilter(primaryKey);
            query.Take = 1;
            
            PreparedEntityQuery preparedQuery = query.Prepare(DefaultOfflineTake);

            string name = "GetBinaryData<" + (query.DataSource.EntitySetName ?? query.DataSource.EntityName) + ">";
            EntityRecord record = await WithDataContextAsync(ctx => ctx.Query(preparedQuery).FirstOrDefault(), name);

            if (record != null && record[attributeInfo] is byte[] bytes)
            {
                return new ExecuteResult(new MemoryStream(bytes));
            }

            return ExecuteResult.None;
        }

        public async Task<ExecuteResult> GetAggregateAsync(AggregateQuery query)
        {
            if (query == null) throw new ArgumentNullException(nameof(query));

            if (query.From.DataSource is FunctionDataSource funcDataSource)
            {
                return ExecuteResult.None;
            }
            
            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(query.From.DataSource.EntityName);

            if (syncPolicy == EntitySyncPolicy.OnlineOnly)
            {
                return ExecuteResult.None;
            }
            
            PreparedAggregateQuery preparedQuery = query.Prepare();

            if (!await _cache.IsCacheReadyAsync(query.From.DataSource.ProjectionName, query.From.DataSource.EntityName))
            {
                return ExecuteResult.None;
            }
            
            string name = query.AggregateType + "Records<" + (query.From.DataSource.EntitySetName ?? query.From.DataSource.EntityName) + ">";
            return await WithDataContextAsync(ctx => new ExecuteResult(ctx.Aggregate(preparedQuery)), name);
        }

        #endregion

        #region Data Updates
        
        public async Task<ExecuteResult> SetBinaryDataAsync(string projectionName, ObjPrimaryKey primaryKey, string attribute, Stream dataStream, CancellationToken cancelToken)
        {
            if (primaryKey == null) throw new ArgumentNullException(nameof(primaryKey));
            if (attribute == null) throw new ArgumentNullException(nameof(attribute));
            if (dataStream == null) throw new ArgumentNullException(nameof(dataStream));

            EntityDataSource dataSource = EntityDataSource.FromEntity(_metadata, projectionName, RemoteNaming.ToEntityName(primaryKey.Table.TableName));
            EntityQuery query = new EntityQuery(dataSource);
            query.Select(new string[] { nameof(RemoteRow.ObjKey) });
            query.SetFilter(primaryKey);
            query.Take = 1;

            EntityQueryResult result = await GetRecordsAsync(query, cancelToken);

            if (result.DataSourceOffline)
            {
                return ExecuteResult.Offline;
            }

            cancelToken.ThrowIfCancellationRequested();

            RemoteRow row = result.Records.FirstOrDefault()?.Row;

            if (row == null)
            {
                return ExecuteResult.None;
            }
            
            using (MemoryStream ms = new MemoryStream())
            {
                await dataStream.CopyToAsync(ms, 81920 /* default */, cancelToken);
                row[attribute] = ms.ToArray();
            }

            cancelToken.ThrowIfCancellationRequested();

            return await EntityUpdateAsync(projectionName, row, new[] { attribute }, cancelToken);
        }

        public async Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken = default(CancellationToken), string entitySetName = "")
        {
            if (entityName == null) throw new ArgumentNullException(nameof(entityName));

            CpiEntity entity = _metadata.FindEntity(projectionName, entityName);
            CpiCrudType updateType = GetCrudType(projectionName, entityName);
            if (entity != null && !updateType.CanUse(CpiCrudType.Create))
            {
                return ExecuteResult.None;
            }
            
            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(entityName);
            if (syncPolicy == EntitySyncPolicy.OnlineOnly)
            {
                ExecuteResult result = await _onlineData.EntityPrepareAsync(projectionName, entityName, cancelToken, entitySetName);

                if (result.Failed)
                {
                    return result;
                }

                RemoteRow createdRow = result.Value as RemoteRow;
                if (createdRow != null)
                {
                    result = await _procExecutor.AfterEntityPrepareAsync(projectionName, createdRow, cancelToken);
                }

                return result;
            }

            return await _procExecutor.EntityPrepareAsync(projectionName, entityName, cancelToken);
        }

        public async Task<ExecuteResult> EntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null) throw new ArgumentNullException(nameof(row));
            row = ExecutionUtils.CloneRow(_metadata, row);

            CpiEntity entity = ValidateUpdateType(projectionName, row, CpiCrudType.Create);

            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(entity.Name);
            if (syncPolicy == EntitySyncPolicy.OnlineOnly)
            {
                IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                string transactionGroup = metaTable.GetTransactionGroup(row);

                ExecuteResult result = await WaitForTransactions(transactionGroup);
                if (result != null)
                {
                    return result;
                }

                result = await _onlineData.EntityInsertAsync(projectionName, row, cancelToken);

                if (result.Failed)
                {
                    return result;
                }

                RemoteRow insertedRow = result.Value as RemoteRow;
                insertedRow.EntitySetName = row.EntitySetName;
                if (insertedRow != null)
                {
                    result = await _procExecutor.AfterEntityInsertAsync(projectionName, insertedRow, cancelToken);
                }

                return result;
            }

            return await _procExecutor.EntityInsertAsync(projectionName, row, cancelToken);
        }

        public async Task<ExecuteResult> EntityUpdateAsync(string projectionName, RemoteRow row, IEnumerable<string> changedMembers, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null) throw new ArgumentNullException(nameof(row));
            row = ExecutionUtils.CloneRow(_metadata, row);
            changedMembers = changedMembers.ToArray();

            CpiEntity entity = ValidateUpdateType(projectionName, row, CpiCrudType.Update);

            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(entity.Name);
            if (syncPolicy == EntitySyncPolicy.OnlineOnly)
            {
                IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                string transactionGroup = metaTable.GetTransactionGroup(row);

                ExecuteResult result = await WaitForTransactions(transactionGroup);
                if (result != null)
                {
                    return result;
                }

                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, row);
                row.PrimaryKeyString = key.ToFormattedKeyRef(projectionName);
                result = await _onlineData.EntityUpdateAsync(projectionName, row, changedMembers, cancelToken);

                if (result.Failed)
                {
                    return result;
                }

                RemoteRow updatedRow = result.Value as RemoteRow;
                updatedRow.EntitySetName = row.EntitySetName;
                if (updatedRow != null)
                {
                    result = await _procExecutor.AfterEntityUpdateAsync(projectionName, updatedRow, cancelToken);
                }

                return result;
            }

            return await _procExecutor.EntityUpdateAsync(projectionName, row, changedMembers, cancelToken);
        }

        public async Task<ExecuteResult> EntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null) throw new ArgumentNullException(nameof(row));
            row = ExecutionUtils.CloneRow(_metadata, row);

            CpiEntity entity = ValidateUpdateType(projectionName, row, CpiCrudType.Delete);

            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(entity.Name);
            if (syncPolicy == EntitySyncPolicy.OnlineOnly)
            {
                IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                string transactionGroup = metaTable.GetTransactionGroup(row);

                ExecuteResult result = await WaitForTransactions(transactionGroup);
                if (result != null)
                {
                    return result;
                }

                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, row);
                row.PrimaryKeyString = key.ToFormattedKeyRef(projectionName);
                result = await _onlineData.EntityDeleteAsync(projectionName, row, cancelToken);

                if (result.Failed)
                {
                    return result;
                }
                
                return await _procExecutor.AfterEntityDeleteAsync(projectionName, row, cancelToken);
            }

            return await _procExecutor.EntityDeleteAsync(projectionName, row, cancelToken);
        }

        public async Task<ExecuteResult> PerformActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken))
        {
            parameters = ExecutionUtils.CloneParameters(_metadata, parameters);
            CpiAction action = _metadata.FindAction(projectionName, name);

            ValidateParameters(name, action?.Parameters, parameters);

            ProcSyncPolicy syncPolicy = _metadata.GetActionSyncPolicy(projectionName, name);
            if (syncPolicy == ProcSyncPolicy.Online)
            {
                string transactionGroup = action.GetActionTransactionGroup(parameters);

                if (transactionGroup != null)
                {                 
                    ExecuteResult transResult = await WaitForTransactions(transactionGroup);
                    if (transResult != null)
                    {
                        return transResult;
                    }
                }

                ExecuteResult result = await _onlineData.CallActionAsync(projectionName, name, parameters, cancelToken);
                
                if (result.Failed)
                {
                    return result;
                }

                return await _procExecutor.AfterCallActionAsync(projectionName, name, parameters, result.Value, cancelToken);
            }

            return await _procExecutor.CallActionAsync(projectionName, name, parameters, cancelToken);
        }

        public async Task<ExecuteResult> PerformBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null) throw new ArgumentNullException(nameof(row));
            row = ExecutionUtils.CloneRow(_metadata, row);
            parameters = ExecutionUtils.CloneParameters(_metadata, parameters);
            string entityName = RemoteNaming.ToEntityName(row.TableName);
            CpiAction action = _metadata.FindBoundAction(projectionName, entityName, name);

            ValidateParameters(name, action?.Parameters, parameters);

            ProcSyncPolicy syncPolicy = _metadata.GetBoundActionSyncPolicy(projectionName, entityName, name);
            if (syncPolicy == ProcSyncPolicy.Online)
            {
                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, row);

                string transactionGroup = action.GetBoundTransactionGroup(row, key, parameters);

                if (transactionGroup != null)
                {
                    ExecuteResult transResult = await WaitForTransactions(transactionGroup);
                    if (transResult != null)
                    {
                        return transResult;
                    }
                }
                row.PrimaryKeyString = key.ToFormattedKeyRef(projectionName);
                ExecuteResult result = await _onlineData.CallBoundActionAsync(projectionName, row, name, parameters, cancelToken);

                if (result.Failed)
                {
                    return result;
                }

                return await _procExecutor.AfterCallBoundActionAsync(projectionName, row, name, parameters, result.Value, cancelToken);
            }

            return await _procExecutor.CallBoundActionAsync(projectionName, row, name, parameters, cancelToken);
        }

        public async Task<ExecuteResult> PerformFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken))
        {
            parameters = ExecutionUtils.CloneParameters(_metadata, parameters);
            CpiFunction function = _metadata.FindFunction(projectionName, name);

            ValidateParameters(name, function?.Parameters, parameters);

            ProcSyncPolicy syncPolicy = _metadata.GetFunctionSyncPolicy(projectionName, name);
            if (syncPolicy == ProcSyncPolicy.Online)
            {
                ExecuteResult result = await _onlineData.CallFunctionAsync(projectionName, name, parameters, cancelToken);
                
                if (result.Failed)
                {
                    return result;
                }

                return await _procExecutor.AfterCallFunctionAsync(projectionName, name, parameters, result.Value, cancelToken);
            }

            return await _procExecutor.CallFunctionAsync(projectionName, name, parameters, cancelToken);
        }

        public async Task<ExecuteResult> PerformBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null) throw new ArgumentNullException(nameof(row));
            row = ExecutionUtils.CloneRow(_metadata, row);
            parameters = ExecutionUtils.CloneParameters(_metadata, parameters);
            string entityName = RemoteNaming.ToEntityName(row.TableName);
            CpiFunction function = _metadata.FindBoundFunction(projectionName, entityName, name);

            ValidateParameters(name, function?.Parameters, parameters);

            ProcSyncPolicy syncPolicy = _metadata.GetBoundFunctionSyncPolicy(projectionName, entityName, name);
            if (syncPolicy == ProcSyncPolicy.Online)
            {
                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, row);

                string transactionGroup = function.GetBoundTransactionGroup(row, key, parameters);

                if (transactionGroup != null)
                {
                    ExecuteResult transResult = await WaitForTransactions(transactionGroup);
                    if (transResult != null)
                    {
                        return transResult;
                    }
                }
                row.PrimaryKeyString = key.ToFormattedKeyRef(projectionName);
                ExecuteResult result = await _onlineData.CallBoundFunctionAsync(projectionName, row, name, parameters, cancelToken);

                if (result.Failed)
                {
                    return result;
                }

                return await _procExecutor.AfterCallBoundFunctionAsync(projectionName, row, name, parameters, result.Value, cancelToken);
            }

            return await _procExecutor.CallBoundFunctionAsync(projectionName, row, name, parameters, cancelToken);
        }

        private void ValidateParameters(string methodName, CpiParam[] expectedParams, IReadOnlyDictionary<string, object> givenParams)
        {
            // Parameters are only sent in Update 7 and above so this will not be helpful for older versions
            if (expectedParams != null)
            {
                if (expectedParams.Length != givenParams.Count)
                {
                    Logger.Current.Error($"Wrong number of parameters passed to {methodName}");
                    return;
                }

                foreach (CpiParam param in expectedParams)
                {
                    if (!givenParams.ContainsKey(param.Name))
                    {
                        Logger.Current.Error($"{methodName} expects a parameter by the name {param.Name}");
                        return;
                    }

                    object value = givenParams[param.Name];

                    if (value != null && !MetadataExtensions.TryConvertToType(param.DataType, value, out _))
                    {
                        Logger.Current.Error($"Wrong data type in parameter {param.Name} passed to {methodName}");
                    }
                }
            }
        }

        private async Task<ExecuteResult> WaitForTransactions(string transactionGroup)
        {
            TransactionWaitResult waitResult = await _transactionWaiter.WaitForTransactionsToSend(transactionGroup, DefaultWaitForTransactionsTimeout);

            if (waitResult == TransactionWaitResult.Offline)
            {
                return ExecuteResult.Offline;
            }
            else if (waitResult == TransactionWaitResult.Ready)
            {
                return null;
            }

            try
            {
                throw new UnfinishedTransactionsException(waitResult);
            }
            catch (Exception ex)
            {
                return new ExecuteResult(ex);
            }
        }

        private CpiEntity ValidateUpdateType(string projectionName, RemoteRow row, CpiCrudType type)
        {
            CpiEntity entity = _metadata.FindEntity(projectionName, row);
            CpiCrudType updateType = GetCrudType(projectionName, RemoteNaming.ToEntityName(row.TableName));
            
            if (!updateType.CanUse(type))
            {
                throw new ExecutionException($"Update type '{type}' not supported on entity '{projectionName}.{entity?.Name}' table '{row.TableName}'");
            }

            return entity;
        }

        public CpiCrudType GetCrudType(string projectionName, string entityName)
        {
            if (string.IsNullOrEmpty(entityName))
            {
                return CpiCrudType.None;
            }

            return _entityCrudTypes.GetOrAdd(entityName, GetCrudTypeImpl);

            CpiCrudType GetCrudTypeImpl(string en)
            {
                CpiEntity entity = _metadata.FindEntity(projectionName, en);

                if (entity == null)
                {
                    return CpiCrudType.None;
                }

                CpiCrudType crudType = entity.GetCrudType();

                if (!_permissions.IsEntityReadGranted(projectionName, new[] { en }))
                {
                    crudType = crudType.ClearFlags(CpiCrudType.Read);
                }

                if (!_permissions.IsEntityWriteGranted(projectionName, new[] { en }))
                {
                    crudType = crudType.ClearFlags(CpiCrudType.Create | CpiCrudType.Update | CpiCrudType.Delete);
                }

                return crudType;
            }
        }

        #endregion
    }

    public static class DataHandlerExtensions
    {
        public static async Task<int?> CountRecordsAsync(this IDataHandler dataHandler, EntityQuery query)
        {
            if (dataHandler == null) throw new ArgumentNullException(nameof(dataHandler));
            if (query == null) throw new ArgumentNullException(nameof(query));

            AggregateQuery aggQuery = AggregateQuery.CreateCount(query);

            ExecuteResult result = await dataHandler.GetAggregateAsync(aggQuery);

            if (!result.Failed && result.Value is long count)
            {
                return (int)count;
            }

            return null;
        }

        public static async Task<EntityRecord> GetRecordAsync(this IDataHandler dataHandler, EntityDataSource source, ObjKey filter, CancellationToken cancelToken)
        {
            if (dataHandler == null) throw new ArgumentNullException(nameof(dataHandler));
            if (source == null) throw new ArgumentNullException(nameof(source));
            if (filter == null) throw new ArgumentNullException(nameof(filter));
            EntityQuery query = new EntityQuery(source);
            query.SetFilter(filter.Values);
            return await GetRecordAsync(dataHandler, query, cancelToken);
        }

        public static async Task<string[]> GetStatusIndicatorValues(this IDataHandler dataHandler, IMetadata metadata)
        {
            string[] colorStrings = metadata.GetStatusIndicator()?.ColorFunction.Split('.');
            string[] iconStrings = metadata.GetStatusIndicator()?.IconFunction.Split('.');

            if (iconStrings == null || iconStrings.Length < 2)
                return null;
            
            ExecuteResult resultIcon = await dataHandler.PerformFunctionAsync(iconStrings[0], iconStrings[1], new Dictionary<string, object>()).ConfigureAwait(false);
            string icon = resultIcon?.Value?.ToString();

            string color = string.Empty;
            if (colorStrings != null && colorStrings.Length == 2)
            {
                ExecuteResult resultColor = await dataHandler.PerformFunctionAsync(colorStrings[0], colorStrings[1], new Dictionary<string, object>()).ConfigureAwait(false);
                color = resultColor?.Value?.ToString();
            }

            return new string[] { icon, color };
        }

        public static async Task<EntityRecord> GetRecordAsync(this IDataHandler dataHandler, EntityQuery query, CancellationToken cancelToken)
        {
            if (dataHandler == null) throw new ArgumentNullException(nameof(dataHandler));
            if (query == null) throw new ArgumentNullException(nameof(query));

            query.Take = 1;

            var result = await dataHandler.GetRecordsAsync(query, cancelToken);            
            return result.Records.FirstOrDefault();
        }
    }
}
