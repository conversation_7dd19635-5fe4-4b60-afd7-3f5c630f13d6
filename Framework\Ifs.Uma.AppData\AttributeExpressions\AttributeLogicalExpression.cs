﻿using System;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.AttributeExpressions
{
    public sealed class AttributeLogicalExpression : AttributeExpression
    {
        public override AttributeExpressionType Type => AttributeExpressionType.Logical;
        public AttributeExpression Left { get; }
        public AttributeLogicalOperator Operator { get; }
        public AttributeExpression Right { get; }

        internal AttributeLogicalExpression(AttributeExpression left, AttributeLogicalOperator op, AttributeExpression right)
        {
            if (left == null) throw new ArgumentNullException(nameof(left));
            if (right == null) throw new ArgumentNullException(nameof(right));

            Left = left;
            Operator = op;
            Right = right;
        }

        internal override Expression ToExpression(EntityDataSource dataSource)
        {
            Expression left = Left.ToExpression(dataSource);
            Expression right = Right.ToExpression(dataSource);

            if (Operator == AttributeLogicalOperator.Or)
            {
                return Expression.OrElse(left, right);
            }
            else
            {
                return Expression.AndAlso(left, right);
            }
        }

        internal override bool Match(EntityDataSource dataSource, EntityRecord record)
        {
            if (dataSource == null) throw new ArgumentNullException(nameof(dataSource));
            if (record == null) throw new ArgumentNullException(nameof(record));

            if (Operator == AttributeLogicalOperator.Or)
            {
                return Left.Match(dataSource, record) || Right.Match(dataSource, record);
            }
            else
            {
                return Left.Match(dataSource, record) && Right.Match(dataSource, record);
            }
        }

        public override string ToString()
        {
            return $"({Left} {Operator} {Right})";
        }
    }
}
