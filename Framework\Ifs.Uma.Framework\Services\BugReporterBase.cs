﻿using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Localization;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI.Services;

namespace Ifs.Uma.Framework.Services
{
    public abstract class BugReporterBase : IBugReporter
    {
        protected const string BugReportFileName = "BugReport.zip";

        private readonly IDatabaseController _db;
        private readonly ITouchApp _touchApp;
        private readonly IDialogService _dialogService;

        protected BugReporterBase(IDatabaseController db, ITouchApp touchApp, IDialogService dialogService)
        {
            _db = db;
            _touchApp = touchApp;
            _dialogService = dialogService;
        }

        public abstract void ShowDialog();

        protected async Task CreateBugReport(string path)
        {
            using (_dialogService.ShowLoadingDialog(Strings.LoadingEllipsis, false))
            using (FileStream zipWriteFileStream = new FileStream(path, FileMode.Create))
            using (ZipArchive archive = new ZipArchive(zipWriteFileStream, ZipArchiveMode.Create))
            {
                await AddLogFiles(archive);
                
                if (ShouldExportDatabase())
                {
                    await AddDataExport(archive);
                }
            }
        }

        private bool ShouldExportDatabase()
        {
            _touchApp.TryResolve(out IAppParameters appParameters);
            return _touchApp.DeveloperMode || appParameters?.IsSendLogsWithDataAllowed() == true;
        }

        protected abstract Task AddLogFiles(ZipArchive archive);

        protected async Task AddLogFile(ZipArchive archive, string name, Stream source)
        {
            await Task.Run(() =>
            {
                ZipArchiveEntry entry = archive.CreateEntry(name);
                using (BinaryWriter writer = new BinaryWriter(entry.Open()))
                {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = source.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        writer.Write(buffer, 0, bytesRead);
                    }
                }
            });
        }

        private async Task AddDataExport(ZipArchive archive)
        {
            await Task.Run(() =>
            {
                ZipArchiveEntry entry = archive.CreateEntry("ExportedData.xml");
                using (Stream stream = entry.Open())
                {
                    _db.ExportDataToStream(stream);
                }
            });
        }

        public abstract void DeleteLogFiles();
    }
}
