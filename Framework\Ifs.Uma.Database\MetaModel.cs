﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Database
{
    public abstract class MetaModel : IMetaModel
    {
        #region IMetaModel Members

        public Type ContextType { get; private set; } 
        public abstract MappingSource Source { get; }

        public IMetaTable GetTable(Type rowType)
        {
            if (rowType == null) throw new ArgumentNullException(nameof(rowType));

            string tableName = GetTableName(rowType);
            return tableName == null ? null : GetTable(tableName);
        }

        public IMetaTable GetTable(string tableName)
        {
            return m_tables.GetOrAdd(tableName, CreateTable);
        }

        public abstract string GetTableName(Type rowType);

        public abstract IEnumerable<string> GetTableNames();

        public IEnumerable<IMetaTable> GetTables()
        {
            IEnumerable<string> tableNames = GetTableNames();
            return tableNames?.Select(GetTable).Where(x => x != null).ToArray();
        }

        public IEnumerable<IMetaRelation> GetRelations(IMetaTable table)
        {
            return m_relations.GetOrAdd(table, CreateRelations);
        }

        public virtual ISelectSpec CreateViewSelectSpec(IMetaTable table)
        {
            return null;
        }

        #endregion

        #region Implementation

        protected MetaModel(Type contextType)
        {
            if (contextType == null) throw new ArgumentNullException(nameof(contextType));
            ContextType = contextType;
            m_tables = new LockedDictionary<string, IMetaTable>();
            m_relations = new LockedDictionary<IMetaTable, IEnumerable<IMetaRelation>>();
        }

        protected abstract IMetaTable CreateTable(string tableName);

        protected abstract IEnumerable<IMetaRelation> CreateRelations(IMetaTable table);
        
        private LockedDictionary<string, IMetaTable> m_tables;
        private LockedDictionary<IMetaTable, IEnumerable<IMetaRelation>> m_relations;

        #endregion
    }

    public delegate ISelectSpec ViewSelectSpecProvider(IMetaTable table);

    public class MetaTable : IMetaTable
    {
        public static IMetaTable Create(string tableName, string displayName, MetaTableClass classification, TableImplementation tableImplementation, IMetaModel model, Type rowType,
            IEnumerable<IMetaDataMember> dataMembers, IEnumerable<IMetaIndex> indexes, string transactionGroup, ViewSelectSpecProvider viewSelectSpecProvider)
        {
            return !string.IsNullOrEmpty(tableName) && rowType != null && dataMembers != null && dataMembers.Any(x => x != null) ?
                new MetaTable(tableName, displayName, classification, tableImplementation, model, rowType, dataMembers, indexes, transactionGroup, viewSelectSpecProvider) : null;
        }

        #region IMetaTable Members

        public string TableName { get; set; }
        public string DisplayName { get; private set; }
        public string TransactionGroup { get; }
        public MetaTableClass Classification { get; private set; }
        public TableImplementation TableImplementation { get; }
        public IMetaModel Model { get; private set; }
        public Type RowType { get; private set; }
        public IEnumerable<IMetaDataMember> DataMembers { get; private set; }
        public IEnumerable<IMetaDataMember> PrimaryKey { get { return DataMembers.Where(x => x != null && x.PrimaryKey); } }
        public IEnumerable<IMetaIndex> Indexes { get; private set; }

        public ISelectSpec CreateViewSelectSpec()
        {
            return _viewSelectSpecProvider?.Invoke(this);
        }

        #endregion

        #region Implementation
        
        private readonly ViewSelectSpecProvider _viewSelectSpecProvider;

        protected MetaTable(string tableName, string displayName, MetaTableClass classification, TableImplementation tableImplementation, IMetaModel model, Type rowType,
            IEnumerable<IMetaDataMember> dataMembers, IEnumerable<IMetaIndex> indexes, string transactionGroup, ViewSelectSpecProvider viewSelectSpecProvider)
        {
            TableName = tableName;
            DisplayName = displayName;
            Classification = classification;
            TableImplementation = tableImplementation;
            Model = model;
            RowType = rowType;
            DataMembers = dataMembers;
            Indexes = indexes;
            TransactionGroup = transactionGroup;
            _viewSelectSpecProvider = viewSelectSpecProvider;
        }

        public override string ToString()
        {
            return "Table: " + TableName;
        }

        #endregion
    }

    public class MetaDataMember : ColumnSpec, IMetaDataMember
    {
        //Last two parameters in this Create method are used to identify the Binary and LongText attributes in CUD entities. Which are set during RemoteMetaDataMember in RemoteMetaModel.cs and false for other occasions.
        public static IMetaDataMember Create(string columnName, string displayName, bool mandatory,
            bool insertable, bool updatable, int maxLength, int scale, 
            FieldInfo storage, PropertyInfo property, bool autoIncrement, bool primaryKey, bool serverPrimaryKey,
            TextFormats textFormat, DateFormats dateFormat, NumberFormat numberFormat, IMetaEnumeration enumeration, int index, int? serverIndex,
            PropertyInfo stringIndexer, string propertyName, Type columnType, SyncRule sync, bool isBinary, bool isLongText, bool isReferenceSource)
        {
            return !string.IsNullOrEmpty(columnName) &&
                (property != null || storage != null || stringIndexer != null) && index >= 0 ?
                new MetaDataMember(columnName, displayName, mandatory, insertable, updatable,
                    maxLength, scale, storage, property, autoIncrement, primaryKey, serverPrimaryKey,
                    textFormat, dateFormat, numberFormat, enumeration, index, serverIndex, stringIndexer, propertyName, columnType, sync, isBinary, isLongText, isReferenceSource) : null;
        }

        public string PropertyName { get; private set; }
        public string DisplayName { get; private set; }
        public bool PrimaryKey { get; private set; }
        public bool ServerPrimaryKey { get; private set; }
        public bool AutoIncrement { get; private set; }
        public PropertyInfo Property { get; private set; }
        public FieldInfo Storage { get; private set; }
        public int MaxLength { get; private set; }
        public int Scale { get; private set; }
        public Type ColumnType { get; private set; }
        public TextFormats TextFormat { get; private set; }
        public DateFormats DateFormat { get; private set; }
        public NumberFormat NumberFormat { get; private set; }
        public bool Mandatory { get; private set; }
        public bool Insertable { get; private set; }
        public bool Updateable { get; private set; }
        public IMetaEnumeration Enumeration { get; private set; }
        public int Index { get; private set; }
        public int? ServerIndex { get; private set; }
        public PropertyInfo StringIndexer { get; private set; }
        public MemberInfo MemberInfo { get { return Storage ?? (MemberInfo)Property; } }
        public SyncRule Sync { get; private set; }
        public bool IsBinary { get; }
        public bool IsLongText { get; }
        public bool IsReferenceSource { get; }

        protected MetaDataMember(string columnName, string displayName, bool mandatory, 
            bool insertable, bool updatable, int maxLength, int scale, FieldInfo storage,
            PropertyInfo property, bool autoIncrement, bool primaryKey, bool serverPrimaryKey, TextFormats textFormat,
            DateFormats dateFormat, NumberFormat numberFormat, IMetaEnumeration enumeration, int index, int? serverIndex, PropertyInfo stringIndexer,
            string propertyName, Type columnType, SyncRule sync, bool isBinary, bool isLongText, bool isReferenceSource)
            : base(columnName)
        {
            DisplayName = displayName;
            Mandatory = mandatory || primaryKey || autoIncrement;
            Insertable = insertable;
            Updateable = updatable;
            MaxLength = maxLength;
            Scale = scale;
            Property = property;
            Storage = storage;
            AutoIncrement = autoIncrement;
            bool isPk = primaryKey || autoIncrement; // If this is put on the same line as PrimaryKey - Xamarin thinks 'false || false' == true
            PrimaryKey = isPk;
            ServerPrimaryKey = serverPrimaryKey;
            TextFormat = textFormat;
            DateFormat = dateFormat;
            NumberFormat = numberFormat;
            Enumeration = enumeration;
            Index = index;
            ServerIndex = serverIndex;
            StringIndexer = stringIndexer;
            PropertyName = property != null ? property.Name : storage != null ? storage.Name : propertyName;
            ColumnType = property != null ? property.PropertyType : storage != null ? storage.FieldType : columnType;
            Sync = sync;
            IsBinary = isBinary;        //This is used to identify the Binary attributes in CUD entities. Which is set during RemoteMetaDataMember in RemoteMetaModel.cs 
            IsLongText = isLongText;    //This is used to identify the LongText attributes in CUD entities. Which is set during RemoteMetaDataMember in RemoteMetaModel.cs 
            IsReferenceSource = isReferenceSource; // This is used to indicate that this attribute refers to a key in another entity through a reference
        }

        public override string ToString()
        {
            return "Column: " + ColumnName;
        }
    }

    public class MetaIndex : IMetaIndex
    {
        public static IMetaIndex Create(string indexName, IEnumerable<IMetaDataMember> columns, bool unique)
        {
            return !string.IsNullOrEmpty(indexName) && columns != null && columns.Any(x => x != null) ?
                new MetaIndex(indexName, columns.Where(x => x != null).ToArray(), unique) : null;
        }
        public string IndexName { get; private set; }
        public IEnumerable<IMetaDataMember> Columns { get; private set; }
        public bool Unique { get; private set; }

        protected MetaIndex(string indexName, IEnumerable<IMetaDataMember> columns, bool unique)
        {
            IndexName = indexName;
            Columns = columns;
            Unique = unique;
        }
    }

    public class MetaEnumeration : IMetaEnumeration
    {
        public static IMetaEnumeration Create(Type enumType, IEnumerable<IMetaEnumValue> values)
        {
            // validate
            return enumType != null && enumType.GetTypeInfo().IsEnum &&
                values != null && values.Any() && !values.Any(x => x == null) ?
                new MetaEnumeration(enumType, values) : null;
        }

        public static IMetaEnumeration Create(IMetaEnumeration localEnumeration, IMetaEnumeration serverEnumeration)
        {
            if (localEnumeration == null) throw new ArgumentNullException("localEnumeration");
            if (serverEnumeration == null) throw new ArgumentNullException("serverEnumeration");
            if (localEnumeration.EnumType == null) throw new ArgumentException("localEnumeration must have an EnumType");

            //JVB: This next line assumes that the underlying type for the enum is compatible with int (i.e. not long or ulong)
            // Luckily, this is always the case (so far).
            int nextEnumValue = localEnumeration.Values.Select(x => Convert.ToInt32(x.LocalValue,
                System.Globalization.CultureInfo.InvariantCulture)).Max() + 1;

            List<IMetaEnumValue> newValues = new List<IMetaEnumValue>();

            if (serverEnumeration.Values != null)
            {
                //Add enums that are on the server but not local, 
                foreach (IMetaEnumValue serverEnumValue in serverEnumeration.Values)
                {
                    object localValue = localEnumeration.LocalValue(serverEnumValue.ServerValue);
                    if (localValue == null)
                    {
                        // This value has not been defined in the local enum so we should
                        // add a new enum value for this server entry
                        localValue = Enum.ToObject(localEnumeration.EnumType, nextEnumValue);

                        nextEnumValue++;

                        IMetaEnumValue newEnumValue = MetaEnumValue.Create(serverEnumValue.ServerValue, serverEnumValue.DisplayName, localValue, false);
                        newValues.Add(newEnumValue);
                    }
                }
            }

            foreach (IMetaEnumValue localEnumValue in localEnumeration.Values)
            {
                IMetaEnumValue serverEnumValue = serverEnumeration.Values.FirstOrDefault(x => x.ServerValue == localEnumValue.ServerValue);
                if (serverEnumValue == null)
                {
                    IMetaEnumValue newEnumValue = MetaEnumValue.Create(localEnumValue.ServerValue, localEnumValue.DisplayName, localEnumValue.LocalValue, true);
                    newValues.Add(newEnumValue);
                }
                else
                {
                    IMetaEnumValue newEnumValue = MetaEnumValue.Create(localEnumValue.ServerValue, serverEnumValue.DisplayName, localEnumValue.LocalValue, false);
                    newValues.Add(newEnumValue);
                }
            }

            return MetaEnumeration.Create(localEnumeration.EnumType, newValues);
        }

        public string Name { get { return m_enumType.Name; } }
        public Type EnumType { get { return m_enumType; } }
        public IEnumerable<IMetaEnumValue> Values { get; private set; }

        protected MetaEnumeration(Type enumType, IEnumerable<IMetaEnumValue> values)
        {
            m_enumType = enumType;
            Values = values;
        }

        private Type m_enumType;
    }

    public class MetaEnumValue : IMetaEnumValue
    {
        public static IMetaEnumValue Create(string serverValue, string displayName, object localValue, bool clientOnly)
        {
            return !string.IsNullOrEmpty(serverValue) && localValue != null ?
                new MetaEnumValue(serverValue, displayName, localValue, clientOnly) : null;
        }

        public string ServerValue { get; private set; }
        public string DisplayName { get; private set; }
        public object LocalValue { get; private set; }
        public bool ClientOnly { get; private set; }

        protected MetaEnumValue(string serverValue, string displayName, object localValue, bool clientOnly)
        {
            ServerValue = serverValue;
            DisplayName = displayName;
            LocalValue = localValue;
            ClientOnly = clientOnly;
        }
    }

    public class ModifiedMemberInfo
    {
        public IMetaDataMember Member { get; private set; }
        public object OriginalValue { get; private set; }

        public ModifiedMemberInfo(IMetaDataMember member, object original)
        {
            if (member == null) throw new ArgumentNullException("member");
            Member = member;
            OriginalValue = original;
        }
    }

    public static class MetaDataMemberExtensions
    {
        public static IMetaDataMember GetMemberForProperty(this IEnumerable<IMetaDataMember> members, string propertyName, string className, ILogger logger)
        {
            IMetaDataMember result = null;
            if (members != null && members.Any(x => x != null) && !string.IsNullOrEmpty(propertyName))
            {
                result = members.Where(x => x != null && propertyName.Equals(x.PropertyName, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                if (result == null)
                {
                    logger.Warning(Strings.PropertyNotFound, propertyName, className);
                }
            }
            return result;
        }

        public static IEnumerable<IMetaDataMember> GetMembersForProperties(this IEnumerable<IMetaDataMember> members, IEnumerable<string> propertyNames, string className, ILogger logger)
        {
            IEnumerable<IMetaDataMember> result = null;
            if (members != null && propertyNames != null)
            {
                IMetaDataMember[] selected = propertyNames.Select(x => members.GetMemberForProperty(x, className, logger)).Distinct().ToArray();
                if (selected.Any() && !selected.Any(x => x == null))
                {
                    result = selected;
                }
            }
            return result;
        }

        public static IEnumerable<IMetaDataMember> GetMembersForProperties(this IEnumerable<IMetaDataMember> members, string propertyNames, string className, ILogger logger)
        {
            return members != null && !string.IsNullOrEmpty(propertyNames) ?
                members.GetMembersForProperties(propertyNames.Split(',').Select(x => x.Trim()), className, logger) : null;
        }

        public static object ConvertValue(this IMetaDataMember member, object value)
        {
            if (member == null) throw new ArgumentNullException("member");
            if (value == null)
            {
                if (!TypeHelper.IsNullAssignable(member.ColumnType)) throw new ArgumentNullException("value");
                return null;
            }
            switch (TypeHelper.GetTypeCode(TypeHelper.GetNonNullableType(member.ColumnType)))
            {
                case TypeCode.Boolean:
                    return ObjectConverter.ToBoolean(value);
                case TypeCode.ByteArray:
                    return ObjectConverter.ToByteArray(value);
                case TypeCode.DateTime:
                    return ObjectConverter.ToDateTime(value);
                case TypeCode.Decimal:
                    return ObjectConverter.ToDecimal(value);
                case TypeCode.Double:
                    return ObjectConverter.ToDouble(value);
                case TypeCode.Enumeration:
                    value = member.Enumeration.LocalValue(ObjectConverter.ToString(value));
                    if (value != null) return value;
                    break;
                case TypeCode.Guid:
                    return ObjectConverter.ToGuid(value);
                case TypeCode.Int16:
                    return ObjectConverter.ToShort(value);
                case TypeCode.Int32:
                    return ObjectConverter.ToInt(value);
                case TypeCode.Int64:
                    return ObjectConverter.ToLong(value);
                case TypeCode.String:
                    return ObjectConverter.ToString(value);
            }
            throw new ArgumentOutOfRangeException("value");
        }

        public static IMetaDataMember SetIndexer(this IMetaDataMember source, PropertyInfo stringIndexer)
        {
            return source == null || source.StringIndexer == stringIndexer ? source :
                MetaDataMember.Create(source.ColumnName, source.DisplayName, source.Mandatory, source.Insertable,
                    source.Updateable, source.MaxLength, source.Scale, source.Storage, source.Property,
                    source.AutoIncrement, source.PrimaryKey, source.ServerPrimaryKey, source.TextFormat, source.DateFormat, source.NumberFormat,
                    source.Enumeration, source.Index, source.ServerIndex, stringIndexer, source.PropertyName, source.ColumnType,
                    source.Sync, source.IsBinary, source.IsLongText, source.IsReferenceSource);
        }

        public static IMetaDataMember SetSyncRule(this IMetaDataMember source, SyncRule sync)
        {
            return source == null || source.Sync == sync ? source :
                MetaDataMember.Create(source.ColumnName, source.DisplayName, source.Mandatory, source.Insertable,
                    source.Updateable, source.MaxLength, source.Scale, source.Storage, source.Property,
                    source.AutoIncrement, source.PrimaryKey, source.ServerPrimaryKey, source.TextFormat, source.DateFormat, source.NumberFormat,
                    source.Enumeration, source.Index, source.ServerIndex, source.StringIndexer, source.PropertyName, source.ColumnType,
                    sync, source.IsBinary, source.IsLongText, source.IsReferenceSource);
        }

        public static void SetValue(this IMetaDataMember member, object row, object value)
        {
            if (member == null) throw new ArgumentNullException("member");
            if (row == null) throw new ArgumentNullException("row");

            if (row is IFastMemberAccess fma)
            {
                fma.SetMemberValue(member, value);
            }
            else if (member.Storage != null)
            {
                member.Storage.SetValue(row, value);
            }
            else if (member.Property != null)
            {
                member.Property.SetValue(row, value, null);
            }
            else if (member.StringIndexer != null)
            {
                // Must be a custom field - use the indexer
                member.StringIndexer.SetValue(row, value, new string[] { member.PropertyName });
            }
            else
            {
                throw new InvalidOperationException(string.Format(System.Globalization.CultureInfo.InvariantCulture,
                    "Member '{0}' not found on row of type '{1}'", member.PropertyName, row.GetType().FullName));
            }
        }

        public static object GetValue(this IMetaDataMember member, object row)
        {
            if (member == null) throw new ArgumentNullException("member");
            if (row == null) throw new ArgumentNullException("row");

            if (row is IFastMemberAccess fma)
            {
                return fma.GetMemberValue(member);
            }

            if (member.Storage != null)
            {
                return member.Storage.GetValue(row);
            }

            if (member.Property != null)
            {
                return member.Property.GetValue(row, null);
            }

            if (member.StringIndexer != null)
            {
                // Must be a custom field - use the indexer
                return member.StringIndexer.GetValue(row, new string[] { member.PropertyName });
            }

            throw new InvalidOperationException(string.Format(System.Globalization.CultureInfo.InvariantCulture,
                "Member '{0}' not found on row of type '{1}'", member.PropertyName, row.GetType().FullName));
        }

        public static object CloneValue(this IMetaDataMember member, object row)
        {
            object value = member.GetValue(row);
            if (value != null)
            {
                // some value types can be modified in place so we need to clone them
                switch (TypeHelper.GetTypeCode(TypeHelper.GetNonNullableType(member.ColumnType)))
                {
                    case TypeCode.ByteArray:
                        value = ((byte[])value).Clone();
                        break;
                }
            }
            return value;
        }
    }

    public static class MetaTableExtensions
    {
        public static IMetaIndex FirstUniqueIndex(this IMetaTable table)
        {
            return table != null && table.Indexes != null ? table.Indexes.FirstOrDefault(x => x.Unique) : null;
        }

        public static IEnumerable<IMetaDataMember> Key(this IMetaTable table, KeyChoice key)
        {
            switch (key)
            {
                case KeyChoice.AutoIncrementKey:
                    return new[] { table.AutoIncrement() };
                default:
                {
                    if (table.AutoIncrement() != null)
                    {
                        IMetaIndex index = table.FirstUniqueIndex();

                        if (index != null)
                        {
                            return index.Columns;
                        }
                    }

                    return table.PrimaryKey;
                }
            }
        }

        public static IEnumerable<IMetaDataMember> FirstUniqueIndexColumns(this IMetaTable table)
        {
            IMetaIndex index = table.FirstUniqueIndex();
            return index != null ? index.Columns : null;
        }

        public static ISystemData GetSystemData(this IMetaTable table)
        {
            return table != null && table.Model != null && table.Model.Source != null ?
                table.Model.Source.SystemData : null;
        }

        public static IEnumerable<Type> GetRowTypes(this IMetaTable table, MetaTableClass classification)
        {
            return table != null && table.Model != null ?
                table.Model.GetTables()
                    .Where(x => x.Classification.IsSet(classification))
                    .Select(x => x.RowType) : null;
        }

        public static object CreateRow(this IMetaTable table)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));

            IEnumerable<ConstructorInfo> constructors = table.RowType.GetTypeInfo().DeclaredConstructors;

            if (constructors.All(x => x.GetParameters().Length != 0))
            {
                return Activator.CreateInstance(table.RowType, table.TableName);
            }

            return Activator.CreateInstance(table.RowType);
        }

        public static NewExpression CreateRowExpression(this IMetaTable table)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));
            
            IEnumerable<ConstructorInfo> constructors = table.RowType.GetTypeInfo().DeclaredConstructors;

            if (constructors.All(x => x.GetParameters().Length != 0))
            {
                // If there are no constructors with no arguments find a constructor that can take a table name
                var constructor = constructors.First(x => x.GetParameters().Length == 1 && x.GetParameters()[0].ParameterType == typeof(string));
                return Expression.New(constructor, Expression.Constant(table.TableName));
            }

            // Use the constructor with no arguments
            return Expression.New(table.RowType);
        }

        public static object CloneRow(this IMetaTable table, object row)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));
            if (row == null) return null;
            object clone = CreateRow(table);
            foreach (IMetaDataMember mi in table.DataMembers)
            {
                mi.SetValue(clone, mi.CloneValue(row));
            }
            return clone;
        }

        public static string GetTransactionGroup(this IMetaTable table, object row)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));
            if (row == null || table.TransactionGroup == null) return null;

            Func<string, string> valueProvider = propertyName =>
            {
                IMetaDataMember member = table.FindMemberByPropertyName(propertyName);
                object value = member?.GetValue(row);
                return ObjectConverter.ToString(value);
            };

            return table.TransactionGroup.ReplaceParams(valueProvider);
        }

        public static bool IsModified(this IMetaTable table, object instance, object original)
        {
            if (table == null) throw new ArgumentNullException("table");
            if (instance == null) throw new ArgumentNullException("instance");
            if (original == null) throw new ArgumentNullException("original");
            foreach (IMetaDataMember member in table.DataMembers)
            {
                if (!object.Equals(member.GetValue(instance), member.GetValue(original)))
                    return true;
            }
            return false;
        }

        public static IMetaDataMember GetMember(this IMetaTable table, MemberInfo info)
        {
            return table != null && info != null ?
                table.DataMembers.FirstOrDefault(x => x.Property == info || x.Storage == info) : null;
        }

        public static IMetaDataMember AutoIncrement(this IMetaTable table)
        {
            return table != null ? table.DataMembers.FirstOrDefault(x => x.AutoIncrement) : null;
        }

        public static IEnumerable<ModifiedMemberInfo> GetModifications(this IMetaTable table, object row, object originalRow)
        {
            if (table == null) throw new ArgumentNullException("table");
            if (row == null) throw new ArgumentNullException("row");
            if (originalRow == null) throw new ArgumentNullException("originalRow");
            ICollection<ModifiedMemberInfo> result = new List<ModifiedMemberInfo>();
            foreach (IMetaDataMember member in table.DataMembers)
            {
                object value = member.GetValue(row);
                object original = member.GetValue(originalRow);
                if (!object.Equals(value, original))
                {
                    result.Add(new ModifiedMemberInfo(member, original));
                }
            }
            return result.AsEnumerable();
        }

        public static IMetaDataMember FindMemberByColumnName(this IMetaTable table, string columnName)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));
            if (columnName == null) throw new ArgumentNullException(nameof(columnName));

            return table.DataMembers.FirstOrDefault(x => x.ColumnName == columnName);
        }

        public static IMetaDataMember FindMemberByPropertyName(this IMetaTable table, string propertyName)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));
            if (propertyName == null) throw new ArgumentNullException(nameof(propertyName));

            return table.DataMembers.FirstOrDefault(x => x.PropertyName == propertyName);
        }
    }

    public static class MetaModelExtensions
    {
        public static IMetaTable GetEntity(this IMetaModel model, Type rowType)
        {
            if (model == null) throw new ArgumentNullException("model");
            if (rowType == null) throw new ArgumentNullException("rowType");
            IMetaTable result = model.GetTable(rowType);
            if (result == null) throw new InvalidOperationException(rowType.FullName + " is an invalid meta table");
            return result;
        }

        public static IMetaTable GetEntity(this IMetaModel model, MemberInfo contextMember)
        {
            if (contextMember == null) throw new ArgumentNullException("contextMember");
            return model.GetEntity(TypeHelper.GetElementType(TypeHelper.GetMemberType(contextMember)));
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "Ifs.Uma.Utility.LoggerExtensions.Warning(Ifs.Uma.Utility.ILogger,System.String,System.String[])",
            Justification = "Only called for diagnostic purposes - no translation required")]
        public static void WriteOutUntranslatedObjects(this IMetaModel model, ILogger logger)
        {
            if (logger != null && model != null)
            {
                string missingTranslations = "\r\n\r\n------- Missing Table/Column translations --------\r\n\r\n";
                string missingTranslationsEnums = "------- Missing Enum translations --------\r\n\r\n";

                string[] columnsToIgnore = new string[] 
                {
                    "RowId",
                    "HandlerMethod",
                    "ObjId",
                    "ObjVersion"
                };

                List<string> foundEnums = new List<string>();
                foreach (var table in model.GetTables().Where(x => !x.Classification.HasFlag(MetaTableClass.System)))
                {
                    string tableName = table.RowType.Name.RemoveSuffix("Row");

                    if (string.IsNullOrEmpty(table.DisplayName))
                    {
                        missingTranslations += string.Format(CultureInfo.InvariantCulture, "{0}\r\n", tableName);
                    }
                    foreach (var column in table.DataMembers.Where(x => !columnsToIgnore.Contains(x.PropertyName)))
                    {
                        if (string.IsNullOrEmpty(column.DisplayName))
                        {
                            missingTranslations += string.Format(CultureInfo.InvariantCulture, "{0}_{1}\r\n", tableName, column.PropertyName);
                        }

                        if (column.Enumeration != null
                            &&
                            !foundEnums.Contains(column.Enumeration.Name)
                            &&
                            column.Enumeration.Values.Any(x => string.IsNullOrEmpty(x.DisplayName))
                            )
                        {
                            foundEnums.Add(column.Enumeration.Name);

                            missingTranslationsEnums += string.Format(CultureInfo.InvariantCulture, "{0}:\r\n", column.Enumeration.Name);
                            foreach (var enumValue in column.Enumeration.Values.Where(x => string.IsNullOrEmpty(x.DisplayName)))
                            {
                                missingTranslationsEnums += string.Format(CultureInfo.InvariantCulture, "   {0}\r\n", enumValue.LocalValue);
                            }
                        }
                    }
                }

                missingTranslations += "--------------------------------------------------\r\n\r\n";
                missingTranslationsEnums += "--------------------------------------------------\r\n\r\n";

                //System.Diagnostics.Debug.WriteLine(missingTranslations + missingTranslationsEnums);
                logger.Warning(missingTranslations + missingTranslationsEnums);
            }
        }
    }
}
