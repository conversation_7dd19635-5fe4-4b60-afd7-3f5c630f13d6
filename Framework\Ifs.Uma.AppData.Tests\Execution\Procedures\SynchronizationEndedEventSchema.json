{"name": "FndTstOffline", "version": "1706901162:1948287535", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "enumerations": {}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}, "references": {}, "arrays": {}, "actions": {}, "functions": {}}}, "structures": {}, "actions": {}, "functions": {}, "procedures": {"Event<SynchronizationEnded>": {"name": "SynchronizationEnded", "type": "Event", "params": [{"name": "ChangedEntities", "datatype": "Text", "collection": true}, {"name": "RowIds", "datatype": "Structure", "subtype": "FndRecordRowId", "collection": true}], "layers": [{"vars": [{"name": "RowIdStruct", "dataType": "Structure", "subType": "FndRecordRowId", "collection": false}, {"name": "ListCount", "dataType": "Integer", "collection": false}, {"name": "Counter", "dataType": "Integer", "collection": false}, {"name": "<PERSON><PERSON><PERSON>", "dataType": "Text", "collection": false}, {"name": "TableName", "dataType": "Text", "collection": false}, {"name": "RowId", "dataType": "Number", "collection": false}], "execute": [{"call": {"method": "set", "args": {"value": 0}}, "assign": "Counter"}, {"call": {"method": "proc", "args": {"name": "Count", "namespace": "List", "paramsArray": ["${RowIds}"]}}, "assign": "ListCount"}, {"call": {"method": "while", "args": {"expression": {"<": [{"var": "Counter"}, {"var": "ListCount"}]}}}, "result": {"TRUE": [{"call": {"method": "proc", "args": {"name": "Get", "namespace": "List", "paramsArray": ["${RowIds}", "${Counter}"]}}, "assign": "RowIdStruct"}, {"call": {"method": "set", "args": {"name": "RowIdStruct.RecordTableName"}}, "assign": "TableName"}, {"call": {"method": "set", "args": {"name": "RowIdStruct.RecordRowId"}}, "assign": "RowId"}, {"call": {"method": "proc", "args": {"name": "GetPkFromRowId", "namespace": "System", "paramsArray": ["${TableName}", "${RowId}"]}}, "assign": "<PERSON><PERSON><PERSON>"}, {"call": {"method": "if", "args": {"expression": {"and": [{"==": [{"var": "Counter"}, 0]}, {"!=": [{"var": "<PERSON><PERSON><PERSON>"}, "CUSTOMER_NO=500^"]}]}}}, "result": {"TRUE": [{"call": {"method": "return", "args": {"value": false}}}]}}, {"call": {"method": "if", "args": {"expression": {"and": [{"==": [{"var": "Counter"}, 1]}, {"!=": [{"var": "<PERSON><PERSON><PERSON>"}, "CUSTOMER_NO=501^"]}]}}}, "result": {"TRUE": [{"call": {"method": "return", "args": {"value": false}}}]}}, {"call": {"method": "set", "args": {"expression": {"+": [{"var": "Counter"}, 1]}}}, "assign": "Counter"}]}}]}]}}, "component": "FNDTST", "layout": {"lists": {}, "cards": {}, "selectors": {}, "pages": {}, "groups": {}, "menus": {}, "commands": {}}}}