﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Address;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Framework.UI.Lists;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Elements.Lists
{
    public sealed class ListElement : ElementWithButtonBase
    {
        private readonly IMetadata _metadata;
        private readonly IDataHandler _data;
        private readonly ILogger _logger;
        private readonly INavigator _navigator;
        private readonly IEventAggregator _eventAggregator;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IBreadcrumbManager _breadcrumbManager;
        private readonly ICardDefCreator _cardDefCreator;
        private readonly IAddressHandler _addressHandler;

        protected override BindingType BindingPropertyType => BindingType.Array;

        private CpiList _list;
        private CpiCard _card;
        private CpiCommand _createCommand, _detailCommand;

        public string CardName => _card?.Name;
        public string ListName => _list?.Name;
        public string ListLabel => _list.Label;
        public string EntityName => _list.Entity;
        public bool HasCommands
        {
            get { return _list.CommandGroups != null; }
        }

        public CardListData<ListElementItem> ListData { get; private set; }
        public CommandItem Create { get; }
        public ScrollPosition SavedScrollPosition { get => ListData.SavedScrollPosition; set => ListData.SavedScrollPosition = value; }

        public event EventHandler<CommandExecutedEventArgs> CommandExecuted;
        private CancellingUpdater _countersUpdater;

        public ListElementState State { get; private set; }

        public ListElement(IMetadata metadata, IDataHandler data, ILogger logger, INavigator navigator, IEventAggregator eventAggregator,
            ICommandExecutor commandExecutor, IBreadcrumbManager breadcrumbManager, ICardDefCreator cardDefCreator, IAddressHandler addressHandler)
        {
            _metadata = metadata;
            _data = data;
            _logger = logger;
            _navigator = navigator;
            _eventAggregator = eventAggregator;
            _breadcrumbManager = breadcrumbManager;
            _commandExecutor = commandExecutor;
            _cardDefCreator = cardDefCreator;
            _addressHandler = addressHandler;

            Create = new CreateCommandItem(this);
            HasHeader = false;

            _countersUpdater = new CancellingUpdater(UpdateCount);

            // TODO: isactive
        }

        protected override bool OnInitialize()
        {
            _list = _metadata.FindList(ProjectionName, Content.List);
            _card = _list.Card == null ? null : _metadata.FindCard(ProjectionName, _list.Card);
            Label = _list?.Label;

            if (_list != null)
            {
                string createCommandName = $"{_list.Name}_{_list.Name}_Create";
                bool hasCreateCommand = _list.CommandGroups != null && _list.CommandGroups.SelectMany(x => x.CommandNames).Any(x => x == createCommandName);
                CpiCommand createCommand = hasCreateCommand ? _metadata.FindCommand(ProjectionName, createCommandName) : null;
                if (createCommand?.Selection == CpiCommandSelection.Global)
                {
                    _createCommand = createCommand;
                }

                State = new ListElementState(PageData?.ViewState, _list.Name);
            }

            return _list != null;
        }

        protected override void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            State = new ListElementState(newValue?.ViewState, _list.Name);
            base.OnPageDataChanged(oldValue, newValue);
        }

        protected override bool OnLoad()
        {
            OpenElementButtonCommand = Command.FromMethod(OpenList, OpenElementButtonCommandCanExecute);
            OpenElementButtonImage = IconUtils.BulletList;

            UpdateElementButtonItemsCount();

            if (ListData != null)
            {
                ListData.SelectedItemChanged -= ListData_SelectedItemChanged;
            }

            ListData = new ListElementListData(_eventAggregator, _metadata, _data, _logger, _cardDefCreator, _commandExecutor, _addressHandler, this, _list, _card);
            ListData.SelectedItemChanged += ListData_SelectedItemChanged;

            string detailsCommandName = ListData?.ItemCardDef?.GetDetailsCommandName();
            if (_card != null && !string.IsNullOrEmpty(detailsCommandName))
            {
                if (_card.CommandGroups != null)
                {
                    foreach (CpiCommandGroup group in _card.CommandGroups)
                    {
                        if (group.CommandNames.Any(x => x == detailsCommandName))
                        {
                            _detailCommand = _metadata.FindCommand(ProjectionName, detailsCommandName);
                            break;
                        }
                    }
                }
            }

            if (Content.DetailPageName == null && _detailCommand == null)
            {
                ListData.IsClickEnabled = false;
            }

            return true;
        }

        private void ListData_SelectedItemChanged(object sender, ListElementItem e)
        {
            ViewData data = ListData.SelectedItem;
            if (data != null)
            {
                ObjPrimaryKey keyRef = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, data.Record.GetRemoteRow());
                string primaryKeyString = keyRef.ToKeyRef();

                State.Selection = primaryKeyString;
            }
            else
            {
                State.Selection = null;
            }
        }

        protected override void OnDataChanged()
        {
            base.OnDataChanged();

            bool openListCommandCanExecute = OpenElementButtonCommandCanExecute();
            if (PreviousOpenElementButtonCommandCanExecuteState != openListCommandCanExecute)
            {
                OpenElementButtonCommand.IsEnabled = openListCommandCanExecute;
                OpenElementButtonCommand.RaiseCanExecuteChanged();
                PreviousOpenElementButtonCommandCanExecuteState = openListCommandCanExecute;
            }

            Create.IsEnabled = CanCreateNew();
            Create.IsVisible = Create.IsEnabled;
        }

        protected override void OnStoredDataChanged(DataChangeSet changeSet)
        {
            base.OnStoredDataChanged(changeSet);

            string countFunction = Content?.CountFunction ?? _list?.CountFunction;
            if (DisplayState == ElementDisplayState.Normal && countFunction != null)
            {
                FunctionInfo function = FunctionInfo.Get(_metadata, ProjectionName, countFunction);
                if (function != null && function.AreResultsEffectedByChangeSet(changeSet))
                {
                    LoadData();
                }
            }
            else if (DataSource != null && DataSource.IsEffectedByChangeSet(changeSet))
            {
                bool isEditableList = ListData.Items.Any(x => x.EditableField != null);
                if (!isEditableList)
                {
                    LoadData();
                }
            }
        }

        protected override void OnDisplayStateChanged()
        {
            base.OnDisplayStateChanged();

            if (HasLoaded && !ListData.IsUpdating)
            {
                LoadData();
            }

            if (DisplayState == ElementDisplayState.Normal)
            {
                _breadcrumbManager?.PopFromBreadcrumbStack();
            }
        }

        protected override void OnDataSourceChanged()
        {
            base.OnDataSourceChanged();

            LoadData();
        }

        private void LoadData()
        {
            if (DataSource is FunctionDataSource funcDataSource)
            {
                if (!string.IsNullOrEmpty(Content.DatasourceBasedOn))
                {
                    DataSource.EntitySetName = Content.DatasourceBasedOn.Split('/')[1];
                }

                if (Content.DatasourceFunctionParams?.Count > 0 && !RecordData.HasLoadedRecord(Record))
                {
                    // This function call needs parameters to be passed in, so we can't proceed until the record is loaded
                    return;
                }
            }

            ExecuteBackgroundTask(LoadDataAsync());
        }

        private async Task LoadDataAsync()
        {
            if (ListData == null)
            {
                // We shouldn't really ever come to this situation because in theory, ListData should never become null.
                // In MOBOFF-2740, we noticed that something odd is happening in single step assistant with repeating sections (HTML page)
                // where ListData becomes null, so this is a quick fix to avoid the exception message from being shown to the user.
                // TODO: This needs a better solution where ListData doesn't become null, even in that scenario.
                return;
            }

            try
            {
                if (DisplayState != ElementDisplayState.Normal)
                {
                    await ListData.UpdateAsync();
                }
                else
                {
                    await StartCountersUpdate();
                }
            }
            catch (Exception ex)
            {
                await HandleException(ex);
            }
        }

        private async Task StartCountersUpdate()
        {
            if (_countersUpdater != null)
            {
                await _countersUpdater.UpdateAsync();
            }
        }

        public UmaColor? GetConditionalFormatColor(ViewData viewData)
        {
            UmaColor? colorToApply = null;

            if (_list.ConditionalFormats != null)
            {
                foreach (CpiConditionalFormat formatting in _list.ConditionalFormats)
                {
                    if (formatting.BackgroundFormat?.Emphasis != null && ExpressionRunner.RunCheck(formatting.Case, viewData, false))
                    {
                        string emphasis = ExpressionRunner.GetEmphasis(formatting.BackgroundFormat.Emphasis, viewData);
                        colorToApply = UmaColor.FromEmphasis(emphasis);
                    }
                }
            }

            return colorToApply;
        }

#if SIGNATURE_SERVICE
        public bool HasSignatureChanges(ListElementItem listItem)
        {
            return ExpressionRunner.RunCheck(_list.DigitalSignature?.HashContent, listItem, false);
        }

        public string GetSignatureEntity()
        {
            return _list.DigitalSignature?.Entity;
        }
#endif

        public List<string> GetFieldsToApplyConditionalFormatting(ViewData viewData)
        {
            HashSet<string> fields = new HashSet<string>();

            if (_list.ConditionalFormats != null)
            {
                foreach (CpiConditionalFormat formatting in _list.ConditionalFormats)
                {
                    if (formatting.Fields != null && ExpressionRunner.RunCheck(formatting.Case, viewData, false))
                    {
                        formatting.Fields.ToList().ForEach(x => fields.Add(x));
                    }
                }
            }

            return fields.ToList();
        }

        private async Task UpdateCount()
        {
            if (_countersUpdater != null)
            {
                await _countersUpdater.UpdateAsync();
            }
        }

        public async Task CancelDataLoading()
        {
            if (_countersUpdater != null)
            {
                await _countersUpdater.CancelAsync();
            }
        }

        private async Task UpdateCount(CancellationToken token)
        {
            EntityDataSource dataSource = DataSource;

            string countFunction = null;
            string[] countFunctionParams = null;

            if (Content.CountFunction != null)
            {
                countFunction = Content.CountFunction;
                countFunctionParams = Content.CountFunctionParams;
            }
            else if (_list.CountFunction != null)
            {
                countFunction = _list.CountFunction;
                countFunctionParams = _list.CountFunctionParams;
            }

            if (!string.IsNullOrEmpty(countFunction))
            {
                Dictionary<string, object> parameters = null;
                if (countFunctionParams != null && countFunctionParams.Length > 0)
                {
                    parameters = new Dictionary<string, object>();
                    foreach (string key in countFunctionParams)
                    {
                        if (ViewData.TryGetValue(key, out object output))
                        {
                            parameters.Add(key, output);
                        }
                    }
                }

                ExecuteResult result = await _data.PerformFunctionAsync(ProjectionName, countFunction, parameters, token);
                result.CheckFailure();

                if (result.Value != null)
                {
                    ElementButtonItemsCount = Convert.ToInt32(result.Value);
                }
                else
                {
                    ElementButtonItemsCount = null;
                }
            }
            else if (dataSource != null)
            {
                EntityQuery query = new EntityQuery(dataSource);

                ElementButtonItemsCount = await _data.CountRecordsAsync(query);
            }
            else
            {
                ElementButtonItemsCount = null;
            }
        }

        protected override void OnReloadData()
        {
            base.OnReloadData();

            LoadData();
        }

        private bool CanCreateNew()
        {
            try
            {
                if (_list == null)
                {
                    return false;
                }

                if (_createCommand != null)
                {
                    _commandExecutor.GetStates(ProjectionName, ViewData, _createCommand, true, out bool isVisible, out bool isEnabled);
                    return isVisible && isEnabled;
                }
                else
                {
                    if (Content.DetailPageName == null)
                    {
                        return false;
                    }

                    CpiPage page = _metadata.FindPage(ProjectionName, Content.DetailPageName);

                    if (page == null)
                    {
                        return false;
                    }

                    // Check if the entity of the list allows creating a new record
                    CpiCrudType entityCrudType = _data.GetCrudType(ProjectionName, _list.Entity);
                    if (!entityCrudType.CanUse(CpiCrudType.Create))
                    {
                        return false;
                    }

                    // Are we allowed to create based on the entity in the detail page
                    entityCrudType = _data.GetCrudType(ProjectionName, page.Entity);
                    if (!entityCrudType.CanUse(CpiCrudType.Create))
                    {
                        return false;
                    }

                    ViewData vdPage = new ViewData(PageData, null);

                    // Are we allowed to create based on the details page that the user will be sent to
                    if (page.CrudActions?.New != null && !ExpressionRunner.RunCheck(page.CrudActions.New.OfflineEnabled ?? page.CrudActions.New.Enabled, vdPage, true))
                    {
                        return false;
                    }

                    // Are we allowed to create based on the list element
                    ViewData vd = new ViewData(PageData, null)
                    {
                        Parent = ViewData
                    };

                    CpiExpression enabled = _list.CrudActions?.New?.OfflineEnabled ?? _list.CrudActions?.New?.Enabled;
                    if (enabled != null && !ExpressionRunner.RunCheck(enabled, vd, true))
                    {
                        return false;
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Unexpected, ex);
            }

            return false;
        }

        internal async Task NavigateToItemAsync(ListElementItem item)
        {
            ListData.IsClickEnabled = false;

            if (item == null)
            {
                // Creating a new item
                if (_createCommand != null)
                {
                    ExecuteResult result = await _commandExecutor.ExecuteAsync(ProjectionName, ViewData, _createCommand);
                    CommandExecuted?.Invoke(this, new CommandExecutedEventArgs(_createCommand, result));
                    ListData.IsClickEnabled = true;
                    return;
                }
            }

            if (_navigator != null && Content.DetailPageName != null)
            {
                PageValues filter = item?.Record != null ? PageValues.FromColumnValues(Content.DetailColumns, item.Record) : null;
                MetadataPageNavParam navParam = new MetadataPageNavParam(ProjectionName, Content.DetailPageName, filter);
                await _navigator.NavigateToAsync(FrameworkLocations.MetadataPage, navParam, item != null ? NavigationMode.Forward : NavigationMode.New);
            }
            else if (_detailCommand != null)
            {
                ListData.IsNavigating = true;

                try
                {
                    ViewData data = _detailCommand.Selection == CpiCommandSelection.Global ? PageData.DefaultViewData : item;
                    ExecuteResult result = await _commandExecutor.ExecuteAsync(ProjectionName, data, _detailCommand);
                    CommandExecuted?.Invoke(this, new CommandExecutedEventArgs(_detailCommand, result));
                    ReloadData();
                }
                finally
                {
                    ListData.IsNavigating = false;
                }
            }

            ListData.IsClickEnabled = true;
        }

        private sealed class CreateCommandItem : CommandItem
        {
            private readonly ListElement _listElement;

            public CreateCommandItem(ListElement listElement)
            {
                _listElement = listElement;
                IsVisible = false;
                IsEnabled = false;
                Text = Strings.Add;
                Icon = IconUtils.New;
            }

            protected override async Task OnExecuteAsync()
            {
                if (IsVisible && IsEnabled)
                {
                    await _listElement.NavigateToItemAsync(null);
                }
            }
        }

        protected override void OnGetSelectAttributes(ICollection<string> attributes)
        {
            base.OnGetSelectAttributes(attributes);

            CpiList list = _metadata.FindList(ProjectionName, Content.List);
            if (list?.Entity != null)
            {
                AttributeFinder.FindInElementBinding(attributes, ProjectionName, list.Entity, _metadata, Content?.Binding);
            }

            // Make sure to include any attributes from list items that reference the parent
            foreach (string listAttribute in GetListSelectAttributes())
            {
                if (listAttribute.StartsWith(ViewData.ParentPrefix))
                {
                    attributes.Add(listAttribute.Substring(ViewData.ParentPrefix.Length));
                }
            }
        }

        public string[] GetListSelectAttributes()
        {
            HashSet<string> attributes = new HashSet<string>();

            AttributeFinder.FindInList(attributes, _metadata, ProjectionName, _list);

            if (_card != null)
            {
                AttributeFinder.FindInCard(attributes, _metadata, ProjectionName, _card, true);
            }

            return attributes.ToArray();
        }

        private void OpenList()
        {
            if (_breadcrumbManager != null)
            {
                _breadcrumbManager.PushToBreadcrumbStack();
            }

            DisplayState = ElementDisplayState.FullScreen;
        }

        public CommandBlock CreateCommandBlock()
        {
            if (_list.CommandGroups == null || _list.CommandGroups.Length <= 0)
            {
                return null;
            }

            CommandBlock commands = new CommandBlock(null, _metadata, CommandExecutor, ExpressionRunner);
            commands.Load(ProjectionName, _list.CommandGroups, true, true, true, null, null);

            return commands;
        }
    }
}
