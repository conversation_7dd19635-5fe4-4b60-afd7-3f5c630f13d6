﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class OnlineDisallowedTests : ProcedureTest
    {
        [Test]
        public async Task Create()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.EntityPrepareAsync(TestOfflineProjection, "TstCustomer");

            CheckResult(result);
        }

        [Test]
        public async Task Insert()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow customer = new RemoteRow("tst_customer");
            customer["CustomerNo"] = "501";
            customer["CustomerName"] = "Test Customer 2";

            ExecuteResult result = await executor.EntityInsertAsync(TestOfflineProjection, customer);

            CheckResult(result);
        }

        [Test]
        public async Task Update()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow customer = new RemoteRow("tst_customer");
            customer["CustomerNo"] = "501";
            customer["CustomerName"] = "Test Customer 2";
            customer.GetType().GetProperty(nameof(RemoteRow.RowId)).SetValue(customer, 1);

            ExecuteResult result = await executor.EntityUpdateAsync(TestOfflineProjection, customer, new[] { "CustomerName" });

            CheckResult(result);
        }

        [Test]
        public async Task Delete()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            RemoteRow customer = new RemoteRow("tst_customer");
            customer["CustomerNo"] = "501";
            customer["CustomerName"] = "Test Customer 2";

            ExecuteResult result = await executor.EntityDeleteAsync(TestOfflineProjection, customer);

            CheckResult(result);
        }

        [Test]
        public async Task EntitySetQuery()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestEntitySet", null);

            CheckResult(result);
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Failed);

            ProcedureException ex = result.Exception as ProcedureException;
            Assert.IsNotNull(ex);
            Assert.IsTrue(ex.Message.Contains("OnlineOnly"));
        }

        protected override void OnErrorLogged(string message)
        {
            if (!message.Contains("OnlineOnly"))
            {
                base.OnErrorLogged(message);
            }
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.OnlineDisallowedSchema", null);
        }
    }
}
