﻿using Ifs.Uma.AppData.Online;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemGetUserName : SystemFunction
    {
        public const string FunctionName = "GetUserName";
        private readonly IOnlineDataHandler _onlineDataHandler;

        public SystemGetUserName(IOnlineDataHandler onlineDataHandler)
            : base(FunctionName, 0)
        {
            _onlineDataHandler = onlineDataHandler;
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return _onlineDataHandler?.UserName;
        }
    }
}
