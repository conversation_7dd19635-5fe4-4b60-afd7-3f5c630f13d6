<?xml version='1.0' encoding='utf-8'?>
<dataexport>
  <data>

    <tst_customer>
      <customer_no>1</customer_no>
      <customer_name>A</customer_name>
      <customer_type>TYPE_A</customer_type>
      <expires>2030-08-26T10:40:02.9118745</expires>
    </tst_customer>

    <tst_customer>
      <customer_no>2</customer_no>
      <customer_name>Jo'hn</customer_name>
      <customer_type>TYPE_B</customer_type>
      <expires>2016-07-26T10:40:02.9118745</expires>
    </tst_customer>

    <tst_customer>
      <customer_no>3</customer_no>
      <customer_name>Jo'hn</customer_name>
      <customer_type>TYPE_A</customer_type>
      <expires>2030-09-26T10:40:02.9118745</expires>
    </tst_customer>

    <tst_customer>
      <customer_no>4</customer_no>
      <customer_name>D</customer_name>
      <customer_type>TYPE_B</customer_type>
      <expires>2017-03-26T10:40:02.9118745</expires>
    </tst_customer>

    <tst_customer_type>
      <type_id>TYPE_A</type_id>
      <type_description>Customer Type A</type_description>
    </tst_customer_type>

    <tst_customer_type>
      <type_id>TYPE_B</type_id>
      <type_description>Customer Type B</type_description>
    </tst_customer_type>

    <tst_customer_type>
      <type_id>TYPE_C</type_id>
      <type_description>Customer Type C</type_description>
    </tst_customer_type>

  </data>
</dataexport>
