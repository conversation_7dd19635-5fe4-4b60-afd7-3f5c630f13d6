{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_RegexSubString>": {"name": "String_RegexSubString", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "RegexPattern", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "String"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexSubString", "paramsArray": ["${TextInput}", "${RegexPattern}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<String_RegexSubString4>": {"name": "String_RegexSubString", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "RegexPattern", "dataType": "Text"}, {"name": "Position", "dataType": "Integer"}, {"name": "Occurrence", "dataType": "Integer"}], "layers": [{"vars": [{"name": "Result", "dataType": "String"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexSubString", "paramsArray": ["${TextInput}", "${RegexPattern}", "${Position}", "${Occurrence}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<String_RegexSubString5>": {"name": "String_RegexSubString", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "RegexPattern", "dataType": "Text"}, {"name": "Position", "dataType": "Integer"}, {"name": "Occurrence", "dataType": "Integer"}, {"name": "RegexOptions", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "String"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexSubString", "paramsArray": ["${TextInput}", "${RegexPattern}", "${Position}", "${Occurrence}", "${RegexOptions}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}