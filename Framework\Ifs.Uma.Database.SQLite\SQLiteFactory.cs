﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using System.Reflection;

namespace Ifs.Uma.Database.SQLite
{
    [DbFactory]
    public class SQLiteFactory : DbProviderFactory
    {
        public SQLiteFactory()
        {
            Type batteries = Type.GetType("SQLitePCL.Batteries, SQLitePCLRaw.batteries_sqlcipher");
            if (batteries != null)
            {
                MethodInfo initMethod = batteries.GetTypeInfo().GetDeclaredMethod("Init");
                initMethod.Invoke(null, null);
            }

            m_pool = new SQLiteConnectionPool();
        }

        public override string ProviderName { get { return "Ifs.Uma.Database.SQLite"; } }

        public override SqlBuilder Builder { get { return SQLiteBuilder.Instance; } }

        public override DbConnectionStringBuilder CreateConnectionStringBuilder()
        {
            return new SQLiteConnectionStringBuilder();
        }

        internal void DisposeConnection(SQLiteConnection connection)
        {
            ConnectionDisposed(connection);
        }

        protected override DbConnection NewConnection(string connectionString, IMapEnumeration enumMapper)
        {
            SQLiteConnectionPool pool = m_pool;
            if (pool == null) throw new ObjectDisposedException("SQLiteFactory");
            return new SQLiteConnection(this, connectionString, pool, enumMapper, Logger, TraceFlag);
        }

        private SQLiteConnectionPool m_pool;

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2213:DisposableFieldsShouldBeDisposed", MessageId = "m_pool",
            Justification = "False Positive")]
        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
            if (disposing)
            {
                // Hey, lookee here Mr Code Analyser, guess what I'm doing
                UsefulExtensions.ThreadSafeDispose(ref m_pool);
            }
        }
    }
}
