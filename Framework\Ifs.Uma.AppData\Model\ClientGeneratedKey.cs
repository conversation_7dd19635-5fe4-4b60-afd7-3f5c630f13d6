﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.App)]
    [Index(Name = "ix_" + DbTableName, Columns = nameof(TableName) + ", " + nameof(ColumnName), Unique = true)]
    public class ClientGeneratedKey : RowBase
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "client_generated_key";

        private string _tableName;
        [Column(Storage = nameof(_tableName))]
        public string TableName
        {
            get { return _tableName; }
            set { SetProperty(ref _tableName, value); }
        }

        private string _columnName;
        [Column(Storage = nameof(_columnName))]
        public string ColumnName
        {
            get { return _columnName; }
            set { SetProperty(ref _columnName, value); }
        }

        private long _clientId;
        [Column(Storage = nameof(_clientId))]
        public long ClientId
        {
            get { return _clientId; }
            set { SetProperty(ref _clientId, value); }
        }
    }
}
