﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Metadata;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.AppData.Database.Expressions
{
    internal sealed class JsonWhereExpression
    {
        // Any Where done using JsonWhereExpression is used by procedures and offline filters 
        // and will only work against the database. Any Where done by AttributeExpression 
        // should be runnable against the server or the database

        internal Expression Expression { get; }

        internal JsonWhereExpression(EntityDataSource dataSource, JObject where)
            : this(dataSource, where, null, null)
        {
        }

        internal JsonWhereExpression(EntityDataSource dataSource, JObject where, ExecutionContext context, string alias)
        {
            if (dataSource == null) throw new ArgumentNullException(nameof(dataSource));
            if (where == null) throw new ArgumentNullException(nameof(where));
            
            Expression exp = IfsExpression.FromJsonLogic(where);

            if (context != null)
            {
                bool isVar(string path)
                {
                    if (alias != null && path.StartsWith(alias + AttributePath.PathSeparator))
                    {
                        return false;
                    }

                    string varName = path;
                    int varNameEnd = path.IndexOf(AttributePath.PathSeparator);
                    if (varNameEnd >= 0)
                    {
                        varName = varName.Substring(0, varNameEnd);
                    }
                    
                    return context != null && context.Vars.ContainsKey(varName);
                }

                exp = ValueReplacer.Rewrite(exp, context, isVar);
            }

            exp = AttributeFindAndReplacer.Rewrite(exp, dataSource, alias);

            Expression = exp;
        }
    }
}
