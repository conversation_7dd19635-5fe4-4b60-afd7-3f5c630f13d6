﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public static class ApiMethodFactory
    {
        public static Expression CreateMethod(string methodName, List<Expression> parameters)
        {
            switch (methodName.Split('.')[1])
            {
                case "dateDiff":
                    return GetTimeDiffExpression(parameters);
                case "match":
                    return GetMatchExpression(parameters);
                case "contains":
                    return GetContainsExpression(parameters);
                case "substring":
                    return GetSubstringExpression(parameters);
                case "resolveCSV":
                    return GetResolveCsvExpression(parameters);
                default:
                    throw new ArgumentException("Invalid Api function type.");
            }
        }

        private static Expression GetTimeDiffExpression(List<Expression> parameters)
        {
            ApiDateDiffExpression dateDiff = new ApiDateDiffExpression(parameters);
            return dateDiff.CreateCallableExpression();
        }

        private static Expression GetMatchExpression(List<Expression> parameters)
        {
            ApiMatchExpression match = new ApiMatchExpression(parameters);
            return match.CreateCallableExpression();
        }

        private static Expression GetContainsExpression(List<Expression> parameters)
        {
            ApiContainsExpression contains = new ApiContainsExpression(parameters);
            return contains.CreateCallableExpression();
        }

        private static Expression GetSubstringExpression(List<Expression> parameters)
        {
            ApiSubstringExpression substring = new ApiSubstringExpression(parameters);
            return substring.CreateCallableExpression();
        }

        private static Expression GetResolveCsvExpression(List<Expression> parameters)
        {
            ApiResolveCsvExpression resolveCsv = new ApiResolveCsvExpression(parameters);
            return resolveCsv.CreateCallableExpression();
        }
    }
}
