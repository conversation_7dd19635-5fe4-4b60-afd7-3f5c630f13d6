﻿{
  "name": "FndTstOffline",
  "version": "1706901162:1948287535",
  "component": "FNDTST",
  "projection": {
    "service": "FndTstOffline.svc",
    "version": "1948287535",
    "contains": {
      "Customers": {
        "name": "Customers",
        "entity": "TstCustomer",
        "array": true
      },
      "MyVirtuals": {
        "name": "MyVirtuals",
        "entity": "MyVirtual",
        "array": true
      }
    },
    "entities": {
      "TstCustomer": {
        "name": "TstCustomer",
        "hasETag": true,
        "CRUD": "Create,Read,Update,Delete",
        "luname": "TstCustomer",
        "ludependencies": [ "TstCustomer" ],
        "keys": [ "CustomerNo" ],
        "attributes": {
          "CustomerNo": {
            "datatype": "Text",
            "keygeneration": "User"
          },
          "CustomerName": {
            "datatype": "Text",
            "keygeneration": "User"
          },
          "Cf_MyCustomField": {
            "datatype": "Text",
            "keygeneration": "User"
          }
        }
      },
      "MyVirtual": {
        "name": "MyVirtual",
        "hasETag": true,
        "hasKeys": true,
        "entitytype": "Virtual",
        "CRUD": "Create,Read,Update,Delete",
        "luname": "TestText",
        "ludependencies": [
          "TestText"
        ],
        "keys": [
          "Id"
        ],
        "attributes": {
          "Id": {
            "datatype": "Text",
            "keygeneration": "User"
          },
          "Cf_Tstcustomer_MyCustomField": {
            "datatype": "Text",
            "keygeneration": "User",
            "FromEntity": "TstCustomer"
          }
        }
      }
    },
    "procedures": {
      "Action<TestCopyCustomFields>": {
        "name": "TestCopyCustomFields",
        "type": "Action",
        "layers": [
          {
            "vars": [
              {
                "name": "SourceVar",
                "dataType": "Structure",
                "subType": "MyVirtual",
                "collection": false
              },
              {
                "name": "DestinationVar",
                "dataType": "Structure",
                "subType": "TstCustomer",
                "collection": false
              }
            ],
            "execute": [
              {
                "call": {
                  "method": "fetch",
                  "args": {
                    "entity": "MyVirtual",
                    "name": "MyVirtuals"
                  }
                },
                "assign": "SourceVar"
              },
              {
                "call": {
                  "method": "fetch",
                  "args": {
                    "entity": "TstCustomer",
                    "name": "Customers"
                  }
                },
                "assign": "DestinationVar"
              },
              {
                "call": {
                  "method": "copyCustomFields",
                  "args": {
                    "source": "SourceVar",
                    "target": "DestinationVar"
                  }
                }
              },
              {
                "call": {
                  "method": "if",
                  "args": {
                    "expression": {
                      "!=": [
                        {
                          "var": "DestinationVar.Cf_MyCustomField"
                        },
                        "Apollo"
                      ]
                    }
                  }
                },
                "result": {
                  "TRUE": [
                    {
                      "call": {
                        "method": "error",
                        "args": {
                          "msg": "Procedure Failed"
                        }
                      }
                    }
                  ]
                }
              }
            ]
          }
        ]
      }
    }
  }
}
