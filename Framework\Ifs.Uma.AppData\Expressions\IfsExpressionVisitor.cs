﻿using System;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public partial class IfsExpressionVisitor : ExpressionVisitor
    {
        protected static Expression ConvertIfNeeded(Expression exp, Type type)
        {
            if (exp.Type == type)
            {
                return exp;
            }

            return Expression.Convert(exp, type);
        }
    }
}
