﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq.Expressions;
using System.Reflection;

namespace IQToolkit
{
    internal abstract class ExpressionVisitorEx : ExpressionVisitor
    {
        protected ExpressionVisitorEx()
        {
        }

        protected override Expression VisitBlock(BlockExpression node)
        {
            if (node == null) return null;
            IEnumerable<ParameterExpression> variables = VisitParameterList(node.Variables);
            IEnumerable<Expression> expressions = VisitExpressionList(node.Expressions);
            return node.Update(variables, expressions);
        }

        protected override ElementInit VisitElementInit(ElementInit node)
        {
            if (node == null) return null;
            IEnumerable<Expression> arguments = VisitExpressionList(node.Arguments);
            return node.Update(arguments);
        }

        protected override Expression VisitInvocation(InvocationExpression node)
        {
            if (node == null) return null;
            IEnumerable<Expression> arguments = VisitExpressionList(node.Arguments);
            Expression expression = Visit(node.Expression);
            return node.Update(expression, arguments);
        }

        protected override Expression VisitListInit(ListInitExpression node)
        {
            if (node == null) return null;
            NewExpression newExpression = VisitAndConvert(node.NewExpression, "ExpressionVisitorEx.VisitListInit");
            IEnumerable<ElementInit> initializers = VisitElementInitList(node.Initializers);
            return node.Update(newExpression, initializers);
        }

        protected override Expression VisitMemberInit(MemberInitExpression node)
        {
            if (node == null) return null;
            NewExpression newExpression = VisitAndConvert(node.NewExpression, "ExpressionVisitorEx.VisitMemberInit");
            IEnumerable<MemberBinding> bindings = VisitBindingList(node.Bindings);
            return node.Update(newExpression, bindings);
        }

        protected override MemberListBinding VisitMemberListBinding(MemberListBinding node)
        {
            if (node == null) return null;
            IEnumerable<ElementInit> initializers = VisitElementInitList(node.Initializers);
            return node.Update(initializers);
        }

        protected override MemberMemberBinding VisitMemberMemberBinding(MemberMemberBinding node)
        {
            if (node == null) return null;
            IEnumerable<MemberBinding> bindings = VisitBindingList(node.Bindings);
            return node.Update(bindings);
        }

        protected override Expression VisitMethodCall(MethodCallExpression node)
        {
            if (node == null) return null;
            Expression obj = Visit(node.Object);
            IEnumerable<Expression> arguments = VisitExpressionList(node.Arguments);
            return node.Update(obj, arguments);
        }

        protected override Expression VisitNew(NewExpression node)
        {
            if (node == null) return null;
            IEnumerable<Expression> args = VisitMemberAndExpressionList(node.Members, node.Arguments);
            return node.Update(args);
        }

        protected override Expression VisitNewArray(NewArrayExpression node)
        {
            if (node == null) return null;
            IEnumerable<Expression> expressions = VisitExpressionList(node.Expressions);
            return node.Update(expressions);
        }

        protected override Expression VisitParameter(ParameterExpression node)
        {
            return node;
        }

        protected virtual IEnumerable<ParameterExpression> VisitParameterList(ReadOnlyCollection<ParameterExpression> original)
        {
            return VisitAndConvert(original, "ExpressionVisitorEx.VisitParameterList");
        }

        protected virtual IEnumerable<Expression> VisitExpressionList(ReadOnlyCollection<Expression> original)
        {
            return original != null ? Visit(original) : null;
        }

        protected virtual IEnumerable<ElementInit> VisitElementInitList(ReadOnlyCollection<ElementInit> original)
        {
            return Visit(original, VisitElementInit);
        }

        protected virtual IEnumerable<MemberBinding> VisitBindingList(ReadOnlyCollection<MemberBinding> original)
        {
            return Visit(original, VisitMemberBinding);
        }

        protected virtual IEnumerable<Expression> VisitMemberAndExpressionList(ReadOnlyCollection<MemberInfo> members, ReadOnlyCollection<Expression> original)
        {
            if (original == null) return null;
            List<Expression> list = null;
            for (int i = 0, n = original.Count; i < n; i++)
            {
                Expression p = VisitMemberAndExpression(members != null ? members[i] : null, original[i]);
                if (list != null)
                {
                    list.Add(p);
                }
                else if (p != original[i])
                {
                    list = new List<Expression>(n);
                    for (int j = 0; j < i; j++)
                    {
                        list.Add(original[j]);
                    }
                    list.Add(p);
                }
            }
            if (list != null) return list;
            return original;
        }

        protected virtual Expression VisitMemberAndExpression(MemberInfo member, Expression expression)
        {
            return Visit(expression);
        }
    }
}