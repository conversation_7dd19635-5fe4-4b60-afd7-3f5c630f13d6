﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using Unity;
using Unity.Lifetime;
using NUnit.Framework;
using Ifs.Uma.AppData.Execution;

namespace Ifs.Uma.AppData.Tests.Cache
{
    [TestFixture]
    public class CachePreparerTests : FrameworkTest
    {
        [Test]
        public async Task OnePageResults()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            int resultCount = CachedEntity.InitialTakeCount - 1;

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.ResultCount = resultCount;

            PrepareCacheResult result = await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Assert.AreEqual(PrepareCacheResult.Ready, result);
            Assert.AreEqual(1, onlineHandler.QueryCount);

            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(resultCount, rows.Length);
        }

        [Test]
        public async Task OnePageResultsExact()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            int resultCount = CachedEntity.InitialTakeCount;

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.ResultCount = resultCount;

            PrepareCacheResult result = await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Assert.AreEqual(PrepareCacheResult.Ready, result);
            Assert.AreEqual(2, onlineHandler.QueryCount);

            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(resultCount, rows.Length);
        }

        [Test]
        public async Task TwoPageResults()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            int resultCount = CachedEntity.InitialTakeCount + 1;

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.ResultCount = resultCount;

            PrepareCacheResult result = await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Assert.AreEqual(PrepareCacheResult.Ready, result);
            Assert.AreEqual(2, onlineHandler.QueryCount);

            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(resultCount, rows.Length);
        }

        [Test]
        public async Task TwoPreparesOneQuery()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();
            
            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.WaitTime = TimeSpan.FromMilliseconds(250);
            onlineHandler.ResultCount = 5;

            Task<PrepareCacheResult> t1 = cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Task<PrepareCacheResult> t2 = cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");

            await Task.WhenAll(t1, t2);
            
            PrepareCacheResult result = await t1;
            Assert.AreEqual(PrepareCacheResult.Ready, result);

            result = await t2;
            Assert.AreEqual(PrepareCacheResult.Ready, result);

            Assert.AreEqual(1, onlineHandler.QueryCount);

            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(5, rows.Length);
        }

        [Test]
        public async Task OneUpdateAtATime()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.WaitTime = TimeSpan.FromMilliseconds(250);
            onlineHandler.ResultCount = 1;

            Stopwatch sw = Stopwatch.StartNew();

            Task<PrepareCacheResult> t1 = cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Task<PrepareCacheResult> t2 = cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer2");

            await Task.WhenAll(t1, t2);

            sw.Stop();

            Assert.IsTrue(sw.ElapsedMilliseconds >= 500);

            PrepareCacheResult result = await t1;
            Assert.AreEqual(PrepareCacheResult.Ready, result);

            result = await t2;
            Assert.AreEqual(PrepareCacheResult.Ready, result);

            Assert.AreEqual(2, onlineHandler.QueryCount);
            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(1, rows.Length);

            rows = GetStoredRows("TstCustomer2");
            Assert.AreEqual(1, rows.Length);
        }

        [Test]
        public async Task CancelPrepare()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.WaitTime = TimeSpan.FromMilliseconds(500);
            onlineHandler.ResultCount = 5;

            CancellationTokenSource cts = new CancellationTokenSource();
            cts.CancelAfter(TimeSpan.FromMilliseconds(250));

            try
            {
                await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer", cts.Token);
                Assert.Fail();
            }
            catch (OperationCanceledException)
            {
            }

            Assert.IsFalse(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));

            // Cache continues to update in the background
            await Task.Delay(1000);
            Assert.IsTrue(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));
            Assert.AreEqual(1, onlineHandler.QueryCount);
            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(5, rows.Length);
        }

        [Test]
        public async Task CancelOnDispose()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.WaitTime = TimeSpan.FromMilliseconds(500);
            onlineHandler.ResultCount = 5;

            Task t = cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");

            await Task.Delay(100);

            ((IDisposable)cachePreparer).Dispose();

            try
            {
                await t;
                Assert.Fail();
            }
            catch (OperationCanceledException)
            {
            }
            
            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(0, rows.Length);
        }

        [Test]
        public async Task ExpireCache()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.ResultCount = 5;

            PrepareCacheResult result = await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Assert.AreEqual(PrepareCacheResult.Ready, result);
            Assert.AreEqual(1, onlineHandler.QueryCount);

            Assert.IsTrue(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));
            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(5, rows.Length);

            FwDataContext ctx = CreateDataContext();
            CacheStatus status = ctx.CacheStatuses.First(x => x.EntityName == "TstCustomer");
            ctx.CacheStatuses.Attach(status);
            status.LastUpdated = new DateTime(2000, 1, 1, 1, 1, 1);
            ctx.SubmitChanges(false);

            Assert.IsFalse(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));

            result = await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Assert.AreEqual(PrepareCacheResult.Ready, result);

            Assert.IsTrue(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));
            rows = GetStoredRows();
            Assert.AreEqual(5, rows.Length);
        }

        [Test]
        public async Task OfflinePrepareCache()
        {
            Container.RegisterType<IOnlineDataHandler, AlwaysOfflineOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            PrepareCacheResult result = await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Assert.AreEqual(PrepareCacheResult.Offline, result);
        }

        [Test]
        public async Task OfflineIsCacheReady()
        {
            Container.RegisterType<IOnlineDataHandler, AlwaysOfflineOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            bool result = await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer");
            Assert.AreEqual(false, result);
        }

        [Test]
        public async Task AddToCache()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            // Populate the cache with items

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.ResultCount = 5;

            await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Assert.IsTrue(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));

            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(5, rows.Length);

            // Clear the items from the local database so they are no longer in the cache

            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource ds = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstCustomer");
            FwDataContext ctx = CreateDataContext();
            ITable table = ctx.GetTable(ds.Table);
            foreach (RemoteRow row in rows)
            {
                table.DeleteOnSubmit(row);
            }
            ctx.SubmitChanges(false);
            rows = GetStoredRows();
            Assert.AreEqual(0, rows.Length);

            // Make sure the cache thinks it is still valid

            Assert.IsTrue(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));

            // Add items from a query to the cache

            EntityQuery query = new EntityQuery(ds);
            query.Skip = 2;
            await cachePreparer.AddToCacheAsync(query);
            
            Assert.IsTrue(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));

            rows = GetStoredRows();
            Assert.AreEqual(3, rows.Length);
        }

        [Test]
        public async Task ClearCache()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            // Populate the cache with items

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.ResultCount = 5;

            await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomer");
            Assert.IsTrue(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));

            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(5, rows.Length);

            // Clear cache

            await cachePreparer.ClearCache();

            FwDataContext ctx = CreateDataContext();
            bool clearedStatus = !ctx.CacheStatuses.Any(x => x.IsReady);
            Assert.IsTrue(clearedStatus, "Failed to clear cache status table");

            rows = GetStoredRows();
            Assert.AreEqual(0, rows.Length, "Failed to clear cached data");

            Assert.IsFalse(await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomer"));
        }

        [Test]
        public async Task ViewOverCachedEntity()
        {
            Container.RegisterType<IOnlineDataHandler, TestOnlineDataHandler>(new ContainerControlledLifetimeManager());
            ICachePreparer cachePreparer = Resolve<ICachePreparer>();

            int resultCount = 5;

            TestOnlineDataHandler onlineHandler = (TestOnlineDataHandler)Resolve<IOnlineDataHandler>();
            onlineHandler.ResultCount = resultCount;

            bool ready = await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomerQuery");
            Assert.AreEqual(false, ready);

            PrepareCacheResult result = await cachePreparer.PrepareCacheAsync(TestOfflineProjection, "TstCustomerQuery");
            Assert.AreEqual(PrepareCacheResult.Ready, result);
            Assert.AreEqual(1, onlineHandler.QueryCount);

            ready = await cachePreparer.IsCacheReadyAsync(TestOfflineProjection, "TstCustomerQuery");
            Assert.AreEqual(true, ready);

            RemoteRow[] rows = GetStoredRows();
            Assert.AreEqual(resultCount, rows.Length);
        }

        private RemoteRow[] GetStoredRows(string entityName = "TstCustomer")
        {
            IMetadata metadata = Resolve<IMetadata>();
            FwDataContext ctx = CreateDataContext();
            EntityQuery query = new EntityQuery(EntityDataSource.FromEntity(metadata, TestOfflineProjection, entityName));
            return ctx.Query(query).Select(x => x.Row).ToArray();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            Container.RegisterType<ICachePreparer, CachePreparer>(new ContainerControlledLifetimeManager());
            PrepareDatabase<FwDataContext>("Cache.CachePreparerSchema", null);
        }

        public sealed class TestOnlineDataHandler : IOnlineDataHandler
        {
            public TimeSpan WaitTime { get; set; }
            public int ResultCount { get; set; }
            public int QueryCount { get; set; }
            public int DeviceId { get; } = 0;
            public bool IsOnline { get; } = true;
            public string AppName => string.Empty;
            public string UserName => string.Empty;

            public async Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken)
            {
                QueryCount++;

                await Task.Delay(WaitTime, cancelToken);

                bool complete = false;
                List<EntityRecord> records = new List<EntityRecord>();
                
                for (int i = query.Skip.Value; i < query.Take.Value; i++)
                {
                    if (i >= ResultCount)
                    {
                        complete = true;
                        break;
                    }

                    RemoteRow row = new RemoteRow(query.DataSource.Table.TableName);
                    row["CustomerNo"] = "CUST_" + i;

                    EntityRecord record = new EntityRecord(row, null);
                    records.Add(record);
                }

                if (query.Skip.Value + query.Take.Value == ResultCount)
                {
                    complete = true;
                }
               
                return new EntityQueryResult(query, records, !complete);
            }
            
            public Task<ExecuteResult> CallActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> CallBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> CallFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> CallBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> EntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> EntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> EntityUpdateAsync(string projectionName, RemoteRow row, IEnumerable<string> changedMembers, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> EntityPrepareAsync(string entityName, CancellationToken cancelToken, string entitySetName = "")
            {
                throw new NotImplementedException();
            }

            public Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken, string entitySetName = "")
            {
                throw new NotImplementedException();
            }

            public Task<EntityQueryResult> GetFunctionRecordsAsync(EntityQuery query, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
            {
                throw new NotImplementedException();
            }
        }
    }
}
