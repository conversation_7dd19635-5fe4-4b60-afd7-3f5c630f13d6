﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq.Expressions;
using System.Text;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class QueryExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.Query;

        public FromExpression From { get; }
        public ReadOnlyCollection<JoinExpression> Joins { get; }
        public Expression Where { get; }
        public ReadOnlyCollection<SortExpression> Sorts { get; }
        public bool Distinct { get; }
        public List<UnionSelectExpression> UnionSelects { get; }
        public ReadOnlyCollection<ResultColumnExpression> ResultColumns { get; }

        internal QueryExpression(FromExpression from, IEnumerable<JoinExpression> joins, Expression where, IEnumerable<SortExpression> sorts, bool distinct, IEnumerable<ResultColumnExpression> resultColumns)
        {
            if (from == null) throw new ArgumentNullException(nameof(from));
            if (resultColumns == null) throw new ArgumentNullException(nameof(resultColumns));

            From = from;
            Joins = ToReadOnly(joins);
            Where = where;
            Sorts = ToReadOnly(sorts);
            Distinct = distinct;
            ResultColumns = ToReadOnly(resultColumns);
        }

        public QueryExpression(FromExpression from, IEnumerable<ResultColumnExpression> resultColumns)
        {
            From = from;
            ResultColumns = ToReadOnly(resultColumns);
        }

        internal QueryExpression(List<UnionSelectExpression> unionSelectExpressions)
        {
            UnionSelects = unionSelectExpressions;
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitQueryExpression(this);
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(From.ToString());

            foreach (JoinExpression join in Joins)
            {
                sb.AppendLine(" " + join.ToString());
            }

            if (Where != null)
            {
                sb.Append(" WHERE ");
                sb.AppendLine(Where.ToString());
            }

            if (Sorts.Count > 0)
            {
                sb.Append(" ORDER BY ");

                for (int i = 0; i < Sorts.Count; i++)
                {
                    if (i > 0)
                    {
                        sb.Append(", ");
                    }
                    
                    sb.Append(Sorts[i].ToString());
                }
            }

            sb.Append(" SELECT ");

            if (Distinct)
            {
                sb.Append("DISTINCT ");
            }

            for (int i = 0; i < ResultColumns.Count; i++)
            {
                if (i > 0)
                {
                    sb.Append(", ");
                }

                sb.Append(ResultColumns[i].ToString());
            }

            return sb.ToString();
        }

        public Expression Update(FromExpression from, IEnumerable<JoinExpression> joins, Expression where,
            IEnumerable<SortExpression> sorts, bool distinct, IEnumerable<ResultColumnExpression> resultColumns)
        {
            if (From == from &&
                ReferenceEquals(Joins, joins) &&
                ReferenceEquals(Sorts, sorts) &&
                Where == where &&
                Distinct == distinct &&
                ReferenceEquals(ResultColumns, resultColumns) )
            {
                return this;
            }

            return new QueryExpression(from, joins, where, sorts, distinct, resultColumns);
        }
    }

    public partial class IfsExpression
    {
        public static QueryExpression Query(FromExpression from, IEnumerable<JoinExpression> joins, Expression where,
            IEnumerable<SortExpression> sorts, bool distinct, IEnumerable<ResultColumnExpression> resultColumns)
        {
            return new QueryExpression(from, joins, where, sorts, distinct, resultColumns);
        }

        public static QueryExpression Query(List<UnionSelectExpression> unionSelectExpressions)
        {
            return new QueryExpression(unionSelectExpressions);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitQueryExpression(QueryExpression exp)
        {
            FromExpression from = VisitAndConvert(exp.From, nameof(VisitQueryExpression));
            ReadOnlyCollection<JoinExpression> joins = Visit(exp.Joins, (x) => VisitAndConvert(x, nameof(VisitQueryExpression)));
            Expression where = exp.Where == null ? null : Visit(exp.Where);
            ReadOnlyCollection<SortExpression> sorts = Visit(exp.Sorts, (x) => VisitAndConvert(x, nameof(VisitQueryExpression)));
            ReadOnlyCollection<ResultColumnExpression> resultColumns = Visit(exp.ResultColumns, (x) => VisitAndConvert(x, nameof(VisitQueryExpression)));
            return exp.Update(from, joins, where, sorts, exp.Distinct, resultColumns);
        }
    }
}
