﻿using Ifs.Uma.UI;
using Ifs.Uma.Utility;
using Prism.Events;
using System;
using System.Threading.Tasks;
using Ifs.Uma.Database;
using Ifs.Uma.UI.Services;
using Unity.Attributes;
using Ifs.Uma.Localization;

namespace Ifs.Uma.Framework.UI.Observables
{
    public class EditingViewModel : EditAwareViewModel
    {
        public Command SaveCommand { get; }
        public Command CancelCommand { get; }

        private bool _isSaving;
        public bool IsSaving
        {
            get { return _isSaving; }
            private set
            {
                if (_isSaving != value)
                {
                    _isSaving = value;
                    OnPropertyChanged(() => IsSaving);
                    OnIsSavingChanged();
                }
            }
        }

        private readonly IDialogService _dialogService;

        public EditingViewModel(IEventAggregator eventAggregator, [OptionalDependency] IDialogService dialogService)
            : base(eventAggregator)
        {
            _dialogService = dialogService;

            SaveCommand = Command.FromAsyncMethod(SaveChangesAsync, () => !IsSaving && HasChanges);
            CancelCommand = Command.FromAsyncMethod(CancelChangesAsync, () => !IsSaving && HasChanges);
        }

        protected override void OnHasChangesChanged()
        {
            base.OnHasChangesChanged();

            SaveCommand.RaiseCanExecuteChanged();
            CancelCommand.RaiseCanExecuteChanged();
        }

        public async Task<bool> SaveChangesAsync()
        {
            if (!HasChanges || IsSaving)
            {
                throw new InvalidOperationException();
            }

            IsSaving = true;

            bool isValid = await ValidateAsync();

            if (isValid)
            {
                Exception exception = null;
                try
                {
                    await OnSaveChangesAsync();
                }
                catch (Exception ex)
                {
                    exception = ex;
                    Logger?.HandleException(ExceptionType.Unexpected, ex);
                }

                if (exception != null)
                {
                    isValid = false;

                    DbException dbException = exception as DbException;
                    if (dbException != null && dbException.ExceptionType != DbExceptionType.Unknown)
                    {
                        await _dialogService.ShowException(dbException, title: Strings.FailedToSaveChanges);
                    }
                    else
                    {
                        await _dialogService.Alert(null, Strings.FailedToSaveChanges);
                    }
                }
            }

            IsSaving = false;
            return isValid;
        }

        protected virtual async Task OnSaveChangesAsync()
        {
            await Task.Yield();
        }

        protected virtual void OnIsSavingChanged()
        {
            SaveCommand.RaiseCanExecuteChanged();
            CancelCommand.RaiseCanExecuteChanged();
        }

        protected async Task<bool> ValidateAsync()
        {
            bool result = false;
            try
            {
                result = await OnValidateAsync();
            }
            catch (Exception ex)
            {
                Logger?.HandleException(ExceptionType.Unexpected, ex);
                await _dialogService.ShowException(ex, title: Strings.FailedToValidateChanges);
                result = false;
            }
            return result;
        }

        protected virtual async Task<bool> OnValidateAsync()
        {
            return await Task.FromResult(OnValidate());
        }

        protected virtual bool OnValidate()
        {
            return true;
        }

        public async Task CancelChangesAsync()
        {
            if (!HasChanges || IsSaving)
            {
                throw new InvalidOperationException();
            }

            IsSaving = true;

            Exception exception = null;
            try
            {
                await OnCancelChangesAsync();
            }
            catch (Exception ex)
            {
                exception = ex;
                Logger?.HandleException(ExceptionType.Unexpected, ex);
            }

            if (exception != null)
            {
                await _dialogService.Alert(null, Strings.FailedToCancelChanges);
            }

            IsSaving = false;
        }

        protected virtual Task OnCancelChangesAsync()
        {
            OnCancelChanges();
            return Task.FromResult(true);
        }

        protected virtual void OnCancelChanges()
        {
        }
    }
}
