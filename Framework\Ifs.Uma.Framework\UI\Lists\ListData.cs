﻿using Ifs.Uma.AppData;
using Ifs.Uma.UI.Lists;
using Unity.Attributes;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Lists
{
    public abstract class ListData<T> : ListDataBase<T> where T : class
    {
        public IEventAggregator EventAggregator { get; }

        protected ListData([OptionalDependency] IEventAggregator eventAggregator)
        {
            EventAggregator = eventAggregator;
        }
        
        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (EventAggregator != null)
            {
                if (IsActive)
                {
                    EventAggregator.GetEvent<DataChangedEvent>().Subscribe(OnDataChanged, ThreadOption.UIThread);
                }
                else
                {
                    EventAggregator.GetEvent<DataChangedEvent>().Unsubscribe(OnDataChanged);
                }
            }
        }

        protected virtual void OnDataChanged(DataChangedEventArgs args)
        {
        }
    }
}
