﻿using System;
using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Comm.TouchApps
{
    internal class ActionResource : CallMethodResource
    {
        protected override string ResourceSection => "action";

        public void ValidateDateParameters(CpiAction action)
        {
            if (action == null)
            {
                Logger.Current.Error("Action is null");
                throw new ArgumentNullException(nameof(action));
            }

            IEnumerable<CpiParam> dateTypeParams = action.Parameters?.AsEnumerable().Where(p => p.DataType.ToType() == typeof(DateTime));

            if (dateTypeParams == null || !dateTypeParams.Any())
            {
                return;
            }

            dateTypeParams.ToList().ForEach(p =>
            {
                JToken token = Data.GetValue(p.Name);
                if (token.Type == JTokenType.Date)
                {
                    DateTime value = (DateTime)token;
                    string formattedDate = value.ToString("s");
                    Data[p.Name] = GetDateString(formattedDate, p.DataType);
                }
            });
        }
    }
}
