﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemHash : SystemFunction
    {
        public const string FunctionName = "Hash";
        private readonly HashSet<string> _supportedHashFunctions = new HashSet<string>();

        public SystemHash()
            : base(FunctionName, 2)
        {
            _supportedHashFunctions.Add(nameof(SHA256));
            _supportedHashFunctions.Add(nameof(SHA384));
            _supportedHashFunctions.Add(nameof(SHA512));
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string input = parameters[0].GetString();
            if (string.IsNullOrEmpty(input))
            {
                return null;
            }

            string hashFunction = parameters[1].GetString();
            if (string.IsNullOrEmpty(hashFunction))
            {
                return null;
            }

            if (!_supportedHashFunctions.Contains(hashFunction))
            {
                Logger.Current.Error("Invalid hash function '{0}' specified as argument to System.Hash. Supported functions are SHA256, SHA384 and SHA512.", hashFunction);
                return null;
            }

            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] resultBytes = null;

            switch (hashFunction)
            {
                case nameof(SHA256):
                    using (SHA256 sha256 = SHA256.Create())
                    {
                       resultBytes = sha256.ComputeHash(inputBytes);
                    }
                    break;
                case nameof(SHA384):
                    using (SHA384 sha384 = SHA384.Create())
                    {
                        resultBytes = sha384.ComputeHash(inputBytes);
                    }
                    break;
                case nameof(SHA512):
                    using (SHA512 sha512 = SHA512.Create())
                    {
                        resultBytes = sha512.ComputeHash(inputBytes);
                    }
                    break;
                default:
                    break;
            }

            if (resultBytes != null && resultBytes.Length > 0)
            {
                return BitConverter.ToString(resultBytes).Replace("-", string.Empty).ToUpper();
            }

            return null;
        }
    }
}
