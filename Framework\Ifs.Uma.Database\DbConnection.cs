﻿using System;
using System.Collections.Generic;
using System.Threading;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public enum DbStatus
    {
#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
        FileNotFound,   // Database does not exist (in homage to TDWTF)
        Unauthorized,   // Database exists but connection is unauthorized
        Invalid,        // Database exists but fails to open (wrong sort of file?)
        Valid           // Database exists and can be opened
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row
    }

    public abstract class DbConnection : IDisposable
    {
        public abstract string ConnectionString { get; }
        public IMapEnumeration EnumMapper { get; private set; }
        public bool TraceFlag { get; set; }

        public DbTransaction BeginTransaction()
        {
            ICollection<DbTransaction> transactions = m_transactions;
            if (transactions == null) throw new ObjectDisposedException("DbConnection");
            if (Interlocked.CompareExchange(ref m_transactionDepth, TRANSACTION_ACTIVE, TRANSACTION_INACTIVE) == TRANSACTION_ACTIVE)
                throw new InvalidOperationException("Transaction already active");
            DbTransaction result = null;
            try
            {
                result = NewTransaction();
                result.Start();
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.BeginTransactionError, ex.FormatXml(false, false));
                if (result != null)
                {
                    result.Dispose();
                }
                throw;
            }
            if (result != null)
            {
                transactions.Add(result);
            }
            return result;
        }

        public void Open()
        {
            try
            {
                DoOpen();
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.OpenDatabaseError, ex.FormatXml(false, false));
                throw;
            }
        }

        public void CreateDatabase()
        {
            try
            {
                DoCreate();
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.CreateDatabaseError, ex.FormatXml(false, false));
                throw;
            }
        }

        public void DropDatabase()
        {
            try
            {
                DoDrop();
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.DropDatabaseError, ex.FormatXml(false, false));
                throw;
            }
        }

        public DbStatus GetStatus()
        {
            try
            {
                return DoStatus();
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.GetDbStatusError, ex.FormatXml(false, false));
                throw;
            }
        }

        public DbCommand CreateCommand(DbTransaction transaction)
        {
            if (transaction != null && transaction.Connection != this)
                throw new ArgumentOutOfRangeException("transaction");
            ICollection<DbCommand> commands = m_commands;
            if (commands == null) throw new ObjectDisposedException("DbConnection");
            DbCommand result;
            try
            {
                result = NewCommand(transaction);
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.NewCommandError, ex.FormatXml(false, false));
                throw;
            }
            if (result != null)
            {
                commands.Add(result);
            }
            return result;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected DbConnection(ILogger logger, bool traceFlag, IMapEnumeration enumMapper)
        {
            Logger = logger;
            TraceFlag = traceFlag;
            EnumMapper = enumMapper;
            m_transactions = new SynchronisedCollection<DbTransaction>();
            m_commands = new SynchronisedCollection<DbCommand>();
            m_transactionDepth = TRANSACTION_INACTIVE;
        }

        ~DbConnection()
        {
            Dispose(false);
        }

        //Just create the transaction object in NewTransaction
        //Override DbTransaction.DoStart for any heavy lifting
        protected abstract DbTransaction NewTransaction();
        protected abstract DbCommand NewCommand(DbTransaction transaction);
        protected abstract void DoOpen();
        protected abstract void DoCreate();
        protected abstract void DoDrop();
        protected abstract DbStatus DoStatus();

        protected ILogger Logger { get; private set; }

        protected void TransactionDisposed(DbTransaction transaction)
        {
            ICollection<DbTransaction> transactions = m_transactions;
            if (transaction != null && transactions != null)
            {
                transactions.Remove(transaction);
            }
        }

        protected void CommandDisposed(DbCommand command)
        {
            ICollection<DbCommand> commands = m_commands;
            if (command != null && commands != null)
            {
                commands.Remove(command);
            }
        }

        private ICollection<DbTransaction> m_transactions;
        private ICollection<DbCommand> m_commands;
        private int m_transactionDepth;

        private const int TRANSACTION_ACTIVE = 1;
        private const int TRANSACTION_INACTIVE = 0;

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                UsefulExtensions.ThreadSafeDisposeAll(ref m_commands);
                UsefulExtensions.ThreadSafeDisposeAll(ref m_transactions);
            }
        }
    }
}
