﻿using System.Linq;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemGetPkFromRowId : SystemFunction
    {
        public const string FunctionName = "GetPkFromRowId";

        public SystemGetPkFromRowId()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string tableName = parameters[0].GetString();
            double? rowId = parameters[1].GetNumber();

            if (tableName == null)
            {
                throw new ProcedureException($"TableName parameter of {FunctionNamespace}.{FunctionName} cannot be null");
            }

            if (rowId == null)
            {
                throw new ProcedureException($"RowId parameter of {FunctionNamespace}.{FunctionName} cannot be null");
            }

            IMetaTable metaTable = context.Metadata.MetaModel.GetTable(tableName);

            if (metaTable == null)
            {
                throw new ProcedureException($"{FunctionNamespace}.{FunctionName} - Could not find table '{tableName}' in the local database");
            }

            ITable<RemoteRow> table = context.DbDataContext.GetTable(metaTable) as ITable<RemoteRow>;
            RemoteRow row = table?.FirstOrDefault(x => x.RowId == rowId);

            return row != null ? ObjPrimaryKey.FromPrimaryKey(context.Metadata.MetaModel, row).ToKeyRef() : null;
        }
    }
}
