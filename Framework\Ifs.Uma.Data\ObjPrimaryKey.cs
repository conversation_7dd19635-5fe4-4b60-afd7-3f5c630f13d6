﻿using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Ifs.Uma.Data
{
    public class ObjPrimaryKey : IEquatable<ObjPrimaryKey>
    {
        private static readonly char[] KeySeparator = new char[] { '\u001F' };
        private static readonly char[] KeyRefSeparator = new char[] { '^' };
        private static readonly string EnumPrefix = "IfsApp";

        private readonly IMetaTable _table;
        private readonly Tuple<IMetaDataMember, object>[] _values;

        public IMetaTable Table => _table;
        public IEnumerable<Tuple<IMetaDataMember, object>> Values => _values;

        public static ObjPrimaryKey FromKeySeparatedValues(IMetaTable table, string joinedKeyValues)
        {
            if (joinedKeyValues == null) throw new ArgumentNullException(nameof(joinedKeyValues));

            IMetaDataMember[] key = GetPrimaryKey(table, true);

            if (key == null)
            {
                return null;
            }
            
            string[] keyValues = joinedKeyValues.Split(KeySeparator, StringSplitOptions.None);

            if (keyValues.Length != key.Length)
            {
                return null;
            }

            Tuple<IMetaDataMember, object>[] values = new Tuple<IMetaDataMember, object>[key.Length];
            for (int i = 0; i < values.Length; i++)
            {
                object value = key[i].ConvertValue(keyValues[i]);
                values[i] = Tuple.Create(key[i], value);
            }

            return new ObjPrimaryKey(table, values);
        }

        public static ObjPrimaryKey FromKeyRef(IMetaModel model, string luName, string keyRef)
        {
            if (model == null) throw new ArgumentNullException(nameof(model));
            if (luName == null) throw new ArgumentNullException(nameof(luName));
            if (keyRef == null) throw new ArgumentNullException(nameof(keyRef));

            IMetaTable referencedTable = model.GetTable(luName.ToLowerCaseUnderscore());

            if (referencedTable == null)
            {
                return null;
            }

            IMetaDataMember[] key = GetPrimaryKey(referencedTable, false);
            IDictionary<string, string> keyValues = KeyRefToDictionary(keyRef);

            if (key == null || keyValues == null)
            {
                return null;
            }

            Tuple<IMetaDataMember, object>[] values = new Tuple<IMetaDataMember, object>[key.Length];
            for (int i = 0; i < values.Length; i++)
            {
                IMetaDataMember dataMember = key[i];
                string colName = dataMember.ColumnName.ToUpperCaseUnderscore();

                string strValue;
                if (keyValues.TryGetValue(colName, out strValue))
                {
                    object value = dataMember.ConvertValue(strValue);
                    values[i] = Tuple.Create(dataMember, value);
                }
                else
                {
                    return null;
                }
            }

            return new ObjPrimaryKey(referencedTable, values);
        }

        public Dictionary<string, object> GetAttributeValues()
        {
            if (_values == null)
            {
                return null;
            }

            Dictionary<string, object> result = new Dictionary<string, object>();
            foreach (Tuple<IMetaDataMember, object> keyPair in _values)
            {
                result.Add(keyPair.Item1.PropertyName, keyPair.Item2);
            }

            return result;
        }

        public static ObjPrimaryKey FromPrimaryKey(IMetaModel metaModel, object row)
        {
            if (metaModel == null) throw new ArgumentNullException(nameof(metaModel));
            if (row == null) throw new ArgumentNullException(nameof(row));

            RemoteRow remoteRow = row as RemoteRow;
            if (remoteRow != null)
            {
                IMetaTable table = metaModel.GetTable(remoteRow.TableName);
                return table == null ? null : FromPrimaryKey(table, row);
            }
            else
            {
                IMetaTable table = metaModel.GetTable(row.GetType());
                return table == null ? null : FromPrimaryKey(table, row);
            }
        }

        //Returns the keys in QueryParam format
        public static string FromPrimaryKeyQueryParams(IMetaModel metaModel, object row, string projection)
        {
            ObjPrimaryKey keyref = FromPrimaryKey(metaModel, row);
            string primaryKeyString = keyref.ToFormattedKeyRef(projection);
            if (!string.IsNullOrEmpty(primaryKeyString))
            {
                primaryKeyString = primaryKeyString.Replace(KeyRefSeparator[0].ToString(), ",");
                primaryKeyString = primaryKeyString.Remove(primaryKeyString.Length - 1);
                primaryKeyString = char.ToUpper(primaryKeyString[0]) + primaryKeyString.Substring(1);
                if (!string.IsNullOrEmpty(primaryKeyString))
                {
                    primaryKeyString = "(" + primaryKeyString + ")";
                }
            }

            return primaryKeyString;
        }

        public static ObjPrimaryKey FromPrimaryKey(IMetaTable table, object row)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));
            if (row == null) throw new ArgumentNullException(nameof(row));
            
            return FromPrimaryKey(table, new RowMemberAccessor(table, row));
        }
        
        public static ObjPrimaryKey FromPrimaryKey(IMetaTable table, IReadOnlyDictionary<string, object> data)
        {
            if (data == null) throw new ArgumentNullException(nameof(data));
            return FromPrimaryKey(table, new DictionaryMemberAccessor(data));
        }

        public static ObjPrimaryKey FromPrimaryKey(IMetaTable table, IMemberAccessor data)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));
            if (data == null) throw new ArgumentNullException(nameof(data));

            IMetaDataMember[] pkMembers = GetPrimaryKey(table, false);

            if (pkMembers == null)
            {
                return null;
            }

            Tuple<IMetaDataMember, object>[] values = new Tuple<IMetaDataMember, object>[pkMembers.Length];
            for (int i = 0; i < values.Length; i++)
            {
                data.TryGetValue(pkMembers[i], out object value);
                if (value == null)
                {
                    return null;
                }
                else
                {
                    values[i] = Tuple.Create(pkMembers[i], value);
                }
            }

            return new ObjPrimaryKey(table, values);
        }

        public static ObjPrimaryKey FromTransitionChanges(IMetaTable table, IEnumerable<TransitionRowField> changes)
        {
            IMetaDataMember[] pkMembers = GetPrimaryKey(table, false);

            if (pkMembers == null)
            {
                return null;
            }

            Tuple<IMetaDataMember, object>[] values = new Tuple<IMetaDataMember, object>[pkMembers.Length];
            for (int i = 0; i < values.Length; i++)
            {
                TransitionRowField change = changes.FirstOrDefault(x => x.FieldName == pkMembers[i].ColumnName);

                if (change == null)
                {
                    return null;
                }

                object value = BinarySerializerHelper.ByteArrayToObject(change.NewValue);

                if (value == null)
                {
                    return null;
                }

                values[i] = Tuple.Create(pkMembers[i], value);
            }

            return new ObjPrimaryKey(table, values);
        }

        public static ObjPrimaryKey FromRelation(IMetaModel model, IMetaRelation relation, object row)
        {
            return FromRelation(model, relation, new RowMemberAccessor(relation.Table, row));
        }

        public static ObjPrimaryKey FromRelation(IMetaModel model, IMetaRelation relation, IReadOnlyDictionary<string, object> data)
        {
            return FromRelation(model, relation, new DictionaryMemberAccessor(data));
        }

        public static ObjPrimaryKey FromRelation(IMetaModel model, IMetaRelation relation, IMemberAccessor data)
        {
            if (relation == null)
                throw new ArgumentNullException(nameof(relation));
            switch (relation.RelationType)
            {
                case RelationType.Reference:
                    return FromRelationReference(relation, data);
                case RelationType.ObjectConnection:
                    return FromRelationObjectConnection(model, relation, data);
                default:
                    return null;
            }
        }

        private static ObjPrimaryKey FromRelationReference(IMetaRelation relation, IMemberAccessor data)
        {
            IMetaDataMember[] refMembers = relation.ReferencedColumns.ToArray();
            Tuple<IMetaDataMember, object>[] values = new Tuple<IMetaDataMember, object>[refMembers.Length];
            for (int i = 0; i < values.Length; i++)
            {
                IMetaDataMember dataMember = relation.Columns.ElementAt(i);

                object value;
                if (data.TryGetValue(dataMember, out value))
                {
                    values[i] = Tuple.Create(refMembers[i], value);
                }
                else
                {
                    return null;
                }
            }

            return new ObjPrimaryKey(relation.ReferencedTable, values);
        }

        private static ObjPrimaryKey FromRelationObjectConnection(IMetaModel model, IMetaRelation relation, IMemberAccessor data)
        {
            IMetaDataMember luNameMember = relation.Columns.ElementAt(0);
            object luName;
            if (!data.TryGetValue(luNameMember, out luName))
            {
                // Data is not for this relation
                return null;
            }

            IMetaDataMember keyRefMember = relation.Columns.ElementAt(1);
            object keyRef;
            if (!data.TryGetValue(keyRefMember, out keyRef))
            {
                // No KeyRef to map
                return null;
            }

            string strLuName = luName as string;
            string strKeyRef = keyRef as string;            
            if (strLuName == null || strKeyRef == null)
            {
                // No KeyRef to map
                return null;
            }

            return FromKeyRef(model, strLuName, strKeyRef);
        }

        private static IDictionary<string, string> KeyRefToDictionary(string keyRef)
        {
            // IFS do not support the '^' character in primary key values that support Object Connections
            Dictionary<string, string> result = new Dictionary<string, string>();
            string[] keyRefParts = keyRef.Split(KeyRefSeparator, StringSplitOptions.RemoveEmptyEntries);
            foreach (string keyRefPart in keyRefParts)
            {
                int eqPos = keyRefPart.IndexOf('=');

                if (eqPos < 0)
                {
                    return null;
                }

                string key = keyRefPart.Substring(0, eqPos).ToUpperCaseUnderscore();
                string value = keyRefPart.Substring(eqPos + 1);
                result[key] = value;
            }
            return result;
        }

        private ObjPrimaryKey(IMetaTable table, Tuple<IMetaDataMember, object>[] values)
        {
            _table = table;
            Array.Sort<Tuple<IMetaDataMember, object>>(values, (x, y) => x.Item1.Index.CompareTo(y.Item1.Index));
            _values = values;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1062:Validate arguments of public methods", MessageId = "0")]
        public void ExtractValues(ref IDictionary<IMetaDataMember, object> values)
        {
            if (values == null)
            {
                values = new Dictionary<IMetaDataMember, object>();
            }

            foreach (Tuple<IMetaDataMember, object> item in _values)
            {
                values[item.Item1] = item.Item2;
            }
        }

        public void ExtractValues(IMetaRelation relation, ref IDictionary<IMetaDataMember, object> values)
        {
            if (relation == null) throw new ArgumentNullException("relation");

            if (values == null)
            {
                values = new Dictionary<IMetaDataMember, object>();
            }

            switch (relation.RelationType)
            {
                case RelationType.Reference:
                    ExtractValuesReference(relation, values);
                    break;
                case RelationType.ObjectConnection:
                    ExtractValuesObjectConnection(relation, values);
                    break;
                default:
                    break;
            }
        }

        private void ExtractValuesReference(IMetaRelation relation, IDictionary<IMetaDataMember, object> values)
        {
            IMetaDataMember[] refMembers = relation.ReferencedColumns.ToArray();
            foreach (Tuple<IMetaDataMember, object> item in _values)
            {
                int reletionIndex = Array.IndexOf(refMembers, item.Item1);
                IMetaDataMember dataMember = relation.Columns.ElementAt(reletionIndex);

                values[dataMember] = item.Item2;
            }
        }

        private void ExtractValuesObjectConnection(IMetaRelation relation, IDictionary<IMetaDataMember, object> values)
        {
            StringBuilder keyRef = new StringBuilder();

            // IFS do not support the '^' character in primary key values that support Object Connections
            IMetaDataMember[] refMembers = GetPrimaryKey(Table, true);
            foreach (IMetaDataMember refMember in refMembers.OrderBy(x => x.ColumnName))
            {
                string keyRefColumnName = refMember.ColumnName.ToUpperCaseUnderscore();

                keyRef.Append(keyRefColumnName);
                keyRef.Append('=');

                Tuple<IMetaDataMember, object> value = _values.First(x => x.Item1 == refMember);
                string strValue = ObjectConverter.ToString(value.Item2);
                keyRef.Append(strValue);

                keyRef.Append('^');
            }

            IMetaDataMember luNameMember = relation.Columns.ElementAt(0);
            values[luNameMember] = Table.TableName.ToTitleCase();

            IMetaDataMember keyRefMember = relation.Columns.ElementAt(1);
            values[keyRefMember] = keyRef.ToString();
        }

        public string ToKeySeparatedValues()
        {
            StringBuilder sb = new StringBuilder();

            foreach (var item in Values.OrderBy(x => x.Item1.ServerIndex).ThenBy(x => x.Item1.Index))
            {
                if (sb.Length > 0)
                {
                    sb.Append(KeySeparator);
                }

                string value = ObjectConverter.ToString(item.Item2);
                sb.Append(value);
            }

            return sb.ToString();
        }

        public string ToKeyRef()
        {
            StringBuilder sb = new StringBuilder();

            foreach (Tuple<string, string> item in Values
                    .Select(x => Tuple.Create(x.Item1.ColumnName.ToUpperCaseUnderscore(), ObjectConverter.ToString(x.Item2)))
                    .OrderBy(x => x.Item1))
            {
                sb.Append(item.Item1);
                sb.Append('=');
                sb.Append(item.Item2);

                sb.Append(KeyRefSeparator);
            }

            return sb.ToString();
        }

        public string ToFormattedKeyRef(string projection, bool urlEncode = false)
        {
            StringBuilder sb = new StringBuilder();

            foreach (Tuple<IMetaDataMember, object> item in Values
                    .Select(x => Tuple.Create(x.Item1, x.Item1.Enumeration != null ? 
                    (EnumPrefix + "." + projection + "." + x.Item1.Enumeration.Name + "'" + x.Item2 + "'") : (x.Item2 is string ? "\'" + x.Item2 + "\'" : x.Item2)))
                    .OrderBy(x => x.Item1.ColumnName.ToUpperCaseUnderscore()))
            {
                sb.Append(item.Item1.ColumnName.ToPascalCase());
                sb.Append('=');

                if (item.Item2 is DateTime || item.Item2 is DateTime?)
                {
                    if (item.Item1.DateFormat == DateFormats.Date)
                    {
                        sb.Append(((DateTime)item.Item2).ToString(ObjectConverter.DateFormat));
                    }
                    else if (item.Item1.DateFormat == DateFormats.Time)
                    {
                        sb.Append(((DateTime)item.Item2).ToString(ObjectConverter.TimeFormat));
                    }
                    else if (item.Item1.DateFormat == DateFormats.Timestamp)
                    {
                        sb.Append(((DateTime)item.Item2).ToString(ObjectConverter.DateTimeZoneFormat));
                    }
                }
                else
                {
                    if (item.Item2 is string && urlEncode && item.Item1.Enumeration == null)
                    {
                        sb.Append(StringExtensions.HandleApostrophe(item.Item2.ToString().Replace("=", "%3D").Replace("^", "%5E")));
                    }
                    else
                    {
                        sb.Append(item.Item2);
                    }
                }

                sb.Append(KeyRefSeparator);
            }

            return sb.ToString();
        }

        public override bool Equals(object obj)
        {
            ObjPrimaryKey other = obj as ObjPrimaryKey;

            if (other != null)
            {
                return Equals(other);
            }

            return false;
        }

        public bool Equals(ObjPrimaryKey other)
        {
            if (other == null)
            {
                return false;
            }

            if (object.ReferenceEquals(this, other))
            {
                return true;
            }

            if (_table != other._table) return false;
            if (_values.Length != other._values.Length) return false;

            for (int i = 0; i < _values.Length; i++)
            {
                if (!object.Equals(_values[i], other._values[i]))
                {
                    return false;
                }
            }

            return true;
        }

        public override int GetHashCode()
        {
            int hc = _table.GetHashCode();
            hc = CombineHashCodes(hc, _values.Length.GetHashCode());

            for (int i = 0; i < _values.Length; i++)
            {
                object value = _values[i];
                hc = CombineHashCodes(hc, value == null ? 13 : value.GetHashCode());
            }

            return hc;
        }

        private static IMetaDataMember[] GetPrimaryKey(IMetaTable table, bool serverOrdered)
        {
            IEnumerable<IMetaDataMember> key = table.Key(KeyChoice.DefaultKey);

            if (serverOrdered && key != null)
            {
                // Need to order by by ServerIndex here since this is the order of the key columns
                return key.OrderBy(x => x.ServerIndex).ThenBy(x => x.Index).ToArray();
            }

            return key?.ToArray();
        }

        private static int CombineHashCodes(int h1, int h2)
        {
            return (((h1 << 5) + h1) ^ h2);
        }
    }
}
