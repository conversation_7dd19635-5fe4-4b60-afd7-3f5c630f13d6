﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit
{
    /// <summary>
    /// Finds the first sub-expression that is of a specified type
    /// </summary>
    internal class TypedSubtreeFinder : ExpressionVisitorEx
    {
        Expression m_root;
        TypeInfo m_type;

        private TypedSubtreeFinder(Type type)
        {
            m_type = type.GetTypeInfo();
        }

        public static Expression Find(Expression expression, Type type)
        {
            TypedSubtreeFinder finder = new TypedSubtreeFinder(type);
            finder.Visit(expression);
            return finder.m_root;
        }

        public override Expression Visit(Expression node)
        {
            Expression result = base.Visit(node);
            // remember the first sub-expression that produces a result
            // that can be assigned to the required type e.g. "kind of IQueryable"
            if (m_root == null && result != null)
            {
                if (m_type.IsAssignableFrom(result.Type.GetTypeInfo()))
                {
                    m_root = result;
                }
            }
            return result;
        }
    }
}