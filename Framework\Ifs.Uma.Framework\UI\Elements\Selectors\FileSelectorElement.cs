﻿using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Location;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Location;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements.Selectors
{
    public class FileSelectorElement : ElementBase
    {
        private readonly IExpressionRunner _expressionRunner;
        private readonly IMetadata _metadata;
        private readonly IFileService _fileService;
        private readonly IAppParameters _appParameters;
        private readonly ICommandExecutor _commandExecutor;

        private string _componentName;

        protected override BindingType BindingPropertyType => BindingType.Reference;

        private CpiFileSelector _cpiFileSelector;

        public FilePickerField Field { get; }

        public FileSelectorState State { get; private set; }

        protected override bool OnInitialize()
        {
            _cpiFileSelector = Content?.FileSelector;
            Label = Content?.FileSelector?.Label;

            if (!string.IsNullOrEmpty(Content?.FileSelector?.Name))
            {
                _componentName = Content.FileSelector.Name;
            }

            State = new FileSelectorState(PageData?.ViewState, _componentName);

            return _cpiFileSelector != null;
        }

        public FileSelectorElement(IMetadata metadata, IExpressionRunner expressionRunner, IFileService fileService, IAppParameters appParameters, ICommandExecutor commandExecutor)
        {
            _metadata = metadata;
            _expressionRunner = expressionRunner;
            _fileService = fileService;
            _appParameters = appParameters;
            _commandExecutor = commandExecutor;

            HasHeader = false;

            Field = new FilePickerField();
            Field.ShowLabel = false;
            Field.SizeHint = SizeHint.FullWidth;
            Field.ValueChanged += Field_ValueChanged;
            Field.PropertyChanged += Field_PropertyChanged;
        }

        private void Field_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(Field.FileExtensions) && _appParameters.IsEnhancedMediaEnabled())
            {
                Field.SetAllowVideoAndAudio();
            }
        }

        protected override void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            State = new FileSelectorState(newValue?.ViewState, _componentName);
            base.OnPageDataChanged(oldValue, newValue);
        }

        protected override void OnDataChanged()
        {
            UpdateField();
        }

        private void UpdateField()
        {
            if (_cpiFileSelector != null)
            {
                Field.Name = _cpiFileSelector.Label;
                Field.ValueType = RunCheck(_cpiFileSelector.OfflineMultiple ?? _cpiFileSelector.Multiple, false) ? FilePickerValueType.PickedFileArray : FilePickerValueType.PickedFile;
                Field.IsReadOnly = !RunCheck(_cpiFileSelector.OfflineEnabled ?? _cpiFileSelector.Enabled, false);
            }
        }

        protected override bool OnLoad()
        {
            Field.ImageOptions = new ImageOptions(_appParameters.GetPictureMaxDimension(),
                                                  _appParameters.GetPictureMaxBytes(),
                                                  _appParameters.IsExistingDeviceMediaAllowed());
            Field.FileService = _fileService;

            if (_appParameters.IsEnhancedMediaEnabled())
            {
                Field.MaxVideoBytes = _appParameters.GetVideoMaxMb() != 0 ? Utils.ConvertMbToBytes(_appParameters.GetVideoMaxMb()) : Utils.ConvertMbToBytes(MediaHandler.DefaultVideoMaxMb);
            }

            bool isEnabled = _expressionRunner.RunCheck(Content?.FileSelector?.Enabled, ViewData, false);
            return isEnabled;
        }

        protected override void OnRecordLoaded()
        {
            base.OnRecordLoaded();

            _ = UpdateState();

            if (_cpiFileSelector?.InitCommand != null && ViewData != null)
            {
                ExecuteBackgroundTask(GetFileExtensions());
            }
        }

        private void Field_ValueChanged(object sender, ValueChangedEventArgs e)
        {
            _ = UpdateState();

            if (_cpiFileSelector?.OnFileSelectCommand != null)
            {
                ExecuteBackgroundCommand(_cpiFileSelector.OnFileSelectCommand);
            }
        }

        private async Task UpdateState()
        {
            PickedFile[] pickedFiles = null;

            if (Field.ValueType == FilePickerValueType.PickedFileArray)
            {
                if (Field.PickedFiles?.Length > 0)
                {
                    pickedFiles = Field.PickedFiles.ToArray();
                }
            }
            else if (Field.PickedFile != null)
            {
                pickedFiles = new[] { Field.PickedFile };
            }

            if (pickedFiles != null && pickedFiles.Length > 0)
            {
                ILocationService locationService = Resolver.Resolve<ILocationService>();
                GpsLocation location = await locationService.GetGpsLocationAsync(); //checks if location permission is enabled and returns location

                foreach (PickedFile fileItem in pickedFiles)
                {
                    if (fileItem.Source == FileSource.Camera && location != null)
                    {
                        // Adds current location
                        fileItem.Latitude = location.Latitude;
                        fileItem.Longitude = location.Longitude;
                    }
                }

                string[] fileNames = pickedFiles.Select(x => x.FileName).ToArray();
                State.FileList = string.Join(",", fileNames);
                State.PickedFiles = pickedFiles;
                State.Count = pickedFiles.Length;
                State.IsEmpty = false;
            }
            else
            {
                State.FileList = null;
                State.PickedFiles = null;
                State.Count = 0;
                State.IsEmpty = true;
            }
        }

        public async Task GetFileExtensions()
        {
            await _cpiFileSelector.InitCommand.TryRunAsync(ProjectionName, ViewData, _commandExecutor);
            Field.FileExtensions = State.AcceptedExtensions?.Split(',');
        }
    }
}
