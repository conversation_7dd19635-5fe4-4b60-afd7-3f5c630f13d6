﻿namespace Ifs.Uma.AppData.Execution.Procedures
{
    public class ProcedureErrorException : ProcedureException
    {
        public string ProcedureName { get; }
        public string Parameter { get; }

        public ProcedureErrorException(string procedureName, string message, string parameter)
            : base(message)
        {
            ProcedureName = procedureName;
            Parameter = parameter;
        }
    }
}
