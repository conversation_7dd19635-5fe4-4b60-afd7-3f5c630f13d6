﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;
using Unity;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteLogTests : ProcedureTest
    {
        [Test]
        public async Task Log()
        {
            Prepare(out TestLogger testLogger, out IProcedureExecutor executor);

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DoLog", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            CollectionAssert.Contains(testLogger.LoggedMessages, "Information: Log value 'TestValue1'");
        }

        [Test]
        public async Task Debug()
        {
            Prepare(out TestLogger testLogger, out IProcedureExecutor executor);
            
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DoDebug", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            CollectionAssert.Contains(testLogger.LoggedMessages, "Debug: Debug value 'TestValue2'");
        }

        [Test]
        public async Task Trace()
        {
            Prepare(out TestLogger testLogger, out IProcedureExecutor executor);
            
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DoTrace", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            CollectionAssert.Contains(testLogger.LoggedMessages, "Trace: Trace value 'TestValue3'");
        }
        
        private void Prepare(out TestLogger testLogger, out IProcedureExecutor executor)
        {
            ILogger logger = Resolve<ILogger>();
            testLogger = new TestLogger(logger);
            Container.RegisterInstance<ILogger>(testLogger);

            executor = Resolve<IProcedureExecutor>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteLogSchema", null);
        }

        private class TestLogger : ILogger
        {
            private readonly ILogger _parentLogger;

            public List<string> LoggedMessages { get; } = new List<string>();

            public TestLogger(ILogger parentLogger)
            {
                _parentLogger = parentLogger;
            }

            public void Log(string message, MessageType messageType)
            {
                LoggedMessages.Add(messageType + ": " + message);
                _parentLogger.Log(message, messageType);
            }

            public void Dispose()
            {
                _parentLogger.Dispose();
            }
        }
    }
}
