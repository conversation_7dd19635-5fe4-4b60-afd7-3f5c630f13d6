<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.recyclerview.widget.RecyclerView
        android:id="@id/List"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    <ProgressBar
        style="?android:attr/progressBarStyle"
        android:layout_centerInParent="true"
        android:layout_gravity="bottom|center"
        android:id="@+id/progressbar_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="10dp"
        android:indeterminateTint="?attr/colorPrimary"
        android:padding="8dp"
        android:layout_marginBottom="64dp"
        android:translationY="50dp"/>
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/FloatingActionButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:layout_gravity="bottom|right"
        app:srcCompat="@drawable/ic_add"
        android:visibility="invisible"
        android:tint="@android:color/white"
        app:layout_behavior="com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior"
        app:elevation="4dp"
        app:layout_anchor="@id/List"
        app:layout_anchorGravity="bottom|right|end" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>
