﻿using System;
using System.Dynamic;
using System.Globalization;
using System.Text;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public interface ISqlExpression : IWriteSql
    {
    }

    public interface IColumnSpec : IWriteSql
    {
        string ColumnName { get; }
    }

    public interface IUnionColumnSpec : IColumnSpec
    {
        bool ColumnAsNull { get; }
    }

    public interface IAliasedColumnSpec : IColumnSpec
    {
        string TableAlias { get; }
    }

    public interface ITypedColumnSpec : IColumnSpec
    {
        Type ColumnType { get; }
    }

    public interface INullableTypedColumnSpec : ITypedColumnSpec
    {
        bool Mandatory { get; }
    }

    public enum EUpdateColumn
    {
        UpdateValue, UpdateExpression
    }

    public interface IColumnUpdateSpec : IColumnSpec
    {
        EUpdateColumn UpdateType { get; }
    }

    public interface IColumnAndExpression : IColumnUpdateSpec
    {
        EColumnExpression ColumnExpression { get; }
        int Amount { get; }
    }

    public interface IColumnSpecAndValue : INullableTypedColumnSpec, IColumnUpdateSpec
    {
        object Value { get; }
        int ValueIndex { get; }
    }

    public interface ISortColumnSpec : IAliasedColumnSpec
    {
        ESortOrder SortOrder { get; }
    }

    public interface ISelectColumnSpec : IAliasedColumnSpec
    {
        EColumnFunction ColumnFunction { get; }
        string ColumnAlias { get; }
    }

    public class ColumnSpec : IColumnSpec
    {
        #region Column Factories

        public static IColumnSpec Create(string columnName)
        {
            return !string.IsNullOrEmpty(columnName) ? new ColumnSpec(columnName) : null;
        }

        public static IAliasedColumnSpec CreateAliased(string columnName)
        {
            return Create(columnName, string.Empty);
        }

        public static IUnionColumnSpec CreateUnionSelect(string columnName, bool columnAsNull)
        {
            return new SelectUnionColumnSpec(columnName, columnAsNull);
        }

        public static IAliasedColumnSpec Create(string columnName, string tableAlias)
        {
            return !string.IsNullOrEmpty(columnName) ? new AliasedColumnSpec(columnName, tableAlias) : null;
        }

        public static ITypedColumnSpec Create(string columnName, Type columnType)
        {
            return !string.IsNullOrEmpty(columnName) && columnType != null ?
                new TypedColumnSpec(columnName, columnType) : null;
        }

        public static INullableTypedColumnSpec Create(string columnName, Type columnType, bool mandatory)
        {
            return !string.IsNullOrEmpty(columnName) && columnType != null ?
                new NullableTypedColumnSpec(columnName, columnType, mandatory) : null;
        }

        public static ISelectColumnSpec CreateSelect(string columnName)
        {
            return Create(columnName, null, null, EColumnFunction.None);
        }

        public static ISelectColumnSpec CreateSelect(string columnName, string tableAlias)
        {
            return Create(columnName, tableAlias, null, EColumnFunction.None);
        }

        public static ISelectColumnSpec Create(string columnName, string tableAlias, string columnAlias)
        {
            return Create(columnName, tableAlias, columnAlias, EColumnFunction.None);
        }

        public static ISelectColumnSpec Create(string columnName, string tableAlias, string columnAlias, EColumnFunction columnFunction)
        {
            return !string.IsNullOrEmpty(columnName) ?
                new SelectColumnSpec(columnName, tableAlias, columnAlias, columnFunction) : null;
        }

        public static ISelectColumnSpec Create(ISqlExpression expression, string columnAlias)
        {
            return expression == null ? null : new SelectExpressionColumnSpec(expression, columnAlias);
        }

        public static ISelectColumnSpec CreateAggregate(EColumnFunction aggregateFunction, ISqlExpression aggregateExpression, string columnAlias)
        {
            return new AggregateColumnSpec(aggregateFunction, aggregateExpression, columnAlias);
        }

        public static IColumnAndExpression Create(string columnName, EColumnExpression columnExpression)
        {
            return Create(columnName, columnExpression, 1);
        }

        public static IColumnAndExpression Create(string columnName, EColumnExpression columnExpression, int amount)
        {
            return new ColumnAndExpression(columnName, columnExpression, amount);
        }

        public static IColumnSpecAndValue Create(INullableTypedColumnSpec spec, object value)
        {
            return Create(spec, value, -1);
        }

        public static IColumnSpecAndValue Create(INullableTypedColumnSpec spec, object value, int valueIndex)
        {
            return spec != null ? new ColumnSpecAndValue(spec.ColumnName, spec.ColumnType, spec.Mandatory, value, valueIndex) : null;
        }

        public static ISortColumnSpec Create(string columnName, ESortOrder sortOrder)
        {
            return Create(columnName, sortOrder, null);
        }

        public static ISortColumnSpec Create(string columnName, ESortOrder sortOrder, string tableAlias)
        {
            return !string.IsNullOrEmpty(columnName) ? new SortColumnSpec(columnName, sortOrder, tableAlias) : null;
        }

        #endregion

        #region IColumnSpec Members

        public string ColumnName { get { return m_columnName; } }

        #endregion

        #region IWriteSql Members

        public virtual void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
        {
            if (builder != null && sb != null)
            {
                sb.Append(builder.DecorateName(ColumnName));
            }
        }

        #endregion

        #region Implementation

        protected ColumnSpec(string columnName)
        {
            m_columnName = columnName;
        }

        private string m_columnName;

        protected class AliasedColumnSpec : ColumnSpec, IAliasedColumnSpec
        {
            #region IAliasedColumnSpec Members

            public string TableAlias { get; private set; }

            #endregion

            public AliasedColumnSpec(string columnName, string tableAlias)
                : base(columnName)
            {
                TableAlias = tableAlias;
            }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (builder != null)
                {
                    builder.WriteColumn(sb, TableAlias, ColumnName);
                }
            }
        }

        protected class SelectColumnSpec : AliasedColumnSpec, ISelectColumnSpec
        {
            public string ColumnAlias { get; private set; }
            public EColumnFunction ColumnFunction { get; private set; }

            public SelectColumnSpec(string columnName, string tableAlias, string columnAlias, EColumnFunction columnFunction)
                : base(columnName, tableAlias)
            {
                ColumnAlias = columnAlias;
                ColumnFunction = columnFunction;
            }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (mode != SqlWriteMode.SelectColumn)
                {
                    base.WriteSql(sb, info, builder, mode);
                }
                else if (builder != null && sb != null)
                {
                    builder.WriteFunction(sb, ColumnFunction, this);
                    if (!string.IsNullOrEmpty(ColumnAlias))
                    {
                        sb.Append(builder.AsClause);
                        sb.Append(builder.DecorateName(ColumnAlias));
                    }
                }
            }
        }

        protected class SelectUnionColumnSpec : IUnionColumnSpec
        {
            public bool ColumnAsNull { get; private set; }

            public string ColumnName { get; private set; }

            public SelectUnionColumnSpec(string columnName, bool columnAsNull)
            {
                ColumnAsNull = columnAsNull;
                ColumnName = columnName;
            }

            public void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (builder != null && sb != null)
                {
                    if (ColumnAsNull)
                    {
                        sb.Append(builder.NullLiteral);
                        sb.Append(' ');
                        sb.Append(builder.AsClause);
                    }
                    sb.Append(builder.DecorateName(ColumnName));
                }
            }
        }

        protected class AggregateColumnSpec : AliasedColumnSpec, ISelectColumnSpec
        {
            public string ColumnAlias { get; }
            public EColumnFunction ColumnFunction { get; }
            public ISqlExpression AggregateExpression { get; }

            public AggregateColumnSpec(EColumnFunction aggregateFunction, ISqlExpression aggregateExpression, string columnAlias)
                : base(null, null)
            {
                ColumnFunction = aggregateFunction;
                AggregateExpression = aggregateExpression;
                ColumnAlias = columnAlias;
            }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (mode != SqlWriteMode.SelectColumn)
                {
                    base.WriteSql(sb, info, builder, mode);
                }
                else if (builder != null && sb != null)
                {
                    builder.WriteAggregateFunction(sb, info, ColumnFunction, AggregateExpression);
                    
                    if (!string.IsNullOrEmpty(ColumnAlias))
                    {
                        sb.Append(builder.AsClause);
                        sb.Append(builder.DecorateName(ColumnAlias));
                    }
                }
            }
        }

        protected class TypedColumnSpec : ColumnSpec, ITypedColumnSpec
        {
            #region ITypedColumnSpec Members

            public Type ColumnType { get; private set; }

            #endregion

            public TypedColumnSpec(string columnName, Type columnType)
                : base(columnName)
            {
                ColumnType = columnType;
            }
        }

        protected class NullableTypedColumnSpec : TypedColumnSpec, INullableTypedColumnSpec
        {
            #region INullableTypedColumnSpec Members

            public bool Mandatory { get; private set; }

            #endregion

            public NullableTypedColumnSpec(string columnName, Type columnType, bool mandatory)
                : base(columnName, columnType)
            {
                Mandatory = mandatory;
            }
        }

        protected class SortColumnSpec : AliasedColumnSpec, ISortColumnSpec
        {
            #region ISortColumnSpec Members

            public ESortOrder SortOrder { get; private set; }

            #endregion

            public SortColumnSpec(string columnName, ESortOrder sortOrder, string tableAlias)
                : base(columnName, tableAlias)
            {
                SortOrder = sortOrder;
            }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                base.WriteSql(sb, info, builder, mode);
                if (builder != null && sb != null && mode == SqlWriteMode.SortColumn)
                {
                    sb.Append(SortOrder == ESortOrder.Ascending ? builder.AscendingOrder : builder.DescendingOrder);
                }
            }
        }

        protected class ColumnAndExpression : ColumnSpec, IColumnAndExpression
        {
            #region IColumnAndExpression Members

            public EColumnExpression ColumnExpression { get; private set; }
            public int Amount { get; private set; }

            #endregion

            #region IColumnUpdateSpec Members

            public EUpdateColumn UpdateType { get { return EUpdateColumn.UpdateExpression; } }

            #endregion

            public ColumnAndExpression(string columnName, EColumnExpression columnExpression, int amount)
                : base(columnName)
            {
                ColumnExpression = columnExpression;
                Amount = amount;
            }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (mode != SqlWriteMode.ColumnValue && mode != SqlWriteMode.ColumnAssign)
                {
                    base.WriteSql(sb, info, builder, mode);
                }
                else if (sb != null && builder != null)
                {
                    string decoratedName = builder.DecorateName(ColumnName);
                    string expressionText = string.Empty;
                    switch (ColumnExpression)
                    {
                        case EColumnExpression.IncrementColumn:
                            expressionText = Amount != 0 ? decoratedName + builder.AddOperator +
                                ObjectConverter.ToString(Amount) : decoratedName;
                            break;
                    }
                    if (mode == SqlWriteMode.ColumnAssign)
                    {
                        sb.Append(decoratedName);
                        sb.Append(builder.AssignOperator);
                    }
                    sb.Append(expressionText);
                }   
            }
        }

        protected class SelectExpressionColumnSpec : ISelectColumnSpec
        {
            public ISqlExpression Expression { get; }
            public string ColumnAlias { get; }
            public EColumnFunction ColumnFunction => EColumnFunction.None;
            public string TableAlias => null;
            public string ColumnName => null;

            public SelectExpressionColumnSpec(ISqlExpression expression, string columnAlias)
            {
                Expression = expression;
                ColumnAlias = columnAlias;
            }

            public void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                Expression.WriteSql(sb, info, builder, mode);

                if (!string.IsNullOrEmpty(ColumnAlias))
                {
                    sb.Append(builder.AsClause);
                    sb.Append(builder.DecorateName(ColumnAlias));
                }
            }
        }

        protected class ColumnSpecAndValue : NullableTypedColumnSpec, IColumnSpecAndValue
        {
            #region IColumnSpecAndValue Members

            public object Value { get; private set; }
            public int ValueIndex { get; private set; }

            #endregion

            #region IColumnUpdateSpec Members

            public EUpdateColumn UpdateType { get { return EUpdateColumn.UpdateValue; } }

            #endregion

            public ColumnSpecAndValue(string columnName, Type columnType, bool mandatory, object value, int valueIndex)
                : base(columnName, columnType, mandatory)
            {
                Value = value;
                ValueIndex = valueIndex;
            }

            public override string ToString()
            {
                return string.Format(CultureInfo.InvariantCulture, "ColumnName = {0}, Value = {1}", ColumnName, Value);
            }

            public override void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
            {
                if (mode != SqlWriteMode.ColumnValue && mode != SqlWriteMode.ColumnAssign)
                {
                    base.WriteSql(sb, info, builder, mode);
                }
                else if (sb != null && builder != null && info != null)
                {
                    if (mode == SqlWriteMode.ColumnAssign)
                    {
                        sb.Append(builder.DecorateName(ColumnName));
                        sb.Append(builder.AssignOperator);
                    }
                    info.WriteParameter(sb, ColumnType, Value, ValueIndex);
                }
            }
        }

        #endregion
    }
}
