﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemCreateGuid : SystemFunction
    {
        public const string FunctionName = "CreateGuid";

        public SystemCreateGuid()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            Guid nextGuid = Guid.NewGuid();
            return nextGuid.ToString("N").ToUpper();
        }
    }
}
