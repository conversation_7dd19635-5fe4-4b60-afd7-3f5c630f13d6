﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace Ifs.Uma.AppData.Expressions
{
   public sealed class ApiContainsExpression : IfsApiExpression
    {
        public override IfsApiMethodName ApiMethodName => IfsApiMethodName.Contains;

        public override IfsApiMethodHandleType ApiMethodHandleType => IfsApiMethodHandleType.String;

        protected override MethodInfo LogicMethodInfo => typeof(ApiContainsExpression).GetTypeInfo().GetDeclaredMethod(nameof(Contains));

        public ApiContainsExpression(List<Expression> expressions)
        {
            Parameters = expressions;
        }

        internal static bool Contains(List<DynamicValue> parameters)
        {
            if (parameters == null || parameters.Count != 2)
            {
                throw new ArgumentNullException("Parameters cannot be null and must contain atleast two elements.");
            }

            string value = parameters[0].GetCleanString();
            IEnumerable<string> list = parameters[1].Value as IEnumerable<string>;

            if (list == null)
            {
                throw new ArgumentException("The second parameter must be a list of strings.");
            }

            return list.Contains(value);
        }
    }
}
