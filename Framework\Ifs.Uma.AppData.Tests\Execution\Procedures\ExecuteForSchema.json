{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}, "CustomerAddresses": {"name": "CustomerAddresses", "entity": "TstCustomerAddress", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}, "CustomerType": {"datatype": "Text", "keygeneration": "User"}, "CustomerTypeDesc": {"datatype": "Text", "keygeneration": "User"}}, "references": {"CustomerTypeRef": {"target": "TstCustomerType", "mapping": {"CustomerType": "TypeId"}}}, "arrays": {"CustomerAddressArray": {"target": "TstCustomerAddress", "mapping": {"CustomerNo": "AddressCustomerNo"}}}}, "TstCustomerType": {"name": "TstCustomerType", "hasETag": true, "CRUD": "Read", "luname": "TstCustomerType", "ludependencies": ["TstCustomerType"], "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text", "keygeneration": "User"}, "TypeDescription": {"datatype": "Text", "keygeneration": "User"}}}, "TstCustomerAddress": {"name": "TstCustomerAddress", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomerAddress", "ludependencies": ["TstCustomerAddress"], "keys": ["AddressCustomerNo", "AddressId"], "attributes": {"AddressCustomerNo": {"datatype": "Text", "keygeneration": "User"}, "AddressId": {"datatype": "Text", "keygeneration": "User"}, "AddressLine": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<ForCustomers>": {"name": "ForCustomers", "type": "Function", "layers": [{"vars": [{"name": "Var1"}, {"name": "Record"}], "execute": [{"call": {"method": "for", "args": {"entity": "Customers"}}, "assign": "Record", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"+": [{"var": "Var1"}, "#"]}, {"var": "Record.CustomerNo"}]}}}, "assign": "Var1"}]}}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<ForCustomerAddress>": {"name": "ForCustomerAddress", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "Var1"}, {"name": "Record"}], "execute": [{"call": {"method": "for", "args": {"name": "Customer.CustomerAddressArray"}}, "assign": "Record", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"+": [{"var": "Var1"}, "#"]}, {"var": "Record.AddressLine"}]}}}, "assign": "Var1"}]}}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<ForFromArrayWithWhere>": {"name": "ForFromArrayWithWhere", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "Var1"}, {"name": "Record"}], "execute": [{"call": {"method": "for", "args": {"name": "Customer.CustomerAddressArray", "where": {"==": [{"var": "AddressId"}, "WORK"]}}}, "assign": "Record", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"+": [{"var": "Var1"}, "#"]}, {"var": "Record.AddressLine"}]}}}, "assign": "Var1"}]}}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<ForWhereAliased>": {"name": "ForWhereAliased", "type": "Function", "layers": [{"vars": [{"name": "CustomerNo"}, {"name": "Record"}, {"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"value": "501"}}, "assign": "CustomerNo"}, {"call": {"method": "for", "args": {"entity": "Customers", "alias": "i", "where": {"==": [{"var": "i.Customer<PERSON>o"}, {"var": "CustomerNo"}]}}}, "assign": "Record", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"+": [{"var": "Var1"}, "#"]}, {"var": "Record.CustomerNo"}]}}}, "assign": "Var1"}]}}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<ForWithReturn>": {"name": "ForWithReturn", "type": "Function", "layers": [{"vars": [{"name": "Var1"}, {"name": "Record"}], "execute": [{"call": {"method": "for", "args": {"entity": "Customers"}}, "assign": "Record", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"var": "Var1"}, "#A"]}}}, "assign": "Var1"}, {"call": {"method": "return", "args": {"name": "Var1"}}}, {"call": {"method": "set", "args": {"value": "B"}}, "assign": "Var1"}]}}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<ForWithError>": {"name": "ForWithError", "type": "Function", "layers": [{"vars": [{"name": "Var1"}, {"name": "Record"}], "execute": [{"call": {"method": "for", "args": {"entity": "Customers"}}, "assign": "Record", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"var": "Var1"}, "#A"]}}}, "assign": "Var1"}, {"call": {"method": "error", "args": {"msg": "Errored '${Var1}'"}}}, {"call": {"method": "set", "args": {"value": "B"}}, "assign": "Var1"}]}}, {"call": {"method": "error", "args": {"msg": "Errored2 '${Var1}'"}}}]}]}, "Function<ForOrderBy>": {"name": "ForOrderBy", "type": "Function", "layers": [{"vars": [{"name": "Var1"}, {"name": "Record"}], "execute": [{"call": {"method": "for", "args": {"entity": "CustomerAddresses", "alias": "i", "orderby": [{"AddressCustomerNo": "asc"}, {"i.AddressLine": "desc"}]}}, "assign": "Record", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"+": [{"var": "Var1"}, "#"]}, {"var": "Record.AddressLine"}]}}}, "assign": "Var1"}]}}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<FnBreakForLoop>": {"name": "FnBreakForLoop", "type": "Function", "params": [], "layers": [{"vars": [{"name": "i", "dataType": "Integer", "collection": false}, {"name": "VarA", "dataType": "Structure", "subType": "TstCustomerAddress", "collection": false}], "execute": [{"call": {"method": "set", "args": {"value": 0}}, "assign": "i"}, {"call": {"method": "for", "args": {"entity": "TstCustomerAddress", "name": "CustomerAddresses"}}, "assign": "VarA", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"var": "i"}, 1]}}}, "assign": "i"}, {"call": {"method": "if", "args": {"expression": {"==": [{"var": "i"}, 3]}}}, "result": {"TRUE": [{"call": {"method": "break"}}]}}]}}, {"call": {"method": "return", "args": {"name": "i"}}}]}]}}}}