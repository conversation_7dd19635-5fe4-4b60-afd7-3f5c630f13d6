<?xml version="1.0" encoding="utf-8"?>
<TableRow xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="5dp">
  <TextView
    android:id="@+id/ColumnName"
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:paddingLeft="5dp"
    android:paddingRight="5dp"
    android:ellipsize="end"
    android:textColor="@android:color/black"/>
  <TextView
    android:id="@+id/ColumnValue"
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:paddingLeft="5dp"
    android:paddingRight="5dp"
    android:maxLines="2"
    android:ellipsize="end"
    android:textColor="@android:color/black"/>
</TableRow>