﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class InExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.In;
        public override Type Type => typeof(bool);

        public Expression Expression { get; }
        public ReadOnlyCollection<Expression> InExpressions { get; }

        internal InExpression(Expression expression, ReadOnlyCollection<Expression> inExpressions)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));
            if (inExpressions == null) throw new ArgumentNullException(nameof(inExpressions));
            if (inExpressions.Count == 0) throw new ArgumentException(nameof(inExpressions));
        
            Expression = expression;
            InExpressions = inExpressions;
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitInExpression(this);
        }

        public override string ToString()
        {
            return Expression + " in (" + string.Join(", ", InExpressions) + ")";
        }

        public InExpression Update(Expression expression, IEnumerable<Expression> inExpressions)
        {
            if (expression == Expression && ReferenceEquals(InExpressions, inExpressions))
            {
                return this;
            }

            return new InExpression(expression, ToReadOnly(inExpressions));
        }
    }

    public partial class IfsExpression
    {
        public static InExpression In(Expression expression, IEnumerable<Expression> inExpressions)
        {
            return new InExpression(expression, ToReadOnly(inExpressions));
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitInExpression(InExpression exp)
        {
            Expression expression = Visit(exp.Expression);
            ReadOnlyCollection<Expression> inExpressions = base.Visit(exp.InExpressions);
            return exp.Update(expression, inExpressions);
        }
    }
}
