﻿using System;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class LikeExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.Like;
        public override Type Type => typeof(bool);
        public Expression Expression { get; }
        public Expression ValueExpression { get; }

        internal LikeExpression(Expression expression, Expression valueExpression)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));
            if (valueExpression == null) throw new ArgumentNullException(nameof(valueExpression));

            Expression = expression;
            ValueExpression = valueExpression;
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitLikeExpression(this);
        }

        public override string ToString()
        {
            return Expression + " LIKE " + ValueExpression;
        }

        public LikeExpression Update(Expression expression, Expression valueExpression)
        {
            if (expression == Expression && valueExpression == ValueExpression)
            {
                return this;
            }

            return new LikeExpression(expression, valueExpression);
        }
    }

    public partial class IfsExpression
    {
        public static LikeExpression Like(Expression expression, Expression valueExpression)
        {
            return new LikeExpression(expression, valueExpression);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitLikeExpression(LikeExpression exp)
        {
            Expression expression = Visit(exp.Expression);
            Expression valueExpression = Visit(exp.ValueExpression);
            return exp.Update(expression, valueExpression);
        }
    }
}
