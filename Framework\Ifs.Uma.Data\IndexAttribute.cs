﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Data
{
    /// <summary>
    /// Represents a database table index
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true, Inherited = false)]
    public sealed class IndexAttribute : Attribute
    {
        public IndexAttribute() { }
        /// <summary>
        /// The database name of the index
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// The comma separated field names (not the database column names) of the columns to be indexed
        /// </summary>
        public string Columns { get; set; }
        /// <summary>
        /// Indicates whether the index is unique or not
        /// </summary>
        public bool Unique { get; set; }
    }
}
