﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringReplace : StringFunction
    {
        public const string FunctionName = "Replace";

        public StringReplace()
            : base(FunctionName, 3, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            object searchObj = parameters[1].GetValue();
            if (searchObj == null)
            {
                return stringToModify;
            }

            string searchString = parameters[1].GetString();
            string replacementString = parameters[2].GetString() ?? string.Empty;
            return stringToModify.Replace(searchString, replacementString);
        }
    }
}
