﻿using Ifs.Uma.AppData.Cache;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Ifs.Uma.AppData
{
    public interface IOfflineChecker
    {
        Task<bool> WillFailAsOffline(EntityDataSource dataSource, IEnumerable<CpiCommandGroup> commandGroups, IEnumerable<CpiElementContent> elementsContent);
        Task<bool> WillFailAsOffline(string projectionName, CpiCommand command);
        Task<bool> WillFailAsOffline(string projectionName, CpiTree tree, CpiTreeNode rootNode);
        Task<bool> WillFailAsOffline(string projectionName, IEnumerable<string> entityNames);
    }

    public sealed class OfflineChecker : IOfflineChecker
    {
        private readonly IMetadata _metadata;
        private readonly IOnlineDataHandler _onlineDataHandler;
        private readonly ICachePreparer _cachePreparer;

        public OfflineChecker(IMetadata metadata, IOnlineDataHandler onlineDataHandler, ICachePreparer cachePreparer)
        {
            _metadata = metadata;
            _onlineDataHandler = onlineDataHandler;
            _cachePreparer = cachePreparer;
        }

        public async Task<bool> WillFailAsOffline(EntityDataSource dataSource, IEnumerable<CpiCommandGroup> commandGroups, IEnumerable<CpiElementContent> elementsContent)
        {
            if (_onlineDataHandler.IsOnline)
            {
                return false;
            }

            HashSet<string> entities = new HashSet<string>();
            bool usedOnlineCall;

            GatherEntitiesFromDataSource(dataSource, entities, out usedOnlineCall);

            if (usedOnlineCall)
            {
                return true;
            }

            GatherEntitiesFromCommands(dataSource.ProjectionName, commandGroups, entities, out usedOnlineCall);

            if (usedOnlineCall)
            {
                return true;
            }

            GatherEntitiesFromElements(dataSource, elementsContent, entities, out usedOnlineCall);

            if (usedOnlineCall)
            {
                return true;
            }

            return await WillFailAsOffline(dataSource.ProjectionName, entities);
        }

        private void GatherEntitiesFromDataSource(EntityDataSource dataSource, HashSet<string> into, out bool usedOnlineCall)
        {
            usedOnlineCall = false;

            if (dataSource != null)
            {
                into.Add(dataSource.EntityName);

                if (dataSource is FunctionDataSource functionDataSource)
                {
                    string[] usedEntities = EntityFinder.FindInFunction(_metadata, dataSource.ProjectionName, functionDataSource.FunctionName, out usedOnlineCall);

                    foreach (string entity in usedEntities)
                    {
                        into.Add(entity);
                    }
                }
            }
        }

        private void GatherEntitiesFromElements(EntityDataSource dataSource, IEnumerable<CpiElementContent> elementsContent, HashSet<string> into, out bool usedOnlineCall)
        {
            usedOnlineCall = false;

            if (elementsContent != null)
            {
                foreach (var elementContent in elementsContent)
                {
                    GatherEntitiesFromElement(dataSource, elementContent, into, out bool thisUsedOnlineCall);

                    if (thisUsedOnlineCall)
                    {
                        usedOnlineCall = true;
                    }
                }
            }
        }

        private void GatherEntitiesFromElement(EntityDataSource dataSource, CpiElementContent elementContent, HashSet<string> into, out bool usedOnlineCall)
        {
            usedOnlineCall = false;

            if (elementContent.Arrange != null)
            {
                GatherEntitiesFromElements(dataSource, elementContent.Arrange, into, out usedOnlineCall);
            }

            string entityName = null;
            if (!string.IsNullOrEmpty(elementContent.DatasourceFunction))
            {
                string[] usedEntities = EntityFinder.FindInFunction(_metadata, dataSource.ProjectionName, elementContent.DatasourceFunction, out usedOnlineCall);

                foreach (string entity in usedEntities)
                {
                    into.Add(entity);
                }

                CpiFunction function = _metadata.FindFunction(dataSource.ProjectionName, elementContent.DatasourceFunction);
                if (function?.ReturnType != null && (function.ReturnType.DataType == CpiDataType.Structure || function.ReturnType.DataType == CpiDataType.Entity))
                {
                    entityName = function.ReturnType.SubType;
                }
            }
            else if (!string.IsNullOrEmpty(elementContent.DatasourceEntitySet))
            {
                CpiContains contains = _metadata.FindContains(dataSource.ProjectionName, elementContent.DatasourceEntitySet);
                entityName = contains?.Entity;
            }
            else
            {
                entityName = dataSource?.EntityName;
            }

            if (entityName != null)
            {
                if (elementContent.Binding?.Property != null)
                {
                    CpiArray array = _metadata.FindArray(dataSource.ProjectionName, entityName, elementContent.Binding.Property);
                    if (array != null)
                    {
                        into.Add(array.Target);
                    }
                }
                else
                {
                    into.Add(entityName);
                }
            }
        }

        private void GatherEntitiesFromCommands(string projectionName, IEnumerable<CpiCommandGroup> commandGroups, HashSet<string> into, out bool usedOnlineCall)
        {
            usedOnlineCall = false;

            if (commandGroups == null) return;

            foreach (var commandGroup in commandGroups)
            {
                if (commandGroup.CommandNames != null)
                {
                    foreach (string commandName in commandGroup.CommandNames)
                    {
                        CpiCommand command = _metadata.FindCommand(projectionName, commandName);
                        if (command == null)
                            continue;

                        string[] usedEntities = EntityFinder.FindInCommand(_metadata, projectionName, command, out bool thisUsedOnlineCall);

                        if (thisUsedOnlineCall)
                        {
                            usedOnlineCall = true;
                        }

                        foreach (string entity in usedEntities)
                        {
                            into.Add(entity);
                        }
                    }
                }
            }
        }

        public async Task<bool> WillFailAsOffline(string projectionName, CpiCommand command)
        {
            if (_onlineDataHandler.IsOnline)
            {
                return false;
            }

            HashSet<string> entities = new HashSet<string>();

            string[] usedEntities = EntityFinder.FindInCommand(_metadata, projectionName, command, out bool usedOnlineCall);

            if (usedOnlineCall)
            {
                return true;
            }

            foreach (string entity in usedEntities)
            {
                entities.Add(entity);
            }

            return await WillFailAsOffline(projectionName, entities);
        }

        public async Task<bool> WillFailAsOffline(string projectionName, IEnumerable<string> entityNames)
        {
            if (_onlineDataHandler.IsOnline)
            {
                return false;
            }

            foreach (string entityName in entityNames)
            {
                EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(entityName);

                if (syncPolicy == EntitySyncPolicy.OnlineOnly)
                {
                    return true;
                }
                else if (!await _cachePreparer.IsCacheReadyAsync(projectionName, entityName))
                {
                    return true;
                }
            }

            return false;
        }

        public async Task<bool> WillFailAsOffline(string projectionName, CpiTree tree, CpiTreeNode rootNode)
        {
            if (tree == null) throw new ArgumentNullException(nameof(tree));
            
            if (_onlineDataHandler.IsOnline || tree.Nodes == null)
            {
                return false;
            }

            if (rootNode == null)
            {
                rootNode = tree.Nodes.Select(x => x.Node).FirstOrDefault(x => x?.Root ?? false);
            }

            if (rootNode == null)
            {
                return false;
            }

            HashSet<string> entities = new HashSet<string>();
            HashSet<string> checkedNodes = new HashSet<string>();

            if (rootNode.Entity != null)
            {
                entities.Add(rootNode.Entity);
                GatherEntities(projectionName, tree, rootNode.Entity, rootNode, entities, checkedNodes);
            }
            
            return await WillFailAsOffline(projectionName, entities);
        }

        private void GatherEntities(string projectionName, CpiTree tree, string baseEntity, CpiTreeNode node, HashSet<string> entities, HashSet<string> checkedNodes)
        {
            if (checkedNodes.Contains(node.Name))
            {
                return;
            }

            checkedNodes.Add(node.Name);

            if (node.Entity != null)
            {
                entities.Add(node.Entity);
            }

            if (node.Connections != null)
            {
                foreach (CpiTreeConnection item in node.Connections.Select(x => x.Connection).Where(x => x != null))
                {
                    string toEntity = baseEntity ?? node.Entity;
                    if (item?.Binding?.BindName != null)
                    {
                        CpiArray array = _metadata.FindArray(projectionName, toEntity, item.Binding.BindName);
                        if (array != null)
                        {
                            entities.Add(array.Target);
                            toEntity = array.Target;
                        }
                    }

                    if (item?.Binding?.Property != null)
                    {
                        CpiTreeNode connectedNode = tree.Nodes.Select(x => x.Node).FirstOrDefault(x => x?.Name == item.Binding.Property);
                        if (connectedNode != null)
                        {
                            GatherEntities(projectionName, tree, toEntity, connectedNode, entities, checkedNodes);
                        }
                    }
                }
            }
        }
    }
}
