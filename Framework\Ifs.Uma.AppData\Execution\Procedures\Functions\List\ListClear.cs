﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.List
{
    internal sealed class ListClear : ListFunction
    {
        public const string FunctionName = "Clear";

        public ListClear()
            : base(FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            MarbleList list = PrepareList(context, parameters[0]);
            list.Clear();
            return null;
        }
    }
}
