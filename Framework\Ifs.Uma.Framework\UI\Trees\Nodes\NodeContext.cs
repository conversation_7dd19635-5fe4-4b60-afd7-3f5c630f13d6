﻿using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Trees.Nodes
{
    public class NodeContext
    {
        public NodeContext(string projectionName, PageData pageData, CpiTree tree, IMetadata metadata, IDataHandler dataHandler, IExpressionRunner expressionRunner, ILogger logger, IEventAggregator eventAggregator, IDialogService dialogService)
        {
            ProjectionName = projectionName;
            PageData = pageData;
            Tree = tree;
            Metadata = metadata;
            DataHandler = dataHandler;
            ExpressionRunner = expressionRunner;
            Logger = logger;
            EventAggregator = eventAggregator;
            DialogService = dialogService;
        }

        public string ProjectionName { get; }
        public PageData PageData { get; }
        public CpiTree Tree { get; }
        public IMetadata Metadata { get; }
        public IDataHandler DataHandler { get; }
        public IExpressionRunner ExpressionRunner { get; }
        public ILogger Logger { get; }
        public IEventAggregator EventAggregator { get; }
        public IDialogService DialogService { get; }
    }
}
