﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection;
using NodaTime;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class ApiDateDiffExpression : IfsApiExpression
    {
        public override IfsApiMethodName ApiMethodName => IfsApiMethodName.Datediff;

        public override IfsApiMethodHandleType ApiMethodHandleType => IfsApiMethodHandleType.Date;

        protected override MethodInfo LogicMethodInfo => typeof(ApiDateDiffExpression).GetTypeInfo().GetDeclaredMethod(nameof(DateDifference));

        public ApiDateDiffExpression(List<Expression> expressions)
        {
            Parameters = expressions;
        }

        private static int DateDifference(List<DynamicValue> parameters)
        {
            if (parameters.Count != 3)
            {
                throw new ArgumentException(nameof(parameters));
            }

            string unit = parameters[0].GetCleanString();
            DateTime? dateValueOne = parameters[1].ToDateTime();
            DateTime? dateValueTwo = parameters[2].ToDateTime();

            if (!dateValueOne.HasValue || !dateValueTwo.HasValue)
            {
                throw new ArgumentNullException("Start date and end date must be valid DateTime values.");
            }

            int diff;
            TimeSpan timeSpan = dateValueTwo.Value - dateValueOne.Value;

            switch (unit)
            {
                case "year":
                    diff = Period.Between(LocalDate.FromDateTime(dateValueOne.Value), LocalDate.FromDateTime(dateValueTwo.Value), PeriodUnits.Years).Years;
                    break;
                case "month":
                    diff = Period.Between(LocalDate.FromDateTime(dateValueOne.Value), LocalDate.FromDateTime(dateValueTwo.Value), PeriodUnits.Months).Months;
                    break;
                case "day":
                    diff = (int)timeSpan.TotalDays;
                    break;
                case "hour":
                    diff = (int)timeSpan.TotalHours;
                    break;
                case "minute":
                    diff = (int)timeSpan.TotalMinutes;
                    break;
                case "second":
                    diff = (int)timeSpan.TotalSeconds;
                    break;
                default:
                    throw new NotSupportedException("This unit is not supported: " + unit);
            }

            return Math.Abs(diff);
        }
    }
}
