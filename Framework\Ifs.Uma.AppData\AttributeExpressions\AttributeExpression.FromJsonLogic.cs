﻿using System;
using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.AppData.Expressions;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.AppData.AttributeExpressions
{
    public partial class AttributeExpression
    {
        public static AttributeExpression FromJsonLogic(JToken json, IExpressionValueProvider valueProvider)
        {
            if (json == null || valueProvider == null)
            {
                return null;
            }
            AttributeExpression exp = new JsonLogic(valueProvider).ConvertExpression(json);
            return exp;
        }

        private class JsonLogic
        {
            private IExpressionValueProvider _valueProvider;
            public JsonLogic(IExpressionValueProvider valueProvider)
            {
                _valueProvider = valueProvider;
            }

            private static readonly Dictionary<string, AttributeCompareOperator> _compareOperations = new Dictionary<string, AttributeCompareOperator>
            {
                ["=="] = AttributeCompareOperator.Equals,
                ["!="] = AttributeCompareOperator.NotEquals,
                [">"] = AttributeCompareOperator.GreaterThan,
                [">="] = AttributeCompareOperator.GreaterThanOrEqual,
                ["<"] = AttributeCompareOperator.LessThan,
                ["<="] = AttributeCompareOperator.LessThanOrEqual
            };

            private static readonly Dictionary<string, AttributeLogicalOperator> _logicalOperations = new Dictionary<string, AttributeLogicalOperator>
            {
                ["or"] = AttributeLogicalOperator.Or,
                ["and"] = AttributeLogicalOperator.And
            };

            public AttributeExpression ConvertExpression(JToken jToken)
            {
                if (jToken.Type != JTokenType.Object)
                {
                    return null;
                }
                else
                {
                    JObject json = (JObject)jToken;
                    return ConvertJsonOperation(json);
                }
            }

            private AttributeExpression ConvertJsonOperation(JObject json)
            {
                if (json != null)
                {
                    JProperty property = json.Properties().FirstOrDefault();

                    if (property != null)
                    {
                        JToken token = json[property.Name];

                        if (_compareOperations.TryGetValue(property.Name, out AttributeCompareOperator compareOp))
                        {
                            return ConvertCompare(compareOp, token);
                        }
                        if (_logicalOperations.TryGetValue(property.Name, out AttributeLogicalOperator logicalOp))
                        {
                            return ConvertLogic(logicalOp, token);
                        }
                    }
                }
                
                return null;
            }

            private AttributeExpression ConvertCompare(AttributeCompareOperator op, JToken json)
            {
                JArray array = (JArray)json;
                string attributeName = GetAttributeName((JObject)array[0]);
                object value = null;
                if (array[1].Type == JTokenType.Object)
                {
                    value = GetVarValue((JObject)array[1]);
                }
                else
                {
                    value = array[1].ToObject<object>();
                }
                return AttributeExpression.Compare(attributeName, op, value);
            }

            private string GetAttributeName(JObject json)
            {
                JProperty property = json.Properties().FirstOrDefault();
                if (property?.Name == "attributeName") 
                {
                    return property.Value.ToString();
                }
                else
                {
                    throw new NotSupportedException($"JsonLogic operator '{property?.Name}' is not supported.");
                }
            }

            private object GetVarValue(JObject json)
            {
                JProperty property = json.Properties().FirstOrDefault();
                if (property?.Name == "var")
                {
                    JToken token = json[property.Name];
                    _valueProvider.TryGetValue(token.ToString(), out object attributeValue);
                    return attributeValue;
                }
                else
                {
                    throw new NotSupportedException($"JsonLogic operator '{property?.Name}' is not supported.");
                }
            }

            private AttributeExpression ConvertLogic(AttributeLogicalOperator op, JToken json)
            {
                JArray array = (JArray)json;
                AttributeExpression left = ConvertJsonOperation((JObject)array[0]);
                AttributeExpression right = ConvertJsonOperation((JObject)array[1]);
                if (op == AttributeLogicalOperator.And)
                {
                    return AttributeExpression.And(left, right);
                }
                else
                {
                    return AttributeExpression.Or(left, right);
                }
            }
        }
    }
}
