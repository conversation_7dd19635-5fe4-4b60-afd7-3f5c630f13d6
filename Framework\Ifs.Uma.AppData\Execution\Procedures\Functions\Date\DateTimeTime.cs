﻿using System;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Date
{ 
    internal sealed class DateTimeTime1 : DateTimeFunction
    {
        public const string FunctionName = "Time";

        public DateTimeTime1()
            : base(FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime? value = parameters[0].GetTimestamp();
            if (value.HasValue)
            {
                return ObjectConverter.ToTime(value.Value);
            }
            else
            {
                return null;
            }
        }
    }

    internal sealed class DateTimeTime3 : DateTimeFunction
    {
        public DateTimeTime3()
            : base(DateTimeTime1.FunctionName, 3)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            long? hours = parameters[0].GetInteger();
            long? mins = parameters[1].GetInteger();
            long? secs = parameters[2].GetInteger();

            if (hours.HasValue && mins.HasValue && secs.HasValue)
            {
                return new DateTime(1, 1, 1, (int)hours.Value, (int)mins.Value, (int)secs.Value);
            }
            else
            {
                return null;
            }
        }
    }
}
