﻿using Ifs.Cloud.Client.Types;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Cloud.Client.Utils;
using Ifs.Uma.Utility;
using System;
using System.Text;

namespace Ifs.Cloud.Client.Comm
{
    public class CustomSerializedResourceProxy<T> where T : BaseResource, ICustomResourceSerializer
    {
        private readonly ContextProvider _ctx;

        /// <summary>
        /// Gets he cloud resource request record
        /// </summary>
        public RequestContent<T> RequestRecord { get; }

        /// <summary>
        /// Gets the cloud resource wrapped in RequestRecord
        /// </summary>
        public T Resource => RequestRecord.Resource;

        /// <summary>
        /// Constructs an instance 
        /// </summary>
        /// <param name="ctx">The context</param>
        /// <param name="resource">The cloud resource this proxy should handle</param>
        public CustomSerializedResourceProxy(ContextProvider ctx, T resource)
        {
            _ctx = ctx;
            RequestRecord = new RequestContent<T>(resource);
        }

        /// <summary>
        /// Executes POST on cloud resource assigned to this proxy
        /// </summary>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public Task<object> ExecutePostAsync(int page, long pageSize, string eTag)
        {
            return ExecutePostAsync(page, pageSize, CancellationToken.None, eTag);
        }

        public Task<object> ExecutePostAsync(int page, long messageId, CancellationToken cancellationToken, string eTag)
        {
            return ExecuteQueryMethods(RequestRecord, HttpMethodType.Post, page, messageId, cancellationToken, eTag);
        }

        public Task<object> ExecutePatchAsync(int page, long messageId, CancellationToken cancellationToken, string eTag)
        {
            return ExecuteQueryMethods(RequestRecord, HttpMethodType.Patch, page, messageId, cancellationToken, eTag);
        }

        public async Task<byte[]> ExecuteBinaryGet()
        {
            CallRequest<T> cr = CallRequest<T>.ForBinaryResource(_ctx, RequestRecord);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();
            var content = await response.GetBinaryContent();

            //TODO: dispose of this properly at some point, but for a workaround dispose manually here
            response.Dispose();

            return content;
        }

        /// <summary>
        /// Executes GET on cloud resource assigned to this proxy
        /// </summary>
        public Task<object> ExecuteGetAsync(int page, int pageSize, CancellationToken cancellationToken)
        {
            return ExecuteQueryMethods(RequestRecord, HttpMethodType.Get, page, pageSize, cancellationToken);
        }

        public async Task<object> ExecutePutAsync()
        {
            CallRequest<T> cr = CallRequest<T>.ForResource(_ctx, RequestRecord, HttpMethodType.Put, -1, 0);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();            
            var obj = Resource.DeserializeJsonString(await response.ConsumeContentAsStringAsync());

            //TODO: dispose of this properly at some point, but for a workaround dispose manually here
            response.Dispose();

            return obj;
        }

        public async Task DeleteAsync(long messageId, string eTag)
        {
            CallRequest<T> cr = CallRequest<T>.ForResource(_ctx, RequestRecord, HttpMethodType.Delete, -1, messageId, eTag);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync();

            //TODO: dispose of this properly at some point, but for a workaround dispose manually here
            response?.Dispose();
        }

        private async Task<object> ExecuteQueryMethods(RequestContent<T> requestContent, HttpMethodType callType, int page, long messageId, CancellationToken cancellationToken, string eTag = "")
        {
            CallRequest<T> cr = CallRequest<T>.ForResource(_ctx, requestContent, callType, page, messageId, eTag);
            CallResponse response = await cr.ExecuteRequestWithRetryAsync(cancellationToken).ConfigureAwait(false);
            string data = await response.ConsumeContentAsStringAsync().ConfigureAwait(false);
            byte[] dataArray = await response.GetBinaryContent();

            if (LoggerManager.Instance.LoggingLevel == MessageType.Trace)
            {
                ODataDebugWrapper resultSetDebug = new ODataDebugWrapper();
                resultSetDebug = ContentSerializationHelper.DeserializeJsonBinary<ODataDebugWrapper>(dataArray);
                Logger.Current.Log("Server Response: " + data, MessageType.Trace);
                data = resultSetDebug?.Response?.Body;
            }

            if (string.IsNullOrEmpty(data))
            {
                return Resource.EmptyResponseHandling(response.IsStatusSuccess());
            }

            //TODO: dispose of this properly at some point, but for a workaround dispose manually here
            response.Dispose();

            return Resource.DeserializeJsonString(data); 
        }
    }
}
