{"name": "FndTstOffline", "component": "FNDTST", "version": "1706901162:1948287535", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}, "CustomersWithoutJohn": {"name": "CustomersWithoutJohn", "entity": "TstCustomer", "array": true, "offlinefilter": {"!=": [{"var": "CustomerName"}, "<PERSON>"]}}, "Expands": {"name": "Expands", "entity": "TstExpand", "array": true}, "ExpandsWithRefFilter": {"name": "ExpandsWithRefFilter", "entity": "TstExpand", "array": true, "offlinefilter": {"==": [{"var": "ExpandLetterRef.ColA"}, "<PERSON>"]}}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}, "CustomerType": {"datatype": "Text", "keygeneration": "User"}, "CustomerTypeDesc": {"datatype": "Text", "keygeneration": "User"}, "CreatedAt": {"datatype": "Timestamp", "keygeneration": "User"}}, "references": {"CustomerTypeRef": {"target": "TstCustomerType", "mapping": {"CustomerType": "TypeId"}}}, "arrays": {"ExpandsArray": {"target": "TstExpand", "mapping": {"CustomerNo": "ColA"}}, "ExpandsFilteredArray": {"target": "TstExpand", "mapping": {"CustomerNo": "ColA"}, "offlinefilter": {"==": [{"var": "Id"}, "<PERSON>"]}}}}, "TstCustomerOfflineQuery": {"name": "TstCustomerOfflineQuery", "CRUD": "Read", "luname": "TstCustomerOfflineQuery", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "c"}, "select": {"columns": [{"name": "c.<PERSON><PERSON><PERSON><PERSON>"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}, "TstCustomerType": {"name": "TstCustomerType", "hasETag": true, "CRUD": "Read", "luname": "TstCustomerType", "ludependencies": ["TstCustomerType"], "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text", "keygeneration": "User"}, "TypeDescription": {"datatype": "Text", "keygeneration": "User"}}}, "TstExpand": {"name": "TstExpand", "keys": ["Id"], "attributes": {"Id": {"datatype": "Text", "keygeneration": "User"}, "ColA": {"datatype": "Text", "keygeneration": "User"}}, "references": {"ExpandNumRef": {"target": "TstExpandNum", "mapping": {"Id": "Id"}}, "ExpandLetterRef": {"target": "TstExpandLetter", "mapping": {"Id": "Id"}}}}, "TstExpandNum": {"name": "TstExpandNum", "keys": ["Id"], "attributes": {"Id": {"datatype": "Text", "keygeneration": "User"}, "Col1": {"datatype": "Text", "keygeneration": "User"}, "Col2": {"datatype": "Text", "keygeneration": "User"}}}, "TstExpandLetter": {"name": "TstExpandLetter", "keys": ["Id"], "attributes": {"Id": {"datatype": "Text", "keygeneration": "User"}, "ColA": {"datatype": "Text", "keygeneration": "User"}, "ColB": {"datatype": "Text", "keygeneration": "User"}}}}}}