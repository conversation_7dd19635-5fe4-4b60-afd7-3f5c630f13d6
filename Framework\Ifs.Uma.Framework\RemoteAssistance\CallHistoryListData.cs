﻿#if REMOTE_ASSISTANCE
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Framework.UI.Lists;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Services;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    public class CallHistoryListData : ListData<CallHistoryItem>
    {
        private const string ActiveRemoteAssistanceCallLogQueryName = "RecentCallLogs";
        private readonly IOnlineDataHandler _onlineDataHandler;
        private readonly IDialogService _dialogService;
        private readonly IToastService _toastService;
        private readonly EntityDataSource _dataSource;
        private EntityQuery _query;

        public CallHistoryListData(IMetadata metadata, IOnlineDataHandler onlineDataHandler, IDialogService dialogService, IToastService toastService)
            : base(null)
        {
            _onlineDataHandler = onlineDataHandler;
            _dialogService = dialogService;
            _toastService = toastService;

            string projectionName = metadata.GetFirstProjectionWithFunction(RemoteAssistanceService.IsUserActiveFunctionName);
            _dataSource = EntityDataSource.FromEntitySet(metadata, projectionName, ActiveRemoteAssistanceCallLogQueryName);
        }

        protected override async Task OnUpdateAsync()
        {
            _query = _dataSource != null ? new EntityQuery(_dataSource) : null;

            if (_query == null)
            {
                _toastService.Show(ToastType.Error, Strings.CouldNotLoadCallHistory);
                return;
            }

            _query.AddSort("Timestamp", Database.ESortOrder.Descending);
            Items.Clear();

            while (true)
            {
                try
                {
                    EntityQueryResult result = await _onlineDataHandler.GetRecordsAsync(_query, CancellationToken.None);

                    using (Items.DeferRefresh())
                    {
                        if (result.Records != null)
                        {
                            foreach (EntityRecord row in result.Records)
                            {
                                CallHistoryItem callHistoryItem = new CallHistoryItem(row.Row[nameof(CallHistoryItem.LogId)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.EventId)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.ActiveGroupUserCount)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.FromFndUserId)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.FromFndUserName)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.ReceiverUserId)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.ReceiverName)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.ToGroupName)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.ToFndUserName)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.EventTypeDb)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.DeclinedReasonCodeDb)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.CallTypeDb)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.CallStatus)]?.ToString(),
                                                              row.Row[nameof(CallHistoryItem.Timestamp)]?.ToString());
                                Items.Add(callHistoryItem);
                            }

                            if (!result.Records.Any())
                            {
                                break;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    await _dialogService.Alert(Strings.RemoteAssistance, ex.Message);
                    break;
                }

                _query.Skip = Items?.Count;
            }
        }

        protected override async void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                await UpdateAsync();
            }
        }
    }
}
#endif
