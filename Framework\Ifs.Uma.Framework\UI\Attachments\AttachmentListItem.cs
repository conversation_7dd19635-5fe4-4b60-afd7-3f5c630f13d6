﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public abstract class AttachmentListItem : ObservableBase
    {
        private string _description;
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        private string _title;
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        private string _statusLabel = string.Empty;
        public string StatusLabel
        {
            get => _statusLabel;
            private set => SetProperty(ref _statusLabel, value);
        }

        private AttachmentStatus _status = AttachmentStatus.Unknown;

        public AttachmentStatus Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    StatusLabel = _status.ToLocalisedString();
                    OnPropertyChanged(nameof(IsDownloadable));
                    OnPropertyChanged(nameof(IsDownloading));

                    if (Status == AttachmentStatus.Downloaded)
                    {
                        NotifyDownLoadCompleted?.Invoke(null, null);
                    }
                }
            }
        }

        private object _thumbnailStorage;
        public object ThumbnailStorage
        {
            get => _thumbnailStorage;
            set => SetProperty(ref _thumbnailStorage, value);
        }

        public bool IsDownloadable
        {
            get => _status == AttachmentStatus.DownloadFailed || _status == AttachmentStatus.RequiresDownload || _status == AttachmentStatus.Unknown;
        }

        public bool IsDownloading
        {
            get => _status == AttachmentStatus.Downloading;
        }

        public event EventHandler<object> NotifyDownLoadCompleted;

        public abstract Task<ILocalFileInfo> GetFileInfoAsync();

        public abstract Task DownloadFileAsync();
    }
}
