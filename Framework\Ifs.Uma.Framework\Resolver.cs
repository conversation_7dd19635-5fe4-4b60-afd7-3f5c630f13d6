﻿using System;

namespace Ifs.Uma.Framework
{
    public interface IResolver
    {
        bool TryResolve<T>(out T result) where T : class;
        object Resolve(Type type);
        object BuildUp(object obj);
    }

    public static class ResolverExtensions
    {
        public static T Resolve<T>(this IResolver resolver)
        {
            return (T)resolver.Resolve(typeof(T));
        }
    }

    public static class Resolver
    {
        public static IResolver Handler { get; set; }

        public static object Resolve(Type type)
        {
            return Handler.Resolve(type);
        }

        public static T Resolve<T>()
        {
            return (T)Handler.Resolve(typeof(T));
        }

        public static bool TryResolve<T>(out T result) where T : class
        {
            return Handler.TryResolve(out result);
        }

        public static object BuildUp(object obj)
        {
            return Handler.BuildUp(obj);
        }
    }
}
