﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_edm_application", Columns = "FileType", Unique = true)]
    public class EdmApplication : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "edm_application";

        #region Field Definitions

        private string _description;
        private string _documentType;
        private string _fileExtention;
        private string _fileType;

        #endregion

        public EdmApplication()
            : base(DbTableName)
        {
        }

        #region Property Definitions

        [Column(Storage = nameof(_description), MaxLength = 250)]
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        [Column(Storage = nameof(_documentType))]
        public string DocumentType
        {
            get => _documentType;
            set => SetProperty(ref _documentType, value);
        }

        [Column(Storage = nameof(_fileExtention))]
        public string FileExtention
        {
            get => _fileExtention;
            set => SetProperty(ref _fileExtention, value);
        }

        [Column(Storage = nameof(_fileType), Mandatory = true, ServerPrimaryKey = true)]
        public string FileType
        {
            get => _fileType;
            set => SetProperty(ref _fileType, value);
        }

        #endregion
    }
}
