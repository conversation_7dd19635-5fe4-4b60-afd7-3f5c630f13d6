﻿using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;
using Unity.Attributes;
using Prism.Events;
using System;

namespace Ifs.Uma.Framework.UI.Observables
{
    public class EditAwareViewModel : ViewModelBase
    {
        private bool _hasChanges;
        public bool HasChanges
        {
            get { return _hasChanges; }
            protected set
            {
                if (_hasChanges != value)
                {
                    _hasChanges = value;
                    OnPropertyChanged(nameof(HasChanges));
                    OnHasChangesChanged();
                }
            }
        }

        private bool _isEditingAllowed = true;
        public bool IsEditingAllowed
        {
            get { return _isEditingAllowed; }
            protected set
            {
                if (_isEditingAllowed != value)
                {
                    _isEditingAllowed = value;
                    OnPropertyChanged(() => IsEditingAllowed);
                    OnIsEditingAllowedChanged();
                }
                else if (!HasChanges)
                {
                    _isEditingAllowed = true;
                    OnPropertyChanged(() => IsEditingAllowed);
                    OnIsEditingAllowedChanged();
                }
            }
        }

        private string _editingGroup = Guid.NewGuid().ToString();
        protected string EditingGroup
        {
            get { return _editingGroup; }
            set
            {
                if (_editingGroup != value)
                {
                    _editingGroup = value;
                    OnPropertyChanged(() => EditingGroup);
                }
            }
        }
        
        private readonly IEventAggregator _eventAggregator;
        public IEventAggregator EventAggregator => _eventAggregator;

        [OptionalDependency]
        public override ILogger Logger { get; set; }

        private readonly Guid _screenId = Guid.NewGuid();

        public EditAwareViewModel(IEventAggregator eventAggregator)
        {
            _eventAggregator = eventAggregator;
        }
        
        protected virtual void OnHasChangesChanged()
        {
            if (_eventAggregator != null)
            {
                if (HasChanges)
                {
                    if (!IsEditingAllowed)
                    {
                        throw new InvalidOperationException("Screen is locked and cannot be edited");
                    }

                    DisableGroup();
                }
                else
                {
                    EnableGroup();
                }
            }
        }

        protected void DisableGroup()
        {
            DisableEditingGroupEvent arg = new DisableEditingGroupEvent();
            arg.Group = EditingGroup;
            arg.ScreenId = _screenId;
            _eventAggregator.GetEvent<DisableEditingGroupEvent>().Publish(arg);
        }

        protected void EnableGroup()
        {
            EnableEditingGroupEvent arg = new EnableEditingGroupEvent();
            arg.Group = EditingGroup;
            _eventAggregator.GetEvent<EnableEditingGroupEvent>().Publish(arg);
        }

        protected virtual void OnIsEditingAllowedChanged()
        {
        }

        protected virtual void OnDisableEditingGroup(DisableEditingGroupEvent obj)
        {
            if (obj.Group == EditingGroup && obj.ScreenId != _screenId)
            {
                IsEditingAllowed = false;
            }
        }

        protected virtual void OnEnableEditingGroup(EnableEditingGroupEvent obj)
        {
            if (obj.Group == EditingGroup)
            {
                IsEditingAllowed = true;
            }
        }

        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                if (_eventAggregator != null)
                {
                    _eventAggregator.GetEvent<EnableEditingGroupEvent>().Subscribe(OnEnableEditingGroup);
                    _eventAggregator.GetEvent<DisableEditingGroupEvent>().Subscribe(OnDisableEditingGroup);
                }
            }
            else
            {
                if (_eventAggregator != null)
                {
                    _eventAggregator.GetEvent<EnableEditingGroupEvent>().Unsubscribe(OnEnableEditingGroup);
                    _eventAggregator.GetEvent<DisableEditingGroupEvent>().Unsubscribe(OnDisableEditingGroup);
                }
            }
        }
    }

    public class EnableEditingGroupEvent : PubSubEvent<EnableEditingGroupEvent>
    {
        public string Group { get; set; }
    }

    public class DisableEditingGroupEvent : PubSubEvent<DisableEditingGroupEvent>
    {
        public string Group { get; set; }
        public Guid ScreenId { get; set; }
    }
}
