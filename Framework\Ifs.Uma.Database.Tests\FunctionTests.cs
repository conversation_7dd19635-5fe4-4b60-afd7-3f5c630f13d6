﻿using System;
using Ifs.Uma.Data;
using Ifs.Uma.Utility;
using NUnit.Framework;

namespace Ifs.Uma.Database.Tests
{
    [TestFixture]
    public class FunctionTests
    {
        [Test]
        public void DateTimeNow()
        {
            string sql = _db.Factory.Builder.IfsDateTimeNow;

            DateTime now = DateTime.Now;

            object result = RunSql("select " + sql);
            Assert.IsTrue(result is long);

            DateTime dt = new DateTime((long)result);
            Assert.IsTrue(dt > now.AddSeconds(-2) && dt < now.AddSeconds(4));
        }

        [Test]
        public void DateTimeNowUtc()
        {
            string sql = _db.Factory.Builder.IfsDateTimeUtcNow;

            DateTime now = DateTime.UtcNow;

            object result = RunSql("select " + sql);
            Assert.IsTrue(result is long);

            DateTime dt = new DateTime((long)result);
            Assert.IsTrue(dt > now.AddSeconds(-2) && dt < now.AddSeconds(4));
        }

        [Test]
        public void DateTimeNowDate()
        {
            string sql = _db.Factory.Builder.IfsDateTimeNowDate;

            DateTime nowDate = DateTime.Now.Date;

            object result = RunSql("select " + sql);
            Assert.IsTrue(result is long);

            DateTime dt = new DateTime((long)result);
            Assert.AreEqual(dt, nowDate);
        }

        [Test]
        public void DateTimeAddYears()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            object result = RunSql("select ifs_datetime_add_years(" + dt.Ticks + ", 2)");
            Assert.IsTrue(result is long);

            DateTime dtResult = new DateTime((long)result);

            Assert.AreEqual(new DateTime(2002, 7, 9, 12, 34, 56), dtResult);
        }

        [Test]
        public void DateTimeAddMonths()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            object result = RunSql("select ifs_datetime_add_months(" + dt.Ticks + ", 3)");
            Assert.IsTrue(result is long);

            DateTime dtResult = new DateTime((long)result);

            Assert.AreEqual(new DateTime(2000, 10, 9, 12, 34, 56), dtResult);
        }

        [Test]
        public void DateTimeAddDays()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            object result = RunSql("select ifs_datetime_add_days(" + dt.Ticks + ", 1.5)");
            Assert.IsTrue(result is long);

            DateTime dtResult = new DateTime((long)result);

            Assert.AreEqual(new DateTime(2000, 7, 11, 0, 34, 56), dtResult);
        }

        [Test]
        public void DateTimeAddHours()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            object result = RunSql("select ifs_datetime_add_hours(" + dt.Ticks + ", 1.5)");
            Assert.IsTrue(result is long);

            DateTime dtResult = new DateTime((long)result);

            Assert.AreEqual(new DateTime(2000, 7, 9, 14, 4, 56), dtResult);
        }

        [Test]
        public void DateTimeAddMinutes()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            object result = RunSql("select ifs_datetime_add_minutes(" + dt.Ticks + ", 1.5)");
            Assert.IsTrue(result is long);

            DateTime dtResult = new DateTime((long)result);

            Assert.AreEqual(new DateTime(2000, 7, 9, 12, 36, 26), dtResult);
        }

        [Test]
        public void DateTimeAddSeconds()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            object result = RunSql("select ifs_datetime_add_seconds(" + dt.Ticks + ", 65)");
            Assert.IsTrue(result is long);

            DateTime dtResult = new DateTime((long)result);

            Assert.AreEqual(new DateTime(2000, 7, 9, 12, 36, 1), dtResult);
        }

        [Test]
        public void DateTimeDateFunction()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            object result = RunSql("select ifs_datetime_date(" + dt.Ticks + ")");
            Assert.IsTrue(result is long);

            DateTime dtResult = new DateTime((long)result);

            Assert.AreEqual(new DateTime(2000, 7, 9, 0, 0, 0), dtResult);
        }

        [Test]
        public void DateTimeTimeFunction()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            object result = RunSql("select ifs_datetime_time(" + dt.Ticks + ")");
            Assert.IsTrue(result is long);

            DateTime dtResult = new DateTime((long)result);

            Assert.AreEqual(new DateTime(1, 1, 1, 12, 34, 56), dtResult);
        }

        [Test]
        [SetCulture("sv-SE")]
        public void DateTimeFormatFunction()
        {
            DateTime dt = new DateTime(2000, 7, 9, 12, 34, 56);

            // Date
            string result = RunSql("select ifs_datetime_format(" + dt.Ticks + ", 'd')") as string;
            Assert.AreEqual("2000-07-09", result);

            // Time
            result = RunSql("select ifs_datetime_format(" + dt.Ticks + ", 't')") as string;
            Assert.AreEqual("12:34", result);

            // DateTime
            result = RunSql("select ifs_datetime_format(" + dt.Ticks + ", 'g')") as string;
            Assert.AreEqual("2000-07-09 12:34", result);

            // DateTime Invariant
            result = RunSql("select ifs_datetime_format(" + dt.Ticks + ", null)") as string;
            Assert.AreEqual("2000-07-09T12:34:56.0000000", result);
        }

        #region Setup

        private const string TestConnectionString = "data source=t.db";
        private ILogger _logger;
        private DbProviderFactory _factory;
        private MappingSource _source;
        private IMetaModel _model;
        private DbInternal _db;

        static FunctionTests()
        {
            PlatformServicesTest.Initialize();
        }

        public FunctionTests()
        {
            _logger = new DebugLogger();
        }

        [SetUp]
        protected virtual void BeforeTest()
        {
            const string DllPath = "Ifs.Uma.Database.SQLite, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null";
            Environment.CurrentDirectory = TestContext.CurrentContext.TestDirectory;

            // select your DBMS
            _factory = DbProviderFactory.Create(DllPath, _logger);
            Assert.IsNotNull(_factory);
            // Select your mapping source (no custom fields)
            _source = new AttributeMappingSource(null, _logger, null);
            Assert.IsNotNull(_source);
            _model = _source.GetModel(typeof(TestDataContext));
            Assert.IsNotNull(_model);

            _db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            _db.CreateDatabase(_model);
            Assert.AreEqual(_db.GetStatus(), DbStatus.Valid);
        }

        [TearDown]
        protected virtual void AfterTest()
        {
            _factory.Dispose();
        }

        #endregion

        private object RunSql(string sql)
        {
            return _db.TransactionF((cmd) =>
            {
                cmd.CommandText = sql;
                return cmd.ExecuteScalar();
            });
        }
    }
}
