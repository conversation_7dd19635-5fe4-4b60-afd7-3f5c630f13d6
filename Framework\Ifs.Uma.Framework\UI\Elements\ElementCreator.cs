﻿using System;
using System.Collections.Generic;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Charts;
using Ifs.Uma.Framework.UI.Elements.Calendars;
using Ifs.Uma.Framework.UI.Elements.Groups;
using Ifs.Uma.Framework.UI.Elements.HtmlText;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Ifs.Uma.Framework.UI.Elements.Maps;
using Ifs.Uma.Framework.UI.Elements.Markdown;
using Ifs.Uma.Framework.UI.Elements.ProcessViewer;
using Ifs.Uma.Framework.UI.Elements.Selectors;
using Ifs.Uma.Framework.UI.Elements.Singletons;
using Ifs.Uma.Framework.UI.Images;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements
{
    public interface IElementCreator
    {
        ElementBase CreateElement(string projectionName, CpiElementContent elementContent);
        ElementBase CreateOfflineWarningElement(string projectionName, IReadOnlyList<CpiCommandGroup> commandGroups, IReadOnlyList<CpiElementContent> elementContent);
    }

    public class ElementCreator : IElementCreator
    {
        private readonly IResolver _resolver;
        private readonly IMetadata _metadata;
        private readonly IExpressionRunner _expressionRunner;
        private readonly ICommandExecutor _commandExecutor;
        private readonly ILogger _logger;
        private readonly IDialogService _dialogService;

        public ElementCreator(IResolver resolver, IMetadata metadata, IExpressionRunner expressionRunner, ICommandExecutor commandExecutor, ILogger logger, IDialogService dialogService)
        {
            _resolver = resolver;
            _metadata = metadata;
            _expressionRunner = expressionRunner;
            _commandExecutor = commandExecutor;
            _logger = logger;
            _dialogService = dialogService;
        }

        public ElementBase CreateElement(string projectionName, CpiElementContent elementContent)
        {
            try
            {
                ElementBase element = null;

                if (elementContent.Map != null)
                {
                    element = _resolver.Resolve<MapElement>();
                }

                if (elementContent.Calendar != null)
                {
                    element = _resolver.Resolve<CalendarElement>();
                }

                if (elementContent.Group != null)
                {
                    element = _resolver.Resolve<GroupElement>();
                }

                if (elementContent.Selector != null)
                {
                    element = _resolver.Resolve<SelectorElement>();
                }

                if (elementContent.Singleton != null)
                {
                    element = _resolver.Resolve<SingletonElement>();
                }

                if (elementContent.MarkdownText != null)
                {
                    element = _resolver.Resolve<MarkdownElement>();
                }

                if (elementContent.HtmlText != null)
                {
                    element = _resolver.Resolve<HtmlTextElement>();
                }

                if (elementContent.List != null)
                {
                    element = _resolver.Resolve<ListElement>();
                }

                if (elementContent.LineChart != null)
                {
                    element = _resolver.Resolve<LineChartElement>();
                }

                if (elementContent.StateIndicator != null)
                {
                    element = _resolver.Resolve<StateIndicatorElement>();
                }

                if (elementContent.FileSelector != null)
                {
                    element = _resolver.Resolve<FileSelectorElement>();
                }

                if (elementContent.ImageViewer != null)
                {
                    element = _resolver.Resolve<ImageViewerElement>();
                }

                if (elementContent.RepeatingSection != null)
                {
                    RepeatingSectionElement rse = _resolver.Resolve<RepeatingSectionElement>();
                    rse.ElementCreator = this;
                    element = rse;
                }

                if (elementContent.ProcessViewer != null)
                {
                    ProcessViewerElement proc = _resolver.Resolve<ProcessViewerElement>();
                    proc.ElementCreator = this;
                    element = proc;
                }

                if (element != null)
                {
                    if (element.Initialize(_metadata, _expressionRunner, _commandExecutor, _logger, _dialogService, projectionName, elementContent))
                    {
                        return element;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Unexpected, ex);
                return null;
            }
        }

        public ElementBase CreateOfflineWarningElement(string projectionName, IReadOnlyList<CpiCommandGroup> commandGroups, IReadOnlyList<CpiElementContent> elementContent)
        {
            try
            {
                OfflineWarningElement element = _resolver.Resolve<OfflineWarningElement>();
                
                if (element != null)
                {
                    if (element.Initialize(_metadata, _expressionRunner, _commandExecutor, _logger, _dialogService, projectionName, null))
                    {
                        element.CommandGroups = commandGroups;
                        element.ElementContent = elementContent;

                        return element;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Unexpected, ex);
                return null;
            }
        }
    }
}
