﻿using Ifs.Uma.UI;
using Ifs.Uma.UI.Images;

namespace Ifs.Uma.Framework.UI.Elements
{
    public abstract class ElementWithButtonBase : ElementBase
    {
        public Command OpenElementButtonCommand { get; set; }

        private string _openElementButtonLabel;

        public string OpenElementButtonLabel
        {
            get { return _openElementButtonLabel; }
            private set { SetProperty(ref _openElementButtonLabel, value); }
        }

        private int? _elementButtonItemsCount;

        protected int? ElementButtonItemsCount
        {
            get
            {
                return _elementButtonItemsCount;
            }
            set
            {
                _elementButtonItemsCount = value;
                UpdateElementButtonItemsCount();
            }
        }

        protected bool? PreviousOpenElementButtonCommandCanExecuteState { get; set; }

        public UmaImage OpenElementButtonImage { get; set; }

        protected bool OpenElementButtonCommandCanExecute()
        {
            return !ViewData.Record.HasChanges;
        }

        protected void UpdateElementButtonItemsCount()
        {
            string label = $"{Header}";

            if (ElementButtonItemsCount.HasValue)
            {
                label += $" ({ElementButtonItemsCount})";
            }

            OpenElementButtonLabel = label;
        }
    }
}
