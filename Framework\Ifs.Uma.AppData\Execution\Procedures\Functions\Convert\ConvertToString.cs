﻿using System;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Convert
{
    internal sealed class ConvertToString : FwFunction
    {
        public const string FunctionNamespace = "Convert";
        public const string FunctionName = "ToString";

        public ConvertToString()
            : base(FunctionNamespace, FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return ObjectConverter.ToString(parameters[0].GetValue());
        }
    }
}
