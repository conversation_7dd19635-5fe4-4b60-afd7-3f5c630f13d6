﻿using System.Linq;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Security
{
    internal sealed class SecurityIsEntityReadGranted : SecurityFunction
    {
        public const string FunctionName = "IsEntityReadGranted";

        public SecurityIsEntityReadGranted()
            : base(FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string projection = context.ProjectionName;
            string name = parameters[0].GetString();

            if (string.IsNullOrEmpty(projection) || string.IsNullOrEmpty(name))
            {
                return false;
            }

            var security = context.DbDataContext.ClientSecurities
                .FirstOrDefault(x => x.Projection == projection && x.ObjectName == name && x.GrantType == "ENTITY_READ");

            if (security == null)
            {
                return true;
            }

            return security.Granted;
        }
    }
}
