﻿#if REMOTE_ASSISTANCE
using System;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    public sealed class RemoteAssistanceCommandItem : CommandItem
    {
        private readonly INavigator _navigator;
        private readonly IExpressionRunner _expressionRunner;
        private readonly ISettings _settings;
        private readonly IToastService _toastService;
        private readonly FwDataContext _ctx;

        private Tuple<string, string> _loadedKey;

        private PageData _pageData;
        public PageData PageData
        {
            get => _pageData;
            set => SetProperty(ref _pageData, value, OnPageDataChanged);
        }

        private ViewData _viewData;
        public ViewData ViewData
        {
            get => _viewData;
            set => SetProperty(ref _viewData, value, OnViewDataChanged);
        }

        private CpiNavigate _remoteAssistanceConfig;
        public CpiNavigate RemoteAssistanceConfig
        {
            get => _remoteAssistanceConfig;
            set => SetProperty(ref _remoteAssistanceConfig, value, OnRemoteAssistanceConfigChanged);
        }

        public UpdatingState UpdatingState { get; } = new UpdatingState();

        public RemoteAssistanceCommandItem(INavigator navigator, IExpressionRunner expressionRunner, ISettings settings, IDatabaseController db, IToastService toastService)
        {
            _navigator = navigator;
            _expressionRunner = expressionRunner;
            _settings = settings;
            _toastService = toastService;
            _ctx = db.CreateDataContext();

            Icon = IconUtils.Phone;
            ShowAsAction = CommandPriority.Always;

            UpdatingState.IsAnythingUpdatingChanged += UpdatingState_IsAnythingUpdatingChanged;
        }

        private void UpdatingState_IsAnythingUpdatingChanged(object sender, EventArgs e)
        {
            OnDataChanged();
        }

        private void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            if (oldValue != null)
            {
                oldValue.PropertyChanged -= PageData_PropertyChanged;
            }

            if (newValue != null)
            {
                newValue.PropertyChanged += PageData_PropertyChanged;
            }

            ViewData = newValue?.DefaultViewData;
        }

        private void PageData_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PageData.HasChanges))
            {
                UpdateIsEnabled();
            }
        }

        private void OnViewDataChanged(ViewData oldValue, ViewData newValue)
        {
            if (oldValue?.Record != null)
            {
                oldValue.Record.DataChanged -= Record_DataChanged;
            }

            if (newValue?.Record != null)
            {
                newValue.Record.DataChanged += Record_DataChanged;
            }

            OnDataChanged();
        }

        private void Record_DataChanged(object sender, EventArgs e)
        {
            OnDataChanged();
        }

        private void OnRemoteAssistanceConfigChanged(CpiNavigate oldValue, CpiNavigate newValue)
        {
            OnDataChanged();
        }

        private void OnDataChanged()
        {
            if (!RecordData.HasLoadedExistingRecord(ViewData?.Record))
            {
                _loadedKey = null;
                IsEnabled = false;
                IsVisible = false;
                return;
            }

            UpdateIsEnabled();

            Tuple<string, string> key = GetKey();
            if (!Equals(_loadedKey, key))
            {
                _loadedKey = key;
            }

            UpdateStates();
        }

        protected override async Task OnExecuteAsync()
        {
            if (IsEnabled && IsVisible)
            {
                string formatStr = _remoteAssistanceConfig?.Url.Replace("[", "{").Replace("]", "}").Replace("?", ";");

                string refUrl = formatStr != null ? _expressionRunner.InterpolateString(formatStr, ViewData?.Record, true, true) : null;

                RemoteAssistNavParam navParam = null;

                if (_loadedKey != null)
                {
                    navParam = new RemoteAssistNavParam(refUrl, _loadedKey.Item1, _loadedKey.Item2);
                }
                else
                {
                    _toastService.Show(ToastType.Info, Strings.DetailsWillNotBeSent);
                }

                await _navigator.NavigateToAsync(FrameworkLocations.RemoteAssistance, navParam);
            }
        }

        public void UpdateStates()
        {
            IsVisible = _settings.GetBoolean(SettingsExtensions.RemoteAssistanceEnabled, false) && _remoteAssistanceConfig != null;

            UpdateIsEnabled();
        }

        private void UpdateIsEnabled()
        {
            bool hasChanges = PageData?.HasChanges == true || ViewData?.Record?.HasChanges == true;

            IsEnabled = IsVisible && !hasChanges && ViewData?.Record?.IsNew() == false && !UpdatingState.IsAnythingUpdating;
        }

        private Tuple<string, string> GetKey()
        {
            ObjPrimaryKey key = ViewData?.Record?.ToPrimaryKey();
            string keyRef = key?.ToKeyRef();

            if (keyRef != null)
            {
                string entityName = ViewData.Record.EntityName;

                // Get the name of the LU connected if there is any
                // This can only be set in Marble for queries
                CpiEntity entity = ViewData.Record.Metadata.FindEntity(ViewData.Record.ProjectionName, ViewData.Record.EntityName);
                EntitySyncPolicy syncPolicy = ViewData.Record.Metadata.GetEntitySyncPolicy(ViewData.Record.EntityName);
                if (syncPolicy == EntitySyncPolicy.None && !string.IsNullOrEmpty(entity?.LuName))
                {
                    entityName = entity.LuName;
                }

                string clientKeys = key.ToKeySeparatedValues();
                ClientKeysMap clientKeyMap = _ctx.ClientKeysMap.FirstOrDefault(x => x.TableName == key.Table.TableName.ToPascalCase() && x.ClientKeys == clientKeys);

                if (clientKeyMap != null) // client-server key mapping exists
                {
                    return Tuple.Create(entityName, ObjPrimaryKey.FromKeySeparatedValues(key.Table, clientKeyMap.ServerKeys).ToKeyRef());
                }
                else if (ViewData.Record.HasObjId()) // this is a record that already has proper server keys
                {
                    return Tuple.Create(entityName, keyRef);
                }
            }

            return null;
        }
    }
}
#endif
