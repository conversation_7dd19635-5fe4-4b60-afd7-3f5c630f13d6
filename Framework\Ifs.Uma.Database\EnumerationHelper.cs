﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public static class EnumerationHelper
    {
        private const char EnumValueSeparator = '^';
        private static readonly char[] EnumValueSeparatorArray = new char[] { EnumValueSeparator };

        public static int MaxServerValueLength(this IMetaEnumeration source)
        {
            return source != null && source.Values != null ?
                source.Values.Select(x => x.ServerValue.Length).Max() : 0;
        }

        public static string NormaliseServerValue(this IMetaEnumeration source, string serverValue)
        {
            string result = null;
            if (source != null && source.Values != null && !string.IsNullOrEmpty(serverValue))
            {
                bool isMultiValue = serverValue[0] == EnumValueSeparator;
                if (isMultiValue)
                {
                    result = source.NormaliseMultiServerValue(serverValue);
                }
                else
                {
                    result = source.NormaliseSingleServerValue(serverValue);
                }
            }
            return result;
        }

        public static string NormaliseMultiServerValue(this IMetaEnumeration source, string serverValue)
        {
            // Multi-enums are in the format '^VALUE1^VALUE2^'

            string result = null;
            if (source != null && source.Values != null && !string.IsNullOrEmpty(serverValue))
            { 
                string[] serverValues = serverValue.Split(EnumValueSeparatorArray, StringSplitOptions.RemoveEmptyEntries);

                StringBuilder sbResult = new StringBuilder();
                sbResult.Append(EnumValueSeparator);
                foreach (IMetaEnumValue enumValue in source.Values)
                {
                    if (serverValues.Contains(enumValue.ServerValue, StringComparer.OrdinalIgnoreCase))
                    {
                        sbResult.Append(enumValue.ServerValue);
                        sbResult.Append(EnumValueSeparator);
                    }
                }

                if (sbResult.Length > 1)
                {
                    result = sbResult.ToString();
                }
            }
            return result;
        }

        public static string NormaliseSingleServerValue(this IMetaEnumeration source, string serverValue)
        {
            string result = null;
            if (source != null && source.Values != null && !string.IsNullOrEmpty(serverValue))
            {
                foreach (IMetaEnumValue enumValue in source.Values)
                {
                    if (string.Equals(serverValue, enumValue.ServerValue, StringComparison.OrdinalIgnoreCase))
                    {
                        result = enumValue.ServerValue;
                        break;
                    }
                }
            }
            return result;
        }

        public static string ServerValue(this IMetaEnumeration source, object value)
        {
            string result = null;
            if (source != null && source.Values != null && value != null)
            {
                if (source.EnumType != null && value.GetType() == source.EnumType)
                {
                    result = source.Values.Where(x => value.Equals(x.LocalValue)).Select(x => x.ServerValue).FirstOrDefault();
                }
                else
                {
                    result = source.NormaliseServerValue(value as string);
                }
            }
            return result;
        }

        public static object LocalValue(this IMetaEnumeration source, string serverValue)
        {
            object result = null;
            if (source != null && source.Values != null && !string.IsNullOrEmpty(serverValue))
            {
                result = source.Values.Where(x => serverValue.Equals(x.ServerValue, StringComparison.OrdinalIgnoreCase))
                    .Select(x => x.LocalValue).FirstOrDefault();
            }
            return result;
        }
    }
}
