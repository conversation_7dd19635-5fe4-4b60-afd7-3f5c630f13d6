﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.OData.Core" version="7.5.1" targetFramework="net451" />
  <package id="Microsoft.OData.Edm" version="7.5.1" targetFramework="net451" />
  <package id="Microsoft.Spatial" version="7.5.1" targetFramework="net451" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="NodaTime" version="3.1.9" targetFramework="net472" />
  <package id="NUnit" version="3.10.1" targetFramework="net451" />
  <package id="NUnit3TestAdapter" version="3.10.0" targetFramework="net451" />
  <package id="OpenCover" version="4.7.922" targetFramework="net461" />
  <package id="OpenCoverToCoberturaConverter" version="0.3.4" targetFramework="net461" />
  <package id="Prism.Core" version="7.0.0.396" targetFramework="net451" />
  <package id="SQLitePCLRaw.bundle_sqlcipher" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.core" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.lib.sqlcipher.linux" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.lib.sqlcipher.osx" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.lib.sqlcipher.windows" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.provider.sqlcipher.net45" version="1.1.14" targetFramework="net461" />
  <package id="StyleCop.Analyzers" version="1.0.2" targetFramework="net451" developmentDependency="true" />
  <package id="System.Configuration.ConfigurationManager" version="4.7.0" targetFramework="net461" />
  <package id="System.Reflection.Emit" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.1" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="6.0.0" targetFramework="net472" />
  <package id="System.Security.Permissions" version="6.0.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net461" />
  <package id="Unity" version="5.8.13" targetFramework="net461" requireReinstallation="true" />
</packages>