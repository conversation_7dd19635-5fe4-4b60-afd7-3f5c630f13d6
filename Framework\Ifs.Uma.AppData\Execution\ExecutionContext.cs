﻿using System;
using System.Collections.Generic;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.StringExpressions;

namespace Ifs.Uma.AppData.Execution
{
    public abstract class ExecutionContext : IExpressionValueProvider, IStringExpressionValueProvider
    {
        public string ProjectionName { get; }
        public IMetadata Metadata { get; }
        public IExpressionRunner ExpressionRunner { get; }
        public IReadOnlyDictionary<string, VariableStorage> Vars { get; }
        public string DebugName { get; }

        protected ExecutionContext(string projectionName, IMetadata metadata, IExpressionRunner expressionRunner, string debugName, IReadOnlyDictionary<string, VariableStorage> vars)
        {
            ProjectionName = projectionName ?? throw new ArgumentNullException(nameof(projectionName));
            Metadata = metadata ?? throw new ArgumentNullException(nameof(metadata));
            ExpressionRunner = expressionRunner ?? throw new ArgumentNullException(nameof(expressionRunner));

            Dictionary<string, VariableStorage> ctxVars = new Dictionary<string, VariableStorage>();

            if (vars != null)
            {
                foreach (KeyValuePair<string, VariableStorage> kvp in vars)
                {
                    ctxVars[kvp.Key] = kvp.Value;
                }
            }

            DebugName = debugName;
            Vars = ctxVars;
        }

        public ExecutionException Fail(string message)
        {
            return NewException(DebugName + ": " + message);
        }

        protected abstract ExecutionException NewException(string message);
        
        public void Assign(string name, object value)
        {
            if (Equals(value, string.Empty))
            {
                value = null;
            }

            if (!OnAssign(name, value))
            {
                string strValue = ExecutionUtils.ValueToPreviewString(value);
                throw Fail($"Failed to assign value to {name}.{Environment.NewLine}Value = {strValue}");
            }
        }

        public bool TryAssign(string name, object value)
        {
            if (Equals(value, string.Empty))
            {
                value = null;
            }

            return OnAssign(name, value);
        }

        protected virtual bool OnAssign(string name, object value)
        {
            if (Vars.TryGetValue(name, out VariableStorage varStorage))
            {
                return varStorage.TrySetValue(value);
            }

            if (TryGetStructureVariable(name, out VariableStorage structStorage, out string attributeName))
            {
                return structStorage.TrySetStructureValue(attributeName, value);
            }

            return false;
        }

        public object GetValue(string name)
        {
            if (OnTryGetValue(name, out object value))
            {
                return value;
            }

            throw Fail($"Failed to read value from {name}");
        }

        public bool TryGetValue(string name, out object value)
        {
            return OnTryGetValue(name, out value);
        }

        protected virtual bool OnTryGetValue(string name, out object value)
        {
            if (Vars.TryGetValue(name, out VariableStorage var))
            {
                value = var.Value;
                return true;
            }

            if (TryGetStructureVariable(name, out VariableStorage storage, out string attribute)
                && storage.TryGetStructureValue(attribute, out value))
            {
                return true;
            }

            value = null;
            return false;
        }
        
        public virtual bool TryCallMethod(string methodName, object[] args, out object result)
        {
            result = null;
            return false;
        }

        private bool TryGetStructureVariable(string name, out VariableStorage storage, out string attributeName)
        {
            int dotPos = name.IndexOf('.');
            if (dotPos >= 0)
            {
                string structureName = name.Substring(0, dotPos);
                attributeName = name.Substring(dotPos + 1);

                if (Vars.TryGetValue(structureName, out storage) && storage != null)
                {
                    return true;
                }
            }

            attributeName = null;
            storage = null;
            return false;
        }
        
        protected virtual string FormatParamValue(string paramName, object value)
        {
            return AttributeFormatter.FormatValue(value);
        }

        public object ReadParamValue(object val)
        {
            if (val == null) return null;

            if (TryGetParamValue(val, out object result))
            {
                return result;
            }

            throw Fail($"Failed to read param value from '{val}'");
        }

        public bool TryGetParamValue(object val, out object result)
        {
            if (val == null)
            {
                result = null;
                return true;
            }

            if (val is string str)
            {
                if ((str.StartsWith("${") && str.EndsWith("}")) ||
                    (str.StartsWith("$[") && str.EndsWith("]")) ||
                    (str.StartsWith("$(") && str.EndsWith(")")))
                {
                    string propertyName = str.Substring(2, str.Length - 3);
                    if (TryGetValue(propertyName, out object value))
                    {
                        result = value;
                        return true;
                    }
                }

                bool success = true;
                string ValueProvider(string attributeName)
                {
                    if (TryGetValue(attributeName, out object paramValue))
                    {
                        return ObjectConverter.ToString(paramValue) ?? string.Empty;
                    }
                    else
                    {
                        success = false;
                        return string.Empty;
                    }
                }
                
                result = str.ReplaceParams(ValueProvider);
                return success;
            }

            result = val;
            return true;
        }

        public string InterpolateString(string str, bool localize)
        {
            if (str == null)
            {
                return null;
            }

            return ExpressionRunner.InterpolateString(str, this, localize);
        }

        bool IExpressionValueProvider.TryGetValue(string propertyName, out object value)
        {
            if (!TryGetValue(propertyName, out value))
            {
                // If an expression tries to read a value from an invalid variable we should 
                // throw an error to the developer to show something is wrong
                throw Fail($"Failed to read expression value from {propertyName}");
            }

            return true;
        }

        bool IStringExpressionValueProvider.TryGetValue(string propertyName, out object value)
        {
            return TryGetValue(propertyName, out value);
        }

        bool IStringExpressionValueProvider.TryGetFormattedValue(string propertyName, out string value)
        {
            value = null;

            if (TryGetValue(propertyName, out object paramValue))
            {
                value = FormatParamValue(propertyName, paramValue);
                return true;
            }

            return false;
        }
    }
}
