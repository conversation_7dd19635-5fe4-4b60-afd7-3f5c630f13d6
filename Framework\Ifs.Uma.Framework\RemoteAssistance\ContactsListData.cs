﻿#if REMOTE_ASSISTANCE
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Framework.UI.Lists;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Services;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    public class ContactsListData : ListData<ContactItem>
    {
        private const string RemoteAssistanceContactsQueryName = "RemoteAssistanceContacts";
        private readonly IOnlineDataHandler _onlineDataHandler;
        private readonly IDialogService _dialogService;
        private readonly EntityQuery _query;

        public ContactsListData(IMetadata metadata, IOnlineDataHandler onlineDataHandler, IDialogService dialogService)
            : base(null)
        {
            _onlineDataHandler = onlineDataHandler;
            _dialogService = dialogService;

            string projectionName = metadata.GetFirstProjectionWithFunction(RemoteAssistanceService.IsUserActiveFunctionName); // Use the first projection that has RA fragments
            EntityDataSource dataSource = EntityDataSource.FromEntitySet(metadata, projectionName, RemoteAssistanceContactsQueryName);
            _query = dataSource != null ? new EntityQuery(dataSource) : null;
            _query?.AddSort("Ordinal", Database.ESortOrder.Ascending);
        }

        protected override async Task OnUpdateAsync()
        {
            if (_query == null)
            {
                await _dialogService.Alert(Strings.Error, Strings.CouldNotLoadContacts);
                return;
            }

            while (true)
            {
                try
                {
                    EntityQueryResult result = await _onlineDataHandler.GetRecordsAsync(_query, CancellationToken.None);

                    using (Items.DeferRefresh())
                    {
                        if (result.Records != null)
                        {
                            foreach (EntityRecord row in result.Records)
                            {
                                Items.Add(new ContactItem(row.Row[nameof(ContactItem.Id)]?.ToString(),
                                                      row.Row[nameof(ContactItem.FullName)]?.ToString(),
                                                      row.Row[nameof(ContactItem.ActiveUserCount)]?.ToString(),
                                                      row.Row[nameof(ContactItem.AvailabilityDb)]?.ToString(),
                                                      row.Row[nameof(ContactItem.Ordinal)]?.ToString(),
                                                      row.Row[nameof(ContactItem.ContactTypeDb)]?.ToString()));
                            }

                            if (!result.Records.Any())
                            {
                                break;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    await _dialogService.Alert(Strings.RemoteAssistance, ex.Message);
                }

                _query.Skip = Items?.Count;
            }
        }

        protected override async void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                await UpdateAsync();
            }
        }
    }
}
#endif
