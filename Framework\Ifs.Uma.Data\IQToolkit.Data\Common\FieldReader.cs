﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Ifs.Uma.Utility;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace IQToolkit.Data.Common
{
    internal abstract class FieldReader
    {
        TypeCode[] typeCodes;

        protected FieldReader()
        {
        }

        protected void Init()
        {
            this.typeCodes = new TypeCode[this.FieldCount];
        }

        protected abstract int FieldCount { get; }
        protected abstract Type GetFieldType(int ordinal);
        protected abstract bool IsDBNull(int ordinal);
        protected abstract T GetValue<T>(int ordinal);
        protected abstract DateTime GetDateTime(int ordinal);
        protected abstract Decimal GetDecimal(int ordinal);
        protected abstract Double GetDouble(int ordinal);
        protected abstract Guid GetGuid(int ordinal);
        protected abstract Int16 GetInt16(int ordinal);
        protected abstract Int32 GetInt32(int ordinal);
        protected abstract Int64 GetInt64(int ordinal);
        protected abstract String GetString(int ordinal);

        public T ReadValue<T>(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(T); //JVB: throw if T is struct???
            }
            return this.GetValue<T>(ordinal);
        }

        public T? ReadNullableValue<T>(int ordinal) where T : struct
        {
            if (this.IsDBNull(ordinal))
            {
                return default(T?);
            }
            return this.GetValue<T>(ordinal);
        }

        public DateTime ReadDateTime(int ordinal)
        {
            if (this.IsDBNull(ordinal)) //JVB: throw???
            {
                return default(DateTime);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.DateTime:
                        return this.GetDateTime(ordinal);
                    default:
                        return this.GetValue<DateTime>(ordinal);
                }
            }
        }

        public DateTime? ReadNullableDateTime(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(DateTime?);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.DateTime:
                        return this.GetDateTime(ordinal);
                    default:
                        return this.GetValue<DateTime>(ordinal);
                }
            }
        }

        public Decimal ReadDecimal(int ordinal)
        {
            if (this.IsDBNull(ordinal)) //JVB: throw???
            {
                return default(Decimal);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return (Decimal)this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return (Decimal)this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return (Decimal)this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return (Decimal)this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Decimal>(ordinal);
                }
            }
        }

        public Decimal? ReadNullableDecimal(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Decimal?);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return (Decimal)this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return (Decimal)this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return (Decimal)this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return (Decimal)this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Decimal>(ordinal);
                }
            }
        }

        public Double ReadDouble(int ordinal)
        {
            if (this.IsDBNull(ordinal)) //JVB: throw???
            {
                return default(Double);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return (Double)this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return (Double)this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return (Double)this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return (Double)this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Double>(ordinal);
                }
            }
        }

        public Double? ReadNullableDouble(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Double?);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return (Double)this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return (Double)this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return (Double)this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return (Double)this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Double>(ordinal);
                }
            }
        }

        public Guid ReadGuid(int ordinal)
        {
            if (this.IsDBNull(ordinal)) //JVB: throw???
            {
                return default(Guid);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Guid:
                        return this.GetGuid(ordinal);
                    default:
                        return this.GetValue<Guid>(ordinal);
                }
            }
        }

        public Guid? ReadNullableGuid(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Guid?);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Guid:
                        return this.GetGuid(ordinal);
                    default:
                        return this.GetValue<Guid>(ordinal);
                }
            }
        }

        public Int16 ReadInt16(int ordinal)
        {
            if (this.IsDBNull(ordinal)) //JVB: throw???
            {
                return default(Int16);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return (Int16)this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return (Int16)this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return (Int16)this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return (Int16)this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Int16>(ordinal);
                }
            }
        }

        public Int16? ReadNullableInt16(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Int16?);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return (Int16)this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return (Int16)this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return (Int16)this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return (Int16)this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Int16>(ordinal);
                }
            }
        }

        public Int32 ReadInt32(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Int32); //JVB: throw???
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return (Int32)this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return (Int32)this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return (Int32)this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return (Int32)this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Int32>(ordinal);
                }
            }
        }

        public Int32? ReadNullableInt32(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Int32?);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return (Int32)this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return (Int32)this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return (Int32)this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return (Int32)this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Int32>(ordinal);
                }
            }
        }

        public Int64 ReadInt64(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Int64); //JVB: throw???
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                   case TypeCode.Int16:
                        return (Int64)this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return (Int64)this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return (Int64)this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return (Int64)this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Int64>(ordinal);
                }
            }
        }

        public Int64? ReadNullableInt64(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Int64?);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return (Int64)this.GetInt16(ordinal);
                    case TypeCode.Int32:
                        return (Int64)this.GetInt32(ordinal);
                    case TypeCode.Int64:
                        return this.GetInt64(ordinal);
                    case TypeCode.Double:
                        return (Int64)this.GetDouble(ordinal);
                    case TypeCode.Decimal:
                        return (Int64)this.GetDecimal(ordinal);
                    default:
                        return this.GetValue<Int64>(ordinal);
                }
            }
        }

        public String ReadString(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(String);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    case TypeCode.Int16:
                        return ObjectConverter.ToString(this.GetInt16(ordinal));
                    case TypeCode.Int32:
                        return ObjectConverter.ToString(this.GetInt32(ordinal));
                    case TypeCode.Int64:
                        return ObjectConverter.ToString(this.GetInt64(ordinal));
                    case TypeCode.Double:
                        return ObjectConverter.ToString(this.GetDouble(ordinal));
                    case TypeCode.Decimal:
                        return ObjectConverter.ToString(this.GetDecimal(ordinal));
                    case TypeCode.DateTime:
                        return ObjectConverter.ToString(this.GetDateTime(ordinal));
                    case TypeCode.Guid:
                        return ObjectConverter.ToString(this.GetGuid(ordinal));
                    case TypeCode.String:
                        return this.GetString(ordinal);
                    default:
                        return this.GetValue<String>(ordinal);
                }
            }
        }

        public Byte[] ReadByteArray(int ordinal)
        {
            if (this.IsDBNull(ordinal))
            {
                return default(Byte[]);
            }
            while (true)
            {
                switch (typeCodes[ordinal])
                {
                    case TypeCode.Empty:
                        typeCodes[ordinal] = GetTypeCode(ordinal);
                        continue;
                    default:
                        return this.GetValue<Byte[]>(ordinal);
                }
            }
        }

        private TypeCode GetTypeCode(int ordinal)
        {
            Type type = this.GetFieldType(ordinal);
            return TypeHelper.GetTypeCode(type);
        }

        public static MethodInfo GetReaderMethod(Type type)
        {
            if (type == null) throw new ArgumentNullException("type");
            MethodInfo mi;
            _readerMethods.TryGetValue(type, out mi);
            if (mi == null)
            {
                if (TypeHelper.IsNullableType(type))
                {
                    mi = _miReadNullableValue.MakeGenericMethod(TypeHelper.GetNonNullableType(type));
                }
                else
                {
                    mi = _miReadValue.MakeGenericMethod(type);
                }
            }
            return mi;
        }

        static IDictionary<Type, MethodInfo> _readerMethods = InitReaders();
        static MethodInfo _miReadValue = typeof(FieldReader).GetRuntimeMethod("ReadValue", new Type[] { typeof(int) });
        static MethodInfo _miReadNullableValue = typeof(FieldReader).GetRuntimeMethod("ReadNullableValue", new Type[] { typeof(int) });

        private static IDictionary<Type, MethodInfo> InitReaders()
        {
            var meths = typeof(FieldReader).GetRuntimeMethods().Where(m => m.IsPublic && m.Name.StartsWith("Read", StringComparison.Ordinal)).ToList();
            return meths.ToDictionary(m => m.ReturnType);
        }
    }
}
