﻿using System;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Forms
{
    public sealed class LovField : LookupField
    {
        private string _refName;
        public string RefName
        {
            get => _refName;
            set => SetProperty(ref _refName, value);
        }

        private string _key;
        public string Key
        {
            get => _key;
            set => SetProperty(ref _key, value);
        }

        private string _decription;
        public string Description
        {
            get => _decription;
            set => SetProperty(ref _decription, value);
        }

        private IExpressionRunner _expressionRunner;
        public IExpressionRunner ExpressionRunner
        {
            get => _expressionRunner;
            set => SetProperty(ref _expressionRunner, value);
        }

        private RecordData _data;
        private RecordRef _recordRef;

        public RecordData Data => _data;

        public LovField()
        {
            Field bf = new Field();
            BackingField = bf;
        }

        public void UpdateStates(RecordData data)
        {
            RecordRef newRecordRef = data?.GetReference(RefName);

            if (newRecordRef != _recordRef)
            {
                if (_recordRef != null)
                {
                    WeakEventManager.RemoveHandler<RecordRef, EventArgs>(_recordRef, nameof(_recordRef.RecordLoaded), RecordRef_RecordChanged);
                }

                _recordRef = newRecordRef;

                if (_recordRef != null)
                {
                    WeakEventManager.AddHandler<RecordRef, EventArgs>(_recordRef, nameof(_recordRef.RecordLoaded), RecordRef_RecordChanged);
                }
            }

            _data = data;

            UpdateDescription();
        }

        private void RecordRef_RecordChanged(object sender, EventArgs e)
        {
            UpdateDescription();
        }

        private void UpdateDescription()
        {
            string key = _expressionRunner == null ? Key : _expressionRunner.InterpolateString(Key, _data);
            string description = _expressionRunner == null ? Description : _expressionRunner.InterpolateString(Description, _data);
            string fieldValue;
            if (string.IsNullOrWhiteSpace(description) || key == description)
            {
                fieldValue = key;
            }
            else
            {
                fieldValue = string.IsNullOrWhiteSpace(key) ? description : key + " - " + description;
            }
            if (string.IsNullOrEmpty(fieldValue))
            {
                fieldValue = null;
            }
            BackingField.Value = fieldValue;
        }
    }
}
