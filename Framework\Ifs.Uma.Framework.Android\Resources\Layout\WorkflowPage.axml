<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">
  <RelativeLayout
    android:id="@+id/NavToolbar"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@color/IfsDarkBlue"
    android:elevation="8dp"
    app:elevation="8dp">
    <android.support.v7.widget.AppCompatImageView
      android:id="@+id/BackImage"
      android:layout_height="match_parent"
      android:layout_width="20dp"
      android:layout_alignParentLeft="true"
      android:adjustViewBounds="true"
      android:scaleType="fitXY"
      android:tint="@color/IfsBlue"
      app:srcCompat="@drawable/ic_left"/>
    <android.support.v7.widget.AppCompatImageView
      android:id="@+id/NextImage"
      android:layout_height="match_parent"
      android:layout_width="20dp"
      android:layout_alignParentRight="true"
      android:gravity="fill_vertical|left"
      android:adjustViewBounds="true"
      android:scaleType="fitXY"
      android:tint="@color/IfsBlue"
      app:srcCompat="@drawable/ic_right"/>
    <LinearLayout
      android:id="@+id/NavContent"
      android:orientation="horizontal"
      android:layout_height="match_parent"
      android:layout_width="0dp"
      android:layout_toRightOf="@id/BackImage"
      android:layout_toLeftOf="@id/NextImage"
      android:weightSum="1"
      android:background="@color/IfsBlue">
      <Button
        android:id="@+id/NavPrevious"
        android:layout_height="match_parent"
        android:layout_width="0dp"
        android:layout_weight="0.5"
        android:paddingRight="10dp"
        android:gravity="start|center_vertical"
        android:maxLines="2"
        android:ellipsize="end"
        android:textAllCaps="false"
        android:drawablePadding="8dp"
        android:textColor="@android:color/white"
        android:textDirection="locale"
        android:background="@android:color/transparent"/>
      <Button
        android:id="@+id/NavNext"
        android:layout_height="match_parent"
        android:layout_width="0dp"
        android:layout_weight="0.5"
        android:paddingLeft="10dp"
        android:gravity="end|center_vertical"
        android:maxLines="2"
        android:ellipsize="end"
        android:textAllCaps="false"
        android:drawablePadding="8dp"
        android:textColor="@android:color/white"
        android:textDirection="locale"
        android:background="@android:color/transparent"/>
    </LinearLayout>
  </RelativeLayout>
  <LinearLayout
      android:background="@color/IfsWhite"
      android:layout_marginLeft = "6dp"
      android:layout_marginRight = "6dp"      
      android:layout_marginBottom = "2dp"
      android:weightSum="100"
      android:orientation="vertical"
      android:layout_height="wrap_content"
      android:layout_width="match_parent">
      <TextView
        android:layout_marginTop = "2dp"
        android:id="@+id/progressTextView"
        android:gravity="left"
        style="@style/FieldStyle_HeaderTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
      <ProgressBar
          android:layout_marginTop = "-2dp"
          android:layout_weight="25"
          android:id="@+id/progressBar"
          style="?android:attr/progressBarStyleHorizontal"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"/>


  </LinearLayout>
          <View
          android:id="@+id/bottomProgressDivider"
          android:layout_width="match_parent"
          android:layout_height="1dp"
            />
  <fragment xmlns:android="http://schemas.android.com/apk/res/android"
    class="Ifs.Uma.Framework.UI.Pages.MetadataPageFragment"
    android:id="@+id/MetadataPageFragment"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent" />
</LinearLayout>
