{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<Convert_ToString>": {"name": "Convert_ToString", "type": "Function", "params": [{"name": "InputVariable"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Convert", "name": "ToString", "paramsArray": ["${InputVariable}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<Convert_ToString_Timestamp>": {"name": "Convert_ToString", "type": "Function", "params": [{"name": "InputVariable", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Convert", "name": "ToString", "paramsArray": ["${InputVariable}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}