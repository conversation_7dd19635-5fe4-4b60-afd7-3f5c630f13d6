﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  2012-02-02 PKULLK Created.
#endregion

using System.Runtime.Serialization;

namespace Ifs.Cloud.Client.Entities
{
    /// <summary>
    /// Data contract to cloud resource CustomerResource
    /// </summary>
    [DataContract]
    internal class Customer : BaseResource
    {
        public override string ResourceName => "CustomerInfos";

        [DataMember]
        public string ShortName { get; set; }

        [DataMember]
        public string Certificate { get; set; }

        [DataMember]
        public string PublicKey { get; set; }

        [DataMember]
        public string IdentityProvider { get; set; }

        [DataMember]
        public bool? PinAuthentication { get; set; }

        [IgnoreDataMember]
        public CloudStatus CloudError { get; set; }
    }
}
