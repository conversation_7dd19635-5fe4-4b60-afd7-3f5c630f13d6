﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = FwDataContext.FwTablePrefix + "client_keys_map", Class = MetaTableClass.App)]
    public class ClientKeysMap
    {
        [Column(PrimaryKey = true)]
        public string TableName { get; set; }

        [Column(PrimaryKey = true)]
        public string ClientKeys { get; set; }

        [Column]
        public string ServerKeys { get; set; }
    }
}
