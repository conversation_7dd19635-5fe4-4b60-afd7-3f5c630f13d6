﻿using System;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class VarAccessExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.VarAccess;
        public override Type Type => typeof(DynamicValue);

        public string PropertyPath { get; }

        internal VarAccessExpression(string propertyPath)
        {
            if (propertyPath == null) throw new ArgumentNullException(nameof(propertyPath));

            PropertyPath = propertyPath;
        }
        
        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitVarAccessExpression(this);
        }
        
        public override string ToString()
        {
            return PropertyPath;
        }
    }

    public partial class IfsExpression 
    {
        public static VarAccessExpression VarAccess(string propertyPath)
        {
            return new VarAccessExpression(propertyPath);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitVarAccessExpression(VarAccessExpression exp)
        {
            return exp;
        }
    }
}
