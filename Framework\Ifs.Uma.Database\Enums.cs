﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Database
{
    public enum EComparisonMethod
    {
        Equals,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON>han,
        Not<PERSON><PERSON>r<PERSON>han,
        <PERSON><PERSON>ess<PERSON>han,
        NotEqual,
    }

    public enum EOperand
    {
        None,
        And,
        Or,
    }

    public enum ESortOrder
    {
        Ascending,
        Descending,
    }

    public enum ECommandType
    {
        Select,
        SelectDistinct,
        SelectFirst,
        Insert,
        Delete,
        Update,
        DeleteFirst
    }

    public enum EColumnFunction
    {
        None,
        MinFunction,
        MaxFunction,
        CountFunction,
        SumFunction,
        AvgFunction,
        ZeroIfNullFunction
    }

    public enum EJoinType
    {
        Inner,
        LeftOuter,
        RightOuter,
        FullOuter
    }

    public enum EColumnExpression
    {
        IncrementColumn
    }
}
