﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_media_item", Columns = nameof(ItemId), Unique = true)]
    public class MediaItem : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "media_item";

        public MediaItem()
            : base(DbTableName)
        {
            EntitySetName = "MediaItems";
        }

        private long? _itemId;
        [Column(Storage = nameof(_itemId), Mandatory = true, ServerPrimaryKey = true)]
        public long? ItemId
        {
            get => _itemId;
            set => SetProperty(ref _itemId, value);
        }

        private string _name;
        [Column(Storage = nameof(_name), MaxLength = 250)]
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        private string _description;
        [Column(Storage = nameof(_description), MaxLength = 250)]
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        private string _mediaFile;
        [Column(Storage = nameof(_mediaFile))]
        public string MediaFile
        {
            get => _mediaFile;
            set => SetProperty(ref _mediaFile, value);
        }

        private MediaType? _mediaItemType;
        [Column(Storage = nameof(_mediaItemType))]
        public MediaType? MediaItemType
        {
            get => _mediaItemType;
            set => SetProperty(ref _mediaItemType, value);
        }

        private double? _latitude;
        [Column(Storage = nameof(_latitude))]
        public double? Latitude
        {
            get => _latitude;
            set => SetProperty(ref _latitude, value);
        }

        private double? _longitude;
        [Column(Storage = nameof(_longitude))]
        public double? Longitude
        {
            get => _longitude;
            set => SetProperty(ref _longitude, value);
        }
    }
}
