﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Elements.Calendars;
using Ifs.Uma.Framework.UI.Elements.Groups;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Ifs.Uma.Framework.UI.Elements.Maps;
using Ifs.Uma.Framework.UI.Elements.ProcessViewer;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements
{
    public sealed class ElementList : ObservableBase, IDisposable
    {
        public ViewableCollection<ElementBase> Elements { get; } = new ViewableCollection<ElementBase>();

        public UpdatingState UpdatingState { get; } = new UpdatingState();
        public event EventHandler ElementsLoaded;

        private ElementBase _primaryElement;

        /// <summary>
        /// The Primary Element (a list, for example) will either be shown in the bottom 2/3 of
        /// the page, or full screen
        /// </summary>
        public ElementBase PrimaryElement
        {
            get => _primaryElement;
            private set
            {
                SetProperty(ref _primaryElement, value);
            } 
        }

        private bool _hasRepeatingSections;
        public bool HasRepeatingSections
        {
            get { return _hasRepeatingSections; }
            set
            {
                if (_hasRepeatingSections != value)
                {
                    _hasRepeatingSections = value;
                    OnPropertyChanged(nameof(HasRepeatingSections));
                }
            }
        }

        public void SetPrimaryElement(ElementBase element, ElementDisplayState? displayState = null)
        {
            foreach (ElementBase curElement in Elements)
            {
                if (displayState.HasValue && (element == null || curElement == element))
                {
                    curElement.DisplayState = displayState.Value;
                }
                else
                {
                    curElement.DisplayState = ElementDisplayState.Normal;
                }
            }

            PrimaryElement = element;
        }

        private RecordLoaderElement _primaryRecordLoader;
        public RecordLoaderElement PrimaryRecordLoader
        {
            get => _primaryRecordLoader;
            set => SetProperty(ref _primaryRecordLoader, value);
        }

        private PageData _pageData;
        public PageData PageData
        {
            get => _pageData;
            set
            {
                if (_pageData != value)
                {
                    PageData oldValue = _pageData;
                    if (SetProperty(ref _pageData, value))
                    {
                        OnPageDataChanged(oldValue, _pageData);
                    }
                }
            }
        }
        
        private readonly IElementCreator _elementCreator;       

        public ElementList(UpdatingState parentUpdatingState, IElementCreator elementCreator)
        {
            _elementCreator = elementCreator;

            UpdatingState.ParentState = parentUpdatingState;
        }

        public void LoadElements(string projectionName, IReadOnlyList<CpiCommandGroup> commandGroups, IReadOnlyList<CpiElementContent> contents, string stateIndicator)
        {
            using (Elements.DeferRefresh())
            {
                foreach (ElementBase element in Elements)
                {
                    element.PropertyChanged -= Element_PropertyChanged;
                    element.UpdatingState.ParentState = null;
                }

                Elements.Clear();
                PrimaryRecordLoader = null;

                if (contents != null)
                {
                    bool isSingleListPage = contents.IsSingleListPage();
                    bool isSingleMapPage = contents.IsSingleMapPage();
                    bool isSingleGroupPage = contents.IsSingleGroupPage();
                    bool isGroupWithMapPage = contents.IsGroupWithMapPage();

                    // If content has any RepeatingSection, a HTML report is generated.
                    // To show unlimited columns in a HTML table we set IsReport to true
                    // (called in CardDefCreator -> SimplifyListContent)
                    if (PageData != null && contents.HasRepeatingSections())
                    {
                        PageData.IsReport = true;
                    }
                    
                    int row = 0;
                    int column = 0;

                    CreateRootElement(projectionName, isSingleListPage, isSingleMapPage, isGroupWithMapPage, isSingleGroupPage, new CpiElementContent()
                    {
                        StateIndicator = stateIndicator
                    }, ref row, ref column);
        
                    foreach (CpiElementContent cpiContent in contents)
                    {
                        CreateRootElement(projectionName, isSingleListPage, isSingleMapPage, isGroupWithMapPage, isSingleGroupPage, cpiContent, ref row, ref column);
                    }
                }

                AddOfflineWarningElement(projectionName, commandGroups, contents);

                HasRepeatingSections = Elements.Any(x => x is RepeatingSectionElement);
            }

            ElementsLoaded?.Invoke(this, EventArgs.Empty);
        }

        private const int _maximumColumnCount = 2;

        private void CreateRootElement(string projectionName, bool isSingleListPage, bool isSingleMapPage, bool isGroupWithMapPage, bool hideAllHeaders, CpiElementContent content, ref int row, ref int column)
        {                    
            if (content.Arrange != null)
            {
                foreach (CpiElementContent arrangeContent in content.Arrange)
                {
                    if (column == _maximumColumnCount)
                    {
                        row++;
                        column = 0;
                    }

                    CreateElement(projectionName, isSingleListPage, isSingleMapPage, isGroupWithMapPage, hideAllHeaders, arrangeContent, row, column, false, true);
                    column++;
                }
            }
            else
            {
                column = 0;
                CreateElement(projectionName, isSingleListPage, isSingleMapPage, isGroupWithMapPage, hideAllHeaders, content, row, column, true);
            }
            row++;
        }

        private void CreateElement(string projectionName, bool isSingleListPage, bool isSingleMapPage, bool isGroupWithMapPage, bool hideAllHeaders, CpiElementContent content, int row, int column, bool fullWidth, bool hasArrange = false)
        {
            if (content.Arrange != null)
            {
                foreach (CpiElementContent arrangeContent in content.Arrange)
                {
                    CreateElement(projectionName, isSingleListPage, isSingleMapPage, isGroupWithMapPage, hideAllHeaders, arrangeContent, row, column, fullWidth, true);
                }
            }
            else
            {
                ElementBase element = _elementCreator.CreateElement(projectionName, content);
                if (element != null)
                {
                    if (PrimaryRecordLoader == null &&
                        element is RecordLoaderElement recordLoader &&
                        content.Binding?.Property == null)
                    {
                        PrimaryRecordLoader = recordLoader;
                        PageData.DefaultBindingName = content.Selector ?? content.Singleton;
                    }

                    if (element is RepeatingSectionElement repeatSection && PageData.DataSource != null)
                    {
                        fullWidth = true;
                        if (PageData.DataSource.SelectArray(PageData.DefaultViewData.Record.GetRemoteRow(), content.Binding.Property) is ArrayDataSource source)
                        {
                            repeatSection.ArrayDataSource = source;
                        }
                    }

                    element.Elements = this;
                    element.PageData = PageData;
                    Elements.Add(element);
                    element.Location.Row = row;
                    element.Location.Column = column;
                    element.Location.FullWidth = fullWidth;
                    element.HasArrange = hasArrange;

                    if (element is ProcessViewerElement processViewer && PageData.DataSource != null)
                    {
                        processViewer.Array = PageData.DataSource.Metadata.FindArray(PageData.DataSource.ProjectionName, PageData.DataSource.EntityName, content.Binding.Property);
                        processViewer.AddContent(content);
                    }

                    if (hideAllHeaders)
                    {
                        element.HasHeader = false;
                    }

                    if (isSingleListPage && element is ListElement)
                    {
                        SetPrimaryElement(element, ElementDisplayState.Expanded);
                    }

                    if ((isSingleMapPage || isGroupWithMapPage) && element is MapElement)
                    {
                        SetPrimaryElement(element, ElementDisplayState.Expanded);
                    }

                    if (element is CalendarElement)
                    {
                        SetPrimaryElement(element, ElementDisplayState.Expanded);
                    }

                    element.UpdatingState.ParentState = UpdatingState;
                    element.PropertyChanged += Element_PropertyChanged;
                }
            }
        }

        public bool MoveFocusToNextField(Field currentField)
        {
            List<GroupElement> groups = Elements.OfType<GroupElement>().Where(x => x.IsVisible).ToList();
            int startGroupIndex = groups.IndexOf(groups.FirstOrDefault(x => x.Name == currentField.Group.Name));
            int fieldIndex;

            if (startGroupIndex >= 0 && startGroupIndex < groups.Count)
            {
                for (int i = startGroupIndex; i < groups.Count; i++)
                {
                    GroupElement currentGroup = groups[i];

                    if (currentGroup.IsCollapsed)
                    {
                        return true; // There's a group that the user can potentially input into, so stop here
                    }

                    fieldIndex = currentField != null ? currentGroup.Form.Fields.IndexOf(currentField) : -1; // -1 so that we start from the first field in a new group

                    for (int j = fieldIndex; j < currentGroup.Form.Fields.Count; j++)
                    {
                        currentField = currentGroup.Form.Fields.ElementAtOrDefault(j + 1);
                        if (currentField != null && currentField.IsVisible && !currentField.IsReadOnly)
                        {
                            currentField.NotifyFocusRequested();
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        private void AddOfflineWarningElement(string projectionName, IReadOnlyList<CpiCommandGroup> commandGroups, IReadOnlyList<CpiElementContent> contents)
        {
            ElementBase offlineWarningElement = _elementCreator.CreateOfflineWarningElement(projectionName, commandGroups, contents);
            if (offlineWarningElement != null)
            {
                bool afterFirst = Elements.Any() && Elements.FirstOrDefault() == PrimaryRecordLoader;

                offlineWarningElement.Elements = this;
                offlineWarningElement.PageData = PageData;
                offlineWarningElement.Location.Row = afterFirst ? 1 : 0;
                offlineWarningElement.Location.FullWidth = true;

                Elements.Insert(afterFirst ? 1 : 0, offlineWarningElement);

                offlineWarningElement.UpdatingState.ParentState = UpdatingState;
                offlineWarningElement.PropertyChanged += Element_PropertyChanged;
            }
        }

        public void ReloadData()
        {
            foreach (var element in Elements)
            {
                element.ReloadData();
            }
        }

        private void Element_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ElementBase.DisplayState) && sender is ElementBase element)
            {
                SetPrimaryElement(element.DisplayState != ElementDisplayState.FullScreen ? null : element, element.DisplayState);
            }
        }

        public void SetInitialFocus()
        {
            bool initialFocusSet = false;
            Field fieldToFocus = null;

            foreach (ElementBase element in Elements)
            {
                if (initialFocusSet)
                {
                    break;
                }

                if (!(element is GroupElement group) || group.Form == null || !group.IsVisible)
                {
                    continue;
                }

                foreach (Field field in group.Form.AllFields)
                {
                    if (field.HasInitialFocus)
                    {
                        fieldToFocus = field;
                        initialFocusSet = true;
                        break;
                    }
                }
            }

            fieldToFocus?.NotifyFocusRequested();
        }

        private void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            foreach (ElementBase element in Elements)
            {
                element.PageData = newValue;
            }
        }

        public async Task<bool> ValidateAsync()
        {
            if (PageData != null)
            {
                await PageData.WaitForBackgroundTasks();
            }

            ClearValidations();

            bool valid = true;
            foreach (ElementBase element in Elements)
            {
                valid = valid && await element.ValidateAsync();
            }

            return valid;
        }

        public void ClearValidations(ViewData viewData = null)
        {
            foreach (ElementBase element in Elements)
            {
                if (viewData == null || element.ViewData == viewData)
                {
                    element.ClearValidations();
                }
            }
        }

        public bool NotifyFieldInvalid(string fieldName, string message)
        {
            bool success = false;
            foreach (ElementBase element in Elements)
            {
                success = success || element.NotifyFieldInvalid(fieldName, message);
            }
            return success;
        }

        internal void NotifyStoredDataChanged(DataChangeSet changeSet)
        {
            foreach (ElementBase element in Elements)
            {
                element.NotifyStoredDataChanged(changeSet);
            }
        }

        public void GetSelectAttributes(ICollection<string> attributes)
        {
            foreach (ElementBase element in Elements)
            {
                element.GetSelectAttributes(attributes);
            }
        }

        #region IDisposable Support

        public void Dispose()
        {
            foreach (ElementBase element in Elements)
            {
                element.Dispose();
            }

            Elements.Clear();
            ElementsLoaded = null;
            GC.SuppressFinalize(this);
        }

        #endregion
    }
}
