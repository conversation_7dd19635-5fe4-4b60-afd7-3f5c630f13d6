﻿using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.AppData.Permissions;
using Ifs.Uma.AppData.Tests.Execution.Procedures;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests.TestClasses;
using NUnit.Framework;
using Unity;
using Unity.Lifetime;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class EntityDataSourceTests : ProcedureTest
    {
        [Test]
        public async Task FromFunction()
        {
            IMetadata metadata = Resolve<IMetadata>();
            IDataHandler dataHandler = Resolve<IDataHandler>();

            EntityDataSource source = FunctionDataSource.Create(metadata, TestOfflineProjection, "GetCustomers", null);
            EntityQuery query = new EntityQuery(source);

            EntityQueryResult result = await dataHandler.GetRecordsAsync(query, CancellationToken.None);            
            CheckResults(result, "CA", "CB");

            query.Search = new AttributeSearch(new[] { "CustomerNo" }, "B");

            result = await dataHandler.GetRecordsAsync(query, CancellationToken.None);
            CheckResults(result, "CB");
        }

        [Test]
        public async Task FromFunctionWithReference()
        {
            IMetadata metadata = Resolve<IMetadata>();
            IDataHandler dataHandler = Resolve<IDataHandler>();

            EntityDataSource source = FunctionDataSource.Create(metadata, TestOfflineProjection, "GetCustomers", null);
            EntityQuery query = new EntityQuery(source);
            query.SelectAttributes = new[] { "CustomerNo", "CustomerTypeRef.TypeDescription" };

            EntityQueryResult result = await dataHandler.GetRecordsAsync(query, CancellationToken.None);

            CheckResults(result, "CA", "CB");

            EntityRecord first = result.Records[0];
            Assert.IsTrue(first.References?.Count == 1, "Failed to load reference");
            RemoteRow row = first.References["CustomerTypeRef"];
            Assert.IsNotNull(row, "Failed to load reference CustomerTypeRef");
            Assert.AreEqual("Customer Type A", row["TypeDescription"], "Failed to load CustomerTypeRef.TypeDescription");

            EntityRecord second = result.Records[1];
            Assert.IsTrue(second.References?.Count == 1, "Failed to load reference 2");
            RemoteRow row2 = second.References["CustomerTypeRef"];
            Assert.IsNull(row2, "Loaded references when there should be none");
        }

        private void CheckResults(EntityQueryResult result, params string[] ids)
        {
            Assert.IsFalse(result.DataSourceOffline);
            string[] actualIds = result.Records.Select(x => (string)x.Row["CustomerNo"]).ToArray();
            Assert.That(actualIds, Is.EquivalentTo(ids));
        }
        
        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            Container.RegisterType<ITransactionWaiter, AlwaysOfflineTransactionWaiter>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IOnlineDataHandler, AlwaysOfflineOnlineDataHandler>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IAppPermissions, TestAppPermissions>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IDataHandler, DataHandler>(new ContainerControlledLifetimeManager());

            PrepareDatabase<FwDataContext>("EntityDataSourceSchema", "EntityDataSourceData");
        }
    }
}
