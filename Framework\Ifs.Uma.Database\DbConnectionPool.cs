﻿using System;
using System.Linq;
using System.Threading;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public abstract class DbConnectionPool<T> : IDisposable
    {
        protected DbConnectionPool()
        {
            m_pool = new LockedDictionary<string, DbPoolData<T>>();
        }

        private LockedDictionary<string, DbPoolData<T>> m_pool;

        public T GetConnection(string connectionString, Func<string, DbPoolData<T>> factory, bool create, ILogger tracer)
        {
            if (string.IsNullOrEmpty(connectionString)) throw new ArgumentNullException("connectionString");
            if (factory == null) throw new ArgumentNullException("factory");
            LockedDictionary<string, DbPoolData<T>> pool = m_pool;
            if (pool == null) throw new ObjectDisposedException("SQLiteConnectionPool");
            DbPoolData<T> data = pool.GetOrAdd(connectionString, factory);
            return data.GetDb(create, tracer);
        }

        public bool PoolDataExists(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString)) throw new ArgumentNullException("connectionString");
            LockedDictionary<string, DbPoolData<T>> pool = m_pool;
            return pool != null && pool.ContainsKey(connectionString);
        }

        public void ReturnConnection(string connectionString, T db, ILogger tracer)
        {
            if (string.IsNullOrEmpty(connectionString)) throw new ArgumentNullException("connectionString");
            LockedDictionary<string, DbPoolData<T>> pool = m_pool;
            if (pool != null)
            {
                DbPoolData<T> data;
                pool.TryGetValue(connectionString, out data);
                if (data != null)
                {
                    data.ReleaseDb(db, tracer);
                    return;
                }
            }
            // if all else fails just close the connection
            CloseDb(db);
        }

        public void ClearConnection(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString)) throw new ArgumentNullException("connectionString");
            LockedDictionary<string, DbPoolData<T>> pool = m_pool;
            DbPoolData<T> data;
            if (pool != null && pool.TryRemove(connectionString, out data) && data != null)
            {
                data.Dispose();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~DbConnectionPool()
        {
            Dispose(false);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                LockedDictionary<string, DbPoolData<T>> pool = Interlocked.Exchange(ref m_pool, null);
                if (pool != null)
                {
                    foreach (DbPoolData<T> data in pool.Values.Where(x => x != null))
                    {
                        data.Dispose();
                    }
                }
            }
        }

        protected abstract void CloseDb(T value);
    }

    public abstract class DbPoolData<T> : IDisposable
    {
        protected DbPoolData(int maxPoolSize, TimeSpan connectTimeout)
        {
            m_dbs = new LockedStack<T>();
            m_semaphore = new SemaphoreSlim(maxPoolSize);
            m_connectTimeout = connectTimeout;
        }

        public T GetDb(bool create, ILogger tracer)
        {
            SemaphoreSlim s = m_semaphore;
            LockedStack<T> dbs = m_dbs;
            if (s == null || dbs == null) throw new ObjectDisposedException("PoolData");
            if (s.Wait(m_connectTimeout))
            {
                //we're allowed in - is there a free connection? if so that's the one.
                T db;
                if (dbs.TryPop(out db))
                {
                    tracer.Trace(Strings.PoolReuse);
                    return db;
                }
                // if not then open a new one
                string path = GetDbPath();
                if (!string.IsNullOrEmpty(path))
                {
                    tracer.Trace(create ? Strings.PoolCreatingPath :
                        Strings.PoolOpeningPath, path);
                }
                db = OpenDb(create);
                tracer.Trace(create ? Strings.PoolCreate : Strings.PoolOpen);
                return db;
            }
            else
            {
                throw new DbException("Connection Timeout");
            }
        }

        public void ReleaseDb(T db, ILogger tracer)
        {
            SemaphoreSlim s = m_semaphore;
            LockedStack<T> dbs = m_dbs;
            if (dbs != null && s != null)
            {
                // add the pointer back to the pool
                // and release the semaphore
                dbs.Push(db);
                tracer.Trace(Strings.PoolRelease);
                s.Release();
            }
            else
            {
                // just close the connection
                // we can't resuse it
                CloseDb(db);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~DbPoolData()
        {
            Dispose(false);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2213:DisposableFieldsShouldBeDisposed", MessageId = "m_semaphore",
            Justification = "False positive")]
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                UsefulExtensions.ThreadSafeDispose(ref m_semaphore);
            }
            LockedStack<T> dbs = Interlocked.Exchange(ref m_dbs, null);
            if (dbs != null)
            {
                foreach (T db in dbs)
                {
                    CloseDb(db);
                }
            }
        }

        protected abstract T OpenDb(bool create);
        protected abstract void CloseDb(T value);
        protected virtual string GetDbPath() { return null; }

        private LockedStack<T> m_dbs;
        private SemaphoreSlim m_semaphore;
        private TimeSpan m_connectTimeout;
    }
}
