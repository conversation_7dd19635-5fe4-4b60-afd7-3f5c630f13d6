{"name": "FndTstOffline", "component": "FNDTST", "version": "1706901162:1948287535", "projection": {"name": "FndTstOffline", "service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true, "defaultfilter": false}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "syncpolicy": {"type": "ClientCache", "cacheInvalidation": {"interval": "AFTER", "time": "2", "period": "hour"}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}, "TstCustomer2": {"name": "TstCustomer2", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer2", "ludependencies": ["TstCustomer2"], "syncpolicy": {"type": "ClientCache"}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}, "TstCustomerQuery": {"name": "TstCustomerQuery", "CRUD": "Read", "luname": "TstCustomerQuery", "ludependencies": ["TstCustomerQuery", "TstCustomer"], "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "q"}, "select": {"columns": [{"name": "q.Customer<PERSON>o"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}}}}