﻿using System;

namespace Ifs.Uma.AppData.Execution
{
    public class ExecutionException : Exception
    {
        public ExecutionException()
        {
        }

        public ExecutionException(string message)
            : base(message)
        {
        }

        public ExecutionException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}
