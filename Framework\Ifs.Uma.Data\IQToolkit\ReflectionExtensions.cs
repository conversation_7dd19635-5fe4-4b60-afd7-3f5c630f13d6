﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Reflection;

namespace IQToolkit
{
    internal static class ReflectionExtensions
    {
        public static object GetValue(this MemberInfo member, object instance)
        {
            //JVB: Start
            PropertyInfo pInfo = member as PropertyInfo;
            if (pInfo != null)
            {
                return pInfo.GetValue(instance, null);
            }
            FieldInfo fInfo = member as FieldInfo;
            if (fInfo != null)
            {
                return fInfo.GetValue(instance);
            }
            throw new InvalidOperationException();
            //switch (member.MemberType)
            //{
            //    case MemberTypes.Property:
            //        return ((PropertyInfo)member).GetValue(instance, null);
            //    case MemberTypes.Field:
            //        return ((FieldInfo)member).GetValue(instance);
            //    default:
            //        throw new InvalidOperationException();
            //}
            //JVB: End
        }

        public static void SetValue(this MemberInfo member, object instance, object value)
        {
            //JVB: Start
            PropertyInfo pInfo = member as PropertyInfo;
            if (pInfo != null)
            {
                pInfo.SetValue(instance, value, null);
            }
            FieldInfo fInfo = member as FieldInfo;
            if (fInfo != null)
            {
                fInfo.SetValue(instance, value);
            }
            throw new InvalidOperationException();
            //switch (member.MemberType)
            //{
            //    case MemberTypes.Property:
            //        var pi = (PropertyInfo)member;
            //        pi.SetValue(instance, value, null);
            //        break;
            //    case MemberTypes.Field:
            //        var fi = (FieldInfo)member;
            //        fi.SetValue(instance, value);
            //        break;
            //    default:
            //        throw new InvalidOperationException();
            //}
            //JVB: End
        }
    }
}