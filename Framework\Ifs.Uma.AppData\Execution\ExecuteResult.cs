using System;
using Ifs.Uma.AppData.Execution.Procedures;

namespace Ifs.Uma.AppData.Execution
{
    public sealed class ExecuteResult
    {
        public static readonly ExecuteResult Ok = new ExecuteResult("OK");
        public static readonly ExecuteResult Cancel = new ExecuteResult("CANCEL");
        public static readonly ExecuteResult Yes = new ExecuteResult("YES");
        public static readonly ExecuteResult No = new ExecuteResult("NO");
        public static readonly ExecuteResult True = new ExecuteResult("TRUE");
        public static readonly ExecuteResult False = new ExecuteResult("FALSE");
        public static readonly ExecuteResult None = new ExecuteResult(null);
        public static readonly ExecuteResult Offline = new ExecuteResult(null);

        public bool Failed => IsOffline || Exception != null;
        public Exception Exception { get; }
        public object Value { get; }
        public bool Returning { get; }
        public bool IsOffline => this == Offline;
        public bool ShouldBreak { get; }

        public ExecuteResult(object value)
        {
            Value = value;
        }

        public ExecuteResult(object value, bool returning, bool shouldBreak = false)
        {
            Value = value;
            Returning = returning;
            ShouldBreak = shouldBreak;
        }

        public ExecuteResult(Exception exception)
        {
            AggregateException ag = exception as AggregateException;
            if (ag?.InnerException != null)
            {
                exception = ag.InnerException;
            }

            Exception = exception;
        }

        public void CheckFailure()
        {
            if (Failed)
            {
                throw new AggregateException(Exception);
            }
        }

        public bool Is(ExecuteResult result)
        {
            return object.Equals(Value, result.Value);
        }

        public static bool IsUserError(Exception ex)
        {
            if (ex is ProcedureErrorException)
            {
                return true;
            }

            if (ex is AggregateException aex && aex.InnerException != null)
            {
                return IsUserError(aex.InnerException);
            }

            return false;
        }
    }
}
