# IFS HTTP Client Tool

A command line tool for making authenticated HTTP GET requests to IFS Cloud services using TOKEN_DIRECT authentication.

## Features

- Makes HTTP GET requests to any URL
- Supports TOKEN_DIRECT authentication using username/password
- Supports direct access token authentication
- Verbose output for debugging
- Proper IFS Cloud authentication headers

## Usage

### Basic Usage

```bash
IfsHttpClient.exe --url "https://your-server.com/api/endpoint"
```

### With TOKEN_DIRECT Authentication

```bash
IfsHttpClient.exe --url "https://your-server.com/api/endpoint" --username "myuser" --password "mypass" --systemid "MYSYSTEM"
```

### With Direct Access Token

```bash
IfsHttpClient.exe --url "https://your-server.com/api/endpoint" --token "your-access-token"
```

### With Verbose Output

```bash
IfsHttpClient.exe --url "https://your-server.com/api/endpoint" --username "myuser" --password "mypass" --verbose
```

## Command Line Options

| Option | Short | Required | Description |
|--------|-------|----------|-------------|
| `--url` | `-u` | Yes | URL to make GET request to |
| `--server` | `-s` | No | Server URL (for reference) |
| `--systemid` | `-i` | No | System ID for authentication |
| `--username` | `-n` | No | Username for TOKEN_DIRECT authentication |
| `--password` | `-p` | No | Password for TOKEN_DIRECT authentication |
| `--token` | `-t` | No | Direct access token (alternative to username/password) |
| `--verbose` | `-v` | No | Enable verbose output |

## Authentication Methods

### TOKEN_DIRECT
Uses the existing IFS authentication infrastructure with username and password:
- Requires `--username` and `--password`
- Optionally uses `--systemid`
- Generates proper authentication tokens using TouchAppsAuthenticator

### Direct Token
Uses a pre-obtained access token:
- Requires `--token`
- Bypasses username/password authentication
- Useful when you already have a valid token

## Examples

### Test API Endpoint
```bash
IfsHttpClient.exe -u "https://myserver.com/mob/ifsapplications/projection/v1/TestService.svc/TestEndpoint" -n "testuser" -p "testpass" -i "IFSAPP" -v
```

### Use Direct Token
```bash
IfsHttpClient.exe -u "https://myserver.com/api/data" -t "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." -v
```

### Unauthenticated Request
```bash
IfsHttpClient.exe -u "https://httpbin.org/get" -v
```

## Building

To build the tool:

```bash
cd Tools/HttpClient
dotnet build
```

To run directly:

```bash
cd Tools/HttpClient
dotnet run -- --url "https://example.com" --verbose
```

## Dependencies

- .NET 6.0
- CommandLineParser package
- IFS Framework libraries:
  - Ifs.Cloud.Client
  - Ifs.Uma.Comm.TouchApps
  - Ifs.Uma.Utility
  - Ifs.Uma.Framework

## Error Handling

The tool provides appropriate exit codes:
- `0`: Success
- `1`: Error (authentication failure, network error, etc.)

Use `--verbose` flag to get detailed error information and request/response details.
