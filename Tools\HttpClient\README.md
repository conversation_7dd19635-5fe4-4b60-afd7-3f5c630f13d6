# IFS HTTP Client Tool

A command line tool for making authenticated HTTP GET requests to IFS Cloud services using TOKEN_DIRECT authentication.

## Features

- Makes HTTP GET requests to any URL
- Supports TOKEN_DIRECT authentication using username/password
- Supports direct access token authentication
- Verbose output for debugging
- Proper IFS Cloud authentication headers

## Usage

### Basic Usage

```bash
IfsHttpClient.exe --url "https://your-server.com/api/endpoint"
```

### With TOKEN_DIRECT Authentication

```bash
IfsHttpClient.exe --url "https://your-server.com/api/endpoint" --username "myuser" --password "mypass"
```

### With Direct Access Token

```bash
IfsHttpClient.exe --url "https://your-server.com/api/endpoint" --token "your-access-token"
```

### With Verbose Output

```bash
IfsHttpClient.exe --url "https://your-server.com/api/endpoint" --username "myuser" --password "mypass" --verbose
```

## Command Line Options

| Option | Short | Required | Description |
|--------|-------|----------|-------------|
| `--url` | `-u` | Yes | URL to make GET request to |
| `--server` | `-s` | No | Server URL (for reference) |
| `--systemid` | `-i` | No | System ID for authentication |
| `--username` | `-n` | No | Username for TOKEN_DIRECT authentication |
| `--password` | `-p` | No | Password for TOKEN_DIRECT authentication |
| `--token` | `-t` | No | Direct access token (alternative to username/password) |
| `--verbose` | `-v` | No | Enable verbose output |

## Authentication Methods

### TOKEN_DIRECT
Uses the extracted IFS TOKEN_DIRECT authentication logic with username and password:
- Requires `--username` and `--password`
- Creates Basic authentication headers using the same logic as the IFS framework
- Works with any IFS server that supports TOKEN_DIRECT authentication

### Direct Token
Uses a pre-obtained access token:
- Requires `--token`
- Bypasses username/password authentication
- Useful when you already have a valid token

## Examples

### Test API Endpoint
```bash
IfsHttpClient.exe -u "https://myserver.com/mob/ifsapplications/projection/v1/TestService.svc/TestEndpoint" -n "testuser" -p "testpass" -v
```

### Use Direct Token
```bash
IfsHttpClient.exe -u "https://myserver.com/api/data" -t "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." -v
```

### Unauthenticated Request
```bash
IfsHttpClient.exe -u "https://httpbin.org/get" -v
```

## Building

To build the tool:

```bash
cd Tools/HttpClient
dotnet build
```

To run directly:

```bash
cd Tools/HttpClient
dotnet run -- --url "https://example.com" --verbose
```

## Dependencies

- .NET 6.0
- CommandLineParser package (for command line argument parsing)
- System.Text.Json package (for JSON serialization)

**No IFS Framework dependencies** - This tool is completely standalone and extracts the authentication logic without requiring any existing framework libraries.

## Error Handling

The tool provides appropriate exit codes:
- `0`: Success
- `1`: Error (authentication failure, network error, etc.)

Use `--verbose` flag to get detailed error information and request/response details.
