﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.IO;
using Ifs.Uma.Utility;

namespace IQToolkit
{
    /// <summary>
    /// Writes out an expression tree in a C#-ish syntax
    /// </summary>
    internal class ExpressionWriter : ExpressionVisitorEx
    {
        TextWriter writer;
        int indent = 2;
        int depth;

        protected ExpressionWriter(TextWriter writer)
        {
            this.writer = writer;
        }

        public static void Write(TextWriter writer, Expression expression)
        {
            new ExpressionWriter(writer).Visit(expression);
        }

        public static string WriteToString(Expression expression)
        {
            using (StringWriter sw = new StringWriter(System.Globalization.CultureInfo.InvariantCulture))
            {
                Write(sw, expression);
                return sw.ToString();
            }
        }

        protected enum Indentation
        {
            Same,
            Inner,
            Outer
        }

        protected int IndentationWidth
        {
            get { return this.indent; }
            set { this.indent = value; }
        }

        protected void WriteLine(Indentation style)
        {
            this.writer.WriteLine();
            this.Indent(style);
            for (int i = 0, n = this.depth * this.indent; i < n; i++)
            {
                this.writer.Write(" ");
            }
        }

        private static readonly char[] splitters = new char[] { '\n', '\r' };

        protected void Write(string text)
        {
            if (!string.IsNullOrEmpty(text) && text.IndexOf('\n') >= 0)
            {
                string[] lines = text.Split(splitters, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0, n = lines.Length; i < n; i++)
                {
                    this.Write(lines[i]);
                    if (i < n - 1)
                    {
                        this.WriteLine(Indentation.Same);
                    }
                }
            }
            else
            {
                this.writer.Write(text);
            }
        }

        protected void Indent(Indentation style)
        {
            if (style == Indentation.Inner)
            {
                this.depth++;
            }
            else if (style == Indentation.Outer)
            {
                this.depth--;
                System.Diagnostics.Debug.Assert(this.depth >= 0);
            }
        }

        protected virtual string GetOperator(ExpressionType type)
        {
            switch (type)
            {
                case ExpressionType.Not:
                    return "!";
                case ExpressionType.Add:
                case ExpressionType.AddChecked:
                    return "+";
                case ExpressionType.Negate:
                case ExpressionType.NegateChecked:
                case ExpressionType.Subtract:
                case ExpressionType.SubtractChecked:
                    return "-";
                case ExpressionType.Multiply:
                case ExpressionType.MultiplyChecked:
                    return "*";
                case ExpressionType.Divide:
                    return "/";
                case ExpressionType.Modulo:
                    return "%";
                case ExpressionType.And:
                    return "&";
                case ExpressionType.AndAlso:
                    return "&&";
                case ExpressionType.Or:
                    return "|";
                case ExpressionType.OrElse:
                    return "||";
                case ExpressionType.LessThan:
                    return "<";
                case ExpressionType.LessThanOrEqual:
                    return "<=";
                case ExpressionType.GreaterThan:
                    return ">";
                case ExpressionType.GreaterThanOrEqual:
                    return ">=";
                case ExpressionType.Equal:
                    return "==";
                case ExpressionType.NotEqual:
                    return "!=";
                case ExpressionType.Coalesce:
                    return "??";
                case ExpressionType.RightShift:
                    return ">>";
                case ExpressionType.LeftShift:
                    return "<<";
                case ExpressionType.ExclusiveOr:
                    return "^";
                default:
                    return null;
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification="pseudo code")]
        protected override Expression VisitBinary(BinaryExpression node)
        {
            if (node == null) return null;
            switch (node.NodeType)
            {
                case ExpressionType.ArrayIndex:
                    this.Visit(node.Left);
                    this.Write("[");
                    this.Visit(node.Right);
                    this.Write("]");
                    break;
                case ExpressionType.Power:
                    this.Write("POW(");
                    this.Visit(node.Left);
                    this.Write(", ");
                    this.Visit(node.Right);
                    this.Write(")");
                    break;
                default:
                    this.Visit(node.Left);
                    this.Write(" ");
                    this.Write(GetOperator(node.NodeType));
                    this.Write(" ");
                    this.Visit(node.Right);
                    break;
            }
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitUnary(UnaryExpression node)
        {
            if (node == null) return null;
            switch (node.NodeType)
            {
                case ExpressionType.Convert:
                case ExpressionType.ConvertChecked:
                    this.Write("((");
                    this.Write(this.GetTypeName(node.Type));
                    this.Write(")");
                    this.Visit(node.Operand);
                    this.Write(")");
                    break;
                case ExpressionType.ArrayLength:
                    this.Visit(node.Operand);
                    this.Write(".Length");
                    break;
                case ExpressionType.Quote:
                    this.Visit(node.Operand);
                    break;
                case ExpressionType.TypeAs:
                    this.Visit(node.Operand);
                    this.Write(" as ");
                    this.Write(this.GetTypeName(node.Type));
                    break;
                case ExpressionType.UnaryPlus:
                    this.Visit(node.Operand);
                    break;
                default:
                    this.Write(this.GetOperator(node.NodeType));
                    this.Visit(node.Operand);
                    break;
            }
            return node;
        }

        protected virtual string GetTypeName(Type type)
        {
            if (type == null) throw new ArgumentNullException("type");
            string name = type.Name;
            name = name.Replace('+', '.');
            int iGeneneric = name.IndexOf('`');
            if (iGeneneric > 0)
            {
                name = name.Substring(0, iGeneneric);
            }
            TypeInfo info = type.GetTypeInfo();
            if (info.IsGenericType || info.IsGenericTypeDefinition)
            {
                StringBuilder sb = new StringBuilder();
                sb.Append(name);
                sb.Append("<");
                var args = type.GenericTypeArguments;
                for (int i = 0, n = args.Length; i < n; i++)
                {
                    if (i > 0)
                    {
                        sb.Append(",");
                    }
                    if (info.IsGenericType)
                    {
                        sb.Append(this.GetTypeName(args[i]));
                    }
                }
                sb.Append(">");
                name = sb.ToString();
            }
            return name;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitConditional(ConditionalExpression node)
        {
            if (node == null) return null;
            this.Visit(node.Test);
            this.WriteLine(Indentation.Inner);
            this.Write("? ");
            this.Visit(node.IfTrue);
            this.WriteLine(Indentation.Same);
            this.Write(": ");
            this.Visit(node.IfFalse);
            this.Indent(Indentation.Outer);
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override IEnumerable<MemberBinding> VisitBindingList(ReadOnlyCollection<MemberBinding> original)
        {
            if (original != null)
            {
                for (int i = 0, n = original.Count; i < n; i++)
                {
                    this.VisitMemberBinding(original[i]);
                    if (i < n - 1)
                    {
                        this.Write(",");
                        this.WriteLine(Indentation.Same);
                    }
                }
            }
            return original;
        }

        private static readonly char[] special = new char[] { '\r', '\n', '\\' };

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitConstant(ConstantExpression node)
        {
            if (node == null) return null;
            if (node.Value == null) 
            {
                this.Write("null");
            }
            else if (node.Type == typeof(string))
            {
                string value = node.Value.ToString();
                if (value.IndexOfAny(special) >= 0)
                    this.Write("@");
                this.Write("\"");
                this.Write(node.Value.ToString());
                this.Write("\"");
            }
            else if (node.Type == typeof(DateTime))
            {
                this.Write("new DateTime(\"");
                this.Write(node.Value.ToString());
                this.Write("\")");
            }
            else if (node.Type.IsArray)
            {
                Type elementType = node.Type.GetElementType();
                this.VisitNewArray(
                    Expression.NewArrayInit(
                        elementType,
                        ((IEnumerable)node.Value).OfType<object>().Select(v => (Expression)Expression.Constant(v, elementType))
                        ));
            }
            else
            {
                this.Write(node.Value.ToString());
            }
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override ElementInit VisitElementInit(ElementInit node)
        {
            if (node == null) return null;
            if (node.Arguments.Count > 1)
            {
                this.Write("{");
                for (int i = 0, n = node.Arguments.Count; i < n; i++)
                {
                    this.Visit(node.Arguments[i]);
                    if (i < n - 1)
                    {
                        this.Write(", ");
                    }
                }
                this.Write("}");
            }
            else
            {
                this.Visit(node.Arguments[0]);
            }
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override IEnumerable<ElementInit> VisitElementInitList(ReadOnlyCollection<ElementInit> original)
        {
            if (original != null)
            {
                for (int i = 0, n = original.Count; i < n; i++)
                {
                    this.VisitElementInit(original[i]);
                    if (i < n - 1)
                    {
                        this.Write(",");
                        this.WriteLine(Indentation.Same);
                    }
                }
            }
            return original;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override IEnumerable<Expression> VisitExpressionList(ReadOnlyCollection<Expression> original)
        {
            if (original != null)
            {
                for (int i = 0, n = original.Count; i < n; i++)
                {
                    this.Visit(original[i]);
                    if (i < n - 1)
                    {
                        this.Write(",");
                        this.WriteLine(Indentation.Same);
                    }
                }
            }
            return original;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitInvocation(InvocationExpression node)
        {
            if (node == null) return null;
            this.Write("Invoke(");
            this.WriteLine(Indentation.Inner);
            this.VisitExpressionList(node.Arguments);
            this.Write(", ");
            this.WriteLine(Indentation.Same);
            this.Visit(node.Expression);
            this.WriteLine(Indentation.Same);
            this.Write(")");
            this.Indent(Indentation.Outer);
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitLambda<T>(Expression<T> node)
        {
            if (node == null) return null;
            if (node.Parameters.Count != 1)
            {
                this.Write("(");
                for (int i = 0, n = node.Parameters.Count; i < n; i++)
                {
                    this.Write(node.Parameters[i].Name);
                    if (i < n - 1)
                    {
                        this.Write(", ");
                    }
                }
                this.Write(")");
            }
            else
            {
                this.Write(node.Parameters[0].Name);
            }
            this.Write(" => ");
            this.Visit(node.Body);
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitListInit(ListInitExpression node)
        {
            if (node == null) return null;
            this.Visit(node.NewExpression);
            this.Write(" {");
            this.WriteLine(Indentation.Inner);
            this.VisitElementInitList(node.Initializers);
            this.WriteLine(Indentation.Outer);
            this.Write("}");
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitMember(MemberExpression node)
        {
            if (node == null) return null;
            this.Visit(node.Expression);
            this.Write(".");
            this.Write(node.Member.Name);
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override MemberAssignment VisitMemberAssignment(MemberAssignment node)
        {
            if (node == null) return null;
            this.Write(node.Member.Name);
            this.Write(" = ");
            this.Visit(node.Expression);
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitMemberInit(MemberInitExpression node)
        {
            if (node == null) return null;
            this.Visit(node.NewExpression);
            this.Write(" {");
            this.WriteLine(Indentation.Inner);
            this.VisitBindingList(node.Bindings);
            this.WriteLine(Indentation.Outer);
            this.Write("}");
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override MemberListBinding VisitMemberListBinding(MemberListBinding node)
        {
            if (node == null) return null;
            this.Write(node.Member.Name);
            this.Write(" = {");
            this.WriteLine(Indentation.Inner);
            this.VisitElementInitList(node.Initializers);
            this.WriteLine(Indentation.Outer);
            this.Write("}");
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override MemberMemberBinding VisitMemberMemberBinding(MemberMemberBinding node)
        {
            if (node == null) return null;
            this.Write(node.Member.Name);
            this.Write(" = {");
            this.WriteLine(Indentation.Inner);
            this.VisitBindingList(node.Bindings);
            this.WriteLine(Indentation.Outer);
            this.Write("}");
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitMethodCall(MethodCallExpression node)
        {
            if (node == null) return null;
            if (node.Object != null)
            {
                this.Visit(node.Object);
            }
            else
            {
                this.Write(this.GetTypeName(node.Method.DeclaringType));
            }
            this.Write(".");
            this.Write(node.Method.Name);
            this.Write("(");
            if (node.Arguments.Count > 1)
                this.WriteLine(Indentation.Inner);
            this.VisitExpressionList(node.Arguments);
            if (node.Arguments.Count > 1)
                this.WriteLine(Indentation.Outer);
            this.Write(")");
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitNew(NewExpression node)
        {
            if (node == null) return null;
            this.Write("new ");
            this.Write(this.GetTypeName(node.Constructor.DeclaringType));
            this.Write("(");
            if (node.Arguments.Count > 1)
                this.WriteLine(Indentation.Inner);
            this.VisitExpressionList(node.Arguments);
            if (node.Arguments.Count > 1)
                this.WriteLine(Indentation.Outer);
            this.Write(")");
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitNewArray(NewArrayExpression node)
        {
            if (node == null) return null;
            this.Write("new ");
            this.Write(this.GetTypeName(TypeHelper.GetElementType(node.Type)));
            this.Write("[] {");
            if (node.Expressions.Count > 1)
                this.WriteLine(Indentation.Inner);
            this.VisitExpressionList(node.Expressions);
            if (node.Expressions.Count > 1)
                this.WriteLine(Indentation.Outer);
            this.Write("}");
            return node;
        }

        protected override Expression VisitParameter(ParameterExpression node)
        {
            if (node == null) return null;
            this.Write(node.Name);
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo code")]
        protected override Expression VisitTypeBinary(TypeBinaryExpression node)
        {
            if (node == null) return null;
            this.Visit(node.Expression);
            this.Write(" is ");
            this.Write(this.GetTypeName(node.TypeOperand));
            return node;
        }
    }
}