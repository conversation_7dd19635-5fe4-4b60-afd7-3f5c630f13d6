﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions
{
    internal abstract class FwFunction
    {
        public string Namespace { get; }
        public string Name { get; }
        public int ParametersCount { get; }

        public FwFunction(string ns, string name, int parametersCount)
        {
            if (string.IsNullOrEmpty(ns)) throw new ArgumentNullException(nameof(ns));
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));

            Namespace = ns;
            Name = name;
            ParametersCount = parametersCount;
        }
        
        public ExecuteResult Execute(ProcedureContext context, object[] parameters)
        {
            if (ParametersCount >= 0)
            {
                int count = parameters?.Length ?? 0;
                if (count != ParametersCount)
                {
                    throw context.Fail($"Invalid parameters in procedure call to '{Namespace}.{Name}'");
                }
            }
            
            FuncParam[] funcParams = new FuncParam[parameters?.Length ?? 0];
            if (parameters != null)
            {
                for (int i = 0; i < parameters.Length; i++)
                {
                    funcParams[i] = new FuncParam(context, parameters[i]);
                }
            }

            object result = OnExecute(context, funcParams);

            return new ExecuteResult(result);
        }

        protected abstract object OnExecute(ProcedureContext context, FuncParam[] parameters);
    }
}
