﻿using System;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    // Legacy function but may still be in use
    internal sealed class SystemDateTime : SystemFunction
    {
        public const string FunctionName = "DateTime";

        private readonly ILogger _logger;

        public SystemDateTime(ILogger logger)
            : base(FunctionName, 0)
        {
            _logger = logger;
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            _logger.Warning("System.DateTime is deprecated use DateTime.Timestamp instead");
            return DateTime.Now;
        }
    }
}
