{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"name": "FndTstOffline", "service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<Attachment_CreateAndConnectMedia>": {"name": "Attachment_CreateAndConnectMedia", "type": "Function", "params": [{"name": "Lu<PERSON>ame", "dataType": "Text"}, {"name": "KeyRef", "dataType": "Text"}, {"name": "Name", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subdataType": "FndMediaKeys"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Attachment", "name": "CreateAndConnectMedia", "paramsArray": ["${LuName}", "${KeyRef}", "${Name}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<Attachment_ConnectMedia>": {"name": "Attachment_ConnectMedia", "type": "Function", "params": [{"name": "MediaKeys", "dataType": "FndMediaKeys"}, {"name": "ToLuName", "dataType": "Text"}, {"name": "ToKeyRef", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subdataType": "FndMediaKeys"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Attachment", "name": "ConnectMedia", "paramsArray": ["${MediaKeys}", "${ToLuName}", "${ToKeyRef}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<Attachment_CreateAndConnectDoc>": {"name": "Attachment_CreateAndConnectDoc", "type": "Function", "params": [{"name": "Lu<PERSON>ame", "dataType": "Text"}, {"name": "KeyRef", "dataType": "Text"}, {"name": "Title", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subdataType": "FndDocumentKeys"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Attachment", "name": "CreateAndConnectDoc", "paramsArray": ["${LuName}", "${KeyRef}", "${Title}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<Attachment_ConnectDoc>": {"name": "Attachment_ConnectDoc", "type": "Function", "params": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "dataType": "FndDocumentKeys"}, {"name": "ToLuName", "dataType": "Text"}, {"name": "ToKeyRef", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Structure", "subdataType": "FndDocumentKeys"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Attachment", "name": "ConnectDocument", "paramsArray": ["${Doc<PERSON><PERSON><PERSON>}", "${ToLuName}", "${ToKeyRef}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<Attachment_GetMediaFileExtensions>": {"name": "Attachment_GetMediaFileExtensions", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Attachment", "name": "GetMediaFileExtensions", "paramsArray": []}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<Attachment_GetDocFileExtensions>": {"name": "Attachment_GetDocFileExtensions", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Attachment", "name": "GetDocFileExtensions", "paramsArray": []}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}