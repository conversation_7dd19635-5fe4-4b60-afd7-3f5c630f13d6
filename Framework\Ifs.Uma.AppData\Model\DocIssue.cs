﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_doc_issue", Unique = true, Columns = 
            nameof(DocClass) + "," +
            nameof(DocNo) + "," +            
            nameof(DocSheet) + "," +
            nameof(DocRev) 
        )]
    public class DocIssue : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "doc_issue";

        #region Field Definitions

        private string _docClass;
        private string _docNo;
        private string _docSheet;
        private string _docRev;        
        private string _title;
        private string _oldDocSheet;
        private string _oldDocRev;

        #endregion

        public DocIssue()
            : base(DbTableName)
        {
            EntitySetName = "DocIssues";
        }

        #region Property Definitions

        // The order of ServerPrimaryKey columns listed here must match the server
        // for client primary keys to work

        [Column(Storage = nameof(_docClass), Mandatory = true, ServerPrimaryKey = true)]
        public string DocClass
        {
            get => _docClass;
            set => SetProperty(ref _docClass, value);
        }

        [Column(Storage = nameof(_docNo), Mandatory = true, ServerPrimaryKey = true)]
        public string DocNo
        {
            get => _docNo;
            set => SetProperty(ref _docNo, value);
        }

        [Column(Storage = nameof(_docSheet), Mandatory = true, ServerPrimaryKey = true)]
        public string DocSheet
        {
            get => _docSheet;
            set => SetProperty(ref _docSheet, value);
        }

        [Column(Storage = nameof(_docRev), Mandatory = true, ServerPrimaryKey = true)]
        public string DocRev
        {
            get => _docRev;
            set => SetProperty(ref _docRev, value);
        }

        [Column(Storage = nameof(_title))]
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        [Column(Storage = nameof(_oldDocSheet))]
        public string OldDocSheet
        {
            get => _oldDocSheet;
            set => SetProperty(ref _oldDocSheet, value);
        }

        [Column(Storage = nameof(_oldDocRev))]
        public string OldDocRev
        {
            get => _oldDocRev;
            set => SetProperty(ref _oldDocRev, value);
        }

        #endregion
    }
}
