﻿using System;
using System.Linq;
using System.Net;
using System.Runtime.Serialization;
using Ifs.Uma.Utility;
using System.Net.Http;
using System.Collections.Generic;
using System.Runtime.Serialization.Json;
using System.Threading.Tasks;
using System.IO;
using System.Text;

namespace Ifs.Cloud.Client.Comm
{
    public abstract class BaseCloudAuthenticator
    {
        /// <summary>
        /// Gives the certificationString returned within ICloudAuthenticator.GetPublicKey()
        /// </summary>
        private string _certificationString = string.Empty;

        private string _tokenEncryptionKey = string.Empty;

        private Interfaces.ICloudAuthenticator _authenticatorPartialImplementor;

        public string IdentityProviderInformation { get; private set; }
        public bool? PinAuthentication { get; private set; }
        public string UserName { get; private set; }

        /// <summary>
        /// Gets the internal ICloudAuthenticator implementation.
        /// Do NOT use this from within GetAuthenticationTokenImpl()
        /// </summary>
        public Interfaces.ICloudAuthenticator Authenticator
        {
            get
            {
                if (_authenticatorPartialImplementor != null)
                {
                    return _authenticatorPartialImplementor;
                }
                else
                {
                    _authenticatorPartialImplementor = new CloudAuthenticatorImpl(this);
                    return _authenticatorPartialImplementor;
                }
            }
        }

        /// <summary>
        /// Called from the internal ICloudAuthenticator implementation(see property Authenticator) in BaseCloudAuthenticator
        /// </summary>
        /// <param name="systemId">The system Id</param>
        /// <param name="userName">The User Id</param>
        /// <param name="password">Password</param>
        /// <param name="publicKey">Public Key retrieved during GetPublicKey() call from the internal ICloudAuthenticator implementation</param>
        /// <param name="certString">Certification in Base64 retrieved during GetPublickKey() call from the internal ICloudAuthenticator implementation</param>
        /// <returns></returns>
        protected abstract string GetAuthenticationTokenImpl(string systemId, string userName, string password, string publicKey, string certString);

        /// <summary>
        /// Called from the internal ICloudAuthenticator implementation(see property Authenticator) in BaseCloudAuthenticator
        /// </summary>
        /// <param name="accessToken">Access Token from the identity provider</param>
        /// <param name="certificationString">Certification in Base64 retrieved during GetPublickKey() call from the internal ICloudAuthenticator implementation</param>
        /// <param name="key">out paramter - key that the id token is encrypted with</param>
        /// <returns></returns>
        protected abstract string GetAuthenticationAccessTokenAndKeyImpl(string accessToken, string publicKey, string certificationString, out string key);

        private class CloudAuthenticatorImpl : Interfaces.ICloudAuthenticator
        {
            private readonly BaseCloudAuthenticator _baseInstanceImplemented;

            internal CloudAuthenticatorImpl(BaseCloudAuthenticator instance)
            {
                _baseInstanceImplemented = instance;
            }
            public string GetIdentityProviderInformation()
            {
                return _baseInstanceImplemented.IdentityProviderInformation;
            }
            public bool? GetPinAuthentication()
            {
                return _baseInstanceImplemented.PinAuthentication;
            }
            public string GetUserName()
            {
                return _baseInstanceImplemented.UserName;
            }
            public string GetKey()
            {
                return _baseInstanceImplemented._tokenEncryptionKey;
            }
            public string GetAuthenticationToken(string systemId, string userName, string password, string publicKey)
            {
                return _baseInstanceImplemented.GetAuthenticationTokenImpl(systemId, userName, password, publicKey, _baseInstanceImplemented._certificationString);
            }

            public string GetSessionID(ContextProvider ctx, string authToken)
            {
                string sessionId = string.Empty;
                try
                {
                    return "Basic " + authToken;
                }
                catch (SerializationException)
                {
                    // Not every response will have a cloud status, so we can't deserialize those responses.
                    // Do nothing.
                }
                finally
                {
                    //TODO: dispose of this properly at some point, but for a workaround dispose manually here
                    //response.Dispose();
                }

                return sessionId;
            }
            public async Task LogoutFromIDP(IdentityProvider identityProvider)
            {
                string uri = identityProvider.LogoutEndpoint;
                var values = new Dictionary<string, string>();
                values.Add("client_id", identityProvider.ClientId);
                values.Add("refresh_token", identityProvider.RefreshToken);
                var content = new FormUrlEncodedContent(values);

                // GetHttpHandler will only return a handler on Android (AndroidClientHandler).
                // Other platforms will use the default HttpClientHandler.
                // This resolves bug TMFW-1317 (Android SSL Hanging) and highlights that there is a bug in the default System.Net.Http.HttpClientHandler that results in an empty body begin returned with a successful status
                // A bug has been submitted to Xamarin: https://bugzilla.xamarin.com/show_bug.cgi?id=51182
                HttpClientHandler handler = new HttpClientHandler();
                handler.AllowAutoRedirect = false;

                using (var client = LoggerManager.SecondaryLoggingEnabled ? new HttpClient(new HttpLoggingHandler(handler)) : new HttpClient(handler))
                {
                    try
                    {
                        var httpResponseMessage = await client.PostAsync(uri, content);
                    }
                    catch (Exception)
                    {
                        // Do nothing.
                    }
                }
            }

            public async Task<string> GetKeyCloakInfo(string uri)
            {
                // GetHttpHandler will only return a handler on Android (AndroidClientHandler).
                // Other platforms will use the default HttpClientHandler.
                // This resolves bug TMFW-1317 (Android SSL Hanging) and highlights that there is a bug in the default System.Net.Http.HttpClientHandler that results in an empty body begin returned with a successful status
                // A bug has been submitted to Xamarin: https://bugzilla.xamarin.com/show_bug.cgi?id=51182
                HttpClientHandler handler = new HttpClientHandler();
                handler.AllowAutoRedirect = false;
                string content = null;

                using (var client = LoggerManager.SecondaryLoggingEnabled ? new HttpClient(new HttpLoggingHandler(handler)) : new HttpClient(handler))
                {
                    try
                    {
                        HttpRequestMessage message = new HttpRequestMessage(HttpMethod.Get, uri);
                        //TO-DO:this check needs to be removed in the server, we wont need this in the client after the server change
                        message.Headers.TryAddWithoutValidation("user-agent", "test");
                        HttpResponseMessage httpResponseMessage = await client.SendAsync(message);
                        IEnumerable<string> values;
                        string ipiString = string.Empty;

                        if (httpResponseMessage.Headers.TryGetValues("WWW-Authenticate", out values))
                        {
                            ipiString = values.First();
                        }

                        if (!string.IsNullOrEmpty(ipiString))
                        {
                            string[] ipiValues = ipiString.Split(' ');
                            string bearerRealm = ipiValues[1].Substring(7, ipiValues[1].Length - 8);
                            //TO-DO: server will send the correct uri, need to be changed after that
                            string[] bearerRealmValues = bearerRealm.Split('@');
                            string uri2 = "https://" + bearerRealmValues[1] + ".well-known/openid-configuration";
                            HttpRequestMessage message2 = new HttpRequestMessage(HttpMethod.Get, uri2);

                            HttpResponseMessage httpResponseMessage2 = await client.SendAsync(message2);
                            Stream receiveStream = await httpResponseMessage2.Content.ReadAsStreamAsync();
                            StreamReader readStream = new StreamReader(receiveStream, Encoding.UTF8);
                            content = readStream.ReadToEnd();
                        }
                    }
                    catch (Exception)
                    {
                        // Do nothing.
                    }
                }
                return content;
            }

            public const int CallTimeoutSeconds = 60 * 10;
            public const int ResponseBufferMaxSize = 900000000;

            //Refesh flow yet to be completed, waiting for server changes
            public async Task<TokenResponseInfo> RefreshAccessToken(IdentityProvider identityProvider)
            {
                // in MOBOFF-12867 we identified that ClientId was not passed in if the app was closed and restarted, so ensure it is non-null
                System.Diagnostics.Debug.Assert(identityProvider.ClientId != null, "IdentityProvider.ClientId cannot be null!");

                string uri = identityProvider.TokenEndpoint;
                var values = new Dictionary<string, string>();
                values.Add("client_id", identityProvider.ClientId);
                values.Add("refresh_token", identityProvider.RefreshToken);
                values.Add("grant_type", "refresh_token");
                values.Add("scope", "openid");
                var content = new FormUrlEncodedContent(values);
                TokenResponseInfo tokenResponse = new TokenResponseInfo();
                if (string.IsNullOrEmpty(identityProvider.RefreshToken))
                {
                    return tokenResponse;
                }
                // GetHttpHandler will only return a handler on Android (AndroidClientHandler).
                // Other platforms will use the default HttpClientHandler.
                // This resolves bug TMFW-1317 (Android SSL Hanging) and highlights that there is a bug in the default System.Net.Http.HttpClientHandler that results in an empty body begin returned with a successful status
                // A bug has been submitted to Xamarin: https://bugzilla.xamarin.com/show_bug.cgi?id=51182
                HttpClientHandler handler = new HttpClientHandler();
                handler.AllowAutoRedirect = false;

                using (var client = LoggerManager.SecondaryLoggingEnabled ? new HttpClient(new HttpLoggingHandler(handler)) : new HttpClient(handler))
                {
                    try
                    {
                        var httpResponseMessage = await client.PostAsync(uri, content);
                        if (httpResponseMessage.StatusCode == HttpStatusCode.OK)
                        {
                            var responseStream = await httpResponseMessage.Content.ReadAsStreamAsync();
                            DataContractJsonSerializerSettings settings =
                                    new DataContractJsonSerializerSettings();
                            settings.UseSimpleDictionaryFormat = true;

                            DataContractJsonSerializer serializer =
                                    new DataContractJsonSerializer(typeof(TokenResponseInfo), settings);

                            tokenResponse = (TokenResponseInfo)serializer.ReadObject(responseStream);
                        }
                        if (httpResponseMessage.StatusCode == HttpStatusCode.ServiceUnavailable || httpResponseMessage.StatusCode == HttpStatusCode.InternalServerError)
                        {
                            httpResponseMessage.EnsureSuccessStatusCode();
                        }
                    }
                    catch (HttpRequestException)
                    {
                        throw;
                    }
                    catch (Exception e)
                    {
                        string t = e.Data.ToString();
                    }
                }
                return tokenResponse;
            }
        }
    }
}
