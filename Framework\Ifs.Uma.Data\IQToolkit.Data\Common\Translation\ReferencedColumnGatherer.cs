﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Gathers all columns referenced by the given expression
    /// </summary>
    internal class ReferencedColumnGatherer : DbExpressionVisitor
    {
        HashSet<ColumnExpression> columns = new HashSet<ColumnExpression>();
        bool first = true;

        public static HashSet<ColumnExpression> Gather(Expression expression)
        {
            var visitor = new ReferencedColumnGatherer();
            visitor.Visit(expression);
            return visitor.columns;
        }

        protected override Expression VisitColumn(ColumnExpression node)
        {
            this.columns.Add(node);
            return node;
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            if (first)
            {
                first = false;
                return base.VisitSelect(node);
            }
            return node;
        }
    }
}