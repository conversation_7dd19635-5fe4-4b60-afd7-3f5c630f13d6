﻿using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Address
{
    public sealed class AddressHandler : DataAccessor<FwDataContext>, IAddressHandler
    {
        private AddressPresentation[] _cached;

        public AddressHandler(IDatabaseController db, ILogger logger, IPerfLogger perfLogger)
            : base(db, logger, perfLogger)
        {
        }

        public async Task<AddressPresentation[]> GetAllAddressPresentations()
        {
            if (_cached != null)
            {
                return _cached;
            }

            return await WithDataContextAsync(ctx =>
            {
                AddressPresentation[] presentations = ctx.AddressPresentations.ToArray();
                _cached = presentations;
                return presentations;
            });
        }
    }
}
