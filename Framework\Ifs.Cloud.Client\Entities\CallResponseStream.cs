﻿using System;
using System.IO;

namespace Ifs.Cloud.Client.Entities
{
    public sealed class CallResponseStream : IDisposable
    {
        public Stream Stream { get; private set; }
        private IDisposable _callResponse;

        public CallResponseStream(Stream stream, IDisposable callResponse)
        {
            Stream = stream;
            _callResponse = callResponse;
        }

        #region IDisposable

        public void Dispose()
        {
            if (Stream != null)
            {
                Stream.Dispose();
                Stream = null;
            }

            if (_callResponse != null)
            {
                _callResponse.Dispose();
                _callResponse = null;
            }
        }

        #endregion
    }
}
