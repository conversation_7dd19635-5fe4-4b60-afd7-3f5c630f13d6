﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Permissions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.AppData.Attachments.Documents
{
    public sealed class DocumentHandler : DataAccessor<FwDataContext>, IDocumentHandler
    {
        public const string DocReferenceObjectProvider = "DocReferenceObject";
        public const string MobileAttachmentsProjection = "MobileAttachments";

        private static readonly string[] DocumentsReadEntities = new[] { "DocIssue", "DocReferenceObject", "EdmApplication", "EdmFile", "MobileDocClass" };
        private static readonly string[] DocumentsWriteEntities = new[] { "DocIssue", "DocReferenceObject", "EdmFile" };

        private readonly ConcurrentDictionary<string, MobileObjectConnectionConfig> _configs = new ConcurrentDictionary<string, MobileObjectConnectionConfig>();
        private readonly IMetaModel _model;
        private readonly ILocalStorage _localStorage;
        private readonly IEventAggregator _eventAggregator;
        private readonly IOnlineAttachmentHandler _attachmentHandler;
        private readonly ILogger _logger;

        private readonly int _dbId;
        private readonly bool _documentsReadAllowed;
        private readonly bool _documentsWriteAllowed;

        public DocumentHandler(IDatabaseController db, ILocalStorage localStorage,
            IEventAggregator eventAggregator, ILogger logger, IPerfLogger perfLogger,
            IOnlineAttachmentHandler attachmentHandler, IAppPermissions permissions)
            : base(db, logger, perfLogger)
        {
            _model = db.GetMetaModel();
            _localStorage = localStorage;
            _eventAggregator = eventAggregator;
            _dbId = db.ConnectedDatabaseId;
            _attachmentHandler = attachmentHandler;
            _logger = logger;
            _documentsReadAllowed = permissions.IsEntityReadGranted(MobileAttachmentsProjection, DocumentsReadEntities);
            _documentsWriteAllowed = permissions.IsEntityWriteGranted(MobileAttachmentsProjection, DocumentsWriteEntities);
        }

        public async Task<ILocalFileInfo> GetLocalFileForDocumentAsync(EdmFile edmFile)
        {
            string fileName = await WithDataContextAsync(ctx => GetFileName(ctx, edmFile), null);
            string filePath = Path.Combine(AttachmentFolders.GetDocumentsFolderPath(_dbId), fileName);
            return await _localStorage.PrivateStorage.GetFileInfoAsync(filePath);
        }

        private static string GetFileName(FwDataContext ctx, EdmFile edmFile)
        {
            // Must always ignore File Name / Path on the row - it may come from the server or
            // another device making it useless. Just always use a predictable location
            // Naming = 'OHS-1060310-1-3-1' = 'DocClass-DocNo-DocSheet-DocRev-FileNo'
            // edmFile.FileType should be given priority when determining the local file extension
            string extension = null;
            if (edmFile.FileType != null )
            {
                EdmApplication edm = ctx.EdmApplications.FirstOrDefault(x => x.FileType == edmFile.FileType);
                if (edm != null)
                {
                    extension = edm.FileExtention?.ToUpperInvariant();
                }
            }

            if (extension == null && edmFile.FileName.Contains("."))
            {
                extension = Path.GetExtension(edmFile.FileName)?.ToUpperInvariant().TrimStart('.');
            }

            string docNo = TidyNegativeId(edmFile.DocNo);
            string fileName = CleanUpFileName($"{edmFile.DocClass}-{docNo}-{edmFile.DocSheet}-{edmFile.DocRev}-{edmFile.FileNo}.{extension}");
            return fileName;
        }

        public async Task CleanupOldDocuments()
        {
            HashSet<string> fileNames = new HashSet<string>();

            await WithDataContextAsync(ctx =>
            {
                CleanupDocReferenceObjects(ctx);
                CleanupEdmFiles(ctx);

                // Use ToBatch here so we do not lock the rest of the app up
                // if there are a lot of documents on the device
                foreach (EdmFile edmFile in ctx.EdmFiles.ToBatch())
                {
                    string fileName = GetFileName(ctx, edmFile);
                    fileNames.Add(fileName);
                }
            });

            string docFolderPath = AttachmentFolders.GetDocumentsFolderPath(_dbId);
            ILocalFolderInfo docFolder = await _localStorage.PrivateStorage.GetFolderInfoAsync(docFolderPath);

            List<ILocalFileInfo> filesToDelete = new List<ILocalFileInfo>();
            foreach (ILocalFileInfo file in await docFolder.GetFilesAsync())
            {
                string fileName = Path.GetFileName(file.FilePath);
                if (!fileNames.Contains(fileName))
                {
                    filesToDelete.Add(file);
                }
            }

            if (filesToDelete.Count > 0)
            {
                _logger.Trace("CleanupOldDocuments: Removing {0} local files", filesToDelete.Count.ToString());

                foreach (ILocalFileInfo file in filesToDelete)
                {
                    try
                    {
                        await file.DeleteAsync();
                    }
                    catch (Exception)
                    {
                        // ignored
                    }
                }
            }
        }

        private void CleanupDocReferenceObjects(FwDataContext ctx)
        {
            try
            {
                string[] entities = ctx.ObjectConnectionConfigs
                    .Where(x => x.ProviderName == DocReferenceObjectProvider)
                    .Select(x => x.Entity)
                    .ToArray();

                List<DocReferenceObject> docRefsToRemove = new List<DocReferenceObject>();
                foreach (DocReferenceObject docRef in ctx.DocReferenceObjects.Where(x => entities.Contains(x.LuName)).ToBatch())
                {
                    ObjPrimaryKey pk = ObjPrimaryKey.FromKeyRef(_model, docRef.LuName, docRef.KeyRef);
                    if (pk != null && !ctx.RecordExists(pk))
                    {
                        docRefsToRemove.Add(docRef);
                    }
                }

                if (docRefsToRemove.Count > 0)
                {
                    _logger.Trace("CleanupOldDocuments: Removing {0} DocReferenceObject records", docRefsToRemove.Count.ToString());
                    ctx.DocReferenceObjects.DeleteAllOnSubmit(MobileAttachmentsProjection, docRefsToRemove);
                    ctx.SubmitChanges(false);
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private void CleanupEdmFiles(FwDataContext ctx)
        {
            try
            {
                EdmFile[] edmFilesToRemove = (
                        from edmFile in ctx.EdmFiles
                        join docRef in ctx.DocReferenceObjects on
                            new { edmFile.DocClass, edmFile.DocNo, edmFile.DocSheet, edmFile.DocRev } equals
                            new { docRef.DocClass, docRef.DocNo, docRef.DocSheet, docRef.DocRev }
                        into docRefs
                        where
                            edmFile.AttachmentStatus != AttachmentStatus.RequiresUpload &&
                            edmFile.AttachmentStatus != AttachmentStatus.Uploading &&
                            edmFile.AttachmentStatus != AttachmentStatus.UploadFailed &&
                            !docRefs.Any()
                        select edmFile
                        ).ToBatch().ToArray();

                if (edmFilesToRemove.Length > 0)
                {
                    _logger.Trace("CleanupOldDocuments: Removing {0} EmdFile records", edmFilesToRemove.Length.ToString());
                    ctx.EdmFiles.DeleteAllOnSubmit(MobileAttachmentsProjection, edmFilesToRemove);
                    ctx.SubmitChanges(false);
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private static string TidyNegativeId(string id)
        {
            if (!string.IsNullOrEmpty(id) &&
                id.ToCharArray().All(x => char.IsDigit(x) || x == '-') &&
                id[0] == '-')
            {
                return "N" + id.TrimStart('-');
            }

            return id;
        }

        private static string CleanUpFileName(string fileName)
        {
            string regexSearch = new string(Path.GetInvalidFileNameChars());
            Regex r = new Regex(string.Format("[{0}]", Regex.Escape(regexSearch)), RegexOptions.None, TimeSpan.FromSeconds(10));
            fileName = r.Replace(fileName, "-");
            return fileName;
        }

        private async Task<MobileObjectConnectionConfig> GetConfigAsync(string entityName)
        {
            if (_configs.TryGetValue(entityName, out MobileObjectConnectionConfig cachedConfig))
            {
                return cachedConfig;
            }

            return await WithDataContextAsync(ctx =>
            {
                MobileObjectConnectionConfig config = ctx.ObjectConnectionConfigs.FirstOrDefault(x => x.Entity == entityName && x.ProviderName == DocReferenceObjectProvider);
                _configs[entityName] = config;
                return config;
            });
        }

        public async Task<MobileDocClass[]> GetSelectableDocumentClassesAsync()
        {
            return await WithDataContextAsync(ctx =>
            {
                return ctx.MobileDocClasses.Where(x => x.NewInMobile == FndBoolean.True).ToArray();
            });
        }

        public async Task<MobileDocClass> GetDocumentClassAsync(string docClass)
        {
            return await WithDataContextAsync(ctx =>
            {
                return ctx.MobileDocClasses.FirstOrDefault(x => x.DocClass == docClass);
            });
        }

        public async Task<MobileDocClass> GetDefaultDocumentClassAsync()
        {
            return await WithDataContextAsync(ctx =>
            {
                return ctx.MobileDocClasses.FirstOrDefault(x => x.MobileDefault == FndBoolean.True);
            });
        }

        private static IQueryable<DocRevisionInfo> GetDocumentsQuery(FwDataContext ctx)
        {
            return from doc in ctx.DocReferenceObjects
                   join docClass in ctx.MobileDocClasses on doc.DocClass equals docClass.DocClass
                   join edmFile in ctx.EdmFiles on
                       new { doc.DocClass, doc.DocNo, doc.DocSheet, doc.DocRev } equals
                       new { edmFile.DocClass, edmFile.DocNo, edmFile.DocSheet, edmFile.DocRev } into edmFiles
                   where edmFiles.Any()
                   orderby doc.Title
                   select new DocRevisionInfo
                   {
                       DocumentRevision = doc,
                       DocumentClass = docClass
                   };
        }

        public async Task<IEnumerable<DocRevisionInfo>> GetDocumentInfosAsync(string entityName, string keyRef)
        {
            await _attachmentHandler.DownloadAttachmentsIfNeeded(entityName, keyRef, false, CancellationToken.None);

            return await WithDataContextAsync(ctx =>
            {
                IQueryable<DocRevisionInfo> documents = GetDocumentsQuery(ctx, entityName, keyRef);

                DocRevisionInfo[] docArray = documents.ToArray();
                foreach (DocRevisionInfo doc in docArray)
                {
                    LoadEdmFile(ctx, doc);
                }

                return docArray;
            });
        }

        public async Task<int> GetDocumentCountAsync(string entityName, string keyRef)
        {
            await _attachmentHandler.DownloadAttachmentsIfNeeded(entityName, keyRef, true, CancellationToken.None);

            return await WithDataContextAsync(ctx =>
            {
                IQueryable<DocRevisionInfo> documents = GetDocumentsQuery(ctx, entityName, keyRef);
                return documents.Count();
            });
        }

        private static IQueryable<DocRevisionInfo> GetDocumentsQuery(FwDataContext ctx, string entityName, string keyRef)
        {
            return from doc in GetDocumentsQuery(ctx)
                   where doc.DocumentRevision.LuName == entityName && doc.DocumentRevision.KeyRef == keyRef
                   select doc;
        }

        public async Task<DocRevisionInfo> GetDocumentInfoAsync(long docRevRowId)
        {
            return await WithDataContextAsync(ctx =>
            {
                var documents = from doc in GetDocumentsQuery(ctx)
                                where doc.DocumentRevision.RowId == docRevRowId
                                select doc;

                DocRevisionInfo info = documents.FirstOrDefault();

                if (info != null)
                {
                    LoadEdmFile(ctx, info);
                }

                return info;
            });
        }

        private static void LoadEdmFile(FwDataContext ctx, DocRevisionInfo info)
        {
            // Unable to currently do this within the query since we need to
            // first load the ORIGINAL one and if not found then load any other
            // Meaning we have to slow load the EdmFiles after we know which documents
            // are needed

            info.EdmFile = ctx.EdmFiles.FirstOrDefault(x =>
                                    x.DocClass == info.DocumentRevision.DocClass &&
                                    x.DocNo == info.DocumentRevision.DocNo &&
                                    x.DocSheet == info.DocumentRevision.DocSheet &&
                                    x.DocRev == info.DocumentRevision.DocRev &&
                                    x.DocType == "ORIGINAL"
                                );

            if (info.EdmFile == null)
            {
                info.EdmFile = ctx.EdmFiles.FirstOrDefault(x =>
                    x.DocClass == info.DocumentRevision.DocClass &&
                    x.DocNo == info.DocumentRevision.DocNo &&
                    x.DocSheet == info.DocumentRevision.DocSheet &&
                    x.DocRev == info.DocumentRevision.DocRev);
            }
        }

        public async Task<IEnumerable<EdmApplication>> GetEdmApplicationsAsync()
        {
            return await WithDataContextAsync(ctx => ctx.EdmApplications.ToArray());
        }

        public async Task<long> NewDocumentAsync(DocReferenceObject docRef, EdmFile edmFile, Stream dataStream)
        {
            if (docRef == null)
                throw new ArgumentNullException(nameof(docRef));
            if (docRef.RowId != 0)
                throw new ArgumentException($"{nameof(docRef)} must be a new record", nameof(docRef));
            if (string.IsNullOrEmpty(docRef.Title))
                throw new ArgumentException($"{nameof(docRef)}.{nameof(docRef.Title)} must have a value");
            if (edmFile == null)
                throw new ArgumentNullException(nameof(edmFile));
            if (edmFile.RowId != 0)
                throw new ArgumentException($"{nameof(edmFile)} must be a new record", nameof(edmFile));
            if (string.IsNullOrEmpty(edmFile.FileName))
                throw new ArgumentException($"{nameof(edmFile)}.{nameof(edmFile.FileName)} must have a value");
            if (dataStream == null)
                throw new ArgumentNullException(nameof(dataStream));

            string docNo = null;
            string docRev = null;
            string docSheet = null;

            await WithDataContextAsync(ctx =>
            {
                docNo = (string)ctx.GenerateClientKey<DocReferenceObject>(nameof(DocReferenceObject.DocNo));
                docRev = (string)ctx.GenerateClientKey<DocReferenceObject>(nameof(DocReferenceObject.DocRev));
                docSheet = (string)ctx.GenerateClientKey<DocReferenceObject>(nameof(DocReferenceObject.DocSheet));
            });

            docRef.DocNo = docNo;
            docRef.DocRev = docRev;
            docRef.DocSheet = docSheet;

            DocIssue docIssue = new DocIssue();
            docIssue.DocClass = docRef.DocClass;
            docIssue.DocNo = docRef.DocNo;
            docIssue.DocRev = docRef.DocRev;
            docIssue.DocSheet = docRef.DocSheet;
            docIssue.Title = docRef.Title;

            edmFile.DocClass = docIssue.DocClass;
            edmFile.DocNo = docIssue.DocNo;
            edmFile.DocRev = docIssue.DocRev;
            edmFile.DocSheet = docIssue.DocSheet;

            await PrepareEdmFileForUpload(edmFile, dataStream);

            await WithDataContextAsync(ctx =>
            {
                ctx.DocIssues.InsertOnSubmit(MobileAttachmentsProjection, docIssue);
                ctx.EdmFiles.InsertOnSubmit(MobileAttachmentsProjection, edmFile);
                ctx.DocReferenceObjects.InsertOnSubmit(MobileAttachmentsProjection, docRef);
                ctx.SubmitChanges(true);
            });

            FireDataChangeEvent(_eventAggregator, new RowBase[] { docIssue, edmFile, docRef });

            return docRef.RowId;
        }

        public async Task UpdateDocumentReferenceAsync(DocReferenceObject docRef, IEnumerable<string> changedMembers)
        {
            await WithDataContextAsync(ctx =>
            {
                RemoteRow rowToUpdate = ctx.DocReferenceObjects.FirstOrDefault(x => x.RowId == docRef.RowId);
                if (rowToUpdate != null)
                {
                    ctx.DocReferenceObjects.Attach(MobileAttachmentsProjection, rowToUpdate);
                    foreach (string member in changedMembers)
                    {
                        rowToUpdate[member] = docRef[member];
                        ObjPrimaryKey pk = ObjPrimaryKey.FromPrimaryKey(_model, rowToUpdate);
                        rowToUpdate.PrimaryKeyString = pk.ToFormattedKeyRef(MobileAttachmentsProjection, true);
                    }
                    ctx.SubmitChanges(true);
                }
            });

            FireDataChangeEvent(_eventAggregator, new RowBase[] { docRef });
        }

        public async Task<long> NewDocumentRevisionAsync(DocReferenceObject docRef, EdmFile edmFile, Stream dataStream)
        {
            if (docRef == null)
                throw new ArgumentNullException(nameof(docRef));
            if (docRef.RowId == 0)
                throw new ArgumentException($"{nameof(docRef)} must be an existing new record", nameof(docRef));
            if (edmFile == null)
                throw new ArgumentNullException(nameof(edmFile));
            if (edmFile.RowId != 0)
                throw new ArgumentException($"{nameof(edmFile)} must be a new record", nameof(edmFile));
            if (string.IsNullOrEmpty(edmFile.FileName))
                throw new ArgumentException($"{nameof(edmFile)}.{nameof(edmFile.FileName)} must have a value");
            if (dataStream == null)
                throw new ArgumentNullException(nameof(dataStream));

            string docSheet = null;
            string docRev = null;

            await WithDataContextAsync(ctx =>
            {
                docSheet = (string)ctx.GenerateClientKey<DocReferenceObject>(nameof(DocReferenceObject.DocSheet));
                docRev = (string)ctx.GenerateClientKey<DocReferenceObject>(nameof(DocReferenceObject.DocRev));
            });

            DocIssue docIssue = new DocIssue();
            docIssue.DocClass = docRef.DocClass;
            docIssue.DocNo = docRef.DocNo;
            docIssue.DocSheet = docSheet;
            docIssue.DocRev = docRev;
            docIssue.OldDocSheet = docRef.DocSheet;
            docIssue.OldDocRev = docRef.DocRev;
            docIssue.Title = docRef.Title;

            edmFile.DocClass = docIssue.DocClass;
            edmFile.DocNo = docIssue.DocNo;
            edmFile.DocSheet = docIssue.DocSheet;
            edmFile.DocRev = docIssue.DocRev;

            DocReferenceObject newDocRef = CloneRow(docRef, true);
            newDocRef.ObjId = docRef.ObjId;
            newDocRef.DocSheet = docIssue.DocSheet;
            newDocRef.DocRev = docIssue.DocRev;
            newDocRef.OldDocSheet = docRef.DocSheet;
            newDocRef.OldDocRev = docRef.DocRev;

            await PrepareEdmFileForUpload(edmFile, dataStream);

            await WithDataContextAsync(ctx =>
            {
                ctx.DocReferenceObjects.DeleteOnSubmit(MobileAttachmentsProjection, docRef);
                ctx.SubmitChanges(false);

                ctx.DocIssues.InsertOnSubmit(MobileAttachmentsProjection, docIssue);
                ctx.EdmFiles.InsertOnSubmit(MobileAttachmentsProjection, edmFile);
                ctx.DocReferenceObjects.InsertOnSubmit(MobileAttachmentsProjection, newDocRef);
                ctx.SubmitChanges(true);
            });

            FireDataChangeEvent(_eventAggregator, new RowBase[] { docIssue, edmFile, newDocRef });

            return newDocRef.RowId;
        }

        public async Task UploadDocumentAsync(string docClass, string docNo, string docSheet, string docRev, string fileName, Stream dataStream)
        {
            EdmFile edmFile = new EdmFile();
            edmFile.DocClass = docClass;
            edmFile.DocNo = docNo;
            edmFile.DocSheet = docSheet;
            edmFile.DocRev = docRev;
            edmFile.FileNo = 1;

            await WithDataContextAsync(ctx =>
            {
                string ext = Path.GetExtension(fileName)?.ToUpperInvariant().TrimStart('.');

                EdmApplication application = ctx.EdmApplications.FirstOrDefault(x => x.DocumentType == "ORIGINAL" && x.FileExtention == ext);

                if (application == null)
                {
                    application = ctx.EdmApplications.FirstOrDefault(x => x.DocumentType == "VIEW" && x.FileExtention == ext);
                }

                if (application == null)
                {
                    throw new Execution.ExecutionException(string.Format(Strings.NoEdmApplicationFound, ext));
                }

                edmFile.FileType = application.FileType;
                edmFile.FileName = GetFileName(ctx, edmFile);
            });

            await PrepareEdmFileForUpload(edmFile, dataStream);

            await WithDataContextAsync(ctx =>
            {
                ctx.EdmFiles.InsertOnSubmit(MobileAttachmentsProjection, edmFile);
                ctx.SubmitChanges(true);
            });

            FireDataChangeEvent(_eventAggregator, edmFile);
        }

        internal static void CreateAndConnectDoc(FwDataContext ctx, string luName, string keyRef, string title,
            out DocIssue preparedDocIssue, out DocReferenceObject preparedDocReferenceObject)
        {
            if (luName == null)
                throw new ArgumentNullException(nameof(luName));

            if (keyRef == null)
                throw new ArgumentNullException(nameof(keyRef));

            MobileDocClass defaultDocumentClass = ctx.MobileDocClasses.FirstOrDefault(x => x.MobileDefault == FndBoolean.True);
            if (defaultDocumentClass == null)
            {
                throw new Execution.ExecutionException(Strings.NoDefaultMobileDocClass);
            }

            DocReferenceObject docRef = new DocReferenceObject();
            docRef.DocClass = defaultDocumentClass.DocClass;
            docRef.DocNo = (string)ctx.GenerateClientKey<DocReferenceObject>(nameof(DocReferenceObject.DocNo));
            docRef.DocRev = (string)ctx.GenerateClientKey<DocReferenceObject>(nameof(DocReferenceObject.DocRev));
            docRef.DocSheet = (string)ctx.GenerateClientKey<DocReferenceObject>(nameof(DocReferenceObject.DocSheet));
            docRef.LuName = luName;
            docRef.KeyRef = keyRef;
            docRef.Title = title;
            docRef.DocumentAccess = DocumentAccess.Edit;
            docRef.DocIssueObjstate = DocIssueObjstate.Preliminary;

            DocIssue docIssue = new DocIssue();
            docIssue.DocClass = docRef.DocClass;
            docIssue.DocNo = docRef.DocNo;
            docIssue.DocSheet = docRef.DocSheet;
            docIssue.DocRev = docRef.DocRev;
            docIssue.Title = docRef.Title;

            ctx.DocIssues.InsertOnSubmit(MobileAttachmentsProjection, docIssue);
            ctx.DocReferenceObjects.InsertOnSubmit(MobileAttachmentsProjection, docRef);
            ctx.SubmitChanges(true);

            preparedDocIssue = docIssue;
            preparedDocReferenceObject = docRef;
        }

        internal static void ConnectDocumentAttachment(FwDataContext ctx, FndDocumentKeys documentKeys, string luName, string keyRef, out DocReferenceObject preparedDocReferenceObject)
        {
            if (documentKeys == null)
                throw new ArgumentNullException(nameof(documentKeys));

            if (luName == null)
                throw new ArgumentNullException(nameof(luName));

            if (keyRef == null)
                throw new ArgumentNullException(nameof(keyRef));

            DocReferenceObject docReferenceObject = ctx.DocReferenceObjects.FirstOrDefault(x => x.DocNo == documentKeys.DocNo);

            DocReferenceObject docRef = new DocReferenceObject();
            docRef.DocClass = documentKeys.DocClass;
            docRef.DocNo = documentKeys.DocNo;
            docRef.DocRev = documentKeys.DocRev;
            docRef.DocSheet = documentKeys.DocSheet;
            docRef.LuName = luName;
            docRef.KeyRef = keyRef;
            docRef.DocumentAccess = DocumentAccess.Edit;
            docRef.DocIssueObjstate = DocIssueObjstate.Preliminary;
            docRef.Title = docReferenceObject?.Title ?? Strings.Document;

            ctx.DocReferenceObjects.InsertOnSubmit(MobileAttachmentsProjection, docRef);
            ctx.SubmitChanges(true);

            preparedDocReferenceObject = docRef;
        }

        private async Task PrepareEdmFileForUpload(EdmFile edmFile, Stream dataStream)
        {
            edmFile.DocType = "ORIGINAL";
            edmFile.FileNo = 1;
            edmFile.AttachmentStatus = AttachmentStatus.RequiresUpload;

            ILocalFileInfo file = await GetLocalFileForDocumentAsync(edmFile);
            await file.WriteStreamAsync(dataStream);
            await file.SetReadOnly();

            if (string.IsNullOrEmpty(edmFile.FileName))
            {
                edmFile.FileName = Path.GetFileName(file.FilePath);
            }
        }

        public async Task RequestDownloadAsync(EdmFile edmFile)
        {
            await ChangeAttachmentStatus(edmFile, AttachmentStatus.RequiresDownload);
        }

        public async Task RequestUploadAsync(EdmFile edmFile)
        {
            await ChangeAttachmentStatus(edmFile, AttachmentStatus.RequiresUpload);
        }

        private async Task ChangeAttachmentStatus(EdmFile edmFile, AttachmentStatus newStatus)
        {
            await WithDataContextAsync(ctx =>
            {
                // Don't change the original row on a background thread
                EdmFile updateRow = CloneRow(edmFile, false);
                ctx.EdmFiles.Attach(MobileAttachmentsProjection, updateRow);
                updateRow.AttachmentStatus = newStatus;
                ctx.SubmitChanges(false);
            });

            edmFile.AttachmentStatus = newStatus;
            FireDataChangeEvent(_eventAggregator, edmFile);
        }

        public async Task<bool> IsEnabledFor(string entityName)
        {
            if (!_documentsReadAllowed)
            {
                return false;
            }

            IMetaTable table = _model.GetTable(RemoteNaming.ToTableName(entityName));
            if (table == null || table.TableImplementation == TableImplementation.View)
            {
                return false;
            }

            return await GetConfigAsync(entityName) != null;
        }

        public async Task<bool> CanCreateNew(string entityName, string keyRef)
        {
            MobileObjectConnectionConfig config = await GetConfigAsync(entityName);
            return _documentsWriteAllowed && config?.Readonly != true;
        }

        public async Task<bool> CanReviseDocument(string entityName, string keyRef)
        {
            MobileObjectConnectionConfig config = await GetConfigAsync(entityName);
            return _documentsWriteAllowed && config?.Readonly != true;
        }
    }
}
