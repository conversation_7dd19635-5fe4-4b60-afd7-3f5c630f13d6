﻿using System;
using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal class StringRegexLike : StringFunction
    {
        public const string FunctionName = "RegexLike";

        public StringRegexLike()
            : base(FunctionName, 2, false)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();
            if (!string.IsNullOrEmpty(regexPattern))
            {
                return Regex.IsMatch(stringToModify, regexPattern, RegexOptions.None, TimeSpan.FromSeconds(10));
            }

            return false;
        }
    }

    internal class StringRegexLike3 : StringFunction
    {
        public const string FunctionName = "RegexLike";

        public StringRegexLike3()
            : base(FunctionName, 3, false)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();
            if (!string.IsNullOrEmpty(regexPattern))
            {
                RegexOptions regexOptions = StringToRegexOptions(parameters[2].GetString());
                return Regex.IsMatch(stringToModify, regexPattern, regexOptions, TimeSpan.FromSeconds(10));
            }

            return false;
        }
    }
}
