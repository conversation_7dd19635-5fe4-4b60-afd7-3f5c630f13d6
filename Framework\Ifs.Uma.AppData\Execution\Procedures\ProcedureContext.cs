﻿using System;
using System.Collections.Generic;
using System.Threading;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Execution.Procedures
{
    public sealed class ProcedureContext : ExecutionContext
    {
        public CpiProc Procedure { get; }
        public string ProcedureName => Procedure?.GetFullName();

        public CpiProcLayer Layer { get;  }
        public int? LayerNo { get; }

        public string ActionName { get; private set; }
        public bool BoundAction { get; private set; }

        public FwDataContext DbDataContext => _sharedData.DbDataContext;
        public DataChangeSet DataChangeSet => InTransaction ? _sharedData.TransactionChangeSet : _sharedData.DataChangeSet;
        public DataChangeSet CommittedDataChangeSet => _sharedData.DataChangeSet;
        public CancellationToken CancelToken => _sharedData.CancelToken;
        public bool InTransaction => _sharedData.TransactionCounter > 0;

        private readonly ProcedureContext _parentContext;
        private readonly SharedData _sharedData;

        internal ProcedureContext(string projectionName, IMetadata metadata, IExpressionRunner expressionRunner, IDataContextProvider db, CancellationToken cancelToken)
            : base(projectionName, metadata, expressionRunner, null, null)
        {
            if (db == null) throw new ArgumentNullException(nameof(db));

            _sharedData = new SharedData(db, cancelToken);
            _parentContext = null;
        }

        private ProcedureContext(string projectionName, IMetadata metadata, IExpressionRunner expressionRunner, ProcedureContext parentContext, CpiProc proc, int? layerNo, CpiProcLayer layer)
            : base(projectionName, metadata, expressionRunner, proc.GetFullName(), CombineParamsAndVars(metadata, proc, layer))
        {
            if (proc == null) throw new ArgumentNullException(nameof(proc));
            if (parentContext == null) throw new ArgumentNullException(nameof(parentContext));

            Procedure = proc;
            Layer = layer;
            LayerNo = layerNo;
            _parentContext = parentContext;
            _sharedData = _parentContext._sharedData;
        }

        protected override ExecutionException NewException(string message)
        {
            return new ProcedureException(message);
        }

        public ProcedureContext CreateSubContext(CpiProc proc, IReadOnlyDictionary<string, object> parameters)
        {
            int? layerNo = null;
            CpiProcLayer layer = GetLayer(proc, ref layerNo);

            ProcedureContext subContext = new ProcedureContext(ProjectionName, Metadata, ExpressionRunner, this, proc, layerNo, layer);

            foreach (KeyValuePair<string, object> kvp in parameters)
            {
                subContext.Assign(kvp.Key, kvp.Value);
            }

            if (proc.Type == ProcedureType.Action)
            {
                subContext.ActionName = proc.Name;
                subContext.BoundAction = proc.Name.Contains(".");
            }

            return subContext;
        }

        public ProcedureContext CreateSuperContext(IReadOnlyDictionary<string, object> parameters)
        {
            int? layerNo = LayerNo - 1;
            CpiProcLayer layer = GetLayer(Procedure, ref layerNo);
            
            ProcedureContext superContext = new ProcedureContext(ProjectionName, Metadata, ExpressionRunner, this, Procedure, LayerNo, layer);
            superContext.ActionName = ActionName;
            superContext.BoundAction = BoundAction;

            foreach (KeyValuePair<string, object> kvp in parameters)
            {
                superContext.Assign(kvp.Key, kvp.Value);
            }

            return superContext;
        }

        private static IReadOnlyDictionary<string, VariableStorage> CombineParamsAndVars(IMetadata metadata, CpiProc proc, CpiProcLayer layer)
        {
            Dictionary<string, VariableStorage> vars = new Dictionary<string, VariableStorage>();

            if (proc.Parameters != null)
            {
                foreach (CpiParam param in proc.Parameters)
                {
                    vars[param.Name] = new VariableStorage(metadata, param.Name, param);
                }
            }

            if (layer?.Vars != null)
            {
                foreach (CpiParam variable in layer.Vars)
                {
                    vars[variable.Name] = new VariableStorage(metadata, variable.Name, variable);
                }
            }

            return vars;
        }

        private static CpiProcLayer GetLayer(CpiProc proc, ref int? layerNo)
        {
            if (proc == null) throw new ArgumentNullException(nameof(proc));

            if (proc.Layers == null || proc.Layers.Length == 0)
            {
                layerNo = -1;
                return null;
            }

            if (!layerNo.HasValue)
            {
                layerNo = proc.Layers.Length - 1;
            }

            if (layerNo < 0)
            {
                layerNo = -1;
                return null;
            }

            return proc.Layers[layerNo.Value];
        }

        public CpiParam GetVarInfo(string name)
        {
            if (Procedure.Parameters != null)
            {
                foreach (CpiParam param in Procedure.Parameters)
                {
                    if (param.Name == name)
                    {
                        return param;
                    }
                }
            }

            if (Layer?.Vars != null)
            {
                foreach (var variable in Layer.Vars)
                {
                    if (variable.Name == name)
                    {
                        return variable;
                    }
                }
            }

            return null;
        }

        public void WithTransaction(Action action)
        {
            _sharedData.WithTransaction(action);
        }

        public void AddPostTransactionAction(Action action)
        {
            if (InTransaction)
            {
                if (_sharedData.PostTransactionActions == null)
                {
                    _sharedData.PostTransactionActions = new List<Action>();
                }

                _sharedData.PostTransactionActions.Add(action);
            }
            else
            {
                action();
            }
        }

        private sealed class SharedData
        {
            public FwDataContext DbDataContext { get; }
            public DataChangeSet DataChangeSet { get; }
            public CancellationToken CancelToken { get; }

            public List<Action> PostTransactionActions { get; set; }
            public int TransactionCounter { get; private set; }
            public DataChangeSet TransactionChangeSet { get; private set; }

            public SharedData(IDataContextProvider db, CancellationToken cancelToken)
            {
                DbDataContext = db.CreateDataContext();
                DataChangeSet = new DataChangeSet();
                CancelToken = cancelToken;
            }

            public void WithTransaction(Action action)
            {
                try
                {
                    TransactionCounter++;

                    if (TransactionChangeSet == null)
                    {
                        TransactionChangeSet = new DataChangeSet();
                    }

                    DbDataContext.ExecuteInTransaction(action);

                    TransactionCounter--;

                    if (TransactionCounter == 0)
                    {
                        DataChangeSet.AddChangeSet(TransactionChangeSet);

                        if (PostTransactionActions != null)
                        {
                            foreach (Action postTransAction in PostTransactionActions)
                            {
                                postTransAction();
                            }
                        }

                        TransactionChangeSet = null;
                        PostTransactionActions?.Clear();
                    }
                }
                catch
                {
                    TransactionCounter--;

                    if (TransactionCounter == 0)
                    {
                        TransactionChangeSet = null;
                        PostTransactionActions?.Clear();
                    }

                    throw;
                }
            }
        }
    }
}
