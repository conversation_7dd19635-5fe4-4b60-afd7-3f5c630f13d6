﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    internal static class DbExpressionExtensions
    {
        public static bool IsDbExpression(this Expression e)
        {
            return e != null && e.NodeType == ExpressionType.Extension && e is DbExpression;
        }

        public static DbExpressionType GetDbNodeType(this Expression e)
        {
            DbExpressionType result = DbExpressionType.None;
            if (e != null && e.NodeType == ExpressionType.Extension)
            {
                DbExpression dbe = e as DbExpression;
                if (dbe != null)
                {
                    result = dbe.DbNodeType;
                }
            }
            return result;
        }

        public static SelectExpression SetColumns(this SelectExpression select, IEnumerable<ColumnDeclaration> columns)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (columns == null) throw new ArgumentNullException("columns");
            return new SelectExpression(select.Alias, columns.OrderBy(c => c.Name), select.From, select.Where, select.OrderBy, select.GroupBy, select.IsDistinct, select.Skip, select.Take, select.IsReverse);
        }

        public static SelectExpression AddColumn(this SelectExpression select, ColumnDeclaration column)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (column == null) throw new ArgumentNullException("column");
            List<ColumnDeclaration> columns = new List<ColumnDeclaration>(select.Columns);
            columns.Add(column);
            return select.SetColumns(columns);
        }

        public static SelectExpression RemoveColumn(this SelectExpression select, ColumnDeclaration column)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (column == null) throw new ArgumentNullException("column");
            List<ColumnDeclaration> columns = new List<ColumnDeclaration>(select.Columns);
            columns.Remove(column);
            return select.SetColumns(columns);
        }

        public static string GetAvailableColumnName(this IList<ColumnDeclaration> columns, string baseName)
        {
            string name = baseName;
            int n = 0;
            while (!IsUniqueName(columns, name))
            {
                name = baseName + (n++);
            }
            return name;
        }

        private static bool IsUniqueName(IList<ColumnDeclaration> columns, string name)
        {
            foreach (var col in columns)
            {
                if (col.Name == name)
                {
                    return false;
                }
            }
            return true;
        }

        public static ProjectionExpression AddOuterJoinTest(this ProjectionExpression proj, Expression expression)
        {
            if (proj == null) throw new ArgumentNullException("proj");
            if (expression == null) throw new ArgumentNullException("expression");
            string colName = proj.Select.Columns.GetAvailableColumnName("Test");
            SelectExpression newSource = proj.Select.AddColumn(new ColumnDeclaration(colName, expression));
            Expression newProjector =
                new OuterJoinedExpression(
                    new ColumnExpression(expression.Type, newSource.Alias, colName),
                    proj.Projector
                    );
            return new ProjectionExpression(newSource, newProjector, proj.Aggregator);
        }

        public static ProjectionExpression AddOuterJoinTest(this ProjectionExpression proj)
        {
            if (proj == null) throw new ArgumentNullException("proj");
            var test = GetOuterJoinTest(proj.Select);
            var select = proj.Select;
            ColumnExpression testCol = null;
            // look to see if test expression exists in columns already
            foreach (var col in select.Columns)
            {
                if (test.Equals(col.Expression))
                {
                    testCol = new ColumnExpression(test.Type, select.Alias, col.Name);
                    break;
                }
            }
            if (testCol == null)
            {
                // add expression to projection
                testCol = test as ColumnExpression;
                string colName = (testCol != null) ? testCol.Name : "Test";
                colName = proj.Select.Columns.GetAvailableColumnName(colName);
                select = select.AddColumn(new ColumnDeclaration(colName, test));
                testCol = new ColumnExpression(test.Type, select.Alias, colName);
            }
            var newProjector = new OuterJoinedExpression(testCol, proj.Projector);
            return new ProjectionExpression(select, newProjector, proj.Aggregator);
        }

        private static Expression GetOuterJoinTest(SelectExpression select)
        {
            if (select == null) throw new ArgumentNullException("select");
            // if the column is used in the join condition (equality test)
            // if it is null in the database then the join test won't match (null != null) so the row won't appear
            // we can safely use this existing column as our test to determine if the outer join produced a row

            // find a column that is used in equality test
            var aliases = DeclaredAliasGatherer.Gather(select.From);
            var joinColumns = JoinColumnGatherer.Gather(aliases, select).ToList();
            if (joinColumns.Count > 0)
            {
                // prefer one that is already in the projection list.
                foreach (var jc in joinColumns)
                {
                    foreach (var col in select.Columns)
                    {
                        if (jc.Equals(col.Expression))
                        {
                            return jc;
                        }
                    }
                }
                return joinColumns[0];
            }

            // fall back to introducing a constant
            return Expression.Constant(1, typeof(int?));
        }

        class JoinColumnGatherer
        {
            HashSet<TableAlias> aliases;
            HashSet<ColumnExpression> columns = new HashSet<ColumnExpression>();

            private JoinColumnGatherer(HashSet<TableAlias> aliases)
            {
                this.aliases = aliases;
            }

            public static HashSet<ColumnExpression> Gather(HashSet<TableAlias> aliases, SelectExpression select)
            {
                var gatherer = new JoinColumnGatherer(aliases);
                gatherer.Gather(select.Where);
                return gatherer.columns;
            }

            private void Gather(Expression expression)
            {
                BinaryExpression b = expression as BinaryExpression;
                if (b != null)
                {
                    switch (b.NodeType)
                    {
                        case ExpressionType.Equal:
                        case ExpressionType.NotEqual:
                            if (b.Left.NodeType == ExpressionType.New && b.Right.NodeType == ExpressionType.New)
                            {
                                GatherMultiColumns((NewExpression)b.Left, (NewExpression)b.Right);
                            }
                            else
                            {
                                GatherColumns(b.Left, b.Right);
                            }
                            break;
                        case ExpressionType.And:
                        case ExpressionType.AndAlso:
                            if (b.Type == typeof(bool) || b.Type == typeof(bool?))
                            {
                                this.Gather(b.Left);
                                this.Gather(b.Right);
                            }
                            break;
                    }
                }
            }

            private void GatherMultiColumns(NewExpression left, NewExpression right)
            {
                if (left.Arguments.Count == right.Arguments.Count)
                {
                    for (int i = 0; i < left.Arguments.Count; i++)
                    {
                        GatherColumns(left.Arguments[i], right.Arguments[i]);
                    }
                }
            }

            private void GatherColumns(Expression left, Expression right)
            {
                if (IsExternalColumn(left) && GetColumn(right) != null)
                {
                    this.columns.Add(GetColumn(right));
                }
                else if (IsExternalColumn(right) && GetColumn(left) != null)
                {
                    this.columns.Add(GetColumn(left));
                }
            }

            private static ColumnExpression GetColumn(Expression exp)
            {
                while (exp.NodeType == ExpressionType.Convert)
                    exp = ((UnaryExpression)exp).Operand;
                return exp as ColumnExpression;
            }

            private bool IsExternalColumn(Expression exp)
            {
                var col = GetColumn(exp);
                if (col != null && !this.aliases.Contains(col.Alias))
                    return true;
                return false;
            }
        }

        public static SelectExpression SetDistinct(this SelectExpression select, bool isDistinct)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (select.IsDistinct != isDistinct)
            {
                return new SelectExpression(select.Alias, select.Columns, select.From, select.Where, select.OrderBy, select.GroupBy, isDistinct, select.Skip, select.Take, select.IsReverse);
            }
            return select;
        }

        public static SelectExpression SetReverse(this SelectExpression select, bool isReverse)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (select.IsReverse != isReverse)
            {
                return new SelectExpression(select.Alias, select.Columns, select.From, select.Where, select.OrderBy, select.GroupBy, select.IsDistinct, select.Skip, select.Take, isReverse);
            }
            return select;
        }

        public static SelectExpression SetWhere(this SelectExpression select, Expression where)
        {
            if (select == null) throw new ArgumentNullException("select");
            return select.Update(select.Columns, select.From, where, select.OrderBy, select.GroupBy, select.Skip, select.Take);
        }

        public static SelectExpression SetOrderBy(this SelectExpression select, IEnumerable<OrderExpression> orderBy)
        {
            if (select == null) throw new ArgumentNullException("select");
            return new SelectExpression(select.Alias, select.Columns, select.From, select.Where, orderBy, select.GroupBy, select.IsDistinct, select.Skip, select.Take, select.IsReverse);
        }

        public static SelectExpression AddOrderExpression(this SelectExpression select, OrderExpression ordering)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (ordering == null) throw new ArgumentNullException("ordering");
            List<OrderExpression> orderby = new List<OrderExpression>();
            if (select.OrderBy != null)
                orderby.AddRange(select.OrderBy);
            orderby.Add(ordering);
            return select.SetOrderBy(orderby);
        }

        public static SelectExpression RemoveOrderExpression(this SelectExpression select, OrderExpression ordering)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (ordering == null) throw new ArgumentNullException("ordering");
            if (select.OrderBy != null && select.OrderBy.Count > 0)
            {
                List<OrderExpression> orderby = new List<OrderExpression>(select.OrderBy);
                orderby.Remove(ordering);
                return select.SetOrderBy(orderby);
            }
            return select;
        }

        public static SelectExpression SetGroupBy(this SelectExpression select, IEnumerable<Expression> groupBy)
        {
            if (select == null) throw new ArgumentNullException("select");
            return new SelectExpression(select.Alias, select.Columns, select.From, select.Where, select.OrderBy, groupBy, select.IsDistinct, select.Skip, select.Take, select.IsReverse);
        }

        public static SelectExpression AddGroupExpression(this SelectExpression select, Expression expression)
        {
            if (select == null) throw new ArgumentNullException("select");
            List<Expression> groupby = new List<Expression>();
            if (select.GroupBy != null)
                groupby.AddRange(select.GroupBy);
            groupby.Add(expression);
            return select.SetGroupBy(groupby);
        }

        public static SelectExpression RemoveGroupExpression(this SelectExpression select, Expression expression)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (select.GroupBy != null && select.GroupBy.Count > 0)
            {
                List<Expression> groupby = new List<Expression>(select.GroupBy);
                groupby.Remove(expression);
                return select.SetGroupBy(groupby);
            }
            return select;
        }

        public static SelectExpression SetSkip(this SelectExpression select, Expression skip)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (skip != select.Skip)
            {
                return new SelectExpression(select.Alias, select.Columns, select.From, select.Where, select.OrderBy, select.GroupBy, select.IsDistinct, skip, select.Take, select.IsReverse);
            }
            return select;
        }

        public static SelectExpression SetTake(this SelectExpression select, Expression take)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (take != select.Take)
            {
                return new SelectExpression(select.Alias, select.Columns, select.From, select.Where, select.OrderBy, select.GroupBy, select.IsDistinct, select.Skip, take, select.IsReverse);
            }
            return select;
        }

        public static SelectExpression AddRedundantSelect(this SelectExpression sel, TableAlias newAlias)
        {
            if (sel == null) throw new ArgumentNullException("sel");
            var newColumns = 
                from d in sel.Columns
                select new ColumnDeclaration(d.Name, new ColumnExpression(d.Expression.Type, newAlias, d.Name));

            var newFrom = new SelectExpression(newAlias, sel.Columns, sel.From, sel.Where, sel.OrderBy, sel.GroupBy, sel.IsDistinct, sel.Skip, sel.Take, sel.IsReverse);
            return new SelectExpression(sel.Alias, newColumns, newFrom, null, null, null, false, null, null, false);
        }

        public static SelectExpression RemoveRedundantFrom(this SelectExpression select)
        {
            if (select == null) throw new ArgumentNullException("select");
            SelectExpression fromSelect = select.From as SelectExpression;
            if (fromSelect != null)
            {
                return SubqueryRemover.Remove(select, fromSelect);
            }
            return select;
        }

        public static SelectExpression SetFrom(this SelectExpression select, Expression from)
        {
            if (select == null) throw new ArgumentNullException("select");
            if (select.From != from)
            {
                return new SelectExpression(select.Alias, select.Columns, from, select.Where, select.OrderBy, select.GroupBy, select.IsDistinct, select.Skip, select.Take, select.IsReverse);
            }
            return select;
        }
    }
}
