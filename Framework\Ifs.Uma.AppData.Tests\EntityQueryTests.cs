﻿using System.Linq;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class EntityQueryTests : FrameworkTest
    {
        [Test]
        public void Basic()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Customers");
            EntityQuery query = new EntityQuery(source);
            
            CheckResults(query, "1", "2", "3", "4");
        }

        [Test]
        public void Sort()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Customers");
            EntityQuery query = new EntityQuery(source);
            query.AddSort("CustomerNo", ESortOrder.Descending);

            CheckResults(query, "4", "3", "2", "1");
        }

        [Test]
        public void Search()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Customers");
            EntityQuery query = new EntityQuery(source);
            query.Search = new AttributeSearch(new[] { "CustomerNo", "CreatedAt" }, "1");

            CheckResults(query, "1", "3");
        }

        [Test]
        public void Expand()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Expand = new[] { "ExpandNumRef", "ExpandLetterRef" };

            FwDataContext ctx = CreateDataContext();
            EntityRecord record = ctx.Query(query).Single();

            Assert.IsNotNull(record.Row);
            Assert.AreEqual("tst_1_col_a", record.Row["ColA"]);

            Assert.IsNotNull(record.References["ExpandNumRef"]);
            Assert.AreEqual("tst_col1", record.References["ExpandNumRef"]["Col1"]);

            Assert.IsNotNull(record.References["ExpandLetterRef"]);
            Assert.AreEqual("tst_col_a", record.References["ExpandLetterRef"]["ColA"]);
        }

        [Test]
        public void ExpandOnSelect()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA", "ExpandNumRef.Col1");

            FwDataContext ctx = CreateDataContext();
            EntityRecord record = ctx.Query(query).Single();

            Assert.IsNotNull(record.Row);
            Assert.AreEqual("tst_1_col_a", record.Row["ColA"]);

            Assert.IsNotNull(record.References["ExpandNumRef"]);
            Assert.AreEqual("tst_col1", record.References["ExpandNumRef"]["Col1"]);

            Assert.IsFalse(record.References.ContainsKey("ExpandLetterRef"));
        }
        
        [Test]
        public void OfflineFilter()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "CustomersWithoutJohn");
            EntityQuery query = new EntityQuery(source);
            
            CheckResults(query, "1", "4");
        }

        [Test]
        public void OfflineFilterWithWhere()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "CustomersWithoutJohn");
            EntityQuery query = new EntityQuery(source);
            query.AddFilter("CustomerName", "D");

            CheckResults(query, "4");
        }

        [Test]
        public void UsedEntities()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "CustomersWithoutJohn");
            EntityQuery query = new EntityQuery(source);
            PreparedEntityQuery preparedQuery = query.Prepare();
            CollectionAssert.AreEquivalent(new[] { "TstCustomer" }, preparedQuery.UsedEntities);
        }

        [Test]
        public void UsedEntitiesOnExpand()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Expand = new[] { "ExpandNumRef", "ExpandLetterRef" };
            PreparedEntityQuery preparedQuery = query.Prepare();
            CollectionAssert.AreEquivalent(new[] { "TstExpand", "TstExpandNum", "TstExpandLetter" }, preparedQuery.UsedEntities);
        }

        [Test]
        public void UsedEntitiesOnRefFilter()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "ExpandsWithRefFilter");
            EntityQuery query = new EntityQuery(source);
            PreparedEntityQuery preparedQuery = query.Prepare();
            CollectionAssert.AreEquivalent(new[] { "TstExpand", "TstExpandLetter", }, preparedQuery.UsedEntities);
        }

        private void CheckResults(EntityQuery query, params string[] ids)
        {
            FwDataContext ctx = CreateDataContext();
            var rows = ctx.Query(query).ToArray();
            string[] actualIds = rows.Select(x => (string)x.Row["CustomerNo"]).ToArray();
            Assert.That(actualIds, Is.EquivalentTo(ids));

            // Check that if we use ApplyTo rather than using the
            // database to do the filtering / sorting we should still get the same rults
            EntityQuery allRowsQuery = new EntityQuery(query.DataSource);
            allRowsQuery.Expand = query.Expand;
            var allRows = ctx.Query(allRowsQuery).ToArray();
            var applyToRows = query.ApplyTo(allRows).Records.ToArray();
            string[] applyToActualIds = applyToRows.Select(x => (string)x.Row["CustomerNo"]).ToArray();
            Assert.That(applyToActualIds, Is.EquivalentTo(ids));
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("EntityQuerySchema", "EntityQueryData");
        }
    }
}
