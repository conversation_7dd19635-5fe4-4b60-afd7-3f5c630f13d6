{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "syncpolicy": {"type": "OnlineOnly"}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<TestEntitySet>": {"name": "TestEntitySet", "type": "Function", "layers": [{"vars": [{"name": "MyCount"}], "execute": [{"call": {"method": "count", "args": {"entity": "Customers"}}, "assign": "MyCount"}]}]}}}}