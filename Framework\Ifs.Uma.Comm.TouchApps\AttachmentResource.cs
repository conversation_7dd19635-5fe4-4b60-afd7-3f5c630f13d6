﻿using System.Runtime.Serialization;
using System.Text;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Cloud.Client.Utils;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Messages;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class AttachmentResource : AppResource, ICustomResourceSerializer
    {
        public override string ResourceName => "MobileAttachments.svc/GetAttachments";

        [DataMember]
        public string AppName { get; set; }

        [DataMember]
        public string LuName { get; set; }

        [DataMember]
        public string KeyRef { get; set; }

        [DataMember]
        public bool IncludeMedia { get; set; }

        [DataMember]
        public bool IncludeDocuments { get; set; }

        [DataMember]
        public int DeviceId { get; set; }

        [DataMember]
        public string Media { get; set; }

        [DataMember]
        public string Documents { get; set; }

        public object DeserializeJsonString(string jsonString)
        {
            return ContentSerializationHelper.DeserializeJsonBinary<AttachmentResource>(Encoding.UTF8.GetBytes(jsonString));
        }

        public object EmptyResponseHandling(bool isSuccess)
        {
            return isSuccess ? ExecuteResult.True : ExecuteResult.False;
        }

        public string SerializeToJsonString()
        {
            JObject attachmentsResource = new JObject();
            attachmentsResource.Add("AppName", AppName);
            attachmentsResource.Add("LuName", LuName);
            attachmentsResource.Add("KeyRef", KeyRef);
            attachmentsResource.Add("IncludeDocuments", IncludeDocuments);
            attachmentsResource.Add("IncludeMedia", IncludeMedia);
            return MessageUtils.JObjectToString(attachmentsResource);
        }

        [DataContract]
        public class MediaData
        {
            [DataMember(Name = "lu_name")]
            public string LuName { get; set; }

            [DataMember(Name = "key_ref")]
            public string KeyRef { get; set; }

            [DataMember(Name = "library_id")]
            public string LibraryId { get; set; }

            [DataMember(Name = "library_item_id")]
            public string LibraryItemId { get; set; }
            
            [DataMember(Name = "item_id")]
            public long ItemId { get; set; }
            
            [DataMember(Name = "name")]
            public string Name { get; set; }

            [DataMember(Name = "description")]
            public string Description { get; set; }

            [DataMember(Name = "latitude")]
            public double? Latitude { get; set; }

            [DataMember(Name = "longitude")]
            public double? Longitude { get; set; }

            [DataMember(Name = "media_file")]
            public string MediaFile { get; set; }
            
            [DataMember(Name = "media_item_type")]
            public string MediaItemType { get; set; }
            
            [DataMember(Name = "private_media_item")]
            public string PrivateMediaItem { get; set; }

            [DataMember(Name = "default_media")]
            public string DefaultMedia { get; set; }

            [DataMember(Name = "main_library")]
            public bool? MainLibrary { get; set; }
        }

        [DataContract]
        public class DocumentData
        {
            [DataMember(Name = "lu_name")]
            public string LuName { get; set; }

            [DataMember(Name = "key_ref")]
            public string KeyRef { get; set; }

            [DataMember(Name = "doc_class")]
            public string DocClass { get; set; }
            
            [DataMember(Name = "doc_no")]
            public string DocNo { get; set; }
            
            [DataMember(Name = "doc_sheet")]
            public string DocSheet { get; set; }
            
            [DataMember(Name = "doc_rev")]
            public string DocRev { get; set; }
            
            [DataMember(Name = "file_no")]
            public long FileNo { get; set; }
            
            [DataMember(Name = "file_name")]
            public string FileName { get; set; }
            
            [DataMember(Name = "doc_title")]
            public string DocTitle { get; set; }
            
            [DataMember(Name = "file_type")]
            public string FileType { get; set; }
            
            [DataMember(Name = "file_ext")]
            public string FileExt { get; set; }
            
            [DataMember(Name = "doc_type")]
            public string DocType { get; set; }

            [DataMember(Name = "doc_issue_objstate")]
            public string DocIssueObjstate { get; set; }

            [DataMember(Name = "user_view_access")]
            public string UserViewAccess { get; set; }
            
            [DataMember(Name = "user_admin_access")]
            public string UserAdminAccess { get; set; }
        }
    }
}
