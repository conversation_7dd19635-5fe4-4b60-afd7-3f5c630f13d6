<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/header"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:background="@color/IfsDarkGreen"
        android:elevation="2dp">
        <TextView
            android:id="@+id/assistant_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/IfsWhite"
            android:gravity="center" />
        <TextView
            android:id="@+id/assistant_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/IfsWhite"
            android:gravity="center" />
    </LinearLayout>
    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        android:layout_weight="1"
        android:background="?android:colorBackground">
        <LinearLayout
            android:id="@+id/steps_list"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </ScrollView>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="right">
        <Button
            style="?android:attr/buttonBarButtonStyle"
            android:background="?android:attr/selectableItemBackground"
            android:id="@+id/done_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:text="Finish" />
    </LinearLayout>
</LinearLayout>
