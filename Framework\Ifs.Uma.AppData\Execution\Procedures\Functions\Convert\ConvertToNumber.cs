﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Convert
{
    internal sealed class ConvertToNumber : FwFunction
    {
        public const string FunctionNamespace = "Convert";
        public const string FunctionName = "ToNumber";
        public ConvertToNumber()
            : base(FunctionNamespace, FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return parameters[0].GetNumber();
        }
    }
}
