﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_doc_reference_object", Unique = true, Columns =
            nameof(DocClass) + "," +
            nameof(DocNo) + "," +
            nameof(DocSheet) + "," +
            nameof(DocRev) + "," +            
            nameof(KeyRef) + "," +
            nameof(LuName)
        )]
    [Relation(ReferencedRowType = typeof(DocIssue),
        Columns =
            nameof(DocClass) + "," +
            nameof(DocNo) + "," +
            nameof(DocSheet) + "," +
            nameof(DocRev),
        ReferencedColumns =
            nameof(DocIssue.DocClass) + "," +
            nameof(DocIssue.DocNo) + "," +
            nameof(DocIssue.DocSheet) + "," +
            nameof(DocIssue.DocRev)
        )]
    [ObjConnection(LuNameColumn = nameof(LuName), KeyRefColumn = nameof(KeyRef))]
    public class DocReferenceObject : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "doc_reference_object";

        #region Field Definitions

        private string _docClass;
        private string _docNo;
        private string _docSheet;
        private string _docRev;
        private string _keyRef;
        private string _f1KeyRef;
        private string _luName;
        private string _title;
        private string _documentAccess;
        private string _docIssueObjstate;
        private string _oldDocSheet;
        private string _oldDocRev;
        private string _surveyLockedFlag;

        #endregion

        public DocReferenceObject()
            : base(DbTableName)
        {
            EntitySetName = "DocReferenceObjects";
        }

        #region Property Definitions

        // The order of ServerPrimaryKey columns listed here must match the server
        // for client primary keys to work

        [Column(Storage = nameof(_docClass), Mandatory = true, ServerPrimaryKey = true)]
        public string DocClass
        {
            get => _docClass;
            set => SetProperty(ref _docClass, value);
        }

        [Column(Storage = nameof(_docNo), Mandatory = true, ServerPrimaryKey = true)]
        public string DocNo
        {
            get => _docNo;
            set => SetProperty(ref _docNo, value);
        }

        [Column(Storage = nameof(_docSheet), Mandatory = true, ServerPrimaryKey = true)]
        public string DocSheet
        {
            get => _docSheet;
            set => SetProperty(ref _docSheet, value);
        }

        [Column(Storage = nameof(_docRev), Mandatory = true, ServerPrimaryKey = true)]
        public string DocRev
        {
            get => _docRev;
            set => SetProperty(ref _docRev, value);
        }
        
        [Column(Storage = nameof(_keyRef), Mandatory = true, ServerPrimaryKey = true)]
        public string KeyRef
        {
            get => _keyRef;
            set => SetProperty(ref _keyRef, value);
        }

        [Column(Storage = nameof(_f1KeyRef))]
        public string F1KeyRef
        {
            get => _f1KeyRef;
            set => SetProperty(ref _f1KeyRef, value);
        }

        [Column(Storage = nameof(_luName), Mandatory = true, ServerPrimaryKey = true)]
        public string LuName
        {
            get => _luName;
            set => SetProperty(ref _luName, value);
        }

        [Column(Storage = nameof(_title))]
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        [Column(Storage = nameof(_documentAccess))]
        public string DocumentAccess
        {
            get => _documentAccess;
            set => SetProperty(ref _documentAccess, value);
        }

        [Column(Storage = nameof(_docIssueObjstate))]
        public string DocIssueObjstate
        {
            get => _docIssueObjstate;
            set => SetProperty(ref _docIssueObjstate, value);
        }

        [Column(Storage = nameof(_oldDocSheet))]
        public string OldDocSheet
        {
            get => _oldDocSheet;
            set => SetProperty(ref _oldDocSheet, value);
        }

        [Column(Storage = nameof(_oldDocRev))]
        public string OldDocRev
        {
            get => _oldDocRev;
            set => SetProperty(ref _oldDocRev, value);
        }

        [Column(Storage = nameof(_surveyLockedFlag))]
        public string SurveyLockedFlag
        {
            get => _surveyLockedFlag;
            set => SetProperty(ref _surveyLockedFlag, value);
        }
        #endregion
    }
}
