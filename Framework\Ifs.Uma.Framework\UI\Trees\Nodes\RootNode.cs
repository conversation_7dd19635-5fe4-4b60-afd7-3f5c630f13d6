﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Images;
using Ifs.Uma.Utility;
using Ifs.Uma.Services.Parameters;

namespace Ifs.Uma.Framework.UI.Trees.Nodes
{
    public class RootNode : Node
    {
        private readonly IList<EntityRecord> _descendants;

        public override string Title => Strings.Top;

        public override UmaImage Icon => IconUtils.CircleSolid.NewWithColor(UmaColors.IfsPurple);

        public override bool IsPromotableToRoot => true;

        public override bool IsSecondActionVisible => MainAction == TreeStructureMainAction.OpenNode;

        public override TreeStructureMainAction MainAction { get; }

        private bool _showIsRootPin = false;
        public override bool ShowIsRootPin
        {
            get => _showIsRootPin;
            set
            {
                if (_showIsRootPin != value)
                {
                    _showIsRootPin = value;
                    base.ShowIsRootPin = value;
                }
            }
        }

        public override UmaImage SecondActionIcon => IconUtils.Pin;

        public RootNode(NodeContext context, CpiTreeNode rootCpiNode, EntityQuery childrenQuery, TreeStructureMainAction mainAction)
            : base(null, context, null, rootCpiNode)
        {
            Id = nameof(RootNode);
            IsRoot = true;
            State.NextQuery = childrenQuery;
            MainAction = mainAction;
        }

        public RootNode(NodeContext context, CpiTreeNode rootCpiNode, IList<EntityRecord> descendants, TreeStructureMainAction mainAction)
            : base(null, context, null, rootCpiNode)
        {
            Id = nameof(RootNode);
            _descendants = descendants;
            MainAction = mainAction;
        }

        protected async override Task<IEnumerable<Node>> GetChildNodes()
        {
            if (_descendants == null)
            {
                return await GetChildNodesNormally();
            }

            return GetChildNodeFromRecords();
        }

        private bool _loadedShellNode;

        private IEnumerable<Node> GetChildNodeFromRecords()
        {
            List<Node> children = new List<Node>();

            if (!_loadedShellNode)
            {
                using (BeginUpdating())
                {
                    EntityRecord record = _descendants[0];
                    _descendants.RemoveAt(0);
                    if (_descendants.Count > 0)
                    {
                        children.Add(new ShellNode(this, Context, record, CpiTreeNode, _descendants));
                    }
                    else
                    {
                        children.Add(new Node(this, Context, record, CpiTreeNode));
                    }
                }
                _loadedShellNode = true;
            }

            return children;
        }

        private async Task<IEnumerable<Node>> GetChildNodesNormally()
        {
            List<Node> children = new List<Node>();
            if (State.NextQuery != null)
            {
                using (BeginUpdating())
                {
                    children = await GetChildrenForQuery(CpiTreeNode, State.NextQuery);
                }
            }
            return children;
        }
    }
}
