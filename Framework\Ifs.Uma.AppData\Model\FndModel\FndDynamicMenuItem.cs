﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndDynamicMenuItem : RemoteRow
    {
        public const string DbTableName = "fnd_dynamic_menu_item";

        [Column]
        public string Label { get; set; }

        [Column]
        public string Item { get; set; }

        [Column]
        public string Name { get; set; }

        [Column]
        public string Type { get; set; }

        [Column]
        public string Icon { get; set; }

        [Column]
        public int? Ordinal { get; set; }

        [Column]
        public string ShowIn { get; set; }

        public FndDynamicMenuItem()
            : base(DbTableName)
        {
        }
    }
}
