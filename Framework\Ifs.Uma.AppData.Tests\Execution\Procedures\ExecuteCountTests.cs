﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteCountTests : ProcedureTest
    {
        private const string TstCustomerEntityName = "TstCustomer";
        private const string CustomerNoAttributeName = "CustomerNo";

        [Test]
        public async Task CountFromEntitySet()
        {
            long count = await DoCount("CountCustomer");
            Assert.AreEqual(2, count);
        }

        [Test]
        public async Task CountFromArray()
        {
            RemoteRow customerRow = GetCustomer("501");

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Customer"] = customerRow;

            long count = await DoCount("CountFromArray", parameters);
            Assert.AreEqual(3, count);
        }

        [Test]
        public async Task CountFromArrayWithWhere()
        {
            RemoteRow customerRow = GetCustomer("501");

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Customer"] = customerRow;

            long count = await DoCount("CountFromArrayWithWhere", parameters);
            Assert.AreEqual(1, count);
        }

        [Test]
        public async Task CountWhereAliased()
        {
            long count = await DoCount("CountWhereAliased");
            Assert.AreEqual(2, count);
        }

        private async Task<long> DoCount(string functionName, Dictionary<string, object> parameters = null)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, functionName, parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return (long)result.Value;
        }

        private RemoteRow GetCustomer(string customerNo)
        {
            IMetadata metadata = Resolve<IMetadata>();

            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, TstCustomerEntityName);
            EntityQuery query = new EntityQuery(source);
            query.AddFilter(CustomerNoAttributeName, customerNo);

            IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
            FwDataContext ctx = (FwDataContext)dataContextProvider.CreateDataContext();
            return ctx.Query(query).FirstOrDefault()?.Row;
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteCountSchema", "Execution.Procedures.CustomerData");
        }
    }
}
