﻿using System.IO;
using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class StreamResource : AppResource
    {
        private string _resourceName;
        public override string ResourceName
        {
            get { return _resourceName; }
        }

        [DataMember]
        public string ResourceQuery { get; set; }

        [DataMember]
        public Stream ResourceStream { get; set; }

        public StreamResource(string resourceName) 
        {
            _resourceName = resourceName;
        }
    }
}
