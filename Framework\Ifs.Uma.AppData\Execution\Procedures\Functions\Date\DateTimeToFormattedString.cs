﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Date
{
    internal sealed class DateTimeToFormattedString : DateTimeFunction
    {
        public const string FunctionName = "ToFormattedString";

        public DateTimeToFormattedString()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime? input = parameters[0].GetTimestamp();
            string format = parameters[1].GetString();

            if (input.HasValue && !string.IsNullOrEmpty(format))
            {
                return input.Value.ToString(format);
            }

            return null;
        }
    }
}
