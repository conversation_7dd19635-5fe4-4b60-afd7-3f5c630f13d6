﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public sealed class DocumentListItem : AttachmentListItem
    {
        private readonly IDocumentHandler _documentHandler;

        public DocReferenceObject DocumentReference { get; set; }
        public EdmFile EdmFile { get; set; }
        public MobileDocClass DocumentClass { get; set; }

        public DocumentListItem(IDocumentHandler documentHandler, DocReferenceObject documentRevision, EdmFile edmFile, MobileDocClass documentClass)
        {
            _documentHandler = documentHandler;

            DocumentReference = documentRevision;
            EdmFile = edmFile;
            DocumentClass = documentClass;

            Description = documentRevision.Title;
            Status = edmFile.AttachmentStatus ?? AttachmentStatus.Unknown;
        }

        public override Task<ILocalFileInfo> GetFileInfoAsync()
        {
            return _documentHandler.GetLocalFileForDocumentAsync(EdmFile);
        }

        public override Task DownloadFileAsync()
        {
            return _documentHandler.RequestDownloadAsync(EdmFile);
        }
    }
}
