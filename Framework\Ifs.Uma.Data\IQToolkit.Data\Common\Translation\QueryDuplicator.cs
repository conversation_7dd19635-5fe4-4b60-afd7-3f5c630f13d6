﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Duplicate the query expression by making a copy with new table aliases
    /// </summary>
    internal class QueryDuplicator : DbExpressionVisitor
    {
        Dictionary<TableAlias, TableAlias> map = new Dictionary<TableAlias, TableAlias>();

        public static Expression Duplicate(Expression expression)
        {
            return new QueryDuplicator().Visit(expression);
        }

        protected override Expression VisitTable(TableExpression node)
        {
            if (node == null) return null;
            TableAlias newAlias = new TableAlias();
            this.map[node.Alias] = newAlias;
            return new TableExpression(newAlias, node.Entity);
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            if (node == null) return null;
            TableAlias newAlias = new TableAlias();
            this.map[node.Alias] = newAlias;
            node = (SelectExpression)base.VisitSelect(node);
            return new SelectExpression(newAlias, node.Columns, node.From, node.Where, node.OrderBy, node.GroupBy, node.IsDistinct, node.Skip, node.Take, node.IsReverse);
        }

        protected override Expression VisitColumn(ColumnExpression node)
        {
            if (node == null) return null;
            TableAlias newAlias;
            if (this.map.TryGetValue(node.Alias, out newAlias))
            {
                return new ColumnExpression(node.Type, newAlias, node.Name);
            }
            return node;
        }
    }
}