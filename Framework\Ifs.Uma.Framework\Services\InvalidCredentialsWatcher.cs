﻿using System;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Localization;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;
using System.Threading.Tasks;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.Framework.Services
{
    internal sealed class InvalidCredentialsWatcher : TransactionSyncEventWatcher
    {
        private readonly IDialogService _dialogService;
        private readonly IIfsConnection _connection;
        private readonly ITouchApp _app;
        private readonly ITransactionSyncDataHandler _dataHandler;
        private DateTime _lastNotifyTime;
        private bool _dialogOpen;

        public InvalidCredentialsWatcher(ILogger logger,
            ITransactionSyncService service,
            IDialogService dialogService,
            IIfsConnection connection,
            ITouchApp app,
            ITransactionSyncDataHandler dataHandler)
            : base(logger, service, ThreadOption.UIThread)
        {
            _lastNotifyTime = DateTime.MinValue;
            _dialogService = dialogService;
            _connection = connection;
            _app = app;
            _dataHandler = dataHandler;
            _dialogOpen = false;
        }

        protected override bool OnMatchSyncEventFilter(TransactionSyncEventArgs e)
        {
            return e.EventType == SyncEventType.SyncEnded;
        }

        protected override void OnSyncEvent(TransactionSyncEventArgs e)
        {
            if (IsBrokenByInvalidCredentials())
            {
                _ = CheckNotifyExpiry();
            }
            else
            {
                _lastNotifyTime = DateTime.MinValue;
            }
        }

        protected override void OnStart()
        {
            _connection.TouchAppsComms.IsBrokenChanged += TouchAppsComms_IsBrokenChanged;
            base.OnStart();
        }

        protected override void OnStop()
        {
            _connection.TouchAppsComms.IsBrokenChanged -= TouchAppsComms_IsBrokenChanged;
            base.OnStop();
        }

        private void TouchAppsComms_IsBrokenChanged(object sender, EventArgs e)
        {
            if (IsBrokenByInvalidCredentials())
            {
                UiContext.Post(async _ => await CheckNotifyExpiry(), null);
            }
        }

        private async Task CheckNotifyExpiry()
        {
            DateTime currentTime = DateTime.UtcNow;
            TimeSpan minimumNotifyPeriod = GetMinimumNotifyPeriod();
            if ((currentTime - _lastNotifyTime) > minimumNotifyPeriod && !_dialogOpen)
            {
                CustomButtonsResult result = CustomButtonsResult.Negative;

                if (DeviceInfo.OperatingSystem == OperatingSystem.iOS)
                {
                    _dialogOpen = true;
                    await Task.Delay(1500);
                }

                try
                {
                    _dialogOpen = true;
                    await _dialogService.WaitForDialogsToClose(ServiceCancelToken);

                    if (IsRunning)
                    {
                        result = await _dialogService.CustomButtons(Strings.RefreshTokenTitle, Strings.RefreshTokenMessage, Strings.Now, Strings.Later, true);
                    }
                }
                catch (Exception ex)
                {
                    LogException(ex);
                }
                finally
                {
                    _dialogOpen = false;
                }

                _lastNotifyTime = DateTime.UtcNow;

                if (result == CustomButtonsResult.Positive)
                {
                    Logger.Information("User selected to re-enter login credentials immediately");
                    await _app.RefreshLogin();
                }
                else
                {
                    Logger.Information("User selected to re-enter login credentials later");
                }
            }
        }

        private TimeSpan GetMinimumNotifyPeriod()
        {
            TransactionSyncParams syncParams = _dataHandler.GetSyncParams();
            TimeSpan syncSpan = syncParams.SyncInterval;
            if (syncSpan.Minutes >= 10)
            {
                return syncSpan;
            }
            else
            {
                return TimeSpan.FromMinutes(10);
            }
        }

        private bool IsBrokenByInvalidCredentials()
        {
            bool isBroken = _connection.TouchAppsComms.IsBroken;
            bool isRefreshTokenSimulation = _connection.TouchAppsComms.IsRefreshTokenSimulation;

            if (isBroken && isRefreshTokenSimulation)
            {
                return true;
            }
            else if (isBroken)
            {
                BrokenCause cause = _connection.TouchAppsComms.BrokenCause;
                if (cause.Reason == BrokenReason.InvalidCredentials)
                {
                    return true;
                }
            }
            return false;
        }
    }
}
