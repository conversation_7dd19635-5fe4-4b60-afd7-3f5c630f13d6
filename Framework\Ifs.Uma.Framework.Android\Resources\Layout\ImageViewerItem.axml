<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    android:requiresFadingEdge="horizontal">
    <ImageView android:id="@+id/ImageView"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="10dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"/>
    <TextView android:id="@+id/ImageTitle" 
        android:layout_width="match_parent" 
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"/>
</LinearLayout>
