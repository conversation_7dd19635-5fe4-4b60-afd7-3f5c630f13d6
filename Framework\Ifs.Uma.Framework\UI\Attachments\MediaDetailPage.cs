﻿using System.ComponentModel;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public class MediaDetailPage : PageBase
    {
        public const string PageName = "MediaDetailPage";

        public MediaFormData FormData { get; }

        public MediaDetailPage(IEventAggregator eventAggregator, 
            IDialogService dialogService, IMetadata metadata, IFileService fileService, 
            IAppParameters appParameters, IMediaHandler mediaHandler, IToastService toastService, 
            INavigator navigator)
            : base(eventAggregator, dialogService)
        {
            Name = PageName;
            Classification = PageClassification.MediaDetail;
            FormData = new MediaFormData(metadata, fileService, appParameters, mediaHandler, toastService, navigator, eventAggregator);
            FormData.PropertyChanged += FormData_PropertyChanged;
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter parameter)
        {
            AttachmentNavParam attachmentNavParam = parameter as AttachmentNavParam;
            MediaNavParam navParam = new MediaNavParam(attachmentNavParam.EntityName, attachmentNavParam.KeyRef); 
            await FormData.Load(navParam);
            Title = FormData.FormTitleText;
            return true;
        }

        private void FormData_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(FormData.FormTitleText))
            {
                Title = FormData.FormTitleText;
            }
            else if (e.PropertyName == nameof(FormData.HasChanges))
            {
                HasChanges = FormData.HasChanges;
            }
        }
    }
}
