﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Assistants;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.DynamicAssistants
{
    public sealed class DynamicAssistantPage : AssistantBase, IAssistantRunner
    {
        public const string StepFilePrefix = "Step_";

        private DynamicAssistantStep _currentStep;
        public DynamicAssistantStep CurrentStep
        {
            get { return _currentStep; }
            private set
            {
                if (_currentStep != value)
                {
                    if (_currentStep?.Record != null)
                    {
                        _currentStep.Record.DataChanged -= Record_DataChanged;
                        _currentStep.Record.PropertyChanged -= Record_PropertyChanged;
                    }

                    if (_currentStep?.Field != null)
                    {
                        _currentStep.Field.ValueChanged -= Field_ValueChanged;
                    }

                    if (_currentStep?.RemarkField != null)
                    {
                        _currentStep.RemarkField.ValueChanged -= RemarkField_ValueChanged;
                    }

                    if (_currentStep?.Commands != null)
                    {
                        _currentStep.Commands.CommandExecuted -= CommandExecuted;
                    }

                    if (_currentStep != null)
                    {
                        _currentStep.IsCurrentStep = false;
                        _currentStep.IsNextStepLoading = false;
                        _currentStep.ShouldNextCommandBeEnabled = true;
                    }

                    _currentStep = value;

                    if (_currentStep?.Field != null)
                    {
                        _currentStep.Field.ValueChanged += Field_ValueChanged;
                    }

                    if (_currentStep?.RemarkField != null)
                    {
                        _currentStep.RemarkField.ValueChanged += RemarkField_ValueChanged;
                    }

                    if (_currentStep?.Commands != null)
                    {
                        _currentStep.Commands.CommandExecuted += CommandExecuted;
                    }

                    int index = _steps.IndexOf(_currentStep?.DynamicStep) + 1;
                    ActiveStep = (index > 0) ? (int?)index : null;

                    UpdateCancelButton();
                    UpdateFinishButton();

                    IsActiveStepDirty = false;
                    IsActiveStepModified = false;
                    UpdateActiveStepValid();

                    if (_currentStep?.Record != null)
                    {
                        _currentStep.Record.DataChanged += Record_DataChanged;
                        _currentStep.Record.PropertyChanged += Record_PropertyChanged;
                    }

                    if (_currentStep != null)
                    {
                        _currentStep.IsCurrentStep = true;
                        _currentStep.ShouldNextCommandBeEnabled = true;
                    }

                    OnPropertyChanged(nameof(CurrentStep));
                    CurrentStepChanged?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public ViewableCollection<DynamicAssistantStep> StepsShown { get; } = new ViewableCollection<DynamicAssistantStep>();

        private FndDynamicAssistSetup _setup;
        public FndDynamicAssistSetup Setup
        {
            get { return _setup; }
            private set
            {
                if (_setup != value)
                {
                    _setup = value;
                    OnPropertyChanged(nameof(Setup));
                }
            }
        }

        public event EventHandler<EventArgs> CurrentStepChanged;

        private readonly IDataHandler _dataHandler;
        private readonly IMetadata _metadata;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IExpressionRunner _expressionRunner;
        private readonly IFileService _fileService;
        private readonly IMediaHandler _mediaHandler;
        private readonly ILovService _lovService;
        private readonly IAppParameters _appParameters;
        private readonly IRoamingProfile _roamingProfile;
        private readonly IInsightsLogger _insightsLogger;
        private readonly IPerfLogger _perfLogger;
        private readonly ElementCreator _elementCreator;
        private readonly IDialogService _dialogService;

        private MetadataPageNavParam _navParam;
        private readonly List<FndDynamicAssistStep> _steps = new List<FndDynamicAssistStep>();

        private bool _assistantStarted, _assistantCompleted;
        private bool _disposed = false;

        public DynamicAssistantPage(IEventAggregator eventAggregator, IDialogService dialogService, IDataHandler data, ILovService lovService, IMetadata metadata,
            ILogger logger, IPerfLogger perfLogger, IInsightsLogger insightsLogger, ElementCreator elementCreator, ICommandExecutor commandExecutor, IExpressionRunner expressionRunner,
            IFileService fileService, IMediaHandler mediaHandler, IAppParameters appParameters, IRoamingProfile roamingProfile)
            : base(eventAggregator, dialogService)
        {
            _dataHandler = data;
            _metadata = metadata;
            _commandExecutor = commandExecutor;
            _expressionRunner = expressionRunner;
            _elementCreator = elementCreator;
            _fileService = fileService;
            _mediaHandler = mediaHandler;
            _lovService = lovService;
            _appParameters = appParameters;
            _roamingProfile = roamingProfile;
            _insightsLogger = insightsLogger;
            _perfLogger = perfLogger;
            _dialogService = dialogService;

            Classification = PageClassification.DynamicAssistant;

            Data = new PageData(logger, metadata, data);
            Data.DefaultViewData.CommandsEnabledOnEmpty = true;
            Data.DefaultViewData.CommandsEnabledOnHasChanges = true;
            Data.Page = this;

            Commands = new AssistantCommandBlock(UpdatingState, metadata, this, commandExecutor, _expressionRunner)
            {
                PageData = Data
            };

            Commands.RestartCommandItem.IsVisible = Commands.RestartCommandItem.IsEnabled = false;
        }

        public async void OnPreviousStepClicked(DynamicAssistantStep step)
        {
            if (step == null)
            {
                await Commands.PreviousCommandItem.ExecuteAsync();
                return;
            }

            bool result = await _dialogService.Confirm(Strings.Previous, string.Format(Strings.GoToPreviousStep, step.DynamicStep.Label ?? step.DynamicStep.Name), Strings.Yes, ConfirmationType.Normal);
            if (result)
            {
                await GoToPreviousSpecificStep(step);
            }
        }

        public override async void OnNavigatedTo(NavigatedToArgs args)
        {
            base.OnNavigatedTo(args);

            MetadataPageNavParam param = args.GetNavParam<MetadataPageNavParam>();

            await LoadPageAsync(param);
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter param)
        {
            _navParam = param as MetadataPageNavParam;

            if (_navParam?.Page != null)
            {
                CpiAssistant assistant = _metadata.FindAssistant(_navParam.ProjectionName, _navParam.Page);
                if (assistant != null)
                {
                    try
                    {
                        using (_perfLogger.Track("PageLoad", _navParam.ProjectionName + "." + assistant.Name))
                        {
                            return await LoadAssistant(_navParam.ProjectionName, assistant);
                        }
                    }
                    catch (Exception ex)
                    {
                        await HandleException(ex);
                    }
                }
                else
                {
                    Logger.Error($"Could not find dynamic assistant '{_navParam.ProjectionName}.{_navParam.Page}'");
                }
            }

            return false;
        }

        public async Task<bool> LoadAssistantAsModal(string projectionName, CpiAssistant assistant)
        {
            IsModal = true;
            return await LoadAssistant(projectionName, assistant);
        }

        private async Task<bool> LoadAssistant(string projectionName, CpiAssistant assistant)
        {
            StatusIconObject = await StatusIcon.GetStatusIconAsync(_metadata, _dataHandler);

            ProjectionName = projectionName ?? throw new ArgumentNullException(nameof(projectionName));
            CpiAssistant = assistant ?? throw new ArgumentNullException(nameof(assistant));
            _steps.Clear();

            Name = CpiAssistant.Name;
            Data.PageSettings = _roamingProfile.GetSettings("DynamicAssistants", ProjectionName, assistant.Name);

            _insightsLogger?.TrackAppFeature("DynamicAssistant-" + ProjectionName + "." + assistant.Name);
            Logger.Trace("Navigate: DynamicAssistant-" + ProjectionName + "." + assistant.Name);

            _assistantStarted = false;
            _assistantCompleted = false;

            StepsShown.Clear();

            if (!assistant.IsDynamic)
            {
                throw new InvalidOperationException("Only dynamic assistants are supported in this page");
            }

            // The entity should allow Create and Update operations
            CpiEntity entity = _metadata.FindEntity(ProjectionName, CpiAssistant.Entity);
            CpiCrudType entityCrudType = _dataHandler.GetCrudType(ProjectionName, CpiAssistant.Entity);
            if (entity != null && !entityCrudType.CanUse(CpiCrudType.Create))
            {
                throw new InvalidOperationException($"Create CRUD action is not supported by the entity {CpiAssistant.Entity} for assistant {assistant.Name}");
            }

            Name = CpiAssistant.Name;
            Title = CpiAssistant.Label;

            if (!IsModal) // In modal assistants, we load the record before this point
            {
                ExecuteResult result = await Data.DefaultViewData.Record.LoadNewRecordAsync(ProjectionName, CpiAssistant.Entity);

                if (!await CheckExecuteResult(result))
                {
                    return false;
                }
            }

            if (!await SetupAndInitialize())
            {
                HasChanges = false;
                return false;
            }

            Dictionary<string, object> setupParams = new Dictionary<string, object>();
            foreach (KeyValuePair<string, object> item in CpiAssistant.DynamicSetupParams)
            {
                setupParams.Add(item.Key, Data.DefaultViewData.Record.ReadParamValue(item.Value?.ToString()));
            }

            ExecuteResult setupResult = await _dataHandler.PerformFunctionAsync(ProjectionName, CpiAssistant.DynamicSetupFunction, setupParams);

            if (setupResult.Exception != null)
            {
                await DialogService.ShowException(setupResult.Exception, showDetails: setupResult.Exception.GetType() != typeof(ProcedureErrorException), title: CpiAssistant.Label);
                return false;
            }

            if (setupResult.Value is FndDynamicAssistSetup setup)
            {
                Setup = setup;

                if (!string.IsNullOrEmpty(_setup.InitMetaFunction))
                {
                    ExecuteResult commands = await ExecuteMetaFunction(_setup.InitMetaFunction);

                    if (commands.Exception != null)
                    {
                        await DialogService.ShowException(commands.Exception, showDetails: commands.Exception.GetType() != typeof(ProcedureErrorException), title: CpiAssistant.Label);
                        return false;
                    }

                    if (commands.Value is IList commandsList)
                    {
                        await ExecuteDynamicCommands(commandsList);
                    }
                }

                if (!string.IsNullOrEmpty(_setup.StepsMetaFunction))
                {
                    ExecuteResult steps = await ExecuteMetaFunction(_setup.StepsMetaFunction);

                    if (steps.Exception != null)
                    {
                        await DialogService.ShowException(steps.Exception, showDetails: steps.Exception.GetType() != typeof(ProcedureErrorException), title: CpiAssistant.Label);
                        return false;
                    }

                    if (steps.Value is IList stepsList)
                    {
                        CreateSteps(stepsList);
                    }
                }

                if (!_steps.Any())
                {
                    await DialogService.Alert(null, "No steps defined for this assistant.");
                }
            }
            else
            {
                await DialogService.Alert(null, "Dynamic setup function did not return a 'FndDynamicAssistSetup' structure");
            }

            Commands.Load(CpiAssistant);

            Commands.RestartCommandItem.IsEnabled = false;
            Commands.RestartCommandItem.IsVisible = false;

            HasChanges = false;

            bool startStepIndexDefined = Setup.StartStep.HasValue || StartStep.HasValue;
            bool resumeSupported = Setup.ResumeSupported ?? false;

            int startStepIndex = 0;

            if (Setup.StartStep.HasValue)
            {
                startStepIndex = (int)Setup.StartStep.Value - 1;
            }
            else if (StartStep.HasValue)
            {
                startStepIndex = StartStep.Value - 1;
            }

            if (startStepIndex < 0 || startStepIndex >= _steps.Count)
            {
                Logger.Log($"Initial step ({startStepIndex}) is not in the range of active steps [1 .. {_steps.Count}].", MessageType.Error);
                startStepIndex = 0; // Reset
            }

            if (resumeSupported && startStepIndexDefined && startStepIndex > 0) // Resuming assistant
            {
                for (int i = 0; i < startStepIndex; i++)
                {
                    FndDynamicAssistStep step = _steps[i];
                    if ((step.Enabled ?? true) && (step.Visible ?? true))
                    {
                        DynamicAssistantStep dynStep = new DynamicAssistantStep(ProjectionName, step, _metadata, _expressionRunner, _fileService, _lovService, _appParameters);
                        if (await SwitchStep(dynStep))
                        {
                            Dictionary<string, string> defaultActionParams = CurrentStep.DynamicStep.DefaultValueActionParameters?.SplitToDictionary();
                            Dictionary<string, object> paramValues = new Dictionary<string, object>();

                            if (defaultActionParams != null)
                            {
                                foreach (KeyValuePair<string, string> item in defaultActionParams)
                                {
                                    paramValues.Add(item.Key, item.Value);
                                }
                            }

                            ExecuteResult result = await _dataHandler.PerformFunctionAsync(ProjectionName, step.DefaultValueAction, paramValues);
                            string defaultValue = result.Value?.ToString();
                            await ApplyRecordDefaultValues(defaultValue ?? step.DefaultValue, step.DefaultClobValue);

                            if (i != startStepIndex && dynStep != null)
                            {
                                dynStep.IsReadOnly = true;
                            }
                        }
                    }
                }
            }
            else
            {
                if (!_steps[startStepIndex].Enabled.GetValueOrDefault(true))
                {
                    Logger.Log($"Cannot start on initial step {startStepIndex} because it is not enabled.", MessageType.Error);
                    startStepIndex = 0; // Reset
                }
                else if (!_steps[startStepIndex].Visible.GetValueOrDefault(true))
                {
                    Logger.Log($"Cannot start on initial step {startStepIndex} because it is not visible.", MessageType.Error);
                    startStepIndex = 0; // Reset
                }
            }

            FndDynamicAssistStep firstStep = _steps[startStepIndex];
            while (!firstStep.Enabled.GetValueOrDefault(true) || !firstStep.Visible.GetValueOrDefault(true))
            {
                // Get the next step which is visible AND enabled
                firstStep = _steps[startStepIndex++];
            }

            if (await SwitchStep(new DynamicAssistantStep(ProjectionName, firstStep, _metadata, _expressionRunner, _fileService, _lovService, _appParameters)))
            {
                await ApplyRecordDefaultValues(firstStep.DefaultValue, firstStep.DefaultClobValue);

                // Force setting HasChanges
                OnHasChangesChanged();

                if (CpiAssistant.Autocomplete)
                {
                    await AutoCompleteStep();
                }
            }

            return true;
        }

        private async Task<bool> SetupAndInitialize()
        {
            if (!string.IsNullOrEmpty(_navParam?.Action) && CpiAssistant.Setups != null)
            {
                if (CpiAssistant.Setups.TryGetValue(_navParam.Action, out CpiCommand command))
                {
                    CommandOptions options = new CommandOptions();
                    options.Vars = _navParam.ActionParameters?.ToDictionary();
                    if (!await command.TryRunAsync(ProjectionName, Data.DefaultViewData, _commandExecutor, options))
                    {
                        return false;
                    }
                }
            }

            return await CpiAssistant.InitCommand.TryRunAsync(ProjectionName, Data.DefaultViewData, _commandExecutor);
        }

        private async Task<ExecuteResult> ExecuteMetaFunction(string functionDef)
        {
            string funcName = functionDef;
            Dictionary<string, object> paramsDict = null;

            int openingBracketIndex = functionDef.IndexOf('(');
            if (openingBracketIndex > 0) // Contains parameters
            {
                funcName = functionDef.Substring(0, openingBracketIndex);
                string funcParams = functionDef.Substring(openingBracketIndex + 1, functionDef.IndexOf(')') - openingBracketIndex - 1);

                CpiProc proc = _metadata.FindProcedure(ProjectionName, $"Function<{funcName}>");

                if (proc == null)
                {
                    throw new InvalidOperationException($"Meta function {funcName} could not be found");
                }

                paramsDict = funcParams.SplitToDictionary().ToDictionary(x => x.Key, x => (object)x.Value);

                if (paramsDict.Keys.Count != proc.Parameters.Length)
                {
                    throw new InvalidOperationException($"Invalid number of parameters sent to meta function {funcName}. Parameters need to be sent in the format: MetaFuncName(Param1:Value1,Param2:Value2)");
                }
            }

            return await _dataHandler.PerformFunctionAsync(ProjectionName, funcName, paramsDict);
        }

        private void CommandExecuted(object sender, Commands.CommandExecutedEventArgs e)
        {
            if (IsModal && !e.Result.Failed && e.Result?.Value != null)
            {
                Result = e.Result;
                OnClosed(EventArgs.Empty);
            }
        }

        private void CreateSteps(IList stepsList)
        {
            _steps.AddRange(stepsList.Cast<FndDynamicAssistStep>());

            if (CpiAssistant.Cancel != null)
            {
                CancelStep = new AssistantStep(_metadata, _commandExecutor, _expressionRunner, _elementCreator, ProjectionName, Data, this, CpiAssistant.Cancel);
            }

            if (CpiAssistant.Final != null)
            {
                FinalStep = new AssistantStep(_metadata, _commandExecutor, _expressionRunner, _elementCreator, ProjectionName, Data, this, CpiAssistant.Final);
            }

            OnStepsLoaded(EventArgs.Empty);
        }

        private async Task ExecuteDynamicCommands(IList commands)
        {
            foreach (FndDynamicCommandDef command in commands)
            {
                CpiCommand cpiCmd = CommandExecutor.CreateDynamicCommand(command);
                await _commandExecutor.ExecuteAsync(ProjectionName, Data.DefaultViewData, cpiCmd);
            }
        }

        private async Task<bool> SwitchStep(DynamicAssistantStep step, bool navigatingBackwards = false)
        {
            if (!navigatingBackwards)
            {
                step.Record = Data.DefaultViewData.Record.CreateNew();
                ExecuteResult result = await step.Record.LoadNewRecordAsync(ProjectionName, CpiAssistant.Entity);

                if (!await CheckExecuteResult(result))
                {
                    return false;
                }

                StepsShown.Add(step);
            }

            bool canNavigateBackwards = StepsShown.Count > 1;
            Commands.PreviousCommandItem.IsEnabled = canNavigateBackwards;
            Commands.PreviousCommandItem.IsVisible = canNavigateBackwards;

            Commands.NextCommandItem.IsEnabled = true;
            Commands.NextCommandItem.IsVisible = true;

            CurrentStep = step;

            return true;
        }

        private void UpdateCancelButton()
        {
            if ((_assistantStarted && !_assistantCompleted) || IsModal)
            {
                Commands.CancelCommandItem.IsEnabled = _expressionRunner.RunCheck(CpiAssistant.CancelCommand?.OfflineEnabled ?? CpiAssistant.CancelCommand?.Enabled, Data.DefaultViewData, true);
                Commands.CancelCommandItem.IsVisible = _expressionRunner.RunCheck(CpiAssistant.CancelCommand?.OfflineVisible ?? CpiAssistant.CancelCommand?.Visible, Data.DefaultViewData, Commands.CancelCommandItem.IsEnabled);
            }
            else
            {
                Commands.CancelCommandItem.IsEnabled = false;
                Commands.CancelCommandItem.IsVisible = false;
            }
        }

        private void UpdateFinishButton()
        {
            Commands.FinishCommandItem.IsEnabled = _currentStep?.DynamicStep != null && _currentStep.DynamicStep.FinishEnabled.GetValueOrDefault(false);
            Commands.FinishCommandItem.IsVisible = Commands.FinishCommandItem.IsEnabled;
        }

        private void Record_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(RecordData.HasChanges))
            {
                IsActiveStepDirty = CurrentStep.Record.HasChanges;
                IsActiveStepModified = IsActiveStepDirty;
            }
        }

        private void Record_DataChanged(object sender, EventArgs e)
        {
            IsActiveStepDirty = CurrentStep.Record.HasChanges;
            IsActiveStepModified = IsActiveStepDirty;
        }

        private void Field_ValueChanged(object sender, ValueChangedEventArgs e)
        {
            if (e.OldValue != e.NewValue)
            {
                IsActiveStepDirty = true;
                IsActiveStepModified = true;
                UpdateActiveStepValid();
            }
        }

        private void RemarkField_ValueChanged(object sender, ValueChangedEventArgs e)
        {
            if (e.OldValue != e.NewValue)
            {
                IsActiveStepDirty = true;
                IsActiveStepModified = true;
                UpdateActiveStepValid();
            }
        }

        private void UpdateActiveStepValid()
        {
            if (CurrentStep?.Field != null)
            {
                IsActiveStepValid = CurrentStep.Field.IsValid;
                if (CurrentStep.Field.ActualIsRequired && !CurrentStep.Field.ActualIsReadOnly && !CurrentStep.Field.HasValue)
                {
                    IsActiveStepValid = false;
                }
            }
            else
            {
                IsActiveStepValid = true;
            }
        }

        public async Task<bool> ExecuteAsync(AssistantCommand command)
        {
            try
            {
                switch (command)
                {
                    case AssistantCommand.Previous:
                        //If step is required it calls the GoToPreviousSpecificStep directly
                        return await GoToPreviousSpecificStep(null);
                    case AssistantCommand.Next:
                        return await GoToNextStep(false);
                    case AssistantCommand.Cancel:
                        return await CancelAssistant();
                    case AssistantCommand.Restart:
                        return await RestartAssistant();
                    case AssistantCommand.Finish:
                        {
                            bool doFinish = true;

                            if (!string.IsNullOrEmpty(CpiAssistant.FinishCommand.Message))
                            {
                                doFinish = await DialogService.CustomButtons(Strings.Finish, CpiAssistant.FinishCommand.Message, Strings.Yes, Strings.No) == CustomButtonsResult.Positive;
                            }

                            if (doFinish)
                            {
                                if (CurrentStep.Field?.HasValue == true)
                                {
                                    ExecuteResult result = await SaveDynamicStep();

                                    if (result.Exception != null)
                                    {
                                        await DialogService.ShowException(result.Exception, showDetails: result.Exception.GetType() != typeof(ProcedureErrorException), title: CpiAssistant.Label);
                                        return false;
                                    }
                                }

                                return await FinishAssistant(true);
                            }

                            return false;
                        }
                    default:
                        throw new ArgumentOutOfRangeException(nameof(command), command, null);
                }
            }
            catch (Exception ex)
            {
                await HandleException(ex);
                return false;
            }
        }

        private async Task<bool> GoToPreviousSpecificStep(DynamicAssistantStep step)
        {
            //Possibly change this if we don't require going back a single item
            if (step == null)
            {
                if (!StepsShown.Remove(StepsShown.LastOrDefault()))
                {
                    return false;
                }

                step = StepsShown.LastOrDefault();
            }
            else
            {
                bool remove = false;
                foreach (DynamicAssistantStep toRemoveStep in StepsShown.ToList())
                {
                    if (remove)
                    {
                        StepsShown.Remove(toRemoveStep);
                    }

                    if (toRemoveStep == step)
                    {
                        remove = true;
                    }
                }
            }

            _assistantStarted = true;

            await SwitchStep(step, true);

            CurrentStep.IsReadOnly = false;
            CurrentStep.Field?.NotifyFocusRequested();

            return true;
        }

        private async Task<bool> GoToNextStep(bool automatedCommand)
        {
            if (CurrentStep.Field != null && CurrentStep.DynamicStep.Required.GetValueOrDefault() == true && !CurrentStep.Field.HasValue)
            {
                await DialogService.Alert(CurrentStep.DynamicStep.Label, Strings.DynamicStepMandatory);
                return false;
            }

            CurrentStep.IsNextStepLoading = true;
            CurrentStep.ShouldNextCommandBeEnabled = false;

            ExecuteResult result = await SaveDynamicStep();

            if (result.Exception != null)
            {
                await DialogService.ShowException(result.Exception, showDetails: result.Exception.GetType() != typeof(ProcedureErrorException), title: CpiAssistant.Label);
                CurrentStep.IsNextStepLoading = false;
                CurrentStep.ShouldNextCommandBeEnabled = true;
                return false;
            }

            if (result.Value is FndDynamicNextStep step)
            {
                CurrentStep.IsReadOnly = true;

                bool hasNextStep = step.NextStep.HasValue && step.NextStep.Value > 0;

                if (hasNextStep && _steps.Count >= step.NextStep.Value)
                {
                    CurrentStep.Record.ResetHasChanges();
                    int sIdx = (int)step.NextStep.Value - 1;

                    FndDynamicAssistStep dynamicStep = _steps[sIdx];

                    while (!dynamicStep.Enabled.GetValueOrDefault(true) || !dynamicStep.Visible.GetValueOrDefault(true))
                    {
                        // Get the next step which is visible AND enabled
                        dynamicStep = _steps[sIdx++];
                    }

                    _assistantStarted = true;
                    DynamicAssistantStep assistantStep = new DynamicAssistantStep(ProjectionName, dynamicStep, _metadata, _expressionRunner, _fileService, _lovService, _appParameters);
                    assistantStep.IsLastStep = !step.NextStep.HasValue;
                    CurrentStep.LoopOccurrence = step.LoopOccurrence;

                    if (await SwitchStep(assistantStep))
                    {
                        await ApplyRecordDefaultValues(step.DefaultValue ?? dynamicStep.DefaultValue, step.DefaultClobValue ?? dynamicStep.DefaultClobValue);

                        // Force setting HasChanges
                        HasChanges = true;
                        OnHasChangesChanged();

                        CurrentStep.Field?.NotifyFocusRequested();

                        if (CpiAssistant.Autocomplete)
                        {
                            await AutoCompleteStep();
                        }
                    }
                }
                else if ((!hasNextStep && automatedCommand) || (CpiAssistant.Autocomplete && automatedCommand))
                {
                    CurrentStep.IsReadOnly = false;
                    CurrentStep.IsNextStepLoading = false;
                    CurrentStep.ShouldNextCommandBeEnabled = true;
                    CurrentStep.LoopOccurrence = step.LoopOccurrence;
                }
                else
                {
                    CurrentStep.Record.ResetHasChanges();
                    bool terminate = CurrentStep.DynamicStep.TerminateAllowed.GetValueOrDefault() && !string.IsNullOrEmpty(step.TerminateInfo);

                    if (terminate)
                    {
                        await DialogService.Alert(Title, step.TerminateInfo);
                    }

                    return await FinishAssistant(false);
                }

                return true;
            }
            else
            {
                await DialogService.Alert(null, "Save method must return a 'FndDynamicNextStep' structure in a Dynamic Assistant.");
                CurrentStep.IsNextStepLoading = false;
                CurrentStep.ShouldNextCommandBeEnabled = true;
                return false;
            }
        }

        private async Task<bool> CancelAssistant()
        {
            bool doCancel = await DialogService.CustomButtons(Strings.Cancel, CpiAssistant.CancelCommand?.Message ?? Strings.ConfirmCancel,
                Strings.Yes, Strings.No) == CustomButtonsResult.Positive;

            if (doCancel && CpiAssistant.CancelCommand != null)
            {
                ExecuteResult result = await _commandExecutor.ExecuteAsync(ProjectionName, Data.DefaultViewData, CpiAssistant.CancelCommand);
                doCancel = !result.Failed;
            }

            if (doCancel)
            {
                if (IsModal)
                {
                    Result = ExecuteResult.Cancel;
                    OnClosed(EventArgs.Empty);
                }
                else
                {
                    SwitchToCompleteStep(CancelStep);
                }

                return true;
            }

            return false;
        }

        private async Task<bool> FinishAssistant(bool isUserInteractionEndingTheAssistance)
        {
            bool success = true;
            Exception ex = null;
            IsUserInteractionEndingTheAssistance = isUserInteractionEndingTheAssistance;

            if (success && CpiAssistant.FinishCommand != null)
            {
                ExecuteResult result = await _commandExecutor.ExecuteAsync(ProjectionName, Data.DefaultViewData, CpiAssistant.FinishCommand);
                success = !result.Failed;
                ex = result.Exception;
            }

            if (success && _setup.FinishMetaFunction != null)
            {
                ExecuteResult commands = await ExecuteMetaFunction(_setup.FinishMetaFunction);
                success = !commands.Failed;
                ex = commands.Exception;

                if (success && commands.Value is IList commandsList)
                {
                    await ExecuteDynamicCommands(commandsList);
                }
            }

            if (success)
            {
                bool autoRestart = _expressionRunner.RunCheck(CpiAssistant.OfflineAutoRestart ?? CpiAssistant.AutoRestart, Data.DefaultViewData, false);

                if (autoRestart)
                {
                    return await RestartAssistant();
                }
                else
                {
                    if (!IsModal)
                    {
                        SwitchToCompleteStep(FinalStep);
                    }
                    else
                    {
                        if (HasFinalStep)
                        {
                            SwitchToCompleteStep(FinalStep);
                        }
                        else
                        {
                            Result = ExecuteResult.Ok;
                            OnClosed(EventArgs.Empty);
                        }
                    }
                }

                return true;
            }

            if (ex != null)
            {
                await DialogService.ShowException(ex, showDetails: ex.GetType() != typeof(ProcedureErrorException), title: CpiAssistant.Label);
            }

            return false;
        }

        private async Task<bool> RestartAssistant()
        {
            return await LoadAssistant(ProjectionName, CpiAssistant);
        }

        private async Task<ExecuteResult> SaveDynamicStep()
        {
            if (string.IsNullOrEmpty(CurrentStep.DynamicStep.SaveAction))
            {
                throw new InvalidOperationException("'SaveAction' must be defined for Dynamic Assistant steps");
            }

            if (CurrentStep.Field != null && CurrentStep.DynamicStep.BindAttribute != null)
            {
                object value = CurrentStep.Field.HasValue ? CurrentStep.Field.Value : null;
                if (CurrentStep.Field is ComboMultiSelectField && value != null)
                {
                    // convert the list to a string
                    string output = SelectedItemsToString((IEnumerable<object>)value);

                    // add enclosing square brackets
                    if (output != null && output.Length > 0)
                    {
                        output = string.Format("[{0}]", output);
                    }

                    value = output;
                }
                else if (value is ISelectableItem selectableItem)
                {
                    value = selectableItem.Value;
                }
                else if (CurrentStep.Field is FilePickerField && value != null && value is PickedFile file)
                {
                    value = file.Data;
                }
                else if (CurrentStep.Field is MediaPickerField mediaPickerField && value != null)
                {
                    if (_appParameters.IsNewSurveyPicSolution())
                    {
                        if (mediaPickerField.PickedFile.FileName != null)
                        {
                            value = mediaPickerField.PickedFile.FileName;

                            mediaPickerField.FileService = _fileService;
                            mediaPickerField.ImageOptions = new ImageOptions(_appParameters.GetPictureMaxDimension(),
                                                                             _appParameters.GetPictureMaxBytes(), _appParameters.IsExistingDeviceMediaAllowed());
                            mediaPickerField.ValueType = FilePickerValueType.ByteArray;

                            if (_appParameters.IsEnhancedMediaEnabled())
                            {
                                mediaPickerField.MaxVideoBytes = _appParameters.GetVideoMaxMb() != 0 ? Utils.ConvertMbToBytes(_appParameters.GetVideoMaxMb()) : Utils.ConvertMbToBytes(MediaHandler.DefaultVideoMaxMb);
                            }

                            using (Stream dataStream = await mediaPickerField.PickedFile.ReadAsync())
                            {
                                int index = _steps.IndexOf(_currentStep?.DynamicStep) + 1;
                                string uuid = Guid.NewGuid().ToString("N");
                                string originalFileName = mediaPickerField.PickedFile.FileName;
                                string combinedFileName = $"{StepFilePrefix}{index}_{uuid}_{originalFileName}";
                                value = combinedFileName;
                                await _mediaHandler.AddMediaFile(combinedFileName, dataStream);
                            }
                        }
                        else
                        {
                            value = null;
                        }
                    }
                }

                CurrentStep.Record.Assign(CurrentStep.DynamicStep.BindAttribute, value);
            }

            if (CurrentStep.RemarkField != null && CurrentStep.DynamicStep.RemarkAttribute != null)
            {
                CurrentStep.Record.Assign(CurrentStep.DynamicStep.RemarkAttribute, CurrentStep.RemarkField.Value);
            }

            Dictionary<string, string> saveActionParams = CurrentStep.DynamicStep.SaveActionParameters?.SplitToDictionary();
            Dictionary<string, object> paramValues = new Dictionary<string, object>();

            if (saveActionParams != null)
            {
                foreach (KeyValuePair<string, string> item in saveActionParams)
                {
                    paramValues.Add(item.Key, CurrentStep.Record.ReadParamValue(item.Value));
                }

                if (paramValues.ContainsKey("LoopOccurrence"))
                {
                    paramValues["LoopOccurrence"] = CurrentStep.LoopOccurrence;
                }
            }

            return await _dataHandler.PerformFunctionAsync(ProjectionName, CurrentStep.DynamicStep.SaveAction, paramValues);
        }

        private async Task ApplyRecordDefaultValues(string values, string clobValue = null)
        {
            if (!string.IsNullOrEmpty(values))
            {
                CurrentStep.Record.AssignParams(_expressionRunner.InterpolateString(values, Data.DefaultViewData.Record));

                if (CurrentStep.Field != null)
                {
                    object value = CurrentStep.Record[CurrentStep.DynamicStep.BindAttribute];

                    if (value != null && CurrentStep.Field is ComboMultiSelectField multiSelectField)
                    {
                        string formattedString = value.ToString();

                        // remove enclosing square brackets
                        if (formattedString != null && formattedString.Length > 2)
                        {
                            formattedString = formattedString.Substring(1, formattedString.Length - 2);

                            if (!string.IsNullOrEmpty(formattedString))
                            {
                                if (multiSelectField.ItemsSource is IEnumerable<ISelectableItem<object>> allItems)
                                {
                                    multiSelectField.Value = SelectedItemsFromString(formattedString, allItems);
                                }
                                else
                                {
                                    Logger.Log($"Could not set default value for multi-select field {multiSelectField.Label}", MessageType.Error);
                                    multiSelectField.Value = null;
                                }
                            }
                            else
                            {
                                multiSelectField.Value = null;
                            }
                        }
                        else
                        {
                            multiSelectField.Value = null;
                        }
                    }
                    else if (value != null && (CurrentStep.Field is ComboField || CurrentStep.Field is RadioGroupField))
                    {
                        SelectableItem<object>[] selectableItems = ((ComboField)CurrentStep.Field).ItemsSource as SelectableItem<object>[];
                        CurrentStep.Field.Value = selectableItems.Where(x => x.Value?.ToString() == value.ToString()).FirstOrDefault();

                        if (CurrentStep.Field.Value == null || (CurrentStep.Field.Value is ISelectableItem item && item.Value == null))
                        {
                            Logger.Log($"Could not set default value for enumeration field {CurrentStep.Field.Label}", MessageType.Error);
                        }
                    }
                    else if (value != null && MetadataExtensions.TryConvertToType(MetadataExtensions.GetDynamicDataType(CurrentStep.DynamicStep.Datatype),
                             value, out object output))
                    {
                        CurrentStep.Field.AllowDisplaySymobl = true;
                        CurrentStep.Field.Value = output;
                    }
                    else
                    {
                        CurrentStep.Field.Value = null;
                    }
                }

                if (CurrentStep.RemarkField != null)
                {
                    CurrentStep.RemarkField.Value = CurrentStep.Record[CurrentStep.DynamicStep.RemarkAttribute];
                }

                // Reset HasChanges property as default values have been set
                CurrentStep.Record.ResetHasChanges();
            }

            if (!string.IsNullOrEmpty(clobValue) && (CurrentStep.Field is MediaPickerField || CurrentStep.Field is SignatureField))
            {
                string name = clobValue.Substring(0, clobValue.IndexOf(':') + 1);
                string value = clobValue.Substring(clobValue.IndexOf(':') + 1);

                // clobValue can be either base64 string or file name
                if (!string.IsNullOrEmpty(value) && !CurrentStep.Field.HasValue)
                {
                    byte[] pictureData = null;
                    bool isFile = false;
                    string fileName = string.Empty;

                    try
                    {
                        pictureData = Convert.FromBase64String(value);
                    }
                    catch
                    {
                        isFile = true;
                    }

                    if (isFile)
                    {
                        ILocalFileInfo imageFile = await _mediaHandler.GetMediaFileForDisplayAsync(CurrentStep.DynamicStep.Entity, value,
                                CancellationToken.None);

                        if (imageFile != null)
                        {
                            pictureData = await GetImageDataAsync(imageFile);
                            fileName = System.IO.Path.GetFileName(imageFile.FilePath);
                        }
                        else
                        {
                             Logger.Log($"Could not set default value for field {CurrentStep.Field.Label}", MessageType.Error);
                        }
                    }

                    if (pictureData != null)
                    {
                        CurrentStep.Record.Assign(name, pictureData);

                        if (CurrentStep.Field is SignatureField signatureField)
                        {
                            signatureField.Value = pictureData;
                        }
                        else if (CurrentStep.Field is MediaPickerField imagePickerField)
                        {
                            imagePickerField.PickedFile = new PickedFile()
                            {
                                Data = pictureData,
                                FileName = fileName
                            };
                        }
                    }
                }

                // Reset HasChanges property as default values have been set
                CurrentStep.Record.ResetHasChanges();
            }
        }

        private async Task<byte[]> GetImageDataAsync(ILocalFileInfo imageFile)
        {
            if (imageFile != null && await imageFile.ExistsAsync())
            {
                using (Stream imageStream = await imageFile.OpenStreamAsync(LocalFileMode.OpenOrCreate, LocalFileAccess.Read))
                {
                    if (imageStream != null)
                    {
                        using (MemoryStream memoryStream = new MemoryStream())
                        {
                            await imageStream.CopyToAsync(memoryStream);
                            return memoryStream.ToArray();
                        }
                    }
                }
            }

            return null;
        }

        private async Task AutoCompleteStep()
        {
            if (CurrentStep != null &&
                !CurrentStep.IsCompleteStep &&
                !CurrentStep.IsLastStep &&
                (CurrentStep.Field == null || CurrentStep.Field.HasValue))
            {
                await GoToNextStep(true);
            }
        }

        protected override async void OnStoredDataChangedAsync(DataChangeSet changeSet)
        {
            base.OnStoredDataChangedAsync(changeSet);

            StatusIconObject = await StatusIcon.GetStatusIconAsync(_metadata, _dataHandler);
        }

        #region IDisposable Support

        protected override void OnClosed(EventArgs args)
        {
            Data.DefaultViewData.Record.ResetHasChanges();
            HasChanges = false;
            Dispose();

            base.OnClosed(args);
        }

        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            _disposed = true;

            // Unsubscribe from events
            if (_currentStep?.Record != null)
            {
                _currentStep.Record.DataChanged -= Record_DataChanged;
                _currentStep.Record.PropertyChanged -= Record_PropertyChanged;
            }

            if (_currentStep?.Field != null)
            {
                _currentStep.Field.ValueChanged -= Field_ValueChanged;
            }

            if (_currentStep?.RemarkField != null)
            {
                _currentStep.RemarkField.ValueChanged -= RemarkField_ValueChanged;
            }

            if (_currentStep?.Commands != null)
            {
                _currentStep.Commands.CommandExecuted -= CommandExecuted;
            }
        }

        #endregion

        private void SwitchToCompleteStep(AssistantStep step)
        {
            _assistantCompleted = true;

            Data.DefaultViewData.Record.ResetHasChanges();
            HasChanges = false;

            Commands.PreviousCommandItem.IsEnabled = false;
            Commands.PreviousCommandItem.IsVisible = false;

            Commands.NextCommandItem.IsEnabled = false;
            Commands.NextCommandItem.IsVisible = false;

            // Don't show Restart command if it's a modal assistant
            Commands.RestartCommandItem.IsVisible = !IsModal;
            Commands.RestartCommandItem.IsEnabled = !IsModal;

            StepsShown.Clear();

            if (step != null)
            {
                FndDynamicAssistStep endStep = new FndDynamicAssistStep()
                {
                    Label = step.CpiStep.Label,
                    Description = step.CpiStep.Description
                };

                DynamicAssistantStep dynStep = new DynamicAssistantStep(ProjectionName, endStep, _metadata, _expressionRunner, _fileService, _lovService, _appParameters)
                {
                    Elements = step.Elements,
                    Commands = step.Commands,
                    IsCompleteStep = true
                };

                StepsShown.Add(dynStep);
                CurrentStep = dynStep;
            }
            else
            {
                CurrentStep = null;
            }
        }

        private static string SelectedItemsToString(IEnumerable<object> items)
        {
            if (items == null)
            {
                return null;
            }

            return string.Join(",", items.Cast<ISelectableItem<object>>().Select(x => x.Value.ToString()));
        }

        private static List<ISelectableItem<object>> SelectedItemsFromString(string input, IEnumerable<ISelectableItem<object>> allItems)
        {
            List<ISelectableItem<object>> result = new List<ISelectableItem<object>>();

            if (string.IsNullOrEmpty(input) || allItems == null)
            {
                return result;
            }

            string[] items = input.Split(',');

            foreach (string s in items)
            {
                ISelectableItem<object> match = allItems.Where(x => x.Value.ToString() == s).FirstOrDefault();
                if (match != null)
                {
                    result.Add(match);
                }
            }

            return result;
        }
    }
}
