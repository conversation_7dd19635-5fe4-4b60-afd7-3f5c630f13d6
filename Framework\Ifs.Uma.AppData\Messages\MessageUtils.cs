﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.AppData.Messages
{
    public static class MessageUtils
    {
        private static readonly HashSet<string> SysTableNames = new HashSet<string>()
        {
            "client_profile_value",
            "mobile_client_param",
            "mobile_client_security",
            "edm_application",
            "edm_file",
            "doc_issue",
            "doc_reference_object",
            "mobile_doc_class",
            "media_library_item",
            "media_library",
            "mobile_object_connection_config",
            "media_item"
        };

        public static object DeserializeValue(IMetaDataMember dataMember, JToken value)
        {
            if (value.Type == JTokenType.Null ||
                value.Type == JTokenType.Undefined ||
                (value.Type == JTokenType.String && ((string)value) == string.Empty))
            {
                return null;
            }

            if (dataMember.Enumeration != null)
            {
                if (dataMember.Enumeration.EnumType != null)
                {
                    return dataMember.Enumeration.LocalValue((string)value);
                }
                else
                {
                    return (string)value;
                }
            }

            if (TypeHelper.GetTypeCode(TypeHelper.GetNonNullableType(dataMember.ColumnType)) == TypeCode.DateTime)
            {
                if (DateTime.TryParseExact((string)value, ObjectConverter.DateTimeFormat, CultureInfo.InvariantCulture,
                   DateTimeStyles.AssumeLocal, out DateTime result))
                {
                    return result;
                }
                else if (DateTimeOffset.TryParseExact((string)value, ObjectConverter.DateTimeZoneFormat,
                  CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal, out DateTimeOffset dateTimeOffset))
                {
                    // If a date is being sent in zulu format it will be saved as it is
                    // 24.1 onwards will get all timestamp and timestampUTC in this format
                    return dateTimeOffset.UtcDateTime;
                }
            }

            return value.ToObject(dataMember.ColumnType);
        }

        public static JObject RowToJObject(IMetaTable table, RemoteRow row, bool includeNull, Func<IMetaDataMember, bool> includeMember, bool shouldFormatTimeStampInZuluFormat = false, TimezoneRefType tzRefType = TimezoneRefType.Server, string tzRefColumn = null, bool isSiteTZEnabled = false)
        {
            JObject data = new JObject();

            foreach (IMetaDataMember member in table.DataMembers)
            {
                if (!CheckIncludeMember(member, includeMember))
                {
                    continue;
                }

                object value = member.GetValue(row);

                if (value == null && (!includeNull || member.Mandatory))
                {
                    continue;
                }

                if (value is DateTime dateTime && member.DateFormat == DateFormats.Date)
                {
                    value = dateTime.ToString(ObjectConverter.DateFormat, CultureInfo.CurrentCulture);
                }
                else if (shouldFormatTimeStampInZuluFormat && value is DateTime dateTime1 && (member.DateFormat == DateFormats.Timestamp || member.DateFormat == DateFormats.TimestampUtc))
                {
                    value = dateTime1.ToString(ObjectConverter.DateTimeZoneFormat, CultureInfo.CurrentCulture);
                }

                string attribName = RemoteNaming.ToServerColumnName(member.ColumnName);
                data.Add(attribName, value == null ? null : JToken.FromObject(value));
            }

            if (tzRefType == TimezoneRefType.Site && isSiteTZEnabled)
            {                
                object value = table.DataMembers.First(x => x.PropertyName == tzRefColumn).GetValue(row);

                if (data.ContainsKey(PlatformServices.TimezoneService.Objsite))
                {
                    data[PlatformServices.TimezoneService.Objsite] = value == null ? null : JToken.FromObject(value);
                }
                else
                {
                    data.Add(PlatformServices.TimezoneService.Objsite, value == null ? null : JToken.FromObject(value));
                }

                if (value == null)
                {
                    Logger.Current.Warning("Site timezone aware message for {0} sent with null {1} value. Falling back to server TZ", table.DisplayName, tzRefColumn);
                }
            }

            return data;
        }

        private static bool CheckIncludeMember(IMetaDataMember member, Func<IMetaDataMember, bool> includeMember)
        {
            if (member.Sync == SyncRule.Never)
            {
                return false;
            }

            if (member.PropertyName == nameof(RemoteRow.ObjId) || member.PropertyName == nameof(RemoteRow.ObjVersion))
            {
                return false;
            }

            if (includeMember == null || member.Sync == SyncRule.Always)
            {
                return true;
            }

            return includeMember(member);
        }

        public static RemoteRow JObjectToRow(IMetaTable table, IClientKeysMapper clientKeysMapper, JObject jRow)
        {
            if (jRow == null)
            {
                return null;
            }

            RemoteRow row = (RemoteRow)table.CreateRow();

            ApplyJObjectToRow(table, clientKeysMapper, jRow, row);

            return row;
        }

        public static void ApplyJObjectToRow(IMetaTable table, IClientKeysMapper clientKeysMapper, JObject jRow, RemoteRow row)
        {
            foreach (KeyValuePair<string, JToken> kvp in jRow)
            {
                string columnName = RemoteNaming.ToColumnName(kvp.Key);
                IMetaDataMember member = table.FindMemberByColumnName(columnName);
                if (member != null)
                {
                    object rowValue = DeserializeValue(member, kvp.Value);
                    member.SetValue(row, rowValue);
                }

                if (columnName.Equals("@odata.etag"))
                {
                    row.ETag = kvp.Value.ToString();
                }
            }

            clientKeysMapper.MapServerToClientKeys(row);
        }

        public static string JObjectToString(JObject obj)
        {
            using (StringWriter sw = new StringWriter(CultureInfo.InvariantCulture))
            {
                JsonTextWriter jw = new JsonTextWriter(sw);
                jw.Formatting = Formatting.None;
                jw.DateTimeZoneHandling = DateTimeZoneHandling.Unspecified;
                jw.DateFormatString = ObjectConverter.DateTimeFormat;
                obj.WriteTo(jw);
                return sw.ToString();
            }
        }

        public static JObject ParametersToJObject(IReadOnlyDictionary<string, object> parameters, bool encodeParams)
        {
            JObject obj = new JObject();
            AddParametersToJObject(parameters, obj, encodeParams);
            return obj;
        }

        public static JObject LobParametersToJObject(IReadOnlyDictionary<string, object> parameters, Dictionary<string, object> lobActionParamTypes, string lobId)
        {
            Dictionary<string, object> updatedParameters = new Dictionary<string, object>();

            foreach (KeyValuePair<string, object> param in parameters)
            {
                updatedParameters.Add(param.Key, param.Value);

                if (lobActionParamTypes.Any(x => x.Key == param.Key && (x.Value.ToString() == CpiDataType.LongText.ToString() || x.Value.ToString() == CpiDataType.Binary.ToString())))
                {
                    updatedParameters.Remove(param.Key);
                    string newKey = param.Key + "TempLobId";
                    JToken newValue = lobId;
                    updatedParameters.Add(newKey, newValue);
                }
            }

            JObject obj = new JObject();
            AddParametersToJObject(updatedParameters, obj, false);
            return obj;
        }

        public static void AddParametersToJObject(IReadOnlyDictionary<string, object> parameters, JObject obj, bool encodeParams)
        {
            foreach (KeyValuePair<string, object> kvp in parameters)
            {
                if (obj[kvp.Key] == null)
                {
                    string attribName = RemoteNaming.ToServerAttributeName(kvp.Key);
                    if (char.IsUpper(kvp.Key[0]))
                    {
                        attribName = RemoteNaming.ToServerColumnName(attribName);
                    }
                    object value = encodeParams && kvp.Value != null && kvp.Value.GetType() == typeof(string) ? Uri.EscapeDataString(kvp.Value.ToString()) : kvp.Value;
                    obj[attribName] = value == null ? null : JToken.FromObject(value);
                }
            }
        }

        public static object ParseToken(IMetadata metadata, IClientKeysMapper clientKeysMapper, CpiTypeInfo type, JToken result)
        {
            if (type != null && type.IsCollection && result is JArray items)
            {
                return ParseCollection(metadata, clientKeysMapper, type, items);
            }
            else
            {
                return ParseObject(metadata, clientKeysMapper, type, result);
            }
        }

        private static object ParseCollection(IMetadata metadata, IClientKeysMapper clientKeysMapper, CpiTypeInfo type, JArray items)
        {
            if (type.DataType == CpiDataType.Structure || type.DataType == CpiDataType.Entity)
            {
                List<RemoteRow> rows = new List<RemoteRow>();
                IMetaTable table = string.IsNullOrEmpty(type.SubType) ? null : metadata.GetTableForEntityName(type.SubType);
                if (table != null)
                {
                    foreach (JObject item in items.OfType<JObject>())
                    {
                        RemoteRow row = JObjectToRow(table, clientKeysMapper, item);
                        rows.Add(row);
                    }
                }
                return rows;
            }
            else
            {
                Type tokenType = type?.DataType.ToType();
                List<object> values = new List<object>();
                foreach (JToken item in items.OfType<JToken>())
                {
                    object value = tokenType != null ? item.ToObject(tokenType) : item.ToObject<object>();
                    values.Add(value);
                }
                return values;
            }
        }

        private static object ParseObject(IMetadata metadata, IClientKeysMapper clientKeysMapper, CpiTypeInfo type, JToken obj)
        {
            if (type != null &&
                (type.DataType == CpiDataType.Structure || type.DataType == CpiDataType.Entity))
            {
                IMetaTable table = string.IsNullOrEmpty(type.SubType) ? null : metadata.GetTableForEntityName(type.SubType);
                if (table != null)
                {
                    return JObjectToRow(table, clientKeysMapper, obj as JObject);
                }
                else
                {
                    return null;
                }
            }

            obj = obj.SelectToken("value");
            Type tokenType = type?.DataType.ToType();
            return tokenType != null ? obj.ToObject(tokenType) : obj.ToObject<object>();
        }

        public static IEnumerable<MessageTableData> ExtractTableData(IMetaModel metaModel, JObject data, ILogger logger)
        {
            List<MessageTableData> tableDatas = new List<MessageTableData>();
            Dictionary<string, HashSet<string>> missingFields = new Dictionary<string, HashSet<string>>();

            foreach (KeyValuePair<string, JToken> tableObject in data)
            {
                string tableName = ServerTableNameToClientTableName(tableObject.Key);
                IMetaTable table = metaModel.GetTable(tableName);

                if (table != null)
                {
                    if (tableObject.Value is JObject jRow)
                    {
                        // Recieved one row from the server
                        MessageTableData tableData = new MessageTableData();
                        tableData.TableName = tableName;
                        tableData.RowData = ExtractRowData(table, jRow, missingFields, logger);
                        tableDatas.Add(tableData);
                    }
                    else
                    {
                        if (tableObject.Value is JArray jTableRows)
                        {
                            // Recieved multiple rows from the server
                            foreach (JObject jTableRow in jTableRows)
                            {
                                MessageTableData tableData = new MessageTableData();
                                tableData.TableName = tableName;
                                tableData.RowData = ExtractRowData(table, jTableRow, missingFields, logger);
                                tableDatas.Add(tableData);
                            }
                        }
                    }
                }
                else if (!missingFields.ContainsKey(tableName))
                {
                    missingFields[tableName] = new HashSet<string>();
                }
            }

            if (logger != null)
            {
                foreach (KeyValuePair<string, HashSet<string>> tableMissingField in missingFields)
                {
                    if (tableMissingField.Value.Count == 0)
                    {
                        logger.Warning("JsonMessageIn-ExtractTableData: Failed to find local table {0}", tableMissingField.Key);
                    }
                    else
                    {
                        foreach (string missingField in tableMissingField.Value)
                        {
                            logger.Warning("JsonMessageIn-ExtractRowData: Failed to find data member in local table: {0}.{1}", tableMissingField.Key, missingField);
                        }
                    }
                }
            }

            return tableDatas;
        }

        public static string ServerTableNameToClientTableName(string tableName)
        {
            string value = string.Concat(tableName.ToCharArray()
                    .Select((x, i) => i > 0 && (char.IsUpper(x) && !tableName.Substring(i - 1, 1).Equals("$")) ? "_" + x.ToString() : x.ToString()))
                    .ToLower();

            if (SysTableNames.Contains(value))
            {
                return Model.FwDataContext.FwTablePrefix + value;
            }

            return value;
        }

        public static string ClientTableNameToServerTableName(string tableName)
        {
            string camelCase = tableName.ToCamelCase();
            return char.ToUpper(camelCase[0]) + camelCase.Substring(1);
        }

        private static MessageRowData ExtractRowData(IMetaTable table, JObject data, Dictionary<string, HashSet<string>> missingFields, ILogger logger)
        {
            MessageRowData rowData = new MessageRowData();
            Dictionary<string, object> values = new Dictionary<string, object>();
            rowData.ColumnData = values;

            foreach (KeyValuePair<string, JToken> value in data)
            {
                string columnName = RemoteNaming.ToColumnName(value.Key);
                IMetaDataMember dataMember = table.FindMemberByColumnName(columnName);
                if (dataMember != null)
                {
                    try
                    {
                        values[dataMember.ColumnName] = DeserializeValue(dataMember, value.Value);
                    }
                    catch (Exception)
                    {
                        logger.Error("JsonMessageIn-ExtractRowData: Failed to deserialize '{0}' into {1}.{2}", value.Value.ToString(), table.TableName, dataMember.ColumnName);
                        throw;
                    }
                }
                else
                {
                    if (!missingFields.TryGetValue(table.TableName, out HashSet<string> tableMissingFields))
                    {
                        tableMissingFields = new HashSet<string>();
                        missingFields[table.TableName] = tableMissingFields;
                    }

                    tableMissingFields.Add(value.Key);
                }
            }

            return rowData;
        }
    }
}
