﻿using System;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class ContextSubstitutionTests
    {
        private readonly DateTime _today = DateTime.Today;

        [TestCase]
        public void TestToday()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TODAY#", out date);
            Assert.AreEqual(DateTime.Today.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestTomorrow()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TOMORROW#", out date);
            Assert.AreEqual(_today.AddDays(1).ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestYesterday()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#YESTERDAY#", out date);
            Assert.AreEqual(_today.AddDays(-1).ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfThisWeek()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_THIS_WEEK#", out date);
            DateTime startOfThisWeek = _today.AddDays(-(int)_today.DayOfWeek);
            Assert.AreEqual(startOfThisWeek.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfThisMonth()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_THIS_MONTH#", out date);
            DateTime startOfThisMonth = new DateTime(_today.Year, _today.Month, 1);
            Assert.AreEqual(startOfThisMonth.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfThisYear()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_THIS_YEAR#", out date);
            DateTime startOfThisYear = new DateTime(_today.Year, 1, 1);
            Assert.AreEqual(startOfThisYear.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestEndOfThisWeek()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#END_OF_THIS_WEEK#", out date);
            DateTime endOfThisWeek = _today.AddDays(6 - (int)_today.DayOfWeek);
            Assert.AreEqual(endOfThisWeek.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestEndOfThisMonth()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#END_OF_THIS_MONTH#", out date);
            DateTime endOfThisMonth = new DateTime(_today.Year, _today.Month, DateTime.DaysInMonth(_today.Year, _today.Month));
            Assert.AreEqual(endOfThisMonth.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestEndOfThisYear()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#END_OF_THIS_YEAR#", out date);
            DateTime endOfThisYear = new DateTime(_today.Year, 12, 31);
            Assert.AreEqual(endOfThisYear.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase] // Invalid Input
        public void TestInvalid()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#INVALID#", out date);
            Assert.AreEqual(null, date);
        }

        [TestCase]
        public void TestTodayPlusOne()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TODAY+1#", out date);
            DateTime todayPlusOne = _today.AddDays(1);
            Assert.AreEqual(todayPlusOne.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestTodayMinusThree()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TODAY-3#", out date);
            DateTime todayPlusOne = _today.AddDays(-3);
            Assert.AreEqual(todayPlusOne.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestYesterdayPlusFour()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#YESTERDAY+4#", out date);
            DateTime yesterdayPlusFour = _today.AddDays(3);
            Assert.AreEqual(yesterdayPlusFour.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestTomorrowMinusOne()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TOMORROW-1#", out date);
            DateTime tomorrowMinusOne = _today;
            Assert.AreEqual(tomorrowMinusOne.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfThisWeekPlusTwo()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_THIS_WEEK+2#", out date);
            DateTime startOfThisWeekPlusTwo = _today.AddDays(-(int)_today.DayOfWeek + 2);
            Assert.AreEqual(startOfThisWeekPlusTwo.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfThisWeekMinusSeven()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_THIS_WEEK-7#", out date);
            DateTime startOfThisWeekMinusSeven = _today.AddDays(-(int)_today.DayOfWeek - 7);
            Assert.AreEqual(startOfThisWeekMinusSeven.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfNextYearPlusFive()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_NEXT_YEAR+5#", out date);
            DateTime startOfNextYear = new DateTime(DateTime.Today.Year + 1, 1, 1);
            DateTime startOfNextYearPlusFive = startOfNextYear.AddDays(5);
            Assert.AreEqual(startOfNextYearPlusFive.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfLastYearMinusNine()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_LAST_YEAR-9#", out date);
            DateTime startOfLastYear = new DateTime(DateTime.Today.Year - 1, 1, 1);
            DateTime startOfLastYearMinusNine = startOfLastYear.AddDays(-9);
            Assert.AreEqual(startOfLastYearMinusNine.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestEndOfNextMonthPlusThree()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#END_OF_NEXT_MONTH+3#", out date);
            DateTime nextMonth = DateTime.Today.AddMonths(1);
            DateTime endOfNextMonth = new DateTime(nextMonth.Year, nextMonth.Month, DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month));
            DateTime endOfNextMonthPlusThree = endOfNextMonth.AddDays(3);
            Assert.AreEqual(endOfNextMonthPlusThree.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void OutOfRangeOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TODAY+30#", out date);
            Assert.AreEqual(null, date);
        }

        [TestCase]
        public void InvalidOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TODAY ++ 3#", out date);
            Assert.AreEqual(null, date);
        }

        [TestCase]
        public void TestTodayPlusOneOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TODAY#+1", out date);
            DateTime todayPlusOne = _today.AddDays(1);
            Assert.AreEqual(todayPlusOne.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestTodayMinusThreeOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TODAY#-3", out date);
            DateTime todayPlusOne = _today.AddDays(-3);
            Assert.AreEqual(todayPlusOne.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestYesterdayPlusFourOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#YESTERDAY#+4", out date);
            DateTime yesterdayPlusFour = _today.AddDays(3);
            Assert.AreEqual(yesterdayPlusFour.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestTomorrowMinusOneOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#TOMORROW#-1", out date);
            DateTime tomorrowMinusOne = _today;
            Assert.AreEqual(tomorrowMinusOne.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfThisWeekPlusTwoOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_THIS_WEEK#+2", out date);
            DateTime startOfThisWeekPlusTwo = _today.AddDays(-(int)_today.DayOfWeek + 2);
            Assert.AreEqual(startOfThisWeekPlusTwo.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfThisWeekMinusSevenOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_THIS_WEEK#-7", out date);
            DateTime startOfThisWeekMinusSeven = _today.AddDays(-(int)_today.DayOfWeek - 7);
            Assert.AreEqual(startOfThisWeekMinusSeven.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfNextYearPlusFiveOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_NEXT_YEAR#+5", out date);
            DateTime startOfNextYear = new DateTime(DateTime.Today.Year + 1, 1, 1);
            DateTime startOfNextYearPlusFive = startOfNextYear.AddDays(5);
            Assert.AreEqual(startOfNextYearPlusFive.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestStartOfLastYearMinusNineOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#START_OF_LAST_YEAR#-9", out date);
            DateTime startOfLastYear = new DateTime(DateTime.Today.Year - 1, 1, 1);
            DateTime startOfLastYearMinusNine = startOfLastYear.AddDays(-9);
            Assert.AreEqual(startOfLastYearMinusNine.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }

        [TestCase]
        public void TestEndOfNextMonthPlusThreeOuterOffset()
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();
            contextSubstitutionVariable.GetCsV("#END_OF_NEXT_MONTH#+3", out date);
            DateTime nextMonth = DateTime.Today.AddMonths(1);
            DateTime endOfNextMonth = new DateTime(nextMonth.Year, nextMonth.Month, DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month));
            DateTime endOfNextMonthPlusThree = endOfNextMonth.AddDays(3);
            Assert.AreEqual(endOfNextMonthPlusThree.ToString("MM/dd/yyyy HH:mm:ss"), Convert.ToDateTime(date).ToString("MM/dd/yyyy HH:mm:ss"));
        }
    }
}
