﻿using System.Linq;
using Ifs.Uma.AppData.Model;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Attachment
{
    internal sealed class AttachmentGetMediaFileExtensions : AttachmentFunction
    {
        public const string FunctionName = "GetMediaFileExtensions";

        public AttachmentGetMediaFileExtensions()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            MobileClientParam param = context.DbDataContext.AppParameters.FirstOrDefault(x => x.Parameter == "SERVER_CAPABILITIES");

            if (param?.Value?.Contains("EnhancedMedia") == true)
            {
                const string parameter = "MEDIA_FILE_EXT_LIST";

                MobileClientParam extensions = context.DbDataContext.AppParameters.FirstOrDefault(x => x.Parameter == parameter);

                if (extensions?.Value != null)
                {
                    return extensions.Value;
                }
                else
                {
                    return new string[] { };
                }
            }
            else
            {
                string[] extensions = { ".PNG", ".JPG", ".JPEG", ".BMP", ".GIF", ".TIF" };
                return string.Join(",", extensions);
            }
        }
    }
}
