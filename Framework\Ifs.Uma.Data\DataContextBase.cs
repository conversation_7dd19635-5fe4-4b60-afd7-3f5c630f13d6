﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Xml;
using IQToolkit.Data.Common;
using IQToolkit.Data.Common.Language;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Data
{
    public interface IChangeSet
    {
        IEnumerable<object> Deletes { get; }
        IEnumerable<object> Inserts { get; }
        IEnumerable<object> Updates { get; }
    }

    public interface ITable : IQueryable, IEnumerable
    {
        DataContext Context { get; }
        IMetaTable Entity { get; }
        void Attach(object row);
        void Attach(string projectionName, object row);
        void Detach(object row);
        void DeleteOnSubmit(object row);
        void DeleteOnSubmit(string projectionName, object row);
        void InsertOnSubmit(object row);
        void InsertOnSubmit(string projectionName, object row);
        object GetOriginalRow(object row);
        IEnumerable<ModifiedMemberInfo> GetRowModifications(object row);
    }

    public interface ITable<T> : IQueryable<T>
    {
        IMetaTable Entity { get; }
        void Attach(T row);
        void Attach(string projectionName, T row);
        void Detach(T row);
        void DeleteOnSubmit(T row);
        void DeleteOnSubmit(string projectionName, T row);
        void InsertOnSubmit(T row);
        void InsertOnSubmit(string projectionName, T row);
        T GetOriginalRow(T row);
        IEnumerable<ModifiedMemberInfo> GetRowModifications(T row);
    }

    public class DataContextBase : IMapEnumeration
    {
        public DataContextBase(DbInternal db)
        {
            if (db == null) throw new ArgumentNullException("db");
            if (db.MappingSource == null) throw new ArgumentOutOfRangeException("db");
            Db = db;
            _model = db.MappingSource.GetModel(this.GetType());
            QueryLanguage language = new SqlBuilderLanguage(Builder);
            _provider = new DbEntityProvider(db, _model, language);
            _tables = new LockedDictionary<IMetaTable, Table>();
            _transitionPerformances = new SynchronisedCollection<Performance>();
            _transitionRows = new List<TrackedRow>();
        }

        public IMetaEnumeration GetEnumeration(Type enumType)
        {
            return Db.MappingSource.GetEnumeration(enumType);
        }

        public IMetaModel Model
        {
            get { return _model; }
        }

        public void CreateDatabase()
        {
            Db.CreateDatabase(_model);
        }

        public void DeleteDatabase()
        {
            Db.DropDatabase();
        }

        public void SubmitChanges(bool withTransition)
        {
            try
            {
                Provider.TransactionA((cmd) => SubmitChanges(cmd, withTransition));
                // if transaction successful (i.e. no exception) we can clear the changes
                ClearTransition();
            }
            catch
            {
                RollBackTransition();
                throw;
            }
        }

        public void PerformOnSubmit(string projectionName, string method, IReadOnlyDictionary<string, object> args, string entitySetName = "", string arraySourceName = "", string primaryKeyString = "", string sessionId = "")
        {
            PerformOnSubmit(projectionName, method, args, null, null, entitySetName, arraySourceName, primaryKeyString, sessionId);
        }

        public void PerformOnSubmit(string projectionName, string method, IReadOnlyDictionary<string, object> args, IReadOnlyDictionary<string, object> paramDataTypes, string transactionGroup, string entitySetName = "", string arraySourceName = "", string primaryKeyString = "", string sessionId = "")
        {
            if (string.IsNullOrEmpty(method)) throw new ArgumentNullException(nameof(method));

            if (!_transitionStart.HasValue)
            {
                _transitionStart = DateTime.Now;
            }

            _transitionPerformances.Add(new Performance(projectionName, method, args, paramDataTypes, transactionGroup, entitySetName, arraySourceName, primaryKeyString, sessionId));
        }

        public IChangeSet GetChangeSet()
        {
            ChangeSet result = new ChangeSet();
            lock (_transitionRows)
            {
                foreach (TrackedRow track in _transitionRows)
                {
                    result.AddChange(track);
                }
            }
            return result;
        }

        public Table<T> GetTable<T>()
        {
            return (Table<T>)GetTable(typeof(T));
        }

        public ITable GetTable(Type type)
        {
            return GetTableImpl(_model.GetEntity(type));
        }

        public ITable GetTable(IMetaTable metaTable)
        {
            if (metaTable == null) throw new ArgumentNullException(nameof(metaTable));
            if (!object.ReferenceEquals(metaTable.Model, _model)) throw new ArgumentException($"The {nameof(metaTable)}'s model must be the same as the DataContext's model");

            return GetTableImpl(metaTable);
        }

        public IReadOnlyDictionary<ObjPrimaryKey, object> FindAll(IEnumerable<ObjPrimaryKey> keys)
        {
            if (keys == null) throw new ArgumentNullException(nameof(keys));

            return Provider.CommandF(cmd =>
            {
                Dictionary<ObjPrimaryKey, object> results = new Dictionary<ObjPrimaryKey, object>();

                RowValueAccessor accessor = new RowValueAccessor();
                DbRowHandlerCache cache = new DbRowHandlerCache(cmd, Model, Builder, accessor, KeyChoice.DefaultKey);

                foreach (ObjPrimaryKey key in keys)
                {
                    if (key != null)
                    {
                        DbRowHandler handler = cache.GetHandler(key.Table);
                        object row = key.Table.CreateRow();

                        foreach (Tuple<IMetaDataMember, object> keyValue in key.Values)
                        {
                            keyValue.Item1.SetValue(row, keyValue.Item2);
                        }

                        if (handler.Select(row))
                        {
                            results[key] = row;
                        }
                    }
                }

                return results;
            });
        }

        public IReadOnlyDictionary<ObjKey, object> FindAll(IEnumerable<ObjKey> keys)
        {
            if (keys == null) throw new ArgumentNullException(nameof(keys));

            return Provider.CommandF(cmd =>
            {
                Dictionary<ObjKey, object> results = new Dictionary<ObjKey, object>();

                RowValueAccessor accessor = new RowValueAccessor();
                DbRowHandler handler = null;
                
                foreach (ObjKey key in keys)
                {
                    if (key == null || key.Values.Any(x => x.Item2 == null))
                    {
                        continue;
                    }

                    if (handler == null || !handler.Key.SequenceEqual(key.Values.Select(x => x.Item1)))
                    {
                        handler = new DbRowHandler(cmd, key.Table, Builder, accessor, key.Values.Select(x => x.Item1).ToArray());
                    }
                    
                    object row = key.Table.CreateRow();

                    foreach (Tuple<IMetaDataMember, object> keyValue in key.Values)
                    {
                        keyValue.Item1.SetValue(row, keyValue.Item2);
                    }

                    if (handler.Select(row))
                    {
                        results[key] = row;
                    }
                }

                return results;
            });
        }
        
        public object Find(ObjPrimaryKey key)
        {
            if (key == null) throw new ArgumentNullException(nameof(key));

            return Provider.CommandF(cmd =>
            {
                RowValueAccessor accessor = new RowValueAccessor();
                DbRowHandler handler = new DbRowHandler(cmd, key.Table, Builder, accessor, null);
                object row = key.Table.CreateRow();

                foreach (var keyValue in key.Values)
                {
                    keyValue.Item1.SetValue(row, keyValue.Item2);
                }

                return handler.Select(row) ? row : null;
            });
        }

        #region Implementation

        internal Table GetTableImpl(IMetaTable entity)
        {
            return _tables.GetOrAdd(entity, CreateTable);
        }

        protected virtual Table CreateTable(IMetaTable metaTable)
        {
            if (metaTable == null) throw new ArgumentNullException(nameof(metaTable));
            return (Table)Activator.CreateInstance(typeof(Table<>).MakeGenericType(metaTable.RowType), new object[] { this, _provider, metaTable });
        }

        protected SqlBuilder Builder
        {
            get { return Db.Factory.Builder; }
        }

        protected IDbEntityProvider Provider
        {
            get { return this._provider; }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1004:GenericMethodsShouldProvideTypeParameter",
            Justification = "Just as clear to specify the type as to provide an unused argument and suppress the message from that")]
        protected void ClearTable<T>(DbCommand command)
        {
            if (command == null) throw new ArgumentNullException(nameof(command));
            IMetaTable table = Model.GetTable(typeof(T));
            IDeleteSpec spec = SqlSpec.CreateDelete(table.TableName, null);
            Builder.BuildSql(command, spec);
            command.ExecuteNonQuery();
        }
        
        public void DropAppsTables()
        {
            Provider.TransactionA(DropAppsTables);
        }

        protected void DropAppsTables(DbCommand command)
        {
            if (command == null) throw new ArgumentNullException(nameof(command));
            IEnumerable<string> ddl = Builder.BuildDdl(Model, DdlChoice.DropTable, MetaTableClass.App);
            DdlStatement.Execute(command, ddl);
        }

        public void RecreateAppsTables()
        {
            Provider.TransactionA(RecreateAppsTables);
        }

        protected void RecreateAppsTables(DbCommand command)
        {
            if (command == null) throw new ArgumentNullException(nameof(command));
            IEnumerable<string> ddl = Builder.BuildDdl(Model, DdlChoice.CreateScript, MetaTableClass.App);
            DdlStatement.Execute(command, ddl);
        }

        internal virtual void InsertTransitions(DbCommand command, DateTime transitionStart,
            IEnumerable<TrackedRow> transitionData)
        {
        }

        public void ExecuteInTransaction(Action action)
        {
            Provider.TransactionA(cmd => action());
        }

        public void ExecuteInTransaction(Action<DbCommand> action)
        {
            Provider.TransactionA(action);
        }

        public IDbRowHandler<T> CreateDbRowHandler<T>(DbCommand command)
        {
            if (command == null) throw new ArgumentNullException(nameof(command));
            return DbRowHandler<T>.Create(command, Model, Builder);
        }

        public DbRowHandlerCache CreateDbRowHandlerCache(DbCommand command, IAccessValue accessor, KeyChoice key)
        {
            if (command == null) throw new ArgumentNullException(nameof(command));
            return new DbRowHandlerCache(command, Model, Builder, accessor, key);
        }

        internal class Performance
        {
            internal Performance(string projectionName, string method, IReadOnlyDictionary<string, object> arguments, IReadOnlyDictionary<string, object> paramDataTypes, string transactionGroup, string entitySetName = "", string arraySourceName = "", string primaryKeyString = "", string sessionId = "")
            {
                ProjectionName = projectionName;
                Method = method;
                Arguments = arguments;
                TransactionGroup = transactionGroup;
                ParamDataTypes = paramDataTypes;
                EntitySetName = entitySetName;
                ArraySourceName = arraySourceName;
                PrimaryKeyString = primaryKeyString;
                SessionId = sessionId;
            }

            public string ProjectionName { get; }
            public string Method { get; }
            public string TransactionGroup { get; }
            public string EntitySetName { get; }
            public string ArraySourceName { get; }
            public string PrimaryKeyString { get; }
            public IReadOnlyDictionary<string, object> ParamDataTypes { get; }
            public IReadOnlyDictionary<string, object> Arguments { get; }
            public string SessionId { get; }
        }

        internal IEnumerable<Performance> Performances { get { return _transitionPerformances; } }

        protected DbInternal Db { get; }

        private readonly DbEntityProvider _provider;
        private readonly LockedDictionary<IMetaTable, Table> _tables;
        private readonly IMetaModel _model;
        private DateTime? _transitionStart;
        private ICollection<Performance> _transitionPerformances;

        private readonly IList<TrackedRow> _transitionRows;

        internal int NextTrackId()
        {
            if (!_transitionStart.HasValue)
            {
                _transitionStart = DateTime.Now;
            }
            lock (_transitionRows)
            {
                _transitionRows.Add(null);
                return _transitionRows.Count;
            }
        }

        internal void AddTrackedRow(TrackedRow row)
        {
            if (row == null) throw new ArgumentNullException("row");
            int idx = row.Sequence - 1;
            lock (_transitionRows)
            {
                if (idx >= 0 && idx < _transitionRows.Count)
                {
                    _transitionRows[idx] = row;
                }
            }
        }

        internal void NullTrackedRow(int sequence)
        {
            int idx = sequence - 1;
            lock (_transitionRows)
            {
                if (idx >= 0 && idx < _transitionRows.Count)
                {
                    _transitionRows[idx] = null;
                }
            }
        }

        private void SubmitChanges(DbCommand command, bool withTransition)
        {
            DateTime transitionStart = _transitionStart.HasValue ? _transitionStart.Value : DateTime.Now;
            ICollection<TrackedRow> transitionData = new List<TrackedRow>();
            RowValueAccessor accessor = new RowValueAccessor();
            DbRowHandlerCache cache = new DbRowHandlerCache(command, Model, Builder, accessor, KeyChoice.DefaultKey);
            lock (_transitionRows)
            {
                foreach (TrackedRow track in _transitionRows)
                {
                    if (track == null) continue;
                    DbRowHandler handler = cache.GetHandler(track.Table);
                    track.ModifyRow(handler, accessor);
                    // if it's a kind of RemoteRow we write a Transition
                    if (typeof(RemoteRow).GetTypeInfo().IsAssignableFrom(track.Table.RowType.GetTypeInfo()))
                    {
                        transitionData.Add(track);
                    }
                }
            }
            // do we have transition data to worry about
            if (transitionData.Any() || Performances.Any())
            {
                if (withTransition)
                {
                    InsertTransitions(command, transitionStart, transitionData);
                }
            }
        }

        private void ClearTransition()
        {
            IEnumerable<IMetaTable> entities = GetTransitionEntities(true);
            foreach (IMetaTable entity in entities)
            {
                GetTableImpl(entity).ClearChanges();
            }
            _transitionPerformances.Clear();
            _transitionStart = null;
        }

        private IEnumerable<IMetaTable> GetTransitionEntities(bool clearChanges)
        {
            HashSet<IMetaTable> result = new HashSet<IMetaTable>();
            lock (_transitionRows)
            {
                foreach (TrackedRow row in _transitionRows)
                {
                    if (row != null)
                    {
                        result.Add(row.Table);
                    }
                }
                if (clearChanges)
                {
                    _transitionRows.Clear();
                }
            }
            return result;
        }

        private void RollBackTransition()
        {
            // SubmitChanges transaction failed
            // Any inserted rows with an AutoIncrement member must be reset
            foreach (IMetaTable entity in GetTransitionEntities(false))
            {
                IMetaDataMember autoIncrement = entity.AutoIncrement();
                if (autoIncrement != null)
                {
                    GetTableImpl(entity).ResetAutoIncrement(autoIncrement);
                }
            }
        }

        #endregion
    }

    public static class DataContextExtensions
    {
        public static void SubmitChanges(this DataContextBase context)
        {
            if (context != null)
            {
                context.SubmitChanges(true);
            }
        }

        public static void ExportData(this DataContextBase context, XmlWriter writer)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (writer == null) throw new ArgumentNullException(nameof(writer));

            writer.WriteStartElement(DataExportName);
            writer.WriteStartElement(DataName);
            foreach (IMetaTable table in context.Model.GetTables())
            {
                if (table.TableImplementation != TableImplementation.Table)
                {
                    continue;
                }

                Table dbTable = context.GetTableImpl(table);
                foreach (object row in dbTable.SortedRows)
                {
                    string elementName = table.TableName.Replace("fnd$", "fnd__");

                    writer.WriteStartElement(elementName);
                    foreach (IMetaDataMember member in table.DataMembers)
                    {
                        // AutoIncrement and Custom fields not supported for export
                        if (!member.AutoIncrement)
                        {
                            object value = member.GetValue(row);
                            if (value != null)
                            {
                                writer.WriteStartElement(member.ColumnName);
                                if (value.GetType().GetTypeInfo().IsEnum)
                                {
                                    writer.WriteValue(value.ToString());
                                }
                                else if (value is string)
                                {
                                    string strValue = (string)value;
                                    // Replace invalid characters
                                    strValue = XmlHelper.Instance.RemoveInvalidChars(strValue);
                                    writer.WriteString(strValue);
                                }
                                else 
                                {
                                    writer.WriteValue(value);
                                }
                                writer.WriteEndElement();
                            }
                        }
                    }
                    writer.WriteEndElement();
                }
            }
            writer.WriteEndElement();
            writer.WriteEndElement();
        }

        public static void ImportData(this DataContextBase context, XmlReader reader)
        {
            Predicate<Type> tableFilter = x => true;
            ImportData(context, reader, tableFilter);
        }

        public static void ImportData(this DataContextBase context, XmlReader reader, Predicate<Type> tableFilter)
        {
            if (context == null) throw new ArgumentNullException("context");
            if (reader == null) throw new ArgumentNullException("reader");
            if (tableFilter == null) throw new ArgumentNullException("tableFilter");

            reader.MoveToContent();
            if (reader.NodeType == XmlNodeType.Element && reader.Name == DataExportName)
            {
                while (reader.Read())
                {
                    if (reader.NodeType == XmlNodeType.Element && reader.Name == DataName)
                    {
                        while (reader.Read())
                        {
                            if (reader.NodeType == XmlNodeType.Element)
                            {
                                string tableName = reader.Name.Replace("fnd__", "fnd$");
                                IMetaTable table = context.Model.GetTable(tableName);

                                if (table != null && 
                                    table.TableImplementation == TableImplementation.Table &&
                                    tableFilter(table.RowType))
                                {
                                    object newRow = table.CreateRow();
                                    while (reader.Read())
                                    {
                                        if (reader.NodeType == XmlNodeType.Element)
                                        {
                                            string columnName = reader.Name;
                                            IMetaDataMember member = table.DataMembers.FirstOrDefault(x => x.ColumnName == columnName);
                                            if (member != null)
                                            {
                                                Type columnType = TypeHelper.GetNonNullableType(member.ColumnType);
                                                if (columnType.GetTypeInfo().IsEnum)
                                                {
                                                    string strEnum = reader.ReadElementContentAsString();
                                                    object value = Enum.Parse(columnType, strEnum, true);
                                                    member.SetValue(newRow, value);
                                                }
                                                else
                                                {
                                                    object value = reader.ReadElementContentAs(columnType, null);
                                                    member.SetValue(newRow, value);
                                                }
                                            }
                                        }
                                        else if (reader.NodeType == XmlNodeType.EndElement)
                                        {
                                            break;
                                        }
                                    }
                                    context.GetTableImpl(table).InsertOnSubmit(newRow);
                                }
                            }
                        }
                    }
                }
            }
        }

        private const string DataExportName = "dataexport";
        private const string DataName = "data";
    }
}
