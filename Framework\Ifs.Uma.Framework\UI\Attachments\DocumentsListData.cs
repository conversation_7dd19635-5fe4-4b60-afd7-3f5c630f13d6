﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.Framework.UI.Lists;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Localization;
using Ifs.Uma.Services.Attachments.Documents;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public sealed class DocumentsListData : ListData<DocumentListItem>, INavigatedTo
    {
        private readonly IDocumentHandler _documentHandler;
        private readonly INavigator _navigator;

        private string _title;
        public string Title
        {
            get => _title;
            private set => SetProperty(ref _title, value);
        }

        private bool _canAccessMedia;
        public bool CanAccessDocuments
        {
            get => _canAccessMedia;
            private set => SetProperty(ref _canAccessMedia, value);
        }

        public Command AddNew { get; }

        private AttachmentNavParam _navParam;

        public DocumentsListData(IEventAggregator eventAggregator, IDocumentHandler documentHandler, INavigator navigator) 
            : base(eventAggregator)
        {
            _documentHandler = documentHandler;
            _navigator = navigator;

            Title = Strings.Documents;
            AddNew = Command.FromMethod(OnAddNew);
            AddNew.IsEnabled = false;
        }

        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                EventAggregator.GetEvent<DocumentStatusChangedEvent>().Subscribe(OnDocumentStatusChanged, ThreadOption.UIThread);
            }
            else
            {
                EventAggregator.GetEvent<DocumentStatusChangedEvent>().Unsubscribe(OnDocumentStatusChanged);
            }
        }

        public async void OnNavigatedTo(NavigatedToArgs args)
        {
            AttachmentNavParam navParam = NavigationParameter.FromNavigationParameter<AttachmentNavParam>(args.Parameter);
            await Load(navParam);
        }

        public async Task Load(AttachmentNavParam navParam)
        {
            _navParam = navParam;

            CanAccessDocuments = navParam != null && await _documentHandler.IsEnabledFor(_navParam.EntityName);
            AddNew.IsEnabled = navParam != null && await _documentHandler.CanCreateNew(_navParam.EntityName, _navParam.KeyRef);

            await UpdateAsync();
        }

        protected override async Task OnUpdateAsync()
        {
            using (Items.DeferRefresh())
            {
                Items.Clear();

                if (_navParam != null && CanAccessDocuments)
                {
                    IEnumerable<DocRevisionInfo> rows = await _documentHandler.GetDocumentInfosAsync(_navParam.EntityName, _navParam.KeyRef);

                    foreach (DocRevisionInfo row in rows)
                    {
                        DocumentListItem item = new DocumentListItem(_documentHandler, row.DocumentRevision, row.EdmFile, row.DocumentClass);
                        ILocalFileInfo file = await _documentHandler.GetLocalFileForDocumentAsync(item.EdmFile);
                        bool fileExists = await file.ExistsAsync();
                        if (fileExists && item.Status == AppData.Model.AttachmentStatus.Unknown)
                        {
                            item.Status = AppData.Model.AttachmentStatus.Downloaded;
                        }
                        Items.Add(item);
                    }
                }

                Title = string.Format(Strings.DocumentsCount, Items.Count);
            }
        }

        protected override bool OnMatchSearchTerm(DocumentListItem item)
        {
            if (item.Description != null &&
                item.Description.IndexOf(SearchTerm, 0, StringComparison.CurrentCultureIgnoreCase) >= 0)
            {
                return true;
            }

            if (item.StatusLabel != null &&
                item.StatusLabel.IndexOf(SearchTerm, 0, StringComparison.CurrentCultureIgnoreCase) >= 0)
            {
                return true;
            }

            return false;
        }

        private void OnAddNew()
        {
            if (_navParam != null && CanAccessDocuments && AddNew.IsEnabled)
            {
                DocumentNavParam navParam = new DocumentNavParam(_navParam.EntityName, _navParam.KeyRef, null, _navParam.RevisionEnabled);
                _navigator.NavigateToAsync(FrameworkLocations.DocumentDetails, navParam);
            }
        }

        protected override void OnSelectedItemChanged()
        {
            base.OnSelectedItemChanged();

            if (SelectedItem != null && CanAccessDocuments)
            { 
                DocumentNavParam navParam = new DocumentNavParam(SelectedItem.DocumentReference.LuName, SelectedItem.DocumentReference.KeyRef, SelectedItem.DocumentReference.RowId, _navParam.RevisionEnabled);
                _navigator.NavigateToAsync(FrameworkLocations.DocumentDetails, navParam);
            }
        }

        private void OnDocumentStatusChanged(DocumentStatusChangedEventArgs args)
        {
            foreach (DocumentListItem item in Items)
            {
                if (item.EdmFile.RowId == args.EdmFileRowId)
                {
                    item.EdmFile.AttachmentStatus = args.Status;
                    item.Status = args.Status;
                }
            }
        }
    }
}
