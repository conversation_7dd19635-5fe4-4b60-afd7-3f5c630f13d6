﻿using System;
using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class PushRegistration : BaseResource
    {
        public override string ResourceName
        {
            get { return "MobileClientRuntime.svc/PushRegistry"; }
        }

        public override bool SingleResponse { get { return true; } }

        [DataMember]
        public string RegistrationId { get; set; }

        [DataMember]
        public string ClientOS { get; set; }

        [DataMember]
        public MobileContext MobileContext { get; set; }

        [DataMember]
        public string PnsHandle { get; set; }

        [DataMember]
        public DateTime? ExpirationTime { get; set; }

        public bool IsExpired
        {
            get { return ExpirationTime != null && ExpirationTime < DateTime.Now; }
        }

        public static PushRegistration Load(ISettings settings)
        {
            string pnsHandle = settings.Get("PnsHandle");
            if (string.IsNullOrEmpty(pnsHandle))
            {
                return null;
            }

            PushRegistration result = new PushRegistration();
            result.RegistrationId = settings.Get("RegistrationId");
            result.PnsHandle = pnsHandle;
            long time = settings.GetInt64("ExpirationTime", -1);
            result.ExpirationTime = time < 0 ? null : (DateTime?)new DateTime(time);
            return result;
        }

        public void Save(ISettings settings)
        {
            settings.Set("RegistrationId", RegistrationId);
            settings.Set("PnsHandle", PnsHandle);
            if (ExpirationTime.HasValue)
            {
                settings.Set("ExpirationTime", ExpirationTime.Value.Ticks);
            }
            else
            {
                settings.Set("ExpirationTime", null);
            }
        }

        public static void Delete(ISettings settings)
        {
            settings.Set("RegistrationId", null);
            settings.Set("PnsHandle", null);
            settings.Set("ExpirationTime", null);
        }
    }
}
