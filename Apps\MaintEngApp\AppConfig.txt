// Shared
Platforms=iOS,Android,Windows
Name=IFS MWO Maintenance
AppName=MaintEngApp
RedirectUri=ifsmwomaintenance
RemoteAssistance=true
SignatureService=false
LocationEnabled=true
LidarService=true
PushNotification=true

// iOS
iOSDisplayName=IFS MWO Maintenance
// Below must be the one having the InHouse suffix, the build script will remove it when building the store app
BundleIdentifier=com.ifs.cloud.Maintenance.InHouse
BundleName=IFS MWO Maintenance

// NOTE: iOS usage descriptions are always needed since we don't remove code for things like media attachments and location APIs
// So it's okay to add some dummy text for apps that don't really use those features
NSLocationWhenInUseUsageDescription=Engineers&apos; locations are used to make planning easier and to enable us to provide arrival estimates for assignments
NSLocationAlwaysAndWhenInUseUsageDescription=Engineers&apos; locations are used to make planning easier and to enable us to provide arrival estimates for assignments
NSCameraUsageDescription=Camera access is required to scan barcodes, add pictures to assignments, and for remote assistance calls
NSPhotoLibraryUsageDescription=Photo access is required to enable pictures to be added to assignments
NSPhotoLibraryAddUsageDescription=Photo access is required to save documents and media items
NSMicrophoneUsageDescription=This is used for remote assistance calls

// Android
AndroidDisplayName=IFS MWO Maintenance
AndroidPackageName=com.ifs.cloud.Maintenance

// Windows
WindowsDisplayName=IFS MWO Maintenance
WindowsDescription=IFS MWO Maintenance
WindowsShortName=IFS MWO Maintenance
IdentityName=IFS.IFSMWOMaintenance
PhoneProductId=5544ec44-5906-4625-bb25-c38f378e4dda
// Below are in Package.StoreAssociation and Package.xml
ReservedName=IFS MWO Maintenance
LandingUrl=https://developer.microsoft.com/dashboard/Application?appId=9P64RW32PMMH