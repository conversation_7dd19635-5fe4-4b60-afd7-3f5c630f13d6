﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Transactions;

namespace Ifs.Uma.Framework.Services
{
    internal sealed class TransactionFailedEvent : ITransactionFailedEvent
    {
        private IProcedureExecutor _procedureExecutor;
        private readonly IResolver _resolver;
        
        private const string EntityName = "EntityName";
        private const string Action = "Action";
        private const string ErrorMessage = "ErrorMessage";
        private const string Keys = "Keys";
        private const string Values = "Values";

        public TransactionFailedEvent(IResolver resolver)            
        {
            _resolver = resolver;
        }

        public async Task<bool> HandleFailedEvent(string eventName, string entityName, OperationType action, string errorMessage, List<string> keys, List<string> values)
        {
            if (eventName == null)
                throw new ArgumentNullException(nameof(eventName));

            if (_resolver.TryResolve(out IProcedureExecutor procedure))
            {
                _procedureExecutor = procedure;
            }

            Dictionary<string, object> parameters = new Dictionary<string, object>
            {
                [EntityName] = entityName,
                [Action] = action,
                [ErrorMessage] = errorMessage,
                [Keys] = keys,
                [Values] = values
            };

            bool status = true;

            if (_resolver.TryResolve(out IMetadata metadata))
            {
                foreach (CpiProjection projection in metadata.CpiMetaData.GetProjections())
                {
                    ExecuteResult result = await _procedureExecutor.CallEventAsync(projection.Name, eventName, parameters);
                    bool? resultValue = result.Value as bool?;
                    if (result.Failed || result == ExecuteResult.False || resultValue == false)
                    {
                        status = false;
                    }
                }
            }

            return status;
        }
    }
}
