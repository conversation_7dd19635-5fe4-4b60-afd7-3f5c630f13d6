<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical">
    <Ifs.Uma.UI.Images.UmaImageView
        android:id="@+id/header_icon"
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:layout_margin="16dp" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:orientation="vertical">
        <TextView
            android:id="@+id/header_app_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.AppCompat.Title"
            android:textDirection="locale"
            android:textColor="@color/IfsGrayDark" />
        <TextView
            android:id="@+id/header_app_version"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="@dimen/abc_text_size_medium_material"
            android:textColor="@color/IfsCustomGrayMedium" />
    </LinearLayout>
</LinearLayout>
