﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringRegexLikeTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringRegexLikeTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("12345", "12_45", ExpectedResult = false)]
        [TestCase("cat house dog", "%house%", ExpectedResult = false)]
        [TestCase("abcde", "ab[ch]de", ExpectedResult = true)]
        [TestCase("abhde", "ab[ch]de", ExpectedResult = true)]
        [TestCase("abab", "ABAB", ExpectedResult = false)]
        [TestCase("123456", null, ExpectedResult = false)]
        [TestCase(1, "1", ExpectedResult = true)]
        [TestCase(1.1, "1.1", ExpectedResult = true)]
        [TestCase(true, "true", ExpectedResult = false)]
        [TestCase(null, null, ExpectedResult = false)]
        public async Task<bool?> String_RegexLike(object input, string likePattern)
        {
            _params["TextInput"] = input;
            _params["LikePattern"] = likePattern;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexLike", _params);
            CheckResult(result);

            return result?.Value as bool?;
        }

        [Test]
        [TestCase("abab", "ABAB", "i", ExpectedResult = true)]
        [TestCase(true, "true", "i", ExpectedResult = true)]
        [TestCase("abab", "ABAB", null, ExpectedResult = false)]
        [TestCase("abab", "abab", null, ExpectedResult = true)]
        [TestCase(null, null, null, ExpectedResult = false)]
        public async Task<bool?> String_RegexLike3(object input, string likePattern, string regexOption)
        {
            _params["TextInput"] = input;
            _params["LikePattern"] = likePattern;
            _params["RegexOption"] = regexOption;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexLike3", _params);
            CheckResult(result);

            return result?.Value as bool?;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
