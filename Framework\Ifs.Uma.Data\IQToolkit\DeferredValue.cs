﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace IQToolkit
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Performance", "CA1815:OverrideEqualsAndOperatorEqualsOnValueTypes",
        Justification="Default is ok")]
    internal struct DeferredValue<T> : IDeferLoadable
    {
        private IEnumerable<T> m_source;
        private bool m_loaded;
        private T m_value;

        public DeferredValue(T value)
        {
            this.m_value = value;
            this.m_source = null;
            this.m_loaded = true;
        }

        public DeferredValue(IEnumerable<T> source)
        {
            this.m_source = source;
            this.m_loaded = false;
            this.m_value = default(T);
        }

        public void Load()
        {
            if (this.m_source != null)
            {
                this.m_value = this.m_source.SingleOrDefault();
                this.m_loaded = true;
            }
        }

        public bool IsLoaded
        {
            get { return this.m_loaded; }
        }

        public bool IsAssigned
        {
            get { return this.m_loaded && this.m_source == null; }
        }

        private void Check()
        {
            if (!this.IsLoaded)
            {
                this.Load();
            }
        }

        public T Value
        {
            get
            {
                this.Check();
                return this.m_value;
            }

            set
            {
                this.m_value = value;
                this.m_loaded = true;
                this.m_source = null;
            }
        }
    }
}