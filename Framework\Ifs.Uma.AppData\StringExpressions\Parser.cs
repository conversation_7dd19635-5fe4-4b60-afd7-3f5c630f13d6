﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Expressions;

namespace Ifs.Uma.AppData.StringExpressions
{
    internal sealed class Parser
    {
        // See http://techblogs/uxx/?p=2332
        // Lexer: http://stackoverflow.com/questions/673113/poor-mans-lexer-for-c-sharp
        // Parsing: http://www.craftinginterpreters.com/parsing-expressions.html

        private readonly Lexer _lexer;

        public Parser(Lexer lexer)
        {
            _lexer = lexer;
        }

        public Expression ToExpession()
        {
            if (!_lexer.Next())
            {
                return null;
            }

            return ParseExpression();
        }

        private Expression ParseExpression()
        {
            return Conditional();
        }

        private static readonly string[] ConditionalOps = new string[] { "?" };
        private static readonly string[] ConditionalElseOps = new string[] { ":" };

        private Expression Conditional()
        {
            Expression exp = LogicalOr();

            if (MatchSymbol(ConditionalOps, out Token op))
            {
                Expression thenExp = ParseExpression();

                if (!MatchSymbol(ConditionalElseOps, out Token elseOp))
                {
                    throw new ArgumentOutOfRangeException("Expected ':' after expression.");
                }

                Expression elseExp = ParseExpression();

                if (exp.Type == typeof(DynamicValue))
                {
                    exp = Expression.Convert(exp, typeof(bool), DynamicValue.ConvertToBooleanMethod);
                }

                exp = Expression.Condition(exp, thenExp, elseExp);
            }

            return exp;
        }

        private static readonly string[] LogicalOrOps = new string[] { "||" };

        private Expression LogicalOr()
        {
            Expression exp = LogicalAnd();

            while (MatchSymbol(LogicalOrOps, out Token op))
            {
                Expression right = LogicalAnd();
                exp = Expression.MakeBinary(ParseSymbol(op.Contents), exp, right);
            }

            return exp;
        }

        private static readonly string[] LogicalAndOps = new string[] { "&&" };

        private Expression LogicalAnd()
        {
            Expression exp = Equality();

            while (MatchSymbol(LogicalAndOps, out Token op))
            {
                Expression right = Equality();
                exp = Expression.MakeBinary(ParseSymbol(op.Contents), exp, right);
            }

            return exp;
        }

        private static readonly string[] EqualityOps = new string[] { "!=", "==" };

        private Expression Equality()
        {
            Expression exp = Comparison();

            while (MatchSymbol(EqualityOps, out Token op ))
            {
                Expression right = Comparison();
                exp = Expression.MakeBinary(ParseSymbol(op.Contents), exp, right);
            }

            return exp;
        }

        private static readonly string[] ComparisonOps = new string[] { ">", ">=", "<", "<=" };

        private Expression Comparison()
        {
            Expression exp = Addition();

            while (MatchSymbol(ComparisonOps, out Token op))
            {
                Expression right = Addition();
                exp = Expression.MakeBinary(ParseSymbol(op.Contents), exp, right);
            }

            return exp;
        }

        private static readonly string[] AdditionOps = new string[] { "-", "+" };

        private Expression Addition()
        {
            Expression exp = Multiplication();

            while (MatchSymbol(AdditionOps, out Token op))
            {
                Expression right = Multiplication();
                exp = Expression.MakeBinary(ParseSymbol(op.Contents), exp, right);
            }

            return exp;
        }

        private static readonly string[] MultiplicationOps = new string[] { "/", "*", "%" };

        private Expression Multiplication()
        {
            Expression exp = Unary();

            while (MatchSymbol(MultiplicationOps, out Token op))
            {
                Expression right = Unary();
                exp = Expression.MakeBinary(ParseSymbol(op.Contents), exp, right);
            }

            return exp;
        }

        private static readonly string[] NotOp = new string[] { "!" };

        private Expression Unary()
        {
            if (MatchSymbol(NotOp, out Token op))
            {
                Expression right = Unary();
                return Expression.Not(right);
            }

            return Call();
        }

        private Expression Call()
        {
            Expression exp = Primary();

            if (exp is VarAccessExpression identifier &&
                Match(TokenType.OpenParen, out _))
            {
                exp = FinishCall(identifier);
            }

            return exp;
        }

        private Expression FinishCall(VarAccessExpression identifier)
        {
            List<Expression> arguments = new List<Expression>();

            if (!Check(TokenType.CloseParen))
            {
                do
                {
                    Expression arg = ParseExpression();
                    arguments.Add(arg);
                }
                while (Match(TokenType.Comma, out _));
            }

            if (!Match(TokenType.CloseParen, out Token token))
            {
                throw new ArgumentOutOfRangeException("Expected ')' after aruments.");
            }

            return IfsExpression.Method(identifier.PropertyPath, arguments);
        }

        private Expression Primary()
        {
            Token token;

            if (Match(TokenType.False, out token))
            {
                return Expression.Constant(new DynamicValue(false));
            }

            if (Match(TokenType.True, out token))
            {
                return Expression.Constant(new DynamicValue(true));
            }

            if (Match(TokenType.Real, out token))
            {
                return Expression.Constant(new DynamicValue(double.Parse(token.Contents, CultureInfo.InvariantCulture)));
            }

            if (Match(TokenType.Integer, out token))
            {
                return Expression.Constant(new DynamicValue(int.Parse(token.Contents, CultureInfo.InvariantCulture)));
            }

            if (Match(TokenType.String, out token))
            {
                return Expression.Constant(new DynamicValue(token.Contents.Substring(1, token.Contents.Length - 2)));
            }

            if (Match(TokenType.OpenParen, out token))
            {
                Expression expr = ParseExpression();

                if (!Match(TokenType.CloseParen, out token))
                {
                    throw new ArgumentOutOfRangeException("Expected ')' after expression.");
                }

                return expr;
            }

            if (Match(TokenType.Identifier, out token))
            {
                return IfsExpression.VarAccess(token.Contents);
            }

            throw new ArgumentOutOfRangeException("Unexpected token " + _lexer.CurrentToken.Type);
        }

        private bool Check(TokenType tokenType)
        {
            return _lexer.CurrentToken.Type == tokenType;
        }

        private bool Match(TokenType tokenType, out Token token)
        {
            if (_lexer.CurrentToken.Type == tokenType)
            {
                token = _lexer.CurrentToken;
                _lexer.Next();
                return true;
            }

            token = Token.None;
            return false;
        }

        private bool MatchSymbol(string[] symbols, out Token token)
        {
            if (_lexer.CurrentToken.Type != TokenType.Symbol)
            {
                token = Token.None;
                return false;
            }

            foreach (string symbol in symbols)
            {
                if (_lexer.CurrentToken.Type == TokenType.Symbol && _lexer.CurrentToken.Contents == symbol)
                {
                    token = _lexer.CurrentToken;
                    _lexer.Next();
                    return true;
                }
            }

            token = Token.None;
            return false;
        }

        private ExpressionType ParseSymbol(string symbol)
        {
            switch (symbol)
            {
                case "==":
                    return ExpressionType.Equal;
                case "!=":
                    return ExpressionType.NotEqual;
                case "||":
                    return ExpressionType.OrElse;
                case "&&":
                    return ExpressionType.AndAlso;

                case ">":
                    return ExpressionType.GreaterThan;
                case ">=":
                    return ExpressionType.GreaterThanOrEqual;
                case "<":
                    return ExpressionType.LessThan;
                case "<=":
                    return ExpressionType.LessThanOrEqual;

                case "+":
                    return ExpressionType.Add;
                case "-":
                    return ExpressionType.Subtract;
                case "*":
                    return ExpressionType.Multiply;
                case "/":
                    return ExpressionType.Divide;
                case "%":
                    return ExpressionType.Modulo;

                case "!":
                    return ExpressionType.Not;

                default:
                    throw new ArgumentOutOfRangeException(nameof(symbol), symbol, null);
            }
        }
    }
}
