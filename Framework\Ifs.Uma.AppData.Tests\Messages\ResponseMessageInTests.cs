﻿using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data.Transitions;
using NUnit.Framework;
using MessageType = Ifs.Uma.AppData.Messages.MessageType;

namespace Ifs.Uma.AppData.Tests.Messages
{
    [TestFixture]
    public partial class ResponseMessageInTests : DataContextTest<FwDataContext>
    {
        [Test]
        public void ResponseByServerKey()
        {
            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            MobileClientParam appParam = new MobileClientParam();
            appParam.Parameter = "Param1";
            appParam.Value = "Value1";
            db.AppParameters.InsertOnSubmit(appParam);
            db.SubmitChanges(true);

            ctx.ExecuteMessage(MessageType.OTHER, @"
            {
              ""Response"": {
                ""mobile_client_param"": [
                  {
                    ""obj_id"": ""id1"",
                    ""obj_version"": ""v1"",
                    ""obj_key"": ""k1"",
                    ""parameter"": ""Param1"",
                  }
                ]
              }
            }");

            MobileClientParam[] appParams = db.AppParameters.OrderBy(x => x.Parameter).ToArray();

            Assert.AreEqual(1, appParams.Length);
            Assert.AreEqual("Param1", appParams[0].Parameter);
            Assert.AreEqual("id1", appParams[0].ObjId);
            Assert.AreEqual("v1", appParams[0].ObjVersion);
            Assert.AreEqual("k1", appParams[0].ObjKey);

            Assert.AreEqual(1, db.TransitionRows.Count());
            Assert.AreEqual(1, db.IgnoreMessageIn.Count());
        }

        [Test]
        public void ResponseByRelatedMessage()
        {
            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            MobileClientParam appParam = new MobileClientParam();
            appParam.Parameter = "Param1";
            appParam.Value = "Value1";
            db.AppParameters.InsertOnSubmit(appParam);
            db.SubmitChanges(true);

            TransitionRow transitionRow = db.TransitionRows.First();

            ctx.ExecuteMessage(MessageType.OTHER, @"
            {
              ""Response"": {
                ""mobile_client_param"": [
                  {
                    ""obj_id"": ""id1"",
                    ""obj_version"": ""v1"",
                    ""obj_key"": ""k1"",
                  }
                ]
              }
            }", transitionRow.RowId);

            MobileClientParam[] appParams = db.AppParameters.OrderBy(x => x.Parameter).ToArray();

            Assert.AreEqual(1, appParams.Length);
            Assert.AreEqual("Param1", appParams[0].Parameter);
            Assert.AreEqual("id1", appParams[0].ObjId);
            Assert.AreEqual("v1", appParams[0].ObjVersion);
            Assert.AreEqual("k1", appParams[0].ObjKey);

            Assert.AreEqual(0, db.TransitionRows.Count());
            Assert.AreEqual(1, db.IgnoreMessageIn.Count());
        }
    }
}
