﻿using System;
using System.Collections.Generic;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Execution.Procedures.Functions.Attachment;
using Ifs.Uma.AppData.Execution.Procedures.Functions.Convert;
using Ifs.Uma.AppData.Execution.Procedures.Functions.Date;
using Ifs.Uma.AppData.Execution.Procedures.Functions.List;
using Ifs.Uma.AppData.Execution.Procedures.Functions.Security;
using Ifs.Uma.AppData.Execution.Procedures.Functions.String;
using Ifs.Uma.AppData.Execution.Procedures.Functions.Sync;
using Ifs.Uma.AppData.Execution.Procedures.Functions.System;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions
{
    internal sealed class FwFunctions
    {
        private readonly Dictionary<Tuple<string, string, int>, FwFunction> _functions = new Dictionary<Tuple<string, string, int>, FwFunction>();

        public FwFunctions(ILogger logger, ILocationLogger locationLogger, IOnlineDataHandler onlineDataHandler, IMediaHandler mediaHandler)
        {
            AddFunction(new SystemDateTime(logger));
            AddFunction(new SystemTimeZone());
            AddFunction(new SystemCreateGuid());
            AddFunction(new SystemGetParameter());
            AddFunction(new SystemLogLocation(locationLogger));
            AddFunction(new SystemGetOnlineState(onlineDataHandler));
            AddFunction(new SystemGetUserName(onlineDataHandler));
            AddFunction(new SystemGetPkFromRowId());
            AddFunction(new SystemGetGpsState());
            AddFunction(new SystemGetServerKeys1());
            AddFunction(new SystemGetServerKeys2());
            AddFunction(new SystemHash());
            AddFunction(new SystemGetDeviceId(onlineDataHandler));

            AddFunction(new SecurityIsEntityReadGranted());
            AddFunction(new SecurityIsEntityWriteGranted());
            AddFunction(new SecurityIsMediaGranted());
            AddFunction(new SecurityIsDocumentGranted());

            AddFunction(new ListAdd());
            AddFunction(new ListClear());
            AddFunction(new ListCount());
            AddFunction(new ListRemove());
            AddFunction(new ListGet());
            AddFunction(new ListIndexOf());

            AddFunction(new DateTimeAddYears());
            AddFunction(new DateTimeAddMonths());
            AddFunction(new DateTimeAddDays());
            AddFunction(new DateTimeAddHours());
            AddFunction(new DateTimeAddMinutes());
            AddFunction(new DateTimeAddSeconds());
            AddFunction(new DateTimeDifferenceInDays());
            AddFunction(new DateTimeDifferenceInHours());
            AddFunction(new DateTimeTimestamp());
            AddFunction(new DateTimeTimestampUtc());
            AddFunction(new DateTimeTimestamp6());
            AddFunction(new DateTimeDate());
            AddFunction(new DateTimeDate1());
            AddFunction(new DateTimeDate3());
            AddFunction(new DateTimeTime1());
            AddFunction(new DateTimeTime3());
            AddFunction(new DateTimeToFormattedString());
            AddFunction(new DateTimeToOracleFormat());

            AddFunction(new StringLength());
            AddFunction(new StringSubStringLength());
            AddFunction(new StringLike());
            AddFunction(new StringRegexCount());
            AddFunction(new StringRegexCount3());
            AddFunction(new StringRegexCount4());
            AddFunction(new StringRegexLike());
            AddFunction(new StringRegexLike3());
            AddFunction(new StringRegexReplace());
            AddFunction(new StringRegexReplace4());
            AddFunction(new StringRegexSubString());
            AddFunction(new StringRegexSubString4());
            AddFunction(new StringRegexSubString5());
            AddFunction(new StringReplace());
            AddFunction(new StringToLower());
            AddFunction(new StringToUpper());
            AddFunction(new StringTrim());
            AddFunction(new StringTrim2());
            AddFunction(new StringTrimStart());
            AddFunction(new StringTrimStart2());
            AddFunction(new StringTrimEnd());
            AddFunction(new StringTrimEnd2());
            AddFunction(new StringTokenize());
            AddFunction(new StringLastIndexOf());
            AddFunction(new StringFirstIndexOf());
            AddFunction(new StringSpecifiedIndexOf());

            AddFunction(new ConvertToBoolean());
            AddFunction(new ConvertToInteger());
            AddFunction(new ConvertToNumber());
            AddFunction(new ConvertToString());
            AddFunction(new ConvertToTimestamp());

            AddFunction(new AttachmentCreateAndConnectDoc());
            AddFunction(new AttachmentCreateAndConnectDoc3());
            AddFunction(new AttachmentCreateAndConnectMedia());
            AddFunction(new AttachmentCreateAndConnectMedia3());
            AddFunction(new AttachmentCreateAndConnectBinaryMedia(mediaHandler));
            AddFunction(new AttachmentConnectDocument());
            AddFunction(new AttachmentConnectMedia());
            AddFunction(new AttachmentGetDocFileExtensions());
            AddFunction(new AttachmentGetMediaFileExtensions());

            AddFunction(new SyncInitiateTransactionSession());
            AddFunction(new SyncFinalizeTransactionSession());
            AddFunction(new SyncTransactionSessionExists());
            AddFunction(new SyncClearTransactionSession());
            AddFunction(new SyncClearAllTransactionSessions());
        }

        private void AddFunction(FwFunction function)
        {
            Tuple<string, string, int> key = new Tuple<string, string, int>(function.Namespace, function.Name, function.ParametersCount);
            _functions[key] = function;
        }

        public ExecuteResult Execute(ProcedureContext context, string functionNamespace, string functionName, object[] parameters)
        {
            Tuple<string, string, int> key = new Tuple<string, string, int>(functionNamespace, functionName, parameters?.Length ?? 0);

            FwFunction function = null;

            if (!_functions.TryGetValue(key, out function))
            {
                key = new Tuple<string, string, int>(functionNamespace, functionName, -1);
                _functions.TryGetValue(key, out function);
            }

            if (function == null)
            {
                throw context.Fail($"Failed to find matching framework method '{functionNamespace}.{functionName}'");
            }
            else
            {
                return function.Execute(context, parameters);
            }
        }
    }
}
