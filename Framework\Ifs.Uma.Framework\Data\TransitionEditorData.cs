﻿using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.UI.Forms;
using Ifs.Uma.Localization;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;
using Ifs.Uma.Data;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Ifs.Uma.Framework.Data
{
    public sealed class TransitionEditorData : ObservableBase
    {
        private readonly TransitionData _transition;
        private readonly IMetaModel _metaModel;
        private readonly List<TransitionChange> _changes;

        private readonly Form _form;
        public Form Form => _form;

        public TransitionEditorData(TransitionData transition, IMetaModel metaModel)
        {
            _transition = transition;
            _metaModel = metaModel;
            _changes = _transition.GetTransitionChanges().ToList();

            _form = new Form();
            _form.EditState = _transition.TransitionRow.Operation == OperationType.Insert ? FieldEditState.Insert : FieldEditState.Update;

            SetupForm();
        }

        private void SetupForm()
        {
            TextField transField = new TextField();
            transField.Name = Strings.Transaction;
            transField.Value = _transition.GetTransitionName(_metaModel);
            transField.IsReadOnly = true;

            _form.AllFields.Add(transField);
            _form.Fields.Add(transField);

            if (!string.IsNullOrEmpty(_transition.TransitionRow.ErrorMessage))
            {
                if (DeviceInfo.OperatingSystem == OperatingSystem.Windows)
                {
                    // Setting the validation does not look nice for this situation on
                    // windows so instead create a new field to show the invalid message

                    TextField transErrorField = new TextField();
                    transErrorField.Name = Strings.Error;
                    transErrorField.Value = _transition.TransitionRow.ErrorMessage;
                    transErrorField.IsReadOnly = true;

                    _form.AllFields.Add(transErrorField);
                    _form.Fields.Add(transErrorField);
                }
                else
                {
                    transField.IsValid = false;
                    transField.ValidationMessages.Add(_transition.TransitionRow.ErrorMessage);
                }
            }

            HeaderField dataHeaderField = new HeaderField();
            dataHeaderField.Name = Strings.TransactionData;
            dataHeaderField.Value = Strings.TransactionData;

            _form.AllFields.Add(dataHeaderField);
            _form.Fields.Add(dataHeaderField);

            SetupTransitionValues();
        }

        private void SetupTransitionValues()
        {
            ObjPrimaryKey keys = ObjPrimaryKey.FromKeyRef(_metaModel, _transition.TransitionRow.TableName, _transition.TransitionRow.PrimaryKeyString);
            Dictionary<string, string> keysDict = keys.Values.ToDictionary(v => v.Item1.PropertyName, v => v.Item2.ToString());

            foreach (KeyValuePair<string, string> key in keysDict)
            {
                TextField keyField = new TextField();
                keyField.Name = key.Key;
                keyField.Value = key.Value.Trim('\'');
                keyField.IsReadOnly = true;

                _form.AllFields.Add(keyField);
                _form.Fields.Add(keyField);
            }

            List<Field> fields = new List<Field>();

            foreach (TransitionChange change in _changes)
            {
                Field field;

                if (change.Member != null)
                {
                    field = FormBuilder.CreateFieldForMetaDataMember(change.Member);
                }
                else
                {
                    field = new TextField();
                    field.Name = change.FieldName;
                }

                field.Id = change.FieldName;
                field.SetBinding(change, nameof(TransitionChange.Value));
                fields.Add(field);
            }

            foreach (Field field in fields.OrderBy(x => x.Label))
            {
                _form.AllFields.Add(field);
                _form.Fields.Add(field);
            }
        }

        public async Task Send()
        {
            if (_changes == null)
            {
                await _transition.Resend();
            }
            else
            {
                await _transition.Resend(_changes);
            }
        }
    }
}
