﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements.Selectors
{
    public sealed class SelectorElement : RecordLoaderElement
    {
        private readonly IMetadata _metadata;
        private readonly ILovService _lovService;
        private readonly ILogger _logger;
        private readonly IDataHandler _data;
        private readonly IThemeService _themeService;
        private CpiSelector _selector;

        protected override BindingType BindingPropertyType => BindingType.Array;

        private string _title;
        public string Title
        {
            get { return _title; }
            set { SetProperty(ref _title, value); }
        }

        private UmaColor _backgroundColor;

        public UmaColor BackgroundColor
        {
            get { return _backgroundColor; }
            set { SetProperty(ref _backgroundColor, value); }
        }

        private bool _isOpenable;

        public bool IsOpenable
        {
            get { return _isOpenable; }
            set { SetProperty(ref _isOpenable, value); }
        }

        public SelectorElement(IMetadata metadata, ILovService lovService, ILogger logger, IDataHandler data, Ifs.Uma.UI.Services.IThemeService themeService)
            : base(metadata)
        {
            _metadata = metadata;
            _lovService = lovService;
            _logger = logger;
            _data = data;
            _themeService = themeService;
        }

        protected override CpiRecordLoader OnInitializeRecordLoader()
        {
            _selector = _metadata.FindSelector(ProjectionName, Content.Selector);

            if (_selector != null)
            {
                _selector.DatasourceEntitySet = Content.DatasourceEntitySet;
            }

            Label = _selector?.Label;
            return _selector;
        }

        public async Task OpenSelectorLov()
        {
            if (!IsOpenable)
            {
                return;
            }

            if (_lovService != null)
            {
                EntityQuery query = new EntityQuery(DataSource);

                if (IsPrimary)
                {
                    PageData.Filter?.Apply(query);
                }

                ViewData viewData = LoaderViewData;
                ObjPrimaryKey selectedRecordKey = viewData.Record?.ToPrimaryKey();

                ObjPrimaryKey newRecordKey = await _lovService.OpenLovAsync(query, _selector, Title, selectedRecordKey);

                if (newRecordKey != null)
                {
                    query.SetFilter(newRecordKey);

                    await LoadRecordAsync(false, query);
                    UpdateState();
                }
            }
        }

        protected override void OnRecordReady()
        {
            base.OnRecordReady();

            UpdateIsOpenable();
            UpdateState();
        }

        protected override void OnDataChanged()
        {
            base.OnDataChanged();

            UpdateState();
        }

        private void UpdateState()
        {
            Title = InterpolateString(_selector?.Label, LoaderViewData?.Record);
            BackgroundColor = _themeService.GetBrandingColorOrGraniteColor(BrandElement.AurenaPageSubHeader, "granite-color-background");
        }

        private void UpdateIsOpenable()
        {
            IsOpenable = !PageData.HasChanges && DataSource != null;  
        }

        protected override void OnGetSelectAttributes(ICollection<string> attributes)
        {
            base.OnGetSelectAttributes(attributes);

            AttributeFinder.FindInSelector(attributes, _metadata, ProjectionName, _selector);
        }
    }
}
