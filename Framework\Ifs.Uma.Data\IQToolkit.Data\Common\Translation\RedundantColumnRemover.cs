﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Removes duplicate column declarations that refer to the same underlying column
    /// </summary>
    internal class RedundantColumnRemover : DbExpressionVisitor
    {
        Dictionary<ColumnExpression, ColumnExpression> map;

        private RedundantColumnRemover()
        {
            this.map = new Dictionary<ColumnExpression, ColumnExpression>();
        }

        public static Expression Remove(Expression expression)
        {
            return new RedundantColumnRemover().Visit(expression);
        }

        protected override Expression VisitColumn(ColumnExpression node)
        {
            ColumnExpression mapped;
            if (this.map.TryGetValue(node, out mapped))
            {
                return mapped;
            }
            return node;
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            node = (SelectExpression)base.VisitSelect(node);

            BitArray removed = new BitArray(node.Columns.Count);
            bool anyRemoved = false;
            Dictionary<ColumnExpression, ColumnExpression> knownColumns = new Dictionary<ColumnExpression, ColumnExpression>();
            for (int i = 0, n = node.Columns.Count; i < n - 1; i++)
            {
                ColumnDeclaration cd = node.Columns[i];
                ColumnExpression exp = cd.Expression as ColumnExpression;
                if (exp != null)
                {
                    ColumnExpression mappedExp;
                    if (knownColumns.TryGetValue(exp, out mappedExp))
                    {
                        ColumnExpression cx = new ColumnExpression(cd.Expression.Type, node.Alias, cd.Name);
                        this.map.Add(cx, mappedExp);
                        removed.Set(i, true);
                        anyRemoved = true;
                    }
                    else
                    {
                        mappedExp = new ColumnExpression(cd.Expression.Type, node.Alias, cd.Name);
                        knownColumns.Add(exp, mappedExp);
                    }
                }
            }

            if (anyRemoved)
            {
                List<ColumnDeclaration> newDecls = new List<ColumnDeclaration>();
                for (int i = 0, n = node.Columns.Count; i < n; i++)
                {
                    if (!removed.Get(i))
                    {
                        newDecls.Add(node.Columns[i]);
                    }
                }
                node = node.SetColumns(newDecls);
            }
            return node;
        }
    }
}