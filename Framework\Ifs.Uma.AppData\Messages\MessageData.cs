﻿using System;
using System.Collections.Generic;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData.Messages
{
    public class MessageData
    {
        public IEnumerable<MessageTableData> TableData { get; set; }
    }

    public class MessageTableData
    {
        public string TableName { get; set; }
        public string PrimaryKeyString { get; set; }
        public string ETagString { get; set; }
        public MessageRowData RowData { get; set; }

        public string GetRowValue(string propertyName)
        {
            if (RowData?.ColumnData == null)
            {
                return null;
            }

            string columnName = RemoteNaming.ToServerAttributeName(propertyName);
            if (RowData.ColumnData.TryGetValue(columnName, out object value))
            {
                return value as string;
            }

            return null;
        }
    }

    public class MessageRowData
    {
        public IReadOnlyDictionary<string, object> Keys { get; set; }
        public IReadOnlyDictionary<string, object> ColumnData { get; set; }
        internal long? SyncRowId { get; set; }
    }

    public class MessageRowDataAccessor : IAccessValue
    {
        public MessageRowDataAccessor()
        {
            _additionalValues = new Dictionary<IMetaDataMember, object>();
        }

        public MessageRowDataAccessor(bool exludeServerPrimaryKey) 
            : this()
        {
            ExcludeServerPrimaryKey = exludeServerPrimaryKey;
        }

        public bool GetValue(IMetaDataMember member, object row, out object value)
        {
            if (member == null) throw new ArgumentNullException("member");
            if (row == null) throw new ArgumentNullException("row");
            MessageRowData data = row as MessageRowData;
            if (data == null) throw new ArgumentOutOfRangeException("row");

            if (ExcludeServerPrimaryKey && member.ServerPrimaryKey)
            {
                value = null;
                return false;
            }

            return data.ColumnData.TryGetValue(member.ColumnName, out value) ||
                _additionalValues.TryGetValue(member, out value);
       }

        public bool AlwaysFound { get { return false; } }
        public bool ReadOnly { get { return false; } }
        public bool ExcludeServerPrimaryKey { get; private set; }

        public void SetValue(IMetaDataMember member, object row, object value)
        {
            MessageRowData data = row as MessageRowData;
            if (data != null && member.AutoIncrement)
            {
                data.SyncRowId = value as long?;
            }
        }

        public void AddValue(IMetaDataMember member, object value)
        {
            if (member == null) throw new ArgumentNullException("member");
            _additionalValues[member] = value;
        }

        private IDictionary<IMetaDataMember, object> _additionalValues;
    }
}
