﻿using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteErrorTests : ProcedureTest
    {
        private const string TstCustomerEntityName = "TstCustomer";
        private static readonly string TstCustomerTableName = RemoteNaming.ToTableName(TstCustomerEntityName);

        [Test]
        public async Task BasicError()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "BasicError", null);

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Failed);

            ProcedureErrorException e = result.Exception as ProcedureErrorException;
            Assert.IsNotNull(e);
            Assert.AreEqual("Var1", e.Parameter);
            Assert.AreEqual("Invalid value 'TestErrorValue'", e.Message);
        }

        [Test]
        public async Task SubProcError()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "SubProcErrorOuter", null);

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Failed);

            ProcedureErrorException e = result.Exception as ProcedureErrorException;
            Assert.IsNotNull(e);
            Assert.AreEqual("Error:Message1", e.Message);
        }

        [Test]
        public async Task ActionTransactionRollback()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallActionAsync(TestOfflineProjection, "TransactionRollback", null);

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Failed);

            ProcedureErrorException e = result.Exception as ProcedureErrorException;
            Assert.IsNotNull(e);
            Assert.AreEqual("ErrorMessage2", e.Message);
            {
                // Make sure no row was created and saved to the DB
                IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
                FwDataContext ctx = dataContextProvider.CreateDataContext();

                IMetaTable metaTable = ctx.Model.GetTable(TstCustomerTableName);
                ITable<RemoteRow> table = (ITable<RemoteRow>)ctx.GetTable(metaTable);
                RemoteRow row = table.FirstOrDefault();
                Assert.IsNull(row);
            }
        }
        
        [Test]
        public async Task FunctionNoRollback()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "NoRollback", null);

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Failed);

            ProcedureErrorException e = result.Exception as ProcedureErrorException;
            Assert.IsNotNull(e);
            Assert.AreEqual("ErrorMessage2", e.Message);
            {
                // Make sure row was created and saved to the DB
                IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
                FwDataContext ctx = dataContextProvider.CreateDataContext();

                IMetaTable metaTable = ctx.Model.GetTable(TstCustomerTableName);
                ITable<RemoteRow> table = (ITable<RemoteRow>)ctx.GetTable(metaTable);
                RemoteRow row = table.FirstOrDefault();
                Assert.IsNotNull(row);
            }
        }

        protected override void OnErrorLogged(string message)
        {
            bool expected = message.Contains(nameof(ProcedureErrorException)) 
                || message.Contains("BasicError")
                || message.Contains("SubProcErrorOuter")
                || message.Contains("Rollback");
            if (!expected)
            {
                base.OnErrorLogged(message);
            }
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteErrorSchema", null);
        }
    }
}
