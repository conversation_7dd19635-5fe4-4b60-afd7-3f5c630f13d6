﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.Execution.Commands
{
    public delegate Task<ExecuteResult> BeforeCommandDelegate(ViewData data, CpiCommand command, CommandOptions options);
    public delegate Task<ExecuteResult> AfterCommandDelegate(ViewData data, CpiCommand command, CommandOptions options, ExecuteResult result);

    public sealed class WrappedCommandExecutor : ICommandExecutor
    {
        private readonly ICommandExecutor _parent;
        private readonly BeforeCommandDelegate _before;
        private readonly AfterCommandDelegate _after;

        public PageBase Page { get; set; }

        public static ICommandExecutor Create(ICommandExecutor parent, BeforeCommandDelegate before, AfterCommandDelegate after)
        {
            if (parent == null) throw new ArgumentNullException(nameof(parent));

            return new WrappedCommandExecutor(parent, before, after);
        }

        public static ICommandExecutor Create(ICommandExecutor parent, AfterCommandDelegate after)
        {
            if (parent == null) throw new ArgumentNullException(nameof(parent));

            return new WrappedCommandExecutor(parent, null, after);
        }

        private WrappedCommandExecutor(ICommandExecutor parent, BeforeCommandDelegate before, AfterCommandDelegate after)
        {
            _parent = parent;
            _before = before;
            _after = after;
        }

        public async Task<ExecuteResult> ExecuteAsync(string projectionName, ViewData data, CpiCommand command, CommandOptions options = null)
        {
            if (_before != null)
            {
                ExecuteResult beforeResult = await _before(data, command, options);

                // If you want the execution to continue, return null as the result from the delegate
                if (beforeResult != null)
                {
                    return beforeResult;
                }
            }

            ExecuteResult result = await _parent.ExecuteAsync(projectionName, data, command, options);

            if (_after != null)
            {
                result = await _after(data, command, options, result);
            }

            return result;
        }

        public void GetStates(string projectionName, ViewData viewData, CpiCommand command, bool enabledOnEmpty, out bool isVisible, out bool isEnabled)
        {
            _parent.GetStates(projectionName, viewData, command, enabledOnEmpty, out isVisible, out isEnabled);
        }
    }
}
