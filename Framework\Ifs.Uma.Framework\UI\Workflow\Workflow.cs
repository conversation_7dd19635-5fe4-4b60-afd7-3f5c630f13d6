﻿using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.UI.Workflow
{
    public class Workflow
    {
        private readonly IDataHandler _data;
        private readonly ViewData _viewData;
        private readonly string _projectionName;
        private readonly CpiWorkflow _workflow;

        public Workflow(IDataHandler data, ViewData viewData, string projectionName, CpiWorkflow workflow)
        {
            _data = data;
            _viewData = viewData;
            _projectionName = projectionName;
            _workflow = workflow;
        }

        public async Task<List<KeyValuePair<int, CpiWorkflowStep>>> GetAvailableWorkflowSteps()
        {
            List<FndWorkflowStep> dynamicSteps = null;
            if (!string.IsNullOrEmpty(_workflow?.DynamicSetupFunction))
            {
                Dictionary<string, object> setupParams = new Dictionary<string, object>();
                if (_workflow.DynamicSetupParams != null && _viewData?.Record != null)
                {
                    foreach (KeyValuePair<string, object> item in _workflow.DynamicSetupParams)
                    {
                        setupParams.Add(item.Key, _viewData.Record.ReadParamValue(item.Value?.ToString()));
                    }
                }

                ExecuteResult steps = await _data.PerformFunctionAsync(_projectionName, _workflow.DynamicSetupFunction, setupParams);
                if (steps.Value is IList fndWorkflowSteps)
                {
                    dynamicSteps = fndWorkflowSteps.Cast<FndWorkflowStep>().ToList();
                }
            }

            if (dynamicSteps != null && dynamicSteps.Any() && _workflow?.Steps != null && _workflow.Steps.Any())
            {
                int counter = 1;
                List<KeyValuePair<int, CpiWorkflowStep>> steps = new List<KeyValuePair<int, CpiWorkflowStep>>();

                foreach (FndWorkflowStep s in dynamicSteps)
                {
                    CpiWorkflowStep step = _workflow.Steps.Where(x => x.Name == s.Name).FirstOrDefault();
                    if (step != null)
                    {
                        steps.Add(new KeyValuePair<int, CpiWorkflowStep>((int)s.Sequence.GetValueOrDefault(counter), step));
                        counter++;
                    }
                }

                return steps;
            }

            return null;
        }
    }
}
