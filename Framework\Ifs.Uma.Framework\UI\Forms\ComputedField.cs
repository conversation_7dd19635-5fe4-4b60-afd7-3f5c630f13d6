﻿using System;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.UI.Fields;

namespace Ifs.Uma.Framework.UI.Forms
{
    internal class ComputedField : TextField
    {
        private ComputedString _computedString;
        public ComputedString ComputedString
        {
            get => _computedString;
            set
            {
                ComputedString oldValue = _computedString;
                if (SetProperty(ref _computedString, value))
                {
                    OnComputedStringChanged(oldValue, value);
                }
            }
        }

        public ComputedField()
        {
            Editability = FieldEditability.Never;
        }
        
        private void OnComputedStringChanged(ComputedString oldValue, ComputedString newValue)
        {
            if (oldValue != null)
            {
                oldValue.ValueChanged -= ComputedStringValueChanged;
            }

            if (newValue != null)
            {
                newValue.ValueChanged += ComputedStringValueChanged;
            }

            Value = ComputedString?.Value;
        }

        private void ComputedStringValueChanged(object sender, EventArgs e)
        {
            Value = ComputedString?.Value;
        }
    }
}
