﻿using System;
using System.Collections.Generic;
using System.Text;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;

namespace Ifs.Uma.Framework.UI.Images
{
    public abstract class ImageViewerItem : ObservableBase
    {
        public PickedFile PickedFile { get; set; }
        public object Image { get; set; }
        public virtual string FileName => PickedFile?.FileName;

        public ImageViewerItem()
        {
        }

        public ImageViewerItem(PickedFile pickedFile)
        {
            PickedFile = pickedFile;
        }
    }
}
