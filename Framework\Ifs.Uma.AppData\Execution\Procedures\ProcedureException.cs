﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures
{
    public class ProcedureException : ExecutionException
    {
        public ProcedureException()
        {
        }

        public ProcedureException(string message)
            : base(message)
        {
        }

        public ProcedureException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}
