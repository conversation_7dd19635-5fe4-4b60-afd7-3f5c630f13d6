﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class DbSelectSpecExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.DbSelectSpec;
        public ISelectSpec SelectSpec { get; }

        internal DbSelectSpecExpression(ISelectSpec selectSpec)
        {
            SelectSpec = selectSpec ?? throw new ArgumentNullException(nameof(selectSpec));
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitDbSelectSpecExpression(this);
        }

        public override string ToString()
        {
            return SqlBuilder.BuildDebugGenericSql(SelectSpec);
        }
    }

    public partial class IfsExpression
    {
        public static DbSelectSpecExpression DbSelectSpec(ISelectSpec selectSpec)
        {
            return new DbSelectSpecExpression(selectSpec);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitDbSelectSpecExpression(DbSelectSpecExpression exp)
        {
            return exp;
        }
    }
}
