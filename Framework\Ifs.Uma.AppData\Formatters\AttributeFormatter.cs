﻿using System;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Ifs.Uma.Utility.Formatters;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.AppData.Formatters
{
    public static class AttributeFormatter
    {
        public static IValueFormatter For(IMetadata metadata, string projectionName, CpiField fieldDef)
        {
            if (metadata == null) throw new ArgumentNullException(nameof(metadata));
            if (fieldDef == null) throw new ArgumentNullException(nameof(fieldDef));

            switch (fieldDef.Datatype)
            {
                case CpiDataType.Enumeration when fieldDef.Enumeration != null:
                    CpiEnumeration enumeration = metadata.FindEnumeration(projectionName, fieldDef.Enumeration);
                    if (enumeration != null)
                    {
                        return new EnumValueFormatter(enumeration);
                    }
                    break;
                case CpiDataType.Boolean:
                    return new BooleanFormatter(fieldDef);
                case CpiDataType.Number:
                    return new DoubleFormatter(metadata, projectionName, fieldDef);
                case CpiDataType.Date:
                    return new DateFormatter(DateFormats.Date);
                case CpiDataType.Time:
                    return new DateFormatter(DateFormats.Time);
                case CpiDataType.Timestamp:
                    return new DateFormatter(DateFormats.Timestamp);
                case CpiDataType.TimestampUtc:
                    return new DateFormatter(DateFormats.TimestampUtc);
            }

            return OrdinalValueFormatter.Instance;
        }

        public static IValueFormatter For(IMetaDataMember member)
        {
            if (member == null) throw new ArgumentNullException(nameof(member));

            if (member.Enumeration != null)
            {
                return new EnumValueFormatter(member.Enumeration);
            }

            Type nonNullType = TypeHelper.GetNonNullableType(member.ColumnType);
            switch (TypeHelper.GetTypeCode(nonNullType))
            {
                case TypeCode.Boolean:
                    return new BooleanFormatter(null, null);
                    
                case TypeCode.Decimal:
                case TypeCode.Double:
                    return new DoubleFormatter(member.Scale, member.NumberFormat);

                case TypeCode.DateTimeOffset:
                case TypeCode.TimeSpan:
                case TypeCode.DateTime:
                        return new DateFormatter(member.DateFormat);
            }

            return OrdinalValueFormatter.Instance;
        }

        public static string FormatValue(object value)
        {
            if (value == null)
            {
                return string.Empty;
            }

            Type type = value.GetType();
            switch (TypeHelper.GetTypeCode(type))
            {
                case TypeCode.Decimal:
                case TypeCode.Double:
                    return DoubleFormatter.Format(ObjectConverter.ToDouble(value), null, NumberFormat.Unformatted);
                    
                case TypeCode.DateTime:
                    return DateFormatter.Format((DateTime)value);
            }

            return OrdinalValueFormatter.Instance.Format(value);
        }
    }
}
