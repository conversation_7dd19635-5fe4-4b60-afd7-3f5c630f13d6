﻿using System.Linq;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Messages
{
    [TestFixture]
    public class DataChangeMessageInAdvancedTests : FrameworkTest
    {
        [Test]
        public void IgnoreReferenceSourceColumns()
        {
            IMetadata metadata = Resolve<IMetadata>();

            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            // Update the DatabaseInfo record so that we pick initialization status properly for the test
            DatabaseInfo dbInfo = db.DatabaseInfos.FirstOrDefault();
            db.DatabaseInfos.Attach(dbInfo);
            dbInfo.InitializeStatus = Data.Sync.InitializeStatus.Initialized;

            // Add the necessary records to the client keys map table
            ClientKeysMap rec1 = new ClientKeysMap();
            rec1.TableName = "FndMotWorkPackage";
            rec1.ClientKeys = "-1";
            rec1.ServerKeys = "100";
            db.ClientKeysMap.InsertOnSubmit(rec1);

            db.SubmitChanges(false);

            // First send a message to change a column when we have
            // the destination record in the client key map
            ctx.ExecuteMessage(MessageType.UPDATE, @"
{
  ""sync"": {
    ""fnd_mot_off_work_order"": [
      {
        ""wo_no"": 1,
        ""description"": ""Modified Description"",
        ""package_no"": 100
      }
    ]
  }
}");

            EntityDataSource woDataSource = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "WorkOrders");
            EntityQuery woQuery = new EntityQuery(woDataSource);
            woQuery.AddFilter("WoNo", 1);
            RemoteRow woRow = db.Query(woQuery).FirstOrDefault()?.Row;
            Assert.IsNotNull(woRow);
            Assert.AreEqual("Modified Description", woRow["Description"]); // Description should be modified after the message
            Assert.AreEqual(-1, woRow["PackageNo"]); // PackageNo should not be modified as it's a reference source column

            // Delete the record added to the client keys map
            db.ClientKeysMap.DeleteOnSubmit(rec1);
            db.SubmitChanges(false);

            // Send the same message again without having an entry in the key map
            ctx.ExecuteMessage(MessageType.UPDATE, @"
{
  ""sync"": {
    ""fnd_mot_off_work_order"": [
      {
        ""wo_no"": 1,
        ""description"": ""Further Modified Description"",
        ""package_no"": 200
      }
    ]
  }
}");

            woRow = db.Query(woQuery).FirstOrDefault()?.Row;
            Assert.IsNotNull(woRow);
            Assert.AreEqual("Further Modified Description", woRow["Description"]); // Description should be modified after the message
            Assert.AreEqual(200, woRow["PackageNo"]); // PackageNo should not be modified as it's a reference source column
        }

        [Test] // Both reference source and destination created in client, connection set in server
        public void MapKeysRef_ConnectReferenceFromServer()
        {
            IMetadata metadata = Resolve<IMetadata>();

            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            // Work Order exists with client key -1 and server key 42
            // Work Package exists with client key -2 and server key 20

            // Send the message from the server that updates the WO with the Work Package reference
            ctx.ExecuteMessage(MessageType.UPDATE, @"
{
  ""sync"": {
    ""fnd_mot_off_work_order"": [
      {
        ""wo_no"": 42,
        ""package_no"": 20
      }
    ]
  }
}");

            // Check the reference in the Work Order
            EntityDataSource woDataSource = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "WorkOrders");
            EntityQuery woQuery = new EntityQuery(woDataSource);
            woQuery.AddFilter("WoNo", -1);
            RemoteRow woRow = db.Query(woQuery).FirstOrDefault()?.Row;

            Assert.IsNotNull(woRow);
            Assert.AreEqual(-2, woRow["PackageNo"]);
        }

        [Test] // Reference source created in client, destination sent from server
        public void MapKeysRef_SourceClient_DestinationServer()
        {
            IMetadata metadata = Resolve<IMetadata>();

            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            // Work Order exists with client key -1 and server key 42

            // Send the Work Package created on the server
            ctx.ExecuteMessage(MessageType.INSERT, @"
{
  ""sync"": {
    ""fnd_mot_work_package"": [
      {
        ""work_package_no"": 300,
        ""description"": ""Work Package from server"",
      }
    ]
  }
}");

            // Update the Work Order with the Work Package reference
            ctx.ExecuteMessage(MessageType.UPDATE, @"
{
  ""sync"": {
    ""fnd_mot_off_work_order"": [
      {
        ""wo_no"": 42,
        ""package_no"": 300
      }
    ]
  }
}");

            // This is a common scenario where a reference is created on the server along with the destination record
            // Work Package is created and connected to the Work Order from the server side

            // Verify reference
            EntityDataSource woDataSource = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "WorkOrders");
            EntityQuery woQuery = new EntityQuery(woDataSource);
            woQuery.AddFilter("WoNo", -1);
            RemoteRow woRow = db.Query(woQuery).FirstOrDefault()?.Row;

            Assert.IsNotNull(woRow);
            Assert.AreEqual(300, woRow["PackageNo"]);
        }

        [Test] // Reference destination created in client, source sent from server
        public void MapKeysRef_DestinationClient_SourceServer()
        {
            IMetadata metadata = Resolve<IMetadata>();

            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            // Work Package exists with client key -2 and server key 20

            // Send the Work Order created on the server, with a reference to the Work Package
            ctx.ExecuteMessage(MessageType.INSERT, @"
{
  ""sync"": {
    ""fnd_mot_off_work_order"": [
      {
        ""wo_no"": 180,
        ""description"": ""Work Order 180"",
        ""package_no"": 20
      }
    ]
  }
}");

            // The Work Package reference on the Work Order should be updated
            // with the client keys for the WP that was created on the client

            // Verify reference
            EntityDataSource woDataSource = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "WorkOrders");
            EntityQuery woQuery = new EntityQuery(woDataSource);
            woQuery.AddFilter("WoNo", 180);
            EntityRecord woRecord = db.Query(woQuery).FirstOrDefault();

            Assert.IsNotNull(woRecord?.Row);
            Assert.AreEqual(-2, woRecord.Row["PackageNo"]);
        }

        [Test] // Array parent created in client, child item sent from server
        public void MapKeysArray_ParentClient_ChildServer()
        {
            IMetadata metadata = Resolve<IMetadata>();

            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            // Work Order exists with client key -1 and server key 42

            // Send the Time Report created on the server
            ctx.ExecuteMessage(MessageType.INSERT, @"
{
  ""sync"": {
    ""fnd_mot_off_time_report"": [
      {
        ""work_order_no"": 42,
        ""seq_no"": 1,
        ""date_time_from"": ""2021-08-08T10:11:00"",
        ""date_time_to"": ""2021-08-08T11:11:00""
      }
    ]
  }
}");

            // The WorkOrderNo value 1 of the Time Report sent from the server should be mapped to the
            // client keys of the Work Order, which is -1

            // Verify array
            EntityDataSource trDataSource = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "TimeReports");
            EntityQuery trQuery = new EntityQuery(trDataSource);
            trQuery.AddFilter("WorkOrderNo", -1);
            EntityRecord trRecord = db.Query(trQuery).FirstOrDefault();

            Assert.IsNotNull(trRecord?.Row);
            Assert.AreEqual(1, trRecord.Row["SeqNo"]);
        }

        [Test] // Array child item created in client, parent sent from server
        public void MapKeysArray_ChildClient_ParentServer()
        {
            IMetadata metadata = Resolve<IMetadata>();

            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            // Time Report exists with client key -1 and server key 12
            // it also maps to the server key of the Work Order, which is 50

            // Send the Work Order created on the server
            ctx.ExecuteMessage(MessageType.INSERT, @"
{
  ""sync"": {
    ""fnd_mot_off_work_order"": [
      {
        ""wo_no"": 50,
        ""Description"": ""Work Order 50""
      }
    ]
  }
}");

            // Basically this should not change anything for Time Report records
            // If somehow a child record exists on the client with client keys (created on client),
            // with the parent keys of a record not on the client (Work Order 50 in this case),
            // sending that WO from the server should not modify any keys of the child record(s)

            // Verify array
            EntityDataSource trDataSource = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "TimeReports");
            EntityQuery trQuery = new EntityQuery(trDataSource);
            trQuery.AddFilter("WorkOrderNo", 50);
            EntityRecord trRecord = db.Query(trQuery).FirstOrDefault();

            Assert.IsNotNull(trRecord?.Row);
            Assert.AreEqual(-1, trRecord.Row["SeqNo"]);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("Messages.DataChangeMessageInAdvancedSchema", "Messages.DataChangeMessageInAdvancedData");
        }
    }
}
