﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.UI.Elements
{
    public abstract class RecordLoaderElement : ElementBase
    {
        protected virtual bool CheckForSingleRecord => false;

        private readonly IMetadata _metadata;
        
        private ViewData _loaderViewData;
        public ViewData LoaderViewData
        {
            get => _loaderViewData;
            private set => SetProperty(ref _loaderViewData, value);
        }

        public bool IsPrimary => LoaderViewData == PageData.DefaultViewData;

        private CpiRecordLoader _cpiRecordLoader;

        protected RecordLoaderElement(IMetadata metadata)
        {
            _metadata = metadata;
            HasHeader = false;
            AlwaysLoad = true;
        }
        
        protected override void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            base.OnPageDataChanged(oldValue, newValue);

            UpdateLoaderViewData();
        }
        
        protected sealed override bool OnInitialize()
        {
            _cpiRecordLoader = OnInitializeRecordLoader();
            return _cpiRecordLoader != null;
        }

        protected abstract CpiRecordLoader OnInitializeRecordLoader();

        protected override bool OnLoad()
        {
            UpdateLoaderViewData();
            return true;
        }

        private void UpdateLoaderViewData()
        {
            ViewData oldViewData = LoaderViewData;
            ViewData newViewData = _cpiRecordLoader.Name == null ? null : PageData.GetViewData(_cpiRecordLoader.Name);

            if (newViewData != oldViewData)
            {
                if (oldViewData != null)
                {
                    oldViewData.CrudActions = null;
                }

                if (newViewData != null)
                {
                    newViewData.CrudActions = _cpiRecordLoader.CrudActions;
                }

                LoaderViewData = newViewData;
            }
        }

        public bool HasChanges()
        {
            return LoaderViewData?.Record?.HasChanges ?? false;
        }

        public Task LoadPageRecordAsync()
        {
            return LoadRecordAsync(false);
        }

        public Task CreateRecordAsync()
        {
            return LoadRecordAsync(true);
        }

        public Task ReloadRecordAsync()
        {
            return LoadRecordAsync(false);
        }

        protected async Task LoadRecordAsync(bool newRecord, EntityQuery entityQuery = null)
        {
            using (UpdatingState.BeginUpdating())
            {
                try
                {
                    CpiExpression enabled = PageData?.CrudActions?.New?.OfflineEnabled ?? PageData?.CrudActions?.New?.Enabled;
                    bool canCreate = true;
                    if (enabled != null)
                    {
                        canCreate = RunCheck(enabled, true);
                    }

                    ViewData viewData = LoaderViewData;

                    Elements.ClearValidations(viewData);

                    ExecuteResult result;
                    EntityDataSource dataSource = DataSource;
                    if (dataSource != null)
                    {
                        Task<ExecuteResult> LoadNewRecord()
                        {
                            if (canCreate)
                            {
                                return viewData.Record.LoadNewRecordAsync(dataSource.ProjectionName ?? ProjectionName, dataSource.EntityName, dataSource.EntitySetName);
                            }

                            return viewData.Record.LoadBlankRecordAsync();
                        }

                        if (newRecord)
                        {
                            result = await LoadNewRecord();
                        }
                        else
                        {
                            EntityQuery query = entityQuery?.Clone() ?? new EntityQuery(dataSource);
                            query.SelectAttributes = PageData.SelectAttributes;

                            bool isPrimary = viewData == PageData.DefaultViewData;
                            if (isPrimary)
                            {
                                PageData.Filter?.Apply(query);
                            }

                            result = await viewData.Record.LoadRecordAsync(query, canCreate, CheckForSingleRecord);
                        }
                    }
                    else
                    {
                        result = await viewData.Record.LoadBlankRecordAsync();
                    }

                    OnRecordReady();

                    await CheckExecuteResult(result, false);
                }
                catch (Exception ex)
                {
                    await HandleException(ex);
                }
            }
        }

        public async Task SaveRecordAsync()
        {
            using (UpdatingState.BeginUpdating())
            {
                if (!await Elements.ValidateAsync())
                {
                    return;
                }

                try
                {
                    ViewData viewData = LoaderViewData;

                    CpiCrudType operation = viewData.Record.IsNew() ? CpiCrudType.Create : CpiCrudType.Update;

                    if (!await RunBeforeCrudCommand(operation))
                    {
                        return;
                    }
                    if (DataSource is ArrayDataSource)
                    {
                        ArrayDataSource ads = (ArrayDataSource)DataSource;
                        string arrayKey = ObjPrimaryKey.FromPrimaryKeyQueryParams(_metadata.MetaModel, viewData.Record.GetRemoteRow(), ProjectionName);

                        viewData.Record.GetRemoteRow().ArraySourceName = ads.ArrayPropertyName + arrayKey;
                        viewData.Record.GetRemoteRow().PrimaryKeyString = ads.ParentKey.ToFormattedKeyRef(Record.ProjectionName);
                    }
                    viewData.Record.GetRemoteRow().EntitySetName = _cpiRecordLoader?.DatasourceEntitySet ?? viewData.PageData.DataSource.EntitySetName;
                    ExecuteResult result = await viewData.Record.SaveRecordAsync();

                    if (result.Failed)
                    {
                        bool notifiedInvalid = false;
                        ProcedureErrorException ex = result.Exception as ProcedureErrorException;
                        const string prefix = ProcedureExecutor.RecordVarName + ".";
                        if (ex?.Parameter != null && ex.Parameter.StartsWith(prefix))
                        {
                            notifiedInvalid = Elements.NotifyFieldInvalid(ex.Parameter.Substring(prefix.Length), ex.Message);
                        }

                        if (!notifiedInvalid)
                        {
                            await CheckExecuteResult(result);
                        }
                    }
                    else
                    {
                        Elements.ClearValidations(LoaderViewData);

                        ObjPrimaryKey pk = ViewData.Record?.ToPrimaryKey();

                        if (pk != null
                            && Content.Binding?.Property == null &&
                            viewData == PageData.DefaultViewData)
                        {
                            PageData.Filter = PageValues.FromPrimaryKey(pk);
                        }

                        await RunAfterCrudCommand(operation);
                    }

                    OnRecordReady();
                }
                catch (Exception ex)
                {
                    await HandleException(ex);
                }
            }
        }

        public async Task DeleteRecordAsync()
        {
            using (UpdatingState.BeginUpdating())
            {
                try
                {
                    ViewData viewData = LoaderViewData;
                    //TO-DO : needs to be handled in a better way
                    viewData.Record.GetRemoteRow().EntitySetName = _cpiRecordLoader?.DatasourceEntitySet ?? viewData.PageData.DataSource.EntitySetName;
                    ExecuteResult result = await viewData.Record.DeleteRecordAsync();
                    
                    if (!await RunBeforeCrudCommand(CpiCrudType.Delete))
                    {
                        return;
                    }

                    if (await CheckExecuteResult(result))
                    {
                        Elements.ClearValidations(viewData);

                        await RunAfterCrudCommand(CpiCrudType.Delete);
                    }

                    OnRecordReady();
                }
                catch (Exception ex)
                {
                    await HandleException(ex);
                }
            }
        }

        private async Task<bool> RunBeforeCrudCommand(CpiCrudType crudOperation)
        {
            CpiCrudActions crudActions = _cpiRecordLoader.CrudActions ?? PageData.CrudActions;
            if (crudActions?.Hooks?.Before != null)
            {
                CommandOptions options = new CommandOptions();
                options.ActionsAllowed = false;
                options.SetCrudOperation(crudOperation);
                ExecuteResult beforeResult = await CommandExecutor.ExecuteAsync(ProjectionName, LoaderViewData, crudActions.Hooks.Before, options);
                if (beforeResult.Failed || beforeResult.Is(ExecuteResult.Cancel))
                {
                    return false;
                }
            }

            return true;
        }

        private async Task RunAfterCrudCommand(CpiCrudType crudOperation)
        {
            CpiCrudActions crudActions = _cpiRecordLoader.CrudActions ?? PageData.CrudActions;
            if (crudActions?.Hooks?.After != null)
            {
                CommandOptions options = new CommandOptions();
                options.SetCrudOperation(crudOperation);
                await CommandExecutor.ExecuteAsync(ProjectionName, LoaderViewData, crudActions.Hooks.After, options);
            }
        }

        protected virtual void OnRecordReady()
        {
        }

        protected override void OnDataSourceChanged()
        {
            base.OnDataSourceChanged();
            
            if (!IsPrimary)
            {
                Task load = LoadRecordAsync(false);
                PageData.BackgroundTasks.Add(load);
            }
        }
    }
}
