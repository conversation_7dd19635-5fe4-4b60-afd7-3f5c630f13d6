﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Rewrites nested singleton projection into server-side joins
    /// </summary>
    internal class SingletonProjectionRewriter : DbExpressionVisitor
    {
        bool isTopLevel = true;
        SelectExpression currentSelect;

        private SingletonProjectionRewriter()
        {
        }

        public static Expression Rewrite(Expression expression)
        {
            return new SingletonProjectionRewriter().Visit(expression);
        }

        protected override Expression VisitClientJoin(ClientJoinExpression node)
        {
            // treat client joins as new top level
            var saveTop = this.isTopLevel;
            var saveSelect = this.currentSelect;
            this.isTopLevel = true;
            this.currentSelect = null;
            Expression result = base.VisitClientJoin(node);
            this.isTopLevel = saveTop;
            this.currentSelect = saveSelect;
            return result;
        }

        protected override Expression VisitProjection(ProjectionExpression node)
        {
            if (node == null) return null;
            if (isTopLevel)
            {
                isTopLevel = false;
                this.currentSelect = node.Select;
                Expression projector = this.Visit(node.Projector);
                if (projector != node.Projector || this.currentSelect != node.Select)
                {
                    return new ProjectionExpression(this.currentSelect, projector, node.Aggregator);
                }
                return node;
            }

            if (node.IsSingleton && CanJoinOnServer(this.currentSelect))
            {
                TableAlias newAlias = new TableAlias();
                this.currentSelect = this.currentSelect.AddRedundantSelect(newAlias);

                // remap any references to the outer select to the new alias;
                SelectExpression source = (SelectExpression)ColumnMapper.Map(node.Select, newAlias, this.currentSelect.Alias);

                // add outer-join test
                ProjectionExpression pex = new ProjectionExpression(source, node.Projector).AddOuterJoinTest();

                var pc = ColumnProjector.ProjectColumns(pex.Projector, this.currentSelect.Columns, this.currentSelect.Alias, newAlias, node.Select.Alias);

                JoinExpression join = new JoinExpression(JoinType.OuterApply, this.currentSelect.From, pex.Select, null);

                this.currentSelect = new SelectExpression(this.currentSelect.Alias, pc.Columns, join, null);
                return this.Visit(pc.Projector);
            }

            var saveTop = this.isTopLevel;
            var saveSelect = this.currentSelect;
            this.isTopLevel = true;
            this.currentSelect = null;
            Expression result = base.VisitProjection(node);
            this.isTopLevel = saveTop;
            this.currentSelect = saveSelect;
            return result;
        }

        private static bool CanJoinOnServer(SelectExpression select)
        {
            if (select == null) throw new ArgumentNullException("select");
            // can add singleton (1:0,1) join if no grouping/aggregates or distinct
            return !select.IsDistinct
                && (select.GroupBy == null || select.GroupBy.Count == 0)
                && !AggregateChecker.HasAggregates(select);
        }

        protected override Expression VisitSubquery(SubqueryExpression node)
        {
            return node;
        }

        protected override Expression VisitCommand(CommandExpression node)
        {
            this.isTopLevel = true;
            return base.VisitCommand(node);
        }
    }
}