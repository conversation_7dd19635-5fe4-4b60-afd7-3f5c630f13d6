﻿using System;
using System.Collections.Generic;
using Ifs.Uma.AppData.Expressions;

namespace Ifs.Uma.Framework.Data
{
    public class ViewState : IExpressionValueProvider
    {
        public const string ViewStatePrefix = "viewstate.";

        public event EventHandler ViewStateChanged;
        public Dictionary<string, object> Vars { get; } = new Dictionary<string, object>();

        public bool TryCallMethod(string methodName, object[] args, out object result)
        {
            result = null;
            return false;
        }

        public bool TryGetValue(string propertyName, out object value)
        {
            if (!propertyName.StartsWith(ViewStatePrefix))
            {
                propertyName = ViewStatePrefix + propertyName;
            }

            return Vars.TryGetValue(propertyName, out value);
        }

        public bool Assign(string name, object value)
        {
            System.Diagnostics.Debug.Assert(!Equals(value, string.Empty));

            if (!name.StartsWith(ViewStatePrefix))
            {
                name = ViewStatePrefix + name;
            }

            if (Vars.ContainsKey(name))
            {
                if (Vars[name] != value)
                {
                    Vars[name] = value;
                    ViewStateChanged?.Invoke(this, EventArgs.Empty);
                }
            }
            else
            {
                Vars.Add(name, value);
                ViewStateChanged?.Invoke(this, EventArgs.Empty);
            }

            return true;
        }
    }
}
