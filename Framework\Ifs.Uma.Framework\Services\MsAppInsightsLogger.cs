﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Utility;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.DependencyCollector;
using Microsoft.ApplicationInsights.Extensibility;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.MsAppInsights
{
    internal sealed class MsAppInsightsLogger : IInsightsLogger
    {
        private readonly TelemetryClient _tc;

        public string InstrumentationKey { get; }

        private readonly DependencyTrackingTelemetryModule _dependencyTrackingModule = new DependencyTrackingTelemetryModule();

        public MsAppInsightsLogger(string instrumentationKey)
        {
            TelemetryConfiguration config = TelemetryConfiguration.CreateDefault();
            config.InstrumentationKey = instrumentationKey;
            config.DisableTelemetry = false;
            _tc = new TelemetryClient(config);
            
            _tc.InstrumentationKey = instrumentationKey;
            _tc.Context.Component.Version = ApplicationInfo.FrameworkVersion.ToString();
            _tc.Context.Session.Id = Guid.NewGuid().ToString();
            _tc.Context.Device.OperatingSystem = DeviceInfo.OperatingSystem + " " + DeviceInfo.OperatingSystemVersion;
            _tc.Context.Device.Model = DeviceInfo.Manufacturer + " " + DeviceInfo.Model;
            if (DeviceInfo.OperatingSystem != OperatingSystem.Windows)
            {
                _tc.Context.Device.Type = DeviceInfo.Type.ToString();
            }

            InstrumentationKey = instrumentationKey;

            // test code for dependency tracking - this block is based on Microsoft's samples
            // prevent Correlation Id to be sent to certain endpoints. You may add other domains as needed.
            _dependencyTrackingModule.ExcludeComponentCorrelationHttpHeadersOnDomains.Add("core.windows.net");

            // enable known dependency tracking, note that in future versions, we will extend this list. 
            // please check default settings in https://github.com/Microsoft/ApplicationInsights-dotnet-server/blob/develop/Src/DependencyCollector/DependencyCollector/ApplicationInsights.config.install.xdt
            _dependencyTrackingModule.IncludeDiagnosticSourceActivities.Add("Microsoft.Azure.ServiceBus");
            _dependencyTrackingModule.IncludeDiagnosticSourceActivities.Add("Microsoft.Azure.EventHubs");

            TelemetryConfiguration dependencyTelemetryConfig = TelemetryConfiguration.CreateDefault();
            dependencyTelemetryConfig.InstrumentationKey = instrumentationKey;
            dependencyTelemetryConfig.DisableTelemetry = false;
            dependencyTelemetryConfig.TelemetryInitializers.Add(new HttpDependenciesParsingTelemetryInitializer());
            _dependencyTrackingModule.Initialize(dependencyTelemetryConfig);
        }

        public void Identify(string id, IDictionary<string, object> properties = null)
        {
            object appName = null;
            properties?.TryGetValue("AppName", out appName);
            object anonSystemId = string.Empty;
            properties?.TryGetValue("System", out anonSystemId);
            object anonUserId = string.Empty;
            properties?.TryGetValue("User", out anonUserId);
            object objDeviceId = string.Empty;
            properties?.TryGetValue("DeviceId", out objDeviceId);
            string deviceId = (objDeviceId != null) ? ObjectConverter.ToString(objDeviceId) : string.Empty;

            _tc.Context.Device.Id = id;
            _tc.Context.GlobalProperties["AppName"] = ObjectConverter.ToString(appName);
            if (!string.IsNullOrEmpty(deviceId))
                _tc.Context.GlobalProperties["DeviceId"] = deviceId; // log Device Id in all calls

            _tc.Context.User.AccountId = ObjectConverter.ToString(anonSystemId);
            _tc.Context.User.Id = ObjectConverter.ToString(anonUserId);
            _tc.Context.User.AuthenticatedUserId = ObjectConverter.ToString(anonUserId);

            // log the device Time Zone info in the Identify call (but not in all calls - so not adding to GlobalProperties)
            Dictionary<string, object> props = new Dictionary<string, object>
            {
                { "DeviceTimeZone", TimeZoneInfo.Local.DisplayName }
            };

            TrackEvent("Identify", Insights.SystemEventType, props);

            Task.Run(() => _tc.Flush());
        }

        public void TrackException(ExceptionType type, Exception ex)
        {
            if (ex is Cloud.Client.Exceptions.CloudException cloudException && cloudException.ErrorType == Cloud.Client.Exceptions.CloudErrorType.TokenRefreshRequired)
            {
                return; // TokenRefreshRequired is generally handled internally, and should not be logged to App Insights
            }

            Dictionary<string, string> props = new Dictionary<string, string>
            {
                { "DeviceDate", GetDeviceDate() },
                { "DeviceTime", GetDeviceTime() }
            };

            // Only track exceptions that are problems
            if (!ExecuteResult.IsUserError(ex))
            {
                _tc.TrackException(ex, props);
                _tc.Flush();
            }
        }

        public void TrackEvent(string name, string type = null, IDictionary<string, object> properties = null)
        {
            Dictionary<string, string> props = Props(properties);

            if (type != null)
            {
                props["EventType"] = type;
            }

            props["DeviceDate"] = GetDeviceDate();
            props["DeviceTime"] = GetDeviceTime();

            _tc.TrackEvent(name, props);
        }

        public void TrackDependency(string name, string data, string target, string type, DateTime startTime, TimeSpan duration, bool success)
        {
            DependencyTelemetry dependencyTelemetry = new DependencyTelemetry()
            {
                Name = name,
                Data = data,
                Target = target,
                Type = type,
                Timestamp = startTime,
                Duration = duration,
                Success = success
            };
            dependencyTelemetry.Properties.Add("DeviceDate", GetDeviceDate());
            dependencyTelemetry.Properties.Add("DeviceTime", GetDeviceTime());
            _tc.TrackDependency(dependencyTelemetry);
        }

        public void TrackTime(string name, DateTimeOffset start, TimeSpan duration, IDictionary<string, object> properties = null)
        {
            EventTelemetry telem = new EventTelemetry(name);
            telem.Timestamp = start;
            telem.Metrics.Add("Duration", duration.TotalMilliseconds);
            telem.Properties.Add("EventType", Insights.PerformanceEventType);
            telem.Properties.Add("DeviceDate", GetDeviceDate());
            telem.Properties.Add("DeviceTime", GetDeviceTime());
            
            // add any additional properties provided
            if (properties != null && properties.Count > 0)
            {
                foreach (string key in properties.Keys)
                {
                    telem.Properties.Add(key, Convert.ToString(properties[key]));
                }
            }

            _tc.TrackEvent(telem);
        }

        public IDisposable TrackTime(string name)
        {
            return new TrackedTime(this, name);
        }

        private static string GetDeviceDate()
        {
            return DateTime.Now.ToString("yyyy-MM-dd"); // Note: Azure seems to convert this to an ISO 8601 string
        }

        private static string GetDeviceTime()
        {
            return DateTime.Now.ToString("HH:mm:ss");
        }

        private sealed class TrackedTime : IDisposable
        {
            private readonly IInsightsLogger _logger;
            private readonly Stopwatch _sw;
            private readonly DateTimeOffset _start;
            private readonly string _name;

            public TrackedTime(IInsightsLogger logger, string name)
            {
                _logger = logger;
                _name = name;
                _start = DateTimeOffset.Now;
                _sw = Stopwatch.StartNew();
            }

            public void Dispose()
            {
                TimeSpan duration = _sw.Elapsed;
                _logger.TrackTime(_name, _start, duration);
            }
        }

        private static Dictionary<string, string> Props(IDictionary<string, object> properties)
        {
            if (properties == null)
            {
                return new Dictionary<string, string>();
            }

            Dictionary<string, string> props = new Dictionary<string, string>();
            foreach (KeyValuePair<string, object> kvp in properties)
            {
                if (kvp.Value != null)
                {
                    props[kvp.Key] = ObjectConverter.ToString(kvp.Value);
                }
            }

            return props;
        }

        public void Dispose()
        {
            _tc.Flush();
            TelemetryConfiguration.Active.DisableTelemetry = true;
        }
    }
}
