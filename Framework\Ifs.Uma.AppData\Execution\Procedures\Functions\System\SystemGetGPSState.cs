﻿using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemGetGpsState : SystemFunction
    {
        public const string FunctionName = "GetGpsState";   

        public SystemGetGpsState()
               : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            bool islocationEnabledInDevice = PlatformServices.Provider.IsGpsEnabledInDevice();

            MobileClientParam param = context.DbDataContext.AppParameters.FirstOrDefault(x => x.Parameter == "LOCATION_ENABLED");

            if (param?.Value != null)
            {
                if (param.Value == "FALSE")
                {
                    return "DISABLED_APP";
                }
                else 
                {
                    if (islocationEnabledInDevice)
                    {
                        return "ENABLED";
                    }
                    else
                    {
                        return "DISABLED_DEVICE";
                    }
                }
            }

            return string.Empty;
        }   
    }
}
