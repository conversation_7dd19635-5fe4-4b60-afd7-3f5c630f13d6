﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Database
{
    public enum ETableSpec
    {
        Name,
        Select
    }

    public interface ITableSpec : IWriteSql
    {
        ETableSpec Kind { get; }
        string TableAlias { get; }
    }

    public interface ITableNameSpec : ITableSpec
    {
        string TableName { get; }
    }

    public interface ITableSelectSpec : ITableSpec
    {
        ISelectSpec Select { get; }
    }

    public interface IJoinSpec : IWriteSql
    {
        EJoinType JoinType { get; }
        ITableSpec Table { get; }
        IEnumerable<IJoinColumn> JoinColumns { get; }
    }

    public interface IJoinColumn
    {
        IAliasedColumnSpec Left { get; }
        EColumnFunction LeftFunction { get; }
        IAliasedColumnSpec Right { get; }
        EColumnFunction RightFunction { get; }
    }
    
    public abstract class TableSpec : ITableSpec
    {
        public static ITableNameSpec Create(string tableName, string tableAlias)
        {
            return !string.IsNullOrEmpty(tableName) ?
                new TableNameSpec(tableName, tableAlias) : null;
        }

        public static ITableNameSpec Create(string tableName)
        {
            return Create(tableName, null);
        }

        public static ITableSelectSpec Create(ISelectSpec select, string tableAlias)
        {
            return select != null && !string.IsNullOrEmpty(tableAlias) ?
                new TableSelectSpec(select, tableAlias) : null;
        }

        public abstract ETableSpec Kind { get; }
        public string TableAlias { get; private set; }
        public void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
        {
            WriteSqlTable(sb, info, builder);
            if (sb != null && builder != null && !string.IsNullOrEmpty(TableAlias))
            {
                sb.Append(builder.AliasSeparator);
                sb.Append(builder.DecorateName(TableAlias));
            }
        }

        protected TableSpec(string tableAlias)
        {
            TableAlias = tableAlias;
        }

        protected abstract void WriteSqlTable(StringBuilder sb, IStatementInfo info, SqlBuilder builder);

        protected class TableNameSpec : TableSpec, ITableNameSpec
        {
            public TableNameSpec(string tableName, string tableAlias)
                : base(tableAlias)
            {
                TableName = tableName;
            }

            public override ETableSpec Kind { get { return ETableSpec.Name; } }
            public string TableName { get; private set; }

            protected override void WriteSqlTable(StringBuilder sb, IStatementInfo info, SqlBuilder builder)
            {
                if (sb != null && builder != null)
                {
                    sb.Append(builder.DecorateTableName(TableName));
                }
            }
        }

        protected class TableSelectSpec : TableSpec, ITableSelectSpec
        {
            public TableSelectSpec(ISelectSpec select, string tableAlias)
                : base(tableAlias)
            {
                Select = select;
            }

            public override ETableSpec Kind { get { return ETableSpec.Select; } }
            public ISelectSpec Select { get; private set; }

            protected override void WriteSqlTable(StringBuilder sb, IStatementInfo info, SqlBuilder builder)
            {
                if (sb != null && builder != null)
                {
                    sb.Append(builder.OpenBracket);
                    Select.WriteSql(sb, info, builder, SqlWriteMode.Statement);
                    sb.Append(builder.CloseBracket);
                }
            }
        }
    }

    public class JoinSpec : IJoinSpec
    {
        public static IJoinSpec Create(EJoinType joinType, ITableSpec table, IEnumerable<IJoinColumn> joinColumns)
        {
            return table != null ?
                new JoinSpec(joinType, table, joinColumns) : null;
        }
        public static IJoinSpec Create(EJoinType joinType, ITableSpec table, ISqlExpression onExpression)
        {
            return table != null ? new JoinExpressionSpec(joinType, table, onExpression) : null;
        }

        #region IJoinSpec Members

        public EJoinType JoinType { get; private set; }
        public ITableSpec Table { get; private set; }
        public IEnumerable<IJoinColumn> JoinColumns { get; private set; }
        
        public void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
        {
            if (sb != null && builder != null)
            {
                sb.Append(builder.JoinText(JoinType));
                Table.WriteSql(sb, info, builder, mode);
                if (JoinColumns != null && JoinColumns.Any(x => x != null))
                {
                    sb.Append(builder.OnClause);
                    bool first = true;
                    foreach (IJoinColumn column in JoinColumns)
                    {
                        if (column != null)
                        {
                            if (first)
                            {
                                first = false;
                            }
                            else
                            {
                                sb.Append(builder.AndOperand);
                            }
                            builder.WriteFunction(sb, column.LeftFunction, column.Left);
                            sb.Append(builder.EqualOperator);
                            builder.WriteFunction(sb, column.RightFunction, column.Right);
                        }
                    }
                }
            }
        }

        #endregion

        #region Implementation

        protected JoinSpec(EJoinType joinType, ITableSpec table, IEnumerable<IJoinColumn> joinColumns)
        {
            JoinType = joinType;
            Table = table;
            JoinColumns = joinColumns;
        }

        #endregion
    }

    internal class JoinExpressionSpec : IJoinSpec
    {
        #region IJoinSpec Members

        public EJoinType JoinType { get; }
        public ITableSpec Table { get; }
        public ISqlExpression OnExpression { get; }
        public IEnumerable<IJoinColumn> JoinColumns { get; }

        public void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
        {
            if (sb != null && builder != null)
            {
                sb.Append(builder.JoinText(JoinType));
                Table.WriteSql(sb, info, builder, mode);

                if (OnExpression != null)
                {
                    sb.Append(builder.OnClause);
                    OnExpression.WriteSql(sb, info, builder, mode);
                }
            }
        }

        #endregion

        #region Implementation

        public JoinExpressionSpec(EJoinType joinType, ITableSpec table, ISqlExpression onExpression)
        {
            JoinType = joinType;
            Table = table;
            OnExpression = onExpression;
        }

        #endregion
    }

    public class JoinColumn : IJoinColumn
    {
        public static IJoinColumn Create(
            string leftTableAlias, string leftColumn,
            string rightTableAlias, string rightColumn)
        {
            return new JoinColumn(leftTableAlias, leftColumn, EColumnFunction.None, rightTableAlias, rightColumn, EColumnFunction.None);
        }

        public static IJoinColumn Create(
            string leftTableAlias, string leftColumn, EColumnFunction leftFunction,
            string rightTableAlias, string rightColumn, EColumnFunction rightFunction)
        {
            return new JoinColumn(leftTableAlias, leftColumn, leftFunction, rightTableAlias, rightColumn, rightFunction);
        }

        #region IJoinColumn Members

        public IAliasedColumnSpec Left { get; private set; }
        public EColumnFunction LeftFunction { get; private set; }
        public IAliasedColumnSpec Right { get; private set; }
        public EColumnFunction RightFunction { get; private set; }

        #endregion

        #region Implementation

        protected JoinColumn(
            string leftTableAlias, string leftColumn, EColumnFunction leftFunction,
            string rightTableAlias, string rightColumn, EColumnFunction rightFunction)
        {
            Left = ColumnSpec.Create(leftColumn, leftTableAlias);
            LeftFunction = leftFunction;
            Right = ColumnSpec.Create(rightColumn, rightTableAlias);
            RightFunction = rightFunction;
        }

        #endregion
    }
}
