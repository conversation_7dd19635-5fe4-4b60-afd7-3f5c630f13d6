﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_" + DbTableName, Columns = nameof(SiteId), Unique = true)]

    public class ServerSite : RemoteRow
    { 
        public const string DbTableName = FwDataContext.FwTablePrefix + "site";

        [Column(Mandatory = true, ServerPrimaryKey = true)]
        public string SiteId { get; set; }

        [Column]
        public string TimeZone { get; set; }

        public ServerSite()
            : base(DbTableName)
        {
        }
    }
}
