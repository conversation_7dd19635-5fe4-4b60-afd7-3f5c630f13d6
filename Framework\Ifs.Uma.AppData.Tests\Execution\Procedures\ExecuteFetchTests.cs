﻿using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using NUnit.Framework;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteFetchTests : ProcedureTest
    {
        private const string TstCustomerEntityName = "TstCustomer";
        private const string CustomerNoAttributeName = "CustomerNo";
        private const string TypeDescriptionAttributeName = "TypeDescription";
        private const string AddressLineAttributeName = "AddressLine";

        [Test]
        public async Task FetchFromEntitySet()
        {
            RemoteRow row = await DoFetch("FetchCustomer");
            Assert.IsNotNull(row);

            // Only one result is selected from a fetch
            // this should be the row with the row with the lowest row ID
            Assert.AreEqual(1, row.RowId); 
        }

        [Test]
        public async Task FetchFromRef()
        {
            RemoteRow customerRow = GetCustomer("501");

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Customer"] = customerRow;

            RemoteRow row = await DoFetch("FetchCustomerType", parameters);
            Assert.IsNotNull(row);
            Assert.AreEqual("Customer Type B", row[TypeDescriptionAttributeName]);
        }

        [Test]
        public async Task FetchFromArray()
        {
            RemoteRow customerRow = GetCustomer("501");

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Customer"] = customerRow;

            RemoteRow row = await DoFetch("FetchCustomerAddress", parameters);
            Assert.IsNotNull(row);

            // Only one result is selected from a fetch
            // this should be the row with the row with the lowest row ID
            // which will be the earliest entry in the data file
            Assert.AreEqual("Test Address 3", row[AddressLineAttributeName]);
        }

        [Test]
        public async Task FetchFromArrayWithWhere()
        {
            RemoteRow customerRow = GetCustomer("501");

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Customer"] = customerRow;

            RemoteRow row = await DoFetch("FetchFromArrayWithWhere", parameters);

            Assert.IsNotNull(row);
            Assert.AreEqual("Test Address 4", row[AddressLineAttributeName]);
        }

        [Test]
        public async Task FetchWhereAliased()
        {
            RemoteRow row = await DoFetch("FetchWhereAliased");

            Assert.IsNotNull(row);
            Assert.AreEqual("501", row[CustomerNoAttributeName]);
        }

        [Test]
        public async Task FetchOrderBy()
        {
            RemoteRow row = await DoFetch("FetchOrderBy");

            Assert.IsNotNull(row);
            Assert.AreEqual("Test Address 2", row[AddressLineAttributeName]);
        }

        private async Task<RemoteRow> DoFetch(string functionName, Dictionary<string, object> parameters = null)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, functionName, parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return result.Value as RemoteRow;
        }

        private RemoteRow GetCustomer(string customerNo)
        {
            IMetadata metadata = Resolve<IMetadata>();

            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, TstCustomerEntityName);
            EntityQuery query = new EntityQuery(source);
            query.AddFilter(CustomerNoAttributeName, customerNo);

            IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
            FwDataContext ctx = (FwDataContext)dataContextProvider.CreateDataContext();
            return ctx.Query(query).FirstOrDefault()?.Row;
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteFetchSchema", "Execution.Procedures.CustomerData");
        }
    }
}
