﻿using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace Ifs.Uma.AppData.Expressions
{
    internal sealed class MethodCallReplacer : IfsExpressionVisitor
    {
        private static readonly MethodInfo CallMethodMethod =
            typeof(MethodCallReplacer).GetTypeInfo().GetDeclaredMethod(nameof(CallMethod));

        private readonly ParameterExpression _methodRunnerParam;

        public static Expression Rewrite(Expression expression, ParameterExpression valueProviderParam)
        {
            IfsExpressionVisitor visitor = new MethodCallReplacer(valueProviderParam);
            return visitor.Visit(expression);
        }

        private MethodCallReplacer(ParameterExpression methodRunnerParam)
        {
            _methodRunnerParam = methodRunnerParam;
        }

        protected internal override Expression VisitMethod(MethodExpression exp)
        {
            Expression args = Expression.NewArrayInit(typeof(DynamicValue), exp.Arguments);
            return Expression.Call(CallMethodMethod, _methodRunnerParam, Expression.Constant(exp.MethodName), args);
        }

        private static DynamicValue CallMethod(IExpressionValueProvider methodRunner, string methodName, DynamicValue[] args)
        {
            if (methodRunner != null && methodRunner.TryCallMethod(methodName, args.Select(x => x.Value).ToArray(), out object result))
            {
                return new DynamicValue(result);
            }

            throw new ExpressionException($"Method call '{methodName}' is not supported here");
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            Expression operand = Visit(node.Operand);

            if (node.NodeType == ExpressionType.Convert && node.Type == operand.Type && node.Method == null)
            {
                return operand;
            }

            // Must override here since the base does some unwanted validation
            return node.Update(operand);
        }
    }
}
