﻿using System;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.UI.Observables;

namespace Ifs.Uma.Framework.UI.Attachments
{
    internal class AttachmentStatusConverter : IBindValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is string strValue)
            {
                if (Enum.TryParse(strValue, out AttachmentStatus status))
                {
                    return status.ToLocalisedString();
                }
            }

            if (value is AttachmentStatus attStatus)
            {
                return attStatus.ToLocalisedString();
            }

            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
