{
  "name": "FndTstOffline",
  "version": "1706901162:1948287535",
  "component": "FNDTST",
  "projection": {
    "service": "FndTstOffline.svc",
    "version": "1948287535",
    "contains": {
      "Customers": {
        "name": "Customers",
        "entity": "TstCustomer",
        "array": true
      },
      "CustomerAddresses": {
        "name": "CustomerAddresses",
        "entity": "TstCustomerAddress",
        "array": true
      }
    },
    "entities": {
      "TstCustomer": {
        "name": "TstCustomer",
        "hasETag": true,
        "CRUD": "Create,Read,Update,Delete",
        "luname": "TstCustomer",
        "ludependencies": [ "TstCustomer" ],
        "keys": [ "CustomerNo" ],
        "attributes": {
          "CustomerNo": { "datatype": "Text", "keygeneration": "User" },
          "CustomerName": { "datatype": "Text", "keygeneration": "User" },
          "CustomerType": { "datatype": "Text", "keygeneration": "User" },
          "CustomerTypeDesc": { "datatype": "Text", "keygeneration": "User" }
        },
        "references": {
          "CustomerTypeRef": {
            "target": "TstCustomerType",
            "mapping": {
              "CustomerType": "TypeId"
            },
          }
        },
        "arrays":  {
          "CustomerAddressArray": {
            "target": "TstCustomerAddress",
            "mapping": {
              "CustomerNo": "AddressCustomerNo"
            } 
          }
        } 
      },
      "TstCustomerType": {
        "name": "TstCustomerType",
        "hasETag": true,
        "CRUD": "Read",
        "luname": "TstCustomerType",
        "ludependencies": [ "TstCustomerType" ],
        "keys": [ "TypeId" ],
        "attributes": {
          "TypeId": { "datatype": "Text", "keygeneration": "User" },
          "TypeDescription": { "datatype": "Text", "keygeneration": "User" }
        }
      },
      "TstCustomerAddress": {
        "name": "TstCustomerAddress",
        "hasETag": true,
        "CRUD": "Create,Read,Update,Delete",
        "luname": "TstCustomerAddress",
        "ludependencies": [ "TstCustomerAddress" ],
        "keys": [ "AddressCustomerNo", "AddressId" ],
        "attributes": {
          "AddressCustomerNo": { "datatype": "Text", "keygeneration": "User" },
          "AddressId": { "datatype": "Text", "keygeneration": "User" },
          "AddressLine": { "datatype": "Text", "keygeneration": "User" }
        }
      }
    },
    "procedures": {
      
      "Function<CountCustomer>": {
        "name": "CountCustomer",
        "type": "Function",
        "layers": [
          {
            "vars": [ { "name": "Record" } ],
            "execute": [
              { "call": { "method": "count", "args": { "entity": "Customers" } }, "assign": "Record" },
              { "call": { "method": "return", "args": { "name": "Record" } } }
            ]
          }]
       },
    
      "Function<CountFromArray>": {
        "name": "CountFromArray",
        "type": "Function",
        "params": [ { "name": "Customer", "dataType": "Structure", "subType": "TstCustomer" } ],
        "layers": [
          {
            "vars": [ { "name": "Var1" } ],
            "execute": [
              { "call": { "method": "count", "args": { "name": "Customer.CustomerAddressArray" } }, "assign": "Var1" },
              { "call": { "method": "return", "args": { "name": "Var1" } } }
            ]
          }]
       },

      "Function<CountFromArrayWithWhere>": {
        "name": "CountFromArrayWithWhere",
        "type": "Function",
        "params": [ { "name": "Customer", "dataType": "Structure", "subType": "TstCustomer" } ],
        "layers": [
          {
            "vars": [ { "name": "Var1" } ],
            "execute": [
              {
                "call": {
                  "method": "count",
                  "args": {
                    "name": "Customer.CustomerAddressArray",
                    "where": { "==": [{ "var": "AddressId" }, "WORK"] }
                  }
                },
                "assign": "Var1"
              },
              { "call": { "method": "return", "args": { "name": "Var1" } } }
            ]
          }]
       },
    
      "Function<CountWhereAliased>": {
        "name": "CountWhereAliased",
        "type": "Function",
        "layers": [
          {
            "vars": [
              { "name": "Var1" },
              { "name": "AddressId" }
            ],
            "execute": [
              { "call": { "method": "set", "args": { "value": "WORK" } }, "assign": "AddressId" },
              {
                "call": {
                  "method": "count",
                  "args": {
                    "entity": "CustomerAddresses",
                    "alias": "i",
                    "where": { "==": [{ "var": "i.AddressId" }, { "var": "AddressId" }] }
                  }
                },
                "assign": "Var1"
              },
              { "call": { "method": "return", "args": { "name": "Var1" } } }
            ]
          }]
      }
    
    }
  }
}
