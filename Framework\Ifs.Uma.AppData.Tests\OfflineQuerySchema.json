{"name": "FndTstOffline", "component": "FNDTST", "version": "1706901162:1948287535", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}, "InUseCustomerTypesSet": {"name": "InUseCustomerTypesSet", "entity": "InUseCustomerTypes", "array": true}, "CustomersByNameSet": {"name": "CustomersByNameSet", "entity": "CustomersByName", "array": true}, "OuterQuerySet": {"name": "OuterQuerySet", "entity": "OuterQuery", "array": true}, "LoopQueryASet": {"name": "LoopQueryASet", "entity": "LoopQueryA", "array": true}, "ExpiredCustomersSet": {"name": "ExpiredCustomersSet", "entity": "ExpiredCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}, "CustomerType": {"datatype": "Text", "keygeneration": "User"}, "CustomerTypeDesc": {"datatype": "Text", "keygeneration": "User"}, "Expires": {"datatype": "Timestamp", "keygeneration": "User"}}, "references": {"CustomerTypeRef": {"target": "TstCustomerType", "mapping": {"CustomerType": "TypeId"}, "prefetch": {"CustomerTypeDesc": "TypeDescription"}}}, "arrays": {"ExpandsArray": {"target": "TstExpand", "mapping": {"CustomerNo": "ColA"}}, "ExpandsFilteredArray": {"target": "TstExpand", "mapping": {"CustomerNo": "ColA"}, "offlinefilter": {"==": [{"var": "Id"}, "<PERSON>"]}}}}, "TstCustomerType": {"name": "TstCustomerType", "hasETag": true, "CRUD": "Read", "luname": "TstCustomerType", "ludependencies": ["TstCustomerType"], "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text", "keygeneration": "User"}, "TypeDescription": {"datatype": "Text", "keygeneration": "User"}}}, "InUseCustomerTypes": {"name": "InUseCustomerTypes", "CRUD": "Read", "luname": "InUseCustomerTypes", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "c"}, "joins": [{"type": "Left", "entity": "TstCustomerType", "alias": "ct", "on": {"==": [{"var": "ct.TypeId"}, {"var": "c.CustomerType"}]}}], "select": {"distinct": true, "columns": [{"name": "ct.TypeId"}, {"name": "c.CustomerTypeRef.TypeDescription", "as": "Description"}]}}, "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text"}}}, "CustomersByName": {"name": "CustomersByName", "CRUD": "Read", "luname": "CustomersByName", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "c"}, "where": {"==": [{"var": "c.<PERSON><PERSON><PERSON>"}, "<PERSON>'<PERSON>n"]}, "select": {"columns": [{"name": "c.<PERSON><PERSON><PERSON><PERSON>"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}, "InnerQuery": {"name": "InnerQuery", "CRUD": "Read", "luname": "InnerQuery", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "q"}, "where": {"!=": [{"var": "q.Customer<PERSON>o"}, "2"]}, "select": {"columns": [{"name": "q.Customer<PERSON>o"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}, "OuterQuery": {"name": "OuterQuery", "CRUD": "Read", "luname": "OuterQuery", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "InnerQuery", "alias": "q"}, "where": {"!=": [{"var": "q.Customer<PERSON>o"}, "3"]}, "select": {"columns": [{"name": "q.Customer<PERSON>o"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}, "LoopQueryA": {"name": "LoopQueryA", "CRUD": "Read", "luname": "LoopQueryA", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "LoopQueryB", "alias": "qb"}, "select": {"columns": [{"name": "qb.Customer<PERSON>o"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}, "LoopQueryB": {"name": "LoopQueryB", "CRUD": "Read", "luname": "LoopQueryB", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "LoopQueryA", "alias": "qa"}, "select": {"columns": [{"name": "qa.Customer<PERSON>o"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}, "ExpiredCustomer": {"name": "ExpiredCustomer", "CRUD": "Read", "luname": "ExpiredCustomer", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "c"}, "where": {"<": [{"var": "c.Expires"}, {"method": ["System.DateTime"]}]}, "select": {"columns": [{"name": "c.<PERSON><PERSON><PERSON><PERSON>"}]}}, "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text"}}}}}}