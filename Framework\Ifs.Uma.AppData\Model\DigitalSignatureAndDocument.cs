﻿#if SIGNATURE_SERVICE
using System;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = FwDataContext.FwTablePrefix + "digital_signature_and_document", Class = MetaTableClass.App)]
    public class DigitalSignatureAndDocument
    {
        [Column(PrimaryKey = true)]
        public string Guid { get; set; }

        [Column]
        public DateTime? Timestamp { get; set; }

        [Column]
        public string DocumentData { get; set; }

        [Column]
        public string SignatureData { get; set; }

        [Column]
        public string JsonMetaData { get; set; }

        [Column]
        public string FileName { get; set; }

        [Column]
        public string LuName { get; set; }

        [Column]
        public string KeyRef { get; set; }

        [Column]
        public string ErrorMessage { get; set; }

        [Column]
        public string FailedStep { get; set; } // This mentions at which point the operation had failed

        [Column]
        public bool? IsSigningFailure { get; set; } // This is used to indicate if the error is due to signing or something else (like a network error)

        [Column]
        public string SessionId { get; set; }
    }
}
#endif
