﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Localization;
using Ifs.Uma.Framework.Execution.Commands;
using System.Collections.Generic;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.Framework.UI.Pages
{
    public class PageCommandItem : FwCommandItem
    {
        public PageCommand CommandType { get; }

        public CpiCrudAction CrudAction { get; set; }
        public CpiCommand CreateCommand { get; set; }

        private readonly ICommandExecutor _commandExecutor;
        private readonly IExpressionRunner _expressionRunner;
        private readonly Func<Task> _onCommand;
        private readonly IMetadata _metadata;

        public PageCommandItem(ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, PageCommand commandType, Func<Task> onCommand)
        {
            _commandExecutor = commandExecutor ?? throw new ArgumentNullException(nameof(commandExecutor));
            _expressionRunner = expressionRunner ?? throw new ArgumentNullException(nameof(expressionRunner));
            _metadata = Resolver.Resolve<IMetadata>();

            CommandType = commandType;
            _onCommand = onCommand ?? throw new ArgumentNullException(nameof(onCommand));
            IsVisible = true;

            switch (commandType)
            {
                case PageCommand.Create:
                    Text = Strings.Add;
                    Icon = Uma.UI.Icons.IconUtils.New;
                    break;
                case PageCommand.Delete:
                    Text = Strings.Delete;
                    Icon = Uma.UI.Icons.IconUtils.Delete;
                    break;
            }
        }

        protected override Task OnExecuteAsync()
        {
            if (CommandType == PageCommand.Create && CreateCommand != null)
            {
                return _commandExecutor.ExecuteAsync(ProjectionName, ViewData, CreateCommand);
            }
            else
            {
                return _onCommand();
            }
        }

        protected override void OnUpdateStates(ViewData viewData, bool isUpdating)
        {
            base.OnUpdateStates(viewData, isUpdating);

            if (viewData != null && RecordData.HasLoadedRecord(viewData.Record))
            {
                bool isInsertedRow = !viewData.Record.IsNew();
                bool hasChanges = PageData.HasChanges;
                CpiExpression enabled = CrudAction?.OfflineEnabled ?? CrudAction?.Enabled;

                switch (CommandType)
                {
                    case PageCommand.Create:
                        if (CreateCommand != null)
                        {
                            _commandExecutor.GetStates(ProjectionName, ViewData, CreateCommand, true, out bool isVisible, out bool isEnabled);
                            IsVisible = isVisible;
                            IsEnabled = isVisible && isEnabled && !isUpdating && !hasChanges;
                        }
                        else
                        {
                            bool canCreate = isInsertedRow && viewData.Record.EntityCrudType.CanUse(CpiCrudType.Create);

                            if (canCreate && enabled != null)
                            {
                                canCreate = _expressionRunner.RunCheck(enabled, viewData, true);
                            }

                            IsVisible = canCreate;
                            IsEnabled = canCreate && !isUpdating && !hasChanges;
                        }
                        break;
                    case PageCommand.Delete:
                        bool canDelete = isInsertedRow && viewData.Record.EntityCrudType.CanUse(CpiCrudType.Delete);

                        if (canDelete && enabled != null)
                        {
                            canDelete = _expressionRunner.RunCheck(enabled, viewData, true);
                        }

                        IsVisible = canDelete;
                        IsEnabled = canDelete && !isUpdating && !hasChanges;
                        break;
                }
            }
            else
            {
                IsVisible = false;
                IsEnabled = false;
            }
        }

        public override void GetSelectAttributes(ICollection<string> attributes)
        {
            base.GetSelectAttributes(attributes);

            AttributeFinder.FindInCommand(attributes, _metadata, ProjectionName, CreateCommand);
        }
    }

    public enum PageCommand
    {
        Create,
        Delete
    }
}
