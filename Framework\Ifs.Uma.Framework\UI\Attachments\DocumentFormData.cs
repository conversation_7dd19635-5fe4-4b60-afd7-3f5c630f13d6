﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Framework.UI.Forms;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Services.Attachments.Documents;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.UI.Summaries;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public sealed class DocumentFormData : FormData, IActivable
    {
        private readonly IMetadata _metadata;
        private readonly ILookupService _lookupController;
        private readonly IDocumentHandler _documentHandler;
        private readonly IDialogService _dialogService;
        private readonly IToastService _toastService;
        private readonly IFileService _fileService;
        private readonly IEventAggregator _eventAggregator;
        private readonly INavigator _navigator;

        private string _formTitleText = Strings.DocumentDetails;
        public string FormTitleText
        {
            get => _formTitleText;
            set => SetProperty(ref _formTitleText, value);
        }

        private MobileDocClass _documentClass;
        public MobileDocClass DocumentClass
        {
            get => _documentClass;
            set
            {
                SetProperty(ref _documentClass, value);
                UpdateDocumentClassDisplay();
            }
        }

        private string _documentClassDisplay;
        public string DocumentClassDisplay
        {
            get => _documentClassDisplay;
            set => SetProperty(ref _documentClassDisplay, value);
        }

        private DocReferenceObject _documentRevision;
        public DocReferenceObject DocumentReference
        {
            get => _documentRevision;
            set => SetProperty(ref _documentRevision, value);
        }

        private EdmFile _edmFile;
        public EdmFile EdmFile
        {
            get => _edmFile;
            set => SetProperty(ref _edmFile, value);
        }

        private PickedFile _document;
        public PickedFile Document
        {
            get => _document;
            set => SetProperty(ref _document, value);
        }

        private PickedFile _revisedDocument;
        public PickedFile RevisedDocument
        {
            get => _revisedDocument;
            set => SetProperty(ref _revisedDocument, value);
        }

        private bool _isActive;
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (SetProperty(ref _isActive, value))
                {
                    OnIsActiveChanged();
                }
            }
        }

        private bool _navigateBackOnSave;
        public bool NavigateBackOnSave
        {
            get => _navigateBackOnSave;
            set => SetProperty(ref _navigateBackOnSave, value);
        }

        private bool CanAddDocument { get; set; }
        private bool CanReviseDocument { get; set; }

        public Command DownloadDocumentCommand { get; }
        public Command UploadDocumentCommand { get; }
        public Command ViewDocumentCommand { get; }

        private List<string> _allowedExtensions;
        private List<EdmApplication> _edmApplications;

        private LookupField _documentClassLookup;
        private ComboField _fileTypeCombo;
        private TextField _titleField;
        private Field _statusField;
        private LargeTextEdit _failReasonField;
        private CommandField _downloadDocumentField;
        private CommandField _uploadDocumentField;
        private CommandField _viewDocumentField;
        private FilePickerField _filePicker;
        private FilePickerField _reviseFilePicker;
        private HeaderField _reviseDocumentHeader;

        private bool _downloadRequested;
        private readonly ImageOptions _imageOptions;
        private readonly bool _canUpdateApprovedOrReleasedDocTitle;
        private DocumentNavParam _navParam;

        public DocumentFormData(ILookupService lookupController,
            IDocumentHandler documentHandler, IDialogService dialogService, IToastService toast,
            IMetadata metadata, IFileService fileService, IAppParameters appParams, IEventAggregator eventAggregator,
            INavigator navigator)
        {
            _lookupController = lookupController;
            _documentHandler = documentHandler;
            _dialogService = dialogService;
            _toastService = toast;
            _metadata = metadata;
            _fileService = fileService;
            _eventAggregator = eventAggregator;
            _navigator = navigator;
            _imageOptions = new ImageOptions(appParams.GetPictureMaxDimension(), appParams.GetPictureMaxBytes(), appParams.IsExistingDeviceMediaAllowed());
            _canUpdateApprovedOrReleasedDocTitle = appParams.CanUpdateApprovedOrReleasedDocTitle();

            DownloadDocumentCommand = Command.FromMethod(OnDownloadDocumentCommand);
            UploadDocumentCommand = Command.FromMethod(OnUploadDocumentCommand);
            ViewDocumentCommand = Command.FromMethod(OnViewDocumentCommand);

            SetupForm();
            UpdateFormState();
            UpdateFieldStates();
        }

        #region Form

        protected override Form OnSetupForm()
        {
            FormBuilder<DocumentFormData> fb = new FormBuilder<DocumentFormData>(_metadata, this);
            fb.OverrideFieldType(fb.Path((x) => x.EdmFile.FileType), typeof(ComboField));

            // Document Fields
            {
                _titleField = (TextField)fb.AddField(x => x.DocumentReference.Title);
                _titleField.Name = Strings.Title;
                _titleField.Editability = FieldEditability.InsertingOrUpdating;
                _titleField.IsRequired = true;
                _titleField.MaxLength = 250;

                Field docClass = fb.AddField(x => x.DocumentClassDisplay);
                docClass.Name = Strings.DocumentClass;
                docClass.Editability = FieldEditability.Never;

                _documentClassLookup = fb.AddLookupField(Strings.DocumentClass, x => x.DocumentClassDisplay);
                _documentClassLookup.IsRequired = true;
                _documentClassLookup.BackingField = docClass;
                _documentClassLookup.Editability = FieldEditability.Inserting;
                _documentClassLookup.Command = Command.FromAsyncMethod(ShowDocumentClassLookupAsync);
                _documentClassLookup.IsReadOnly = false;

                _statusField = fb.AddField(x => x.EdmFile.AttachmentStatus);
                _statusField.Name = Strings.Status;
                _statusField.Editability = FieldEditability.Never;
                _statusField.Converter = new AttachmentStatusConverter();

                fb.OverrideFieldType(fb.Path(x => x.EdmFile.FailReason), typeof(LargeTextEdit));
                _failReasonField = (LargeTextEdit)fb.AddField(x => x.EdmFile.FailReason);
                _failReasonField.Name = Strings.FailureReason;
                _failReasonField.Editability = FieldEditability.Never;
                _failReasonField.Converter = new FailureReasonMessageConverter();

                _fileTypeCombo = (ComboField)fb.AddField(x => x.EdmFile.FileType);
                _fileTypeCombo.Name = Strings.FileType;
                _fileTypeCombo.Editability = FieldEditability.Inserting;
                _fileTypeCombo.IsRequired = true;

                FilePickerField filePicker = (FilePickerField)fb.AddField(x => x.Document);
                filePicker.Name = Strings.Document;
                filePicker.Editability = FieldEditability.Inserting;
                filePicker.IsRequired = true;
                filePicker.FileService = _fileService;
                filePicker.ImageOptions = _imageOptions;
                filePicker.ValueChanged += Document_ValueChanged;
                _filePicker = filePicker;

                _downloadDocumentField = fb.AddCommandField(nameof(DownloadDocumentCommand), Strings.DownloadDocument, DownloadDocumentCommand);
                _downloadDocumentField.Icon = IconUtils.Download;

                _uploadDocumentField = fb.AddCommandField(nameof(UploadDocumentCommand), Strings.RetryDocumentUpload, UploadDocumentCommand);
                _uploadDocumentField.Icon = IconUtils.Upload;

                _viewDocumentField = fb.AddCommandField(nameof(ViewDocumentCommand), Strings.ViewDocument, ViewDocumentCommand);
                _viewDocumentField.Icon = IconUtils.View;
            }

            // Revision Fields
            {
                _reviseDocumentHeader = fb.AddHeaderField("ReviseDocumentHeader", Strings.ReviseDocument);
                _reviseDocumentHeader.IsVisible = false;

                FilePickerField reviseFilePicker = (FilePickerField)fb.AddField(x => x.RevisedDocument);
                reviseFilePicker.Name = Strings.Document;
                reviseFilePicker.Editability = FieldEditability.Updating;
                reviseFilePicker.IsVisible = false;
                reviseFilePicker.FileService = _fileService;
                reviseFilePicker.ImageOptions = _imageOptions;
                reviseFilePicker.ValueChanged += RevisedDoument_ValueChanged;
                _reviseFilePicker = reviseFilePicker;
            }

            fb.SetLayout(new[]
            {
                new[] { _filePicker.Id },

                new[] { _titleField.Id },
                new[] { _statusField.Id },
                new[] { _failReasonField.Id },

                new[] { _downloadDocumentField.Id },
                new[] { _uploadDocumentField.Id },
                new[] { _viewDocumentField.Id },

                new[] { _documentClassLookup.Id },
                new[] { _fileTypeCombo.Id },

                new[] { _reviseDocumentHeader.Id },
                new[] { _reviseFilePicker.Id }
            });

            return fb.Form;
        }

        private void UpdateFormState()
        {
            bool isNew = IsNew();

            if (DocumentReference == null)
            {
                Form.EditState = FieldEditState.ReadOnly;
            }
            else
            {
                bool canEdit = isNew ? CanAddDocument : CanReviseDocument;

                if (canEdit)
                {
                    Form.EditState = isNew ? FieldEditState.Insert : FieldEditState.Update;
                }
                else
                {
                    Form.EditState = FieldEditState.ReadOnly;
                }
            }
        }

        private void UpdateFieldStates()
        {
            bool isNew = IsNew();
            bool isExisting = DocumentReference != null && !isNew;
            bool hasEditAccess = DocumentReference != null && (DocumentReference.DocumentAccess == DocumentAccess.Edit || DocumentReference.DocumentAccess == DocumentAccess.Admin);

            _filePicker.IsReadOnly = DocumentReference == null || isExisting || !CanAddDocument;
            _documentClassLookup.IsVisible = isNew;

            int fileTypeCount = 0;
            if (_fileTypeCombo.ItemsSource is IEnumerable<SelectableItem<string>> selectableFileTypes)
            {
                fileTypeCount = selectableFileTypes.Count();
            }

            _fileTypeCombo.IsReadOnly = isExisting || fileTypeCount <= 1;
            _fileTypeCombo.IsVisible = fileTypeCount > 0;

            _statusField.IsVisible = (EdmFile?.AttachmentStatus).ShouldDisplay(_downloadRequested);
            _failReasonField.IsVisible = EdmFile != null && (EdmFile.AttachmentStatus == AttachmentStatus.UploadFailed || EdmFile.AttachmentStatus == AttachmentStatus.DownloadFailed);

            bool isApprovedOrReleased = DocumentReference != null && (DocumentReference.DocIssueObjstate == DocIssueObjstate.Approved || DocumentReference.DocIssueObjstate == DocIssueObjstate.Released);
            bool canEditTitle = hasEditAccess && (isNew || !isApprovedOrReleased || _canUpdateApprovedOrReleasedDocTitle);
            _titleField.IsReadOnly = !canEditTitle;

            bool hasSavedEdmFile = EdmFile != null && EdmFile.RowId != 0;
            bool isTransferActive = EdmFile != null && EdmFile.AttachmentStatus.IsTransferActive();
            DownloadDocumentCommand.IsEnabled = Document == null && hasSavedEdmFile && !isTransferActive;
            UploadDocumentCommand.IsEnabled = Document != null && hasSavedEdmFile && EdmFile.AttachmentStatus == AttachmentStatus.UploadFailed;
            ViewDocumentCommand.IsEnabled = Document != null;

            _downloadDocumentField.IsVisible = DownloadDocumentCommand.IsEnabled;
            _uploadDocumentField.IsVisible = UploadDocumentCommand.IsEnabled;
            _viewDocumentField.IsVisible = ViewDocumentCommand.IsEnabled;

            bool canReviseDocument = Form.EditState != FieldEditState.ReadOnly && isExisting && hasEditAccess;
            _reviseDocumentHeader.IsVisible = canReviseDocument;
            _reviseFilePicker.IsVisible = canReviseDocument;
            _reviseFilePicker.IsRequired = _titleField.ActualIsReadOnly;
        }

        private void UpdateFormTitleText()
        {
            if (IsNew())
            {
                FormTitleText = Strings.AddDocument;
            }
            else if (HasChanges)
            {
                FormTitleText = Strings.ReviseDocument;
            }
            else
            {
                FormTitleText = Strings.DocumentDetails;
            }
        }

        private void UpdateDocumentClassDisplay()
        {
            if (_documentClass == null)
            {
                DocumentClassDisplay = null;
            }
            else if (string.IsNullOrWhiteSpace(_documentClass.DocName))
            {
                DocumentClassDisplay = _documentClass.DocClass;
            }
            else
            {
                DocumentClassDisplay = $"{_documentClass.DocClass} - {_documentClass.DocName}";
            }
        }

        private bool IsNew()
        {
            return DocumentReference != null && DocumentReference.RowId == 0;
        }

        #endregion

        #region Lookup Handling

        private async Task ShowDocumentClassLookupAsync()
        {
            LookupResult<MobileDocClass> result = await LookupDocumentClass(DocumentReference?.DocClass);

            if (!result.Canceled && DocumentReference != null)
            {
                DocumentReference.DocClass = result.Item?.DocClass;
                DocumentClass = result.Item;
            }
        }

        private async Task<LookupResult<MobileDocClass>> LookupDocumentClass(string selectedDocClass)
        {
            MobileDocClass[] docClasses = await _documentHandler.GetSelectableDocumentClassesAsync();

            if (string.IsNullOrEmpty(selectedDocClass) || selectedDocClass.Equals("*"))
            {
                selectedDocClass = docClasses
                    .Where(x => x.MobileDefault == FndBoolean.True)
                    .Select(x => x.DocClass)
                    .FirstOrDefault();
            }

            SummaryDef summaryDef = new SummaryDef();

            SummaryDefItem item = new SummaryDefItem();
            item.LabelLocation = LabelLocation.None;
            item.Id = nameof(MobileDocClass.DocName);
            item.Name = Strings.DocumentClass;
            item.BindingPath = $"[{nameof(MobileDocClass.DocName)}]";
            item.Row = 0;
            item.Column = 0;
            summaryDef.Items.Add(item);

            item = new SummaryDefItem();
            item.LabelLocation = LabelLocation.None;
            item.Id = nameof(MobileDocClass.DocClass);
            item.Name = Strings.DocumentClass;
            item.BindingPath = $"[{nameof(MobileDocClass.DocClass)}]";
            item.Row = 0;
            item.Column = 1;
            summaryDef.Items.Add(item);

            MobileDocClass currentItem = docClasses.FirstOrDefault(x => x.DocClass == selectedDocClass);
            LookupRequest<MobileDocClass> request = new LookupRequest<MobileDocClass>(Strings.DocumentClass, docClasses, currentItem);
            request.AllowNull = false;
            request.SummaryDef = summaryDef;
            return await _lookupController.OpenLookup(request);
        }

        #endregion

        private void OnIsActiveChanged()
        {
            if (IsActive)
            {
                _eventAggregator.GetEvent<DocumentStatusChangedEvent>().Subscribe(OnDocumentStatusChanged, ThreadOption.UIThread);
            }
            else
            {
                _eventAggregator.GetEvent<DocumentStatusChangedEvent>().Unsubscribe(OnDocumentStatusChanged);
            }
        }

        public async Task Load(DocumentNavParam navParam)
        {
            _navParam = navParam;

            CanAddDocument = navParam != null && await _documentHandler.CanCreateNew(_navParam.EntityName, _navParam.KeyRef);
            CanReviseDocument = navParam != null && _navParam.RevisionEnabled && await _documentHandler.CanReviseDocument(_navParam.EntityName, _navParam.KeyRef);

            if (_navParam != null && _navParam.DocumentReferenceRowId.HasValue)
            {
                await LoadExistingDocumentRevisionAsync(_navParam.DocumentReferenceRowId.Value);
            }
            else
            {
                await LoadNewDocumentRevisionAsync();
            }
        }

        private async Task ReloadAsync(long? docRefRowId)
        {
            if (docRefRowId == null)
            {
                await LoadNewDocumentRevisionAsync();
            }
            else
            {
                await LoadExistingDocumentRevisionAsync(docRefRowId.Value);
            }
        }

        public async Task LoadNewDocumentRevisionAsync()
        {
            if (!CanAddDocument)
            {
                await LoadExtensionDataAsync();
                InitialiseDocumentManagement(null, null, null, null);
            }
            else
            {
                DocReferenceObject documentRevision = new DocReferenceObject();
                documentRevision.DocumentAccess = DocumentAccess.Edit;
                documentRevision.DocIssueObjstate = DocIssueObjstate.Preliminary;
                EdmFile edmFile = new EdmFile();
                MobileDocClass defaultDocumentClass = await _documentHandler.GetDefaultDocumentClassAsync();

                if (defaultDocumentClass != null)
                {
                    documentRevision.DocClass = defaultDocumentClass.DocClass;
                }

                await LoadExtensionDataAsync();
                InitialiseDocumentManagement(documentRevision, edmFile, defaultDocumentClass, null);
            }
        }

        private async Task LoadExistingDocumentRevisionAsync(long docRowId)
        {
            DocRevisionInfo doc = await _documentHandler.GetDocumentInfoAsync(docRowId);

            if (doc == null)
            {
                await LoadNewDocumentRevisionAsync();
                return;
            }

            PickedFile pickedFile = null;
            ILocalFileInfo file = await _documentHandler.GetLocalFileForDocumentAsync(doc.EdmFile);
            bool fileExists = await file.ExistsAsync();
            if (fileExists)
            {
                pickedFile = new PickedFile()
                {
                    Data = file,
                    FileName = doc.EdmFile.FileName
                };
            }

            await LoadExtensionDataAsync();
            InitialiseDocumentManagement(doc.DocumentRevision, doc.EdmFile, doc.DocumentClass, pickedFile);
        }

        private async Task LoadExtensionDataAsync()
        {
            if (_edmApplications == null)
            {
                _edmApplications = (await _documentHandler.GetEdmApplicationsAsync()).ToList();
                _allowedExtensions = _edmApplications.Select(x => x.FileExtention).ToList();
            }
        }

        private void InitialiseDocumentManagement(DocReferenceObject documentRevision, EdmFile edmFile, MobileDocClass docClass, PickedFile file)
        {
            if (DocumentReference != null)
            {
                DocumentReference.PropertyChanged -= Data_PropertyChanged;
            }

            if (EdmFile != null)
            {
                EdmFile.PropertyChanged -= Data_PropertyChanged;
            }

            Form.ClearValidation();

            DocumentReference = documentRevision;
            EdmFile = edmFile;
            DocumentClass = docClass;
            _downloadRequested = false;
            Document = file;
            RevisedDocument = null;
            HasChanges = false;

            UpdateFormState();
            UpdateFormTitleText();
            UpdateSelectableFileTypes();
            UpdateSelectableExtensions();
            UpdateFieldStates();

            if (EdmFile != null)
            {
                EdmFile.PropertyChanged += Data_PropertyChanged;
            }

            if (DocumentReference != null)
            {
                DocumentReference.PropertyChanged += Data_PropertyChanged;
            }
        }

        private void Data_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(EdmFile.FileType))
            {
                UpdateSelectableExtensions();
            }

            if (e.PropertyName == nameof(EdmFile.AttachmentStatus) ||
                e.PropertyName == nameof(EdmFile.FailReason))
            {
                UpdateFieldStates();
            }
            else
            {
                HasChanges = true;
            }
        }

        protected override bool OnValidate()
        {
            Form.ClearValidation();

            bool valid = Form.ValidateRequiredFields();

            PickedFile doc = RevisedDocument ?? Document;

            EdmApplication currentApplication = DocumentReference == null ? null : _edmApplications.FirstOrDefault(x => x.FileType == EdmFile.FileType);
            if (currentApplication == null || (IsNew() && doc != null && !IsExtensionAllowed(doc.FileExtension)))
            {
                valid = false;
                Form.SetFieldInvalid(_filePicker, Strings.InvalidDocumentType);
                Form.FocusOnField(_filePicker);
            }

            if (RevisedDocument != null && !MatchesAvailableFileTypes(RevisedDocument.FileExtension))
            {
                valid = false;
                Form.SetFieldInvalid(_reviseFilePicker, Strings.InvalidDocumentType);
                Form.FocusOnField(_reviseFilePicker);
            }

            if (DocumentClass?.MaxDocSize != null && doc?.ContentSize != null)
            {
                // MaxDocSize is in megabytes. 
                long fileLimit = (long)(DocumentClass.MaxDocSize.Value * 1000000);

                if (fileLimit < doc.ContentSize.Value)
                {
                    valid = false;

                    Field docField = IsNew() ? _filePicker : _reviseFilePicker;
                    Form.SetFieldInvalid(docField, Strings.DocumentExceedsMaximumDocSize);
                    Form.FocusOnField(docField);
                }
            }

            return valid;
        }

        protected override async Task OnSaveChangesAsync()
        {
            bool isNew = IsNew();

            long docRevRowId = DocumentReference.RowId;

            if (Document != null && isNew)
            {
                DocumentReference.LuName = _navParam?.EntityName;
                DocumentReference.KeyRef = _navParam?.KeyRef;

                Stream dataStream = await Document.ReadAsync();

                docRevRowId = await _documentHandler.NewDocumentAsync(DocumentReference, EdmFile, dataStream);
            }
            else if (RevisedDocument != null && !isNew)
            {
                UpdateSelectableFileTypes();
                EdmFile edmFile = new EdmFile();
                edmFile.FileName = RevisedDocument.FileName;
                if (_fileTypeCombo.ItemsSource is IEnumerable<SelectableItem<string>> selectableFileTypes)
                {
                    SelectableItem<string> item = selectableFileTypes.FirstOrDefault();
                    edmFile.FileType = item?.Value;
                }

                Stream dataStream = await RevisedDocument.ReadAsync();

                docRevRowId = await _documentHandler.NewDocumentRevisionAsync(DocumentReference, edmFile, dataStream);
            }
            else
            {
                await _documentHandler.UpdateDocumentReferenceAsync(DocumentReference, new[] { nameof(DocumentReference.Title) });
            }

            bool didNavigateBack = false;
            if (isNew && _navParam?.DocumentReferenceRowId == null && _navigator.CanNavigateBack() && NavigateBackOnSave)
            {
                // If the user is adding a new document item and came to this screen by the 
                // media list then take them back to the media list instead
                // of leaving them on the media they just added
                HasChanges = false; // Make sure we reset disabled UI before navigating back
                didNavigateBack = await _navigator.NavigateBackAsync();
            }

            if (isNew)
            {
                _toastService.Show(ToastType.Success, Strings.DocumentCreatedSuccessfully);
            }

            if (NavigateBackOnSave && !didNavigateBack)
            {
                await ReloadAsync(docRevRowId);
            }
            else
            {
                await LoadNewDocumentRevisionAsync();
            }
        }

        protected override async Task OnCancelChangesAsync()
        {
            await ReloadAsync(DocumentReference?.RowId);
        }

        private async void OnDownloadDocumentCommand()
        {
            _downloadRequested = true;

            var file = await _documentHandler.GetLocalFileForDocumentAsync(EdmFile);
            if (!await file.ExistsAsync())
            {
                await _documentHandler.RequestDownloadAsync(EdmFile);
            }

            UpdateFieldStates();
        }

        private async void OnUploadDocumentCommand()
        {
            var file = await _documentHandler.GetLocalFileForDocumentAsync(EdmFile);
            if (await file.ExistsAsync())
            {
                await _documentHandler.RequestUploadAsync(EdmFile);
            }

            UpdateFieldStates();
        }

        private async void OnViewDocumentCommand()
        {
            if (Document != null && !await _fileService.LaunchFileAsync(Document))
            {
                await _dialogService.Alert(string.Empty, Strings.FailedToOpenDocument);
            }
        }

        private void Document_ValueChanged(object sender, ValueChangedEventArgs e)
        {
            if (e.Source == ValueChangedSource.Observer && Document != null)
            {
                EdmFile.FileName = Document.FileName;
                DocumentReference.Title = Document.DisplayName;

                UpdateSelectableFileTypes();

                if (_fileTypeCombo.ItemsSource is IEnumerable<SelectableItem<string>> selectableFileTypes)
                {
                    SelectableItem<string> item = selectableFileTypes.FirstOrDefault();
                    EdmFile.FileType = item?.Value;
                }

                HasChanges = true;
                UpdateFieldStates();
            }
        }

        private void RevisedDoument_ValueChanged(object sender, ValueChangedEventArgs e)
        {
            if (e.Source == ValueChangedSource.Observer && RevisedDocument != null)
            {
                HasChanges = true;
                UpdateFieldStates();
            }
        }

        public async void OnDocumentStatusChanged(DocumentStatusChangedEventArgs args)
        {
            EdmFile edmFile = EdmFile;
            if (edmFile != null && edmFile.RowId == args.EdmFileRowId)
            {
                edmFile.AttachmentStatus = args.Status;
                edmFile.FailReason = args.FailReason;

                if (!IsNew() && Document == null)
                {
                    ILocalFileInfo file = await _documentHandler.GetLocalFileForDocumentAsync(edmFile);
                    bool fileExists = await file.ExistsAsync();

                    if (fileExists)
                    {
                        _document = new PickedFile()
                        {
                            Data = file,
                            FileName = EdmFile?.FileName
                        };
                        OnPropertyChanged(() => Document);
                    }
                }

                UpdateFieldStates();
            }
        }

        #region Utility

        private void UpdateSelectableExtensions()
        {
            string[] extensions = null;
            if (DocumentReference != null)
            {
                extensions = _allowedExtensions.ToArray();
            }

            _filePicker.FileExtensions = extensions;
            _reviseFilePicker.FileExtensions = extensions;
        }

        private bool MatchesAvailableFileTypes(string fileExtension)
        {
            if (DocumentReference == null || IsNew())
            {
                return true;
            }

            if (string.IsNullOrWhiteSpace(fileExtension))
            {
                return false;
            }

            foreach (EdmApplication edm in _edmApplications)
            {
                if (fileExtension.Equals(edm.FileExtention, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        private void UpdateSelectableFileTypes()
        {
            bool existingDocument = DocumentReference != null && !IsNew();

            ObservableCollection<SelectableItem<string>> fileTypes = new ObservableCollection<SelectableItem<string>>();

            if (RevisedDocument != null)
            {
                AddCurrentFileType(fileTypes, RevisedDocument.FileExtension);
            }
            else if (existingDocument)
            {
                foreach (EdmApplication edm in _edmApplications)
                {
                    if (EdmFile.FileType.Equals(edm.FileType, StringComparison.OrdinalIgnoreCase))
                    {
                        fileTypes.Add(new SelectableItem<string>(edm.Description, edm.FileType));
                        break;
                    }
                }
            }
            else if (Document != null)
            {
                AddCurrentFileType(fileTypes, Document.FileExtension);
            }

            UpdateFileTypeItemSource(fileTypes);
        }

        private void AddCurrentFileType(ObservableCollection<SelectableItem<string>> fileTypes, string fileExtension)
        {
            if (IsExtensionAllowed(fileExtension))
            {
                foreach (EdmApplication edm in _edmApplications)
                {
                    if (fileExtension.Equals(edm.FileExtention, StringComparison.OrdinalIgnoreCase))
                    {
                        fileTypes.Add(new SelectableItem<string>(edm.Description, edm.FileType));
                        break;
                    }
                }
            }
        }

        private bool IsExtensionAllowed(string fileExtension)
        {
            return !string.IsNullOrWhiteSpace(fileExtension) &&
                _allowedExtensions.Any(x => x.Equals(fileExtension, StringComparison.OrdinalIgnoreCase));
        }

        private void UpdateFileTypeItemSource(ObservableCollection<SelectableItem<string>> fileTypes)
        {
            // TODO: Note: item source resets the already selected FileType! Win10 issue.... 
            // need to place correct put correct implementation in UMA

            bool bothCollectionsSame = false;
            if (_fileTypeCombo.ItemsSource is Collection<SelectableItem<string>> existingItems)
            {
                IEnumerable<string> existingItemsValues = existingItems.Select(i => i.Value);
                IEnumerable<string> fileTypesValues = fileTypes.Select(i => i.Value);
                bothCollectionsSame = existingItemsValues.SequenceEqual(fileTypesValues);
            }

            if (!bothCollectionsSame)
            {
                _fileTypeCombo.ItemsSource = fileTypes;
            }
        }

        #endregion

        internal class FailureReasonMessageConverter : IBindValueConverter
        {
            private static readonly string[] KnownErrors = new[]
            {
                "ORA-20105:" // Max document size exceeded.
            };

            public object Convert(object value, Type targetType, object parameter, string language)
            {
                if (value is string strValue)
                {
                    foreach (string knownError in KnownErrors)
                    {
                        if (strValue.StartsWith(knownError))
                        {
                            strValue = strValue.Substring(knownError.Length).Trim();
                            break;
                        }
                    }

                    return strValue;
                }

                return value;
            }

            public object ConvertBack(object value, Type targetType, object parameter, string language)
            {
                throw new NotImplementedException();
            }
        }
    }
}
