﻿using Ifs.Cloud.Client;
using Ifs.Uma.UI.Images;
using Ifs.Uma.Utility;
using System;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Unity;

namespace Ifs.Uma.Framework.App
{
    public abstract class AppRegistration
    {
        public abstract string Name { get; }
        public abstract string AppName { get; }
        public abstract UmaImage AppLogo { get; }
        public abstract string AuthenticationRedirectUri { get; }
        public virtual Type DatabaseControllerType { get; } = typeof(DatabaseController<FwDataContext>);

        public string DisplayVersion => string.Join(".",
                                            ApplicationInfo.FrameworkVersion.Major,
                                            ApplicationInfo.FrameworkVersion.Minor,
                                            ApplicationInfo.FrameworkVersion.Build,
                                            ApplicationInfo.FrameworkVersion.Revision);

        public virtual ClientInfo GetClientInfo()
        {
            ClientInfo ci = new ClientInfo();
            ci.ApplicationId = ApplicationInfo.ApplicationId;
            ci.AppVersion = DisplayVersion;
            ci.ClientAppVendor = "IFS";
            ci.AppName = AppName;
            ci.PackageName = AppName;
            return ci;
        }

        public virtual void ApplyRegistrations(IUnityContainer container)
        {
        }

        public UmaImage CreateAppMenuIcon()
        {
            return AppLogo.NewWithColor(UmaColors.White);
        }

        public UmaImage CreateAppMenuIcon(UmaColor umaColor)
        {
            return AppLogo.NewWithColor(umaColor);
        }
    }
}
