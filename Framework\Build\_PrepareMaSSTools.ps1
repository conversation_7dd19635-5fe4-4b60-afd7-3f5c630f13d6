Write-Output "============ Prepare MaSS Tools"


if (Test-Path $massToolsDir)
{
    Push-Location $massToolsDir
    git clean -xdf
    git pull
    if ($LastExitCode -ne 0) { Exit $LastExitCode }
    Pop-Location
} 
else 
{
    Remove-Item -Force -Recurse $massToolsDir -ErrorAction SilentlyContinue
    git clone -b master --depth 1 "https://<EMAIL>/ifs-pd/ifs-technology-mobile-masstools.git" $massToolsDir
    if ($LastExitCode -ne 0) { Exit $LastExitCode }
}

Push-Location "$massToolsDir\Win8AppInstaller\Build"
.\Build.bat
if ($LastExitCode -ne 0) { Exit $LastExitCode }
Pop-Location

Write-Output "============ End - Prepare"