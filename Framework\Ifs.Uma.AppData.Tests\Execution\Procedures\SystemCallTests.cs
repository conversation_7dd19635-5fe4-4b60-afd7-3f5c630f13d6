﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;
using NUnit.Framework;
using Unity;
using Unity.Lifetime;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class SystemCallTests : ProcedureTest
    {
        [Test]
        public async Task LogLocation()
        {
            //procedure Function<TestLogLocation> {
            //    variable Var1 Text;
            //    execute {
            //        set Var1 = "AAA";
            //        call System.LogLocation("Test info ${Var1}");
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestLogLocation", null);
            result.CheckFailure();

            TestLocationLogger locationLogger = (TestLocationLogger)Resolve<ILocationLogger>();
            Assert.AreEqual("Test info AAA", locationLogger.LastInfo);
        }

        [Test]
        public async Task LogLocationBeforeErrorTransaction()
        {
            // If the procedure fails the location should not be logged

            //procedure Action<LogLocationBeforeErrorTransaction> {
            //    execute {
            //        call System.LogLocation("Test info");
            //        error("ProcedureTestError");
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallActionAsync(TestOfflineProjection, "TestLogLocationBeforeErrorTransaction", null);
            Assert.IsTrue(result.Failed);

            TestLocationLogger locationLogger = (TestLocationLogger)Resolve<ILocationLogger>();
            Assert.IsNull(locationLogger.LastInfo);
        }
        
        [Test]
        public async Task LogLocationBeforeError()
        {
            // If the procedure fails the location will still be logged

            //procedure Function<TestLogLocationBeforeError> {
            //    execute {
            //        call System.LogLocation("Test info");
            //        error("ProcedureTestError");
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestLogLocationBeforeError", null);
            Assert.IsTrue(result.Failed);

            TestLocationLogger locationLogger = (TestLocationLogger)Resolve<ILocationLogger>();
            Assert.IsNotNull(locationLogger.LastInfo);
        }

        [Test]
        public async Task GenerateGuid()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "GenerateGuid", null);

            string guid = result.Value.ToString();
            Assert.IsTrue(Guid.TryParseExact(guid, "N", out Guid output));
        }

        [Test]
        public async Task Get_TimeZone()
        {
            //procedure Function<TimeZone> string {
            //    execute {
            //        return TimeZoneConverter.CurrentTimeZoneInfo;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TimeZone", null);
            string timeZoneFromFunction = result.Value as string;

            Assert.AreEqual(PlatformServices.TimezoneService.Local, timeZoneFromFunction);
        }

        [Test]
        [TestCase("foobar", "SHA256", "C3AB8FF13720E8AD9047DD39466B3C8974E592C2FA383D4A3960714CAEF0C4F2")]
        [TestCase("foobar", "SHA384", "3C9C30D9F665E74D515C842960D4A451C83A0125FD3DE7392D7B37231AF10C72EA58AEDFCDF89A5765BF902AF93ECF06")]
        [TestCase("foobar", "SHA512", "0A50261EBD1A390FED2BF326F2673C145582A6342D523204973D0219337F81616A8069B012587CF5635F6925F1B56C360230C19B273500EE013E030601BF2425")]
        public async Task TestHash(string input, string hashFunction, string expected)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            Dictionary<string, object> parameters = new Dictionary<string, object> { ["Input"] = input, ["HashFunction"] = hashFunction };
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "FnTestHash", parameters);

            string hash = result.Value.ToString();
            Assert.AreEqual(expected, hash);
        }

        protected override void OnErrorLogged(string message)
        {
            if (!message.Contains("ProcedureTestError"))
            {
                base.OnErrorLogged(message);
            }
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("Execution.Procedures.SystemCallSchema", null);
        }
    }
}
