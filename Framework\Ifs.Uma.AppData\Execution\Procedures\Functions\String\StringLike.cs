﻿using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringLike : StringFunction
    {
        public const string FunctionName = "Like";

        public StringLike()
            : base(FunctionName, 2, false)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            object pattern = parameters[1].GetValue();

            if (pattern is string stringPattern)
            {
                Regex regex = LikeToRegex(stringPattern, RegexOptions.IgnoreCase);
                return regex.IsMatch(stringToModify);
            }
            else
            {
                return false;
            }
        }
    }
}
