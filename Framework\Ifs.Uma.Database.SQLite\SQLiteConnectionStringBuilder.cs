﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database.SQLite
{
    internal enum CacheMode
    {
        Shared,
        Private
    }

    internal class SQLiteConnectionStringBuilder : DbConnectionStringBuilder
    {
        internal SQLiteConnectionStringBuilder()
        {
            m_cache = DefaultCache;
            m_busyTimeout = DefaultBusyTimeout;
            m_connectTimeout = DefaultConnectTimeout;
            m_maxPoolSize = DefaultMaxPoolSize;
            m_transactionTimeout = DefaultTransactionTimeout;
        }

        public override bool RequiresPassword()
        {
            return false;  // password is optional
        }

        public string DataSource
        {
            get { return m_dataSource; }
            set
            {
                if (value != null && System.IO.Path.IsPathRooted(value)) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(value, m_dataSource, StringComparison.Ordinal))
                {
                    m_dataSource = value;
                    SetValue(DataSourceKey, value);
                }
            }
        }

        public CacheMode Cache
        {
            get { return m_cache; }
            set
            {
                if (m_cache != value)
                {
                    m_cache = value;
                    SetValue(CacheKey, NormaliseEnum(value, DefaultCache));
                }
            }
        }

        public int ConnectTimeout
        {
            get { return m_connectTimeout; }
            set
            {
                if (m_connectTimeout != value)
                {
                    m_connectTimeout = value;
                    SetValue(ConnectTimeoutKey, NormaliseInt(value, DefaultConnectTimeout));
                }
            }
        }

        public double BusyTimeout
        {
            get { return m_busyTimeout; }
            set
            {
                if (m_busyTimeout != value)
                {
                    m_busyTimeout = value;
                    SetValue(BusyTimeoutKey, NormaliseDouble(value, DefaultBusyTimeout));
                }
            }
        }

        public int MaxPoolSize
        {
            get { return m_maxPoolSize; }
            set
            {
                if (m_maxPoolSize != value)
                {
                    m_maxPoolSize = value;
                    SetValue(MaxPoolSizeKey, NormaliseInt(value, DefaultMaxPoolSize));
                }
            }
        }

        public int TransactionTimeout
        {
            get { return m_transactionTimeout; }
            set
            {
                if (m_transactionTimeout != value)
                {
                    m_transactionTimeout = value;
                    SetValue(TransactionTimeoutKey, NormaliseInt(value, DefaultTransactionTimeout));
                }
            }
        }

        private string m_dataSource;
        private CacheMode m_cache;
        private int m_connectTimeout;
        private double m_busyTimeout;
        private int m_maxPoolSize;
        private int m_transactionTimeout;

        private const CacheMode DefaultCache = CacheMode.Shared;
        private const int DefaultConnectTimeout = 15;
        private const int MinConnectTimeout = 1;
        private const int MaxConnectTimeout = 300;
        private const double DefaultBusyTimeout = 0.1;
        private const double MinBusyTimeout = 0.01;
        private const double MaxBusyTimeout = 30;
        private const int DefaultMaxPoolSize = 10;
        private const int MinMaxPoolSize = 1;
        private const int MaxMaxPoolSize = 100;
        private const int DefaultTransactionTimeout = 5;
        private const int MinTransactionTimeout = 1;
        private const int MaxTransactionTimeout = 300;

        private const string DataSourceKey = "Data Source";
        private const string ConnectTimeoutKey = "Connect Timeout";
        private const string CacheKey = "Cache";
        private const string BusyTimeoutKey = "Busy Timeout";
        private const string MaxPoolSizeKey = "Max Pool Size";
        private const string TransactionTimeoutKey = "Transaction Timeout";

        private static readonly IEnumerable<string> SQLiteKeys = new string[]
        {
            DataSourceKey,
            ConnectTimeoutKey,
            CacheKey,
            BusyTimeoutKey,
            MaxPoolSizeKey,
            TransactionTimeoutKey
        };

        protected override IEnumerable<string> AllNormalisedKeys
        {
            get
            {
                return SQLiteKeys.Concat(base.AllNormalisedKeys);
            }
        }

        protected override string NormaliseKey(string key)
        {
            foreach (string knownKey in SQLiteKeys)
            {
                if (string.Equals(key, knownKey, StringComparison.OrdinalIgnoreCase))
                {
                    return knownKey;
                }
            }

            return base.NormaliseKey(key);
        }

        protected override string NormaliseValue(string key, string value)
        {
            if (string.Equals(key, DataSourceKey, StringComparison.OrdinalIgnoreCase))
            {
                return DataSourceHelper.Instance.Normalise(value);
            }
            if (string.Equals(key, CacheKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseEnum(ValidateEnum(value, DefaultCache), DefaultCache);
            }
            if (string.Equals(key, ConnectTimeoutKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseInt(
                    ValidateInt(value, MinConnectTimeout, MaxConnectTimeout, DefaultConnectTimeout),
                    DefaultConnectTimeout);
            }
            if (string.Equals(key, BusyTimeoutKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseDouble(
                    ValidateDouble(value, MinBusyTimeout, MaxBusyTimeout, DefaultBusyTimeout),
                    DefaultBusyTimeout);
            }
            if (string.Equals(key, MaxPoolSizeKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseInt(
                    ValidateInt(value, MinMaxPoolSize, MaxMaxPoolSize, DefaultMaxPoolSize),
                    DefaultMaxPoolSize);
            }
            if (string.Equals(key, TransactionTimeoutKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseInt(
                    ValidateInt(value, MinTransactionTimeout, MaxTransactionTimeout, DefaultTransactionTimeout),
                    DefaultTransactionTimeout);
            }

            return base.NormaliseValue(key, value);
        }

        protected override void SetMember(string key, string value)
        {
            if (string.Equals(key, DataSourceKey, StringComparison.OrdinalIgnoreCase))
            {
                DataSource = DataSourceHelper.Instance.Normalise(value);
            }
            else if (string.Equals(key, ConnectTimeoutKey, StringComparison.OrdinalIgnoreCase))
            {
                ConnectTimeout = ValidateInt(value, MinConnectTimeout, MaxConnectTimeout, DefaultConnectTimeout);
            }
            else if (string.Equals(key, CacheKey, StringComparison.OrdinalIgnoreCase))
            {
                Cache = ValidateEnum(value, DefaultCache);
            }
            else if (string.Equals(key, BusyTimeoutKey, StringComparison.OrdinalIgnoreCase))
            {
                BusyTimeout = ValidateDouble(value, MinBusyTimeout, MaxBusyTimeout, DefaultBusyTimeout);
            }
            else if (string.Equals(key, MaxPoolSizeKey, StringComparison.OrdinalIgnoreCase))
            {
                MaxPoolSize = ValidateInt(value, MinMaxPoolSize, MaxMaxPoolSize, DefaultMaxPoolSize);
            }
            else if (string.Equals(key, TransactionTimeoutKey, StringComparison.OrdinalIgnoreCase))
            {
                TransactionTimeout = ValidateInt(value, MinTransactionTimeout, MaxTransactionTimeout, DefaultTransactionTimeout);
            }
            else
            {
                base.SetMember(key, value);
            }
        }

        protected override string GetDefault(string key)
        {
            if (string.Equals(key, ConnectTimeoutKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseInt(DefaultConnectTimeout, null);
            }
            if (string.Equals(key, CacheKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseEnum(DefaultCache, null);
            }
            if (string.Equals(key, BusyTimeoutKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseDouble(DefaultBusyTimeout, null);
            }
            if (string.Equals(key, MaxPoolSizeKey, StringComparison.OrdinalIgnoreCase))
            {
                return NormaliseInt(DefaultMaxPoolSize, null);
            }

            return base.GetDefault(key);
        }
    }
}
