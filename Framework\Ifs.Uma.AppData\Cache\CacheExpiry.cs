﻿using System;
using System.Globalization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Cache
{
    internal sealed class CacheExpiry
    {
        private readonly InvalidationPeriod _period;
        private readonly TimeSpan _after;

        public static CacheExpiry FromEntity(IMetadata metadata, ILogger logger, string projectionName, string entityName)
        {
            CpiEntity entityFromAppMeta = null;
            metadata.CpiMetaData.App?.SyncEntities?.Entities?.TryGetValue(entityName, out entityFromAppMeta);

            CpiEntity entityFromProjection = metadata.FindEntity(projectionName, entityName);

            CpiCacheInvalidation invalidation = entityFromAppMeta?.SyncPolicy?.CacheInvalidation ?? entityFromProjection?.SyncPolicy?.CacheInvalidation;

            if (entityFromAppMeta == null && entityFromProjection?.SyncPolicy != null) // Check syncpolicy to verify if it's not a virtual or structure
            {
                Logger.Current.Warning("The entity/query {0} was not found in app meta. Cache invalidation defined in projection meta will be used.", entityName);
            }

            InvalidationPeriod period = invalidation?.Interval ?? InvalidationPeriod.Never;
            TimeSpan after = TimeSpan.Zero;

            if (invalidation?.Interval == InvalidationPeriod.After)
            {
                if (int.TryParse(invalidation.Time, NumberStyles.Integer, CultureInfo.InvariantCulture, out int amount))
                {
                    switch (invalidation.Period)
                    {
                        case "day":
                            after = TimeSpan.FromDays(amount);
                            break;
                        case "hour":
                            after = TimeSpan.FromHours(amount);
                            break;
                        case "min":
                            after = TimeSpan.FromMinutes(amount);
                            break;
                    }
                }

                if (after <= TimeSpan.Zero)
                {
                    logger.Warning($"Failed to load cache invalidation configuration for entity {projectionName}.{entityName}. Using 'Never'");
                    period = InvalidationPeriod.Never;
                }
            }

            return new CacheExpiry(period, after);
        }

        public CacheExpiry(InvalidationPeriod period, TimeSpan after)
        {
            _period = period;
            _after = after;
        }

        public bool HasExpired(DateTime lastUpdateTime)
        {
            switch (_period)
            {
                case InvalidationPeriod.Daily:
                    DateTime startOfDay = DateTime.Now.Date;
                    return lastUpdateTime < startOfDay;
                case InvalidationPeriod.Weekly:
                    DateTime startOfWeek = StartOfWeek();
                    return lastUpdateTime < startOfWeek;
                case InvalidationPeriod.After:
                    DateTime nextUpdate = SafeDateTimeAdd(lastUpdateTime, _after);
                    return nextUpdate < DateTime.Now;
                default:
                    return false;
            }
        }

        private static DateTime StartOfWeek()
        {
            int diff = DateTime.Now.DayOfWeek - DayOfWeek.Monday;
            if (diff < 0)
            {
                diff += 7;
            }
            return DateTime.Now.AddDays(-diff).Date;
        }

        private static DateTime SafeDateTimeAdd(DateTime dateTime, TimeSpan timeSpan)
        {
            if (DateTime.MaxValue.Subtract(timeSpan) < dateTime)
            {
                return DateTime.MaxValue;
            }

            return dateTime.Add(timeSpan);
        }
    }
}
