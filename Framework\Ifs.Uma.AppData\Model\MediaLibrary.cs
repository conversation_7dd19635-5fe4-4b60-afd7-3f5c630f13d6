﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_media_library", Columns = nameof(LibraryId) + "," + nameof(LuName) + "," + nameof(KeyRef), Unique = true)]
    [ObjConnection(LuNameColumn = nameof(LuName), KeyRefColumn = nameof(KeyRef))]
    public class MediaLibrary : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "media_library";

        public MediaLibrary()
            : base(DbTableName)
        {
            EntitySetName = "MediaLibraries";
        }

        private string _libraryId;
        [Column(Storage = nameof(_libraryId), Mandatory = true, ServerPrimaryKey = true)]
        public string LibraryId
        {
            get => _libraryId;
            set => SetProperty(ref _libraryId, value);
        }

        private string _luName;
        [Column(Storage = nameof(_luName), Mandatory = true, ServerPrimaryKey = true)]
        public string LuName
        {
            get => _luName;
            set => SetProperty(ref _luName, value);
        }

        private string _keyRef;
        [Column(Storage = nameof(_keyRef), Mandatory = true, ServerPrimaryKey = true)]
        public string KeyRef
        {
            get => _keyRef;
            set => SetProperty(ref _keyRef, value);
        }

        private string _f1KeyRef;
        [Column(Storage = nameof(_f1KeyRef))]
        public string F1KeyRef
        {
            get => _f1KeyRef;
            set => SetProperty(ref _f1KeyRef, value);
        }

        private bool? _mainLibrary;
        [Column(Storage = nameof(_mainLibrary))]
        public bool? MainLibrary
        {
            get => _mainLibrary;
            set => SetProperty(ref _mainLibrary, value);
        }
    }
}
