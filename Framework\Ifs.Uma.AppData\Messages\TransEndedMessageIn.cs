﻿using System.Linq;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Messages
{
    internal sealed class TransEndedMessageIn : MessageIn
    {
        public TransEndedMessageIn()
            : base(MessageType.TRANENDED)
        {
        }

        public override void Execute(FwDataContext ctx, IClientKeysMapper clientKeysMapper, ILogger logger, DataChangeSet dataChangeSet, bool isInitializing)
        {
            int totalCounter = 0, totalMessages = ctx.MessageIn.Count(x => x.TransactionId == TransactionId);

            while (true)
            {
                // Some rows could contain a lot of data, loading all of that could take up a lot of memory
                // so process one row at a time.
                MessageInRow row = ctx.MessageIn.OrderBy(x => x.RowId).FirstOrDefault(x => x.TransactionId == TransactionId);

                if (row == null)
                {
                    break;
                }

                totalCounter++;

                Logger.Current.Trace("Processing saved messages ({0}/{1})", totalCounter.ToString(), totalMessages.ToString());
                ProcessMessage(ctx, clientKeysMapper, logger, dataChangeSet, row, isInitializing);
            }

            base.Execute(ctx, clientKeysMapper, logger, dataChangeSet, isInitializing);
        }

        private static void ProcessMessage(FwDataContext ctx, IClientKeysMapper clientKeysMapper, ILogger logger, DataChangeSet dataChangeSet, MessageInRow row, bool isInitializing)
        {
            // Process each message in its own transaction so we do not lock the database

            InitializeStatus status = ctx.DatabaseInfos.Select(x => x.InitializeStatus).FirstOrDefault();
            bool initializing = status < InitializeStatus.Initialized;

            MessageIn msg = Create(row.MessageType);
            msg.ServerMessageId = row.ServerMessageId;
            msg.ClientRelatedMessageId = row.ClientRelatedMessageId;
            msg.MessageData = row.Message;
            msg.TransactionId = row.TransactionId;

            bool isDataChangeMessage = msg is DataChangeMessageIn;

            if (initializing || !isDataChangeMessage)
            {
                ctx.ExecuteInTransaction((command) =>
                {
                    IDbRowHandler<MessageInRow> deleteHandler = ctx.CreateDbRowHandler<MessageInRow>(command);
                    deleteHandler.DeleteRow(row);
                    msg.Execute(ctx, command, clientKeysMapper, logger, dataChangeSet, isInitializing);
                });
            }
            else
            {
                ctx.ExecuteInTransaction((command) =>
                {
                    IDbRowHandler<MessageInRow> deleteHandler = ctx.CreateDbRowHandler<MessageInRow>(command);
                    deleteHandler.DeleteRow(row);
                });

                DataChangeMessageIn dcmi = msg as DataChangeMessageIn;
                dcmi.ExecuteInOwnTransaction(ctx, clientKeysMapper, logger, dataChangeSet, isInitializing);
            }
        }

        protected override void OnExecute(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper, 
            ILogger logger, DataChangeSet dataChangeSet, TransitionRow transitionRow, bool isInitializing)
        {
            // Nothing to do - all messages sent
        }
    }
}
