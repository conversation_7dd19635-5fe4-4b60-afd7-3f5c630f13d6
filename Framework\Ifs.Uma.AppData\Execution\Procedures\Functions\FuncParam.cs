﻿using System;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions
{
    internal struct FuncParam
    {
        private readonly ProcedureContext _context;
        public object RawValue { get; }

        public FuncParam(ProcedureContext context, object param)
        {
            _context = context;
            RawValue = param;
        }

        public object GetValue()
        {
            return _context.ReadParamValue(RawValue);
        }

        public bool? GetBoolean()
        {
            object value = GetValue();
            if (RawValue == null)
            {
                return false;
            }

            if (MetadataExtensions.TryConvertToType(CpiDataType.Boolean, value, out object result))
            {
                return (bool)result;
            }
            else
            {
                return null;
            }
        }

        public string GetString()
        {
            if (RawValue == null)
            {
                return null;
            }

            if (RawValue is string str)
            {
                return _context.InterpolateString(str, false);
            }
            else
            {
                return ObjectConverter.ToString(RawValue);
            }
        }

        public long? GetInteger()
        {
            object value = GetValue();
            if (RawValue == null)
            {
                return null;
            }

            if (MetadataExtensions.TryConvertToType(CpiDataType.Integer, value, out object result))
            {
                return (long)result;
            }
            else
            {
                // String could be a double in disguise. 
                // Try get the double value, and then convert it to an Integer to truncate it.
                double? doubleValue = GetNumber();
                if (doubleValue.HasValue && MetadataExtensions.TryConvertToType(CpiDataType.Integer, doubleValue, out object integerResult))
                {
                    return (long)integerResult;
                }
            }

            return null;
        }

        public double? GetNumber()
        {
            object value = GetValue();
            if (RawValue == null)
            {
                return null;
            }

            if (MetadataExtensions.TryConvertToType(CpiDataType.Number, value, out object result))
            {
                return (double)result;
            }

            return null;
        }

        public byte[] GetByteArray()
        {
            if (RawValue == null)
            {
                return new byte[0];
            }

            object value = GetValue();

            if (value != null)
            {
                return ObjectConverter.ToByteArray(value);
            }
            else
            {
                return new byte[0];
            }
        }

        public DateTime? GetTimestamp()
        {
            object value = GetValue();
            if (RawValue == null)
            {
                return null;
            }

            if (MetadataExtensions.TryConvertToType(CpiDataType.Timestamp, value, out object result))
            {
                return (DateTime)result;
            }

            return null;
        }

        public string GetVariableName()
        {
            if (RawValue is string str && str.StartsWith("${") && str.EndsWith("}"))
            {
                return str.Substring(2, str.Length - 3);
            }

            return null;
        }
    }
}
