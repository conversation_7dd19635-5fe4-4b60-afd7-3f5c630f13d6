﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_" + DbTableName, 
           Columns = nameof(ProfileSection) + ", " + nameof(ProfileEntry) + ", " + nameof(Owner),
           Unique = true)]
    public class RoamingProfileValue : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "client_profile_value";
        public const string ProjectionName = "MobileClientRuntime";

        [Column(MaxLength = 1000, ServerPrimaryKey = true)]
        public string ProfileSection { get; set; }

        [Column(MaxLength = 200, ServerPrimaryKey = true)]
        public string ProfileEntry { get; set; }

        [Column(ServerPrimaryKey = true)]
        public bool Owner { get; set; }

        private string _profileValue;
        [Column(Storage = nameof(_profileValue), MaxLength = 4000)]
        public string ProfileValue
        {
            get { return _profileValue; }
            set { SetProperty(ref _profileValue, value); }
        }

        private byte[] _profileBinaryValue;
        [SuppressMessage("Microsoft.Performance", "CA1819:PropertiesShouldNotReturnArrays", 
            Justification="Property is used for database schema")]
        [Column(Storage = nameof(_profileBinaryValue), MaxLength = 4000)]
        public byte[] ProfileBinaryValue
        {
            get { return _profileBinaryValue; }
            set { SetProperty(ref _profileBinaryValue, value); } 
        }

        private string _binaryValueType;
        [Column(Storage = nameof(_binaryValueType), MaxLength = 100)]
        public string BinaryValueType
        {
            get { return _binaryValueType; }
            set { SetProperty(ref _binaryValueType, value); }
        }

        public RoamingProfileValue()
            : base(DbTableName)
        {
            EntitySetName = "SaveClientProfileValue";
        }

        public static CpiAction GetRoamingProfileAction(Dictionary<string, object> parameters)
        {
            CpiAction action = new CpiAction();
            action.SyncPolicy = new CpiProcSyncPolicy() { Type = ProcSyncPolicy.Offline };
            CpiParam[] actionParams = new CpiParam[parameters.Count];

            int i = 0;
            foreach (KeyValuePair<string, object> kvp in parameters)
            {
                CpiParam cpiParam = new CpiParam() { Name = kvp.Key };

                if (kvp.Key.Equals("ProfileValue"))
                {
                    cpiParam.DataType = CpiDataType.LongText;
                }

                actionParams[i] = cpiParam;
                i++;
            }

            action.Parameters = actionParams;
            return action;
        }
    }
}
