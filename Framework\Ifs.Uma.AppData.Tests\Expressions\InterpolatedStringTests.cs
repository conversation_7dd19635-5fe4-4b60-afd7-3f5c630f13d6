﻿using System;
using System.Collections.Generic;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.AppData.StringExpressions;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Expressions
{
    [TestFixture]
    public class InterpolatedStringTests
    {
        [Test]
        public void String()
        {
            TestValues values = new TestValues();

            InterpolatedString sxp = new InterpolatedString(null, "Hello");
            string result = sxp.GetString(values, true);
            
            Assert.AreEqual("Hello", result);
        }

        [Test]
        public void Attribute()
        {
            TestValues values = new TestValues();
            values["MyVar"] = 1;
            values["MyVar2"] = 2.5;

            InterpolatedString sxp = new InterpolatedString(null, "A ${MyVar} B ${MyVar2} C");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("A 1 B 2.5 C", result);
        }

        [Test]
        public void Expression()
        {
            TestValues values = new TestValues();
            values["MyVar"] = 1;
            values["MyVar2"] = 2.5;

            InterpolatedString sxp = new InterpolatedString(null, "A #{MyVar + MyVar2} B #{MyVar2} C");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("A 3.5 B 2.5 C", result);
        }

        [Test]
        [SetCulture("sv-SE")]
        public void ExpressionLocalized()
        {
            TestValues values = new TestValues();
            values["MyVar"] = 1;
            values["MyVar2"] = 2.5;

            InterpolatedString sxp = new InterpolatedString(null, "A #{MyVar + MyVar2} B #{MyVar2} C");

            string result = sxp.GetString(values, true);
            Assert.AreEqual("A 3,5 B 2,5 C", result);

            result = sxp.GetString(values, false);
            Assert.AreEqual("A 3.5 B 2.5 C", result);
        }

        [Test]
        public void Ternary()
        {
            TestValues values = new TestValues();
            values["VarA"] = 1;
            values["VarB"] = 2.5;

            InterpolatedString sxp = new InterpolatedString(null, "#{VarA > VarB ? 2 : 3}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("3", result);
        }

        [Test]
        public void Mixed()
        {
            TestValues values = new TestValues();
            values["MyVar"] = 1;
            values["MyVar2"] = 2.5;
            values["MyVar3"] = "Hello";

            InterpolatedString sxp = new InterpolatedString(null, "A #{MyVar + MyVar2} B ${MyVar3} C");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("A 3.5 B Hello C", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void DateTimeFormat()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);
            values["MyDate"] = new DateTime(2001, 2, 3, 0, 0, 0);
            values["MyTime"] = new DateTime(1, 1, 1, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "${MyTimestamp} # ${MyDate} # ${MyTime}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/3/2001 5:06 AM # 2/3/2001 # 5:06 AM", result);
        }

        [Test]
        [SetCulture("sv-SE")]
        public void SwedishDateTimeFormat()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);
            values["MyDate"] = new DateTime(2001, 2, 3, 0, 0, 0);
            values["MyTime"] = new DateTime(1, 1, 1, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "${MyTimestamp} # ${MyDate} # ${MyTime}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2001-02-03 05:06 # 2001-02-03 # 05:06", result);
        }
        
        [Test]
        [SetCulture("en-US")]
        public void UnknownMethod()
        {
            TestValues values = new TestValues();
            values["MyVar"] = 1;

            InterpolatedString sxp = new InterpolatedString(null, "Woo #{unknownMethod(MyVar, 2)} Ahhh");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("Woo ##INVALID## Ahhh", result);
        }
        
        [Test]
        [SetCulture("en-US")]
        public void AddYears()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "In two years = '#{addYears(MyTimestamp, 2)}'");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("In two years = '2/3/2003 5:06 AM'", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void AddMonths()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{addMonths(MyTimestamp, 2)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("4/3/2001 5:06 AM", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void AddDays()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{addDays(MyTimestamp, 2.5)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/5/2001 5:06 PM", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void AddHours()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{addHours(MyTimestamp, 2.5)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/3/2001 7:36 AM", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void AddMinutes()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{addMinutes(MyTimestamp, 2)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/3/2001 5:08 AM", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void AddSeconds()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{addSeconds(MyTimestamp, 65)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/3/2001 5:07 AM", result);
        }
        
        [Test]
        [SetCulture("en-US")]
        public void Date()
        {
            TestValues values = new TestValues();

            InterpolatedString sxp = new InterpolatedString(null, "#{date(2001, 2, 3)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/3/2001", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void Timestamp()
        {
            TestValues values = new TestValues();

            InterpolatedString sxp = new InterpolatedString(null, "#{timestamp(2001, 2, 3, 5, 6, 7)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/3/2001 5:06 AM", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void Time()
        {
            TestValues values = new TestValues();

            InterpolatedString sxp = new InterpolatedString(null, "#{time(5, 6, 7)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("5:06 AM", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void ToDate()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{toDate(MyTimestamp)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/3/2001", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void ToTimestamp()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{toTimestamp(MyTimestamp)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/3/2001 5:06 AM", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void ToTime()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{toTime(MyTimestamp)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("5:06 AM", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void ToNumber()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{toNumber(2 + '0' + 5.555)}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("205.555", result);
        }

        [Test]
        [SetCulture("en-US")]
        public void MultiCall()
        {
            TestValues values = new TestValues();
            values["MyTimestamp"] = new DateTime(2001, 2, 3, 5, 6, 7);

            InterpolatedString sxp = new InterpolatedString(null, "#{toDate(addDays(MyTimestamp, 3))}");
            string result = sxp.GetString(values, true);

            Assert.AreEqual("2/6/2001", result);
        }

        private class TestValues : Dictionary<string, object>, IStringExpressionValueProvider
        {
            public bool TryGetFormattedValue(string propertyName, out string value)
            {
                if (TryGetValue(propertyName, out object val))
                {
                    value = AttributeFormatter.FormatValue(val);
                    return true;
                }

                value = null;
                return false;
            }
        }
    }
}
