﻿using Ifs.Uma.Data;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Metadata;
using System;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = TableName, System = true)]
    public class DatabaseInfo
    {
        public const string TableName = FwDataContext.FwTablePrefix + "database_info";

        [Column(MaxLength = 30, PrimaryKey=true)]
        public string ActivatedUser { get; set; }

        [Column(MaxLength = 64, Mandatory=true)]
        public string PasswordHash { get; set; }

        [Column]
        public DateTime Creation { get; set; }

        [Column(Mandatory = true)]
        public int Version { get; set; }

        [Column(MaxLength = 30)]
        public string AppVersion { get; set; }

        [Column]
        public InitializeStatus InitializeStatus { get; set; }

        [Column]
        public byte[] MetadataBlob { get; set; }

        [Column]
        public byte[] NavigatorEntriesBlob { get; set; }
    }
}
