﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringTrimStart : StringFunction
    {
        public const string FunctionName = "TrimStart";

        public StringTrimStart() 
            : base(FunctionName, 1, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify) => stringToModify.TrimStart();
    }

    internal sealed class StringTrimStart2 : StringFunction
    {
        public const string FunctionName = "TrimStart";

        public StringTrimStart2()
            : base(FunctionName, 2, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string trimString = parameters[1].GetString();

            if (string.IsNullOrEmpty(trimString))
                return null;

            return stringToModify.TrimStart(trimString[0]);
        }
    }
}
