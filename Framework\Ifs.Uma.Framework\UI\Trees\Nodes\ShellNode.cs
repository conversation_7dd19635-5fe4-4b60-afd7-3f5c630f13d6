﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.UI.Trees.Nodes
{
    public class ShellNode : Node
    {
        private readonly IList<EntityRecord> _descendants;

        public ShellNode(Node parent, NodeContext context, EntityRecord record, CpiTreeNode cpiTreeNode, IList<EntityRecord> descendants)
            : base(parent, context, record, cpiTreeNode, true)
        {
            _descendants = descendants;
        }

        protected override Task<IEnumerable<Node>> GetChildNodes()
        {
            if (_descendants.Count == 0)
            {
                return Task.FromResult<IEnumerable<Node>>(new List<Node>());
            }

            EntityRecord record = _descendants[0];
            _descendants.RemoveAt(0);

            if (_descendants.Count > 0)
            {
                ShellNode shellChild = new ShellNode(this, Context, record, CpiTreeNode, _descendants);
                return Task.FromResult<IEnumerable<Node>>(new List<Node> { shellChild });
            }
            Node child = new Node(this, Context, record, CpiTreeNode, true);
            return Task.FromResult<IEnumerable<Node>>(new List<Node> { child });
        }

        public Node ConvertToNode()
        {
            return new Node(Parent, Context, Record, CpiTreeNode);
        }
    }
}
