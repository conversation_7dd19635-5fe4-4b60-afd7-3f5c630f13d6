﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.Framework.UI.Lists;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Localization;
using Ifs.Uma.Services.Attachments.Media;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public sealed class MediaListData : ListData<MediaListItem>, INavigatedTo
    {
        private readonly IMediaHandler _mediaHandler;
        private readonly INavigator _navigator;

        private string _title;
        public string Title
        {
            get => _title;
            private set => SetProperty(ref _title, value);
        }

        private bool _canAccessMedia;
        public bool CanAccessMedia
        {
            get => _canAccessMedia;
            private set => SetProperty(ref _canAccessMedia, value);
        }

        public Command AddNew { get; }

        private AttachmentNavParam _navParam;

        public MediaListData(IEventAggregator eventAggregator, IMediaHandler mediaHandler, INavigator navigator)
            : base(eventAggregator)
        {
            _mediaHandler = mediaHandler;
            _navigator = navigator;

            Title = Strings.Media;
            AddNew = Command.FromMethod(OnAddNew);
            AddNew.IsEnabled = false;
        }

        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                EventAggregator.GetEvent<MediaStatusChangedEvent>().Subscribe(OnMediaStatusChanged, ThreadOption.UIThread);
            }
            else
            {
                EventAggregator.GetEvent<MediaStatusChangedEvent>().Unsubscribe(OnMediaStatusChanged);
            }
        }
        
        public async void OnNavigatedTo(NavigatedToArgs args)
        {
            AttachmentNavParam navParam = NavigationParameter.FromNavigationParameter<AttachmentNavParam>(args.Parameter);
            await Load(navParam);
        }

        public async Task Load(AttachmentNavParam navParam)
        {
            _navParam = navParam;

            CanAccessMedia = navParam != null && await _mediaHandler.IsEnabledFor(_navParam.EntityName);
            AddNew.IsEnabled = navParam != null && await _mediaHandler.CanCreateNew(_navParam.EntityName, _navParam.KeyRef);

            await UpdateAsync();
        }

        protected override async Task OnUpdateAsync()
        {
            using (Items.DeferRefresh())
            {
                Items.Clear();

                if (_navParam != null && CanAccessMedia)
                {
                    IEnumerable<MediaInfo> rows = await _mediaHandler.GetMediaAsync(_navParam.EntityName, _navParam.KeyRef);

                    var tasks = rows.Select(async row =>
                    {
                        MediaListItem item = new MediaListItem(_mediaHandler, row.Library, row.MediaItem);

                        if (item.Status == AppData.Model.AttachmentStatus.RequiresDownload)
                        {
                            try
                            {
                                ILocalFileInfo fileInfo = await item.GetFileInfoAsync();
                                if (fileInfo != null && await fileInfo.ExistsAsync())
                                {
                                    item.Status = AppData.Model.AttachmentStatus.Downloaded;
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log and handle errors appropriately
                                Logger.Current.Error("Error processing media item: {0}", ex.Message);
                            }
                        }
                        return item;
                    });

                    var items = await Task.WhenAll(tasks);
                    foreach (var item in items)
                    {
                        Items.Add(item);
                    }

                    Title = string.Format(Strings.MediaCount, Items.Count);
                }
            }
        }

        protected override bool OnMatchSearchTerm(MediaListItem item)
        {
            if (item.Title != null &&
                item.Title.IndexOf(SearchTerm, 0, StringComparison.CurrentCultureIgnoreCase) >= 0)
            {
                return true;
            }

            if (item.Description != null &&
                item.Description.IndexOf(SearchTerm, 0, StringComparison.CurrentCultureIgnoreCase) >= 0)
            {
                return true;
            }

            if (item.StatusLabel != null &&
                item.StatusLabel.IndexOf(SearchTerm, 0, StringComparison.CurrentCultureIgnoreCase) >= 0)
            {
                return true;
            }

            return false;
        }

        private void OnAddNew()
        {
            if (_navParam != null && CanAccessMedia && AddNew.IsEnabled)
            {
                MediaNavParam navParam = new MediaNavParam(_navParam.EntityName, _navParam.KeyRef, null);
                _navigator.NavigateToAsync(FrameworkLocations.MediaDetails, navParam);
            }
        }

        protected override void OnSelectedItemChanged()
        {
            base.OnSelectedItemChanged();

            if (SelectedItem != null && CanAccessMedia)
            {
                MediaNavParam navParam = new MediaNavParam(SelectedItem.Library.LuName, SelectedItem.Library.KeyRef, SelectedItem.MediaItem.RowId);
                _navigator.NavigateToAsync(FrameworkLocations.MediaDetails, navParam);
            }
        }

        private void OnMediaStatusChanged(MediaStatusChangedEventArgs args)
        {
            foreach (MediaListItem item in Items)
            {
                if (item.MediaItem.RowId == args.MediaItemRowId)
                {
                    item.MediaItem.AttachmentStatus = args.Status;
                    item.Status = args.Status;
                }
            }
        }
    }
}
