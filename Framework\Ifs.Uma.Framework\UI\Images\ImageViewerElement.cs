﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Images
{
    public class ImageViewerElement : ElementBase
    {
        private readonly IMetadata _metadata;
        private readonly IMediaHandler _mediaHandler;
        private readonly IDocumentHandler _documentHandler;
        private readonly IFileService _fileService;

        private CpiImageViewer _cpiImageViewer;
        private ObjPrimaryKey _objPrimaryKey;

        public ViewableCollection<ImageViewerItem> Items { get; } = new ViewableCollection<ImageViewerItem>();

        protected override BindingType BindingPropertyType => BindingType.Reference;

        public CpiScaleMode Scale { get; private set; }

        public CpiImageViewerHeight Height { get; private set; }

        public int CurrentPosition { get; set; } = 0;

        public ILogger Logger { get; }

        private bool _hasItems = false;
        public bool HasItems
        {
            get => _hasItems;
            set => SetProperty(ref _hasItems, value);
        }

        public ImageViewerElement(IMetadata metadata, IMediaHandler mediaHandler, IDocumentHandler documentHandler, ILogger logger, IFileService fileService)
        {
            _metadata = metadata;
            _mediaHandler = mediaHandler;
            _documentHandler = documentHandler;
            Logger = logger;
            _fileService = fileService;
        }

        protected override bool OnInitialize()
        {
            _cpiImageViewer = _metadata.FindImageViewer(ProjectionName, Content.ImageViewer);

            if (_cpiImageViewer == null)
            {
                return false;
            }

            Label = _cpiImageViewer.Label;
            Scale = _cpiImageViewer.ScalingMode;
            Height = _cpiImageViewer.Height;

            return base.OnInitialize();
        }

        public async Task OpenItemAsync(ImageViewerItem imageViewerItem)
        {
            await _fileService.LaunchFileAsync(imageViewerItem.PickedFile);
        }

        protected override bool OnLoad()
        {
            UpdateItems();
            return true;
        }

        protected override void OnReloadData()
        {
            base.OnReloadData();
            _objPrimaryKey = null;
            UpdateItems();
        }

        protected override void OnStoredDataChanged(DataChangeSet changeSet)
        {
            base.OnStoredDataChanged(changeSet);

            if (changeSet.HasChanges<MediaLibrary>() ||
                changeSet.HasChanges<MediaLibraryItem>() ||
                changeSet.HasChanges<DocReferenceObject>() ||
                changeSet.HasChanges<MobileDocClass>() ||
                changeSet.HasChanges<EdmFile>())
            {
                _objPrimaryKey = null;
                UpdateItems();
            }
        }

        protected override void OnDataChanged()
        {
            base.OnDataChanged();
            UpdateItems();
        }

        private void UpdateItems()
        {
            if (HasKeyChanged())
            {
                bool documentsEnabled = RunCheck(_cpiImageViewer.Documents?.OfflineEnabled ?? _cpiImageViewer.Documents?.Enabled, false);
                bool mediaEnabled = RunCheck(_cpiImageViewer.Media?.OfflineEnabled ?? _cpiImageViewer.Media?.Enabled, false);
                HasItems = false;
                Items.Clear();
                if (documentsEnabled)
                {
                    ExecuteBackgroundTask(LoadDocumentItemsAsync());
                }

                if (mediaEnabled)
                {
                    ExecuteBackgroundTask(LoadMediaItemsAsync());
                }
            }
        }

        private async Task LoadMediaItemsAsync()
        {
            if (_objPrimaryKey != null)
            {
                try
                {
                    IEnumerable<MediaInfo> mediaInfos = await _mediaHandler.GetMediaAsync(ViewData?.Record?.EntityName, _objPrimaryKey.ToKeyRef());
                    foreach (MediaInfo mediaInfo in mediaInfos)
                    {
                        try
                        {
                            ILocalFileInfo file = await _mediaHandler.GetLocalFileForMediaAsync(mediaInfo.MediaItem);
                            bool fileExists = await file.ExistsAsync();
                            if (fileExists)
                            {
                                PickedFile pickedFile = new PickedFile
                                {
                                    Data = file,
                                    FileName = mediaInfo.MediaItem.Name + System.IO.Path.GetExtension(file.FilePath).ToLower()
                                };

                                Items.Add(new MediaItem(pickedFile));
                                HasItems = true;
                            }
                        }
                        catch (Exception innerException)
                        {
                            Logger.HandleException(ExceptionType.Recoverable, innerException);
                        }
                    }
                }
                catch (Exception outerException)
                {
                    Logger.HandleException(ExceptionType.Recoverable, outerException);
                }
            }
        }

        private async Task LoadDocumentItemsAsync()
        {
            if (_objPrimaryKey != null)
            {
                try
                {
                    IEnumerable<DocRevisionInfo> documents = await _documentHandler.GetDocumentInfosAsync(ViewData?.Record?.EntityName, _objPrimaryKey.ToKeyRef());
                    foreach (DocRevisionInfo documentInfo in documents)
                    {
                        try
                        {
                            ILocalFileInfo file = await _documentHandler.GetLocalFileForDocumentAsync(documentInfo.EdmFile);
                            bool fileExists = await file.ExistsAsync();
                            if (fileExists)
                            {
                                PickedFile pickedFile = new PickedFile
                                {
                                    Data = file,
                                    FileName = documentInfo.DocumentRevision.Title + System.IO.Path.GetExtension(file.FilePath).ToLower()
                                };

                                Items.Add(new DocumentItem(pickedFile));
                                HasItems = true;
                            }
                        }
                        catch (Exception innerException)
                        {
                            Logger.HandleException(ExceptionType.Recoverable, innerException);
                        }
                    }
                }
                catch (Exception outerException)
                {
                    Logger.HandleException(ExceptionType.Recoverable, outerException);
                }
            }
        }

        private bool HasKeyChanged()
        {
            ObjPrimaryKey newObjPrimaryKey = ViewData?.Record?.ToPrimaryKey();

            if (!Equals(_objPrimaryKey, newObjPrimaryKey))
            {
                _objPrimaryKey = newObjPrimaryKey;
                return true;
            }

            return false;
        }
    }
}
