﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// An extended expression visitor including custom DbExpression nodes
    /// </summary>
    internal abstract class DbExpressionVisitor : ExpressionVisitorEx
    {
        protected override Expression VisitExtension(Expression node)
        {
            if (node == null) return null;
            switch (node.GetDbNodeType())
            {
                case DbExpressionType.Table:
                    return VisitTable((TableExpression)node);
                case DbExpressionType.Column:
                    return VisitColumn((ColumnExpression)node);
                case DbExpressionType.Select:
                    return VisitSelect((SelectExpression)node);
                case DbExpressionType.Join:
                    return VisitJoin((JoinExpression)node);
                case DbExpressionType.OuterJoined:
                    return VisitOuterJoined((OuterJoinedExpression)node);
                case DbExpressionType.Aggregate:
                    return VisitAggregate((AggregateExpression)node);
                case DbExpressionType.Scalar:
                case DbExpressionType.Exists:
                case DbExpressionType.In:
                    return VisitSubquery((SubqueryExpression)node);
                case DbExpressionType.AggregateSubquery:
                    return VisitAggregateSubquery((AggregateSubqueryExpression)node);
                case DbExpressionType.IsNull:
                    return VisitIsNull((IsNullExpression)node);
                case DbExpressionType.Between:
                    return VisitBetween((BetweenExpression)node);
                case DbExpressionType.RowCount:
                    return VisitRowNumber((RowNumberExpression)node);
                case DbExpressionType.Projection:
                    return VisitProjection((ProjectionExpression)node);
                case DbExpressionType.NamedValue:
                    return VisitNamedValue((NamedValueExpression)node);
                case DbExpressionType.ClientJoin:
                    return VisitClientJoin((ClientJoinExpression)node);
                case DbExpressionType.If:
                case DbExpressionType.Block:
                case DbExpressionType.Declaration:
                    return VisitCommand((CommandExpression)node);
                case DbExpressionType.Batch:
                    return VisitBatch((BatchExpression)node);
                case DbExpressionType.Variable:
                    return VisitVariable((VariableExpression)node);
                case DbExpressionType.Function:
                    return VisitFunction((FunctionExpression)node);
                case DbExpressionType.Entity:
                    return VisitEntity((EntityExpression)node);
            }
            return base.VisitExtension(node);
        }

        protected virtual Expression VisitEntity(EntityExpression node)
        {
            if (node == null) return null;
            var exp = Visit(node.Expression);
            return node.Update(exp);
        }

        protected virtual Expression VisitTable(TableExpression node)
        {
            return node;
        }

        protected virtual Expression VisitColumn(ColumnExpression node)
        {
            return node;
        }

        protected virtual Expression VisitSelect(SelectExpression node)
        {
            if (node == null) return null;
            var from = VisitSource(node.From);
            var where = Visit(node.Where);
            var orderBy = VisitOrderBy(node.OrderBy);
            var groupBy = VisitExpressionList(node.GroupBy);
            var skip = Visit(node.Skip);
            var take = Visit(node.Take);
            var columns = VisitColumnDeclarations(node.Columns);
            return node.Update(columns, from, where, orderBy, groupBy, skip, take);
        }

        protected virtual Expression VisitJoin(JoinExpression node)
        {
            if (node == null) return null;
            var left = VisitSource(node.Left);
            var right = VisitSource(node.Right);
            var condition = Visit(node.On);
            return node.Update(node.Join, left, right, condition);
        }

        protected virtual Expression VisitOuterJoined(OuterJoinedExpression node)
        {
            if (node == null) return null;
            var test = Visit(node.Test);
            var expression = Visit(node.Expression);
            return node.Update(test, expression);
        }

        protected virtual Expression VisitAggregate(AggregateExpression node)
        {
            if (node == null) return null;
            var arg = Visit(node.Argument);
            return node.Update(arg);
        }

        protected virtual Expression VisitIsNull(IsNullExpression node)
        {
            if (node == null) return null;
            var expr = Visit(node.Expression);
            return node.Update(expr);
        }

        protected virtual Expression VisitBetween(BetweenExpression node)
        {
            if (node == null) return null;
            var expr = Visit(node.Expression);
            var lower = Visit(node.Lower);
            var upper = Visit(node.Upper);
            return node.Update(expr, lower, upper);
        }

        protected virtual Expression VisitRowNumber(RowNumberExpression node)
        {
            if (node == null) return null;
            var orderby = VisitOrderBy(node.OrderBy);
            return node.Update(orderby);
        }

        protected virtual Expression VisitNamedValue(NamedValueExpression node)
        {
            return node;
        }

        protected virtual Expression VisitSubquery(SubqueryExpression node)
        {
            if (node == null) return null;
            switch (node.DbNodeType)
            {
                case DbExpressionType.Scalar:
                    return VisitScalar((ScalarExpression)node);
                case DbExpressionType.Exists:
                    return VisitExists((ExistsExpression)node);
                case DbExpressionType.In:
                    return VisitIn((InExpression)node);
            }
            return node;
        }

        protected virtual Expression VisitScalar(ScalarExpression node)
        {
            if (node == null) return null;
            var select = (SelectExpression)Visit(node.Select);
            return node.Update(select);
        }

        protected virtual Expression VisitExists(ExistsExpression node)
        {
            if (node == null) return null;
            var select = (SelectExpression)Visit(node.Select);
            return node.Update(select);
        }

        protected virtual Expression VisitIn(InExpression node)
        {
            if (node == null) return null;
            var expr = Visit(node.Expression);
            var select = VisitAndConvert(node.Select, "DbExpressionVisitor.VisitIn");
            var values = VisitExpressionList(node.Values);
            return node.Update(expr, select, values);
        }

        protected virtual Expression VisitAggregateSubquery(AggregateSubqueryExpression node)
        {
            if (node == null) return null;
            var subquery = VisitAndConvert(node.AggregateAsSubquery, "DbExpressionVisitor.VisitAggregateSubquery");
            //JVB: is it deliberate that we do not visit AggregateInGroupSelect?
            return node.Update(node.AggregateInGroupSelect, subquery);
        }

        protected virtual Expression VisitSource(Expression node)
        {
            return Visit(node);
        }

        protected virtual Expression VisitProjection(ProjectionExpression node)
        {
            if (node == null) return null;
            var select = VisitAndConvert(node.Select, "DbExpressionVisitor.VisitProjectionn");
            var projector = Visit(node.Projector);
            return node.Update(select, projector, node.Aggregator);
        }

        protected virtual Expression VisitClientJoin(ClientJoinExpression node)
        {
            if (node == null) return null;
            var projection = VisitAndConvert(node.Projection, "DbExpressionVisitor.VisitClientJoin");
            var outerKey = VisitExpressionList(node.OuterKey);
            var innerKey = VisitExpressionList(node.InnerKey);
            return node.Update(projection, outerKey, innerKey);
        }

        protected virtual Expression VisitCommand(CommandExpression node)
        {
            if (node == null) return null;
            switch (node.DbNodeType)
            {
                case DbExpressionType.If:
                    return VisitIf((IfCommand)node);
                case DbExpressionType.Block:
                    return VisitBlock((BlockCommand)node);
                case DbExpressionType.Declaration:
                    return VisitDeclaration((DeclarationCommand)node);
                default:
                    return node;
            }
        }
        
        protected virtual Expression VisitBatch(BatchExpression node)
        {
            if (node == null) return null;
            var operation = VisitAndConvert(node.Operation, "DbExpressionVisitor.VisitBatch");
            var batchSize = Visit(node.BatchSize);
            var stream = Visit(node.Stream);
            return node.Update(node.Input, operation, batchSize, stream);
        }

        protected virtual Expression VisitIf(IfCommand node)
        {
            if (node == null) return null;
            var check = Visit(node.Check);
            var ifTrue = Visit(node.IfTrue);
            var ifFalse = Visit(node.IfFalse);
            return node.Update(check, ifTrue, ifFalse);
        }

        protected virtual Expression VisitBlock(BlockCommand node)
        {
            if (node == null) return null;
            var commands = VisitExpressionList(node.Commands);
            return node.Update(commands);
        }

        protected virtual Expression VisitDeclaration(DeclarationCommand node)
        {
            if (node == null) return null;
            var variables = VisitVariableDeclarations(node.Variables);
            var source = VisitAndConvert(node.Source, "DbExpressionVisitor.VisitDeclaration");
            return node.Update(variables, source);
        }

        protected virtual Expression VisitVariable(VariableExpression node)
        {
            return node;
        }

        protected virtual Expression VisitFunction(FunctionExpression node)
        {
            if (node == null) return null;
            var arguments = VisitExpressionList(node.Arguments);
            return node.Update(arguments);
        }

        protected virtual ColumnAssignment VisitColumnAssignment(ColumnAssignment node)
        {
            if (node == null) return null;
            ColumnExpression c = VisitAndConvert(node.Column, "DbExpressionVisitor.VisitColumnAssignment");
            Expression e = Visit(node.Expression);
            return node.Update(c, e);
        }

        protected virtual IEnumerable<ColumnAssignment> VisitColumnAssignments(ReadOnlyCollection<ColumnAssignment> original)
        {
            return Visit(original, VisitColumnAssignment);
        }

        protected virtual ColumnDeclaration VisitColumnDeclaration(ColumnDeclaration node)
        {
            if (node == null) return null;
            Expression expression = Visit(node.Expression);
            return node.Update(expression);
        }

        protected virtual IEnumerable<ColumnDeclaration> VisitColumnDeclarations(ReadOnlyCollection<ColumnDeclaration> original)
        {
            return Visit(original, VisitColumnDeclaration);
        }

        protected virtual VariableDeclaration VisitVariableDeclaration(VariableDeclaration node)
        {
            if (node == null) return null;
            Expression expression = Visit(node.Expression);
            return node.Update(expression);
        }

        protected virtual IEnumerable<VariableDeclaration> VisitVariableDeclarations(ReadOnlyCollection<VariableDeclaration> original)
        {
            return Visit(original, VisitVariableDeclaration);
        }

        protected virtual OrderExpression VisitOrder(OrderExpression node)
        {
            if (node == null) return null;
            Expression expression = Visit(node.Expression);
            return node.Update(expression);
        }

        protected virtual IEnumerable<OrderExpression> VisitOrderBy(ReadOnlyCollection<OrderExpression> original)
        {
            return Visit(original, VisitOrder);
        }
    }
}