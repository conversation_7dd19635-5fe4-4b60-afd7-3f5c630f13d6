﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringReplaceTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringReplaceTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("123456", "34", "##", ExpectedResult = "12##56")]
        [TestCase("abABab", "b", "#", ExpectedResult = "a#ABa#")]
        [TestCase("123456", "34", null, ExpectedResult = "1256")]
        [TestCase("123456", null, "#", ExpectedResult = "123456")]
        [TestCase(null, null, null, ExpectedResult = null)]
        public async Task<string> String_Replace(object input, string searchString, string replacementString)
        {
            _params["TextInput"] = input;
            _params["SearchString"] = searchString;
            _params["TextReplacement"] = replacementString;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Replace", _params);
            CheckResult(result);

            return result?.Value as string;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
