﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Metadata.Cpi;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringTokenizeTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringTokenizeTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("TASK_SEQ=20267^", "&")]
        public async Task TokenizeSingle(object input, object delimiter)
        {
            _params["TextInput"] = input;
            _params["Delimiter"] = delimiter;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Tokenize", _params);

            CheckResult(result, new MarbleList(new CpiTypeInfo())
            {
                { "TASK_SEQ=20267^" }
            });
        }

        [Test]
        [TestCase("TASK_SEQ=20267^&TASK_SEQ=20268^", "&")]
        public async Task TokenizeAnd(object input, object delimiter)
        {
            _params["TextInput"] = input;
            _params["Delimiter"] = delimiter;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Tokenize", _params);

            CheckResult(result, new MarbleList(new CpiTypeInfo())
            {
                { "TASK_SEQ=20267^" },
                { "TASK_SEQ=20268^" }
            });
        }

        [Test]
        [TestCase("TASK_SEQ=4^Boop=7^;TASK_SEQ=1^Boop=2^", ";")]
        public async Task TokenizeSemiColon(object input, object delimiter)
        {
            _params["TextInput"] = input;
            _params["Delimiter"] = delimiter;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Tokenize", _params);
            CheckResult(result, new MarbleList(new CpiTypeInfo())
            {
                { "TASK_SEQ=4^Boop=7^" },
                { "TASK_SEQ=1^Boop=2^" }
            });
        }

        [Test]
        [TestCase("Test1^Test2^^Test3", "^")]
        public async Task TokenizeEmptySegment(object input, object delimiter)
        {
            _params["TextInput"] = input;
            _params["Delimiter"] = delimiter;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Tokenize", _params);
            CheckResult(result, new MarbleList(new CpiTypeInfo())
            {
                { "Test1" },
                { "Test2" },
                { "Test3" },
            });
        }

        [Test]
        [TestCase("", ";")]
        public async Task TokenizeEmptyString(object input, object delimiter)
        {
            _params["TextInput"] = input;
            _params["Delimiter"] = delimiter;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Tokenize", _params);

            CheckResult(result, new MarbleList(new CpiTypeInfo()));
        }

        [Test]
        [TestCase(null, ";")]
        public async Task TokenizeNull(object input, object delimiter)
        {
            _params["TextInput"] = input;
            _params["Delimiter"] = delimiter;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Tokenize", _params);

            CheckResult(result, new MarbleList(new CpiTypeInfo()));
        }

        private static void CheckResult(ExecuteResult result, MarbleList expected)
        {
            Assert.IsNotNull(result);

            MarbleList resultList = result.Value as MarbleList;

            CollectionAssert.AreEqual(expected, resultList);

            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
