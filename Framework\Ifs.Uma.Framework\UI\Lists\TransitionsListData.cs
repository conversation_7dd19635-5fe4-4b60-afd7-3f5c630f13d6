﻿using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Unity.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.Localization;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.Framework.UI.Lists
{
    public class TransitionsListData : ListData<TransitionItem>
    {
        private readonly IDataContextProvider _db;
        private readonly ILogger _logger;
        private readonly IDialogService _dialogService;
        private ITransactionSyncService _transactionSyncService;
        private SynchronizationContext _synchronizationContext = SynchronizationContext.Current;

        private TransitionItem _lastFixedTransaction;

        public TransitionsListData(IDataContextProvider db, [OptionalDependency] IDialogService dialogService, [OptionalDependency] ILogger logger) 
            : base(null)
        {
            _db = db;
            _dialogService = dialogService;
            _logger = logger;

            Items.Sort = (x, y) => x.TransitionRow.RowId.CompareTo(y.TransitionRow.RowId);
        }

        protected override async Task OnUpdateAsync()
        {
            TransitionRow[] rows = null;

            if (Items.Count == 1 && Items[0]?.TransitionRow?.RowId != _lastFixedTransaction?.TransitionRow?.RowId)
            {
                SelectedItem = Items[0];
            }
            else if (Items.Count == 0 && _lastFixedTransaction != null)
            {
                _lastFixedTransaction = null;
            }

            await Task.Run(() =>
            {
                try
                {
                    var ctx = _db.CreateDataContext();
                    rows = ctx.TransitionRows.Where(x => x.SyncState != SyncState.SentAcknowledged).ToArray();
                }
                catch (Exception)
                {
                    rows = new TransitionRow[0];
                }
            });

            using (Items.DeferRefresh())
            {
                Items.Clear();

                if (rows != null)
                {
                    IMetaModel metaModel = _db.GetMetaModel();
                    foreach (TransitionRow row in rows)
                    {
                        Items.Add(new TransitionItem(row, metaModel));
                    }
                }
            }
        }

        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                Resolver.TryResolve<ITransactionSyncService>(out _transactionSyncService);

                if (_transactionSyncService != null)
                {
                    _transactionSyncService.SyncEvent += SyncService_SyncEvent;
                }

                UpdateAsync();
            }
            else
            {
                if (_transactionSyncService != null)
                {
                    _transactionSyncService.SyncEvent -= SyncService_SyncEvent;
                    _transactionSyncService = null;
                }
            }
        }

        private void SyncService_SyncEvent(object sender, TransactionSyncEventArgs e)
        {
            if (e.EventType == SyncEventType.SyncEnded ||
               (e.EventType == SyncEventType.SyncTrace && e.TraceType == SyncTraceType.UploadedMessages))
            {
                _synchronizationContext.Post((state) => UpdateAsync(), null);                
            }
        }

        protected override void OnSelectedItemChanged()
        {
            base.OnSelectedItemChanged();

            TransitionItem selectedItem = SelectedItem;
            if (selectedItem != null)
            {
                _lastFixedTransaction = selectedItem;
                _ = SelectTransition(selectedItem.TransitionRow);
            }

            SelectedItem = null;
        }

        private async Task SelectTransition(TransitionRow item)
        {
            if (item.SyncState == SyncState.Failed && _dialogService != null)
            {
                TransitionData ft = await TransitionData.FromTransitionRowId(_db, _logger, item.RowId);
                
                if (ft != null)
                {
                    if (await HandleFailedTransition(ft))
                    {
                        await UpdateAsync();

                        if (_transactionSyncService != null)
                        {
                            _transactionSyncService.RequestSync();
                        }
                    }
                }
            }
        }

        private async Task<bool> HandleFailedTransition(TransitionData ft)
        {
            List<string> actions = new List<string>();
            IFailedTransitionHandler transitionHandler;

            //Prevent multiple dialogs to be shown at the same time
            await _dialogService.WaitForDialogsToClose(CancellationToken.None);
            
            //Windows Dialog has a limitation of showing 3 buttons, therefore displays only Edit, Delete and Close.
            //iOS & Android will show all options of Resend, Edit, Delete and Cancel.
            if (DeviceInfo.OperatingSystem == OperatingSystem.Windows)
            {
                if (Resolver.TryResolve<IFailedTransitionHandler>(out transitionHandler))
                {
                    actions.Add(Strings.Edit);
                }
                else
                {
                    actions.Add(Strings.Resend);
                }
                actions.Add(Strings.Delete);
                actions.Add(Strings.Close);
            }
            else
            {
                actions.Add(Strings.Resend);
                if (Resolver.TryResolve<IFailedTransitionHandler>(out transitionHandler))
                {
                    actions.Add(Strings.Edit);
                }
                actions.Add(Strings.Delete);
                actions.Add(Strings.Cancel);
            }
            
            IMetaModel metaModel = _db.GetMetaModel();
            int selectedOption = await _dialogService.ShowAsync(Strings.FixTransaction, ft.GetTransitionName(metaModel), actions.ToArray());
            string selectedAction = actions.ElementAt(selectedOption);
            
            if (selectedAction.Equals(Strings.Resend))
            {
                await ft.Resend();
                return true;
            }
            else if (selectedAction.Equals(Strings.Edit))
            {
                bool result = await transitionHandler.HandleFailedTransition(ft);
                return result;
            }
            else if (selectedAction.Equals(Strings.Delete))
            {
                bool confirmed = await _dialogService.Confirm(null, Strings.ConfirmDeleteTransaction, Strings.Delete, ConfirmationType.Destructive);

                if (confirmed)
                {
                    await ft.Delete();

                    if (Items.Count == 1)
                    {
                        if (await _dialogService.Confirm(Strings.DeviceMustBeReinitialized, Strings.ReInitializeDialog, Strings.Reinitialize, ConfirmationType.Normal))
                        {
                            if (_transactionSyncService != null)
                            {
                                _transactionSyncService.RequestInitialization(false);
                            }
                        }
                    }
                }
                return confirmed;
            }
            else
            {
                return false;
            }
        }
    }

    public class TransitionItem : ObservableBase
    {
        public TransitionRow TransitionRow { get; }
        public string TableName { get; }
        public bool IsFailing { get; }
        public string Detail { get; }

        public TransitionItem(TransitionRow transitionRow, IMetaModel metaModel)
        {
            TransitionRow = transitionRow;
            TableName = TransitionData.GetTransitionName(transitionRow, metaModel);
            IsFailing = transitionRow.SyncState == SyncState.Failed;
            Detail = GetDetailText(transitionRow);
        }

        private string GetDetailText(TransitionRow transitionRow)
        {
            switch (transitionRow.SyncState)
            {
                case SyncState.Sent:
                case SyncState.SentAcknowledged:
                    return Strings.Sent;
                case SyncState.Failed:
                    if (string.IsNullOrEmpty(transitionRow.ErrorMessage))
                    {
                        return Strings.Failed;
                    }
                    else
                    {
                        return string.Format(Strings.FailedMessage, transitionRow.ErrorMessage);
                    }
                case SyncState.Unsent:
                default:
                    return Strings.Waiting;
            }
        }
    }
}
