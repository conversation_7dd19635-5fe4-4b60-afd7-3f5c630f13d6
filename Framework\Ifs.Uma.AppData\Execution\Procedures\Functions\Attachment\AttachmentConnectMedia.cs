﻿using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Model;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Attachment
{
    internal sealed class AttachmentConnectMedia : AttachmentFunction
    {
        public const string FunctionName = "ConnectMedia";

        public AttachmentConnectMedia()
            : base(FunctionName, 3)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            FndMediaKeys mediaKeys = parameters[0].GetValue() as FndMediaKeys;
            string luName = parameters[1].GetString();
            string keyRef = parameters[2].GetString();
            
            return ConnectMedia(context, mediaKeys, luName, keyRef);
        }

        internal static object ConnectMedia(ProcedureContext context, FndMediaKeys mediaKeys, string luName, string keyRef)
        {
            MediaHandler.ConnectMedia(context.DbDataContext, mediaKeys, luName, keyRef, out MediaLibrary mediaLibrary, out MediaLibraryItem mediaLibraryItem, out bool createdMediaLibrary);

            if (createdMediaLibrary)
            {
                context.DataChangeSet.AddRow(context.Metadata.MetaModel, mediaLibrary);
            }

            context.DataChangeSet.AddRow(context.Metadata.MetaModel, mediaLibraryItem);

            return null;
        }
    }
}
