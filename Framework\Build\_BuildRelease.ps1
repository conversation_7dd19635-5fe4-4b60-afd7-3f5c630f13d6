$env:MSBUILDDISABLENODEREUSE = 1;
$ErrorActionPreference = "Stop" 

# Determine the git branch name
$gitBranch = (git symbolic-ref --short HEAD)

$appVersionMajorMinor = (Get-Date -Format "yy") + ".99"

# Release builds should follow yy.M format (like 22.3)
# Dev builds (master branch) should follow yy.99 format (like 22.99)
if ($gitBranch -ne "master") {
	$appVersionMajorMinor = Get-Date -Format "yy.M"
}

$solutionDir = "..\"
$solutionFile = $solutionDir + "Ifs.Uma.sln"
$deliverablesDir = $solutionDir + "Deliverables\"
$appVersionRevision = "0"
$publishDirName = $appVersionMajorMinor + "_dev"
$deliverableName = "RiverTASAurenaNative"
$tagPrefix = ""
$documentationDir = "..\..\Documentation\"
$documentationBuildDir = $documentationDir + "Publish\"
$publishDocumentationDir = ""
$massToolsDir = ".\MaSSTools\"
$iOSAppStoreBundleId = ""
$androidKeystoreAlias = ""
$androidSigningKeyPass = ""

# Override variables if a specific app should be built
# Running the script inside the app folder should set the correct values
if ($env:APP_NAME)
{
	$appVariablesScriptPath = "..\..\Apps\" + $env:APP_NAME + "\SetupVariables.ps1"
	if (Test-Path -Path $appVariablesScriptPath)
	{
		. $appVariablesScriptPath
	}
	else
	{
		Write-Host "App folder or required scripts were not found. Cannot continue."
		Exit -1
	}
}

$publishDir = "X:\" + $publishDirName + "\"
$publishUrl = "https://mass.ifsworld.com/mass/IfsAurenaNative/" + $publishDirName + "/"

# Set service user credentials
# These must be defined in Jenkins correctly
$username = $env:username
$password = $env:password
$encryptedPassword = ConvertTo-SecureString $password -AsPlainText -Force
$serviceUser = New-Object -TypeName System.Management.Automation.PSCredential -ArgumentList $userName,$encryptedPassword;

# Define new drive (X:) as copy destination
New-PSDrive -Name X -PSProvider FileSystem -Root "\\dse1pde116\mass\IfsAurenaNative" -Credential $serviceUser -ErrorAction SilentlyContinue

Remove-Item -Force -Recurse $deliverablesDir -ErrorAction SilentlyContinue
New-Item -ItemType Directory -Force -Path $deliverablesDir | Out-Null

$buildIos = 1
$buildAndroid = 1
$buildWindows = 1

& .\_PrepareVersion.ps1
if ($LastExitCode -ne 0) { Exit $LastExitCode }

& .\_CommitAndPushChangeLog.ps1
if ($LastExitCode -ne 0) { Exit $LastExitCode }

& .\_PrepareMaSSTools.ps1
if ($LastExitCode -ne 0) { Exit $LastExitCode }

# Call AppBuilder here
if ($env:APP_NAME)
{
	Write-Output "============ Run AppBuilder"
	
	foreach ($line in Get-Content ("..\..\Apps\" + $env:APP_NAME + "\AppConfig.txt"))
	{
		if ($line -match "Platforms")
		{
			if (-Not ($line -match "iOS")) { $buildIos = 0 }
			if (-Not ($line -match "Android")) { $buildAndroid = 0 }
			if (-Not ($line -match "Windows")) { $buildWindows = 0 }
			break
		}
	}
	
	& ..\.nuget\NuGet.exe restore "..\..\Tools\AppBuilder\AppBuilder.sln"
	msbuild "..\..\Tools\AppBuilder\AppBuilder.sln" /m /t:Rebuild /nr:false /v:m /p:Configuration=Release /p:RestorePackages=false
	& "..\..\Tools\AppBuilder\bin\Release\AppBuilder.exe" ("..\..\Apps\" + $env:APP_NAME) $solutionDir ($massToolsDir + "AppIconCreator\AppIconCreator")
	
	if ($LastExitCode -ne 0) { Exit $LastExitCode }
	
	Write-Output "============ End - Run AppBuilder"
}

Write-Output "============ NuGet Restore"

& "$($solutionDir).nuget/nuget" restore $solutionFile
if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - NuGet Restore"

& .\_RunTests.ps1
if ($LastExitCode -ne 0) { Exit $LastExitCode }

if (($buildIos -eq 1) -and ($env:BUILD_IOS -eq $true))
{
    & .\_BuildReleaseIOS.ps1
    if ($LastExitCode -ne 0) { Exit $LastExitCode }
}

if (($buildAndroid -eq 1) -and ($env:BUILD_ANDROID -eq $true))
{
    & .\_BuildReleaseAndroid.ps1
    if ($LastExitCode -ne 0) { Exit $LastExitCode }
}

if (($buildWindows -eq 1) -and ($env:BUILD_WINDOWS -eq $true))
{
    & .\_BuildReleaseWindows.ps1
    if ($LastExitCode -ne 0) { Exit $LastExitCode }
}

if (($env:COPY_TO_SHARE -eq $null) -or ($env:COPY_TO_SHARE -eq $true))
{
    & .\_Publish.ps1
    # If publishing fails, we should not fail the entire build
}

if (![string]::IsNullOrEmpty($tagPrefix))
{
    & .\_AddTag.ps1
}