﻿using System;
using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public class DbInternal
    {
        /// <summary>
        /// Gets UTC Now with centisecond accuracy and Unspecified Kind
        /// The value can survive a database roundtrip
        /// </summary>
        public static DateTime NowUtc
        {
            get
            {
                DateTime utcNow = DateTime.UtcNow;
                int centi = utcNow.Millisecond / 10;
                return new DateTime(utcNow.Year, utcNow.Month, utcNow.Day, utcNow.Hour, utcNow.Minute, utcNow.Second, centi * 10);
            }
        }
        /// <summary>
        /// Gets Now with centisecond accuracy and Unspecified Kind
        /// The value can survive a database roundtrip
        /// </summary>
        public static DateTime Now
        {
            get
            {
                DateTime now = DateTime.Now;
                int centi = now.Millisecond / 10;
                return new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second, centi * 10);
            }
        }

        /// <summary>
        /// Insert command support with additional support for upsert and identity columns
        /// </summary>
        /// <param name="command">Database command</param>
        /// <param name="spec">Insert to perform</param>
        /// <param name="builder">SQL builder</param>
        /// <returns>Identity column of inserted or updated row or -1 on failure</returns>
        public static long InsertWithIdentity(DbCommand command, IInsertSpec spec, SqlBuilder builder)
        {
            if (command == null) throw new ArgumentNullException("command");
            if (spec == null) throw new ArgumentNullException("spec");
            if (builder == null) throw new ArgumentNullException("builder");
            long result = -1;
            if (!builder.SupportsUpsert)
            {
                // check for upsert
                IUpdateSpec update = spec.ToUpdate();
                if (update != null)
                {
                    // we are attempting an upsert - try the update first
                    builder.BuildSql(command, update);
                    if (command.ExecuteNonQuery() > 0)
                    {
                        // update succeeded
                        if (!string.IsNullOrEmpty(spec.IdentityColumn))
                        {
                            // find the identity column that got changed
                            ISelectSpec select = SqlSpec.CreateSelect(
                                new ISelectColumnSpec[] { ColumnSpec.CreateSelect(spec.IdentityColumn) },
                                TableSpec.Create(spec.TableName), null, update.Where, null, null, false);
                            builder.BuildSql(command, select);
                            object oResult = command.ExecuteScalar();
                            if (oResult != null)
                            {
                                result = ObjectConverter.ToLong(oResult);
                            }
                        }
                        return result;
                    }
                    // update failed so switch to the non-upsert version of the insert.
                    spec = spec.ToInsert();
                }
            }
            builder.BuildSql(command, spec);
            if (!string.IsNullOrEmpty(spec.IdentityColumn))
            {
                // insert with Identity column
                string scopeIdentityCommand = builder.ScopeIdentityCommand(spec);
                if (!string.IsNullOrEmpty(scopeIdentityCommand))
                {
                    command.ExecuteNonQuery();
                    command.CommandText = scopeIdentityCommand;
                }
                object oResult = command.ExecuteScalar();
                if (oResult != null)
                {
                    result = ObjectConverter.ToLong(oResult);
                }
            }
            else
            {
                // ordinary insert (just in case)
                command.ExecuteNonQuery();
            }
            return result;
        }

        public DbInternal(DbProviderFactory factory, string connectionString,
            MappingSource mappingSource, ILogger logger)
        {
            if (factory == null) throw new ArgumentNullException("factory");
            if (string.IsNullOrEmpty(connectionString)) throw new ArgumentNullException("connectionString");
            m_factory = factory;
            m_connectionString = connectionString;
            m_mappingSource = mappingSource;
            m_logger = logger;
        }

        public DbProviderFactory Factory { get { return m_factory; } }
        public string ConnectionString { get { return m_connectionString; } }
        public ILogger Logger { get { return m_logger; } }
        public MappingSource MappingSource { get { return m_mappingSource; } }

        public T CommandF<T>(Func<DbCommand, T> func)
        {
            if (func == null) throw new ArgumentNullException("func");
            T result;
            using (DbConnection conn = GetConnection())
            {
                conn.Open();
                using (DbCommand cmd = conn.CreateCommand(null))
                {
                    try
                    {
                        result = func(cmd);
                    }
                    catch (Exception ex)
                    {
                        m_logger.Error(Strings.CommandFunctionError, ex.FormatXml(false, false));
                        throw;
                    }
                }
            }
            return result;
        }

        public IEnumerable<T> CommandDeferred<T>(Func<DbCommand, IEnumerable<T>> func)
        {
            if (func == null) throw new ArgumentNullException(nameof(func));
            using (DbConnection conn = GetConnection())
            {
                conn.Open();
                using (DbCommand cmd = conn.CreateCommand(null))
                {
                    IEnumerator<T> enumerator = null;
                    try
                    {
                        try
                        {
                            IEnumerable<T> results = func(cmd);
                            enumerator = results.GetEnumerator();
                        }
                        catch (Exception ex)
                        {
                            m_logger.Error(Strings.CommandFunctionError, ex.FormatXml(false, false));
                            throw;
                        }

                        while (true)
                        {
                            T result;
                            try
                            {
                                if (enumerator.MoveNext())
                                {
                                    result = enumerator.Current;
                                }
                                else
                                {
                                    break;
                                }
                            }
                            catch (Exception ex)
                            {
                                m_logger.Error(Strings.CommandFunctionError, ex.FormatXml(false, false));
                                throw;
                            }

                            yield return result;
                        }
                    }
                    finally
                    {
                        if (enumerator != null)
                        {
                            enumerator.Dispose();
                        }
                    }
                }
            }
        }

        public void CommandA(Action<DbCommand> act)
        {
            if (act == null) throw new ArgumentNullException("act");
            CommandF((cmd) =>
            {
                act(cmd);
                return 0;
            });
        }

        public T TransactionF<T>(Func<DbCommand, T> func)
        {
            if (func == null) throw new ArgumentNullException("func");
            T result;
            using (DbConnection conn = GetConnection())
            {
                conn.Open();
                using (DbTransaction tran = conn.BeginTransaction())
                {
                    using (DbCommand cmd = conn.CreateCommand(tran))
                    {
                        try
                        {
                            result = func(cmd);
                        }
                        catch (Exception ex)
                        {
                            m_logger.Error(Strings.TransactionFunctionError, ex.FormatXml(false, false));
                            throw;
                        }
                    }
                    tran.Commit();
                }
            }
            return result;
        }

        public void TransactionA(Action<DbCommand> act)
        {
            if (act == null) throw new ArgumentNullException("act");
            TransactionF((cmd) =>
            {
                act(cmd);
                return 0;
            });
        }

        /// <summary>
        /// Creates a new empty database.  Any existing database is dropped.
        /// </summary>
        /// <param name="model">Tables to create</param>
        public void CreateDatabase(IMetaModel model)
        {
            using (DbConnection connection = GetConnection())
            {
                connection.CreateDatabase();
                if (model != null)
                {
                    BuildDatabaseImpl(connection, model);
                }
            }
        }

        public void BuildDatabase(IMetaModel model)
        {
            if (model == null)
            {
                throw new ArgumentNullException(nameof(model));
            }

            using (DbConnection connection = GetConnection())
            {
                connection.Open();
                BuildDatabaseImpl(connection, model);
            }
        }

        private void BuildDatabaseImpl(DbConnection connection, IMetaModel model)
        {
            IEnumerable<string> ddl = m_factory.Builder.BuildDdl(model);
            if (ddl != null && ddl.Any())
            {
                using (DbTransaction tran = connection.BeginTransaction())
                {
                    using (DbCommand command = connection.CreateCommand(tran))
                    {
                        DdlStatement.Execute(command, ddl);
                        tran.Commit();
                    }
                }
            }
        }
        
        /// <summary>
        /// Drop the database - if the database file does not exist then nothing to do.
        /// </summary>
        public void DropDatabase()
        {
            using (DbConnection connection = GetConnection())
            {
                connection.DropDatabase();
            }
        }

        /// <summary>
        /// Returns whether a file exists for the database and if so whether it can be opened successfully
        /// </summary>
        /// <returns>Status value</returns>
        public DbStatus GetStatus()
        {
            using (DbConnection connection = GetConnection())
            {
                return connection.GetStatus();
            }
        }

        protected DbConnection GetConnection()
        {
            return m_factory.CreateConnection(m_connectionString, m_mappingSource);
        }

        private string m_connectionString;
        private DbProviderFactory m_factory;
        private MappingSource m_mappingSource;
        private ILogger m_logger;
    }
}
