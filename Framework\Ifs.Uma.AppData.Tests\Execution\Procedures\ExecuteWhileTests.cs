﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteWhileTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteWhileSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        public async Task WhileWithReturn()
        {
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "FnTestWhile", _params);
            result.CheckFailure();
            Assert.AreEqual("OK", result.Value);
        }

        [Test]
        public async Task WhileNoReturn()
        {
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "FnTestWhileNoReturn", _params);
            result.CheckFailure();
            Assert.Pass();
        }

        [Test]
        public async Task WhileWithBreak()
        {
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "FnBreakWhileLoop", _params);
            result.CheckFailure();
            Assert.AreEqual(5, result.Value);
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
