﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_media_library_item", Columns = nameof(ItemId) + "," + nameof(LibraryId) + "," + nameof(LibraryItemId), Unique = true)]
    [Relation(ReferencedRowType = typeof(MediaLibrary), Columns = nameof(LibraryId), ReferencedColumns = nameof(MediaLibrary.LibraryId))]
    [Relation(ReferencedRowType = typeof(MediaItem), Columns = nameof(ItemId), ReferencedColumns = nameof(MediaItem.ItemId))]    
    [EnumServerNaming(EnumType = typeof(MediaType), Convention = NamingConvention.UpperCase)]
    public class MediaLibraryItem : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "media_library_item";

        public MediaLibraryItem()
            : base(DbTableName)
        {
            EntitySetName = "MediaLibraryItems";
        }

        private string _libraryId;
        [Column(Storage = nameof(_libraryId), Mandatory = true, ServerPrimaryKey = true)]
        public string LibraryId
        {
            get => _libraryId;
            set => SetProperty(ref _libraryId, value);
        }

        private long? _libraryItemId;
        [Column(Storage = nameof(_libraryItemId), Mandatory = true, ServerPrimaryKey = true)]
        public long? LibraryItemId
        {
            get => _libraryItemId;
            set => SetProperty(ref _libraryItemId, value);
        }

        private long? _itemId;
        [Column(Storage = nameof(_itemId), Mandatory = true, ServerPrimaryKey = true)]
        public long? ItemId
        {
            get => _itemId;
            set => SetProperty(ref _itemId, value);
        }
        
        private string _name;
        [Column(Storage = nameof(_name), MaxLength = 250)]
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        private string _description;
        [Column(Storage = nameof(_description), MaxLength = 250)]
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        private string _mediaFile;
        [Column(Storage = nameof(_mediaFile))]
        public string MediaFile
        {
            get => _mediaFile;
            set => SetProperty(ref _mediaFile, value);
        }
        
        private bool? _privateMediaItem;
        [Column(Storage = nameof(_privateMediaItem))]
        public bool? PrivateMediaItem
        {
            get => _privateMediaItem;
            set => SetProperty(ref _privateMediaItem, value);
        }

        private bool? _defaultMedia;
        [Column(Storage = nameof(_defaultMedia))]
        public bool? DefaultMedia
        {
            get => _defaultMedia;
            set => SetProperty(ref _defaultMedia, value);
        }

        private MediaType? _mediaItemType;
        [Column(Storage = nameof(_mediaItemType))]
        public MediaType? MediaItemType
        {
            get => _mediaItemType;
            set => SetProperty(ref _mediaItemType, value);
        }

        private AttachmentStatus? _attachmentStatus;
        [Column(Storage = nameof(_attachmentStatus))]
        public AttachmentStatus? AttachmentStatus
        {
            get => _attachmentStatus;
            set => SetProperty(ref _attachmentStatus, value);
        }

        private string _failReason;
        [Column(Storage = nameof(_failReason))]
        public string FailReason
        {
            get => _failReason;
            set => SetProperty(ref _failReason, value);
        }

        private double? _latitude;
        [Column(Storage = nameof(_latitude))]
        public double? Latitude
        {
            get => _latitude;
            set => SetProperty(ref _latitude, value);
        }

        private double? _longitude;
        [Column(Storage = nameof(_longitude))]
        public double? Longitude
        {
            get => _longitude;
            set => SetProperty(ref _longitude, value);
        }
    }

    public enum MediaType
    {
        Image,
        Text,
        Audio,
        Video
    }
}
