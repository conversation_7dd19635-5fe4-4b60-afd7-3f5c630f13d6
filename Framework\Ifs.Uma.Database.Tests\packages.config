﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="NUnit" version="3.10.1" targetFramework="net451" />
  <package id="NUnit3TestAdapter" version="3.10.0" targetFramework="net451" />
  <package id="OpenCover" version="4.7.922" targetFramework="net461" />
  <package id="OpenCoverToCoberturaConverter" version="0.3.4" targetFramework="net461" />
  <package id="SQLitePCLRaw.bundle_sqlcipher" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.core" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.lib.sqlcipher.linux" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.lib.sqlcipher.osx" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.lib.sqlcipher.windows" version="1.1.14" targetFramework="net461" />
  <package id="SQLitePCLRaw.provider.sqlcipher.net45" version="1.1.14" targetFramework="net461" />
  <package id="StyleCop.Analyzers" version="1.0.2" targetFramework="net451" developmentDependency="true" />
</packages>