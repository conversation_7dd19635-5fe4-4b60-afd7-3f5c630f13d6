﻿using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Ifs.Uma.Utility;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Logger = Ifs.Uma.Utility.Logger;

namespace Ifs.Uma.Framework.MSTeams
{
    public static partial class MSAccessTokenProvider
    {
        public static string ClientId { get; set; }
        private static string[] _scopes = new string[] { "Presence.Read User.ReadBasic.All Presence.Read.All User.read" };
        private static string _iOSKeychainSecurityGroup = null;
        public static string[] GetScopes { get { return _scopes; } }
        public static string GetiOSKeychainSecurityGroup { get { return _iOSKeychainSecurityGroup; } }
        public static string SetiOSKeychainSecurityGroup(string iOSKeychainSecurityGroup) { return _iOSKeychainSecurityGroup = iOSKeychainSecurityGroup; }

        private static string GetUri()
        {
            string uri = " ";
            if (Resolver.TryResolve(out App.ITouchApp touchApp) && !string.IsNullOrEmpty(touchApp.Registration.AuthenticationRedirectUri))
            {
                uri = touchApp.Registration.AuthenticationRedirectUri;
                uri = uri.Substring(0, uri.IndexOf(":"));
            }

            return "msauth://" + uri;
        }

        public static IPublicClientApplication GetPublicClientApp()
        {
            IPublicClientApplication app;
            try
            {
                if (DeviceInfo.OperatingSystem == Utility.OperatingSystem.Windows)
                {
                    app = PublicClientApplicationBuilder.Create(ClientId)
                                                        .WithBroker()
                                                        .Build();
                }
                else if (!string.IsNullOrEmpty(GetiOSKeychainSecurityGroup))
                {
                    app = PublicClientApplicationBuilder.Create(ClientId).WithRedirectUri(GetUri()).WithIosKeychainSecurityGroup(GetiOSKeychainSecurityGroup).Build();
                }
                else
                {
                    app = PublicClientApplicationBuilder.Create(ClientId)
                                                        .WithRedirectUri(GetUri())
                                                        .Build();
                }
                return app;
            }
            catch (Exception ex)
            {
                Logger.Current.Error(ex.Message);
                return null;
            }
        }

        public static async Task Authenticate(object authActivity = null)
        {
            IPublicClientApplication app = GetPublicClientApp();
            if (app != null)
            {
                System.Collections.Generic.IEnumerable<IAccount> accounts = await app.GetAccountsAsync();

                try
                {
                    await app.AcquireTokenSilent(GetScopes, accounts.FirstOrDefault()).ExecuteAsync();
                }
                catch (MsalUiRequiredException)
                {
                    try
                    {
                        AcquireTokenInteractiveParameterBuilder interactiveRequest = app.AcquireTokenInteractive(GetScopes).WithAccount(accounts.FirstOrDefault());

                        if (authActivity != null)
                        {
                            interactiveRequest = interactiveRequest.WithPrompt(Prompt.ForceLogin)
                                                                   .WithUseEmbeddedWebView(true)
                                                                   .WithParentActivityOrWindow(authActivity);
                        }

                        await interactiveRequest.ExecuteAsync();
                    }
                    catch (MsalException msalex)
                    {
                        Logger.Current.Error(msalex.Message);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Current.Error(ex.Message);
                }
            }
        }

        public static async Task LogOut()
        {
            try
            {
                if (GetPublicClientApp() != null)
                {
                    System.Collections.Generic.IEnumerable<IAccount> accounts = await GetPublicClientApp().GetAccountsAsync();

                    if (accounts.Any())
                    {
                        try
                        {
                            await GetPublicClientApp().RemoveAsync(accounts.FirstOrDefault());
                        }
                        catch (Exception ex)
                        {
                            Logger.Current.Error(ex.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Current.Error(ex.Message);
            }
        }

        public static async Task<string> GetUserIdByEmail(string email)
        {
            string userId = null;

            try
            {
                if (GetPublicClientApp() != null)
                {
                    System.Collections.Generic.IEnumerable<IAccount> accounts = await GetPublicClientApp().GetAccountsAsync();

                    Microsoft.Graph.DelegateAuthenticationProvider authProvider = new Microsoft.Graph.DelegateAuthenticationProvider(async (request) =>
                    {
                        AuthenticationResult result = await GetPublicClientApp().AcquireTokenSilent(GetScopes, accounts.FirstOrDefault()).ExecuteAsync();
                        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", result.AccessToken);
                    });

                    Microsoft.Graph.GraphServiceClient graphClient = new Microsoft.Graph.GraphServiceClient(authProvider);

                    HttpRequestMessage usersRequest = graphClient.Users.Request().Filter($"mail eq '{email}'").GetHttpRequestMessage();
                    HttpResponseMessage usersResponse = await graphClient.HttpProvider.SendAsync(usersRequest);
                    string jsonContent = await usersResponse.Content.ReadAsStringAsync();
                    JObject jsonObject = (JObject)JsonConvert.DeserializeObject(jsonContent);

                    if (jsonObject != null && jsonObject.ContainsKey("value"))
                    {
                        JArray valueObj = (JArray)jsonObject.SelectToken("value");
                        if (valueObj != null && valueObj.Count > 0)
                        {
                            JObject userObj = (JObject)valueObj[0];
                            if (userObj != null && userObj.ContainsKey("id"))
                            {
                                userId = userObj.GetValue("id").ToString();
                            }
                        }
                    }
                }

                return userId;
            }
            catch (Exception ex)
            {
                Logger.Current.Error(ex.Message);
                return userId;
            }
        }

        public static async Task<string> GetPresenceAsync(string email)
        {
            string presenceActivity = null;
            string userId = await GetUserIdByEmail(email);

            try
            {
                if (GetPublicClientApp() != null)
                {
                    System.Collections.Generic.IEnumerable<IAccount> accounts = await GetPublicClientApp().GetAccountsAsync();

                    Microsoft.Graph.DelegateAuthenticationProvider authProvider = new Microsoft.Graph.DelegateAuthenticationProvider(async (request) =>
                    {
                        var result = await GetPublicClientApp().AcquireTokenSilent(GetScopes, accounts.FirstOrDefault()).ExecuteAsync();
                        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", result.AccessToken);
                    });

                    Microsoft.Graph.GraphServiceClient graphClient = new Microsoft.Graph.GraphServiceClient(authProvider);
                    HttpRequestMessage usersRequest = graphClient.Users[userId].Presence.Request().GetHttpRequestMessage();
                    HttpResponseMessage usersResponse = await graphClient.HttpProvider.SendAsync(usersRequest);
                    string jsonContent = await usersResponse.Content.ReadAsStringAsync();
                    JObject jsonObject = (JObject)JsonConvert.DeserializeObject(jsonContent);
                    if (jsonObject != null && jsonObject.ContainsKey("availability"))
                    {
                        presenceActivity = jsonObject.GetValue("availability").ToString();
                    }
                }

                return presenceActivity;
            }
            catch (Exception ex)
            {
                Logger.Current.Error(ex.Message);
                return presenceActivity;
            }
        }
    }
}
