﻿using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Data.Tests
{
    [TestFixture]
    public abstract class DataContextTest<T> where T : DataContextBase
    {
        private const string TestConnectionString = "data source=t.db";
        private ILogger _logger;
        private DbInternal _db;

        public DataContextTest()
        {
            _logger = new DebugLogger();
        }

        [SetUp]
        protected virtual void BeforeTest()
        {
            const string DllPath = "Ifs.Uma.Database.SQLite, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null";
            PlatformServicesTest.Initialize();

            QueryCache.Clear();
            Assert.IsTrue(QueryCache.IsEnabled);

            DbProviderFactory factory = DbProviderFactory.Create(DllPath, _logger);
            MappingSource source = new AttributeMappingSource(null, _logger, typeof(T));
            DbInternal db = new DbInternal(factory, TestConnectionString, source, _logger);
            db.CreateDatabase(db.MappingSource.GetModel(typeof(T)));
            _db = db;
        }

        [TearDown]
        protected virtual void AfterTest()
        {
            if (_db != null)
            {
                _db.Factory.Dispose();
                _db = null;
            }
        }
        
        protected T CreateDataContext()
        {
            return (T)Activator.CreateInstance(typeof(T), new object[] { _db });
        }
    }
}
