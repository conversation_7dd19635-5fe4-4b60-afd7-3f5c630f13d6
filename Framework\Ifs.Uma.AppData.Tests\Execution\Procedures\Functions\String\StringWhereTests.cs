﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringWhereTests : ProcedureTest
    {
        [Test]
        public async Task Like()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["MySearch"] = "51%";
            string result = await CallFunction<string>(TestOfflineProjection, "GetCustomers", parameters);
            Assert.AreEqual("#512#513", result);

            parameters["MySearch"] = "%51";
            result = await CallFunction<string>(TestOfflineProjection, "GetCustomers", parameters);
            Assert.AreEqual("#551", result);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>(
                "Execution.Procedures.Functions.String.StringWhereTestsSchema",
                "Execution.Procedures.Functions.String.StringWhereTestsData");
        }
    }
}
