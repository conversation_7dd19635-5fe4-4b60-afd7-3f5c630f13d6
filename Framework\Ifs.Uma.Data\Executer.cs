﻿using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using IQToolkit;
using IQToolkit.Data;
using IQToolkit.Data.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Ifs.Uma.Data
{
    internal class Executer : QueryExecutor
    {
        private DbEntityProvider _provider;
        private int _rowsAffected;

        public Executer(DbEntityProvider provider)
        {
            _provider = provider;
        }

        public EntityProvider Provider
        {
            get { return _provider; }
        }

        public override int RowsAffected
        {
            get { return _rowsAffected; }
        }

        public override object Convert(object value, Type type)
        {
            if (value == null)
                return TypeHelper.GetDefault(type);

            type = TypeHelper.GetNonNullableType(type);
            Type vtype = value.GetType();
            if (type != vtype)
            {
                if (type.GetTypeInfo().IsEnum)
                {
                    if (vtype == typeof(string))
                    {
                        return _provider.GetEnumeration(type).LocalValue((string)value);
                    }
                    else
                    {
                        Type utype = Enum.GetUnderlyingType(type);
                        if (utype != vtype)
                        {
                            value = System.Convert.ChangeType(value, utype, System.Globalization.CultureInfo.InvariantCulture);
                        }
                        return Enum.ToObject(type, value);
                    }
                }

                if (type == typeof(DateTime) && vtype == typeof(long))
                {
                    return ObjectConverter.ToDateTime((long)value);
                }

                if (type == typeof(Guid) && vtype == typeof(string))
                {
                    return ObjectConverter.ToGuid((string)value);
                }

                return System.Convert.ChangeType(value, type, System.Globalization.CultureInfo.InvariantCulture);
            }
            return value;
        }

        public override IEnumerable<T> Execute<T>(QueryCommand query, Func<FieldReader, T> fnProjector, object[] paramValues)
        {
            if (query == null) throw new ArgumentNullException("query");
            if (fnProjector == null) throw new ArgumentNullException("fnProjector");
            return _provider.CommandF((cmd) => Execute(cmd, query, fnProjector, paramValues));
        }

        private IEnumerable<T> Execute<T>(DbCommand cmd, QueryCommand query, Func<FieldReader, T> fnProjector, object[] paramValues)
        {
            ICollection<T> result = new List<T>();
            SetCommand(cmd, query, paramValues, false);
            using (DbDataReader reader = cmd.ExecuteReader())
            {
                var freader = new DbFieldReader(this, reader);
                while (reader.Read())
                {
                    GetValue<T>(fnProjector, result, freader);
                }
            }
            return result.AsEnumerable();
        }

        // This method will fix the .NET Native error that occurs when an app is compiled using .NET Native
        private static void GetValue<T>(Func<FieldReader, T> fnProjector, ICollection<T> result, DbFieldReader freader)
        {
            result.Add(fnProjector(freader));
        }

        public override IEnumerable<int> ExecuteBatch(QueryCommand query, IEnumerable<object[]> paramSets, int batchSize, bool stream)
        {
            if (query == null) throw new ArgumentNullException("query");
            if (paramSets == null) throw new ArgumentNullException("paramSets");
            bool defer = stream && _provider.ExecutingTransaction();
            return _provider.TransactionF((cmd) => Wrap(() => ExecuteBatch(cmd, query, paramSets), defer));
        }

        private IEnumerable<int> ExecuteBatch(DbCommand cmd, QueryCommand query, IEnumerable<object[]> paramSets)
        {
            SetCommand(cmd, query, null, false);
            foreach (var paramValues in paramSets)
            {
                SetCommand(cmd, query, paramValues, true);
                _rowsAffected = cmd.ExecuteNonQuery();
                yield return _rowsAffected;
            }
        }

        public override IEnumerable<T> ExecuteBatch<T>(QueryCommand query, IEnumerable<object[]> paramSets, Func<FieldReader, T> fnProjector, int batchSize, bool stream)
        {
            if (query == null) throw new ArgumentNullException("query");
            if (paramSets == null) throw new ArgumentNullException("paramSets");
            if (fnProjector == null) throw new ArgumentNullException("fnProjector");
            bool defer = stream && _provider.Executing();
            return _provider.CommandF((cmd) => Wrap(() => ExecuteBatch(cmd, query, paramSets, fnProjector), defer));
        }

        private IEnumerable<T> ExecuteBatch<T>(DbCommand cmd, QueryCommand query, IEnumerable<object[]> paramSets, Func<FieldReader, T> fnProjector)
        {
            SetCommand(cmd, query, null, false);
            foreach (var paramValues in paramSets)
            {
                SetCommand(cmd, query, paramValues, true);
                using (DbDataReader reader = cmd.ExecuteReader())
                {
                    var fReader = new DbFieldReader(this, reader);
                    if (reader.Read())
                    {
                        yield return fnProjector(fReader);
                    }
                    else
                    {
                        yield return default(T);
                    }
                }
            }
        }

        public override IEnumerable<T> ExecuteDeferred<T>(QueryCommand query, Func<FieldReader, T> fnProjector, object[] paramValues)
        {
            if (query == null) throw new ArgumentNullException("query");
            if (fnProjector == null) throw new ArgumentNullException("fnProjector");
            if (_provider.Executing()) throw new InvalidOperationException();
            return _provider.CommandDeferred((cmd) => ExecuteDeferred(cmd, query, fnProjector, paramValues));
        }

        private IEnumerable<T> ExecuteDeferred<T>(DbCommand cmd, QueryCommand query, Func<FieldReader, T> fnProjector, object[] paramValues)
        {
            SetCommand(cmd, query, paramValues, false);
            using (DbDataReader reader = cmd.ExecuteReader())
            {
                var fReader = new DbFieldReader(this, reader);
                while (reader.Read())
                {
                    yield return fnProjector(fReader);
                }
            }
        }

        public override int ExecuteCommand(QueryCommand query, object[] paramValues)
        {
            if (query == null) throw new ArgumentNullException("query");
            _rowsAffected = _provider.TransactionF((cmd) => ExecuteCommand(cmd, query, paramValues));
            return _rowsAffected;
        }

        private static int ExecuteCommand(DbCommand cmd, QueryCommand query, object[] paramValues)
        {
            SetCommand(cmd, query, paramValues, false);
            return cmd.ExecuteNonQuery();
        }

        private static void SetCommand(DbCommand cmd, QueryCommand query, object[] paramValues, bool reuseParameters)
        {
            if (!reuseParameters)
            {
                cmd.CommandText = query.CommandText;
            }
            int n = query.Parameters.Count;
            if (n > 0)
            {
                if (reuseParameters)
                {
                    if (paramValues != null)
                    {
                        for (int i = 0; i < n; i++)
                        {
                            cmd.Parameters[i].Value = paramValues[i];
                        }
                    }
                }
                else
                {
                    cmd.Parameters.Clear();
                    for (int i = 0; i < n; i++)
                    {
                        DbParameter p = cmd.CreateParameter(query.Parameters[i].ValueName, query.Parameters[i].ValueType, i);
                        if (paramValues != null)
                        {
                            p.Value = paramValues[i];
                        }
                        cmd.Parameters.Add(p);
                    }
                }
            }
        }

        private static IEnumerable<T> Wrap<T>(Func<IEnumerable<T>> func, bool delayExecution)
        {
            IEnumerable<T> result = func();
            if (delayExecution)
            {
                return new EnumerateOnce<T>(result);  // executes when the collection is enumerated
            }
            return result.ToArray(); // executes now
        }

        protected class DbFieldReader : FieldReader
        {
            private QueryExecutor executor;
            private DbDataReader reader;

            public DbFieldReader(QueryExecutor executor, DbDataReader reader)
            {
                this.executor = executor;
                this.reader = reader;
                this.Init();
            }

            protected override int FieldCount
            {
                get { return this.reader.FieldCount; }
            }

            protected override Type GetFieldType(int ordinal)
            {
                return this.reader.GetFieldType(ordinal);
            }

            protected override bool IsDBNull(int ordinal)
            {
                return this.reader.IsDBNull(ordinal);
            }

            protected override T GetValue<T>(int ordinal)
            {
                return (T)this.executor.Convert(this.reader.GetValue(ordinal), typeof(T));
            }

            protected override DateTime GetDateTime(int ordinal)
            {
                return this.reader.GetDateTime(ordinal);
            }

            protected override Decimal GetDecimal(int ordinal)
            {
                return this.reader.GetDecimal(ordinal);
            }

            protected override Double GetDouble(int ordinal)
            {
                return this.reader.GetDouble(ordinal);
            }

            protected override Guid GetGuid(int ordinal)
            {
                return this.reader.GetGuid(ordinal);
            }

            protected override Int16 GetInt16(int ordinal)
            {
                return this.reader.GetInt16(ordinal);
            }

            protected override Int32 GetInt32(int ordinal)
            {
                return this.reader.GetInt32(ordinal);
            }

            protected override Int64 GetInt64(int ordinal)
            {
                return this.reader.GetInt64(ordinal);
            }

            protected override String GetString(int ordinal)
            {
                return this.reader.GetString(ordinal);
            }
        }
    }
}
