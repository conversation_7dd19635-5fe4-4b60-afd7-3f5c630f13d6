﻿using System;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Prism.Events;
using Unity.Attributes;

namespace Ifs.Uma.Framework.UI.Lists
{
    public class CommandExecutedItemEventArgs : EventArgs
    {
        public object Item { get; private set; }

        public CommandExecutedItemEventArgs(object item)
        {
            Item = item;
        }
    }

    public abstract class CardListData<T> : ListData<T> where T : class
    {
        private CardDef _itemCardDef;
        public event EventHandler<CommandExecutedItemEventArgs> CommmandExecutedEvent;

        public CardListData([OptionalDependency] IEventAggregator eventAggregator)
            : base(eventAggregator)
        {
        }

        public CardDef ItemCardDef
        {
            get { return _itemCardDef; }
            set
            {
                if (_itemCardDef != value)
                {
                    _itemCardDef = value;
                    OnPropertyChanged(() => ItemCardDef);
                    OnItemCardDefValueChanged();
                }
            }
        }

        public virtual void OnItemCardDefValueChanged() { }

        public virtual CardDef GetCardDefForItem(T item)
        {
            return ItemCardDef;
        }

        protected override bool OnMatchSearchTerm(T item)
        {
            return ItemCardDef.MatchFilter(SearchTerm, item);
        }

        public void CommandExecuted(ListElementItem item)
        {
            CommmandExecutedEvent?.Invoke(this, new CommandExecutedItemEventArgs(item));
        }
    }
}
