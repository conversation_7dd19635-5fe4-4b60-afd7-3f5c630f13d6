﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Date
{ 
    internal sealed class DateTimeTimestamp : DateTimeFunction
    {
        public const string FunctionName = "Timestamp";

        public DateTimeTimestamp()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime now = DateTime.Now;
            // Strip milliseconds
            return new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second, now.Kind);
        }
    }

    internal sealed class DateTimeTimestampUtc : DateTimeFunction
    {
        public const string FunctionName = "TimestampUtc";

        public DateTimeTimestampUtc()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime now = DateTime.SpecifyKind(DateTime.Now.ToClientUniversalTime(), DateTimeKind.Utc);

            // Strip milliseconds
            return new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second, now.Kind);
        }
    }

    internal sealed class DateTimeTimestamp6 : DateTimeFunction
    {
        public DateTimeTimestamp6()
            : base(DateTimeTimestamp.FunctionName, 6)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            long? year = parameters[0].GetInteger();
            long? month = parameters[1].GetInteger();
            long? day = parameters[2].GetInteger();
            long? hours = parameters[3].GetInteger();
            long? mins = parameters[4].GetInteger();
            long? secs = parameters[5].GetInteger();

            if (year.HasValue && month.HasValue && day.HasValue && hours.HasValue && mins.HasValue && secs.HasValue)
            {
                return new DateTime((int)year.Value, (int)month.Value, (int)day.Value, (int)hours.Value, (int)mins.Value, (int)secs.Value);
            }
            else
            {
                return null;
            }
        }
    }
}
