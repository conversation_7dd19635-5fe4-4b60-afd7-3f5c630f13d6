﻿using Ifs.Uma.AppData;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Elements.Markdown;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Ifs.Uma.Framework.UI.Elements
{
    public sealed class OfflineWarningElement : MarkdownElementBase
    {
        protected override BindingType BindingPropertyType => BindingType.None;

        private IReadOnlyList<CpiCommandGroup> _commandGroups;
        public IReadOnlyList<CpiCommandGroup> CommandGroups
        {
            get => _commandGroups;
            set => SetProperty(ref _commandGroups, value);
        }

        private IReadOnlyList<CpiElementContent> _elementsContent;
        public IReadOnlyList<CpiElementContent> ElementContent
        {
            get => _elementsContent;
            set => SetProperty(ref _elementsContent, value);
        }        
                
        private readonly IOfflineChecker _offlineChecker;
        private bool _showMessage;

        public OfflineWarningElement(<PERSON>Off<PERSON><PERSON>he<PERSON> offlineChecker)
        {
            _offlineChecker = offlineChecker;
            AlwaysLoad = true;
        }   
        
        protected override bool OnLoad()
        {
            Markdown.Color = UmaColors.Warning;
            Markdown.Text = Strings.DeviceOfflineFeaturesUnavailable;
            return true;
        }

        protected override void OnDataSourceChanged()
        {
            base.OnDataSourceChanged();

            Task update = UpdateVisibilityAsync();
            PageData.BackgroundTasks.Add(update);
        }

        private async Task UpdateVisibilityAsync()
        {
            _showMessage = await _offlineChecker.WillFailAsOffline(PageData?.DataSource, CommandGroups, ElementContent);

            DataChanged();
        }

        protected override bool CalculateIsVisible()
        {
            return _showMessage;
        }
    }
}
