﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Database;

namespace Ifs.Uma.Data
{
    public interface IMemberAccessor
    {
        bool TryGetValue(IMetaDataMember member, out object value);
    }

    public sealed class DictionaryMemberAccessor : IMemberAccessor
    {
        private readonly IReadOnlyDictionary<string, object> _data;

        public DictionaryMemberAccessor(IReadOnlyDictionary<string, object> data)
        {
            _data = data;
        }

        public bool TryGetValue(IMetaDataMember member, out object value)
        {
            return _data.TryGetValue(member.ColumnName, out value);
        }
    }

    public sealed class RowMemberAccessor : IMemberAccessor
    {
        private readonly IMetaTable _table;
        private readonly object _row;

        public RowMemberAccessor(IMetaTable table, object row)
        {
            _table = table;
            _row = row;
        }

        public bool TryGetValue(IMetaDataMember member, out object value)
        {
            value = member.GetValue(_row);
            return true;
        }
    }
}
