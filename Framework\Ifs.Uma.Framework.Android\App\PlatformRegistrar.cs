using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Reporting;
using Ifs.Uma.Framework.Services;
using Ifs.Uma.Framework.UI.Assistants;
using Ifs.Uma.Framework.UI.Dialogs;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Scanning.Services;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Helpers;
using Ifs.Uma.Utility;
using Unity;
using Unity.Lifetime;

namespace Ifs.Uma.Framework.App
{
    public class PlatformRegistrar : Registrar
    {
        protected override void OnApplyInitializedRegistrations(IUnityContainer container)
        {
            base.OnApplyInitializedRegistrations(container);

            container.RegisterType<ILookupService, LookupService>(new ContainerControlledLifetimeManager());
            container.RegisterType<IElementDialogService, ElementDialogService>(new ContainerControlledLifetimeManager());
            container.RegisterType<IAssistantDialogService, AssistantDialogService>(new ContainerControlledLifetimeManager());
            container.RegisterType<IBugReporter, BugReporter>(new ContainerControlledLifetimeManager());
            container.RegisterType<IFailedTransitionHandler, FailedTransitionHandler>(new ContainerControlledLifetimeManager());
            container.RegisterType<IBarcodeService, ScanditBarcodeService>(new ContainerControlledLifetimeManager());
            container.RegisterType<IScreenshotMode, ScreenshotMode>(new ContainerControlledLifetimeManager());
            container.RegisterType<IDataTransferService, DataTransferService>(new ContainerControlledLifetimeManager());
            container.RegisterType<IPdfGenerator, PdfGenerator>(new ContainerControlledLifetimeManager());
            container.RegisterService<IPushNotificationService, PushNotificationService>();
            container.RegisterLocationService<LocationService>();

#if REMOTE_ASSISTANCE
            container.RegisterType<RemoteAssistance.IRemoteAssistanceDialogManager, Uma.RemoteAssistance.RemoteAssistanceDialogManager>(new ContainerControlledLifetimeManager());
#endif
        }
    }
}
