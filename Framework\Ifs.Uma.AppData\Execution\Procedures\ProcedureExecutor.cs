﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Execution.Procedures.Functions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.AppData.Execution.Procedures
{
    public interface IProcedureExecutor
    {
        Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> AfterEntityPrepareAsync(string projectionName, RemoteRow row, CancellationToken cancelToken);
        Task<ExecuteResult> EntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> AfterEntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken);
        Task<ExecuteResult> EntityUpdateAsync(string projectionName, RemoteRow row, IEnumerable<string> changedMembers, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> AfterEntityUpdateAsync(string projectionName, RemoteRow row, CancellationToken cancelToken);
        Task<ExecuteResult> EntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> AfterEntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken);
        Task<ExecuteResult> CallFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> AfterCallFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, object result, CancellationToken cancelToken);
        Task<ExecuteResult> CallBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken);
        Task<ExecuteResult> AfterCallBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, object result, CancellationToken cancelToken);
        Task<ExecuteResult> CallActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> AfterCallActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, object result, CancellationToken cancelToken);
        Task<ExecuteResult> CallBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken));
        Task<ExecuteResult> AfterCallBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, object result, CancellationToken cancelToken);
        Task<ExecuteResult> CallEventAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken = default(CancellationToken));
    }

    public sealed class ProcedureExecutor : Executor<ProcedureContext>, IProcedureExecutor
    {
        public const string RecordVarName = "Record";
        public const string ResultVarName = "Result";
        public const string EntitySetVariableName = "FWEntitySetName";

        private readonly IMetadata _metadata;
        private readonly IDataContextProvider _db;
        private readonly IPerfLogger _perfLogger;
        private readonly IEventAggregator _eventAggregator;
        private readonly ICachePreparer _cache;
        private readonly FwFunctions _fwFunctions;

        public ProcedureExecutor(IMetadata metadata, IDataContextProvider db, ILogger logger, IPerfLogger perfLogger,
            IEventAggregator eventAggregator, IExpressionRunner expressionRunner, ICachePreparer cache, ILocationLogger locationLogger, IOnlineDataHandler onlineDataHandler, IMediaHandler mediaHandler)
            : base(logger, expressionRunner)
        {
            _metadata = metadata;
            _db = db;
            _perfLogger = perfLogger;
            _eventAggregator = eventAggregator;
            _cache = cache;
            _fwFunctions = new FwFunctions(logger, locationLogger, onlineDataHandler, mediaHandler);
        }

        #region IProcedureExecutor

        public Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken)
        {
            if (entityName == null)
                throw new ArgumentNullException(nameof(entityName));

            string procName = ProcedureType.EntityPrepare.GetFullProcedureName(entityName);
            return WithContextTransactionAsync(projectionName, procName, ctx =>
            {
                RemoteRow row = CreateAndPrepareRow(ctx, entityName);
                return new ExecuteResult(row);
            }, cancelToken);
        }

        public Task<ExecuteResult> AfterEntityPrepareAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            string entityName = RemoteNaming.ToEntityName(row.TableName);
            string procName = ProcedureType.EntityPrepare.GetFullProcedureName(entityName);
            CpiProc proc = _metadata.FindProcedure(projectionName, procName);

            if (proc == null)
            {
                return Task.FromResult(new ExecuteResult(row));
            }

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            row = ExecutionUtils.CloneRow(_metadata, row);
            parameters[ResultVarName] = row;

            return WithContextAsync(projectionName, procName, ctx => CallProcedure(ctx, proc, parameters, false), cancelToken);
        }

        public Task<ExecuteResult> EntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken)
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            if (row.RowId != 0)
                throw new ArgumentException("Record has already been inserted", nameof(row));

            string entityName = RemoteNaming.ToEntityName(row.TableName);
            string procName = ProcedureType.EntityInsert.GetFullProcedureName(entityName);

            Dictionary<string, object> parameters = PrepareParameters(row, out row);

            return WithContextTransactionAsync(projectionName, procName, ctx =>
            {
                ValidateEntityAccess(ctx, entityName);

                SetClientGeneratedKeys(ctx, row);

                CpiProc proc = _metadata.FindProcedure(projectionName, procName);

                if (proc == null)
                {
                    proc = DefaultProcedures.CreateSaveProc(entityName, true);
                }

                ExecuteResult result = CallProcedure(ctx, proc, parameters, false);

                if (!result.Failed)
                {
                    ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, row);
                    RemoteRow insertedRow = key == null ? null : (RemoteRow)ctx.DbDataContext.Find(key);
                    return new ExecuteResult(insertedRow);
                }

                return result;
            }, cancelToken);
        }

        public Task<ExecuteResult> AfterEntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            string entityName = RemoteNaming.ToEntityName(row.TableName);
            string procName = ProcedureType.EntityInsert.GetFullProcedureName(entityName);
            CpiProc proc = _metadata.FindProcedure(projectionName, procName);

            if (proc == null)
            {
                return Task.FromResult(new ExecuteResult(row));
            }

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            row = ExecutionUtils.CloneRow(_metadata, row);
            parameters[ResultVarName] = row;

            return WithContextAsync(projectionName, procName, ctx => CallProcedure(ctx, proc, parameters, false), cancelToken);
        }

        public Task<ExecuteResult> EntityUpdateAsync(string projectionName, RemoteRow row, IEnumerable<string> changedMembers, CancellationToken cancelToken)
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            if (changedMembers == null)
                throw new ArgumentNullException(nameof(changedMembers));

            if (row.RowId == 0)
                throw new ArgumentException("Record has not yet been inserted", nameof(row));

            string entityName = RemoteNaming.ToEntityName(row.TableName);
            string procName = ProcedureType.EntityUpdate.GetFullProcedureName(entityName);

            Dictionary<string, object> parameters = PrepareParameters(row, out row);

            return WithContextTransactionAsync(projectionName, procName, ctx =>
            {
                ValidateEntityAccess(ctx, entityName);

                IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                ITable<RemoteRow> table = metaTable == null ? null : ctx.DbDataContext.GetTable(metaTable) as ITable<RemoteRow>;
                if (table == null)
                    throw Fail(ctx, $"Invalid record table '{row.TableName}' when updating");

                // Only want to update the columns that have been changed. Get the existing row out of the 
                // database that may have changed during backgrond sync and apply any changes to it
                RemoteRow rowToUpdate = LoadExistingRecord(ctx, table, row);
                if (rowToUpdate == null)
                    throw Fail(ctx, "Missing entity record to update");

                parameters[RecordVarName] = rowToUpdate;

                foreach (string member in changedMembers)
                {
                    rowToUpdate[member] = row[member];
                }

                CpiProc proc = _metadata.FindProcedure(projectionName, procName);

                if (proc == null)
                {
                    proc = DefaultProcedures.CreateSaveProc(entityName, false);
                }

                ExecuteResult result = CallProcedure(ctx, proc, parameters, false);

                if (!result.Failed)
                {
                    ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, rowToUpdate);
                    RemoteRow updatedRow = key == null ? null : (RemoteRow)ctx.DbDataContext.Find(key);

                    if (updatedRow != null)
                    {
                        updatedRow.PrimaryKeyString = key?.ToFormattedKeyRef(projectionName, true);
                    }

                    return new ExecuteResult(updatedRow);
                }

                return result;
            }, cancelToken);
        }

        public Task<ExecuteResult> AfterEntityUpdateAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            string entityName = RemoteNaming.ToEntityName(row.TableName);
            string procName = ProcedureType.EntityUpdate.GetFullProcedureName(entityName);
            CpiProc proc = _metadata.FindProcedure(projectionName, procName);

            if (proc == null)
            {
                return Task.FromResult(new ExecuteResult(row));
            }

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            row = ExecutionUtils.CloneRow(_metadata, row);
            parameters[ResultVarName] = row;

            return WithContextAsync(projectionName, procName, ctx => CallProcedure(ctx, proc, parameters, false), cancelToken);
        }

        public Task<ExecuteResult> EntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken)
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            string entityName = RemoteNaming.ToEntityName(row.TableName);
            string procName = ProcedureType.EntityDelete.GetFullProcedureName(entityName);

            Dictionary<string, object> parameters = PrepareParameters(row, out row);

            return WithContextTransactionAsync(projectionName, procName, ctx =>
            {
                ValidateEntityAccess(ctx, entityName);

                IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                ITable<RemoteRow> table = metaTable == null ? null : ctx.DbDataContext.GetTable(metaTable) as ITable<RemoteRow>;
                if (table == null)
                    throw Fail(ctx, $"Invalid record table '{row.TableName}' when deleting");

                // The row that has been passed in may not have all column values so do a select 
                // before the procedure to get them all
                RemoteRow rowToDelete = LoadExistingRecord(ctx, table, row);
                if (rowToDelete == null)
                    throw Fail(ctx, "Missing entity record to delete");

                parameters[RecordVarName] = rowToDelete;
                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, rowToDelete);
                rowToDelete.PrimaryKeyString = key == null ? null : key.ToFormattedKeyRef(projectionName, true);

                CpiProc proc = _metadata.FindProcedure(projectionName, procName);

                if (proc == null)
                {
                    proc = DefaultProcedures.CreateDeleteProc(entityName);
                }

                return CallProcedure(ctx, proc, parameters, false);
            }, cancelToken);
        }

        public Task<ExecuteResult> AfterEntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            string entityName = RemoteNaming.ToEntityName(row.TableName);
            string procName = ProcedureType.EntityDelete.GetFullProcedureName(entityName);
            CpiProc proc = _metadata.FindProcedure(projectionName, procName);

            if (proc == null)
            {
                return Task.FromResult(ExecuteResult.None);
            }

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            row = ExecutionUtils.CloneRow(_metadata, row);
            parameters[RecordVarName] = row;

            return WithContextAsync(projectionName, procName, ctx => CallProcedure(ctx, proc, parameters, false), cancelToken);
        }

        public Task<ExecuteResult> CallFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string procName = ProcedureType.Function.GetFullProcedureName(name);

            Dictionary<string, object> clonedParameters = ExecutionUtils.CloneParameters(_metadata, parameters);
            return WithContextAsync(projectionName, procName, ctx => CallProcedure(ctx, procName, clonedParameters, false), cancelToken);
        }

        public Task<ExecuteResult> AfterCallFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, object result, CancellationToken cancelToken = default(CancellationToken))
        {
            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string procName = ProcedureType.Function.GetFullProcedureName(name);
            return AfterCallAsync(projectionName, procName, parameters, null, result, cancelToken);
        }

        public Task<ExecuteResult> CallBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string functionName = $"{RemoteNaming.ToEntityName(row.TableName)}.{name}";
            string procName = ProcedureType.Function.GetFullProcedureName(functionName);

            row = ExecutionUtils.CloneRow(_metadata, row);
            Dictionary<string, object> clonedParameters = ExecutionUtils.CloneParameters(_metadata, parameters);

            return WithContextAsync(projectionName, procName, ctx =>
            {
                IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                ITable<RemoteRow> table = metaTable == null ? null : ctx.DbDataContext.GetTable(metaTable) as ITable<RemoteRow>;
                if (table == null)
                    throw Fail(ctx, $"Invalid record table '{row.TableName}' when performing bound function");

                // The row that has been passed in may not have all column values so do a select 
                // before the procedure to get them all
                RemoteRow rowToPerformOn = LoadExistingRecord(ctx, table, row);
                if (rowToPerformOn == null)
                    throw Fail(ctx, "Record not found in local database. Unable to perform bound function.");

                clonedParameters[RecordVarName] = rowToPerformOn;

                return CallProcedure(ctx, procName, clonedParameters, false);
            }, cancelToken);
        }

        public Task<ExecuteResult> AfterCallBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, object result, CancellationToken cancelToken)
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string procName = ProcedureType.Function.GetFullProcedureName($"{RemoteNaming.ToEntityName(row.TableName)}.{name}");
            return AfterCallAsync(projectionName, procName, parameters, row, result, cancelToken);
        }

        public Task<ExecuteResult> CallActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string procName = ProcedureType.Action.GetFullProcedureName(name);

            Dictionary<string, object> clonedParameters = ExecutionUtils.CloneParameters(_metadata, parameters);

            return WithContextTransactionAsync(projectionName, procName, ctx =>
            {
                CpiProc proc = _metadata.FindProcedure(projectionName, procName);

                if (proc == null)
                {
                    proc = DefaultProcedures.CreatePerformActionProc(name, clonedParameters);
                }

                return CallProcedure(ctx, proc, clonedParameters, false);
            }, cancelToken);
        }

        public Task<ExecuteResult> AfterCallActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, object result, CancellationToken cancelToken = default(CancellationToken))
        {
            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string procName = ProcedureType.Action.GetFullProcedureName(name);
            return AfterCallAsync(projectionName, procName, parameters, null, result, cancelToken);
        }

        public Task<ExecuteResult> CallBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string actionName = $"{RemoteNaming.ToEntityName(row.TableName)}.{name}";
            string procName = ProcedureType.Action.GetFullProcedureName(actionName);

            row = ExecutionUtils.CloneRow(_metadata, row);
            Dictionary<string, object> clonedParameters = ExecutionUtils.CloneParameters(_metadata, parameters);

            return WithContextTransactionAsync(projectionName, procName, ctx =>
            {
                IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                ITable<RemoteRow> table = metaTable == null ? null : ctx.DbDataContext.GetTable(metaTable) as ITable<RemoteRow>;
                if (table == null)
                    throw Fail(ctx, $"Invalid record table '{row.TableName}' when performing bound action");

                // The row that has been passed in may not have all column values so do a select 
                // before the procedure to get them all
                RemoteRow rowToPerformOn = LoadExistingRecord(ctx, table, row);
                if (rowToPerformOn == null)
                    throw Fail(ctx, "Record not found in local database. Unable to perform bound action.");

                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, rowToPerformOn);

                clonedParameters[RecordVarName] = rowToPerformOn;

                CpiProc proc = _metadata.FindProcedure(projectionName, procName);

                if (proc == null)
                {
                    proc = DefaultProcedures.CreatePerformActionProc(actionName, clonedParameters);
                }

                return CallProcedure(ctx, proc, clonedParameters, false);
            }, cancelToken);
        }

        public Task<ExecuteResult> AfterCallBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, object result, CancellationToken cancelToken = default(CancellationToken))
        {
            if (row == null)
                throw new ArgumentNullException(nameof(row));

            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string procName = ProcedureType.Action.GetFullProcedureName($"{RemoteNaming.ToEntityName(row.TableName)}.{name}");
            return AfterCallAsync(projectionName, procName, parameters, row, result, cancelToken);
        }

        private Task<ExecuteResult> AfterCallAsync(string projectionName, string procName, IReadOnlyDictionary<string, object> parameters, RemoteRow row, object result, CancellationToken cancelToken)
        {
            CpiProc proc = _metadata.FindProcedure(projectionName, procName);

            if (proc == null)
            {
                return Task.FromResult(new ExecuteResult(result));
            }

            Dictionary<string, object> clonedParameters = row == null ?
                ExecutionUtils.CloneParameters(_metadata, parameters, proc, result) : ExecutionUtils.CloneParameters(_metadata, parameters, proc, row, result);

            Func<ProcedureContext, ExecuteResult> callProc = ctx => CallProcedure(ctx, proc, clonedParameters, false);

            return WithContextAsync(projectionName, procName, callProc, cancelToken);
        }

        public Task<ExecuteResult> CallEventAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            if (name == null)
                throw new ArgumentNullException(nameof(name));

            string procName = ProcedureType.Event.GetFullProcedureName(name);

            Dictionary<string, object> clonedParameters = ExecutionUtils.CloneParameters(_metadata, parameters);

            CpiProc proc = _metadata.FindProcedure(projectionName, procName);

            if (proc == null)
            {
                return Task.FromResult(ExecuteResult.None);
            }

            return WithContextAsync(projectionName, procName, ctx => CallProcedure(ctx, proc, clonedParameters, false), cancelToken);
        }

        #endregion

        private async Task<ExecuteResult> WithContextAsync(string projectionName, string procName, Func<ProcedureContext, ExecuteResult> action, CancellationToken cancelToken)
        {
            try
            {
                PrepareCacheResult prepareResult = await _cache.PrepareCacheAsync(_metadata, projectionName, procName, cancelToken);

                if (prepareResult == PrepareCacheResult.Offline)
                {
                    return ExecuteResult.Offline;
                }
            }
            catch (Exception ex)
            {
                return new ExecuteResult(ex);
            }

            return await Task.Run(() =>
            {
                Stopwatch sw = Stopwatch.StartNew();

                ExecuteResult innerResult = ExecuteResult.None;
                DataChangeSet changeSet = null;

                try
                {
                    ProcedureContext ctx = new ProcedureContext(projectionName, _metadata, ExpressionRunner, _db, cancelToken);
                    changeSet = ctx.CommittedDataChangeSet;

                    innerResult = action(ctx);

                    cancelToken.ThrowIfCancellationRequested();
                }
                catch (Exception ex)
                {
                    if (ex is ProcedureErrorException pex)
                    {
                        Logger.Error(procName + ": " + pex.Message);
                    }
                    else if (ex is OperationCanceledException)
                    {
                        Logger.Trace($"Procedure cancelled ({procName})");
                    }
                    else
                    {
                        Logger.HandleException(ExceptionType.Unexpected, ex);
                    }

                    innerResult = new ExecuteResult(ex);
                }

                if (changeSet != null)
                {
                    FireDataChangeEvent(changeSet);
                }

                sw.Stop();

                _perfLogger?.Log("Procedures", procName, (int)sw.ElapsedMilliseconds);
                Logger?.Trace("{0}: {1} {2}ms", "Procedures", procName, ((int)sw.ElapsedMilliseconds).ToString());

                return innerResult;
            });
        }

        private Task<ExecuteResult> WithContextTransactionAsync(string projectionName, string procName, Func<ProcedureContext, ExecuteResult> action, CancellationToken cancelToken)
        {
            Func<ProcedureContext, ExecuteResult> transactionWrapper = ctx =>
            {
                ExecuteResult result = ExecuteResult.None;

                ctx.WithTransaction(() =>
                {
                    result = action(ctx);

                    cancelToken.ThrowIfCancellationRequested();
                });

                return result;
            };

            return WithContextAsync(projectionName, procName, transactionWrapper, cancelToken);
        }

        private RemoteRow LoadExistingRecord(ProcedureContext ctx, ITable<RemoteRow> table, RemoteRow row)
        {
            string entitySetName = row.EntitySetName;
            string arraySource = row.ArraySourceName;
            string primaryKeyString = row.PrimaryKeyString;

            RemoteRow tempRow = null;

            TableImplementation impl = table.Entity.TableImplementation;
            if (impl == TableImplementation.Table)
            {
                tempRow = table.FirstOrDefault(x => x.RowId == row.RowId);
            }
            else if (impl == TableImplementation.View)
            {
                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, row);
                if (key != null)
                {
                    tempRow = ctx.DbDataContext.Find(key) as RemoteRow;
                }
            }

            if (tempRow != null)
            {
                tempRow.EntitySetName = entitySetName;
                tempRow.ArraySourceName = arraySource;
                tempRow.PrimaryKeyString = primaryKeyString;

                if (string.IsNullOrEmpty(primaryKeyString))
                {
                    tempRow.PrimaryKeyString = row.GetPrimaryKeysString(_metadata.MetaModel, ctx.ProjectionName);
                }
            }

            return tempRow;
        }

        private void FireDataChangeEvent(DataChangeSet changeSet)
        {
            if (!changeSet.IsEmpty)
            {
                DataChangedEventArgs args = new DataChangedEventArgs(changeSet, DataChangedCauses.Procedure);
                _eventAggregator.GetEvent<DataChangedEvent>().Publish(args);
            }
        }

        private ExecuteResult CallProcedure(ProcedureContext context, string name, IReadOnlyDictionary<string, object> parameters, bool cloneParams)
        {
            CpiProc proc = _metadata.FindProcedure(context.ProjectionName, name);
            if (proc == null)
                throw Fail(context, $"Failed to find procedure '{name}'");

            return CallProcedure(context, proc, parameters, cloneParams);
        }

        private ExecuteResult CallProcedure(ProcedureContext context, CpiProc proc, IReadOnlyDictionary<string, object> parameters, bool cloneParams)
        {
            if (proc == null)
                throw new ArgumentNullException(nameof(proc));

            if (cloneParams)
            {
                parameters = ExecutionUtils.CloneParameters(_metadata, parameters);
            }

            ProcedureContext subContext = context.CreateSubContext(proc, parameters);

            ExecuteResult ret = ExecuteResult.None;

            if (MustRunInTransaction(proc))
            {
                subContext.WithTransaction(() =>
                {
                    ret = Execute(subContext, subContext.Layer?.Execute);

                    subContext.CancelToken.ThrowIfCancellationRequested();
                });
            }
            else
            {
                ret = Execute(subContext, subContext.Layer?.Execute);
            }

            return ret.Returning ? new ExecuteResult(ret.Value) : ExecuteResult.None;
        }

        private bool MustRunInTransaction(CpiProc proc)
        {
            return proc.Type == ProcedureType.EntityPrepare ||
                   proc.Type == ProcedureType.EntityInsert ||
                   proc.Type == ProcedureType.EntityUpdate ||
                   proc.Type == ProcedureType.EntityDelete ||
                   proc.Type == ProcedureType.Action;
        }

        private ExecuteResult Execute(ProcedureContext context, CpiExecute[] executions)
        {
            ExecuteResult ret = ExecuteResult.None;

            if (executions != null)
            {
                foreach (CpiExecute execute in executions)
                {
                    ret = Execute(context, execute);

                    if (ret.Returning)
                    {
                        return ret;
                    }
                }
            }

            return ret;
        }

        private ExecuteResult Execute(ProcedureContext context, CpiExecute execute)
        {
            ExecuteResult ret = ExecuteResult.None;

            if (execute.Call != null)
            {
                ret = ExecuteCall(context, execute, execute.Call.Method, execute.Call.Args);
            }

            bool isLoop = execute.Call != null && (execute.Call.Method == CpiExecuteCallMethod.For || execute.Call.Method == CpiExecuteCallMethod.While);
            if (!string.IsNullOrEmpty(execute.Assign) && !isLoop)
            {
                context.Assign(execute.Assign, ret.Value);
            }

            if (ret.Returning)
            {
                return ret;
            }

            if (execute.Result != null && ret.Value is string)
            {
                string resultCode = (string)ret.Value;

                CpiExecute[] resultExecutions;
                if (execute.Result.TryGetValue(resultCode, out resultExecutions) && resultExecutions != null)
                {
                    ExecuteResult subRet = Execute(context, resultExecutions);

                    if (subRet.Returning)
                    {
                        return subRet;
                    }
                }
            }

            return ret;
        }

        private ExecuteResult ExecuteCall(ProcedureContext context, CpiExecute exec, CpiExecuteCallMethod method, CpiExecuteCallArgs args)
        {
            context.CancelToken.ThrowIfCancellationRequested();

            switch (method)
            {
                case CpiExecuteCallMethod.Proc:
                    return ExecuteProc(context, (CpiProcCallArgs)args);
                case CpiExecuteCallMethod.Super:
                    return ExecuteSuper(context, (CpiSuperCallArgs)args);
                case CpiExecuteCallMethod.Error:
                    return ExecuteError(context, (CpiErrorCallArgs)args);
                case CpiExecuteCallMethod.Save:
                    return ExecuteSave(context, (CpiSaveCallArgs)args);
                case CpiExecuteCallMethod.Delete:
                    return ExecuteDelete(context, (CpiDeleteCallArgs)args);
                case CpiExecuteCallMethod.PerformAction:
                    return ExecutePerformAction(context);
                case CpiExecuteCallMethod.Create:
                    return ExecuteCreate(context, (CpiCreateCallArgs)args);
                case CpiExecuteCallMethod.Fetch:
                    return ExecuteFetch(context, (CpiFetchCallArgs)args);
                case CpiExecuteCallMethod.For:
                    return ExecuteFor(context, exec, (CpiForCallArgs)args);
                case CpiExecuteCallMethod.While:
                    return ExecuteWhile(context, exec, (CpiWhileCallArgs)args);
                case CpiExecuteCallMethod.Count:
                    return ExecuteCount(context, (CpiCountCallArgs)args);
                case CpiExecuteCallMethod.Log:
                    return ExecuteLog(context, (CpiLogCallArgs)args);
                case CpiExecuteCallMethod.Debug:
                    return ExecuteDebug(context, (CpiLogCallArgs)args);
                case CpiExecuteCallMethod.Trace:
                    return ExecuteTrace(context, (CpiLogCallArgs)args);
                case CpiExecuteCallMethod.CopyCustomFields:
                    return ExecuteCopyCustomFields(context, (CpiCopyCustomFieldsCallArgs)args);
                default:
                    return ExecuteCommonCall(context, method, args);
            }
        }

        private ExecuteResult ExecuteSave(ProcedureContext context, CpiSaveCallArgs args)
        {
            FwDataContext ctx = context.DbDataContext;

            string entitySetName = null;
            if (context.Vars.TryGetValue(EntitySetVariableName, out VariableStorage var))
            {
                entitySetName = var?.Value?.ToString();
            }

            object value = context.GetValue(args.Name);
            RemoteRow row = value as RemoteRow;
            if (row == null)
                throw Fail(context, $"Invalid record '{args.Name}' when saving");

            IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
            ITable<RemoteRow> table = metaTable == null ? null : ctx.GetTable(metaTable) as ITable<RemoteRow>;
            if (table == null)
                throw Fail(context, $"Invalid record '{args.Name}' table '{row.TableName}' when saving");

            ValidateChangeAccess(context, RemoteNaming.ToEntityName(row.TableName));

            bool isEvent = context.ProcedureName == "SynchronizationEnded" || context.ProcedureName == "TransactionFailed";
            string sessionId = isEvent ? string.Empty : FndTransactionSession.GetCurrentOpenSession(ctx);

            if (row.RowId == 0)
            {
                if (!string.IsNullOrEmpty(entitySetName))
                {
                    row.EntitySetName = entitySetName;
                }

                row.SessionId = sessionId;

                table.InsertOnSubmit(context.ProjectionName, row);
            }
            else
            {
                RemoteRow rowToUpdate = LoadExistingRecord(context, table, row);
                if (rowToUpdate == null)
                    throw Fail(context, $"Missing record '{args.Name}' when saving");

                if (!string.IsNullOrEmpty(entitySetName))
                {
                    rowToUpdate.EntitySetName = entitySetName;
                }

                rowToUpdate.SessionId = sessionId;

                table.Attach(context.ProjectionName, rowToUpdate);

                foreach (IMetaDataMember member in metaTable.DataMembers)
                {
                    if (member.PrimaryKey || member.ServerPrimaryKey)
                    {
                        continue;
                    }

                    if (member.ColumnType == typeof(byte[]))
                    {
                        byte[] oldValue = member.GetValue(rowToUpdate) as byte[];
                        byte[] newValue = member.GetValue(row) as byte[];
                        if (oldValue == null || newValue == null ||
                            !oldValue.SequenceEqual(newValue))
                        {
                            member.SetValue(rowToUpdate, newValue);
                        }
                    }
                    else
                    {
                        member.SetValue(rowToUpdate, member.GetValue(row));
                    }
                }
            }

            ctx.SubmitChanges(args.Send);

            context.DataChangeSet.AddRow(metaTable, row.RowId);

            return ExecuteResult.None;
        }

        private ExecuteResult ExecuteDelete(ProcedureContext context, CpiDeleteCallArgs args)
        {
            FwDataContext ctx = context.DbDataContext;

            string entitySetName = null;
            if (context.Vars.TryGetValue(EntitySetVariableName, out VariableStorage var))
            {
                entitySetName = var?.Value?.ToString();
            }

            object value = context.GetValue(args.Name);
            RemoteRow row = value as RemoteRow;
            if (row != null)
            {
                if (!string.IsNullOrEmpty(entitySetName))
                {
                    row.EntitySetName = entitySetName;
                }

                IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                ITable table = metaTable == null ? null : ctx.GetTable(metaTable);
                if (table == null)
                    throw Fail(context, $"Invalid record '{args.Name}' table '{row.TableName}' when deleting");

                ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, row);
                row.PrimaryKeyString = key?.ToFormattedKeyRef(context.ProjectionName, true);

                ValidateChangeAccess(context, RemoteNaming.ToEntityName(row.TableName));

                if (row.RowId > 0)
                {
                    table.DeleteOnSubmit(context.ProjectionName, row);
                    ctx.SubmitChanges(args.Send);

                    context.DataChangeSet.AddRow(metaTable, row.RowId);
                }
            }

            return ExecuteResult.None;
        }

        private ExecuteResult ExecutePerformAction(ProcedureContext context)
        {
            if (string.IsNullOrEmpty(context.ActionName))
            {
                throw Fail(context, $"Invalid perform action in '{context.ProcedureName}'.");
            }

            if (_metadata.GetActionSyncPolicy(context.ProjectionName, context.ActionName) == ProcSyncPolicy.Online)
            {
                throw Fail(context, $"Unable to perform online action from within a procedure ({context.ProcedureName}).");
            }

            FwDataContext ctx = context.DbDataContext;

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            if (context.Procedure.Parameters != null)
            {
                foreach (CpiParam param in context.Procedure.Parameters)
                {
                    VariableStorage variableStorage;
                    context.Vars.TryGetValue(param.Name, out variableStorage);
                    parameters[param.Name] = variableStorage?.Value;
                }
            }

            CpiAction action = _metadata.FindAction(context.ProjectionName, context.ActionName);
            if (action == null)
            {
                action = ctx.FindFwAction(context, parameters);
            }

            CpiEntity entity = null; // Only used for bound action

            string transactionGroup;
            string entitySetName = string.Empty;
            string arraySourceName = string.Empty;
            string primaryKeyString = string.Empty;
            if (context.BoundAction)
            {
                object value;
                parameters.TryGetValue(RecordVarName, out value);
                parameters.Remove(RecordVarName);

                RemoteRow row = value as RemoteRow;
                entitySetName = row.EntitySetName;
                arraySourceName = row.ArraySourceName;
                primaryKeyString = row.PrimaryKeyString;
                ObjPrimaryKey key = row == null ? null : ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, row);
                entity = _metadata.FindEntity(context.ProjectionName, row);

                if (row == null || key == null)
                {
                    throw Fail(context, $"Invalid bound record in perform action '{context.ProcedureName}'.");
                }

                parameters[nameof(row.ObjId)] = row.ObjId;
                parameters[nameof(row.ObjVersion)] = row.ObjVersion;

                foreach (var kvp in key.Values)
                {
                    parameters[kvp.Item1.PropertyName] = kvp.Item2;
                }

                transactionGroup = action.GetBoundTransactionGroup(row, key, parameters);
            }
            else
            {
                transactionGroup = action.GetActionTransactionGroup(parameters);
            }

            Dictionary<string, object> remoteParams = new Dictionary<string, object>();
            foreach (KeyValuePair<string, object> kvp in parameters)
            {
                remoteParams[RemoteNaming.ToServerAttributeName(kvp.Key)] = kvp.Value;
            }

            // Creating new dictionary to keep parameters with DataType
            Dictionary<string, object> paramDataTypes = new Dictionary<string, object>();

            foreach (KeyValuePair<string, object> kvp in parameters)
            {
                string serverAttributeName = RemoteNaming.ToServerAttributeName(kvp.Key);

                if (action?.Parameters != null)
                {
                    string dataType = action.Parameters.FirstOrDefault(x => x.Name == kvp.Key)?.DataType.ToString() ?? null;
                    paramDataTypes[serverAttributeName] = dataType;
                }

                if (entity != null && entity.Attributes.ContainsKey(kvp.Key) && (!paramDataTypes.ContainsKey(serverAttributeName) || paramDataTypes[serverAttributeName] == null))
                {
                    paramDataTypes[serverAttributeName] = entity.Attributes[kvp.Key].Datatype.ToString();
                }
            }

            string sessionId = FndTransactionSession.GetCurrentOpenSession(ctx);

            ctx.PerformOnSubmit(context.ProjectionName, context.ActionName, remoteParams, paramDataTypes, transactionGroup, entitySetName, arraySourceName, primaryKeyString, sessionId);
            ctx.SubmitChanges(true);

            context.DataChangeSet.AddTable(ctx.Transitions.Entity);

            return ExecuteResult.None;
        }

        private ExecuteResult ExecuteCreate(ProcedureContext context, CpiCreateCallArgs args)
        {
            ValidateEntityAccess(context, args.Entity);

            RemoteRow row = CreateAndPrepareRow(context, args.Entity);

            SetClientGeneratedKeys(context, row);

            return new ExecuteResult(row);
        }

        private RemoteRow CreateAndPrepareRow(ProcedureContext context, string entityName)
        {
            ValidateEntityAccess(context, entityName);

            IMetaTable metaTable = _metadata.GetTableForEntityName(entityName);
            if (metaTable == null)
                throw Fail(context, $"Invalid entity '{entityName}' when creating record.");

            RemoteRow row = (RemoteRow)metaTable.CreateRow();

            string procName = $"EntityPrepare<{entityName}>";
            CpiProc proc = _metadata.FindProcedure(context.ProjectionName, procName);
            if (proc != null)
            {
                Dictionary<string, object> parameters = new Dictionary<string, object>();
                parameters[RecordVarName] = row;

                ExecuteResult prepareResult = CallProcedure(context, procName, parameters, false);

                row = prepareResult.Value as RemoteRow;
                if (row == null)
                    throw Fail(context, $"Failed to prepare entity '{entityName}' when creating record.");
            }

            return row;
        }

        private ExecuteResult ExecuteFetch(ProcedureContext context, CpiFetchCallArgs args)
        {
            EntityQuery query = CreateQuery(context, args);
            query.Take = 1;

            ValidateEntityAccess(context, query.DataSource.EntityName);

            EntityRecord row = context.DbDataContext.Query(query, context.CancelToken).FirstOrDefault();

            return new ExecuteResult(row?.Row);
        }

        private ExecuteResult ExecuteFor(ProcedureContext context, CpiExecute exec, CpiForCallArgs args)
        {
            CpiExecute[] executions;
            if (exec.Assign != null &&
                exec.Result != null &&
                exec.Result.TryGetValue((string)ExecuteResult.True.Value, out executions) &&
                executions != null)
            {
                EntityQuery query = CreateQuery(context, args);

                ValidateEntityAccess(context, query.DataSource.EntityName);

                IEnumerable<EntityRecord> rows = context.DbDataContext.Query(query, context.CancelToken);
                foreach (EntityRecord row in rows)
                {
                    context.CancelToken.ThrowIfCancellationRequested();

                    context.Assign(exec.Assign, row.Row);

                    ExecuteResult ret = Execute(context, executions);

                    if (ret.Returning)
                    {
                        if (ret.ShouldBreak)
                        {
                            break;
                        }

                        return ret;
                    }
                }
            }

            return new ExecuteResult(null);
        }

        private ExecuteResult ExecuteWhile(ProcedureContext context, CpiExecute exec, CpiWhileCallArgs args)
        {
            if (exec.Result != null
                && exec.Result.TryGetValue((string)ExecuteResult.True.Value, out CpiExecute[] executions)
                && executions != null)
            {
                CpiExpression expression = args.Expression;
                ExecuteResult ret = new ExecuteResult(null);

                bool result = RunExpressionCheck(context, expression, true);
                while (result)
                {
                    ret = Execute(context, executions);

                    if (ret.Returning)
                    {
                        if (ret.ShouldBreak)
                        {
                            break;
                        }

                        return ret;
                    }

                    result = RunExpressionCheck(context, expression, true);
                }
            }

            return new ExecuteResult(null);
        }

        private ExecuteResult ExecuteCount(ProcedureContext context, CpiCountCallArgs args)
        {
            EntityQuery query = CreateQuery(context, args);
            ValidateEntityAccess(context, query.DataSource.EntityName);
            long count = context.DbDataContext.Count(query);
            return new ExecuteResult(count);
        }

        private ExecuteResult ExecuteProc(ProcedureContext context, CpiProcCallArgs args)
        {
            if (string.IsNullOrEmpty(args.Namespace))
            {
                Dictionary<string, object> parameters = new Dictionary<string, object>();
                if (args.Params != null)
                {
                    foreach (var kvp in args.Params)
                    {
                        parameters[kvp.Key] = context.ReadParamValue(kvp.Value);
                    }
                }

                return CallProcedure(context, args.Name, parameters, true);
            }

            return _fwFunctions.Execute(context, args.Namespace, args.Name, args.ParamsArray);
        }

        private ExecuteResult ExecuteError(ProcedureContext context, CpiErrorCallArgs args)
        {
            string message = context.InterpolateString(args.Message, false);
            string[] messageSplit = message.Split(new char[] { ':' }, 2);
            message = messageSplit.Length == 2 ? messageSplit[1].ToString() : message;

            throw new ProcedureErrorException(context.ProcedureName, message.Trim(), args.Parameter);
        }

        private ExecuteResult ExecuteLog(ProcedureContext context, CpiLogCallArgs args)
        {
            string message = context.InterpolateString(args.Message, false);
            Logger.Information(message);
            return ExecuteResult.None;
        }

        private ExecuteResult ExecuteDebug(ProcedureContext context, CpiLogCallArgs args)
        {
            string message = context.InterpolateString(args.Message, false);
            Logger.Debug(message);
            return ExecuteResult.None;
        }

        private ExecuteResult ExecuteTrace(ProcedureContext context, CpiLogCallArgs args)
        {
            string message = context.InterpolateString(args.Message, false);
            Logger.Trace(message);
            return ExecuteResult.None;
        }

        private ExecuteResult ExecuteSuper(ProcedureContext context, CpiSuperCallArgs args)
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            if (args.Params != null)
            {
                foreach (var kvp in args.Params)
                {
                    parameters[kvp.Key] = context.ReadParamValue(kvp.Value);
                }
            }

            parameters = ExecutionUtils.CloneParameters(_metadata, parameters);

            ProcedureContext subContext = context.CreateSuperContext(parameters);
            ExecuteResult ret = Execute(subContext, subContext.Layer?.Execute);
            return ret.Returning ? new ExecuteResult(ret.Value) : ExecuteResult.None;
        }

        private ExecuteResult ExecuteCopyCustomFields(ProcedureContext context, CpiCopyCustomFieldsCallArgs args)
        {
            if (context.Vars.TryGetValue(args.Source, out VariableStorage source) && context.Vars.TryGetValue(args.Target, out VariableStorage target) &&
                source.Value is RemoteRow sourceVar && target.Value is RemoteRow targetVar)
            {
                List<KeyValuePair<string, CpiAttribute>> customFields = _metadata.FindEntity(context.ProjectionName, sourceVar)?.Attributes?.
                    Where(x => x.Key.StartsWith("Cf_") && x.Value.FromEntity == RemoteNaming.ToEntityName(targetVar.TableName)).ToList();

                if (customFields != null)
                {
                    foreach (KeyValuePair<string, CpiAttribute> cf in customFields)
                    {
                        if (source.TryGetStructureValue(cf.Key, out object cfValue))
                        {
                            string entityCfName = null;
                            string[] parts = cf.Key.Split(new char[] { '_' }, 3); // Split into three parts: Cf, Entityname, AttributeName
                            if (parts.Length == 3 && parts[1].ToLower() == target.SubType.ToLower())
                            {
                                entityCfName = $"{parts[0]}_{parts[2]}";
                            }

                            if (entityCfName == null || !target.TrySetStructureValue(entityCfName, cfValue))
                            {
                                Logger.Error("Cannot set value to target {0}.{1} when calling copyCustomFields in procedure {2}", target.VariableName, cf.Key, context.ProcedureName);
                            }
                        }
                        else
                        {
                            Logger.Error("Cannot get value from source {0}.{1} when calling copyCustomFields in procedure {2}", source.VariableName, cf.Key, context.ProcedureName);
                        }
                    }
                }
            }
            else
            {
                Logger.Error("Invalid entity/virtual information passed to copyCustomFields in procedure {0}", context.ProcedureName);
            }

            return ExecuteResult.None;
        }

        private void SetClientGeneratedKeys(ProcedureContext context, RemoteRow row)
        {
            IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
            if (metaTable != null)
            {
                string entityName = RemoteNaming.ToEntityName(row.TableName);
                foreach (IMetaDataMember pkMember in metaTable.DataMembers.Where(x => x.ServerPrimaryKey))
                {
                    CpiAttribute attrib = _metadata.FindAttribute(context.ProjectionName, entityName, pkMember.PropertyName);
                    if (attrib != null &&
                        attrib.KeyGeneration != CpiKeyGeneration.User &&
                        pkMember.GetValue(row) == null)
                    {
                        object key = GenerateClientKey(context, entityName, metaTable, pkMember);
                        pkMember.SetValue(row, key);
                    }
                }
            }
        }

        private object GenerateClientKey(ProcedureContext context, string entityName, IMetaTable metaTable, IMetaDataMember pkMember)
        {
            FwDataContext ctx = context.DbDataContext;

            ClientGeneratedKey key = ctx.ClientGeneratedKeys.FirstOrDefault(x =>
                     x.TableName == metaTable.TableName &&
                     x.ColumnName == pkMember.ColumnName);

            if (key == null)
            {
                key = new ClientGeneratedKey();
                key.TableName = metaTable.TableName;
                key.ColumnName = pkMember.ColumnName;
                ctx.ClientGeneratedKeys.InsertOnSubmit(key);
            }
            else
            {
                ctx.ClientGeneratedKeys.Attach(key);
            }

            long clientId = key.ClientId - 1;
            object clientIdValue = pkMember.ConvertValue(clientId);

            EntityDataSource source = EntityDataSource.FromEntity(_metadata, context.ProjectionName, entityName);
            EntityQuery query = new EntityQuery(source);

            while (true)
            {
                query.AddFilter(pkMember.PropertyName, clientIdValue);

                long count = ctx.Count(query);
                if (count == 0)
                {
                    break;
                }

                clientId--;
                clientIdValue = pkMember.ConvertValue(clientId);
            }

            key.ClientId = clientId;
            ctx.SubmitChanges(false);

            return clientIdValue;
        }

        private Dictionary<string, object> PrepareParameters(RemoteRow row, out RemoteRow cloned)
        {
            Dictionary<string, object> ret = new Dictionary<string, object>();
            cloned = ExecutionUtils.CloneRow(_metadata, row);
            ret[RecordVarName] = cloned;
            return ret;
        }

        private void ValidateChangeAccess(ProcedureContext context, string entityName)
        {
            ValidateEntityAccess(context, entityName);

            CpiEntity entityFromAppMeta = null;
            _metadata.CpiMetaData.App?.SyncEntities?.Entities?.TryGetValue(entityName, out entityFromAppMeta);
            CpiEntity entity = _metadata.FindEntity(context.ProjectionName, entityName);
            if (entityFromAppMeta?.IsView() == true || entity?.IsView() == true)
            {
                throw Fail(context, $"Save or delete cannot be executed against offline query '{context.ProjectionName}.{entityName}' from a procedure");
            }
        }

        private void ValidateEntityAccess(ProcedureContext context, string entityName)
        {
            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(entityName);
            if (syncPolicy == EntitySyncPolicy.OnlineOnly)
            {
                throw Fail(context, $"Unable to access OnlineOnly entity '{context.ProjectionName}.{entityName}' from a procedure");
            }
        }

        private EntityQuery CreateQuery(ProcedureContext context, CpiQueryCallArgs args)
        {
            EntityQuery query;

            string entitySet = string.IsNullOrEmpty(args.Name) ? args.Entity : args.Name;
            if (entitySet.IndexOf('.') >= 0)
            {
                entitySet = null;
            }

            if (!string.IsNullOrEmpty(entitySet))
            {
                // Standard entityset query
                EntityDataSource source = EntityDataSource.FromEntitySet(_metadata, context.ProjectionName, entitySet);
                if (source == null)
                    throw Fail(context, $"Invalid entity set '{context.ProjectionName}.{entitySet}' in query");

                query = new EntityQuery(source);
            }
            else
            {
                string[] parts = args.Name.Split('.');
                if (parts.Length != 2)
                    throw Fail(context, $"Invalid query set '{context.ProjectionName}.{args.Name}'");

                object value = context.GetValue(parts[0]);
                RemoteRow row = value as RemoteRow;
                if (row == null)
                    throw Fail(context, $"Invalid record '{context.ProjectionName}.{parts[0]}'");

                string entityName = RemoteNaming.ToEntityName(row.TableName);
                CpiReference reference = _metadata.FindReference(context.ProjectionName, entityName, parts[1]);
                if (reference != null)
                {
                    // select single reference row
                    EntityDataSource source = EntityDataSource.FromEntity(_metadata, context.ProjectionName, reference.Target);
                    if (source == null)
                        throw Fail(context, $"Invalid query set '{context.ProjectionName}.{args.Name}'");

                    query = new EntityQuery(source);

                    foreach (var mapping in reference.Mapping)
                    {
                        IMetaDataMember referenceColumn = source.Table.FindMemberByPropertyName(mapping.Value);
                        if (referenceColumn == null)
                            throw Fail(context, $"Invalid query '{context.ProjectionName}.{args.Name}' mapping column '{mapping.Value}'");

                        object refValue = row[mapping.Key];
                        query.AddFilter(mapping.Value, refValue);
                    }
                }
                else
                {
                    // select reference array
                    EntityDataSource source = EntityDataSource.FromEntity(_metadata, context.ProjectionName, RemoteNaming.ToEntityName(row.TableName));
                    EntityDataSource arraySource = source?.SelectArray(row, parts[1]);
                    if (arraySource == null)
                        throw Fail(context, $"Invalid query set '{context.ProjectionName}.{args.Name}'");

                    query = new EntityQuery(arraySource);
                }
            }

            if (args.Where != null)
            {
                query.JsonWhereExpression = new JsonWhereExpression(query.DataSource, args.Where, context, args.Alias);
            }

            if (args.OrderBy != null)
            {
                string alias = args.Alias != null ? args.Alias + "." : null;
                foreach (CpiKeyValue kv in args.OrderBy)
                {
                    string attributeName = kv.Key;
                    if (alias != null && attributeName.StartsWith(alias))
                    {
                        attributeName = attributeName.Substring(alias.Length);
                    }

                    query.AddSort(attributeName, kv.Value.ToLowerInvariant() == "desc" ? ESortOrder.Descending : ESortOrder.Ascending);
                }
            }

            return query;
        }
    }
}
