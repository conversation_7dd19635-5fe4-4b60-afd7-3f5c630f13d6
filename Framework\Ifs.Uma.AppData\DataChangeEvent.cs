﻿using System;
using System.Collections.Generic;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Prism.Events;

namespace Ifs.Uma.AppData
{
    public class DataChangedEvent : PubSubEvent<DataChangedEventArgs>
    {
    }

    public static class DataChangedCauses
    {
        public const string BackgroundSync = "Sync";
        public const string User = "User";
        public const string Procedure = "Procedure";
        public const string Online = "Online";
    }

    public class DataChangedEventArgs : EventArgs
    {
        public DataChangeSet ChangeSet { get; private set; }
        public string Cause { get; private set; }

        public DataChangedEventArgs(DataChangeSet changeSet, string cause)
        {
            if (changeSet == null) throw new ArgumentNullException("changeSet");

            ChangeSet = changeSet;
            Cause = cause;
        }

        public static DataChangedEventArgs FromRows(IMetaModel metaModel, IEnumerable<RowBase> rows, string cause)
        {
            DataChangeSet changeSet = new DataChangeSet();
            changeSet.AddRows(metaModel, rows);
            return new DataChangedEventArgs(changeSet, cause);
        }
    }
}
