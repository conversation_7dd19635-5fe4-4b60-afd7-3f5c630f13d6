﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Online
{
    public interface IOnlineDataHandler
    {
        string UserName { get; }
        int DeviceId { get; }
        string AppName { get; }
        bool IsOnline { get; }

        Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken);

        Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken, string entitySetName = "");
        Task<ExecuteResult> EntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken);
        Task<ExecuteResult> EntityUpdateAsync(string projectionName, RemoteRow row, IEnumerable<string> changedMembers, CancellationToken cancelToken);
        Task<ExecuteResult> EntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken);

        Task<ExecuteResult> CallActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken);
        Task<ExecuteResult> CallBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken);
        Task<EntityQueryResult> GetFunctionRecordsAsync(EntityQuery query, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken);
        Task<ExecuteResult> CallFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken);
        Task<ExecuteResult> CallBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken);
    }
}
