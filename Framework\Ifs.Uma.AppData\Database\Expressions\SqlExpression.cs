﻿using System;
using System.Linq.Expressions;
using System.Text;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Database.Expressions
{
    internal sealed class SqlExpression : ISqlExpression
    {
        private readonly Expression _expression;
        
        internal static ISqlExpression Create(Expression expression)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));
            return new SqlExpression(expression);
        }

        private SqlExpression(Expression expression)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));

            DisallowVarChecker.Check(expression);

            _expression = expression;
        }

        public void WriteSql(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
        {
            SqlExpressionWriter.WriteSql(_expression, sb, info, builder, mode);
        }
    }
}
