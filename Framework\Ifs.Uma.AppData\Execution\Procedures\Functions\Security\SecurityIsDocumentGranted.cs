﻿using System.Linq;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Model;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Security
{
    internal sealed class SecurityIsDocumentGranted : SecurityFunction
    {
        public const string FunctionName = "IsDocumentGranted";

        public SecurityIsDocumentGranted()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string projection = DocumentHandler.MobileAttachmentsProjection;
            string entity = nameof(EdmFile);

            var security = context.DbDataContext.ClientSecurities
                .FirstOrDefault(x => x.Projection == projection && x.ObjectName == entity);

            if (security == null)
            {
                return false;
            }

            return security.Granted;
        }
    }
}
