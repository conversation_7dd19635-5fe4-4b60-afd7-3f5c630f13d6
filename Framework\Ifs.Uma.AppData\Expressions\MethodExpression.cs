﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class MethodExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.Method;
        public override Type Type => typeof(DynamicValue);
        public string MethodName { get; }
        public ReadOnlyCollection<Expression> Arguments { get; }

        internal MethodExpression(string methodName, IEnumerable<Expression> arguments)
        {
            if (methodName == null) throw new ArgumentNullException(nameof(methodName));
            if (arguments == null) throw new ArgumentNullException(nameof(arguments));

            MethodName = methodName;
            Arguments = ToReadOnly(arguments);
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitMethod(this);
        }

        public override string ToString()
        {
            return MethodName + "(" + string.Join(", ", Arguments) + ")";
        }

        public Expression Update(string methodName, ReadOnlyCollection<Expression> args)
        {
            if (methodName == MethodName && ReferenceEquals(Arguments, args))
            {
                return this;
            }

            return new MethodExpression(MethodName, args);
        }
    }

    public partial class IfsExpression
    {
        public static MethodExpression Method(string methodName, IEnumerable<Expression> arguments)
        {
            return new MethodExpression(methodName, arguments ?? new Expression[0]);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitMethod(MethodExpression exp)
        {
            ReadOnlyCollection<Expression> args = Visit(exp.Arguments);
            return exp.Update(exp.MethodName, args);
        }
    }
}
