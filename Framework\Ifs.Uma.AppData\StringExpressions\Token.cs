﻿using System;
using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData.StringExpressions
{
    internal enum TokenType
    {
        None,
        String,
        Real,
        Integer,
        True,
        False,
        Symbol,
        Comma,
        Space,
        OpenParen,
        CloseParen,
        Identifier,
        Eof,
    }

    internal struct Token
    {
        public static readonly Token None = new Token(TokenType.None, null);

        public TokenType Type { get; }
        public string Contents { get; }

        public Token(TokenType type, string contents)
        {
            Type = type;
            Contents = contents;
        }
    }

    internal sealed class TokenDefinition
    {
        private readonly Regex _regex;
        public TokenType LexerToken { get; }

        public TokenDefinition(string regex, TokenType lexerToken)
        {
            _regex = new Regex($"^{regex}", RegexOptions.None, TimeSpan.FromSeconds(10));
            LexerToken = lexerToken;
        }

        public int Match(string text)
        {
            var m = _regex.Match(text);
            return m.Success ? m.Length : 0;
        }

        public override string ToString()
        {
            return _regex.ToString();
        }

        public static TokenDefinition[] GetDefinitions()
        {
            return new[]
            {
                new TokenDefinition(@"([""'])(?:\\\1|.)*?\1", TokenType.String),
                new TokenDefinition(@"[-+]?\d*\.\d+([eE][-+]?\d+)?", TokenType.Real),
                new TokenDefinition(@"[-+]?\d+", TokenType.Integer),
                new TokenDefinition(@"true", TokenType.True),
                new TokenDefinition(@"false", TokenType.False),
                new TokenDefinition(@"[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*", TokenType.Identifier),
                new TokenDefinition(@"[*<>=\?:\-+/->!]+", TokenType.Symbol),
                new TokenDefinition(@",", TokenType.Comma),
                new TokenDefinition(@"\(", TokenType.OpenParen),
                new TokenDefinition(@"\)", TokenType.CloseParen),
                new TokenDefinition(@"\s", TokenType.Space)
            };
        }
    }
}
