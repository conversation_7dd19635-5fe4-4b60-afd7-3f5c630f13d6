﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Database;

namespace Ifs.Uma.Data
{
    /// <summary>
    /// Identifies a class as representing rows in a database table
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false, Inherited = false)]
    public sealed class TableAttribute : Attribute
    {
        public TableAttribute() { }
        /// <summary>
        /// The name of the database table - case may be changed by the implementation
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Indicates whether the table is a system table or not
        /// </summary>
        public bool System
        {
            get => Class == MetaTableClass.System;
            set => Class = value ? MetaTableClass.System : MetaTableClass.App;
        }

        /// <summary>
        /// Indicates what table class the table is
        /// </summary>
        public MetaTableClass Class { get; set; } = MetaTableClass.App;
    }
}
