﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.Security
{
    [TestFixture]
    public class SecurityEntityTests : ProcedureTest
    {
        [Test]
        public async Task EntityRead_Granted()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["EntityName"] = "TstCustomer";
            bool result = await Call("Security_IsEntityReadGranted", parameters);
            Assert.AreEqual(true, result);
        }

        [Test]
        public async Task EntityRead_NotGranted()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["EntityName"] = "TstCompany";
            bool result = await Call("Security_IsEntityReadGranted", parameters);
            Assert.AreEqual(false, result);
        }

        [Test]
        public async Task EntityRead_Missing()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["EntityName"] = "TstWorkOrder";
            bool result = await Call("Security_IsEntityReadGranted", parameters);
            Assert.AreEqual(true, result);
        }

        [Test]
        public async Task EntityWrite_Granted()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["EntityName"] = "TstCompany";
            bool result = await Call("Security_IsEntityWriteGranted", parameters);
            Assert.AreEqual(true, result);
        }

        [Test]
        public async Task EntityWrite_NotGranted()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["EntityName"] = "TstCustomer";
            bool result = await Call("Security_IsEntityWriteGranted", parameters);
            Assert.AreEqual(false, result);
        }

        [Test]
        public async Task EntityWrite_Missing()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["EntityName"] = "TstWorkOrder";
            bool result = await Call("Security_IsEntityWriteGranted", parameters);
            Assert.AreEqual(true, result);
        }

        private async Task<bool> Call(string functionName, Dictionary<string, object> parameters = null)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, functionName, parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return (bool)result.Value;
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>(
                "Execution.Procedures.Functions.Security.SecurityEntityTestsSchema",
                "Execution.Procedures.Functions.Security.SecurityEntityTestsData");
        }
    }
}
