﻿using System;
using System.Collections.Generic;
using System.Text;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Model;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Attachment
{
    internal sealed class AttachmentConnectDocument : AttachmentFunction
    {
        public const string FunctionName = "ConnectDocument";

        public AttachmentConnectDocument()
            : base(FunctionName, 3)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            FndDocumentKeys documentKeys = parameters[0].GetValue() as FndDocumentKeys;
            string luName = parameters[1].GetString();
            string keyRef = parameters[2].GetString();

            return ConnectDocument(context, documentKeys, luName, keyRef);
        }

        internal static object ConnectDocument(ProcedureContext context, FndDocumentKeys documentKeys, string luName, string keyRef)
        {
            DocumentHandler.ConnectDocumentAttachment(context.DbDataContext, documentKeys, luName, keyRef, out DocReferenceObject docRef);
            context.DataChangeSet.AddRow(context.Metadata.MetaModel, docRef);

            return null;
        }
    }
}
