﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndDocumentKeys : RemoteRow
    {
        public const string DbTableName = "fnd_document_keys";

        [Column]
        public string DocClass { get; set; }

        [Column]
        public string DocNo { get; set; }

        [Column]
        public string DocSheet { get; set; }

        [Column]
        public string DocRev { get; set; }

        public FndDocumentKeys()
            : base(DbTableName)
        {
        }
    }
}
