﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.IO;
using Ifs.Uma.Utility;
using IQToolkit.Data.Common.Language;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Writes out an expression tree (including DbExpression nodes) in a C#-ish syntax
    /// </summary>
    internal class DbExpressionWriter : ExpressionWriter
    {
        Dictionary<TableAlias, int> m_aliasMap = new Dictionary<TableAlias, int>();

        protected DbExpressionWriter(TextWriter writer)
            : base(writer)
        {
        }

        public static new void Write(TextWriter writer, Expression expression)
        {
            new DbExpressionWriter(writer).Visit(expression);
        }

        public static new string WriteToString(Expression expression)
        {
            using (StringWriter sw = new StringWriter(System.Globalization.CultureInfo.InvariantCulture))
            {
                Write(sw, expression);
                return sw.ToString();
            }
        }

        protected override Expression VisitExtension(Expression node)
        {
            if (node == null) return null;
            switch (node.GetDbNodeType())
            {
                case DbExpressionType.Projection:
                    return VisitProjection((ProjectionExpression)node);
                case DbExpressionType.ClientJoin:
                    return VisitClientJoin((ClientJoinExpression)node);
                case DbExpressionType.Select:
                    return VisitSelect((SelectExpression)node);
                case DbExpressionType.OuterJoined:
                    return VisitOuterJoined((OuterJoinedExpression)node);
                case DbExpressionType.Column:
                    return VisitColumn((ColumnExpression)node);
                case DbExpressionType.If:
                case DbExpressionType.Block:
                case DbExpressionType.Declaration:
                    return VisitCommand((CommandExpression)node);
                case DbExpressionType.Batch:
                    return VisitBatch((BatchExpression)node);
                case DbExpressionType.Function:
                    return VisitFunction((FunctionExpression)node);
                case DbExpressionType.Entity:
                    return VisitEntity((EntityExpression)node);
                default:
                    if (node is DbExpression)
                    {
                        Write(FormatQuery(node));
                        return node;
                    }
                    return base.VisitExtension(node);
            }
        }

        protected void AddAlias(TableAlias alias)
        {
            if (!m_aliasMap.ContainsKey(alias))
            {
                m_aliasMap.Add(alias, m_aliasMap.Count);
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification="pseudo/debug code")]
        protected virtual Expression VisitProjection(ProjectionExpression projection)
        {
            if (projection == null) throw new ArgumentNullException("projection");
            AddAlias(projection.Select.Alias);
            Write("Project(");
            WriteLine(Indentation.Inner);
            Write("@\"");
            Visit(projection.Select);
            Write("\",");
            WriteLine(Indentation.Same);
            Visit(projection.Projector);
            Write(",");
            WriteLine(Indentation.Same);
            Visit(projection.Aggregator);
            WriteLine(Indentation.Outer);
            Write(")");
            return projection;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo/debug code")]
        protected virtual Expression VisitClientJoin(ClientJoinExpression join)
        {
            if (join == null) throw new ArgumentNullException("join");
            AddAlias(join.Projection.Select.Alias);
            Write("ClientJoin(");
            WriteLine(Indentation.Inner);
            Write("OuterKey(");
            VisitExpressionList(join.OuterKey);
            Write("),");
            WriteLine(Indentation.Same);
            Write("InnerKey(");
            VisitExpressionList(join.InnerKey);
            Write("),");
            WriteLine(Indentation.Same);
            Visit(join.Projection);
            WriteLine(Indentation.Outer);
            Write(")");
            return join;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo/debug code")]
        protected virtual Expression VisitOuterJoined(OuterJoinedExpression outer)
        {
            if (outer == null) throw new ArgumentNullException("outer");
            Write("Outer(");
            WriteLine(Indentation.Inner);
            Visit(outer.Test);
            Write(", ");
            WriteLine(Indentation.Same);
            Visit(outer.Expression);
            WriteLine(Indentation.Outer);
            Write(")");
            return outer;
        }

        protected virtual Expression VisitSelect(SelectExpression select)
        {
            if (select == null) throw new ArgumentNullException("select");
            Write(FormatQuery(select));
            return select;
        }

        protected virtual Expression VisitCommand(CommandExpression command)
        {
            if (command == null) throw new ArgumentNullException("command");
            Write(FormatQuery(command));
            return command;
        }

        private class GenericSqlBuilder : Ifs.Uma.Database.SqlBuilder
        {
        }

        protected virtual string FormatQuery(Expression query)
        {
            return SqlBuilderFormatter.Format(new GenericSqlBuilder(), query, true);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo/debug code")]
        protected virtual Expression VisitBatch(BatchExpression batch)
        {
            if (batch == null) throw new ArgumentNullException("batch");
            Write("Batch(");
            WriteLine(Indentation.Inner);
            Visit(batch.Input);
            Write(",");
            WriteLine(Indentation.Same);
            Visit(batch.Operation);
            Write(",");
            WriteLine(Indentation.Same);
            Visit(batch.BatchSize);
            Write(", ");
            Visit(batch.Stream);
            WriteLine(Indentation.Outer);
            Write(")");
            return batch;
        }

        protected virtual Expression VisitVariable(VariableExpression vex)
        {
            Write(FormatQuery(vex));
            return vex;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo/debug code")]
        protected virtual Expression VisitFunction(FunctionExpression node)
        {
            if (node == null) return null;
            Write("FUNCTION ");
            Write(node.Name);
            if (node.Arguments.Count > 0)
            {
                Write("(");
                VisitExpressionList(node.Arguments);
                Write(")");
            }
            return node;
        }

        protected virtual Expression VisitEntity(EntityExpression node)
        {
            if (node == null) return null;
            Visit(node.Expression);
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo/debug code")]
        protected override Expression VisitConstant(ConstantExpression node)
        {
            if (node == null) return null;
            if (node.Type == typeof(QueryCommand))
            {
                QueryCommand qc = (QueryCommand)node.Value;
                Write("new QueryCommand {");
                WriteLine(Indentation.Inner);
                Write("\"" + qc.CommandText + "\"");
                Write(",");
                WriteLine(Indentation.Same);
                Visit(Expression.Constant(qc.Parameters));
                Write(")");
                WriteLine(Indentation.Outer);
                return node;
            }
            return base.VisitConstant(node);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1303:Do not pass literals as localized parameters",
            MessageId = "IQToolkit.ExpressionWriter.Write(System.String)", Justification = "pseudo/debug code")]
        protected virtual Expression VisitColumn(ColumnExpression column)
        {
            if (column == null) throw new ArgumentNullException("column");
            int iAlias;
            string aliasName = column.Alias == null ? "A?" :
                m_aliasMap.TryGetValue(column.Alias, out iAlias) ?
                "A" + ObjectConverter.ToString(iAlias) :
                column.Alias.ToString() + "?";
            Write(aliasName);
            Write(".");
            Write("Column(\"");
            Write(column.Name);
            Write("\")");
            return column;
        }
    }
}