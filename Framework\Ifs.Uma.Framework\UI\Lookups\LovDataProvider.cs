﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Data;
using Ifs.Uma.UI.Lists;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Lookups
{
    internal abstract class LovDataProvider
    {
        protected ILogger Logger { get; }
        protected IMetadata Metadata { get; }
        protected IDataHandler DataHandler { get; }
        protected EntityQuery BaseQuery { get; }
        protected IEnumerable<string> SearchAttributes { get; }

        protected LovDataProvider(ILogger logger, IMetadata metadata, IDataHandler dataHandler, EntityQuery baseQuery, IEnumerable<string> searchAttributes)
        {
            Logger = logger;
            Metadata = metadata;
            DataHandler = dataHandler;
            BaseQuery = baseQuery;
            SearchAttributes = searchAttributes;
        }

        public IDataLoader<LovItem> RequestDataProvider(LookupQuery q)
        {
            EntityQuery thisQuery = BaseQuery.Clone();

            if (!string.IsNullOrWhiteSpace(q.SearchTerm) && SearchAttributes != null)
            {
                thisQuery.Search = new AttributeSearch(SearchAttributes, q.SearchTerm);
            }

            foreach (ListSortOption sort in q.SortOptions.Where(x => !x.IsNone && x.SortOrder.HasValue))
            {
                thisQuery.Sorts.Add(new AttributeSort(sort.Id, sort.SortOrder.Value));
            }

            IDataLoader<EntityRecord> recordLoader = new EntityQueryDataLoader(thisQuery, GetRecordsAsync);
            return recordLoader.Select(CreateLovItem);
        }

        protected abstract Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken);

        public async Task<IEnumerable<LovItem>> RequestItemRetriever(IEnumerable<string> itemIds, CancellationToken cancelToken)
        {
            if (itemIds == null || !itemIds.Any())
            {
                return Enumerable.Empty<LovItem>();
            }

            EntityQuery thisQuery = BaseQuery.Clone();

            AttributeExpression attributeExpression = null;

            foreach (string item in itemIds)
            {
                AttributeExpression itemAttributeExpression = null;
                if (item.StartsWith("#"))
                {
                    string objKey = item.Substring(1);
                    itemAttributeExpression = AttributeExpression.Compare(nameof(RemoteRow.ObjKey), AttributeCompareOperator.Equals, objKey);
                }
                else
                {
                    ObjPrimaryKey objPrimaryKey = ObjPrimaryKey.FromKeySeparatedValues(thisQuery.DataSource.Table, item);
                    if (objPrimaryKey != null)
                    {
                        itemAttributeExpression = AttributeExpression.FromObjPrimaryKey(objPrimaryKey);
                    }
                }

                // OR each of the items
                if (attributeExpression == null)
                {
                    attributeExpression = itemAttributeExpression;
                }
                else if (itemAttributeExpression != null)
                {
                    attributeExpression = AttributeExpression.Or(attributeExpression, itemAttributeExpression);
                }
            }

            if (attributeExpression != null)
            {
                thisQuery.FilterExpression = thisQuery.FilterExpression == null ? attributeExpression : AttributeExpression.And(thisQuery.FilterExpression, attributeExpression);   
            }

            EntityQueryResult results = await GetRecordsAsync(thisQuery, cancelToken);
            return results.Records.Select(CreateLovItem).ToArray();
        }

        public string RequestItemIdRetriever(object item)
        {
            if (item is LovItem lovItem)
            {
                return lovItem.Record.GetRemoteRow().GetRowIdentifier(Metadata.MetaModel);
            }

            return null;
        }

        private LovItem CreateLovItem(EntityRecord record)
        {
            PageData pageData = new PageData(Logger, Metadata, DataHandler);
            RecordData recordData = new RecordData(Logger, Metadata, DataHandler);
            recordData.LoadRecord(BaseQuery.DataSource.ProjectionName, record);
            return new LovItem(pageData, recordData);
        }
    }

    internal sealed class FunctionDataProvider : LovDataProvider, ILovOnDemandDataDecorator
    {
        private EntityQueryResult _queryResult;
        private SemaphoreSlim _queryLock = new SemaphoreSlim(1);

        public FunctionDataProvider(ILogger logger, IMetadata metadata, IDataHandler dataHandler,
            EntityQuery baseQuery, IEnumerable<string> searchAttributes)
            : base(logger, metadata, dataHandler, baseQuery, searchAttributes)
        {
        }

        public Task<EntityQueryResult> GetRecordsAsyncDecorator(EntityQuery query, CancellationToken cancelToken)
        {
            return GetRecordsAsync(query, cancelToken);
        }

        protected override async Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken)
        {
            if (query.DataSource is FunctionDataSource funcDataSource)
            {
                // Online function calls that return a collection of records should be handled differently, to allow pagination
                ProcSyncPolicy syncPolicy = Metadata.GetFunctionSyncPolicy(funcDataSource.ProjectionName, funcDataSource.FunctionName);
                if (syncPolicy == ProcSyncPolicy.Online)
                {
                    return await DataHandler.GetRecordsAsync(query, cancelToken);
                }
            }
            
            if (_queryResult == null)
            {
                // Cache the results so we only run the function once
                await _queryLock.WaitAsync();
                try
                {
                    if (_queryResult == null)
                    {
                        _queryResult = await DataHandler.GetRecordsAsync(BaseQuery, cancelToken);
                    }
                }
                finally
                {
                    _queryLock.Release();
                }
            }

            if (_queryResult.DataSourceOffline)
            {
                return _queryResult;
            }

            return query.ApplyTo(_queryResult.Records);
        }
    }

    internal sealed class EntityDataProvider : LovDataProvider, ILovOnDemandDataDecorator
    {
        public EntityDataProvider(ILogger logger, IMetadata metadata, IDataHandler dataHandler,
            EntityQuery baseQuery, IEnumerable<string> searchAttributes)
            : base(logger, metadata, dataHandler, baseQuery, searchAttributes)
        {
        }

        public Task<EntityQueryResult> GetRecordsAsyncDecorator(EntityQuery query, CancellationToken cancelToken)
        {
           return GetRecordsAsync(query, cancelToken);
        }

        protected override async Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken)
        {            
            return await DataHandler.GetRecordsAsync(query, cancelToken);
        }
    }

    #region OnDemandSync
    internal interface ILovOnDemandDataDecorator
    {
        Task<EntityQueryResult> GetRecordsAsyncDecorator(EntityQuery query, CancellationToken cancelToken);
    }

    internal abstract class LovDataProviderDecorator : LovDataProvider, ILovOnDemandDataDecorator
    {
        private readonly ILovOnDemandDataDecorator _decorator;
        private bool _searchOnline;

        protected LovDataProviderDecorator(ILogger logger, IMetadata metadata, IDataHandler dataHandler,
            EntityQuery baseQuery, IEnumerable<string> searchAttributes, ILovOnDemandDataDecorator dataProvider)
            : base(logger, metadata, dataHandler, baseQuery, searchAttributes)
        {
            _decorator = dataProvider;
        }

        public void LovSearchModeChanged()
        {
            // Toggling the search online boolean value
            _searchOnline = !_searchOnline;
        }

        public bool SearchStausIsOnline()
        {
            return _searchOnline;
        }

        public virtual Task<EntityQueryResult> GetRecordsAsyncDecorator(EntityQuery query, CancellationToken cancelToken)
        {
           return _decorator.GetRecordsAsyncDecorator(query, cancelToken);
        }
    }

    internal sealed class EntityDataProviderDecorator : LovDataProviderDecorator
    {
        public EntityDataProviderDecorator(ILogger logger, IMetadata metadata, IDataHandler dataHandler,
            EntityQuery baseQuery, IEnumerable<string> searchAttributes, ILovOnDemandDataDecorator dataProvider)
            : base(logger, metadata, dataHandler, baseQuery, searchAttributes, dataProvider)
        {
        }

        public override Task<EntityQueryResult> GetRecordsAsyncDecorator(EntityQuery query, CancellationToken cancelToken)
        {
            if (SearchStausIsOnline())
            {
                return DataHandler.GetOnlineRecordsWithReferencesAsync(query, cancelToken);
            }
            else
            {
                return base.GetRecordsAsyncDecorator(query, cancelToken);
            }
        }

        protected override Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken)
        {
            return GetRecordsAsyncDecorator(query, cancelToken);
        }
    }

    internal sealed class FunctionDataProviderDecorator : LovDataProviderDecorator
    {
        private readonly FunctionDataSource _onlineFunctionDataSource;

        public FunctionDataProviderDecorator(ILogger logger, IMetadata metadata, IDataHandler dataHandler, EntityQuery baseQuery, IEnumerable<string> searchAttributes, ILovOnDemandDataDecorator dataProvider, FunctionDataSource onlineDataSource)
            : base(logger, metadata, dataHandler, baseQuery, searchAttributes, dataProvider)
        {
            _onlineFunctionDataSource = onlineDataSource;
        }

        public override Task<EntityQueryResult> GetRecordsAsyncDecorator(EntityQuery query, CancellationToken cancelToken)
        {
            if (SearchStausIsOnline())
            {
                // In an Online Search the data source should change to the online function
                query.ChangeDataSource(_onlineFunctionDataSource);
            }

            return base.GetRecordsAsyncDecorator(query, cancelToken);
        }

        protected override Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken)
        {
            return GetRecordsAsyncDecorator(query, cancelToken);
        }
    }
    #endregion
}
