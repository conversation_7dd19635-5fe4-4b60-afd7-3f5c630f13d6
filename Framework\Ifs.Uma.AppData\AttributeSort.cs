﻿using System;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData
{
    public struct AttributeSort
    {
        public string AttributeName { get; }
        public ESortOrder SortOrder { get; }

        public AttributeSort(string attributeName, ESortOrder sortOrder)
        {
            AttributeName = attributeName;
            SortOrder = sortOrder;
        }

        internal int Compare(EntityDataSource dataSource, EntityRecord x, EntityRecord y)
        {
            if (dataSource == null) throw new ArgumentNullException(nameof(dataSource));
            if (x == null) throw new ArgumentNullException(nameof(x));
            if (y == null) throw new ArgumentNullException(nameof(y));

            AttributePathInfo attribute = AttributePathInfo.Get(dataSource.Metadata, dataSource.ProjectionName, dataSource.EntityName, AttributeName);

            if (attribute != null)
            {
                IComparable left = x[attribute] as IComparable;
                IComparable right = y[attribute] as IComparable;
                int compare = Compare(left, right);
                return SortOrder == ESortOrder.Descending ? -compare : compare;
            }

            return 0;
        }

        internal static int Compare(IComparable left, IComparable right)
        {
            if (left == null)
            {
                return right == null ? 0 : -1;
            }
            else if (right == null)
            {
                return 1;
            }
            else
            {
                return left.CompareTo(right);
            }
        }
    }
}
