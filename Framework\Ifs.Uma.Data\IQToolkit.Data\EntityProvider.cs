﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace IQToolkit.Data
{
    using Common;
    using TypeCode = Ifs.Uma.Utility.TypeCode;

    /// <summary>
    /// A LINQ IQueryable query provider that executes database queries over a DbConnection
    /// </summary>
    internal abstract class EntityProvider : QueryProvider, IEntityProvider, ICreateExecutor
    {
        private QueryLanguage language = null;
        private IMetaModel model;
        private QueryPolicy policy;
        private TextWriter log;
        private QueryCache cache;

        protected EntityProvider(IMetaModel model, QueryLanguage language)
        {
            if (model == null) throw new ArgumentNullException("model");
            
            policy = QueryPolicy.Default;

            this.model = model;
            this.language = language;
            this.cache = QueryCache.Instance;
        }

        public IMetaModel Model
        {
            get { return this.model; }
        }

        public QueryLanguage Language
        {
            get { return this.language; }
        }

        public QueryPolicy Policy
        {
            get { return this.policy; }
            set
            {
                if (value == null)
                {
                    this.policy = QueryPolicy.Default;
                }
                else
                {
                    this.policy = value;
                }
            }
        }

        public TextWriter Log
        {
            get { return this.log; }
            set { this.log = value; }
        }

        public QueryCache Cache
        {
            get { return this.cache; }
            set { this.cache = value; }
        }
        
        public bool CanBeEvaluatedLocally(Expression expression)
        {
            return this.Model.CanBeEvaluatedLocally(expression);
        }

        public virtual bool CanBeParameter(Expression expression)
        {
            if (expression == null) throw new ArgumentNullException("expression");
            Type type = TypeHelper.GetNonNullableType(expression.Type);
            return TypeHelper.GetTypeCode(type) != TypeCode.Object;
        }

        protected abstract QueryExecutor CreateExecutor();

        QueryExecutor ICreateExecutor.CreateExecutor()
        {
            return this.CreateExecutor();
        }

        public override string GetQueryText(Expression expression)
        {
            Expression plan = this.GetExecutionPlan(expression);
            var commands = CommandGatherer.Gather(plan).Select(c => c.CommandText).ToArray();
            return string.Join("\n\n", commands);
        }

        class CommandGatherer : DbExpressionVisitor
        {
            List<QueryCommand> commands = new List<QueryCommand>();

            public static ReadOnlyCollection<QueryCommand> Gather(Expression expression)
            {
                var gatherer = new CommandGatherer();
                gatherer.Visit(expression);
                return gatherer.commands.AsReadOnly();
            }

            protected override Expression VisitConstant(ConstantExpression c)
            {
                if (c == null) throw new ArgumentNullException("c");
                QueryCommand qc = c.Value as QueryCommand;
                if (qc != null)
                {
                    this.commands.Add(qc);
                }
                return c;
            }
        }

        public string GetQueryPlan(Expression expression)
        {
            Expression plan = this.GetExecutionPlan(expression);
            return DbExpressionWriter.WriteToString(plan);
        }

        protected virtual QueryTranslator CreateTranslator()
        {
            return new QueryTranslator(this.language, this.model, this.policy);
        }

        public void TransactionA(Action<DbCommand> act)
        {
            if (act == null) throw new ArgumentNullException("act");
            TransactionF((cmd) =>
            {
                act(cmd);
                return 0;
            });
        }

        public abstract T TransactionF<T>(Func<DbCommand, T> func);

        public void CommandA(Action<DbCommand> act)
        {
            if (act == null) throw new ArgumentNullException("act");
            CommandF((cmd) =>
            {
                act(cmd);
                return 0;
            });
        }

        public abstract T CommandF<T>(Func<DbCommand, T> func);

        public abstract bool Executing();
        public abstract bool ExecutingTransaction();

        /// <summary>
        /// Execute the query expression (does translation, etc.)
        /// </summary>
        /// <param name="expression"></param>
        /// <returns></returns>
        protected override object ExecuteInner(Expression expression)
        {
            if (expression == null) throw new ArgumentNullException("expression");
            LambdaExpression lambda = expression as LambdaExpression;

            if (lambda == null && this.cache != null && expression.NodeType != ExpressionType.Constant)
            {
                return this.cache.Execute(expression);
            }

            Expression plan = this.GetExecutionPlan(expression);

            if (lambda != null)
            {
                // compile & return the execution plan so it can be used multiple times
                LambdaExpression fn = Expression.Lambda(lambda.Type, plan, lambda.Parameters);
                return ExpressionUtils.CreateDelegate(fn);
            }
            else
            {
                // compile the execution plan and invoke it
                Expression<Func<object>> efn = Expression.Lambda<Func<object>>(Expression.Convert(plan, typeof(object)));
                return ExpressionUtils.Eval(efn);
            }
        }

        /// <summary>
        /// Convert the query expression into an execution plan
        /// </summary>
        /// <param name="expression"></param>
        /// <returns></returns>
        public virtual Expression GetExecutionPlan(Expression expression)
        {
            // strip off lambda for now
            LambdaExpression lambda = expression as LambdaExpression;
            if (lambda != null)
                expression = lambda.Body;

            QueryTranslator translator = this.CreateTranslator();

            // translate query into client & server parts
            Expression translation = translator.Translate(expression);

            var parameters = lambda != null ? lambda.Parameters : null;
            Expression provider = Find(expression, parameters, typeof(EntityProvider));
            if (provider == null)
            {
                Expression rootQueryable = Find(expression, parameters, typeof(IQueryable));
                provider = Expression.Property(rootQueryable, typeof(IQueryable).GetRuntimeProperty("Provider"));
            }

            return translator.Police.BuildExecutionPlan(translation, provider);
        }

        private static Expression Find(Expression expression, IList<ParameterExpression> parameters, Type type)
        {
            if (parameters != null)
            {
                Expression found = parameters.FirstOrDefault(p => type.GetTypeInfo().IsAssignableFrom(p.Type.GetTypeInfo()));
                if (found != null)
                    return found;
            }
            return TypedSubtreeFinder.Find(expression, type);
        }
    }
}
