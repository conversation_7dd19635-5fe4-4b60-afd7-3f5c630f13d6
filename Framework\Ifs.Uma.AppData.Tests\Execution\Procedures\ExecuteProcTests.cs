﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteProcTests : ProcedureTest
    {
        [Test]
        public async Task BasicProc()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "BasicProc", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("TestProcValue", result.Value);
        }

        [Test]
        public async Task SubProc()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "SubProcOuter", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("TestProcValue2", result.Value);
        }

        [Test]
        public async Task Params()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            var parameters = new Dictionary<string, object> { ["TestParam1"] = "TestProcValue3" };
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "ParamsOuter", parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("TestProcValue3", result.Value);
        }

        [Test]
        public async Task GetPlatformTime()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "GetPlatformTime", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.IsTrue(result.Value is DateTime);

            DateTime returned = (DateTime)result.Value;
            DateTime now = DateTime.Now;
            Assert.IsTrue(Math.Abs(now.Subtract(returned).TotalSeconds) < 5);
        }

        [Test]
        public async Task Scope()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "ScopeOuter", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("ScopeOuterValue", result.Value);
        }

        [Test]
        public async Task NoReturnValue()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "NoReturnValue", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual(ExecuteResult.None, result);
        }

        [Test]
        public async Task ReturnInResult()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "ReturnInResult", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreEqual("ReturnVal1", result.Value);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteProcSchema", null);
        }
    }
}
