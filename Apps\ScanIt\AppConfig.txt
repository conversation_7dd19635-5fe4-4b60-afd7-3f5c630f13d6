// Shared
Platforms=iOS,Android,Windows
Name=IFS Scan It
AppName=ScanIt
RedirectUri=ifsscanit
RemoteAssistance=false
SignatureService=false
LocationEnabled=false
LidarService=false
PushNotification=false

// iOS
iOSDisplayName=IFS Scan It
// Below must be the one having the InHouse suffix, the build script will remove it when building the store app
BundleIdentifier=com.ifs.cloud.ScanIt.InHouse
BundleName=IFS Scan It

// NOTE: iOS usage descriptions are always needed since we don't remove code for things like media attachments and location APIs
// So it's okay to add some dummy text for apps that don't really use those features
NSLocationWhenInUseUsageDescription=This application requires location services to work
NSLocationAlwaysAndWhenInUseUsageDescription=This application requires location services to work
NSCameraUsageDescription=This application requires access to the camera to scan barcodes
NSPhotoLibraryUsageDescription=This app needs access to photos for adding media and document attachments
NSPhotoLibraryAddUsageDescription=TThis app needs access to save media and document attachments
NSMicrophoneUsageDescription=This is used for remote assistance calls.

// Android
AndroidDisplayName=IFS Scan It
AndroidPackageName=com.ifs.cloud.ScanIt

// Windows
WindowsDisplayName=IFS Scan It
WindowsDescription=IFS Scan It
WindowsShortName=IFS Scan It
IdentityName=IFS.IFSScanIt
PhoneProductId=94de8054-d6f5-4b7e-be29-9262d4d372e9
// Below are in Package.StoreAssociation and Package.xml
ReservedName=IFS Scan It
LandingUrl=https://developer.microsoft.com/dashboard/Application?appId=9WZDNCRDNTBS