{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_ToLower>": {"name": "String_ToLower", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "<PERSON><PERSON><PERSON><PERSON>", "paramsArray": ["${TextInput}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}