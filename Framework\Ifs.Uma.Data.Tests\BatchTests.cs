﻿using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using IQToolkit.Data.Common;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;

namespace Ifs.Uma.Data.Tests
{
    [TestFixture]
    public class BatchTests : DataContextTest<DataContext>
    {
        [Test]
        public void Batch()
        {
            DataContext dc = CreateDataContext();

            AddRows(dc);

            var tc = dc.TransitionRows.Where(x => x.Operation == OperationType.Update);

            var stream = tc.ToBatch();
            
            List<TransitionRow> rows = new List<TransitionRow>();
            foreach (var row in stream)
            {
                rows.Add(row);
            }

            Assert.AreEqual(rows.Count, 80);
        }

        [Test]
        public void BatchOnTable()
        {
            DataContext dc = CreateDataContext();

            AddRows(dc);

            var stream = dc.TransitionRows.ToBatch();

            List<TransitionRow> rows = new List<TransitionRow>();
            foreach (var row in stream)
            {
                rows.Add(row);
            }

            Assert.AreEqual(rows.Count, 100);
        }

        private void AddRows(DataContext dc)
        {
            for (int i = 0; i < 100; i++)
            {
                TransitionRow row = new TransitionRow();
                dc.TransitionRows.InsertOnSubmit(row);
                row.TableName = "test_" + i;
                row.Operation = (i % 5 < 4) ? OperationType.Update : OperationType.Insert;
            }

            dc.SubmitChanges(false);
        }
    }
}
