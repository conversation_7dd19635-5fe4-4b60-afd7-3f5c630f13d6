{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_TrimStart>": {"name": "String_TrimStart", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "TrimStart", "paramsArray": ["${TextInput}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<String_TrimStart2>": {"name": "String_TrimStart", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "TrimParameter", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "TrimStart", "paramsArray": ["${TextInput}", "${TrimParameter}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}