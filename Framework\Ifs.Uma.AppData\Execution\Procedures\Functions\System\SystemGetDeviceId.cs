﻿using Ifs.Uma.AppData.Online;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemGetDeviceId : SystemFunction
    {
        public const string FunctionName = "GetDeviceId";
        private readonly IOnlineDataHandler _onlineDataHandler;

        public SystemGetDeviceId(IOnlineDataHandler onlineDataHandler)
            : base(FunctionName, 0)
        {
            _onlineDataHandler = onlineDataHandler;
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return _onlineDataHandler?.DeviceId.ToString();
        }
    }
}
