﻿using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Messages
{
    [Table(Name = FwDataContext.FwTablePrefix + "message_in", Class = MetaTableClass.App)]
    public class MessageInRow
    {
        [Column(AutoIncrement = true, PrimaryKey = true)]
        public long RowId { get; set; }

        [Column]
        public long ServerMessageId { get; set; }

        [Column]
        public long? ClientRelatedMessageId { get; set; }

        [Column]
        public MessageType MessageType { get; set; }

        [Column]
        public string Message { get; set; }

        [Column]
        public long TransactionId { get; set; }
    }
}
