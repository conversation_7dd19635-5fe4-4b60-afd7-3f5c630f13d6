﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Attachments;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Pages
{
    public class WorkflowPage : PageBase
    {
        private readonly IMetadata _metadata;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IDataHandler _dataHandler;
        private readonly IRoamingProfile _roamingProfile;
        private readonly IPageCreator _pageCreator;
        private readonly IExpressionRunner _expressionRunner;
        private readonly IInsightsLogger _insightsLogger;
        private readonly IPerfLogger _perfLogger;

        private int _stepIndex;
        private List<KeyValuePair<int, CpiWorkflowStep>> _steps = new List<KeyValuePair<int, CpiWorkflowStep>>();

        private CpiWorkflow _workflow;

        public int? SequenceNo
        {
            get
            {
                PageData.ViewState.TryGetValue(_workflow.Name + ".sequenceNo", out object valueHolder);

                if (valueHolder != null)
                {
                    return Convert.ToInt32(valueHolder);
                }

                return null;
            }
            set
            {
                PageData.ViewState.Assign(_workflow.Name + ".sequenceNo", value);
            }
        }

        public string StepName
        {
            get
            {
                PageData.ViewState.TryGetValue(_workflow.Name + ".stepName", out object valueHolder);

                if (valueHolder != null)
                {
                    return valueHolder.ToString();
                }

                return null;
            }
            set
            {
                PageData.ViewState.Assign(_workflow.Name + ".stepName", value);
            }
        }

        private CpiWorkflowStep _previousStep;
        public CpiWorkflowStep PreviousStep
        {
            get => _previousStep;
            set
            {
                SetProperty(ref _previousStep, value);
            }
        }

        private CpiWorkflowStep _nextStep;
        public CpiWorkflowStep NextStep
        {
            get => _nextStep;
            set
            {
                SetProperty(ref _nextStep, value);
            }
        }

        private UmaColor _backgroundColor;
        public UmaColor BackgroundColor
        {
            get { return _backgroundColor; }
            set { SetProperty(ref _backgroundColor, value); }
        }

        private UmaColor _foregroundColor;
        public UmaColor ForegroundColor
        {
            get { return _foregroundColor; }
            set { SetProperty(ref _foregroundColor, value); }
        }

        private CpiWorkflowStep _currentStep;
        public CpiWorkflowStep CurrentStep
        {
            get => _currentStep;
            private set
            {
                SetProperty(ref _currentStep, value);

                if (_currentStep != null)
                {
                    StepName = _currentStep.Name;

                    if (_steps != null)
                    {
                        SequenceNo = _steps[_stepIndex].Key;
                    }
                }
                else
                {
                    SequenceNo = null;
                    StepName = null;
                }
            }
        }

        private double _progressTotal;
        public double ProgressTotal
        {
            get => _progressTotal;
            set
            {
                SetProperty(ref _progressTotal, value);
            }
        }

        private string _stepProgressLabel;
        public string StepProgressLabel
        {
            get => _stepProgressLabel;
            set
            {
                SetProperty(ref _stepProgressLabel, value);
            }
        }

        private PageBase _contentPage;
        public PageBase ContentPage
        {
            get => _contentPage;
            set
            {
                if (_contentPage != null)
                {
                    _contentPage.PropertyChanged -= ContentPage_PropertyChanged;
                }

                SetProperty(ref _contentPage, value);

                if (_contentPage != null)
                {
                    _contentPage.PropertyChanged += ContentPage_PropertyChanged;
                }
            }
        }

        public Command PreviousCommand { get; }

        public Command NextCommand { get; }

        private CpiCommandItem _finishCommand;
        public CpiCommandItem FinishCommand
        {
            get => _finishCommand;
            set
            {
                SetProperty(ref _finishCommand, value);
            }
        }

        public PageData PageData { get; }

        public WorkflowPage(IEventAggregator eventAggregator, IDialogService dialogService, IMetadata metadata, PageCreator pageCreator, ICommandExecutor commandExecutor,
            IDataHandler data, ILogger logger, IPerfLogger perfLogger, IInsightsLogger insightsLogger, IRoamingProfile roamingProfile, IExpressionRunner expressionRunner)
            : base(eventAggregator, dialogService)
        {
            Classification = PageClassification.Workflow;

            _metadata = metadata;
            _commandExecutor = commandExecutor;
            _dataHandler = data;
            _roamingProfile = roamingProfile;
            _pageCreator = pageCreator;
            _expressionRunner = expressionRunner;
            _insightsLogger = insightsLogger;
            _perfLogger = perfLogger;

            PreviousCommand = Command.FromAsyncMethod(GoToPreviousStep);
            NextCommand = Command.FromAsyncMethod(GoToNextStep);

            BackgroundColor = UmaColors.IfsBlueLight.ColorWithAlpha((float)0.2);
            ForegroundColor = UmaColors.IfsBlue;

            PageData = new PageData(logger, metadata, data);
            PageData.Page = this;
        }

        public override async void OnNavigatedTo(NavigatedToArgs args)
        {
            base.OnNavigatedTo(args);

            MetadataPageNavParam param = args.GetNavParam<MetadataPageNavParam>();

            if (args.NavigationMode != NavigationMode.Back)
            {
                await LoadPageAsync(param);
            }
            else if (args.NavigationMode == NavigationMode.Back && (ContentPage.Classification == PageClassification.MediaList || ContentPage.Classification == PageClassification.DocumentList))
            {
                await LoadPageFromStep(CurrentStep);
            }
            else
            {
                if (ContentPage != null)
                {
                    ContentPage.OnNavigatedTo(args);
                }
            }
        }

        private void ContentPage_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ContentPage.HasChanges))
            {
                PreviousCommand.IsEnabled = !ContentPage.HasChanges;
                NextCommand.IsEnabled = !ContentPage.HasChanges;

                if (FinishCommand != null)
                {
                    FinishCommand.IsEnabled = !ContentPage.HasChanges;
                }
            }
        }

        private void UpdateWorkflowSteps()
        {
            PreviousStep = _stepIndex != 0 ? _steps[_stepIndex - 1].Value : null;
            NextStep = _stepIndex != _steps.Count - 1 ? _steps[_stepIndex + 1].Value : null;

            if (FinishCommand != null)
            {
                FinishCommand.IsVisible = (NextStep == null);
            }

            int currentStepNumber = _stepIndex + 1;
            StepProgressLabel = currentStepNumber + "/" + _steps.Count;
            ProgressTotal = Math.Round((100.0 / _steps.Count) * currentStepNumber, 0);
        }

        private async Task GoToPreviousStep()
        {
            if (_currentStep.PreviousCommand != null)
            {
                ExecuteResult result = await _commandExecutor.ExecuteAsync(ProjectionName, PageData.DefaultViewData, _currentStep.PreviousCommand);

                if (result.Value == null || !result.Value.Equals(ExecuteResult.Ok.Value))
                {
                    return;
                }
            }

            _stepIndex -= 1;
            await LoadPageFromStep(PreviousStep);
        }

        private async Task GoToNextStep()
        {
            if (_currentStep.NextCommand != null)
            {
                ExecuteResult result = await _commandExecutor.ExecuteAsync(ProjectionName, PageData.DefaultViewData, _currentStep.NextCommand);

                if (result.Value == null || !result.Value.Equals(ExecuteResult.Ok.Value))
                {
                    return;
                }
            }

            _stepIndex += 1;
            await LoadPageFromStep(NextStep);
        }

        private async Task LoadRecord()
        {
            EntityQuery query = new EntityQuery(PageData.DataSource);

            HashSet<string> selectAttributes = (PageData.SelectAttributes != null) ? new HashSet<string>(PageData.SelectAttributes) : new HashSet<string>();

            if (_workflow.InitCommand != null)
            {
                AttributeFinder.FindInCommand(selectAttributes, _metadata, ProjectionName, _workflow.InitCommand);
            }

            if (_workflow.FinishCommand != null)
            {
                AttributeFinder.FindInCommand(selectAttributes, _metadata, ProjectionName, _workflow.FinishCommand);
            }

            // Add attributes from the dynamic set up command
            if (_workflow.DynamicSetupParams != null)
            {
                foreach (object param in _workflow.DynamicSetupParams.Values)
                {
                    selectAttributes.Add(AttributeFinder.ExtractAttributeName(param.ToString()));
                }
            }

            // Add attributes required for each workflow step
            if (_workflow.Steps != null)
            {
                foreach (CpiWorkflowStep step in _workflow.Steps)
                {
                    AttributeFinder.FindInWorkflowStep(selectAttributes, _metadata, ProjectionName, step);
                }
            }

            if (selectAttributes.Count > 0)
            {
                query.SelectAttributes = selectAttributes;
            }

            PageData.Filter?.Apply(query);
            await PageData.DefaultViewData.Record.LoadRecordAsync(query, false);
        }

        private async Task LoadPageFromStep(CpiWorkflowStep step)
        {
            string projectionName = step.Args?.Projection ?? step.Args.Client ?? ProjectionName;

            PreviousCommand.IsEnabled = false;
            NextCommand.IsEnabled = false;

            if (FinishCommand != null)
            {
                FinishCommand.IsEnabled = false;
            }

            NavigationParameter navParam = null;

            string pageName = step.Args?.PageName ?? step.Args?.Page;

            if (pageName != null)
            {
                if (pageName == MediaListPage.PageName || pageName == DocumentListPage.PageName ||
                    pageName == MediaDetailPage.PageName || pageName == DocumentDetailPage.PageName || pageName == DocumentListPage.NoRevPageName)
                {
                    ObjPrimaryKey key = PageData.DefaultViewData.Record.ToPrimaryKey();
                    string keyRef = key?.ToKeyRef();
                    if (pageName == DocumentListPage.NoRevPageName)
                    {
                        navParam = new AttachmentNavParam(pageName, PageData.DefaultViewData.Record.EntityName, keyRef, false);
                    }
                    else
                    {
                        navParam = new AttachmentNavParam(pageName, PageData.DefaultViewData.Record.EntityName, keyRef, true);
                    }
                }
                else
                {
                    PageValues filters = PageValues.FromColumnValues(step.Args.Columns, PageData.DefaultViewData.Record);
                    navParam = new MetadataPageNavParam(projectionName, pageName, filters);
                }
            }
            else if (step.Args?.Assistant != null)
            {
                PageValues actionParams = PageValues.FromRecordData(step.Args.Parameters, PageData.DefaultViewData.Record);
                navParam = new MetadataPageNavParam(projectionName, step.Args.Assistant, step.Args.Action, actionParams);
            }
            else if (step.Args?.Tree != null)
            {
                bool openSelector = _expressionRunner.RunCheck(step.Args.OpenSelector, PageData.DefaultViewData, false);
                PageValues filters = PageValues.FromRecordData(step.Args.Filters, PageData.DefaultViewData.Record);
                navParam = new MetadataPageNavParam(projectionName, step.Args.Tree, openSelector, filters);
            }

            if (navParam != null && _pageCreator.CreatePage(navParam) is PageBase page)
            {
                await page.LoadPageAsync(navParam);

                if (page is MediaListPage mediaListPage && mediaListPage.ListData?.AddNew != null)
                {
                    mediaListPage.ListData.AddNew.IsEnabled = false;
                }
                else if (page is DocumentListPage documentListPage && documentListPage.ListData?.AddNew != null)
                {
                    documentListPage.ListData.AddNew.IsEnabled = false;
                }

                CurrentStep = step;
                UpdateWorkflowSteps();
                page.IsSubPage = true;
                ContentPage = page;
            }
            else
            {
                throw new InvalidOperationException($"Could not create page for workflow step {step.Name}");
            }

            PreviousCommand.IsEnabled = true;
            NextCommand.IsEnabled = true;

            if (FinishCommand != null)
            {
                FinishCommand.IsEnabled = true;
            }
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter param)
        {
            MetadataPageNavParam navParam = param as MetadataPageNavParam;
            if (navParam == null)
            {
                return false;
            }

            ProjectionName = navParam.ProjectionName;
            _workflow = _metadata.FindWorkflow(ProjectionName, navParam.WorkflowName);

            if (_workflow != null)
            {
                using (_perfLogger.Track("PageLoad", ProjectionName + "." + _workflow.Name))
                {
                    Name = _workflow.Name;

                    _insightsLogger?.TrackAppFeature("Workflow-" + ProjectionName + "." + _workflow.Name);
                    Logger.Trace("Navigate: Workflow-" + ProjectionName + "." + _workflow.Name);

                    EntityDataSource dataSource = EntityDataSource.FromEntity(_metadata, ProjectionName, _workflow.Entity);

                    PageData.PageSettings = _roamingProfile.GetSettings("Workflows", ProjectionName, navParam.WorkflowName);
                    PageData.Filter = navParam.Filter;
                    PageData.DataSource = dataSource;

                    await LoadRecord();

                    Workflow.Workflow wf = new Workflow.Workflow(_dataHandler, PageData?.DefaultViewData, ProjectionName, _workflow);
                    _steps = await wf.GetAvailableWorkflowSteps();

                    CpiWorkflowStep step = null;
                    if (_steps != null && _steps.Any())
                    {
                        KeyValuePair<int, CpiWorkflowStep> startStep = _steps.FirstOrDefault(x => x.Key == navParam.WorkflowStartSequence.GetValueOrDefault());
                        _stepIndex = _steps.IndexOf(startStep);
                        step = startStep.Value;
                    }

                    if (_workflow.FinishCommand != null)
                    {
                        FinishCommand = new WorkflowFinishCommand(_commandExecutor, _expressionRunner, ProjectionName, _workflow.FinishCommand, DialogService);

                        if (string.IsNullOrEmpty(_workflow.FinishCommand.Label))
                        {
                            FinishCommand.Text = Strings.Finish;
                        }

                        FinishCommand.PageData = PageData;
                    }

                    if (step != null)
                    {
                        await LoadPageFromStep(step);
                    }
                    else if (_steps != null && _steps.Any())
                    {
                        throw new InvalidOperationException($"The workflow step with sequence {navParam.WorkflowStartSequence} could not be found in workflow {navParam.WorkflowName}");
                    }

                    await PageData.WaitForBackgroundTasks();

                    return true;
                }
            }
            else
            {
                Logger.Error($"Could not find workflow {ProjectionName}.{navParam.WorkflowName}");
                return false;
            }
        }

        protected override async void OnStoredDataChangedAsync(DataChangeSet changeSet)
        {
            base.OnStoredDataChangedAsync(changeSet);

            RemoteRow row = PageData.DefaultViewData?.Record?.GetRemoteRow();

            if (row != null && changeSet.EffectedTables.Select(x => x.TableName).Contains(row.TableName))
            {
                await PageData.DefaultViewData.Record.ReloadRecordAsync();
            }
        }

        private class WorkflowFinishCommand : CpiCommandItem
        {
            private readonly CpiCommand _finishCommand;
            private readonly IDialogService _dialogService;

            public WorkflowFinishCommand(ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, string projectionName, CpiCommand command, IDialogService dialogService)
                : base(commandExecutor, expressionRunner, projectionName, command)
            {
                _finishCommand = command;
                _dialogService = dialogService;
            }

            protected override async Task OnExecuteAsync()
            {
                bool doFinish = true;

                if (!string.IsNullOrEmpty(_finishCommand.Message))
                {
                    doFinish = await _dialogService.CustomButtons(_finishCommand.Label, _finishCommand.Message, Strings.Yes, Strings.No) == CustomButtonsResult.Positive;
                }

                if (doFinish)
                {
                    await base.OnExecuteAsync();
                }
            }
        }
    }
}
