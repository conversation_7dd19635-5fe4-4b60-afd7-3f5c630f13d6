﻿using Ifs.Uma.AppData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Framework.UI.Lists;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Prism.Events;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.UI.Data;
using Ifs.Uma.UI.Lists;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Data;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Address;
using Ifs.Uma.Database;

namespace Ifs.Uma.Framework.UI.Elements.Lists
{
    internal sealed class ListElementListData : CardListData<ListElementItem>
    {
        private readonly IDataHandler _data;
        private readonly ILogger _logger;
        private readonly IMetadata _metadata;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IAddressHandler _addressHandler;

        private readonly ListElement _element;
        private readonly CpiList _list;
        private readonly CpiCard _card;

        public IEnumerable<CpiKeyValue> OrderBy { get; set; }

        private AddressPresentation[] _addressPresentations;
        public AddressPresentation[] AddressPresentations
        {
            get => _addressPresentations;
            set
            {
                if (_addressPresentations != value)
                {
                    _addressPresentations = value;
                    UpdateCardDefsWithAddressData();
                }
            }
        }

        private bool _isCommandRunning;
        private bool _updateAfterCommand;

        public ListElementListData(IEventAggregator eventAggregator, IMetadata metadata, IDataHandler data, ILogger logger,
            ICardDefCreator cardDefCreator, ICommandExecutor commandExecutor, IAddressHandler addressHandler,
            ListElement element, CpiList list, CpiCard card)
            : base(eventAggregator)
        {
            _data = data;
            _logger = logger;
            _element = element;
            _metadata = metadata;
            _commandExecutor = WrappedCommandExecutor.Create(commandExecutor, BeforeCommand, AfterCommand);
            _list = list;
            _card = card;
            _addressHandler = addressHandler;

            ItemCardDef = (card == null) ?
                 cardDefCreator.CreateCardDef(_element.ProjectionName, list, GetListItemValue, _commandExecutor, _element.PageData.IsReport) :
                 cardDefCreator.CreateCardDef(_element.ProjectionName, card, list, GetListItemValue, _commandExecutor);

            if (card != null)
            {
                // If the layout comes from a card we can allow editing in lists
                ItemCardDef.AllowEditing = true;
            }

            OrderBy = list.OrderBy;
        }

        private void UpdateCardDefsWithAddressData()
        {
            IEnumerable<CardDefItem> cardDefItems = ItemCardDef.Items.Where(c => !string.IsNullOrWhiteSpace(c.Field?.Label));

            for (int i = 0; i < cardDefItems.ToList().Count; i++)
            {
                // TODO: Added condition to check for AddressField and update main ItemCardDef.Items 
                // for taking the _addressPresentations passed - https://ifsdev.atlassian.net/browse/MOBOFF-15846
                if (ItemCardDef.Items[i].Field != null && ItemCardDef.Items[i].Field.Control == CpiControlType.AddressField)
                {
                    ItemCardDef.Items[i].AddressPresentations = _addressPresentations;
                }
            }
        }

        private object GetListItemValue(AttributePath attribute, object dataContext)
        {
            ListElementItem l = (ListElementItem)dataContext;
            return l.Record[attribute];
        }

        private Task<ExecuteResult> BeforeCommand(ViewData data, CpiCommand command, CommandOptions options)
        {
            _isCommandRunning = true;
            _updateAfterCommand = false;
            UpdateWrappedCommandExecution(true);
            data.Record.GetRemoteRow().EntitySetName = data.Record.GetRemoteRow().EntitySetName ?? _element.DataSource.EntitySetName;

            if (_element.DataSource is ArrayDataSource)
            {
                ArrayDataSource ads = _element.DataSource as ArrayDataSource;
                string arrayKey = ObjPrimaryKey.FromPrimaryKeyQueryParams(_metadata.MetaModel, data.Record.GetRemoteRow(), data.Record.ProjectionName);

                data.Record.GetRemoteRow().ArraySourceName = ads.ArrayPropertyName + arrayKey;
                data.Record.GetRemoteRow().PrimaryKeyString = ads.ParentKey.ToFormattedKeyRef(_element.DataSource.ProjectionName);
            }

            return Task.FromResult<ExecuteResult>(null);
        }

        private async Task<ExecuteResult> AfterCommand(ViewData data, CpiCommand command, CommandOptions options, ExecuteResult result)
        {
            _isCommandRunning = false;
            UpdateWrappedCommandExecution(false);
            if (data is ListElementItem item && item.Record.HasChanges && !IsValidateCommand(item, command))
            {
                // Calling ValidateAndSaveAsync for the validate command will cause an infinite loop
                await item.ValidateAndSaveAsync();
                await UpdateAsync();
            }
            else if (data is CardData cardData && cardData.EditableField == null)
            {
                await UpdateAsync();
            }

            if (!result.Failed)
            {
                ObjPrimaryKey primaryKey = data.Record.ToPrimaryKey();
                if (primaryKey != null)
                {
                    foreach (ListElementItem item1 in Items)
                    {
                        ObjPrimaryKey itemPrimaryKey = item1.Record.ToPrimaryKey();
                        if (itemPrimaryKey.Equals(primaryKey))
                        {
                            if (_updateAfterCommand)
                            {
                                ScrollToItem(item1);
                            }

                            CommandExecuted(item1);

                            break;
                        }
                    }
                }
            }

            return result;
        }

        private bool IsValidateCommand(ListElementItem item, CpiCommand command)
        {
            CardDefItem editableItem = item.EditableField?.Tag as CardDefItem;

            if (editableItem?.Field?.ValidateCommand != null && command == editableItem.Field.ValidateCommand)
            {
                return true;
            }

            return false;
        }

        public override async Task UpdateAsync()
        {
            _element.ListData?.SetFilterBarHidden(_list?.HideFilter);

            if (_isCommandRunning)
            {
                _updateAfterCommand = true;
            }
            else
            {
                await base.UpdateAsync();
            }
        }

        public override void OnItemCardDefValueChanged()
        {
            SortOptions.Clear();
            // Add "None" option
            SortOptions.Add(ListSortOption.None);

            foreach (CardDefItem cardDefItem in ItemCardDef.Items.Where(CanSortOnCardDefItem))
            {
                ListSortOption listSortOption = new ListSortOption(cardDefItem.Field.Attribute, cardDefItem.Field.Label);
                SortOptions.Add(listSortOption);
            }

            SortOptions.Settings = _element.GetSettings();
            SortOptions.LoadFromSettings(false);
        }

        private bool CanSortOnCardDefItem(CardDefItem item)
        {
            if (item.Field == null)
            {
                return false;
            }

            if (item.Field.Control == CpiControlType.Image ||
                item.Field.Control == CpiControlType.ComputedField ||
                item.Field.Control == CpiControlType.AddressField)
            {
                return false;
            }

            if (string.IsNullOrEmpty(item.Field?.Label))
            {
                return false;
            }

            if (item.Field.Label.Contains("${"))
            {
                return false;
            }

            return true;
        }

        protected override void OnSelectedItemChanged()
        {
            base.OnSelectedItemChanged();

            ListElementItem item = SelectedItem;
            if (item != null)
            {
                Task ignored = _element.NavigateToItemAsync(item);
                SelectedItem = null;
            }
        }

        protected override async Task OnUpdateAsync(CancellationToken cancelToken)
        {
            foreach (ListElementItem item in Items)
            {
                if (item.EditableField != null && item.Record.HasChanges)
                {
                    await item.ValidateAndSaveAsync();
                }
            }

            EntityDataSource dataSource = _element.DataSource;
            if (dataSource != null)
            {
                dataSource.EntitySetName = dataSource.EntitySetName ?? _element.PageData?.DataSource?.EntitySetName;
                EntityQuery query = new EntityQuery(dataSource);
                query.SelectAttributes = _element.GetListSelectAttributes();
                AddressPresentations = await _addressHandler.GetAllAddressPresentations();
                // User selected sort option
                List<ListSortOption> selectedSortOptions = SortOptions?.Where(s => s.SortOrder != null).ToList();
                if (selectedSortOptions != null)
                {
                    foreach (ListSortOption sortOption in selectedSortOptions)
                    {
                        if (!sortOption.IsNone && sortOption.SortOrder.HasValue)
                        {
                            query.AddSort(sortOption.Id, sortOption.SortOrder.Value);
                        }
                    }
                }

                // Client developer sort option
                if (OrderBy != null)
                {
                    if (!selectedSortOptions.Any() && OrderBy.Any())
                    {
                        string orderByKey = string.Join(", ", OrderBy.Select(kv => kv.Key));
                        string orderByValue = string.Join(", ", OrderBy.Select(kv => kv.Value));
                        var optionToUpdate = SortOptions.FirstOrDefault(s => s.Id == orderByKey);
                        if (optionToUpdate != null)
                        {
                            optionToUpdate.SortOrder = string.Equals(orderByValue, "desc", StringComparison.OrdinalIgnoreCase) ? ESortOrder.Descending : ESortOrder.Ascending;
                            selectedSortOptions.Add(optionToUpdate);
                        }
                        else
                        {
                            ListSortOption listSortOption = new ListSortOption(orderByKey, orderByKey, string.Equals(orderByValue, "desc", StringComparison.OrdinalIgnoreCase) ? ESortOrder.Descending : ESortOrder.Ascending);
                            SortOptions.Add(listSortOption);
                            selectedSortOptions.Add(listSortOption);
                        }

                        var noneOption = SortOptions.FirstOrDefault(option => option.IsNone);
                        if (noneOption != null)
                        {
                            SortOptions.Remove(noneOption);
                        }
                    }

                    query.AddSorts(OrderBy);
                }

                if (!string.IsNullOrWhiteSpace(SearchTerm))
                {
                    query.Search = new AttributeSearch(GetListSearchAttributes(), SearchTerm);
                }

                if (dataSource == _element?.PageData.DataSource)
                {
                    _element?.PageData.Filter?.Apply(query);
                }

                IDataLoader<EntityRecord> recordLoader = new EntityQueryDataLoader(_data, query);
                IDataLoader<ListElementItem> itemLoader = recordLoader.Select(EntityRecordToListElementItem);

                await LoadDataLoaderItemsAsync(itemLoader, true, cancelToken, null);
            }
            else
            {
                Items.Clear();
            }
        }

        private string[] GetListSearchAttributes()
        {
            HashSet<string> attributes = new HashSet<string>();

            if (_card != null)
            {
                AttributeFinder.FindInCardForSearch(attributes, _card);
            }
            else
            {
                AttributeFinder.FindInListForSearch(attributes, _list);
            }

            return attributes.ToArray();
        }

        protected override async Task<bool> OnHandleUpdateException(Exception ex)
        {
            await _element.HandleException(ex);
            return true;
        }

        protected override async Task OnHandleUpdateOffline()
        {
            await _element.CheckExecuteResult(ExecuteResult.Offline, true);
        }

        private ListElementItem EntityRecordToListElementItem(EntityRecord record)
        {
            RecordData dataContext = new RecordData(_logger, _metadata, _data);
            dataContext.LoadRecord(_element.ProjectionName, record);
            ListElementItem item = new ListElementItem(_element?.PageData, dataContext, _element?.DataSource);
            item.Parent = _element?.ViewData;
            return item;
        }

        protected override async void OnSearchTermChanged()
        {
            await UpdateAsync();
        }

        protected override bool OnMatchSearchTerm(ListElementItem item)
        {
            // Always return true here since the search will be done against the database
            return true;
        }
    }
}
