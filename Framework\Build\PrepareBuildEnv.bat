echo off

rem Setup Environment for building
if defined VS_DIR (
	exit /B 0
)

setlocal enabledelayedexpansion

REM Setup default VS version, it is only used before upgrade complete.
if "%VS_VERSION%"=="" (
	set "VS_VERSION=2022"
)

if "%VS_VERSION%" NEQ "" (
	for /F "delims=" %%j in ('"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -property installationPath') do (
        echo %%j | findstr "%VS_VERSION%" >nul
		if !errorlevel! equ 0 (
			set "VS_DIR=%%j"
		)
    )
) else (
        for /f "delims=#" %%i in ('"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -latest -property installationPath') do (
        set "VS_DIR=%%i"
    )
)

endlocal & set "VS_DIR=%VS_DIR%"

set "PATH=%VS_DIR%\MSBuild\Current\bin;%PATH%"


where msbuild.exe 2>&1 > NUL

if "%ERRORLEVEL%" NEQ "0" (
    echo "Failed to find msbuild"
    exit /B 1
)
