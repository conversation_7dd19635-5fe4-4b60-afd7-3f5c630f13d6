using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.Utility;
using Ifs.Uma.Utility.Formatters;

namespace Ifs.Uma.Framework.UI.Elements
{
    public sealed class CpiBadge : Badge
    {
        private readonly IExpressionRunner _expressionRunner;
        private readonly IValueFormatter _valueFormatter;

        public CpiField FieldDef { get; }

        public CpiBadge(IMetadata metadata, IExpressionRunner expressionRunner, string projectionName, CpiField fieldDef)
        {
            _expressionRunner = expressionRunner;
            FieldDef = fieldDef;

            _valueFormatter = AttributeFormatter.For(metadata, projectionName, fieldDef);

            if (fieldDef.Style.HasValue)
            {
                ShowImage = fieldDef.Style.Value == CpiFieldStyle.IconAndText || fieldDef.Style.Value == CpiFieldStyle.IconOnly;
                ShowText = fieldDef.Style.Value == CpiFieldStyle.IconAndText || fieldDef.Style.Value == CpiFieldStyle.TextOnly;
            }

            if (ShowImage)
            {
                if (fieldDef.Icon == null)
                {
                    ShowImage = false;
                }
                else
                {
                    Image = IconUtils.Load(fieldDef.Icon);
                }
            }

            Color = UmaColors.IfsGrayDark;
        }

        public void UpdateStates(ViewData viewData)
        {
            string emphasis = _expressionRunner.GetEmphasis(FieldDef.OfflineEmphasis ?? FieldDef.Emphasis, viewData);
            Color = UmaColor.FromEmphasis(emphasis) ?? UmaColors.IfsGrayDark;
        }

        public override void UpdateTextFromValue(object value)
        {
            if (_valueFormatter == null)
            {
                base.UpdateTextFromValue(value);
            }
            else
            {
                Text = _valueFormatter.Format(value) ?? string.Empty;
            }
        }
    }
}
