﻿using System.Linq;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Attachment
{
    internal sealed class AttachmentGetDocFileExtensions : AttachmentFunction
    {
        public const string FunctionName = "GetDocFileExtensions";

        public AttachmentGetDocFileExtensions()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string[] extensions =
                context.DbDataContext.EdmApplications
                .Where(x => x.DocumentType == "ORIGINAL" || x.DocumentType == "VIEW")
                .Select(x => "." + x.FileExtention)
                .ToArray();

            return string.Join(",", extensions);
        }
    }
}
