﻿using System.Collections.Generic;
using System.Linq;
using System;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData
{
    public class AttributeSearch
    {
        public IEnumerable<string> Attributes { get; }
        public string SearchTerm { get; }

        public AttributeSearch(IEnumerable<string> attributes, string searchTerm)
        {
            if (attributes == null) throw new ArgumentNullException(nameof(attributes));
            if (searchTerm == null) throw new ArgumentNullException(nameof(searchTerm));

            Attributes = attributes.ToArray();
            SearchTerm = searchTerm;
        }
        
        internal AttributeExpression ToAttributeExpression(EntityDataSource dataSource)
        {
            AttributeExpression expression = null;

            EntityInfo entityInfo = EntityInfo.Get(dataSource.Metadata, dataSource.ProjectionName, dataSource.EntityName);

            foreach (string attributeName in Attributes)
            {
                AttributePathInfo attribute = AttributePathInfo.Get(dataSource.Metadata, dataSource.ProjectionName, dataSource.EntityName, attributeName);
                if (attribute == null || !CanSearchOnAttribute(attribute, entityInfo))
                {
                    continue;
                }

                AttributeExpression attrExpression = null;
                if (attribute.Member.ColumnType == typeof(string) && attribute.Member.Enumeration != null)
                {
                    string[] enumValues = attribute.Member.Enumeration.Values
                        .Where(x => (x.DisplayName ?? x.ServerValue).IndexOf(SearchTerm, StringComparison.CurrentCultureIgnoreCase) >= 0)
                        .Select(x => x.ServerValue)
                        .ToArray();

                    foreach (string enumValue in enumValues)
                    {
                        AttributeExpression enumValueExpression = AttributeExpression.Compare(attributeName, AttributeCompareOperator.Equals, enumValue);
                        attrExpression = (attrExpression == null) ? enumValueExpression : AttributeExpression.Or(attrExpression, enumValueExpression);
                    }
                }
                else
                {
                    attrExpression = AttributeExpression.Like(attributeName, "%" + SearchTerm + "%");
                }

                if (attrExpression != null)
                {
                    expression = (expression == null) ? attrExpression : AttributeExpression.Or(expression, attrExpression);
                }
            }

            return expression;
        }

        private static bool CanSearchOnAttribute(AttributePathInfo attribute, EntityInfo entityInfo)
        {
            // Ignore columns that are blobs
            if (attribute.Member.ColumnType == typeof(byte[]))
            {
                return false;
            }

            if (entityInfo?.SyncPolicy == EntitySyncPolicy.OnlineOnly &&
                attribute.Member.ColumnType == typeof(DateTime?))
            {
                // Ignore columns that are date, times, and timestamps for online queries
                return false;
            }

            return true;
        }
    }
}
