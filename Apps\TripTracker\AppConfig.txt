// Shared
Platforms=iOS,Android
Name=IFS Trip Tracker
AppName=TripTracker
RedirectUri=ifstriptracker
RemoteAssistance=false
SignatureService=false
LocationEnabled=false
LidarService=false
PushNotification=true

// iOS
iOSDisplayName=IFS Trip Tracker
// Below must be the one having the InHouse suffix, the build script will remove it when building the store app
BundleIdentifier=com.ifs.cloud.TripTracker.InHouse
BundleName=IFS Trip Tracker

// NOTE: iOS usage descriptions are always needed since we don't remove code for things like media attachments and location APIs
// So it's okay to add some dummy text for apps that don't really use those features
NSLocationWhenInUseUsageDescription=This application requires location services to work.
NSLocationAlwaysAndWhenInUseUsageDescription=This application requires location services to work.
NSCameraUsageDescription=This application requires access to the camera to capture media attachments.
NSPhotoLibraryUsageDescription=This app needs access to photos to pick media attachments.
NSPhotoLibraryAddUsageDescription=This app needs access to save media and documents.
NSMicrophoneUsageDescription=This is used for remote assistance calls.

// Android
AndroidDisplayName=IFS Trip Tracker
AndroidPackageName=com.ifs.cloud.TripTracker

// Windows
WindowsDisplayName=IFS Trip Tracker
WindowsDescription=IFS Trip Tracker
WindowsShortName=IFS Trip Tracker
IdentityName=IFS.TripTracker
PhoneProductId=8416fb11-8671-4035-8637-09fa5d140701
// Below are in Package.StoreAssociation and Package.xml
ReservedName=IFS Aurena Native 10 IBS
LandingUrl=https://developer.microsoft.com/dashboard/Application?appId=9NT07CQC9MHN