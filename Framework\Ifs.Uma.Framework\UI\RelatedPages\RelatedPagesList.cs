﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Framework.UI.Lists;
using Ifs.Uma.Framework.UI.Workflow;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;

namespace Ifs.Uma.Framework.UI.RelatedPages
{
    public sealed class RelatedPagesList : ListData<RelatedPage>, IDisposable
    {
        private readonly IMetadata _metadata;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IExpressionRunner _expressionRunner;
        private readonly IDataHandler _dataHandler;
        private readonly IDialogService _dialogService;
        private readonly INavigator _navigator;

        public event EventHandler VisibilityChanged;
        public event EventHandler<CommandEnabledChangedEventArgs> EnabledChanged;

        private PageData _pageData;
        public PageData PageData
        {
            get => _pageData;
            set
            {
                if (_pageData != value)
                {
                    PageData oldValue = _pageData;
                    _pageData = value;
                    OnPageDataChanged(oldValue, _pageData);
                }
            }
        }

        private bool _hasRelatedPages;
        public bool HasRelatedPages
        {
            get => _hasRelatedPages;
            private set
            {
                if (_hasRelatedPages != value)
                {
                    _hasRelatedPages = value;
                    OnPropertyChanged(nameof(HasRelatedPages));
                }
            }
        }

        private readonly List<RelatedPage> _allItems = new List<RelatedPage>();
        private IEnumerable<CpiCommand> _commands;
        private IEnumerable<string> _workflows;

        public UpdatingState UpdatingState { get; } = new UpdatingState();

        private string _projectionName;

        public RelatedPagesList(UpdatingState parentUpdatingState, IMetadata metadata, ICommandExecutor commandExecutor,
            IExpressionRunner expressionRunner, IDataHandler dataHandler, IDialogService dialogService, INavigator navigator)
            : base(null)
        {
            _metadata = metadata;
            _commandExecutor = commandExecutor;
            _expressionRunner = expressionRunner;
            _dataHandler = dataHandler;
            _dialogService = dialogService;
            _navigator = navigator;

            Items.Filter = x => x.IsVisible;

            UpdatingState.ParentState = parentUpdatingState;
            UpdatingState.IsAnythingUpdatingChanged += UpdatingState_IsAnythingUpdatingChanged;
        }

        private void UpdatingState_IsAnythingUpdatingChanged(object sender, EventArgs e)
        {
            UpdateStates();
        }

        public void Load(IEnumerable<CpiCommandGroup> commandGroups, IEnumerable<string> workflows, string projectionName, string pageName)
        {
            _projectionName = projectionName;

            _commands = commandGroups?
                .Where(x => x.CommandNames != null)
                .SelectMany(x => x.CommandNames)
                .Select(x => _metadata.FindCommand(projectionName, x))
                .Where(x => x != null && !CommandExecutor.IsPageCreateCommand(pageName, x) && CommandExecutor.IsRelatedPageCommand(x)).ToArray();

            _workflows = workflows;
        }

        protected override void OnSelectedItemChanged()
        {
            base.OnSelectedItemChanged();

            SelectedItem?.ExecuteAsync();

            // reseting the selected item
            SelectedItem = null;
        }

        private void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            if (oldValue != null)
            {
                oldValue.RecordDataChanged -= RecordDataChanged;

                if (oldValue.DefaultViewData?.Record != null)
                {
                    oldValue.DefaultViewData.Record.RecordLoaded -= RecordLoaded;
                }
            }

            if (newValue != null)
            {
                newValue.RecordDataChanged += RecordDataChanged;

                if (newValue.DefaultViewData?.Record != null)
                {
                    newValue.DefaultViewData.Record.RecordLoaded += RecordLoaded;
                }
            }

            UpdateStates();
        }

        private void RecordLoaded(object sender, EventArgs e)
        {
            Task updateTask = LoadRelatedPageCommandsAsync();
            PageData?.BackgroundTasks.Add(updateTask);
        }

        private async Task LoadRelatedPageCommandsAsync()
        {
            List<RelatedPage> relatedPages = await GetRelatedPagesAsync();

            using (Items.DeferRefresh())
            {
                foreach (RelatedPage relatedPage in _allItems)
                {
                    relatedPage.PropertyChanged -= RelatedPage_PropertyChanged;
                }

                _allItems.Clear();
                Items.Clear();

                foreach (RelatedPage relatedPage in relatedPages)
                {
                    relatedPage.PropertyChanged += RelatedPage_PropertyChanged;
                    _allItems.Add(relatedPage);
                    Items.Add(relatedPage);
                }

                UpdateStates();
            }

            HasRelatedPages = Items.Count > 0;
        }

        private void RelatedPage_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ButtonData.IsVisible))
            {
                Items.RefreshFilter();
            }
        }

        private async Task<List<RelatedPage>> GetRelatedPagesAsync()
        {
            List<RelatedPage> pages = new List<RelatedPage>();

            if (_workflows != null)
            {
                foreach (string workflow in _workflows)
                {
                    CpiWorkflow wf = _metadata.FindWorkflow(_projectionName, workflow);
                    bool workflowVisible = _expressionRunner.RunCheck(wf.OfflineVisible ?? wf.Visible, _pageData.DefaultViewData, true);
                    if (workflowVisible)
                    {
                        await AddWorkflowItems(wf, pages);
                    }
                }
            }

            if (_commands != null)
            {
                foreach (CpiCommand command in _commands)
                {
                    RelatedPage relatedPage = new RelatedPage(_commandExecutor, _expressionRunner, _projectionName, command);
                    relatedPage.IsExecuting.ParentState = UpdatingState;
                    relatedPage.PageData = PageData;
                    pages.Add(relatedPage);
                }
            }

            return pages;
        }

        private async Task AddWorkflowItems(CpiWorkflow workflow, List<RelatedPage> pages)
        {
            if (workflow == null)
            {
                return;
            }

            Workflow.Workflow wf = new Workflow.Workflow(_dataHandler, PageData?.DefaultViewData, _projectionName, workflow);
            List<KeyValuePair<int, CpiWorkflowStep>> steps = await wf.GetAvailableWorkflowSteps();

            if (steps == null || !steps.Any())
            {
                return;
            }

            if (workflow.InitCommand != null)
            {
                KeyValuePair<int, CpiWorkflowStep> firstStep = steps.FirstOrDefault();

                WorkflowRelatedPage item = new WorkflowRelatedPage(_commandExecutor, _expressionRunner, _navigator, _projectionName, workflow.InitCommand, workflow, firstStep.Value, firstStep.Key)
                {
                    PageData = PageData
                };

                pages.Add(item);
            }

            foreach (KeyValuePair<int, CpiWorkflowStep> step in steps)
            {
                if (!step.Value.HideWorkflowCommand)
                {
                    CpiCommand cmd = new CpiCommand()
                    {
                        Emphasis = step.Value.OfflineEmphasis ?? step.Value.Emphasis,
                        Icon = step.Value.Icon,
                        Label = step.Value.Label,
                        Name = step.Key + "." + step.Value.Name
                    };

                    WorkflowRelatedPage item = new WorkflowRelatedPage(_commandExecutor, _expressionRunner, _navigator, _projectionName, cmd, workflow, step.Value, step.Key)
                    {
                        PageData = PageData
                    };

                    pages.Add(item);
                }
            }
        }

        private void RecordDataChanged(object sender, EventArgs e)
        {
            UpdateStates();
        }

        private void UpdateStates()
        {
            bool shouldFireVisibilityChanged = false;

            foreach (RelatedPage item in _allItems)
            {
                bool visibilityBefore = item.IsVisible;
                bool enabledBefore = item.IsEnabled;

                item.UpdateStates(UpdatingState.IsAnythingUpdating);

                if (item.IsVisible != visibilityBefore)
                {
                    shouldFireVisibilityChanged = true;
                }
                if (item.IsEnabled != enabledBefore)
                {
                    EnabledChanged?.Invoke(this, new CommandEnabledChangedEventArgs(item));
                }
            }

            Items.RefreshFilter();

            if (shouldFireVisibilityChanged)
            {
                VisibilityChanged?.Invoke(this, EventArgs.Empty);
            }

            AllowItemSelection = _allItems.Any(x => x.IsVisible && x.IsEnabled);

            if (!AllowItemSelection)
            {
                SelectedItem = null;
            }
        }

        public void GetSelectAttributes(ICollection<string> attributes)
        {
            if (_commands != null)
            {
                foreach (CpiCommand command in _commands)
                {
                    AttributeFinder.FindInCommand(attributes, _metadata, _projectionName, command);
                }
            }
        }

        #region IDisposable Support

        private bool _disposed;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (_disposed)
            {
                return;
            }

            if (disposing)
            {
                // Unsubscribe from events
                foreach (RelatedPage relatedPage in _allItems)
                {
                    relatedPage.PropertyChanged -= RelatedPage_PropertyChanged;
                }

                UpdatingState.IsAnythingUpdatingChanged -= UpdatingState_IsAnythingUpdatingChanged;
            }

            _disposed = true;
        }

        ~RelatedPagesList()
        {
            Dispose(false);
        }

        #endregion
    }
}
