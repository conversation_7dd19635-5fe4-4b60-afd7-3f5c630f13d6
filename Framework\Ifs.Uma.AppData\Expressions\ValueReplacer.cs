﻿using System;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class ValueReplacer : IfsExpressionVisitor
    {
        private readonly IExpressionValueProvider _valueProvider;
        private readonly Func<string, bool> _shouldReplace;

        public static Expression Rewrite(Expression expression, IExpressionValueProvider valueProvider)
        {
            return Rewrite(expression, valueProvider, null);
        }

        public static Expression Rewrite(Expression expression, IExpressionValueProvider valueProvider, Func<string, bool> shouldReplace)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));
            if (valueProvider == null) throw new ArgumentNullException(nameof(valueProvider));

            ValueReplacer visitor = new ValueReplacer(valueProvider, shouldReplace);
            return visitor.Visit(expression);
        }

        private ValueReplacer(IExpressionValueProvider valueProvider, Func<string, bool> shouldReplace)
        {
            _valueProvider = valueProvider;
            _shouldReplace = shouldReplace;
        }

        protected internal override Expression VisitVarAccessExpression(VarAccessExpression exp)
        {
            if ((_shouldReplace == null || _shouldReplace(exp.PropertyPath)) &&
                _valueProvider.TryGetValue(exp.PropertyPath, out object value))
            {
                return Expression.Constant(new DynamicValue(value));
            }

            return exp;
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            Expression operand = Visit(node.Operand);

            if (node.NodeType == ExpressionType.Convert && node.Type == operand.Type && node.Method == null)
            {
                return operand;
            }

            // Must override here since the base does some unwanted validation
            return node.Update(operand);
        }
    }
}
