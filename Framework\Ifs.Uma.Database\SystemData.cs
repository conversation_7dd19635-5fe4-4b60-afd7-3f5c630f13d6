﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    /// <summary>
    /// Convenient access to all system data
    /// Initially this is just custom enumerations and fields
    /// Extend as required
    /// </summary>
    public interface ISystemData
    {
        string NormalisePropertyName(Type rowType, string propertyName);
        object GetDefaultValue(Type rowType, string propertyName);
        object ValidateCustomValue(Type rowType, string propertyName, object value);
    }

    public class SystemData : ISystemData
    {
        /// <summary>
        /// The last SystemData object created
        /// </summary>
        public static ISystemData Instance { get { return g_instance; } }

        /// <summary>
        /// Creates a SystemData object
        /// Initially this just contains custom enumerations and fields.
        /// We can extend this as required
        /// Data comes either from the database or the Touch Apps Server/
        /// </summary>
        /// <param name="logger">ILogger object for logging</param>
        /// <returns>SystemData object</returns>
        public static ISystemData Create(ILogger logger)
        {
            ISystemData result = new SystemData(logger);
            if (result != null)
            {
                Interlocked.Exchange(ref g_instance, result);
            }
            return result;
        }

        #region Implementation

        private static ISystemData g_instance;

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity",
            Justification = "It's difficult - ok?")]
        protected SystemData(ILogger logger)
        {
        }

        public string NormalisePropertyName(Type rowType, string propertyName)
        {
            throw new NotImplementedException();
        }

        public object GetDefaultValue(Type rowType, string propertyName)
        {
            throw new NotImplementedException();
        }

        public object ValidateCustomValue(Type rowType, string propertyName, object value)
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
