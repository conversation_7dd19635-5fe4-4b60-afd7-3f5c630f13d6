﻿using System;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class ResultColumnExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.ResultColumn;

        public Expression ResultColumn { get; }
        public string Alias { get; }
        public bool AsNull { get; }

        internal ResultColumnExpression(Expression resultColumn, string alias)
        {
            ResultColumn = resultColumn ?? throw new ArgumentNullException(nameof(resultColumn));
            Alias = alias;
        }

        internal ResultColumnExpression(Expression resultColumn, bool asNull)
        {
            ResultColumn = resultColumn ?? throw new ArgumentNullException(nameof(resultColumn));
            AsNull = asNull;
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitResultColumnExpression(this);
        }

        public override string ToString()
        {
            if (Alias != null)
            {
                return ResultColumn.ToString() + " AS " + Alias;
            }
            else if (AsNull)
            {
                return "NULL AS " + ResultColumn.ToString();
            }

            return ResultColumn.ToString();
        }

        public ResultColumnExpression Update(Expression resultColumn, string alias)
        {
            if (ResultColumn == resultColumn &&
                Alias == alias)
            {
                return this;
            }

            return new ResultColumnExpression(resultColumn, alias);
        }
    }

    public partial class IfsExpression
    {
        public static ResultColumnExpression QueryResultColumn(string attributePath)
        {
            return new ResultColumnExpression(VarAccess(attributePath), null);
        }

        public static ResultColumnExpression QueryResultColumn(string attributePath, string alias)
        {
            return new ResultColumnExpression(VarAccess(attributePath), alias);
        }

        public static ResultColumnExpression QueryResultColumn(string attributePath, bool asNull)
        {
            return new ResultColumnExpression(VarAccess(attributePath), asNull);
        }

        public static ResultColumnExpression QueryResultColumn(Expression resultColumn, string alias)
        {
            return new ResultColumnExpression(resultColumn, alias);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitResultColumnExpression(ResultColumnExpression exp)
        {
            Expression resultColumn = Visit(exp.ResultColumn);
            return exp.Update(resultColumn, exp.Alias);
        }
    }
}
