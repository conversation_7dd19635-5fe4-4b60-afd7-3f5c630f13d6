﻿using System.Linq;
using System.Runtime.Serialization;
using System.Reflection;

namespace Ifs.Cloud.Client.Entities
{
    /// <summary>
    /// Abstract class to represent a cloud Resource.    
    /// </summary>
    [DataContract]
    public abstract class BaseResource
    {
        /// <summary>
        /// The Resource Name. Must be overriden.
        /// Framework usese this to append to the .../Downlink.svc/resources/
        /// Remember to enter the fully qualified name as seen in the cloud resource.
        /// </summary>
        public abstract string ResourceName { get; }

        public virtual bool SingleResponse { get { return false; } }

        /// <summary>
        /// The Resource that has this as true will pass the full ResourceName.
        /// Framework uses this boolean to check before prefixing a url to the ResourceName 
        /// Remember to enter the fully qualified ResourceName if you are overriding this to true.
        /// </summary>
        public virtual bool FullNameDefined { get { return false; } } 

        public virtual string GetResourceName(ClientInfo clientInfo)
        {
            return ResourceName;
        }

        /// <summary>
        /// Copies property values of one BaseResource to another.
        /// The Base Resources could be of two types, however the copying would be done matching parameters names and types.
        /// There will be no validation for private get or set.
        /// </summary>
        /// <param name="resourceToTransferData"></param>
        public virtual void CopyValuesTo(ref BaseResource resourceToTransferData)
        {            
            PropertyInfo[] sourceProperties = GetType().GetRuntimeProperties().ToArray();
            PropertyInfo[] destinationProperties = resourceToTransferData.GetType().GetRuntimeProperties().ToArray();

            var commonProps = from sp in sourceProperties
                              join dp in destinationProperties on new { sp.Name, sp.PropertyType } equals new { dp.Name, dp.PropertyType }
                              select new { sp, dp };

            foreach (var item in commonProps)
            {
                if (item.sp.Name != "ResourceName")
                {                    
                    //item.dp.SetValue(resourceToTransferData, item.sp.GetValue(this));
                    if (item.sp.GetType() != typeof(CloudStatus))
                    {
                        item.dp.SetValue(resourceToTransferData, item.sp.GetValue(this, null), null);
                    }
                    else
                    {
                        CloudStatus cl = (CloudStatus)item.sp.GetValue(this, null);
                        if (cl != null)
                        {
                            item.dp.SetValue(resourceToTransferData, cl, null);
                        }
                    }
                }
            }                                               
        }
    }
}
