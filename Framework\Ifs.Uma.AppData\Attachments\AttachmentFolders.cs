﻿using System.IO;

namespace Ifs.Uma.AppData.Attachments
{
    public static class AttachmentFolders
    {
        private const string MediaFolderName = "Media";
        private const string DocumentsFolderName = "Documents";
        private const string AttachmentsFolderName = "Attachments";

        public static string GetAttachmentsFolderPath(int dbId)
        {
            return AttachmentsFolderName + "_" + dbId;
        }

        public static string GetMediaFolderPath(int dbId)
        {
            return Path.Combine(GetAttachmentsFolderPath(dbId), MediaFolderName);
        }

        public static string GetDocumentsFolderPath(int dbId)
        {
            return Path.Combine(GetAttachmentsFolderPath(dbId), DocumentsFolderName);
        }
    }
}
