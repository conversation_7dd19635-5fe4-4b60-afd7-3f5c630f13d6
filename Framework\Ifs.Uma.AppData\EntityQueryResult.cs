﻿using System.Collections.Generic;

namespace Ifs.Uma.AppData
{
    public sealed class EntityQueryResult
    {
        public static EntityQueryResult Offline { get; } = new EntityQueryResult();

        public IReadOnlyList<EntityRecord> Records { get; }

        public bool HasMoreResults { get; }
        public bool DataSourceOffline { get; }

        private readonly EntityQuery _query;

        public EntityQueryResult(EntityQuery query, IReadOnlyList<EntityRecord> records, bool hasMoreResults)
        {
            _query = query;
            Records = records;
            HasMoreResults = hasMoreResults && Records.Count != 0;
        }

        private EntityQueryResult()
        {
            Records = new EntityRecord[0];
            DataSourceOffline = true;
        }

        public EntityQuery GetNextQuery()
        {
            if (!HasMoreResults)
            {
                return null;
            }

            EntityQuery next = _query.Clone();
            next.Skip = next.Skip.GetValueOrDefault(0) + Records.Count;

            return next;
        }
    }
}
