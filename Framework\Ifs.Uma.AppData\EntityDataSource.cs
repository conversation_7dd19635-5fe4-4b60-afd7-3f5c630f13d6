using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData
{
    public class EntityDataSource
    {
        public IMetadata Metadata { get; }

        public RecordType RecordType { get; }
        public string ProjectionName { get; private set; }
        public string EntitySetName { get;  set; }

        public IMetaTable Table => RecordType.Table;
        public string EntityName => RecordType.Name;

        private JsonWhereExpression _whereExpression;

        public static EntityDataSource FromEntitySet(IMetadata metadata, string projectionName, string entitySetName)
        {
            if (metadata == null)
                throw new ArgumentNullException(nameof(metadata));

            if (string.IsNullOrEmpty(entitySetName))
            {
                return null;
            }

            CpiContains contains = metadata.FindContains(projectionName, entitySetName);

            if (contains == null)
            {
                return null;
            }
            
            EntityDataSource rds = FromEntity(metadata, projectionName, contains.Entity);
            rds.EntitySetName = entitySetName;

            if (contains.Filter != null)
            {
                rds._whereExpression = new JsonWhereExpression(rds, contains.Filter);
            }

            return rds;
        }

        public static EntityDataSource FromEntity(IMetadata metadata, string projectionName, string entityName)
        {
            if (metadata == null)
                throw new ArgumentNullException(nameof(metadata));

            RecordType recordType = metadata.GetRecordType(projectionName, entityName);

            if (recordType == null)
            {
                return null;
            }

            return new EntityDataSource(metadata, projectionName, recordType);
        }

        protected EntityDataSource(IMetadata metadata, string projectionName, RecordType recordType)
        {
            if (projectionName == null) throw new ArgumentNullException(nameof(projectionName));
            if (metadata == null) throw new ArgumentNullException(nameof(metadata));
            if (recordType == null) throw new ArgumentNullException(nameof(recordType));

            Metadata = metadata;
            ProjectionName = projectionName;
            RecordType = recordType;
        }

        public EntityDataSource SelectArray(RemoteRow row, string arrayPropertyName)
        {
            if (row == null || arrayPropertyName == null) return null;
            return ArrayDataSource.Create(this, row, arrayPropertyName);
        }

        internal virtual QueryExpression ToQueryExpression(string fromAlias)
        {
            FromExpression from = IfsExpression.QueryFrom(EntityName, fromAlias);

            Expression where = _whereExpression?.Expression;

            List<ResultColumnExpression> resultColumns = new List<ResultColumnExpression>();
            foreach (IMetaDataMember member in Table.DataMembers)
            {
                AttributePathInfo attribute = AttributePathInfo.Get(Metadata, ProjectionName, EntityName, member.PropertyName);
                ResultColumnExpression exp = IfsExpression.QueryResultColumn(IfsExpression.AttributeAccess(attribute), member.PropertyName);
                resultColumns.Add(exp);
            }

            return IfsExpression.Query(from, null, where, null, false, resultColumns);
        }

        public virtual bool IsEffectedByChangeSet(DataChangeSet changeSet)
        {
            if (changeSet.HasChanges(Table))
            {
                return true;
            }

            foreach (CpiProjection projection in Metadata.CpiMetaData.GetProjections())
            {
                CpiEntity entity = Metadata.FindEntity(projection.Name, EntityName);

                if (entity != null && entity.LuDependencies != null)
                {
                    foreach (string entityName in entity.LuDependencies)
                    {
                        IMetaTable table = Metadata.GetTableForEntityName(entityName);
                        if (table != null && changeSet.HasChanges(table))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public virtual bool IsTheSameAs(EntityDataSource dataSource)
        {
            if (dataSource == null || dataSource.GetType() != typeof(EntityDataSource)) return false;

            return dataSource.ProjectionName == ProjectionName && dataSource.EntityName == EntityName && dataSource.EntitySetName == EntitySetName;
        }

        public static bool IsTheSame(EntityDataSource a, EntityDataSource b)
        {
            if (a == null && b == null) return true;
            return a != null && a.IsTheSameAs(b);
        }
    }
}
