﻿using System;
using System.Linq;
using System.Net;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Comm;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Exceptions;
using Ifs.Cloud.Client.Types;
using Ifs.Uma.Utility;

namespace Ifs.Cloud.Client
{
    public class ContextProvider
    {
        #region instance variables\properties

        #region private variables
        public const string DefaultUrl = "https://cloud.ifsworld.com:8080";
        private readonly Interfaces.ICloudAuthenticator _authenticator;
        private const string IPIEndpointSuffix = "/mob/ifsapplications/projection/v1/";

        #endregion

        #region Properties
        /// <summary>
        /// Holds the session ID of this context
        /// </summary>
        internal string SessionId { get; set; }

        /// <summary>
        /// Gets or Sets the AuthenticationToken
        /// </summary>
        public string AuthenticationToken { get; set; }

        /// <summary>
        /// Returns if current context was initiated as DEV_MODE.
        /// </summary>
        public bool DevMode { get; }

        /// <summary>
        /// Returns weather the current context has been Authenticated
        /// </summary>
        public bool HasAuthenticated => SessionId != null && AuthenticationToken != null;

        /// <summary>
        /// Gets or Sets if the application is on TryMeMode
        /// </summary>
        public bool TryMeMode { get; set; }

        public string BaseUrl { get; } = DefaultUrl;
        public string SystemId { get; }
        public IdentityProvider IdentityProvider { get; private set; }

        public event EventHandler TokensRefreshed;
        internal ClientInfo ClientInfo { get; }
        internal CookieContainer CookieContainer { get; }
        public ILogger Logger { get; }
        public string DeviceId { get; set; }

        #endregion

        #endregion

        #region Constructor
        private ContextProvider(ILogger logger, ClientInfo ci, string baseUrl, string systemId, Interfaces.ICloudAuthenticator authenticator, bool devMode, string deviceId)
        {
            _authenticator = authenticator;
            ClientInfo = ci;
            DevMode = devMode || "#DEV#".Equals(baseUrl);
            if (devMode && string.IsNullOrEmpty(baseUrl)) // in dev mode defaults to localmachine dev scafold
            {
                baseUrl = "http://localhost:48080";
            }
            if (!string.IsNullOrEmpty(baseUrl)) //if no baseURL is specified defaults to production
            {
                BaseUrl = baseUrl;
            }
            SystemId = systemId;
            DeviceId = deviceId;
            CookieContainer = new CookieContainer();
            Logger = logger;
        }

        #endregion

        #region Static Methods

        /// <summary>
        /// Initialize the context with client information.        
        /// </summary>
        /// <param name="ci">Client information (Mandetory)</param>
        /// <param name="baseUrl">Base URL for cloud (If null will default to PROD URL)</param>
        /// <param name="authenticator">The authenticator</param>
        /// <param name="devMode">If the app is running on dev mode and connects to a scaffold (if no baseURL is defined when true, defaults to localmachine:48080)</param>
        /// <param name="systemId">The system ID</param>
        public static ContextProvider CreateContext(ILogger logger, ClientInfo ci, string baseUrl, string systemId, Interfaces.ICloudAuthenticator authenticator, bool devMode, string deviceId = null)
        {
            if (ci == null)
            {
                throw new ArgumentNullException(nameof(ci));
            }

            return new ContextProvider(logger, ci, baseUrl, systemId, authenticator, devMode, deviceId);
        }

        /// <summary>
        /// Initialize the context with client information in try me mode.
        /// </summary>
        /// <param name="ci">ClientInformation (Mandetory)</param>
        public static ContextProvider CreateContextInTryMeMode(ILogger logger, ClientInfo ci)
        {
            ContextProvider ctx = CreateContext(logger, ci, null, null, null, false);
            ctx.TryMeMode = true;
            return ctx;
        }

        #endregion

        public async Task<IdentityProvider> GetIdentityProviderInformation(string baseUrl, string appName)
        {
            if (DevMode || TryMeMode || _authenticator == null)
            {
                return null;
            }

            string ipi = await _authenticator.GetKeyCloakInfo(baseUrl + IPIEndpointSuffix);

            if (!string.IsNullOrEmpty(ipi))
            {
                return new IdentityProvider(ipi) { ClientId = "IFS_aurena_native" };
            }

            return null;
        }

        public async Task<string> GetUserName(string baseUrl, string authToken)
        {
            if (DevMode || TryMeMode || _authenticator == null)
            {
                return null;
            }
            SessionId = authToken;
            UsernameResource usernameResource = new UsernameResource();
            CloudResourceProxy<UsernameResource> proxy = new CloudResourceProxy<UsernameResource>(this, usernameResource);
            usernameResource = (await proxy.ExecuteGetAsync(0, 1, CancellationToken.None)).FirstOrDefault();

            return usernameResource?.Value;
        }

        #region Instance Methods

        public async Task AuthenticateSession(string userName, string accessTokenOrPassword, IdentityProvider identityProvider)
        {
            if (TryMeMode || _authenticator == null)
            {
                return;
            }

            if (identityProvider != null)
            {
                SessionId = accessTokenOrPassword;
                AuthenticationToken = accessTokenOrPassword;
                IdentityProvider = identityProvider;
            }
            else
            {
                await AuthenticateBasic(userName, accessTokenOrPassword);
            }
        }

        public async Task LogoutTasSessionAsync()
        {
            if (DevMode || TryMeMode || _authenticator == null)
                return;

            try
            {
                CallRequest<ClientSession> callRequest =
                    CallRequest<ClientSession>.ForResource(this,
                        new RequestContent<ClientSession>(new ClientSession()),
                        HttpMethodType.Delete, 0, 0);

                // try to delete the session in tas
                await callRequest.ExecuteRequestWithRetryAsync();
            }
            catch (Exception)
            {
                // exceptions ignored, wont be retrying. session will be deleted after its lifetime
                // Do nothing.
            }
        }

        [DataContract]
        private class ClientSession : BaseResource
        {
            private const string TasLogoutResourceName = "Ifs.Cloud.BaseResources.ClientSession";

            public override string ResourceName => TasLogoutResourceName;
        }

        public async Task LogoutIdpSessionAsync(IdentityProvider identityProvider)
        {
            if (DevMode || TryMeMode || _authenticator == null)
                return;
            await _authenticator.LogoutFromIDP(identityProvider);
        }

        public async Task Reauthenticate()
        {
            if (TryMeMode || _authenticator == null)
            {
                return;
            }
            
            if (IdentityProvider != null)
            {
                await RefreshOpenIdAccessToken();
            }
            else
            {
                await ReauthenticateBasic();
            }
        }
        
        private async Task AuthenticateOpenId(IdentityProvider identityProvider, string accessToken)
        {
            IdentityProvider = identityProvider;

            if (accessToken == null)
            {
                await RefreshOpenIdAccessToken();
            }
            else
            {
                //string publicKey = await _authenticator.GetPublicKey(this);
               // AuthenticationToken = _authenticator.GetAuthenticationAccessTokenAndKey(SystemId, accessToken, publicKey);

                try
                {
                    SessionId = accessToken;
                    AuthenticationToken = accessToken;
                }
                catch (CloudException ce)
                {
                    if (ce.ShouldRetryAfterReauthentication)
                    {
                        await RefreshOpenIdAccessToken().ConfigureAwait(false);
                    }
                    else
                    {
                        throw ce;
                    }
                }
            }
        }

        private async Task RefreshOpenIdAccessToken()
        {
            // If the RefreshToken has expired the call will throw an exception 
            // The user needs to re login to get a new RefreshToken

            if (!string.IsNullOrEmpty(IdentityProvider.RefreshToken))
            {
                Logger.Information("Updating OpenID access token");

                TokenResponseInfo tokenResponse = await _authenticator.RefreshAccessToken(IdentityProvider);

                if (tokenResponse != null)
                {
                    if (string.IsNullOrEmpty(tokenResponse.AccessToken))
                    {
                        // Refresh token has expired
                        IdentityProvider.RefreshToken = null;
                        IdentityProvider.AccessToken = null;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(tokenResponse.RefreshToken))
                        {
                            IdentityProvider.RefreshToken = tokenResponse.RefreshToken;
                        }

                        IdentityProvider.AccessToken = tokenResponse.AccessToken;
                    }

                    TokensRefreshed?.Invoke(this, new RefreshTokenEventArgs());
                }
            }

            if (!string.IsNullOrEmpty(IdentityProvider.AccessToken))
            {
                AuthenticationToken = IdentityProvider.AccessToken;
                SessionId = IdentityProvider.AccessToken;
            }
            else
            {
                AuthenticationToken = null;
            }

            if (AuthenticationToken == null)
            {
                SessionId = null;
                throw CloudException.FromCause("ERROR_OPEN_ID_TOKEN_EXPIRED: New refresh token required", HttpStatusCode.Unauthorized, null, false);
            }
        }
        public void InvalidateSession()
        {
            AuthenticationToken = "INVALID";
            SessionId = "session:INVALID";
        }

        private async Task AuthenticateBasic(string userName, string password)
        {
            AuthenticationToken = _authenticator.GetAuthenticationToken(SystemId, userName, password, string.Empty);
            try
            {
                //TO-DO:temp fix
                await Task.FromResult(true);
                SessionId = _authenticator.GetSessionID(this, AuthenticationToken);
            }
            catch (CloudException ce)
            {
                if (ce.ErrorType == CloudErrorType.TwoFactorAuthenticationRequired || ce.ErrorType == CloudErrorType.RenewSecretKey)
                {
                    throw new NotSupportedException("Two factor authentication is not supported with OAuth");
                }
                else
                {
                    // Any other CloudException, we don't handle. Tell the user about it.
                    throw;
                }
            }
        }

        private async Task ReauthenticateBasic()
        {
            //TO-DO:temp fix
            await Task.FromResult(true);
            SessionId = _authenticator.GetSessionID(this, AuthenticationToken);
        }

        #endregion
    }
}
