﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Online
{
    public sealed class AlwaysOfflineOnlineDataHandler : IOnlineDataHandler
    {
        public int DeviceId { get; } = 0;

        public bool IsOnline { get; } = false;

        public string AppName => string.Empty;

        public string UserName => string.Empty;

        public Task<EntityQueryResult> GetRecordsAsync(EntityQuery query, CancellationToken cancelToken)
        {
            return Task.FromResult(EntityQueryResult.Offline);
        }

        public Task<ExecuteResult> EntityPrepareAsync(string projectionName, string entityName, CancellationToken cancelToken, string entitySetName = "")
        {
            return Task.FromResult(ExecuteResult.Offline);
        }

        public Task<ExecuteResult> EntityInsertAsync(string projectionName, RemoteRow row, CancellationToken cancelToken)
        {
            return Task.FromResult(ExecuteResult.Offline);
        }

        public Task<ExecuteResult> EntityUpdateAsync(string projectionName, RemoteRow row, IEnumerable<string> changedMembers, CancellationToken cancelToken)
        {
            return Task.FromResult(ExecuteResult.Offline);
        }

        public Task<ExecuteResult> EntityDeleteAsync(string projectionName, RemoteRow row, CancellationToken cancelToken)
        {
            return Task.FromResult(ExecuteResult.Offline);
        }

        public Task<ExecuteResult> CallActionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            return Task.FromResult(ExecuteResult.Offline);
        }

        public Task<ExecuteResult> CallBoundActionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            return Task.FromResult(ExecuteResult.Offline);
        }

        public Task<ExecuteResult> CallFunctionAsync(string projectionName, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            return Task.FromResult(ExecuteResult.Offline);
        }

        public Task<ExecuteResult> CallBoundFunctionAsync(string projectionName, RemoteRow row, string name, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            return Task.FromResult(ExecuteResult.Offline);
        }

        public Task<EntityQueryResult> GetFunctionRecordsAsync(EntityQuery query, IReadOnlyDictionary<string, object> parameters, CancellationToken cancelToken)
        {
            return Task.FromResult(EntityQueryResult.Offline);
        }
    }
}
