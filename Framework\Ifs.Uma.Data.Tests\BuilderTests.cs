﻿using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Database.SQLite;
using NUnit.Framework;
using System.Linq;

namespace Ifs.Uma.Data.Tests
{
    [TestFixture]
    public class BuilderTests
    {
        private const string DllPath = "Ifs.Uma.Database.SQLite, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null";

        [Test]
        public void AddTable()
        {
            DbProviderFactory factory = DbProviderFactory.Create(DllPath, null);
            
            MappingSource mappingSource = new AttributeMappingSource(null, null, null);
            IMetaModel metaModel = mappingSource.GetModel(typeof(DataContext));

            var table = metaModel.GetTable(typeof(Transition));
            
            string[] sql = factory.Builder.BuildDdl(new[] { table }, DdlChoice.CreateScript).ToArray();

            Assert.AreEqual(sql[0], "DROP TABLE IF EXISTS \"fnd$transition_grouping\"");
            Assert.AreEqual(sql[1], "CREATE TABLE \"fnd$transition_grouping\" (\r\n" +
                "    \"transition_id\" INTEGER PRIMARY KEY AUTOINCREMENT,\r\n" +
                "    \"timestamp\" INTEGER NOT NULL" +
                ")");
        }

        [Test]
        public void AddColumn()
        {
            DbProviderFactory factory = DbProviderFactory.Create(DllPath, null);

            MappingSource mappingSource = new AttributeMappingSource(null, null, null);
            IMetaModel metaModel = mappingSource.GetModel(typeof(DataContext));

            var table = metaModel.GetTable(typeof(TransitionRow));
            var member = table.DataMembers.FirstOrDefault(x => x.PropertyName == nameof(TransitionRow.ErrorDetail));
            
            string[] sql = factory.Builder.BuildAddColumnDdl(table, member).ToArray();

            Assert.AreEqual(sql[0], "ALTER TABLE \"fnd$transition_row\" ADD COLUMN \"error_detail\" TEXT");
        }
    }
}
