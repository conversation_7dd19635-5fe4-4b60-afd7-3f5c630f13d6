﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Database
{
    public abstract class DbCommand : IDisposable
    {
        public bool TraceFlag { get; set; }

        public string CommandText 
        {
            get { return m_commandText; }
            set 
            {
                string newValue = NormaliseCommandText(value);
                if (!string.Equals(m_commandText, newValue))
                {
                    NeedPrepare();
                    m_commandText = newValue;
                }
            }
        }

        public abstract DbConnection Connection { get; }
        public abstract DbTransaction Transaction { get; }

        public IList<DbParameter> Parameters { get { return m_parameters; } }

        /// <summary>
        /// Creates a parameter that can be bound to a SQL command
        /// </summary>
        /// <param name="id">The undecorated name of the parameter e.g. P0, P1</param>
        /// <param name="parameterType">The type of the parameter.  The choice is limited.</param>
        /// <param name="valueIndex">An index to associate with the value</param>
        /// <returns>The database parameter. Name property gives the name to write into SQL</returns>
        public DbParameter CreateParameter(string id, Type parameterType, int valueIndex)
        {
            if (parameterType == null) throw new ArgumentNullException("parameterType");
            DbConnection connection = Connection;
            if (connection == null) throw new ObjectDisposedException("DbCommand");
            Type nnType = TypeHelper.GetNonNullableType(parameterType);
            TypeCode parameterCode = TypeHelper.ValidateDbType(nnType);
            if (parameterCode == TypeCode.Object || parameterCode == TypeCode.Empty ||
                (parameterCode == TypeCode.Enumeration && connection.EnumMapper == null))
                throw new ArgumentOutOfRangeException("parameterType");
            IMetaEnumeration enumeration = parameterCode == TypeCode.Enumeration ?
                connection.EnumMapper.GetEnumeration(nnType) : null;
            try
            {
                return NewParameter(id, parameterCode, enumeration, valueIndex);
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.NewParameterError, ex.FormatXml(false, false));
                throw;
            }
        }

        public int ExecuteNonQuery()
        {
            Prepare();
            int result;
            if (TraceFlag)
            {
                TraceParameters();
            }
            try
            {
                result = DoExecuteNonQuery();
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.ExecuteNonQueryError, TraceFlag ? string.Empty : ToString(true), ex.FormatXml(false, false));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceExecuteNonQuery, ObjectConverter.ToString(result));
            }
            return result;
        }

        public DbDataReader ExecuteReader()
        {
            ICollection<DbDataReader> readers = m_readers;
            if (readers == null) throw new ObjectDisposedException("DbCommand");
            Prepare();
            if (TraceFlag)
            {
                TraceParameters();
            }
            DbDataReader result = null;
            string sql = TraceFlag ? string.Empty : ToString(true);
            try
            {
                result = DoExecuteReader(sql);
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.ExecuteReaderError, sql, ex.FormatXml(false, false));
                throw;
            }
            if (result != null)
            {
                readers.Add(result);
            }
            return result;
        }

        public object ExecuteScalar()
        {
            Prepare();
            if (TraceFlag)
            {
                TraceParameters();
            }
            object result;
            try
            {
                result = DoExecuteScalar();
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.ExecuteScalarError, TraceFlag ? string.Empty : ToString(true), ex.FormatXml());
                throw;
            }
            if (TraceFlag)
            {
                if (result != null)
                {
                    Logger.Trace(Strings.TraceExecuteScalar, ObjectConverter.ToPrettyString(result));
                }
                else
                {
                    Logger.Trace(Strings.TraceExecuteScalarNull);
                }
            }
            return result;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        public override string ToString()
        {
            return ToString(TraceFlag);
        }

        public string ToString(bool traceFlag)
        {
            StringBuilder sb = new StringBuilder();
            if (!string.IsNullOrEmpty(CommandText))
            {
                sb.AppendLine();
                sb.Append(CommandText);
                if (traceFlag)
                {
                    WriteParameters(sb);
                }
            }
            return sb.ToString();
        }

        protected void WriteParameters(StringBuilder sb)
        {
            if (sb != null)
            {
                foreach (DbParameter p in m_parameters)
                {
                    sb.AppendLine();
                    sb.Append(p.ToString());
                }
            }
        }

        protected void TraceParameters()
        {
            StringBuilder sb = new StringBuilder();
            WriteParameters(sb);
            if (sb.Length > 0)
            {
                Logger.Trace(sb.ToString());
            }
        }

        protected DbCommand(ILogger logger, bool traceFlag)
        {
            Logger = logger;
            TraceFlag = traceFlag;
            m_parameters = new DbParameterCollection(this);
            m_readers = new SynchronisedCollection<DbDataReader>();
        }

        protected void ReaderDisposed(DbDataReader reader)
        {
            ICollection<DbDataReader> readers = m_readers;
            if (reader != null && readers != null)
            {
                readers.Remove(reader);
            }
        }

        protected virtual void NeedPrepare()
        {
            m_needPrepare = true;
        }

        protected void BindParameterError(DbParameter parameter, Exception ex)
        {
            if (parameter == null) throw new ArgumentNullException("parameter");
            Logger.Error(Strings.BindParameterError, parameter.ToString(), ex.FormatXml(false, false));
        }

        protected ILogger Logger { get; private set; }

        private IList<DbParameter> m_parameters;
        private bool m_needPrepare;
        private string m_commandText;
        private ICollection<DbDataReader> m_readers;

        ~DbCommand()
        {
            Dispose(false);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                UsefulExtensions.ThreadSafeDisposeAll(ref m_readers);
            }
        }

        protected abstract bool DoPrepare();
        protected abstract int DoExecuteNonQuery();
        protected abstract DbDataReader DoExecuteReader(string sql);
        protected abstract object DoExecuteScalar();
        protected abstract DbParameter NewParameter(string id, TypeCode parameterCode, IMetaEnumeration enumeration, int valueIndex);
        protected abstract bool ValidateParameter(DbParameter parameter);

        // override if for example the driver does not like \r (CR) characters.
        // Yes, I'm talking to you Oracle.
        protected virtual string NormaliseCommandText(string commandText)
        {
            return commandText;
        }

        private void Prepare()
        {
            if (m_needPrepare)
            {
                try
                {
                    if (DoPrepare() && TraceFlag)
                    {
                        Logger.Trace(ToString(false));
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(Strings.PrepareCommandError, ToString(false), ex.FormatXml(false, false));
                    throw;
                }
                m_needPrepare = false;
            }
        }

        private class DbParameterCollection : IList<DbParameter>
        {
            public DbParameterCollection(DbCommand parent)
            {
                if (parent == null) throw new ArgumentNullException("parent");
                m_parent = parent;
                m_list = new List<DbParameter>();
            }

            private IList<DbParameter> m_list;
            private DbCommand m_parent;

            public int IndexOf(DbParameter item)
            {
                return m_list.IndexOf(item);
            }

            public void Insert(int index, DbParameter item)
            {
                if (item == null) throw new ArgumentNullException("item");
                if (m_list.Any(x => string.Equals(x.Id, item.Id, StringComparison.OrdinalIgnoreCase)))
                    throw new ArgumentOutOfRangeException("item");
                if (!m_parent.ValidateParameter(item)) throw new ArgumentOutOfRangeException("item");
                m_list.Insert(index, item);
                m_parent.NeedPrepare();
            }

            public void RemoveAt(int index)
            {
                m_list.RemoveAt(index);
                m_parent.NeedPrepare();
            }

            public DbParameter this[int index]
            {
                get
                {
                    return m_list[index];
                }
                set
                {
                    if (value == null) throw new ArgumentNullException("value");
                    if (m_list.Any(x => string.Equals(x.Id, value.Id, StringComparison.OrdinalIgnoreCase)))
                        throw new ArgumentOutOfRangeException("value");
                    if (!m_parent.ValidateParameter(value)) throw new ArgumentOutOfRangeException("value");
                    m_list[index] = value;
                    m_parent.NeedPrepare();
                }
            }

            public void Add(DbParameter item)
            {
                if (item == null) throw new ArgumentNullException("item");
                if (m_list.Any(x => string.Equals(x.Id, item.Id, StringComparison.OrdinalIgnoreCase)))
                    throw new ArgumentOutOfRangeException("item");
                if (!m_parent.ValidateParameter(item)) throw new ArgumentOutOfRangeException("item");
                m_list.Add(item);
                m_parent.NeedPrepare();
            }

            public void Clear()
            {
                m_list.Clear();
                m_parent.NeedPrepare();
            }

            public bool Contains(DbParameter item)
            {
                return m_list.Contains(item);
            }

            public void CopyTo(DbParameter[] array, int arrayIndex)
            {
                m_list.CopyTo(array, arrayIndex);
            }

            public int Count
            {
                get { return m_list.Count; }
            }

            public bool IsReadOnly
            {
                get { return m_list.IsReadOnly; }
            }

            public bool Remove(DbParameter item)
            {
                bool result = m_list.Remove(item);
                if (result)
                {
                    m_parent.NeedPrepare();
                }
                return result;
            }

            public IEnumerator<DbParameter> GetEnumerator()
            {
                return m_list.GetEnumerator();
            }

            System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
            {
                return GetEnumerator();
            }
        }
    }
}
