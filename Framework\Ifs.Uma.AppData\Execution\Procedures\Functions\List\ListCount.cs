﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.List
{
    internal sealed class ListCount : ListFunction
    {
        public const string FunctionName = "Count";

        public ListCount()
            : base(FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            MarbleList list = PrepareList(context, parameters[0]);
            return (long)list.Count;
        }
    }
}
