﻿using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.Extensions;
using Ifs.Uma.Framework.UI.Attachments;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using static Ifs.Uma.Framework.UI.Cards.CardDefItem;

namespace Ifs.Uma.Framework.UI.Cards
{
    public sealed class CardDef : ObservableBase
    {
        public ObservableCollection<CardDefItem> Items { get; } = new ObservableCollection<CardDefItem>();

        public CardDefItem HeaderItem => Items.FirstOrDefault(x => x.ControlType == ControlTypes.HeaderLabel);
        public CardDefItem ImageItem => Items.FirstOrDefault(x => x.ControlType == ControlTypes.Image);
        public CardDefItem BadgeItem => Items.LastOrDefault(x => x.ControlType == ControlTypes.Badge);

        public CpiCommandGroup[] CommandGroups { get; set; }

        public string ProjectionName { get; private set; }

        public string CardName { get; private set; }

        public IMetadata Metadata { get; }

        public ICommandExecutor CommandExecutor { get; private set; }

        public IExpressionRunner ExpressionRunner { get; }
        public ILogger Logger { get; }
        public IDialogService DialogService { get; }
        public bool AllowEditing { get; set; }

        private readonly IMediaHandler _mediaHandler;
        private readonly IDocumentHandler _documentHandler;
        private readonly INavigator _navigator;

        public bool FluidLayout { get; set; }

        private CpiAttachments _attachmentsConfig;

        public CardDef(IMetadata metadata, IExpressionRunner expressionRunner,
            IMediaHandler mediaHandler, IDocumentHandler documentHandler, INavigator navigator,
            ILogger logger, IDialogService dialogService)
        {
            Metadata = metadata;
            ExpressionRunner = expressionRunner;
            _mediaHandler = mediaHandler;
            _documentHandler = documentHandler;
            _navigator = navigator;
            Logger = logger;
            DialogService = dialogService;
        }

        public void Load(string projectionName, string cardName, ICommandExecutor commandExecutor, CpiAttachments attachmentsConfig)
        {
            ProjectionName = projectionName;
            CardName = cardName;
            CommandExecutor = commandExecutor;
            _attachmentsConfig = attachmentsConfig;
        }

        /// <summary>
        ///  When passed the name of the current card, this method will return the name of the command which will not be shown on the card, but will be used as the card's click command instead.
        /// </summary>
        public string GetDetailsCommandName()
        {
            return $"{CardName}_{CardName}_Details";
        }

        public IEnumerable<CardDefItem> PrepareItems(CardData cardData)
        {
            List<CardDefItem> items = new List<CardDefItem>();

            bool foundEditable = false;

            foreach (CardDefItem item in Items)
            {
                if (item.ControlType == ControlTypes.LoV)
                {
                    continue;
                }
            }

            foreach (CardDefItem item in Items)
            {
                item.UpdateIsVisible(cardData);

                if (item.ControlType == ControlTypes.Image || item.ControlType == ControlTypes.HeaderLabel || (FluidLayout && !item.IsVisible))
                {
                    continue;
                }

                if (item.IsLabelDynamic())
                {
                    item.UpdateDynamicLabel(cardData);
                }

                if (AllowEditing && !foundEditable && CanEditCardDefItem(cardData, item) && item.IsVisible)
                {
                    cardData.SetCardDef(this, item);
                    foundEditable = true;
                }
                else
                {
                    items.Add(item);
                }
            }

            if (!foundEditable)
            {
                cardData.SetCardDef(this, null);
            }

            return items;
        }

        private static bool CanEditCardDefItem(CardData cardData, CardDefItem item)
        {
            if (item.ControlType == ControlTypes.LoV)
            {
                return true;
            }

            if (item.Field == null || item.ControlType != ControlTypes.Field)
            {
                return false;
            }

            if (item.Field.Control != CpiControlType.Field)
            {
                return false;
            }

            CpiDataType dataType = item.Field.Datatype ?? CpiDataType.Text;
            if (dataType != CpiDataType.Number &&
                dataType != CpiDataType.Integer &&
                dataType != CpiDataType.Text &&
                dataType != CpiDataType.Boolean)
            {
                return false;
            }

            return item.IsEditable(cardData);
        }

        public bool MatchFilter(string filter, object dataContext)
        {
            if (string.IsNullOrEmpty(filter))
            {
                return true;
            }

            foreach (CardDefItem item in Items)
            {
                if (item.MatchFilter(filter, dataContext))
                {
                    return true;
                }
            }

            return false;
        }

        public async Task<Stream> GetCardImage(ViewData cardData, CancellationToken cancelToken)
        {
            if (ImageItem != null)
            {
                return await ImageItem.GetImage(cardData, cancelToken);
            }

            return null;
        }

        public int GetImageSize()
        {
            if (ImageItem?.Field?.ControlSize != null)
            {
                switch (ImageItem.Field.ControlSize.ToSizeHint())
                {
                    case SizeHint.Small:
                        return 50;
                    case SizeHint.Large:
                    case SizeHint.FullWidth:
                        return 150;
                    case SizeHint.Medium:
                    default:
                        return 100;
                }
            }
            else
            {
                return 100;
            }
        }

        public async Task<byte[]> GetCardImageBytes(ViewData cardData, CancellationToken cancelToken)
        {
            byte[] imageData = null;

            using (Stream imageStream = await GetCardImage(cardData, cancelToken))
            {
                cancelToken.ThrowIfCancellationRequested();

                if (imageStream is MemoryStream ms)
                {
                    imageData = ms.ToArray();
                }
                else if (imageStream != null)
                {
                    using (MemoryStream msCopy = new MemoryStream())
                    {
                        await imageStream.CopyToAsync(msCopy, 81920 /* default */, cancelToken);
                        cancelToken.ThrowIfCancellationRequested();

                        imageData = msCopy.ToArray();
                    }
                }
            }

            return imageData;
        }

        public string GetHeaderText(ViewData cardData)
        {
            CardDefItem headerItem = HeaderItem;
            if (headerItem != null)
            {
                if (headerItem.Converter is CardDefCreator.LabelConvertor &&
                    headerItem.ConverterParameter != null)
                {
                    return (string)headerItem.Converter.Convert(cardData, typeof(string), headerItem.ConverterParameter, null);
                }
                else
                {
                    return headerItem.GetDisplayValue(cardData);
                }
            }
            return null;
        }

        public CommandBlock CreateCommandBlock()
        {
            if (_attachmentsConfig == null && (CommandGroups == null || CommandGroups.Length <= 0))
            {
                return null;
            }

            CommandBlock commands = new CommandBlock(null, Metadata, CommandExecutor, ExpressionRunner);

            AttachmentCommandItem attachmentCommand = null;
            if (_attachmentsConfig != null)
            {
                attachmentCommand = new AttachmentCommandItem(_documentHandler, _mediaHandler, _navigator, ExpressionRunner);
                attachmentCommand.AttachmentsConfig = _attachmentsConfig;
            }

            commands.Load(ProjectionName, CommandGroups, false, true, false, new string[] { GetDetailsCommandName() }, attachmentCommand);

            return commands;
        }
    }
}
