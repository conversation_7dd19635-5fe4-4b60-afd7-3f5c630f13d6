﻿using System;
using Ifs.Uma.Framework.UI.Assistants;
using Ifs.Uma.Framework.UI.Attachments;
using Ifs.Uma.Framework.UI.DynamicAssistants;
using Ifs.Uma.Framework.UI.Trees;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Pages
{
    public interface IPageCreator
    {
        PageBase CreatePage(NavigationParameter navParam);
        PageBase CreatePage(PageClassification classification);
    }

    public sealed class PageCreator : IPageCreator
    {
        private readonly IResolver _resolver;
        private readonly IMetadata _metadata;
        private readonly ILogger _logger;

        public PageCreator(IResolver resolver, IMetadata metadata, ILogger logger)
        {
            _resolver = resolver;
            _metadata = metadata;
            _logger = logger;
        }

        public PageBase CreatePage(NavigationParameter navParam)
        {
            try
            {
                PageClassification pageClass = PageClassification.Detail;
                if (navParam is MetadataPageNavParam metaNavParam)
                {
                    pageClass = metaNavParam.GetPageClassification(_metadata);
                }
                else if (navParam is AttachmentNavParam attachNavParam)
                {
                    if (attachNavParam.Page == MediaListPage.PageName)
                    {
                        pageClass = PageClassification.MediaList;
                    }
                    else if (attachNavParam.Page == DocumentListPage.PageName || attachNavParam.Page == DocumentListPage.NoRevPageName)
                    {
                        pageClass = PageClassification.DocumentList;
                    }
                    else if (attachNavParam.Page == MediaDetailPage.PageName)
                    {
                        pageClass = PageClassification.MediaDetail;
                    }
                    else if (attachNavParam.Page == DocumentDetailPage.PageName)
                    {
                        pageClass = PageClassification.DocumentDetail;
                    }
                }

                return CreatePage(pageClass);
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Unexpected, ex);
                return null;
            }
        }

        public PageBase CreatePage(PageClassification classification)
        {
            try
            {
                if (classification == PageClassification.Assistant)
                {
                    return _resolver.Resolve<AssistantPage>();
                }
                else if (classification == PageClassification.DynamicAssistant)
                {
                    return _resolver.Resolve<DynamicAssistantPage>();
                }
                else if (classification == PageClassification.Tree)
                {
                    return _resolver.Resolve<TreePage>();
                }
                else if (classification == PageClassification.Workflow)
                {
                    return _resolver.Resolve<WorkflowPage>();
                }
                else if (classification == PageClassification.MediaDetail)
                {
                    return _resolver.Resolve<MediaDetailPage>();
                }
                else if (classification == PageClassification.MediaList)
                {
                    return _resolver.Resolve<MediaListPage>();
                }
                else if (classification == PageClassification.DocumentDetail)
                {
                    return _resolver.Resolve<DocumentDetailPage>();
                }
                else if (classification == PageClassification.DocumentList)
                {
                    return _resolver.Resolve<DocumentListPage>();
                }

                return _resolver.Resolve<ElementPage>();
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Unexpected, ex);
                return null;
            }
        }
    }
}
