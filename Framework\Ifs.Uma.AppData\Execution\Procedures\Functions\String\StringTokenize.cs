﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringTokenize : StringFunction
    {
        public const string FunctionName = "Tokenize";

        public StringTokenize()
            : base(FunctionName, 2, false)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string stringDelimiter = parameters[1].GetString();

            CpiTypeInfo stringType = new CpiTypeInfo
            {
                DataType = CpiDataType.Text,
                IsCollection = true
            };

            MarbleList strings = new MarbleList(stringType);

            if (string.IsNullOrEmpty(stringToModify) || string.IsNullOrEmpty(stringDelimiter))
            {
                return strings;
            }

            if (stringToModify.Contains(stringDelimiter))
            {
                foreach (string s in stringToModify.Split(stringDelimiter.ToCharArray(), StringSplitOptions.RemoveEmptyEntries))
                {
                    strings.TryConvertAndAdd(s);
                }
            }
            else
            {
                strings.TryConvertAndAdd(stringToModify);
            }

            return strings;
        }
    }
}
