﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.Tests;
using NUnit.Framework;
using Unity.Lifetime;
using Unity;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;
using Ifs.Uma.Tests.TestClasses;
using Ifs.Uma.AppData.Permissions;
using System.IO;
using System.Linq;
using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Tests.Attachments
{
    [TestFixture]
    public class DocumentHandlerTests : FrameworkTest
    {
        protected override void BeforeTest()
        {
            base.BeforeTest();

            Container.RegisterType<IOnlineAttachmentHandler, AlwaysOfflineOnlineAttachmentHandler>(new ContainerControlledLifetimeManager());
            Container.RegisterType<ILocalStorage, TestLocalStorage>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IAppPermissions, TestAppPermissions>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IDocumentHandler, DocumentHandler>(new ContainerControlledLifetimeManager());

            PrepareDatabase<FwDataContext>("Attachments.DocumentHandlerTestsSchema", "Attachments.DocumentHandlerTestsData");
        }

        [Test]
        public async Task CleanUpOldDocuments()
        {
            IDocumentHandler documentHandler = Resolve<IDocumentHandler>();
            FwDataContext ctx = CreateDataContext();

            // Insert a customer record

            RemoteRow customer = new RemoteRow("tst_customer");
            customer["CustomerNo"] = "CUST1";
            ITable customerTable = ctx.GetTable(ctx.Model.GetTable(customer.TableName));
            customerTable.InsertOnSubmit(customer);
            ctx.SubmitChanges(false);

            // Attach a document to the customer

            DocReferenceObject docRef = new DocReferenceObject();
            docRef.DocumentAccess = DocumentAccess.Edit;
            docRef.DocIssueObjstate = DocIssueObjstate.Preliminary;
            docRef.Title = "TestDocRef";
            docRef.LuName = "TstCustomer";
            docRef.KeyRef = "CUSTOMER_NO=CUST1^";
            docRef.DocClass = "200";
            EdmFile edmFile = new EdmFile();
            edmFile.FileName = "myfile.jpg";
            edmFile.DocType = "ORIGINAL";
            edmFile.FileType = "BITMAP";

            long docRefRowId;
            using (MemoryStream ms = new MemoryStream(new byte[] { 0x47 }))
            {
                docRefRowId = await documentHandler.NewDocumentAsync(docRef, edmFile, ms);
            }

            DocReferenceObject docRefInserted = ctx.DocReferenceObjects.SingleOrDefault(x => x.RowId == docRefRowId);
            Assert.IsNotNull(docRefInserted, "Failed to setup DocReferenceObject");
            EdmFile edmFileInserted = ctx.EdmFiles.SingleOrDefault();
            Assert.IsNotNull(edmFileInserted, "Failed to setup EdmFile");
            Assert.AreEqual(AttachmentStatus.RequiresUpload, edmFileInserted.AttachmentStatus, "EdmFile should be in status RequiresUpload after creation");

            ILocalFileInfo localFile = await documentHandler.GetLocalFileForDocumentAsync(edmFileInserted);
            bool fileExists = await localFile.ExistsAsync();
            Assert.IsTrue(fileExists, "File was not stored");

            // Check the document still exists after a cleanup

            await documentHandler.CleanupOldDocuments();

            Assert.IsNotNull(ctx.DocReferenceObjects.FirstOrDefault(), "DocReferenceObject was cleaned up while record still exists");
            Assert.IsNotNull(ctx.EdmFiles.FirstOrDefault(), "EdmFile was cleaned up while record still exists");

            // Remove the customer and check the doc ref was cleaned up

            customerTable.DeleteOnSubmit(customer);
            ctx.SubmitChanges(false);

            await documentHandler.CleanupOldDocuments();

            Assert.IsNull(ctx.DocReferenceObjects.FirstOrDefault(), "Failed to cleanup DocReferenceObject");
            Assert.IsNotNull(ctx.EdmFiles.FirstOrDefault(), "EdmFile was cleaned up before it finished uploading");

            fileExists = await localFile.ExistsAsync();
            Assert.IsTrue(fileExists, "File was cleared up too early");

            // Set the file to uploaded and make sure the edm file and local file get cleaned up

            ctx.EdmFiles.Attach(DocumentHandler.MobileAttachmentsProjection, edmFileInserted);
            edmFileInserted.AttachmentStatus = AttachmentStatus.Uploaded;
            ctx.SubmitChanges(false);

            await documentHandler.CleanupOldDocuments();

            Assert.IsNull(ctx.EdmFiles.FirstOrDefault(), "Failed to cleanup EdmFile");

            fileExists = await localFile.ExistsAsync();
            Assert.IsFalse(fileExists, "File was not deleted");
        }

        [Test]
        public async Task UpdateTitle()
        {
            IDocumentHandler documentHandler = Resolve<IDocumentHandler>();
            FwDataContext ctx = CreateDataContext();

            // Insert a customer record

            RemoteRow customer = new RemoteRow("tst_customer");
            customer["CustomerNo"] = "CUST1";
            ITable customerTable = ctx.GetTable(ctx.Model.GetTable(customer.TableName));
            customerTable.InsertOnSubmit(customer);
            ctx.SubmitChanges(false);

            // Attach a document to the customer

            DocReferenceObject docRef = new DocReferenceObject();
            docRef.DocumentAccess = DocumentAccess.Edit;
            docRef.DocIssueObjstate = DocIssueObjstate.Preliminary;
            docRef.Title = "TestDocRef";
            docRef.LuName = "TstCustomer";
            docRef.KeyRef = "CUSTOMER_NO=CUST1^";
            docRef.DocClass = "200";
            EdmFile edmFile = new EdmFile();
            edmFile.FileName = "myfile.jpg";
            edmFile.DocType = "ORIGINAL";
            edmFile.FileType = "BITMAP";

            long docRefRowId;
            using (MemoryStream ms = new MemoryStream(new byte[] { 0x47 }))
            {
                docRefRowId = await documentHandler.NewDocumentAsync(docRef, edmFile, ms);
            }

            DocReferenceObject docRefInserted = (await documentHandler.GetDocumentInfoAsync(docRefRowId))?.DocumentRevision;
            Assert.IsNotNull(docRefInserted, "Failed to setup DocReferenceObject");
            Assert.AreEqual("TestDocRef", docRefInserted.Title, "Failed to set doc ref title");

            docRefInserted.Title = "UpdateTitle";

            await documentHandler.UpdateDocumentReferenceAsync(docRefInserted, new[] { nameof(docRefInserted.Title) });

            DocReferenceObject docRefUpdated = (await documentHandler.GetDocumentInfoAsync(docRefRowId))?.DocumentRevision;
            Assert.IsNotNull(docRefUpdated, "Failed to update DocReferenceObject");
            Assert.AreEqual("UpdateTitle", docRefUpdated.Title, "Failed to update doc ref title");
        }

        [Test]
        public async Task NewDocumentAndRevise()
        {
            IDocumentHandler documentHandler = Resolve<IDocumentHandler>();
            FwDataContext ctx = CreateDataContext();

            // Insert a customer record

            RemoteRow customer = new RemoteRow("tst_customer");
            customer["CustomerNo"] = "CUST1";
            ITable customerTable = ctx.GetTable(ctx.Model.GetTable(customer.TableName));
            customerTable.InsertOnSubmit(customer);
            ctx.SubmitChanges(false);

            // Attach a document to the customer

            DocReferenceObject docRef = new DocReferenceObject();
            docRef.DocumentAccess = DocumentAccess.Edit;
            docRef.DocIssueObjstate = DocIssueObjstate.Preliminary;
            docRef.Title = "TestDocRef";
            docRef.LuName = "TstCustomer";
            docRef.KeyRef = "CUSTOMER_NO=CUST1^";
            docRef.DocClass = "200";
            EdmFile edmFile = new EdmFile();
            edmFile.FileName = "myfile.jpg";
            edmFile.DocType = "ORIGINAL";
            edmFile.FileType = "BITMAP";

            long docRefRowId;
            byte[] docData = new byte[] { 0x47 };
            using (MemoryStream ms = new MemoryStream(docData))
            {
                docRefRowId = await documentHandler.NewDocumentAsync(docRef, edmFile, ms);
            }

            DocRevisionInfo docInserted = await documentHandler.GetDocumentInfoAsync(docRefRowId);
            Assert.IsNotNull(docInserted, "Failed to add document");
            Assert.AreEqual("TestDocRef", docInserted.DocumentRevision.Title, "Failed to set doc ref title");
            Assert.AreEqual(AttachmentStatus.RequiresUpload, docInserted.EdmFile.AttachmentStatus, "Attachment status was not set to requries upload");
            ILocalFileInfo localFile = await documentHandler.GetLocalFileForDocumentAsync(docInserted.EdmFile);
            await CheckFileContents(localFile, docData);

            // Revise the document with a new title and file

            docInserted.DocumentRevision.Title = "UpdateTitle";

            EdmFile edmFileRevision = new EdmFile();
            edmFileRevision.FileName = "myfile.jpg";
            edmFileRevision.DocType = "ORIGINAL";
            edmFileRevision.FileType = "BITMAP";

            byte[] docRevisionData = new byte[] { 0x48 };
            using (MemoryStream ms = new MemoryStream(docRevisionData))
            {
                docRefRowId = await documentHandler.NewDocumentRevisionAsync(docInserted.DocumentRevision, edmFileRevision, ms);
            }

            DocRevisionInfo docUpdated = await documentHandler.GetDocumentInfoAsync(docRefRowId);
            Assert.IsNotNull(docUpdated, "Failed to revise document");
            Assert.AreEqual("UpdateTitle", docUpdated.DocumentRevision.Title, "Failed to update doc ref title");
            Assert.AreEqual(AttachmentStatus.RequiresUpload, docUpdated.EdmFile.AttachmentStatus, "Attachment status was not set to requries upload");

            ILocalFileInfo localFileRevision = await documentHandler.GetLocalFileForDocumentAsync(docUpdated.EdmFile);
            await CheckFileContents(localFileRevision, docRevisionData);
        }

        private async Task CheckFileContents(ILocalFileInfo localFile, byte[] data)
        {
            bool fileExists = await localFile.ExistsAsync();
            Assert.IsTrue(fileExists, "File was not stored");
            using (MemoryStream ms = new MemoryStream())
            using (Stream stream = await localFile.OpenStreamAsync(LocalFileMode.OpenOrCreate, LocalFileAccess.Read))
            {
                stream.CopyTo(ms);
                byte[] fileBytes = ms.ToArray();
                CollectionAssert.AreEqual(data, fileBytes, "File does not have the correct content");
            }
        }
    }
}
