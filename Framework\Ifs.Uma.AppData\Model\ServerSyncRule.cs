﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_" + DbTableName, Columns = nameof(Entity), Unique = true)]

    public class ServerSyncRule : RemoteRow
    { 
        public const string DbTableName = FwDataContext.FwTablePrefix + "sync_rule";

        [Column(Mandatory = true, ServerPrimaryKey = true)]
        public string Entity { get; set; }

        [Column]
        public string DeliveryMethod { get; set; }

        [Column]
        public int OnDemandExpiry { get; set; }

        [Column]
        public string FilterMethod { get; set; }

        public ServerSyncRule()
            : base(DbTableName)
        {
        }
    }
}
