﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Attempt to rewrite cross joins as inner joins
    /// </summary>
    internal class CrossJoinRewriter : DbExpressionVisitor
    {
        private Expression m_currentWhere;

        public static Expression Rewrite(Expression expression)
        {
            return new CrossJoinRewriter().Visit(expression);
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            if (node == null) return null;
            var saveWhere = m_currentWhere;
            try
            {
                m_currentWhere = node.Where;
                var result = (SelectExpression)base.VisitSelect(node);
                if (m_currentWhere != result.Where)
                {
                    return result.SetWhere(m_currentWhere);
                }
                return result;
            }
            finally
            {
                m_currentWhere = saveWhere;
            }
        }

        protected override Expression VisitJoin(JoinExpression node)
        {
            node = (JoinExpression)base.VisitJoin(node);
            if ((node.Join == JoinType.CrossJoin || node.Join == JoinType.CrossApply) && m_currentWhere != null)
            {
                // try to figure out which parts of the current where expression can be used for a join condition
                var declaredLeft = DeclaredAliasGatherer.Gather(node.Left);
                var declaredRight = DeclaredAliasGatherer.Gather(node.Right);
                var declared = new HashSet<TableAlias>(declaredLeft.Union(declaredRight));
                var exprs = m_currentWhere.Split(ExpressionType.And, ExpressionType.AndAlso);
                var good = exprs.Where(e => CanBeJoinCondition(e, declaredLeft, declaredRight, declared)).ToList();
                if (good.Count > 0)
                {
                    var condition = good.Join(ExpressionType.And);
                    node = node.Update(JoinType.InnerJoin, node.Left, node.Right, condition);
                    var newWhere = exprs.Where(e => !good.Contains(e)).Join(ExpressionType.And);
                    m_currentWhere = newWhere;
                }
            }
            return node;
        }

        private static bool CanBeJoinCondition(Expression expression, HashSet<TableAlias> left, HashSet<TableAlias> right, HashSet<TableAlias> all)
        {
            // an expression is good if it has at least one reference to an alias from both left & right sets and does
            // not have any additional references that are not in both left & right sets
            var referenced = ReferencedAliasGatherer.Gather(expression);
            var leftOkay = referenced.Intersect(left).Any();
            var rightOkay = referenced.Intersect(right).Any();
            var subset = referenced.IsSubsetOf(all);
            return leftOkay && rightOkay && subset;
        }
    }
}