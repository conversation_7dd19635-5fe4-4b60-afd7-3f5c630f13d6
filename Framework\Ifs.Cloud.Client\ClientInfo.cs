﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Ifs.Uma.Utility;

namespace Ifs.Cloud.Client
{
    /// <summary>
    /// Info about an app that a client provides.
    /// </summary>
    [DataContract]
    public class ClientInfo
    {
        #region Properties

        /// <summary>
        /// Name of the application
        /// </summary>
        [IgnoreDataMember]
        public string AppName { get; set; }
        
        /// <summary>
        /// Name of Client Version.
        /// </summary>  
        [DataMember]
        public string ClientVersion { get; set; }
        
        /// <summary>
        /// Name of Device Brand.
        /// </summary>
        [DataMember]
        public string Brand { get; set; }
        
        /// <summary>
        /// Name of Client Carrier.
        /// </summary>
        [DataMember]
        public string Carrier { get; set; }
        
        /// <summary>
        /// Name of Client Database.
        /// </summary>
        [DataMember]
        public string ClientDb { get; set; }
        
        /// <summary>
        /// Name of Client Runtime.
        /// </summary>
        [DataMember]
        public string ClientRuntime { get; set; }
        
        /// <summary>
        /// Name of Device Model.
        /// </summary>
        [DataMember]
        public string Model { get; set; }
        
        /// <summary>
        /// Name of Client Platform.
        /// </summary>
        [DataMember]
        public string Platform { get; set; }
        
        /// <summary>
        /// Name of Client Operating System.
        /// </summary>
        [DataMember]
        public string Os { get; set; }
        
        /// <summary>
        /// Unique Device Identitifier.
        /// </summary> 
        [DataMember]
        public string DeviceIdentifier { get; set; }

        /// <summary>
        /// Version of the client OS.
        /// </summary>
        [DataMember]
        public string OsVersion { get; set; }

        /// <summary>
        /// The vendor responsible for the client application - null if this is the same as the cloud application vendor
        /// </summary>
        [IgnoreDataMember]
        public string ClientAppVendor { get; set; }
        
        /// <summary>
        /// Identifier for the Application
        /// </summary>
        [IgnoreDataMember]
        public string ApplicationId { get; set; }

        /// <summary>
        /// Version of the Application
        /// </summary>
        [IgnoreDataMember]
        public string AppVersion { get; set; }

        /// <summary>
        /// Name of the Package
        /// </summary>
        [IgnoreDataMember]
        public string PackageName { get; set; }

        #endregion

        #region Constructors/Destructors

        public ClientInfo()
        {
            Os = DeviceInfo.OperatingSystem + "-" + DeviceInfo.Type;
            OsVersion = DeviceInfo.OperatingSystemVersion;
            Model = DeviceInfo.Model;
            Platform = DeviceInfo.OperatingSystem.ToString();
            //Locale = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        }
        
        #endregion        
    }
}
