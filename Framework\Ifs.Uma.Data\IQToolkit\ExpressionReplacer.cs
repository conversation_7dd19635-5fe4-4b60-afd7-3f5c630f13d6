﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

namespace IQToolkit
{
    /// <summary>
    /// Replaces references to one specific instance of an expression node with another node
    /// </summary>
    internal class ExpressionReplacer : ExpressionVisitorEx
    {
        Expression searchFor;
        Expression replaceWith;

        private ExpressionReplacer(Expression searchFor, Expression replaceWith)
        {
            this.searchFor = searchFor;
            this.replaceWith = replaceWith;
        }

        public static Expression Replace(Expression expression, Expression searchFor, Expression replaceWith)
        {
            return new ExpressionReplacer(searchFor, replaceWith).Visit(expression);
        }

        public static Expression ReplaceAll(Expression expression, Expression[] searchFor, Expression[] replaceWith)
        {
            if (searchFor != null && replaceWith != null)
            {
                for (int i = 0, n = searchFor.Length; i < n; i++)
                {
                    expression = Replace(expression, searchFor[i], replaceWith[i]);
                }
            }
            return expression;
        }

        public override Expression Visit(Expression node)
        {
            if (node == this.searchFor)
            {
                return this.replaceWith;
            }
            return base.Visit(node);
        }
    }
}
