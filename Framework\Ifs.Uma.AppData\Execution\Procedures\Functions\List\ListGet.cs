﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.List
{
    internal sealed class ListGet : ListFunction
    {
        public const string FunctionName = "Get";

        public ListGet()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            MarbleList list = PrepareList(context, parameters[0]);
            long? value = parameters[1].GetInteger();

            if (!value.HasValue)
            {
                throw context.Fail($"Invalid index '{value}'");
            }

            return list[(int)value];
        }
    }
}
