# IFS HTTP Client Tool - Examples

## Basic Usage Examples

### 1. Simple GET Request (No Authentication)
```bash
dotnet run -- --url "https://httpbin.org/get" --verbose
```

### 2. TOKEN_DIRECT Authentication
```bash
dotnet run -- --url "https://your-ifs-server.com/mob/ifsapplications/projection/v1/TestService.svc/TestEndpoint" --username "testuser" --password "testpass" --systemid "IFSAPP" --verbose
```

### 3. Using Direct Access Token
```bash
dotnet run -- --url "https://your-ifs-server.com/api/data" --token "your-access-token-here" --verbose
```

### 4. Using the Batch File (Windows)
```cmd
run.bat --url "https://httpbin.org/get" --verbose
```

## Real-World IFS Examples

### Testing IFS Cloud API Endpoint
```bash
dotnet run -- --url "https://your-ifs-cloud.com/mob/ifsapplications/projection/v1/MobileClientRuntime.svc/Activate" --username "your-username" --password "your-password" --systemid "IFSAPP" --verbose
```

### Testing with Different System ID
```bash
dotnet run -- --url "https://your-ifs-cloud.com/mob/ifsapplications/projection/v1/TestService.svc/GetData" --username "testuser" --password "testpass" --systemid "TESTSYS" --verbose
```

## Output Examples

### Successful Request with Authentication
```
Making GET request to: https://your-server.com/api/endpoint
Server: Not specified
System ID: IFSAPP
Username: testuser
Using Token: False

Using TOKEN_DIRECT authentication
Added TOKEN_DIRECT authentication header for user: testuser
Request Headers:
  Authorization: Basic <encoded-token>
  User-Agent: IFS-HttpClient-Tool/1.0

Response Status: OK
Response Headers:
  Content-Type: application/json
  Date: Wed, 28 May 2025 13:07:29 GMT

Response Content:
{
  "status": "success",
  "data": {...}
}
```

### Failed Authentication
```
Making GET request to: https://your-server.com/api/endpoint
Server: Not specified
System ID: IFSAPP
Username: baduser
Using Token: False

Using TOKEN_DIRECT authentication
Warning: TOKEN_DIRECT authentication failed: Authentication failed

Response Status: Unauthorized
Response Content:
{
  "error": "Invalid credentials"
}
```

## Tips

1. **Use --verbose flag** to see detailed request/response information
2. **System ID** is optional but recommended for IFS Cloud environments
3. **Direct tokens** can be used instead of username/password for pre-authenticated scenarios
4. **Exit codes**: 0 = success, 1 = error (useful for scripting)

## Common IFS Cloud Endpoints

- Activation: `/mob/ifsapplications/projection/v1/MobileClientRuntime.svc/Activate`
- Test Service: `/mob/ifsapplications/projection/v1/TestService.svc/TestEndpoint`
- Binary Resources: `/mob/ifsapplications/projection/v1/KathCustomerInfo.svc/binaryresource/`
- General API: `/mob/ifsapplications/projection/v1/YourService.svc/YourEndpoint`
