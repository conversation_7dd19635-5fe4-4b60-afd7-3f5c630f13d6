﻿using System.Collections.Generic;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class UnionSelectExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.UnionSelect;
        
        public string From { get; set; }

        public List<ResultColumnExpression> Colunms { get; set; }

        internal UnionSelectExpression(string from, List<ResultColumnExpression> colunms) 
        {
            From = from;
            Colunms = colunms;
        }
    }

    public partial class IfsExpression
    {
        public static UnionSelectExpression UnionSelect(string from, List<ResultColumnExpression> colunms)
        {
            return new UnionSelectExpression(from, colunms);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitUnionSelectExpression(UnionSelectExpression exp)
        {
            return exp;
        }
    }
}
