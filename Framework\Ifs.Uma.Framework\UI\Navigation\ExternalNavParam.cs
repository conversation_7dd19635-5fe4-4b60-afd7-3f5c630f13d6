﻿using System;
using System.Runtime.Serialization;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Navigation
{
    [DataContract]
    public class ExternalNavParam : NavigationParameter
    {
        [DataMember]
        public string Url { get; private set; }

        private ExternalNavParam(string url)
        {
            Url = url;
        }

        public static ExternalNavParam Create(string url)
        {
            try
            {
                ExternalNavParam navParam = new ExternalNavParam(url);
                return navParam;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}
