{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<Convert_ToBoolean>": {"name": "Convert_ToBoolean", "type": "Function", "params": [{"name": "TextInput"}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Convert", "name": "ToBoolean", "paramsArray": ["${TextInput}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}