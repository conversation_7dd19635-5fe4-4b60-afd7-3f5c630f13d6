﻿using System.Linq;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Model;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Security
{
    internal sealed class SecurityIsMediaGranted : SecurityFunction
    {
        public const string FunctionName = "IsMediaGranted";

        public SecurityIsMediaGranted()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string projection = MediaHandler.MediaProjectionName;
            string entity = nameof(MediaLibrary);

            var security = context.DbDataContext.ClientSecurities
                .FirstOrDefault(x => x.Projection == projection && x.ObjectName == entity);

            if (security == null)
            {
                return false;
            }

            return security.Granted;
        }
    }
}
