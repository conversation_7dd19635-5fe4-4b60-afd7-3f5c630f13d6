﻿{
  "name": "FndTstOffline",
  "version": "1706901162:1948287535",
  "component": "FNDTST",
  "projection": {
    "service": "FndTstOffline.svc",
    "version": "1948287535",
    "contains": {},
    "entities": {},
    "procedures": {
      "Function<StringLastIndexTest>": {
        "name": "StringLastIndexTest",
        "type": "Function",
        "params": [
          {
            "name": "TextInput",
            "dataType": "Text"
          },
          {
            "name": "TargetChar",
            "dataType": "Text"
          }
        ],
        "layers": [
          {
            "vars": [
              {
                "name": "Result",
                "dataType": "Integer"
              }
            ],
            "execute": [
              {
                "call": {
                  "method": "proc",
                  "args": {
                    "name": "LastIndexOf",
                    "namespace": "String",
                    "paramsArray": [ "${TextInput}", "${TargetChar}" ]
                  }
                },
                "assign": "Result"
              },
              {
                "call": {
                  "method": "return",
                  "args": {
                    "name": "Result"
                  }
                }
              }
            ]
          }
        ]
      },
      "Function<StringTestFirstIndexOf>": {
        "name": "StringTestFirstIndexOf",
        "type": "Function",
        "params": [
          {
            "name": "TextInput",
            "dataType": "Text"
          },
          {
            "name": "TargetChar",
            "dataType": "Text"
          }
        ],
        "layers": [
          {
            "vars": [
              {
                "name": "Result",
                "dataType": "Integer"
              }
            ],
            "execute": [
              {
                "call": {
                  "method": "proc",
                  "args": {
                    "name": "FirstIndexOf",
                    "namespace": "String",
                    "paramsArray": [ "${TextInput}", "${TargetChar}" ]
                  }
                },
                "assign": "Result"
              },
              {
                "call": {
                  "method": "return",
                  "args": {
                    "name": "Result"
                  }
                }
              }
            ]
          }
        ]
      },
      "Function<StringTestSpecifiedIndexOf>": {
        "name": "StringTestSpecifiedIndexOf",
        "type": "Function",
        "params": [
          {
            "name": "TextInput",
            "dataType": "Text"
          },
          {
            "name": "TargetChar",
            "dataType": "Text"
          },
          {
            "name": "Occurrence",
            "dataType": "Integer"
          }
        ],
        "layers": [
          {
            "vars": [
              {
                "name": "Result",
                "dataType": "Integer"
              }
            ],
            "execute": [
              {
                "call": {
                  "method": "proc",
                  "args": {
                    "name": "SpecifiedIndexOf",
                    "namespace": "String",
                    "paramsArray": [ "${TextInput}", "${TargetChar}", "${Occurrence}" ]
                  }
                },
                "assign": "Result"
              },
              {
                "call": {
                  "method": "return",
                  "args": {
                    "name": "Result"
                  }
                }
              }
            ]
          }
        ]
      }
    }
  }
}
