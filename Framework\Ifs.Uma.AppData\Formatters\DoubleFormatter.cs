﻿using System;
using System.Globalization;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility.Formatters;

namespace Ifs.Uma.AppData.Formatters
{
    public sealed class DoubleFormatter : IValueFormatter
    {
        private readonly int? _decimalPlaces;
        private readonly NumberFormat _format;

        public DoubleFormatter(IMetadata metadata, string projectionName, CpiField fieldDef)
        {
            if (fieldDef == null) throw new ArgumentNullException(nameof(fieldDef));
        
            var attribute = metadata.FindAttribute(projectionName, fieldDef.Entity, fieldDef.Attribute);
            if (attribute?.Scale != null)
            {
                _decimalPlaces = attribute.Scale.Value;
            }

            if (fieldDef.Format.HasValue)
            {
                switch (fieldDef.Format.Value)
                {
                    case CpiFormat.IfsCurrency:
                        _format = NumberFormat.Currency;
                        break;
                    case CpiFormat.Percentage:
                        _format = NumberFormat.Percentage;
                        break;
                    default:
                        _format = NumberFormat.Decimal;
                        break;
                }
            }
        }

        public DoubleFormatter(int? decimalPlaces, NumberFormat format)
        {
            _decimalPlaces = decimalPlaces;
            _format = format;
        }

        public string Format(object value)
        {
            if (value is double dValue)
            {
                return Format(dValue, _decimalPlaces, _format);
            }

            return OrdinalValueFormatter.Instance.Format(value);
        }

        public static string Format(double value, int? decimalPlaces, NumberFormat format)
        {
            if (format == NumberFormat.Percentage)
            {
                double percValue = value;
                return percValue.ToString("P");
            }

            if (format == NumberFormat.Decimal)
            {
                return value.ToString("N");
            }

            if (format == NumberFormat.Currency)
            {
                return value.ToString("F2");
            }

            string str;
            if (decimalPlaces.HasValue && decimalPlaces.Value >= 0 && decimalPlaces.Value <= 16)
            {
                str = value.ToString("F" + decimalPlaces.Value.ToString(CultureInfo.InvariantCulture), CultureInfo.CurrentCulture);

                if (decimalPlaces.Value == 0)
                {
                    return str;
                }
            }
            else
            {
                str = value.ToString("F16", CultureInfo.CurrentCulture);
            }
            str = str.TrimEnd('0');
            str = str.TrimEnd(CultureInfo.CurrentCulture.NumberFormat.NumberDecimalSeparator.ToCharArray());
            return str;
        }
    }
}
