using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Tests
{
    public class TestDataContext : FwDataContext
    {
        public TestDataContext(DbInternal db)
            : base(db)
        {
        }

        public Table<CityRow> Cities
        {
            get { return GetTable<CityRow>(); }
        }

        public Table<LibraryRow> Libraries
        {
            get { return GetTable<LibraryRow>(); }
        }

        public Table<BookRow> Books
        {
            get { return GetTable<BookRow>(); }
        }

        public Table<ConnectableRow> Connectables
        {
            get { return GetTable<ConnectableRow>(); }
        }

        public Table<ObjectConnectionRow> ObjectConnections
        {
            get { return GetTable<ObjectConnectionRow>(); }
        }
    }

    [Table(Name = "city")]
    [Index(Name = "ix_city", Columns = "CityId", Unique = true)]
    public class CityRow
    {
        [Column(PrimaryKey = true)]
        public string CityId { get; set; }
        [Column(Mandatory = true)]
        public string CityName { get; set; }
        [Column]
        public string Country { get; set; }
    }

    [Table(Name = "library")]
    [Index(Name = "ix_library", Columns = "LibraryId", Unique = true)]
    [Relation(ReferencedRowType = typeof(CityRow), Columns = nameof(CityId), ReferencedColumns = nameof(CityRow.CityId))]
    public class LibraryRow
    {
        [Column(PrimaryKey = true)]
        public long LibraryId { get; set; }
        [Column]
        public string LibraryName { get; set; }
        [Column]
        public string CityId { get; set; }
    }

    [Table(Name = "book")]
    [Index(Name = "ix_book", Columns = "ISBN", Unique = true)]
    public class BookRow
    {
        [Column(PrimaryKey = true)]
        public string ISBN { get; set; }
        [Column]
        public long LibraryId { get; set; }
        [Column(Mandatory = true)]
        public string Name { get; set; }
    }

    [Table(Name = "object_connection")]
    [Index(Name = "ix_object_connection", Columns = "ObjConnectionId", Unique = true)]
    [ObjConnection(LuNameColumn = nameof(LuName), KeyRefColumn = nameof(KeyRef))]
    public class ObjectConnectionRow
    {
        [Column(PrimaryKey = true)]
        public string ObjConnectionId { get; set; }
        [Column]
        public string LuName { get; set; }
        [Column]
        public string KeyRef { get; set; }
    }

    [Table(Name = "connectable")]
    [Index(Name = "ix_connectable", Columns = "NumberId, StringId", Unique = true)]
    public class ConnectableRow
    {
        [Column(PrimaryKey = true)]
        public long NumberId { get; set; }
        [Column(PrimaryKey = true)]
        public string StringId { get; set; }
        [Column]
        public string Value { get; set; }
    }
}
