﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.Attachment
{
    [TestFixture]
    public class AttachmentConnectTests : ProcedureTest
    {
        [Test]
        public async Task Attachment_CreateAndConnectMedia()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["LuName"] = "TstCustomer";
            parameters["KeyRef"] = "CUSTOMER=100^";
            parameters["Name"] = "TestName";

            FndMediaKeys result = await Call<FndMediaKeys>("Attachment_CreateAndConnectMedia", parameters);
            Assert.AreEqual(-1L, result.ItemId);

            FwDataContext ctx = CreateDataContext();

            MediaLibraryItem item = ctx.MediaLibraryItems.SingleOrDefault();
            Assert.IsNotNull(item, "Failed to create MediaLibraryItem");
            Assert.AreEqual(-1L, item.ItemId, "MediaLibraryItem ItemId does not match");
            Assert.AreEqual("TestName", item.Name, "MediaLibraryItem Name does not match");
            Assert.IsNotNull(item.MediaFile, "MediaLibraryItem MediaFile shouldn't be null");
            Assert.AreEqual(AttachmentStatus.Preparing, item.AttachmentStatus, "Generated MediaLibraryItem AttachmentStatus is incorrect");

            MediaLibrary library = ctx.MediaLibraries.SingleOrDefault();
            Assert.IsNotNull(library, "Failed to create MediaLibrary");
            Assert.AreEqual(item.LibraryId, library.LibraryId, "MediaLibrary LibraryId does not match");
            Assert.AreEqual("TstCustomer", library.LuName, "MediaLibrary LuName does not match");
            Assert.AreEqual("CUSTOMER=100^", library.KeyRef, "MediaLibrary KeyRef does not match");

            MediaItem mediaItem = ctx.MediaItems.SingleOrDefault();
            Assert.IsNotNull(mediaItem, "Failed to create MediaItem");
            Assert.AreEqual(mediaItem.ItemId, item.ItemId);
            Assert.AreEqual(item.Name, mediaItem.Name, "MediaItem Name does not match");
        }

        [Test]
        public async Task Attachment_ConnectMedia()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>
            {
                ["LuName"] = "TstCustomer",
                ["KeyRef"] = "CUSTOMER=100^",
                ["Name"] = "TestName"
            };

            FndMediaKeys result = await Call<FndMediaKeys>("Attachment_CreateAndConnectMedia", parameters);
            Assert.AreEqual(-1L, result.ItemId);

            FwDataContext ctx = CreateDataContext();

            Dictionary<string, object> parameters2 = new Dictionary<string, object>
            {
                ["MediaKeys"] = result,
                ["ToLuName"] = "TstCustomer",
                ["ToKeyRef"] = "CUSTOMER=101^"
            };

            await Call<FndMediaKeys>("Attachment_ConnectMedia", parameters2);

            MediaLibraryItem[] mediaLibraryItems = ctx.MediaLibraryItems.ToArray();
            MediaLibrary[] mediaLibraries = ctx.MediaLibraries.ToArray();

            MediaLibraryItem item = mediaLibraryItems[0];
            Assert.IsNotNull(item, "Failed to create MediaLibraryItem");
            Assert.AreEqual(-1L, item.ItemId, "MediaLibraryItem ItemId does not match");
            Assert.AreEqual("TestName", item.Name, "MediaLibraryItem Name does not match");
            Assert.IsNotNull(item.MediaFile, "MediaLibraryItem MediaFile shouldn't be null");
            Assert.AreEqual(AttachmentStatus.Preparing, item.AttachmentStatus, "Generated MediaLibraryItem AttachmentStatus is incorrect");

            MediaLibrary library = mediaLibraries[0];
            Assert.IsNotNull(library, "Failed to create MediaLibrary");
            Assert.AreEqual(item.LibraryId, library.LibraryId, "MediaLibrary LibraryId does not match");
            Assert.AreEqual("TstCustomer", library.LuName, "MediaLibrary LuName does not match");
            Assert.AreEqual("CUSTOMER=100^", library.KeyRef, "MediaLibrary KeyRef does not match");

            MediaLibraryItem connectedItem = mediaLibraryItems[1];
            Assert.IsNotNull(connectedItem, "Failed to create MediaLibraryItem");
            Assert.AreEqual(-1L, connectedItem.ItemId, "MediaLibraryItem ItemId does not match");
            Assert.AreEqual("TestName", connectedItem.Name, "MediaLibraryItem Name does not match");
            Assert.IsNotNull(connectedItem.MediaFile, "MediaLibraryItem MediaFile shouldn't be null");

            MediaLibrary library2 = mediaLibraries[1];
            Assert.IsNotNull(library2, "Failed to create MediaLibrary");
            Assert.AreEqual(connectedItem.LibraryId, library2.LibraryId, "MediaLibrary LibraryId does not match");
            Assert.AreEqual("TstCustomer", library2.LuName, "MediaLibrary LuName does not match");
            Assert.AreEqual("CUSTOMER=101^", library2.KeyRef, "MediaLibrary KeyRef does not match");

            MediaItem mediaItem = ctx.MediaItems.SingleOrDefault();
            Assert.IsNotNull(mediaItem, "Failed to create MediaItem");
            Assert.AreEqual(mediaItem.ItemId, item.ItemId);
            Assert.AreEqual(item.Name, mediaItem.Name, "MediaItem Name does not match");
        }

        [Test]
        public async Task Attachment_CreateAndConnectDoc()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["LuName"] = "TstCustomer";
            parameters["KeyRef"] = "CUTSTOMER=100^";
            parameters["Title"] = "TestTitle";

            FndDocumentKeys result = await Call<FndDocumentKeys>("Attachment_CreateAndConnectDoc", parameters);
            Assert.AreEqual("200", result.DocClass);
            Assert.AreEqual("-1", result.DocNo);
            Assert.AreEqual("-1", result.DocSheet);
            Assert.AreEqual("-1", result.DocRev);

            FwDataContext ctx = CreateDataContext();

            DocIssue docIssue = ctx.DocIssues.SingleOrDefault();
            Assert.IsNotNull(docIssue, "Failed to create DocIssue");
            Assert.AreEqual(result.DocClass, docIssue.DocClass, "DocIssue DocClass does not match");
            Assert.AreEqual(result.DocNo, docIssue.DocNo, "DocIssue DocNo does not match");
            Assert.AreEqual(result.DocSheet, docIssue.DocSheet, "DocIssue DocSheet does not match");
            Assert.AreEqual(result.DocRev, docIssue.DocRev, "DocIssue DocRev does not match");
            Assert.AreEqual("TestTitle", docIssue.Title, "DocIssue Title does not match");

            DocReferenceObject docRef = ctx.DocReferenceObjects.SingleOrDefault();
            Assert.IsNotNull(docRef, "Failed to create DocReferenceObject");
            Assert.AreEqual(result.DocClass, docRef.DocClass, "DocReferenceObject DocClass does not match");
            Assert.AreEqual(result.DocNo, docRef.DocNo, "DocReferenceObject DocNo does not match");
            Assert.AreEqual(result.DocSheet, docRef.DocSheet, "DocReferenceObject DocSheet does not match");
            Assert.AreEqual(result.DocRev, docRef.DocRev, "DocReferenceObject DocRev does not match");
            Assert.AreEqual("TstCustomer", docRef.LuName, "DocReferenceObject LuName does not match");
            Assert.AreEqual("CUTSTOMER=100^", docRef.KeyRef, "DocReferenceObject KeyRef does not match");
            Assert.AreEqual("TestTitle", docRef.Title, "DocReferenceObject Title does not match");
        }

        [Test]
        public async Task Attachment_ConnectDoc()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>
            {
                ["LuName"] = "TstCustomer",
                ["KeyRef"] = "CUSTOMER=100^",
                ["Title"] = "TestTitle"
            };

            FndDocumentKeys result = await Call<FndDocumentKeys>("Attachment_CreateAndConnectDoc", parameters);
            Assert.AreEqual("200", result.DocClass);
            Assert.AreEqual("-1", result.DocNo);
            Assert.AreEqual("-1", result.DocSheet);
            Assert.AreEqual("-1", result.DocRev);

            FwDataContext ctx = CreateDataContext();

            DocIssue docIssue = ctx.DocIssues.SingleOrDefault();
            Assert.IsNotNull(docIssue, "Failed to create DocIssue");
            Assert.AreEqual(result.DocClass, docIssue.DocClass, "DocIssue DocClass does not match");
            Assert.AreEqual(result.DocNo, docIssue.DocNo, "DocIssue DocNo does not match");
            Assert.AreEqual(result.DocSheet, docIssue.DocSheet, "DocIssue DocSheet does not match");
            Assert.AreEqual(result.DocRev, docIssue.DocRev, "DocIssue DocRev does not match");
            Assert.AreEqual("TestTitle", docIssue.Title, "DocIssue Title does not match");

            Dictionary<string, object> parameters2 = new Dictionary<string, object>
            {
                ["DocKeys"] = result,
                ["ToLuName"] = "TstCustomer",
                ["ToKeyRef"] = "CUSTOMER=101^"
            };

            await Call<FndDocumentKeys>("Attachment_ConnectDoc", parameters2);

            DocReferenceObject[] docReferenceObjects = ctx.DocReferenceObjects.ToArray();

            Assert.AreEqual(2, docReferenceObjects.Length);

            DocReferenceObject docRef = docReferenceObjects[0];
            Assert.IsNotNull(docRef, "Failed to create DocReferenceObject");
            Assert.AreEqual(result.DocClass, docRef.DocClass, "DocReferenceObject DocClass does not match");
            Assert.AreEqual(result.DocNo, docRef.DocNo, "DocReferenceObject DocNo does not match");
            Assert.AreEqual(result.DocSheet, docRef.DocSheet, "DocReferenceObject DocSheet does not match");
            Assert.AreEqual(result.DocRev, docRef.DocRev, "DocReferenceObject DocRev does not match");
            Assert.AreEqual("TstCustomer", docRef.LuName, "DocReferenceObject LuName does not match");
            Assert.AreEqual("CUSTOMER=100^", docRef.KeyRef, "DocReferenceObject KeyRef does not match");
            Assert.AreEqual("TestTitle", docRef.Title, "DocReferenceObject Title does not match");

            DocReferenceObject connectedDocRef = docReferenceObjects[1];
            Assert.IsNotNull(connectedDocRef, "Failed to create DocReferenceObject");
            Assert.AreEqual(result.DocClass, connectedDocRef.DocClass, "DocReferenceObject DocClass does not match");
            Assert.AreEqual(result.DocNo, connectedDocRef.DocNo, "DocReferenceObject DocNo does not match");
            Assert.AreEqual(result.DocSheet, connectedDocRef.DocSheet, "DocReferenceObject DocSheet does not match");
            Assert.AreEqual(result.DocRev, connectedDocRef.DocRev, "DocReferenceObject DocRev does not match");
            Assert.AreEqual("TstCustomer", connectedDocRef.LuName, "DocReferenceObject LuName does not match");
            Assert.AreEqual("CUSTOMER=101^", connectedDocRef.KeyRef, "DocReferenceObject KeyRef does not match");
            Assert.AreEqual("TestTitle", connectedDocRef.Title, "DocReferenceObject Title does not match");
        }

        [Test]
        public async Task Attachment_GetMediaFileExtensions()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            string result = await Call<string>("Attachment_GetMediaFileExtensions", parameters);

            Assert.AreEqual(".PNG,.JPG,.JPEG,.BMP,.GIF,.TIF", result);

            FwDataContext ctx = CreateDataContext();
            MobileClientParam param = ctx.AppParameters.FirstOrDefault(x => x.Parameter == "SERVER_CAPABILITIES");
            ctx.AppParameters.DeleteOnSubmit(param); // For some reason, updating didn't work so we're deleting and re-adding
            ctx.SubmitChanges(false);

            // Test again with the EnhancedMedia server capability
            param.Value += "EnhancedMedia^";
            ctx.AppParameters.InsertOnSubmit(param);
            ctx.SubmitChanges(false);

            result = await Call<string>("Attachment_GetMediaFileExtensions", parameters);

            Assert.AreEqual(".png,.jpg,.jpeg,.gif,.bmp,.tif,.mp3,.wma,.ogg,.mid,.aac,.wmv,.wav,.txt,.mov,.mp4,.flv,.vob,.avi,.mkv,.mng,.amv,.mpeg,.m4v,.3gp,.mxf,.mpv", result);
        }

        [Test]
        public async Task Attachment_GetDocFileExtensions()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            string result = await Call<string>("Attachment_GetDocFileExtensions", parameters);
            Assert.AreEqual(".PDF,.DWG,.JPG", result);
        }

        private async Task<T> Call<T>(string functionName, Dictionary<string, object> parameters = null)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, functionName, parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return (T)result.Value;
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>(
                "Execution.Procedures.Functions.Attachment.AttachmentConnectTestsSchema",
                "Execution.Procedures.Functions.Attachment.AttachmentConnectTestsData");
        }
    }
}
