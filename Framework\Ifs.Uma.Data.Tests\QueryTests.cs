﻿using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using IQToolkit.Data.Common;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;

namespace Ifs.Uma.Data.Tests
{
    [TestFixture]
    public class QueryTests : DataContextTest<TestDataContext>
    {
        [Test]
        public void FindByObjPrimaryKey()
        {
            TestDataContext dc = CreateDataContext();
            SyncedRow row = AddSyncedRow(dc, "test");
            ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(dc.Model, row);
            Assert.AreEqual(nameof(SyncedRow.MyId), key.Values.First().Item1.PropertyName);

            SyncedRow foundRow = dc.SyncedRows.Find(key);
            Assert.IsNotNull(foundRow);
            Assert.AreNotEqual(row, foundRow);
            Assert.AreNotEqual(0, foundRow.RowId);
            Assert.AreEqual(row.Name, foundRow.Name);
        }

        [Test]
        public void FindByObjPrimaryKey_System()
        {
            TestDataContext dc = CreateDataContext();
            TransitionRow row = AddTransitionRow(dc);
            ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(dc.Model, row);

            TransitionRow foundRow = dc.TransitionRows.Find(key);
            Assert.IsNotNull(foundRow);
            Assert.AreNotEqual(row, foundRow);
            Assert.AreNotEqual(0, foundRow.RowId);
            Assert.AreEqual(row.TableName, foundRow.TableName);
        }

        [Test]
        public void FindAllByObjPrimaryKey()
        {
            TestDataContext dc = CreateDataContext();

            SyncedRow rowA = AddSyncedRow(dc, "ra");
            ObjPrimaryKey keyA = ObjPrimaryKey.FromPrimaryKey(dc.Model, rowA);

            SyncedRow rowB = AddSyncedRow(dc, "rb");
            ObjPrimaryKey keyB = ObjPrimaryKey.FromPrimaryKey(dc.Model, rowB);

            SyncedRow[] foundRows = dc.SyncedRows.FindAll(new[] { keyA, keyB }).ToArray();
            Assert.AreEqual(2, foundRows.Length);

            SyncedRow foundRowA = foundRows.FirstOrDefault(x => x.MyId == "ra");
            Assert.IsNotNull(foundRowA);
            Assert.AreNotEqual(rowA, foundRowA);

            SyncedRow foundRowB = foundRows.FirstOrDefault(x => x.MyId == "rb");
            Assert.IsNotNull(foundRowB);
            Assert.AreNotEqual(rowB, foundRowB);
        }

        private SyncedRow AddSyncedRow(TestDataContext dc, string id)
        {
            SyncedRow row = new SyncedRow();
            dc.SyncedRows.InsertOnSubmit(row);
            row.MyId = id;
            row.Name = "Woo";
            dc.SubmitChanges(false);
            return row;
        }

        private TransitionRow AddTransitionRow(TestDataContext dc)
        {
            TransitionRow row = new TransitionRow();
            dc.TransitionRows.InsertOnSubmit(row);
            row.TableName = "test";
            row.Operation = OperationType.Insert;
            dc.SubmitChanges(false);
            return row;
        }
    }
}
