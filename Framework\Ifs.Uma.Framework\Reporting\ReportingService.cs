﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Entities;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Reporting.Generator;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Reporting.Render;
using Ifs.Uma.Reporting.Report;
using Ifs.Uma.Services.Attachments;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Newtonsoft.Json;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.Framework.Reporting
{
    public sealed class ReportingService : IReportingService
    {
        private readonly ReportGenerator _reportGenerator;
        private readonly ILocalStorage _localStorage;
        private readonly IFileService _fileService;
        private readonly IDialogService _dialogService;

        public ReportingService(IPageCreator pageCreator, ILocalStorage localStorage, IFileService fileService, IDialogService dialogService, ILogger logger)
        {
            _reportGenerator = new ReportGenerator(pageCreator, logger);
            _localStorage = localStorage;
            _fileService = fileService;
            _dialogService = dialogService;
        }

        public async Task ShowPageReportAsync(string projectionName, string pageName, PageValues filter)
        {
            try
            {
                ILocalFileInfo tempFile = null;
                ILocalFileInfo tempPdfFile = null;
                
                using (LoadingSession loadingSession = _dialogService.ShowLoadingDialog(null, true))
                {
                    CancellationToken cancelToken = loadingSession.CancellationToken;

                    string html = await GenerateHtmlFromPage(projectionName, pageName, filter, cancelToken);

                    ITouchApp touchApp = Resolver.Resolve<ITouchApp>();
                    TouchAppAccount account = touchApp?.Accounts.FirstOrDefault();

                    if (!string.IsNullOrEmpty(account?.ServerVersion) && PlatformServices.GetServerVersionAsDouble(account?.ServerVersion) > 23.2)
                    {
                        tempPdfFile = await SaveHtmlToPdf(projectionName, pageName, filter, html, false);
                    }
                    else
                    {
                        tempFile = await SaveHtmlToTempFile(projectionName, pageName, filter, html, false);
                    }
                }

                if (tempPdfFile != null)
                {
                    await _fileService.LaunchFileAsync(tempPdfFile);
                }
                else if (tempFile != null)
                {
                    await _fileService.LaunchFileAsync(tempFile);
                }
            }
            catch (OperationCanceledException)
            {
                // ignored
            }
        }

        public async Task<string> GenerateHtmlFromPage(string projectionName, string pageName, PageValues filter, CancellationToken cancelToken)
        {
            ReportDoc report = await _reportGenerator.GeneratePageReportAsync(projectionName, pageName, filter, cancelToken);
            IThemeService themeService = Resolver.Resolve<IThemeService>();
            string logoHeader = themeService.GetBrandingLogo(BrandElement.AurenaNativeCompanyLogo)?.ToString();

            if (!string.IsNullOrEmpty(logoHeader) && DeviceInfo.OperatingSystem != OperatingSystem.iOS)
            {
                string varStr = Convert.ToBase64String(File.ReadAllBytes(logoHeader));
                report.Logo = varStr;
            }
            else
            {
                report.IsSignature = true;
            }
            return await HtmlRenderer.RenderAsync(report, string.Empty, false, cancelToken);
        }

        public async Task<string> GenerateHtmlFromContent(string title, string logo, ElementList elements, bool allowSigning, CancellationToken cancelToken)
        {
            ReportDoc report = await _reportGenerator.AddReportItems(title, elements, cancelToken);
            report.IsSignature = allowSigning;
            if (!string.IsNullOrEmpty(logo) && logo.Contains(":") && logo.Contains("^"))
            {
                string[] mediaLibraryRef = logo.Split(':');
                MediaInfo media = null;

                if (Resolver.TryResolve(out IMediaHandler mediaHandler) && mediaLibraryRef.Length == 2)
                {
                    IEnumerable<MediaInfo> rows = await mediaHandler.GetMediaAsync(mediaLibraryRef[0], mediaLibraryRef[1]);

                    foreach (MediaInfo row in rows)
                    {
                        if ((bool)row.MediaItem.DefaultMedia)
                        {
                            media = row;
                            break;
                        }
                    }
                }

                if (media != null)
                {
                    ILocalFileInfo file = await mediaHandler.GetLocalFileForMediaAsync(media.MediaItem);
                    string varStr = Convert.ToBase64String(File.ReadAllBytes(file.FullFilePath));
                    report.Logo = varStr;
                }
            }
            else if (!string.IsNullOrEmpty(logo))
            {
                if (Resolver.TryResolve(out IIfsConnection connection) && !connection.TouchAppsComms.IsBroken)
                {
                    using (var response = await DownloadFile(connection, logo, string.Empty))
                    {
                        if (response != null)
                        {
                            byte[] imageBuffer = ReadStream(response.Stream);
                            string varStr = Convert.ToBase64String(imageBuffer);
                            report.Logo = varStr;
                        }
                    }
                }
            }
            else
            {
                IThemeService themeService = Resolver.Resolve<IThemeService>();
                string logoHeader = themeService.GetBrandingLogo(BrandElement.AurenaNativeCompanyLogo)?.ToString();

                if (!string.IsNullOrEmpty(logoHeader))
                {
                    string varStr = Convert.ToBase64String(File.ReadAllBytes(logoHeader));
                    report.Logo = varStr;
                }
            }
            
            return await HtmlRenderer.RenderAsync(report, logo, allowSigning, cancelToken);
        }

        public async Task<ReponseStream> DownloadFile(IIfsConnection connection, string resourceName, string queryParams)
        {
            try
            {
                CallResponseStream response = await connection.TouchAppsComms.GetStream(resourceName, queryParams);
                return new ReponseStream() { Stream = response.Stream };
            }
            catch (Exception ex)
            {
                Resolver.TryResolve(out ILogger logger);
                logger.Error($"Downloading theme logo failed: {ex.Message}");
                return null;
            }
        }

        public byte[] ReadStream(Stream input)
        {
            byte[] buffer = new byte[16 * 1024];
            using (MemoryStream ms = new MemoryStream())
            {
                int read;
                while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
                {
                    ms.Write(buffer, 0, read);
                }
                return ms.ToArray();
            }
        }

#if SIGNATURE_SERVICE
        public string GenerateJsonFromContent(string assistantName, ElementList elements, bool allowSigning, CancellationToken cancelToken)
        {
            object jsonData = _reportGenerator.AddJsonReportItems(assistantName, elements, cancelToken);
            return JsonConvert.SerializeObject(jsonData, Formatting.Indented);
        }
#endif

        private async Task<ILocalFileInfo> SaveHtmlToTempFile(string projectionName, string pageName, PageValues filter, string html, bool isSignature)
        {
            ILocalFileInfo tempFile = await GetTempReportFile(projectionName, pageName, filter, "html", isSignature);

            using (Stream stream = await tempFile.OpenStreamAsync(LocalFileMode.Create, LocalFileAccess.ReadWrite))
            using (TextWriter tw = new StreamWriter(stream, Encoding.UTF8))
            {
                await tw.WriteAsync(html);
            }

            return tempFile;
        }

        public async Task<ILocalFileInfo> SaveHtmlToPdf(string projectionName, string pageName, PageValues filter, string html, bool isSignature)
        {
            ILocalFileInfo tempFile = await GetTempReportFile(projectionName, pageName, filter, "pdf", isSignature);

            Resolver.TryResolve(out IPdfGenerator pdfGenerator);

            using (MemoryStream ms = new MemoryStream(pdfGenerator.ConvertToPdfAsync(html, tempFile.FullFilePath)))
            {
                await tempFile.WriteStreamAsync(ms);
            }

            return tempFile;
        }

        private Task<ILocalFileInfo> GetTempReportFile(string projectionName, string pageName, PageValues filter, string fileExt, bool isSignature)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(DateTime.Now.ToString("yyyy-MM-dd HH-mm-ss")); // Custom date time format used for report file name
            sb.Append(' ');
            sb.Append(projectionName + "." + pageName);

            if (filter != null)
            {
                foreach (var item in filter.ToDictionary())
                {
                    if (item.Value != null)
                    {
                        sb.Append(' ');
                        sb.Append(item.Value);
                    }
                }
            }

            return isSignature ? _localStorage.GetTemporaryFile(pageName, fileExt) : _localStorage.GetTemporaryFile(sb.ToString(), fileExt);
        }
    }
}
