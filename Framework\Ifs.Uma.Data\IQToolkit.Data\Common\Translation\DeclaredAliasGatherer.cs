﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace IQToolkit.Data.Common
{
    /// <summary>
    ///  returns the set of all aliases produced by a query source
    /// </summary>
    internal class DeclaredAliasGatherer : DbExpressionVisitor
    {
        HashSet<TableAlias> aliases;

        private DeclaredAliasGatherer()
        {
            this.aliases = new HashSet<TableAlias>();
        }

        public static HashSet<TableAlias> Gather(Expression source)
        {
            var gatherer = new DeclaredAliasGatherer();
            gatherer.Visit(source);
            return gatherer.aliases;
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            if (node == null) return null;
            this.aliases.Add(node.Alias);
            return node;
        }

        protected override Expression VisitTable(TableExpression node)
        {
            if (node == null) return null;
            this.aliases.Add(node.Alias);
            return node;
        }
    }
}
