﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.Localization;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Images;

namespace Ifs.Uma.Framework.UI.Trees.Nodes
{
    public class MaxDepthNode : Node
    {
        public MaxDepthNode(Node parent, int? count)
            : base(parent, null, null, null)
        {
            if (count == null)
            {
                Title = Strings.More;
            }
            else
            {
                Title = string.Format(Strings.CountMore, count.Value);
            }
        }

        public override string Title { get; }

        public override UmaImage Icon => IconUtils.New;

        public override bool IsPromotableToRoot => false;

        public override bool IsSecondActionVisible => false;

        public async Task LoadMore()
        {
            await Parent.OnlineOnlyLoadChildren();
        }

        public Node ActualParent
        {
            get
            {
                return Parent;
            }
        }

        protected override Task<IEnumerable<Node>> GetChildNodes()
        {
            return Task.FromResult<IEnumerable<Node>>(new List<Node>());
        }
    }
}
