﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Cloud.Client;
using Ifs.Cloud.Client.Comm;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Exceptions;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Cloud.Client.Types;
using Ifs.Cloud.Client.Utils;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Comm.TouchApps
{
    public interface ITouchAppsAccount
    {
        string ServiceUrl { get; }
        string SystemId { get; }
        string UserName { get; }
        string UserDisplayName { get; set; }
        IdentityProvider IdentityProvider { get; set; }
        int? DeviceId { get; set; }
        bool PinAuthentication { get; set; }
        string ScopeId { get; set; }
        string ServerVersion { get; set; }
        string BrandingCode { get; set; }
    }

    public class TouchAppsComms : Service
    {
        // PIN related app param names
        private const string PinAuthenticationKey = "PIN_AUTHENTICATION";
        private const string PinCodeMinLengthKey = "PIN_CODE_MIN_LENGTH";
        private const string PinIsComplexKey = "PIN_CODE_IS_COMPLEX";
        private const string Invalid = "INVALID";

        private const int PinMinimumLength = 6; // PIN must be at least 6 characters in 22R1+
        private const int PinMinimumLength21Rn = 4; // PIN must be at least 4 characters in 21R1/21R2

        private readonly ILogger _logger;
        private readonly ClientInfo _clientInfo;
        private ITouchAppsAccount _account;
        private string _password;
        private SemaphoreSlim _authLock = new SemaphoreSlim(1);
        private ContextProvider _ctx;

        public event EventHandler IsBrokenChanged;
        public event EventHandler TokensRefreshed;

        public BrokenCause BrokenCause { get; private set; }

        public string UserName => _account.UserName;

        public bool IsServerTimeZoneAware => PlatformServices.GetServerVersionAsDouble(ServerVersion) >= 24.1;

        public bool IsSiteTimezoneEnabled => PlatformServices.IsSiteTimezoneEnabled;

        public bool DevMode { get; set; }
        public ClientInfo ClientInfo { get { return _clientInfo; } }

        private bool _isBroken;
        public bool IsBroken
        {
            get { return _isBroken; }
            set
            {
                if (_isBroken != value)
                {
                    _isBroken = value;

                    string logMessage = $"Broken is {_isBroken}";
                    if (!_isBroken)
                    {
                        BrokenCause = null;
                    }
                    else if (BrokenCause != null)
                    {
                        logMessage += $"- {BrokenCause.Reason}";
                    }

                    Logger?.Warning(logMessage);

                    EventHandler handler = IsBrokenChanged;
                    if (handler != null)
                    {
                        Task.Run(() => handler(this, EventArgs.Empty));
                    }
                }
            }
        }

        public bool IsRefreshTokenSimulation { get; set; }

        public int DeviceId { get { return _account == null ? -1 : _account.DeviceId.GetValueOrDefault(-1); } }

        public string ScopeId { get { return _account == null ? string.Empty : _account.ScopeId; } }

        public string ServerVersion { get { return _account == null ? string.Empty : _account.ServerVersion; } }

        public TouchAppsComms(ILogger logger, ClientInfo clientInfo)
            : base(logger)
        {
            if (clientInfo == null)
                throw new ArgumentNullException(nameof(clientInfo));

            _logger = logger;
            _clientInfo = clientInfo;
        }

        public async Task<string> Activate(ITouchAppsAccount account, string password)
        {
            DestroyContext();
            BrokenCause = null;
            IsBroken = false;

            try
            {
                _ctx = ContextProvider.CreateContext(_logger, GetClientInfo(), account.ServiceUrl, account.SystemId, GetAuthenticator(), DevMode, account.DeviceId.ToString());
                _ctx.TokensRefreshed += OnTokensRefreshed;
                await _ctx.AuthenticateSession(account.UserName, password, account.IdentityProvider);

                ActivateResource activateResource = new ActivateResource();
                PrepareActivateResource(activateResource);

                CloudResourceProxy<ActivateResource> proxy = new CloudResourceProxy<ActivateResource>(_ctx, activateResource);
                activateResource = (await proxy.ExecutePostAsync(0, 1)).FirstOrDefault();

                GetServerVersionResource serverVersionResource = await GetServerVersion();

                if (activateResource != null)
                {
                    account.DeviceId = activateResource.DeviceId;
                    account.PinAuthentication = activateResource.PinAuthentication;
                    account.UserDisplayName = activateResource.UserName;
                    account.ScopeId = activateResource.ScopeId;
                    account.BrandingCode = activateResource.BrandingCode;
                    if (serverVersionResource != null)
                    {
                        account.ServerVersion = serverVersionResource.ImplementationVersion;
                    }
                }

                string userName = activateResource?.UserId;
                _ctx.DeviceId = account.DeviceId.ToString();

                string clientParamString = activateResource?.ClientParams;

                if (clientParamString != null)
                {
                    Dictionary<string, string>[] jsonPairs = JsonConvert.DeserializeObject<Dictionary<string, string>[]>(clientParamString);
                    foreach (Dictionary<string, string> json in jsonPairs)
                    {
                        string value = string.Empty;
                        if (json.TryGetValue(PinAuthenticationKey, out value) && value != string.Empty)
                        {
                            bool pinAuth = false;
                            bool.TryParse(value, out pinAuth);
                            account.PinAuthentication = pinAuth;
                        }

                        // if PIN minimum length is set, try to parse it. It can't be shorter than the mandatory minimum length
                        if (json.TryGetValue(PinCodeMinLengthKey, out value) && value != string.Empty)
                        {
                            int length = Convert.ToInt32(value);
                            OfflinePinCodeConfigurations.PinCodeMinLength = Math.Max(length, PinMinimumLength); // set PIN min length to param value or mandatory minimum, whatever is higher
                        }

                        if (json.TryGetValue(PinIsComplexKey, out value) && value != string.Empty)
                        {
                            bool pinAuthIsComplex = false;
                            bool.TryParse(value, out pinAuthIsComplex);
                            OfflinePinCodeConfigurations.PinCodeIsComplex = pinAuthIsComplex;
                        }
                    }
                }
                else
                {
                    // activateResource.ClientParams is null means we're likely running against a 21R1/21R2 environment, so set defaults for that
                    OfflinePinCodeConfigurations.PinCodeIsComplex = false; // 21R1/21R2 doesn't need complex PIN
                    OfflinePinCodeConfigurations.PinCodeMinLength = PinMinimumLength21Rn; // 21R1/21R2 PIN min length is 4
                }

                _account = account;
                _password = password;

                IsBroken = !_ctx.HasAuthenticated;
                return userName;
            }
            catch (Exception)
            {
                DestroyContext();
                throw;
            }
        }

        public async Task<List<ThemeData>> GetBrandingThemeData()
        {
            ThemeData themeData = new ThemeData(_account.BrandingCode);
            CloudResourceProxy<ThemeData> proxythemeProfiles = new CloudResourceProxy<ThemeData>(_ctx, themeData);
            return await proxythemeProfiles.ExecuteGetAsync(1, 60, CancellationToken.None);
        }

        public async Task<AddClientLogResourceBase> AddClientLog(ITouchAppsAccount account, string appName, string messageText, string type)
        {
            try
            {
                _ctx = ContextProvider.CreateContext(_logger, GetClientInfo(), account.ServiceUrl, account.SystemId, GetAuthenticator(), DevMode, account.DeviceId.ToString());
                _ctx.TokensRefreshed += OnTokensRefreshed;
                await _ctx.AuthenticateSession(account.UserName, account.IdentityProvider.AccessToken, account.IdentityProvider);

                MobileContext mobileContext = new MobileContext();
                mobileContext.AppName = appName;
                mobileContext.DeviceId = account.DeviceId.ToString();

                AddClientLogResourceBase addClientLog = new AddClientLogResourceBase(mobileContext, messageText, type);

                CloudResourceProxy<AddClientLogResourceBase> proxy = new CloudResourceProxy<AddClientLogResourceBase>(_ctx, addClientLog);
                return (await proxy.ExecutePostAsync(0, 1)).FirstOrDefault();
            }
            catch (Exception)
            {
                DestroyContext();
                throw;
            }
        }

        public async Task<AddOnDemandEntityResourceBase> AddOnDemandEntityResourceBaseAsync(string entity, string keyRef)
        {
            try
            {
                AddOnDemandEntityResourceBase result = null;
                await HandleAuthenticatedRequest(async () =>
                {
                    AddOnDemandEntityResourceBase onDemandEntityResourceBase = new AddOnDemandEntityResourceBase();
                    onDemandEntityResourceBase.AppName = ClientInfo.AppName;
                    onDemandEntityResourceBase.DeviceId = _account.DeviceId.ToString();
                    onDemandEntityResourceBase.Entity = entity;
                    onDemandEntityResourceBase.KeyRef = keyRef;
                    CloudResourceProxy<AddOnDemandEntityResourceBase> resourceProxy = new CloudResourceProxy<AddOnDemandEntityResourceBase>(_ctx, onDemandEntityResourceBase);
                    result = (await resourceProxy.ExecutePostAsync(0, 1)).FirstOrDefault();
                });

                return result;
            }
            catch (Exception)
            {
                DestroyContext();
                throw;
            }
        }

        public async Task<bool> TryLogin(ITouchAppsAccount account, IdentityProvider identityProvider)
        {
            ContextProvider ctx = ContextProvider.CreateContext(_logger, GetClientInfo(), account.ServiceUrl, account.SystemId, GetAuthenticator(), DevMode, account.DeviceId.ToString());
            string userName = await ctx.GetUserName(account.ServiceUrl, identityProvider.AccessToken);
            return !(account.UserName != null && !account.UserName.Equals(userName));
        }

        private void DestroyContext()
        {
            if (_ctx != null)
            {
                _ctx.TokensRefreshed -= OnTokensRefreshed;
                _ctx = null;
            }
        }

        public async Task<IdentityProvider> GetIdentityProviderInformation(string serviceUrl, string appName)
        {
            ContextProvider ctx = ContextProvider.CreateContext(_logger, GetClientInfo(), serviceUrl, appName, GetAuthenticator(), DevMode, null);
            return await ctx.GetIdentityProviderInformation(serviceUrl, appName);
        }

        public async Task<string> GetUserName(string serviceUrl, string systemId, string authToken)
        {
            ContextProvider ctx = ContextProvider.CreateContext(_logger, GetClientInfo(), serviceUrl, systemId, GetAuthenticator(), DevMode, null);
            return await ctx.GetUserName(serviceUrl, authToken);
        }

        public async Task LogoutIdpSessionAsync(ITouchAppsAccount account)
        {
            _ctx = ContextProvider.CreateContext(_logger, GetClientInfo(), account.ServiceUrl, account.SystemId, GetAuthenticator(), DevMode, account.DeviceId.ToString());

            if (_ctx != null)
            {
                await _ctx.LogoutIdpSessionAsync(account.IdentityProvider);
            }
        }

        public async Task<GetServerVersionResource> GetServerVersion()
        {
            GetServerVersionResource result = null;
            try
            {
                await HandleAuthenticatedRequest(async () =>
                {
                    GetServerVersionResource versionResource = new GetServerVersionResource();
                    CloudResourceProxy<GetServerVersionResource> proxy = new CloudResourceProxy<GetServerVersionResource>(_ctx, versionResource);
                    result = (await proxy.ExecuteGetAsync(0, 1, CancellationToken.None).ConfigureAwait(false)).FirstOrDefault();
                }, true).ConfigureAwait(false);

                return result;
            }
            catch (Exception e)
            {
                // This endpoint is only available from 22.1 environments (including 22.1)
                _logger.Warning(e.Message);
                return result;
            }
        }

        public async Task VerifyPassword(ITouchAppsAccount account, string password)
        {
            if (_authLock == null)
            {
                return;
            }

            await _authLock.WaitAsync();
            try
            {
                DestroyContext();

                _ctx = ContextProvider.CreateContext(_logger, GetClientInfo(), account.ServiceUrl, account.SystemId, GetAuthenticator(), DevMode, account.DeviceId.ToString());

                await _ctx.AuthenticateSession(account.UserName, password, account.IdentityProvider);
            }
            finally
            {
                DestroyContext();
                _authLock.Release();
            }
        }

        public void LoginOffline(ITouchAppsAccount account, string password)
        {
            if (!account.DeviceId.HasValue)
            {
                throw new InvalidOperationException("Touch app comms have not been activated");
            }

            DestroyContext();

            _account = account;
            _password = password;

            IsBroken = false;
        }

        public void Logoff()
        {
            //DestroyContext();

            _account = null;
            _password = null;

            IsBroken = false;
        }

        public bool IsAvailable()
        {
            return !IsBroken && IsInternetAvailable();
        }

        private async Task LoginIfRequired()
        {
            if (_authLock == null)
            {
                return;
            }

            if (!IsBroken && (_ctx == null || !_ctx.HasAuthenticated))
            {
                await _authLock.WaitAsync();

                try
                {
                    if (_ctx == null)
                    {
                        try
                        {
                            _ctx = ContextProvider.CreateContext(_logger, GetClientInfo(), _account.ServiceUrl, _account.SystemId, GetAuthenticator(), DevMode, _account.DeviceId.ToString());
                            _ctx.TokensRefreshed += OnTokensRefreshed;
                        }
                        catch (Exception)
                        {
                            DestroyContext();
                            throw;
                        }
                    }

                    if (_ctx != null && !_ctx.HasAuthenticated)
                    {
                        await _ctx.AuthenticateSession(_account.UserName, _password, _account.IdentityProvider);

                        IsBroken = !_ctx.HasAuthenticated;
                    }
                }
                finally
                {
                    _authLock.Release();
                }
            }
        }

        public async Task HandleAuthenticatedRequest(Func<Task> action, bool isInitializing = false)
        {
            try
            {
                await LoginIfRequired().ConfigureAwait(false);

                if (!IsBroken)
                {
                    await action().ConfigureAwait(false);
                }
            }
            catch (CloudException ex)
            {
                BrokenCause cause = GetBrokenCause(ex);
                if (cause != null && !(cause.Reason == BrokenReason.RequiresInitialization && isInitializing))
                {
                    BrokenCause = cause;
                    IsBroken = true;
                }

                throw;
            }

            if (IsBroken)
            {
                // Connection is already broken - we should throw an error
                throw BrokenCause.Exception.Clone();
            }
        }

        private BrokenCause GetBrokenCause(CloudException ex)
        {
            if (ex.ErrorType == CloudErrorType.InvalidAuthorizationHeader)
            {
                return new BrokenCause(BrokenReason.Other, ex);
            }
            else if (ex.ErrorType == CloudErrorType.PropagatedSystemError &&
                     ex.Message.Contains("MOBILE_DEVICE_APP_DISABLED"))
            {
                return new BrokenCause(BrokenReason.RequiresInitialization, ex);
            }
            else if (ex.ErrorType == CloudErrorType.PropagatedSystemError &&
                     ex.Message.Contains("DEVICE_APP_NOT_ACTIVATED"))
            {
                _account.DeviceId = null; //deactivate device
                return new BrokenCause(BrokenReason.RequiresActivation, ex);
            }
            else if (ex.ErrorType == CloudErrorType.PropagatedSystemError &&
                     (ex.Message.Contains("MOBILE_APP_NOT_ENABLED") ||
                      ex.Message.Contains("MOBILE_USER_DISABLED") ||
                      ex.Message.Contains("DEVICE_DISABLED"))) //This one can happen if the device has been disabled on the server, so
                                                               //just telling the user to reactivate might not be enough
            {
                _account.DeviceId = null; //deactivate device
                _account.PinAuthentication = false;
                return new BrokenCause(BrokenReason.Other, ex);
            }
            else if (ex.ErrorType == CloudErrorType.AppDevicePendingApproval ||
                     ex.ErrorType == CloudErrorType.AppDeviceRejected ||
                     ex.ErrorType == CloudErrorType.AppDeviceRegistrationRequestSent ||
                     ex.ErrorType == CloudErrorType.DatabaseError ||
                     ex.ErrorType == CloudErrorType.ODataProviderError ||
                     ex.ErrorType == CloudErrorType.RequestError)
            {
                return new BrokenCause(BrokenReason.Other, ex);
            }
            else if (ex.ErrorType == CloudErrorType.TokenRefreshRequired)
            {
                return new BrokenCause(BrokenReason.InvalidCredentials, ex);
            }

            return null;
        }

        public void FixConnection()
        {
            IsBroken = false;
        }

        private void OnTokensRefreshed(object sender, EventArgs e)
        {
            TokensRefreshed?.Invoke(this, new RefreshTokenEventArgs());
        }

        #region Platform Implementation Requirements

        protected virtual ICloudAuthenticator GetAuthenticator()
        {
            return TouchAppsAuthenticator.Instance.Authenticator;
        }

        protected virtual ClientInfo GetClientInfo()
        {
            return _clientInfo;
        }

        protected virtual void PrepareActivateResource(ActivateResource resource)
        {
            resource.ClientInfo = _clientInfo;
            resource.MobileContext = new MobileContext();
            resource.MobileContext.AppName = _clientInfo.AppName;
            resource.ClientInfo.DeviceIdentifier = ApplicationInfo.ApplicationId;
            resource.ClientInfo.Brand = DeviceInfo.Manufacturer;
            resource.ClientInfo.Model = DeviceInfo.Model;
            resource.ClientInfo.ClientVersion = ApplicationInfo.FrameworkVersion.ToString();
            resource.ClientInfo.Carrier = string.Empty;
            resource.ClientInfo.ClientDb = "SQLite";
            resource.ClientInfo.ClientRuntime = DeviceInfo.OperatingSystem.ToString();
            resource.ClientInfo.Os = DeviceInfo.OperatingSystem.ToString();
            resource.ClientInfo.OsVersion = DeviceInfo.OperatingSystemVersion;
            resource.ClientInfo.Platform = DeviceInfo.OperatingSystem.ToString();
        }

        protected virtual bool IsInternetAvailable()
        {
            return PlatformServices.IsNetworkAvailable();
        }

        #endregion

        public async Task<CallResponseStream> GetStream(string resourceName, string queryParams)
        {
            CallResponseStream fileStream = null;

            async Task Execute()
            {
                if (queryParams != null)
                {
                    queryParams = "(" + queryParams + ")";
                }

                StreamResource streamResource = new StreamResource(resourceName);
                streamResource.ResourceQuery = queryParams;
                CloudResourceProxy<StreamResource> proxy = new CloudResourceProxy<StreamResource>(_ctx, streamResource);
                fileStream = await proxy.ExecuteGetStream(queryParams).ConfigureAwait(false);
            }

            await HandleAuthenticatedRequest(Execute).ConfigureAwait(false);

            return fileStream;
        }

        public async Task PatchStream(string resourceName, string queryParams, string columnName, Stream stream, DataFormat dataFormat, string eTag, long messageId, bool offline = true, string fileName = null)
        {
            async Task Execute()
            {
                if (queryParams != null)
                {
                    queryParams = "(" + queryParams + ")/" + columnName;
                }

                StreamResource streamResource = new StreamResource(resourceName);
                streamResource.ResourceQuery = queryParams;
                streamResource.ResourceStream = stream;
                CloudResourceProxy<StreamResource> proxy = new CloudResourceProxy<StreamResource>(_ctx, streamResource);
                await proxy.ExecutePatchStream(queryParams, eTag, messageId, stream, dataFormat, offline, fileName).ConfigureAwait(false);
            }

            await HandleAuthenticatedRequest(Execute).ConfigureAwait(false);
        }

        public async Task<List<PushRegistration>> RegisterForPushNotifications(PushRegistration reg)
        {
            List<PushRegistration> result = null;

            await HandleAuthenticatedRequest(async () =>
            {
                reg.MobileContext = new MobileContext();
                reg.MobileContext.DeviceId = DeviceId.ToString();
                reg.MobileContext.AppName = _clientInfo.AppName;
                reg.ClientOS = _clientInfo.Os;
                CloudResourceProxy<PushRegistration> proxy = new CloudResourceProxy<PushRegistration>(_ctx, reg);
                result = await proxy.ExecutePostAsync(0, 0);
            });

            return result;
        }

        public async Task<MetadataResource> GetMetadataAsync()
        {
            MetadataResource result = null;

            await HandleAuthenticatedRequest(async () =>
                {
                    MetadataResource mdr = new MetadataResource();
                    mdr.AppName = _clientInfo.AppName;
                    mdr.DeviceId = DeviceId.ToString();
                    mdr.ScopeId = _account.ScopeId;
                    CustomSerializedResourceProxy<MetadataResource> proxy = new CustomSerializedResourceProxy<MetadataResource>(_ctx, mdr);
                    // CustomSerializedResourceProxy<MetadataResource> proxy = new CustomSerializedResourceProxy<MetadataResource>(_ctx, new MetadataResource() { ScopeId = ScopeId });
                    result = (MetadataResource)await proxy.ExecutePostAsync(0, 0, CancellationToken.None, string.Empty).ConfigureAwait(false);
                }, true)
                .ConfigureAwait(false);

            return result;
        }

        public async Task<LoadNavigatorResource> GetNavigatorEntriesAsync()
        {
            LoadNavigatorResource result = null;
            await HandleAuthenticatedRequest(async () =>
            {
                LoadNavigatorResource lnr = new LoadNavigatorResource();
                lnr.AppName = _clientInfo.AppName;
                lnr.ScopeId = _account.ScopeId;
                CloudResourceProxy<LoadNavigatorResource> proxy = new CloudResourceProxy<LoadNavigatorResource>(_ctx, lnr);
                result = (await proxy.ExecuteGetAsync(0, 1, CancellationToken.None).ConfigureAwait(false)).FirstOrDefault();
            }, true).ConfigureAwait(false);
            return result;
        }

        public async Task<ClientMetadataResource> GetClientMetadataAsync(string name)
        {
            ClientMetadataResource result = null;

            await HandleAuthenticatedRequest(async () =>
            {
                ClientMetadataResource mdr = new ClientMetadataResource();
                mdr.ClientName = name;
                mdr.ScopeId = _account.ScopeId;
                CustomSerializedResourceProxy<ClientMetadataResource> proxy = new CustomSerializedResourceProxy<ClientMetadataResource>(_ctx, mdr);
                result = (ClientMetadataResource)await proxy.ExecutePostAsync(0, 0, CancellationToken.None, string.Empty).ConfigureAwait(false);
            }, true).ConfigureAwait(false);

            return result;
        }

        public async Task<InitializationResource> SynchronizeNow(InitializationResource resource)
        {
            InitializationResource result = new InitializationResource();
            bool response = true;

            await HandleAuthenticatedRequest(async () =>
            {
                CustomSerializedResourceProxy<InitializationResource> proxy = new CustomSerializedResourceProxy<InitializationResource>(_ctx, resource);
                response = (bool)await proxy.ExecutePostAsync(0, 0, CancellationToken.None, string.Empty).ConfigureAwait(false);
            }, true)
                .ConfigureAwait(false);

            result.SyncNowValue = response;
            return result;
        }

        public async Task<IEnumerable<MessageResource>> GetMessagesAsync(MessageResource resource, int pageSize, bool isInitializing)
        {
            IEnumerable<MessageResource> result = null;

            await HandleAuthenticatedRequest(async () =>
            {
                CloudResourceProxy<MessageResource> proxy = new CloudResourceProxy<MessageResource>(_ctx, resource);
                result = await proxy.ExecutePostAsync(0, 0, CancellationToken.None).ConfigureAwait(false);
            }, isInitializing).ConfigureAwait(false);

            return result;
        }

        public async Task<IEnumerable<T>> DownloadResourceAsync<T>(T resource, int page, int pageSize, CancellationToken cancellationToken)
            where T : Ifs.Cloud.Client.Entities.BaseResource
        {
            IEnumerable<T> result = null;

            await HandleAuthenticatedRequest(async () =>
            {
                CloudResourceProxy<T> proxy = new CloudResourceProxy<T>(_ctx, resource);
                result = await proxy.ExecutePostAsync(page, 0, cancellationToken).ConfigureAwait(false);
            }).ConfigureAwait(false);

            return result;
        }

        public async Task<object> DownloadCustomResourceAsync<T>(T resource, int page, int pageSize, CancellationToken cancellationToken)
            where T : Ifs.Cloud.Client.Entities.BaseResource, ICustomResourceSerializer
        {
            object result = null;

            await HandleAuthenticatedRequest(async () =>
            {
                CustomSerializedResourceProxy<T> proxy = new CustomSerializedResourceProxy<T>(_ctx, resource);
                result = await proxy.ExecuteGetAsync(page, 0, cancellationToken).ConfigureAwait(false);
            }).ConfigureAwait(false);

            return result;
        }

        public async Task<object> PostCustomResourceAsync<T>(T resource, int page, long messageId, CancellationToken cancelToken, string eTag = "")
            where T : Ifs.Cloud.Client.Entities.BaseResource, ICustomResourceSerializer
        {
            object result = null;

            await HandleAuthenticatedRequest(async () =>
            {
                CustomSerializedResourceProxy<T> proxy = new CustomSerializedResourceProxy<T>(_ctx, resource);
                result = await proxy.ExecutePostAsync(page, messageId, cancelToken, eTag).ConfigureAwait(false);
            }).ConfigureAwait(false);

            return result;
        }

        internal async Task<ExecuteResult> StreamLobActionCustomResourceAsync(ActionResource actionResource, Dictionary<string, object> lobActionParamTypes, int page, long messageId, CancellationToken cancelToken, string eTag = "")
        {
            //TERT-178 Lob Handling : This is the common method which is used for both online and offline scenarios 

            //lobActionParamTypes == null during SyncPolicy Online, therefore need to create it
            if (lobActionParamTypes == null)
            {
                lobActionParamTypes = new Dictionary<string, object>();
                CpiAction cpiLobAction = actionResource.Metadata.FindAction(actionResource.Projection, actionResource.MethodName);

                foreach (CpiParam kvp in cpiLobAction.Parameters)
                {
                    lobActionParamTypes[kvp.Name] = kvp.DataType;
                }
            }

            //TempLobStoreInfo resource used to send the CreatedByModule: ProjectionName to FndTempLobStore
            TempLobStoreInfo tempLobStoreResource = new TempLobStoreInfo();
            tempLobStoreResource.ProjectionName = actionResource.Projection;

            //Sending the 1st POST call which returns the 'LobId' in the response
            ExecuteResult tempLobsResult = (ExecuteResult)await PostCustomResourceAsync(tempLobStoreResource, 0, 0, cancelToken);

            if (tempLobsResult.Failed)
            {
                //Next calls cannot be executed if this fails
                return tempLobsResult;
            }

            //Preparing for 2nd PatchStream call

            //Filtering the 1st calls response to get the resourceName and eTagFromTempLob
            TempLobStoreInfo tempLobStoreResult = (TempLobStoreInfo)tempLobsResult.Value;
            tempLobStoreResult.ProjectionName = actionResource.Projection;
            string resourceName = tempLobStoreResult.ResourceName + "(LobId='" + tempLobStoreResult.LobId + "')";
            string eTagFromTempLob = tempLobStoreResult.ETagString;

            //Need to get LobActionData from ActionResourceData to create the string stream
            Dictionary<string, object> lobActionData = GetLobActionDataFromActionResource(actionResource.Data);

            //Right now this only supports single LongText/single Binary/LongText and Binary scenarios only.Multiple parameter support can be done later on the need.
            //Checking whether action contains Binary or LongText parameters
            bool hasBlob = (lobActionParamTypes.Where(x => x.Value.ToString() == CpiDataType.Binary.ToString())).Any();
            bool hasClob = (lobActionParamTypes.Where(x => x.Value.ToString() == CpiDataType.LongText.ToString())).Any();

            //Sending 2nd PatchStream call based on Lob action parameter types
            if (hasBlob && hasClob)
            {
                //When there are both LongText and Binary parameters in the action
                //In this scenario there are two PatchStream calls against the same LobId. One for BlobData and another one for ClobData.
                resourceName = resourceName + "/BlobData";
                byte[] blobBytesForData = GetMetaDataLobBytesForActionData(lobActionData, lobActionParamTypes);

                if (blobBytesForData != null)
                {
                    using (Stream stream = new MemoryStream(blobBytesForData))
                    {
                        await PatchStream(resourceName, null, null, stream, DataFormat.Binary, eTagFromTempLob, 0, false);
                    }
                }

                resourceName = resourceName + "/ClobData";
                string clobStringForData = GetMetaDataLobStringForActionData(lobActionData, lobActionParamTypes);

                if (!string.IsNullOrEmpty(clobStringForData))
                {
                    using (Stream stream = GenerateStreamFromString(clobStringForData))
                    {
                        await PatchStream(resourceName, null, null, stream, DataFormat.LongText, eTagFromTempLob, 0, false);
                    }
                }
            }
            else if (hasBlob)
            {
                //When there is only one Binary parameter in the action
                //In this scenario there is only one PatchStream call against the LobId. Only for BlobData.
                resourceName = resourceName + "/BlobData";
                byte[] blobBytesForData = GetMetaDataLobBytesForActionData(lobActionData, lobActionParamTypes);

                if (blobBytesForData != null)
                {
                    using (Stream stream = new MemoryStream(blobBytesForData))
                    {
                        await PatchStream(resourceName, null, null, stream, DataFormat.Binary, eTagFromTempLob, 0, false);
                    }
                }
            }
            else
            {
                //When there is only one LongText parameter in the action
                //In this scenario there is only one PatchStream call against the LobId. Only for ClobData.
                resourceName = resourceName + "/ClobData";
                string clobStringForData = GetMetaDataLobStringForActionData(lobActionData, lobActionParamTypes);

                if (!string.IsNullOrEmpty(clobStringForData))
                {
                    using (Stream stream = GenerateStreamFromString(clobStringForData))
                    {
                        await PatchStream(resourceName, null, null, stream, DataFormat.LongText, eTagFromTempLob, 0, false);
                    }
                }
            }

            //Preparing for 3rd POST call 

            //Modifying actionResource JSON data with the swapped values.
            actionResource.Data = MessageUtils.LobParametersToJObject(lobActionData, lobActionParamTypes, tempLobStoreResult.LobId);

            //Sending the 3rd POST call which returns no values in the response
            ExecuteResult executeResult = (ExecuteResult)await PostCustomResourceAsync(actionResource, 0, messageId, cancelToken);

            return executeResult;
        }

        private byte[] GetMetaDataLobBytesForActionData(Dictionary<string, object> lobActionData, Dictionary<string, object> lobActionParamTypes)
        {
            byte[] lobBytes = null;

            foreach (KeyValuePair<string, object> param in lobActionData)
            {
                KeyValuePair<string, object> paramType = lobActionParamTypes.Where(x => x.Key == param.Key && x.Value.ToString() == CpiDataType.Binary.ToString()).FirstOrDefault();

                if (paramType.Key != null)
                {
                    JToken token = param.Value as JToken;
                    lobBytes = token.ToObject<byte[]>();
                    break;
                }
            }

            return lobBytes;
        }

        private string GetMetaDataLobStringForActionData(Dictionary<string, object> lobActionData, Dictionary<string, object> lobActionParamTypes)
        {
            //Creating lobString for LOB Data in the Action
            string lobDataString = null;

            foreach (KeyValuePair<string, object> param in lobActionData)
            {
                KeyValuePair<string, object> paramType = lobActionParamTypes.Where(x => x.Key == param.Key && x.Value.ToString() == CpiDataType.LongText.ToString()).FirstOrDefault();

                if (paramType.Key != null)
                {
                    JToken token = param.Value as JToken;
                    lobDataString = token.ToObject<string>();
                    break;
                }
            }

            return lobDataString;
        }

        private Dictionary<string, object> GetLobActionDataFromActionResource(JObject actionResourcedata)
        {
            //Returns lobActionData Dictionary for JObject
            Dictionary<string, object> lobActionData = new Dictionary<string, object>();

            if (actionResourcedata != null)
            {
                foreach (KeyValuePair<string, JToken> kvp in actionResourcedata)
                {
                    lobActionData[kvp.Key] = kvp.Value;
                }
            }

            return lobActionData;
        }

        private Stream GenerateStreamFromString(string s)
        {
            MemoryStream stream = new MemoryStream();
            StreamWriter writer = new StreamWriter(stream);
            writer.Write(s);
            writer.Flush();
            stream.Position = 0;
            return stream;
        }

        internal async Task PrepareEntityLobStreamAsync(MessageInResource resource, MessageOut messageOutItem)
        {
            string resourceName = resource.ResourceName;
            string queryParamsString = Formatter.ToParamString((Dictionary<string, object>)messageOutItem.Data.RowData.Keys);

            string eTag = resource.ETagString;
            long messageId = resource.MessageId; // Original resource.MessageId will be used to update

            foreach (KeyValuePair<string, object> lobAttribute in messageOutItem.LobAttributeTypes)
            {
                if (lobAttribute.Value.ToString() == CpiDataType.Binary.ToString())
                {
                    byte[] binary = (byte[])messageOutItem.Data.RowData.ColumnData[lobAttribute.Key] ?? new byte[0];

                    using (Stream stream = new MemoryStream(binary))
                    {
                        if (messageOutItem.MessageType == AppData.Messages.MessageType.UPDATE)
                        {
                            resourceName = resourceName.Substring(0, resourceName.LastIndexOf("("));
                            resourceName = resourceName + "(" + queryParamsString + ")/" + lobAttribute.Key;
                            await PatchStream(resourceName, null, null, stream, DataFormat.Binary, eTag, messageId);
                        }
                        else
                        {
                            await PatchStream(resourceName, queryParamsString, lobAttribute.Key, stream, DataFormat.Binary, eTag, messageId);
                        }
                    }
                }
                else if (lobAttribute.Value.ToString() == CpiDataType.LongText.ToString())
                {
                    string longText = (string)messageOutItem.Data.RowData.ColumnData[lobAttribute.Key] ?? string.Empty;

                    using (Stream stream = GenerateStreamFromString(longText))
                    {
                        if (messageOutItem.MessageType == AppData.Messages.MessageType.UPDATE)
                        {
                            resourceName = resourceName.Substring(0, resourceName.LastIndexOf("("));
                            resourceName = resourceName + "(" + queryParamsString + ")/" + lobAttribute.Key;
                            await PatchStream(resourceName, null, null, stream, DataFormat.LongText, eTag, messageId);
                        }
                        else
                        {
                            await PatchStream(resourceName, queryParamsString, lobAttribute.Key, stream, DataFormat.LongText, eTag, messageId);
                        }
                    }
                }
            }
        }

        public async Task<object> PatchCustomResourceAsync<T>(T resource, int page, long messageId, CancellationToken cancelToken, string eTag)
            where T : Ifs.Cloud.Client.Entities.BaseResource, ICustomResourceSerializer
        {
            object result = null;

            await HandleAuthenticatedRequest(async () =>
            {
                CustomSerializedResourceProxy<T> proxy = new CustomSerializedResourceProxy<T>(_ctx, resource);
                result = await proxy.ExecutePatchAsync(page, messageId, cancelToken, eTag).ConfigureAwait(false);
            }).ConfigureAwait(false);

            return result;
        }

        public async Task DeleteCustomResourceAsync<T>(T resource, long messageId, string eTag)
           where T : Ifs.Cloud.Client.Entities.BaseResource, ICustomResourceSerializer
        {
            await HandleAuthenticatedRequest(async () =>
            {
                CustomSerializedResourceProxy<T> proxy = new CustomSerializedResourceProxy<T>(_ctx, resource);
                await proxy.DeleteAsync(messageId, eTag).ConfigureAwait(false);
            }).ConfigureAwait(false);
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            if (disposing)
            {
                IsBrokenChanged = null;
                Logoff();

                if (_authLock != null)
                {
                    _authLock.Dispose();
                    _authLock = null;
                }
            }
        }

        public void SimulateRefreshTokenExpiration()
        {
            _account.IdentityProvider.AccessToken = Invalid;
            IsBroken = true;
            IsRefreshTokenSimulation = true;
            _ctx.InvalidateSession();
        }
    }
}
