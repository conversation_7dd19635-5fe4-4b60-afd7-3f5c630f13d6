﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Date
{
    internal sealed class DateTimeToOracleFormat : DateTimeFunction
    {
        public const string FunctionName = "ToOracleFormat";

        public DateTimeToOracleFormat()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime? input = parameters[0].GetTimestamp();
            string format = parameters[1].GetString();

            // Convert Oracle format to .NET format
            format = format.Replace("YY", "yy");
            format = format.Replace("MONTH", "MMMM");
            format = format.Replace("MON", "MMM");
            format = format.Replace("DY", "ddd");
            format = format.Replace("DD", "dd");
            format = format.Replace("HH24", "H24"); // Special case to avoid HH being replaced later
            format = format.Replace("HH12", "hh");
            format = format.Replace("HH", "hh");
            format = format.Replace("H24", "HH"); // Handle the special case for HH24 which might get replaced as hh
            format = format.Replace("MI", "mm");
            format = format.Replace("SS", "ss");

            if (input.HasValue && !string.IsNullOrEmpty(format))
            {
                return input.Value.ToString(format);
            }

            return null;
        }
    }
}
