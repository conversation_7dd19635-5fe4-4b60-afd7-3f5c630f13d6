﻿<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:orientation="vertical"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:background="@color/IfsWhite">

  <com.google.android.material.floatingactionbutton.FloatingActionButton
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/close_button"
    android:layout_gravity="right"
    android:layout_marginTop="20dp"
    android:layout_marginRight="18dp"
    android:background="@null"
    android:outlineProvider="none"
    android:backgroundTint="@android:color/transparent"
    android:foregroundTint="@android:color/black"
    app:borderWidth="0dp"/>

  <TextView
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/calendar_view_header"
    android:layout_marginTop="12dp"
    android:layout_marginBottom="18dp"
    android:layout_marginLeft="10dp"
    android:textStyle="bold"
    android:textSize="16dp"/>

  <GridLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:columnCount="3">

    <Button
      android:layout_width="0dp"
      android:layout_height="38dp"
      android:id="@+id/agenda_button"
      android:layout_marginLeft="6dp"
      android:layout_marginRight="6dp"
      android:layout_marginBottom="10dp"
      android:textSize="14dp"
      android:layout_columnWeight="1"/>

    <Button
      android:layout_width="0dp"
      android:layout_height="38dp"
      android:id="@+id/day_button"
      android:layout_marginLeft="6dp"
      android:layout_marginRight="6dp"
      android:layout_marginBottom="10dp"
      android:textSize="14dp"
      android:layout_columnWeight="1"/>

    <Button
      android:layout_width="0dp"
      android:layout_height="38dp"
      android:id="@+id/week_button"
      android:layout_marginLeft="6dp"
      android:layout_marginRight="6dp"
      android:layout_marginBottom="10dp"
      android:textSize="14dp"
      android:layout_columnWeight="1"/>

    <Button
      android:layout_width="0dp"
      android:layout_height="38dp"
      android:id="@+id/work_week_button"
      android:layout_marginLeft="6dp"
      android:layout_marginRight="6dp"
      android:layout_marginBottom="10dp"
      android:textSize="14dp"
      android:layout_columnWeight="1"/>

    <Button
      android:layout_width="0dp"
      android:layout_height="38dp"
      android:id="@+id/month_button"
      android:layout_marginLeft="6dp"
      android:layout_marginRight="6dp"
      android:layout_marginBottom="10dp"
      android:textSize="14dp"
      android:layout_columnWeight="1"/>

  </GridLayout>
  <TextView
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/event_view_header"
    android:layout_marginTop="25dp"
    android:layout_marginBottom="18dp"
    android:layout_marginLeft="10dp"
    android:textStyle="bold"
    android:textSize="16dp"/>
  <GridView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/event_grid_view"
    android:numColumns="3"
    android:verticalSpacing="10dp"
    android:horizontalSpacing="12dp"
    android:layout_marginLeft="6dp"
    android:layout_marginRight="6dp"
    android:gravity="center"/>
</LinearLayout>
