﻿using System.Text;
using Ifs.Uma.Database;

namespace Ifs.Uma.Data
{
    public class RemoteRow : RowBase
    {
        public string TableName { get; }

        public string EntitySetName { get; set; }

        private string _arraySourceName;
        [Column(Storage = nameof(_arraySourceName), MaxLength = 200)]
        public string ArraySourceName
        {
            get { return _arraySourceName; }
            set { SetProperty(ref _arraySourceName, value); }
        }

        private string _objId;
        [Column(Storage = nameof(_objId), MaxLength = 50)]
        public string ObjId
        {
            get { return _objId; }
            set { SetProperty(ref _objId, value); }
        }

        private string _objVersion;
        [Column(Storage = nameof(_objVersion), MaxLength = 200)]
        public string ObjVersion
        {
            get { return _objVersion; }
            set { SetProperty(ref _objVersion, value); }
        }

        private string _objKey;
        [Column(Storage = nameof(_objKey), MaxLength = 200)]
        public string ObjKey
        {
            get { return _objKey; }
            set { SetProperty(ref _objKey, value); }
        }

        private string _etag;
        [Column(Storage = nameof(_etag), MaxLength = 200)]
        public string ETag
        {
            get { return _etag; }
            set { SetProperty(ref _etag, value); }
        }

        private string _primaryKeyString;
        [Column(Storage = nameof(_primaryKeyString), MaxLength = 200)]
        public string PrimaryKeyString
        {
            get { return _primaryKeyString; }
            set { SetProperty(ref _primaryKeyString, value); }
        }

        private string _sessionId;
        [Column(Storage = nameof(_primaryKeyString), MaxLength = 200)]
        public string SessionId
        {
            get { return _sessionId; }
            set { SetProperty(ref _sessionId, value); }
        }

        public RemoteRow(string tableName)
        {
            TableName = tableName;
        }

        public string GetRowIdentifier(IMetaModel metaModel)
        {
            if (!string.IsNullOrWhiteSpace(ObjKey))
            {
                return $"#{ObjKey}";
            }
            else
            {
                return ObjPrimaryKey.FromPrimaryKey(metaModel, this)?.ToKeySeparatedValues();
            }
        }

        public string GetETagString()
        {
            if (string.IsNullOrEmpty(_objId) || string.IsNullOrEmpty(_objVersion))
            {
                return string.Empty;
            }

            string eTag = "W/\"" + _objId + ":" + _objVersion + "\"";
            byte[] buffer = Encoding.UTF8.GetBytes(eTag);
            string encoded = System.Convert.ToBase64String(buffer);
            return "W/\"" + encoded + "\"";
        }

        public string GetPrimaryKeysString(IMetaModel metaModel, string projection)
        {
            return ObjPrimaryKey.FromPrimaryKey(metaModel, this)?.ToFormattedKeyRef(projection);
        }
    }
}
