﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Framework.Services;
using Ifs.Uma.Framework.UI.Forms;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Sync;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;
using Ifs.Uma.UI.Helpers;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Unity.Attributes;

namespace Ifs.Uma.Framework.Data
{
    public class SettingsFormData : FormData
    {
        private readonly ITouchApp _app;
        private readonly IMetadata _metadata;
        private readonly INavigator _navigator;
        private readonly IDialogService _dialogService;
        private readonly IPushNotificationService _pushNotificationService;
        private readonly IAppParameters _appParameters;
        private readonly ISystemNavigator _systemNavigator;
        private readonly ICachePreparer _cache;
        private readonly IToastService _toast;
        private readonly IIfsConnection _connection;
        private TextField _screenshotModeName;
        private BoolField _screenshotModeEnabled;
        private readonly TouchAppAccount _account;
        private readonly IScreenshotMode _screenshotMode;
        private string _oldTitle;
        private readonly ISyncController _syncController;

        private CommandField _simulateRefreshField;
        private CommandField _showAlertAfterDelayField;

        public SettingsFormData(ITouchApp app, IMetadata metadata, INavigator navigator, IDialogService dialogService,
            IPushNotificationService pushNotificationService, IAppParameters appParameters, ISystemNavigator systemNavigator,
            ICachePreparer cache, IToastService toast, IScreenshotMode screenshotMode, [OptionalDependency] IIfsConnection connection, ISyncController syncController)
        {
            _app = app;
            _metadata = metadata;
            _navigator = navigator;
            _dialogService = dialogService;
            _pushNotificationService = pushNotificationService;
            _appParameters = appParameters;
            _systemNavigator = systemNavigator;
            _cache = cache;
            _toast = toast;
            _screenshotMode = screenshotMode;
            _account = app.CurrentSession.Account;
            _connection = connection;

            SetupForm();
            _syncController = syncController;
        }

        protected override Form OnSetupForm()
        {
            FormBuilder<SettingsFormData> fb = new FormBuilder<SettingsFormData>(_metadata, this);
            fb.SetLayout(CreateFields(fb.Form));
            return fb.Form;
        }

        private string[][] CreateFields(Form form)
        {
            List<string[]> formItemIds = new List<string[]>();
            _oldTitle = _metadata?.CpiMetaData?.GetClients()?.FirstOrDefault()?.Name;
            if (_account != null && _account.PinAuthentication)
            {
                CommandField changePinCommand = new CommandField
                {
                    Value = Strings.ChangePinCode,
                    Command = Command.FromAsyncMethod(this.ChangePin)
                };
                form.AllFields.Add(changePinCommand);
                formItemIds.Add(new[] { changePinCommand.Id });
            }

            if (_app.DeveloperMode)
            {
                BoolField pushNotificationsEnabled = new BoolField
                {
                    Name = Strings.PushNotifications,
                    TrueLabel = Strings.On,
                    FalseLabel = Strings.Off,
                    IsRequired = true,
                    Value = _pushNotificationService.IsEnabled
                };
                pushNotificationsEnabled.ValueChanged += PushNotificationsEnabledOnValueChanged;
                form.AllFields.Add(pushNotificationsEnabled);
                formItemIds.Add(new[] { pushNotificationsEnabled.Id });

                if (!_app.CurrentSession.IsTryMeMode && (PlatformServices.SimulateNoConnection || PlatformServices.Provider.IsNetworkAvailable()))
                {
                    BoolField isNetworkAvailableField = new BoolField
                    {
                        Name = Strings.SimulateNoConnection,
                        TrueLabel = Strings.On,
                        FalseLabel = Strings.Off,
                        IsRequired = true,
                        Value = PlatformServices.SimulateNoConnection
                    };

                    isNetworkAvailableField.ValueChanged += SimulateNoConnectionOnValueChanged;
                    form.AllFields.Add(isNetworkAvailableField);
                    formItemIds.Add(new[] { isNetworkAvailableField.Id });
                }

                BoolField screenshotModeEnabled = new BoolField
                {
                    Name = Strings.ScreenshotMode,
                    TrueLabel = Strings.On,
                    FalseLabel = Strings.Off,
                    IsRequired = true,
                    Value = _screenshotMode.IsEnabled
                };
                screenshotModeEnabled.Value = _screenshotMode.IsEnabled;
                form.AllFields.Add(screenshotModeEnabled);
                formItemIds.Add(new[] { screenshotModeEnabled.Id });
                _screenshotModeEnabled = screenshotModeEnabled;

                _screenshotModeName = new TextField
                {
                    Name = Strings.ScreenshotModeAppName,
                    IsVisible = _screenshotMode.IsEnabled
                };
                if (_screenshotMode.IsEnabled)
                    _screenshotModeName.Value = _screenshotMode.Title;
                form.AllFields.Add(_screenshotModeName);
                formItemIds.Add(new[] { _screenshotModeName.Id });
                _screenshotModeName.ValueChanged += ScreenshotModeNameOnValueChanged;
                screenshotModeEnabled.ValueChanged += ScreenshotModeOnValueChanged;
            }

            CommandField openBugReporter = new CommandField
            {
                Value = Localization.Strings.SendDeviceLogs,
                Command = Command.FromMethod(() => { NavigateToAsync(FrameworkLocations.BugReporter); })
            };
            form.AllFields.Add(openBugReporter);
            formItemIds.Add(new[] { openBugReporter.Id });

            if (DeviceInfo.OperatingSystem == OperatingSystem.Windows && _app.DeveloperMode)
            {
                CommandField openDeveloperTools = new CommandField
                {
                    Value = Strings.DeveloperTools,
                    Command = Command.FromMethod(() => { NavigateToAsync(FrameworkLocations.DeveloperTools); })
                };
                form.AllFields.Add(openDeveloperTools);
                formItemIds.Add(new[] { openDeveloperTools.Id });
            }
            else if (_app.DeveloperMode || _appParameters.IsDatabaseViewerEnabled())
            {
                CommandField openDatabaseViewer = new CommandField
                {
                    Value = Localization.Strings.DatabaseViewer,
                    Command = Command.FromMethod(() => { NavigateToAsync(FrameworkLocations.DatabaseViewer); })
                };
                form.AllFields.Add(openDatabaseViewer);
                formItemIds.Add(new[] { openDatabaseViewer.Id });
            }

            CommandField clearCache = new CommandField
            {
                Value = Localization.Strings.ClearCache,
                Command = Command.FromMethod(() =>
                {
                    _cache.ClearCache();
                    _toast.Show(ToastType.Success, Strings.CacheCleared);
                })
            };

            CommandField clearDeviceLogs = new CommandField
            {
                Value = Strings.ClearDeviceLogs,
                Command = Command.FromMethod(async () =>
                {
                    bool confirmed = await _dialogService.Confirm(Strings.BackupData, Strings.SaveLogs, Strings.Clear, ConfirmationType.Normal);
                    if (confirmed)
                    {
                        NavigateToAsync(FrameworkLocations.LogCleaner);
                    }
                })
            };

            if (_app.CurrentSession.Account?.AppName == "FndMotOffline")
            {
                _simulateRefreshField = new CommandField
                {
                    Value = Strings.SimulateRefresh,
                    Command = Command.FromAsyncMethod(SimulateRefresh)
                };
                form.AllFields.Add(_simulateRefreshField);
                formItemIds.Add(new[] { _simulateRefreshField.Id });

                _showAlertAfterDelayField = new CommandField
                {
                    Value = Strings.ShowAlertAfterDelay,
                    Command = Command.FromAsyncMethod(ShowAlertAfterDelay)
                };
                form.AllFields.Add(_showAlertAfterDelayField);
                formItemIds.Add(new[] { _showAlertAfterDelayField.Id });
            }

            if (_app.DeveloperMode)
            {
                CommandField certificateInfo = new CommandField
                {
                    Value = "(Debug) Show Certificate Info",
                    Command = Command.FromAsyncMethod(ShowCertificateInfo)
                };
                form.AllFields.Add(certificateInfo);
                formItemIds.Add(new[] { certificateInfo.Id });

                CommandField pushHandle = new CommandField
                {
                    Value = "(Debug) Show Push Handle",
                    Command = Command.FromAsyncMethod(ShowPushHandle)
                };
                form.AllFields.Add(pushHandle);
                formItemIds.Add(new[] { pushHandle.Id });

#if SIGNATURE_SERVICE
                CommandField clearDssData = new CommandField
                {
                    Value = "(Debug) Clear local DSS data",
                    Command = Command.FromMethod(ClearDssData)
                };
                form.AllFields.Add(clearDssData);
                formItemIds.Add(new[] { clearDssData.Id });
#endif
            }

            CommandField openAbout = new CommandField
            {
                Value = Localization.Strings.About,
                Command = Command.FromMethod(() => { _systemNavigator.NavigateToAboutAsync(); })
            };
            form.AllFields.Add(openAbout);
            formItemIds.Add(new[] { openAbout.Id });

            form.AllFields.Add(clearCache);
            formItemIds.Add(new[] { clearCache.Id });

            form.AllFields.Add(clearDeviceLogs);
            formItemIds.Add(new[] { clearDeviceLogs.Id });

            return formItemIds.ToArray();
        }

        private void NavigateToAsync(NavigationLocation navigationLocation)
        {
            _navigator.NavigateFromRootAsync(navigationLocation, null);
        }

        private async Task ChangePin()
        {
            await _app.ChangePinCode();
        }

        private async Task ShowAlertAfterDelay()
        {
            _showAlertAfterDelayField.CanExecute = false;

            for (int i = 10; i > 0; i--)
            {
                _showAlertAfterDelayField.Value = Strings.ShowAlertAfterDelay + " (" + i + ")";
                await Task.Delay(1000);
            }
            _showAlertAfterDelayField.Value = Strings.ShowAlertAfterDelay;

            await _dialogService.ShowAsync(string.Empty, Strings.ShowAlertAfterDelay, new[] { Strings.Ok });

            _showAlertAfterDelayField.CanExecute = true;
        }

        private async Task SimulateRefresh()
        {
            _simulateRefreshField.CanExecute = false;

            for (int i = 10; i > 0; i--)
            {
                _simulateRefreshField.Value = Strings.SimulateRefresh + " (" + i + ")";

                await Task.Delay(1000);
            }
            _simulateRefreshField.Value = Strings.SimulateRefresh;

            _connection?.TouchAppsComms.SimulateRefreshTokenExpiration();
            _syncController.RequestSync();

            _simulateRefreshField.CanExecute = true;
        }

        private async Task ShowCertificateInfo()
        {
            Dictionary<string, string> info = _appParameters.GetCertificateInfo();
            if (info != null)
            {
                info.TryGetValue(AppParameterNames.CommonName, out string commonName);
                info.TryGetValue(AppParameterNames.DigestValue, out string digestValue);
                await _dialogService.Alert("(Debug) Certificate Info", string.Format("{0}: {1}{2}{3}: {4}",
                    AppParameterNames.CommonName,
                    commonName,
                    System.Environment.NewLine,
                    AppParameterNames.DigestValue,
                    digestValue));
            }
        }

        private async Task ShowPushHandle()
        {
            await _dialogService.Alert("(Debug) Push Handle", _pushNotificationService.GetPushHandle());
        }

#if SIGNATURE_SERVICE
        private void ClearDssData()
        {
            if (Resolver.TryResolve(out IDatabaseController db))
            {
                FwDataContext ctx = db.CreateDataContext();

                if (ctx.DigitalSignatureAndDocument.Any())
                {
                    ctx.DigitalSignatureAndDocument.DeleteAllOnSubmit(ctx.DigitalSignatureAndDocument.ToArray());
                    ctx.SubmitChanges(false);
                    _dialogService.Alert("(Debug) Clear local DSS data", "Local DSS data table has been cleared.");
                }
                else
                {
                    _dialogService.Alert("(Debug) Clear local DSS data", "No records found in local DSS data table.");
                }
            }
        }
#endif

        private void PushNotificationsEnabledOnValueChanged(object sender, ValueChangedEventArgs valueChangedEventArgs)
        {
            if (sender is BoolField pushNotificationsEnabled && pushNotificationsEnabled.Value is bool)
            {
                _pushNotificationService.IsEnabled = (bool)pushNotificationsEnabled.Value;
            }
        }

        private void SimulateNoConnectionOnValueChanged(object sender, ValueChangedEventArgs e)
        {
            if (sender is BoolField isNetworkAvaiableField && isNetworkAvaiableField.Value is bool)
            {
                PlatformServices.SimulateNoConnection = (bool)isNetworkAvaiableField.Value;
            }
        }

        private void ScreenshotModeOnValueChanged(object sender, ValueChangedEventArgs valueChangedEventArgs)
        {
            if (sender is BoolField screenshotModeEnabled && screenshotModeEnabled.Value is bool)
            {
                bool screenshotEnabledValue;
                if (_screenshotModeEnabled.Value.Equals(true))
                {
                    _screenshotModeName.IsVisible = true;
                    screenshotEnabledValue = true;
                    _oldTitle = _metadata.CpiMetaData.GetClients().FirstOrDefault()?.Name.ToString();
                    _screenshotMode.Title = _screenshotModeName.Value.ToString();
                    _screenshotMode.IsEnabled = screenshotEnabledValue;
                }
                else
                {
                    _screenshotModeName.IsVisible = false;
                    screenshotEnabledValue = false;
                    _screenshotMode.Title = _oldTitle;
                    _screenshotMode.IsEnabled = screenshotEnabledValue;
                }
            }
        }

        private void ScreenshotModeNameOnValueChanged(object sender, ValueChangedEventArgs e)
        {
            _screenshotMode.Title = _screenshotModeName.Value.ToString();
        }
    }
}
