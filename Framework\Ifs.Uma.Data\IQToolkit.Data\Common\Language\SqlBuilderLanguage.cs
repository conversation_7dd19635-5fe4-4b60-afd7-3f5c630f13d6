﻿using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using IQToolkit.Data;
using IQToolkit.Data.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace IQToolkit.Data.Common.Language
{
    internal sealed class SqlBuilderLanguage : QueryLanguage
    {
        private SqlBuilder _sqlBuilder;

        public SqlBuilderLanguage(SqlBuilder sqlBuilder)
        {
            if (sqlBuilder == null) throw new ArgumentNullException("sqlBuilder");
            _sqlBuilder = sqlBuilder;
        }

        public override Expression GetGeneratedIdExpression(IMetaDataMember member)
        {
            if (member == null) throw new ArgumentNullException("member");
            return new FunctionExpression(member.ColumnType, _sqlBuilder.ScopeIdentityFuncName, null);
        }

        public override Expression GetRowsAffectedExpression(Expression command)
        {
            return new FunctionExpression(typeof(int), _sqlBuilder.RowsAffectedFuncName, null);
        }

        public override bool IsRowsAffectedExpressions(Expression expression)
        {
            FunctionExpression fex = expression as FunctionExpression;
            return fex != null && fex.Name == _sqlBuilder.RowsAffectedFuncName;
        }

        public override QueryLinguist CreateLinguist(QueryTranslator translator)
        {
            return new SqlBuilderLinguist(this, translator, _sqlBuilder);
        }

        private class SqlBuilderLinguist : QueryLinguist
        {
            private SqlBuilder _sqlBuilder;

            public SqlBuilderLinguist(SqlBuilderLanguage language, QueryTranslator translator, SqlBuilder sqlBuilder)
                : base(language, translator)
            {
                if (sqlBuilder == null) throw new ArgumentNullException("sqlBuilder");
                _sqlBuilder = sqlBuilder;
            }

            public override Expression Translate(Expression expression)
            {
                // fix up any order-by's
                expression = OrderByRewriter.Rewrite(expression);

                expression = base.Translate(expression);

                //expression = SkipToNestedOrderByRewriter.Rewrite(expression);
                expression = UnusedColumnRemover.Remove(expression);

                return expression;
            }

            public override string Format(Expression expression)
            {
                return SqlBuilderFormatter.Format(_sqlBuilder, expression);
            }
        }
    }
}
