{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_Replace>": {"name": "String_Replace", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "SearchString", "dataType": "Text"}, {"name": "TextReplacement", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "Replace", "paramsArray": ["${TextInput}", "${SearchString}", "${TextReplacement}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}