﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization;
using Ifs.Cloud.Client;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class ActivateResource : ActivateResourceBase
    {
        public ActivateResource()
        {
        }

        public ActivateResource(int deviceId)
        {
            DeviceId = deviceId;
        }

        public override bool SingleResponse { get { return true; } }

        [DataMember(Name = "ClientInfo")]
        public ClientInfo ClientInfo { get; set; }

        [DataMember(Name = "ClientParams", EmitDefaultValue = false)]
        public string ClientParams { get; set; }

        [DataMember(Name = "MobileContext")]
        public MobileContext MobileContext { get; set; }

        [DataMember(Name = "value", EmitDefaultValue = false)]
        public string Value { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? DeviceId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FndmobVersion { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LocaleCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool PinAuthentication { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }

        [DataMember(Name = "@odata.context", EmitDefaultValue = false)]
        public string Context { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ScopeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BrandingCode { get; set; }
    }

    [DataContract]
    public class ActivationInfo : AppResource
    {
        public override string ResourceName => "MobileClientRuntime.svc/ActivateDevice";

        [DataMember]
        public string AppName { get; set; }

        //[DataMember]
        //public string AppVersion { get; set; }

        [DataMember]
        public string Os { get; set; }

        [DataMember]
        public string Identifier { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public string LocaleCode { get; set; }
    }

    [DataContract]
    public class TempLobStoreInfo : AppResource, ICustomResourceSerializer
    {
        public override string ResourceName => ProjectionName + ".svc/FndTempLobs";

        [DataMember(Name = "@odata.etag")]
        public string ETagString { get; set; }
        [DataMember]
        public string ProjectionName { get; set; }
        [DataMember]
        public string LobId { get; set; }

        public EntityQuery Query { get; set; }
        public IClientKeysMapper ClientKeysMapper { get; set; }

        public object DeserializeJsonString(string jsonString)
        {
            TempLobStoreInfo tempLobStoreInfo;
            using (StringReader sr = new StringReader(jsonString))
            using (JsonReader reader = new JsonTextReader(sr))
            {
                JsonSerializer serializer = new JsonSerializer();
                tempLobStoreInfo = serializer.Deserialize<TempLobStoreInfo>(reader);
            }

            return new ExecuteResult(tempLobStoreInfo);
        }

        public object EmptyResponseHandling(bool isSuccess)
        {
            return isSuccess ? ExecuteResult.True : ExecuteResult.False;
        }

        public string SerializeToJsonString()
        {
            JObject mobileContext = new JObject();
            mobileContext.Add("CreatedByModule", ProjectionName);

            return MessageUtils.JObjectToString(mobileContext);
        }

        private EntityRecord ReadRecord(JObject jRow, List<Tuple<string, IMetaTable>> refs)
        {
            IMetaTable table = Query.DataSource.Table;
            RemoteRow row = MessageUtils.JObjectToRow(table, ClientKeysMapper, jRow);

            Dictionary<string, RemoteRow> refRows = null;
            if (refs != null)
            {
                foreach (Tuple<string, IMetaTable> reference in refs)
                {
                    string jsonRefName = RemoteNaming.ToServerEntityReferenceName(reference.Item1);
                    JToken jRef = jRow[jsonRefName];
                    if (jRef != null)
                    {
                        if (jRef.Type == JTokenType.Null)
                        {
                            if (refRows == null)
                            {
                                refRows = new Dictionary<string, RemoteRow>();
                            }

                            refRows[reference.Item1] = null;
                        }
                        else if (jRow[jsonRefName] is JObject refJRow)
                        {
                            RemoteRow refRow = MessageUtils.JObjectToRow(table, ClientKeysMapper, refJRow);
                            if (refRow != null)
                            {
                                if (refRows == null)
                                {
                                    refRows = new Dictionary<string, RemoteRow>();
                                }

                                refRows[reference.Item1] = refRow;
                            }
                        }
                    }
                }
            }

            return new EntityRecord(row, refRows);
        }

        private List<Tuple<string, IMetaTable>> GetReferences()
        {
            Dictionary<string, CpiReference> refs = Query.DataSource.Metadata.FindReferences(Query.DataSource.ProjectionName, Query.DataSource.EntityName);
            if (refs == null || refs.Count == 0)
            {
                return null;
            }

            List<Tuple<string, IMetaTable>> references = new List<Tuple<string, IMetaTable>>();

            foreach (var reference in refs)
            {
                IMetaTable refTable = Query.DataSource.Metadata.GetTableForEntityName(reference.Value.Target);
                if (refTable != null)
                {
                    references.Add(new Tuple<string, IMetaTable>(reference.Key, refTable));
                }
            }

            return references;
        }
    }

    [DataContract]
    public class ThemeData : AppResource
    {
        private string _context;

        public ThemeData(string context)
        {
            _context = context;
        }

        public override string ResourceName => $"MobileClientRuntime.svc/GetBrandingPropertyList(Context='{_context}')";

        [DataMember(Name = "Code")]
        public string Code { get; set; }

        [DataMember(Name = "Property")]
        public string Property { get; set; }

        [DataMember(Name = "Theme")]
        public string ThemeName { get; set; }

        [DataMember(Name = "Value")]
        public string Value { get; set; }

        [DataMember(Name = "Type")]
        public string ValueType { get; set; }

        [DataMember(Name = "CssProperty")]
        public string CssProperty { get; set; }

        [DataMember(Name = "CssOverride")]
        public string CssOverride { get; set; }
    }
}
