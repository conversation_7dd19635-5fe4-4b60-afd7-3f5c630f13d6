﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading;
using Ifs.Uma.Utility;
using IQToolkit.Data.Common;
using System.Collections;
using Ifs.Uma.Data;
using IQToolkit.Data;

namespace IQToolkit
{
    internal class QueryCache
    {
        private const int DefaultCacheSize = 100;

        MostRecentlyUsedCache<QueryCompiler.CompiledQuery> m_cache;

        static readonly Func<QueryCompiler.CompiledQuery, QueryCompiler.CompiledQuery, bool> fnCompareQueries = CompareQueries;
        static readonly Func<object, object, bool> fnCompareValues = CompareConstantValues;

        private static Lazy<QueryCache> _instance = new Lazy<QueryCache>(() => new QueryCache(DefaultCacheSize));
        public static QueryCache Instance
        {
            get
            {
                Lazy<QueryCache> cache = _instance;
                return cache == null ? null : cache.Value;
            }
        }

        public static bool IsEnabled
        {
            get
            {
                return _instance != null;
            }
            set
            {
                if (value)
                {
                    if (_instance == null)
                    {
                        _instance = new Lazy<QueryCache>(() => new QueryCache(DefaultCacheSize));
                    }
                }
                else
                {
                    _instance = null;
                }
            }
        }

        public QueryCache(int maxSize)
        {
            m_cache = new MostRecentlyUsedCache<QueryCompiler.CompiledQuery>(maxSize, fnCompareQueries);
        }

        private static bool CompareQueries(QueryCompiler.CompiledQuery x, QueryCompiler.CompiledQuery y)
        {
            return ExpressionComparer.AreEqual(x.Query, y.Query, fnCompareValues);
        }

        private static bool CompareConstantValues(object x, object y)
        {
            if (x == y) return true;
            if (x == null || y == null) return false;
            if (x is ITable && y is ITable) return CompareTable((ITable)x, (ITable)y);
            if (x is IQueryable && y is IQueryable && x.GetType() == y.GetType()) return true;
            
            if (x is IEnumerable && y is IEnumerable && x.GetType() == y.GetType())
            {
                IEnumerator e1 = ((IEnumerable)x).GetEnumerator();
                IEnumerator e2 = ((IEnumerable)y).GetEnumerator();

                while (e1.MoveNext())
                {
                    if (!(e2.MoveNext() && object.Equals(e1.Current, e2.Current))) return false;
                }

                if (e2.MoveNext()) return false;

                return true;
            }
            
            return object.Equals(x, y);
        }

        private static bool CompareTable(ITable x, ITable y)
        {
            return x.Entity == y.Entity;
        }

        public object Execute(Expression query)
        {
            query = EnumRewriter.Rewrite(query);

            object[] args;
            var cached = Find(query, true, out args);
            return cached.Invoke(args);
        }

        public object Execute(IQueryable query)
        {
            if (query == null) throw new ArgumentNullException("query");
            return Execute(query.Expression);
        }

        public IEnumerable<T> Execute<T>(IQueryable<T> query)
        {
            if (query == null) throw new ArgumentNullException("query");
            return (IEnumerable<T>)Execute(query.Expression);
        }

        public int Count { get { return m_cache.Count; } }

        public void Clear()
        {
            m_cache.Clear();
        }

        public bool Contains(Expression query)
        {
            object[] args;
            return Find(query, false, out args) != null;
        }

        public bool Contains(IQueryable query)
        {
            if (query == null) throw new ArgumentNullException("query");
            return Contains(query.Expression);
        }

        internal QueryCompiler.CompiledQuery Find(Expression query, bool add, out object[] args)
        {
            var pq = Parameterize(query, out args);
            var cq = new QueryCompiler.CompiledQuery(pq);
            QueryCompiler.CompiledQuery cached = null;
            m_cache.Lookup(cq, add, out cached);
            return cached;
        }

        private static LambdaExpression Parameterize(Expression query, out object[] arguments)
        {
            IQueryProvider provider = FindProvider(query);
            if (provider == null)
            {
                throw new ArgumentException("Cannot deduce query provider from query");
            }

            EntityProvider ep = provider as EntityProvider;

            if (ep == null)
            {
                throw new ArgumentException("Cannot deduce EntityProvider from query");
            }

            Func<Expression, bool> epCanBeEvaluatedLocally = ep.CanBeEvaluatedLocally;
            List<ParameterExpression> parameters = new List<ParameterExpression>();
            List<object> values = new List<object>();           

            Expression body = PartialEvaluator.Eval(query, epCanBeEvaluatedLocally, c =>
            {
                return ExtractParameters(c, ep, parameters, values);
            });

            if (body.Type != typeof(object))
            {
                body = Expression.Convert(body, typeof(object));
            }

            // The first parmater of the compiled method is always the EntityProvider
            ParameterExpression epParam = Expression.Parameter(typeof(EntityProvider), "ep");

            if (values.Count < 4)
            {
                parameters.Insert(0, epParam);
                values.Insert(0, ep);
                arguments = values.ToArray();

                return Expression.Lambda(body, parameters.ToArray());
            }
            else
            {
                arguments = new object[] { ep, values.ToArray() };
                return ExplicitToObjectArray.Rewrite(body, epParam, parameters);
            }
        }

        private static Expression ExtractParameters(ConstantExpression c, EntityProvider ep, 
            List<ParameterExpression> parameters, List<object> values)
        {
            IQueryable queryable = c.Value as IQueryable;
            if (queryable != null)
            {
                if (queryable.Expression == c)
                {
                    return c;
                }
                else
                {
                    Expression innerQuery = PartialEvaluator.Eval(queryable.Expression, ep.CanBeEvaluatedLocally, c2 =>
                    {
                        return ExtractParameters(c2, ep, parameters, values);
                    });

                    return innerQuery;
                }
            }

            if (!ep.CanBeParameter(c))
            {
                return c;
            }

            var p = Expression.Parameter(c.Type, "p" + ObjectConverter.ToString(parameters.Count));
            parameters.Add(p);
            values.Add(c.Value);
            return p;
        }

        private static IQueryProvider FindProvider(Expression expression)
        {
            ConstantExpression root = TypedSubtreeFinder.Find(expression, typeof(IQueryProvider)) as ConstantExpression;
            if (root == null)
            {
                root = TypedSubtreeFinder.Find(expression, typeof(IQueryable)) as ConstantExpression;
            }
            if (root != null)
            {
                IQueryProvider provider = root.Value as IQueryProvider;
                if (provider == null)
                {
                    IQueryable query = root.Value as IQueryable;
                    if (query != null)
                    {
                        provider = query.Provider;
                    }
                }
                return provider;
            }
            return null;
        }

        class ExplicitToObjectArray : ExpressionVisitorEx
        {
            private IList<ParameterExpression> parameters;
            private ParameterExpression array = Expression.Parameter(typeof(object[]), "array");
            // This stops the OSX complier elevating the warning to an error "assigned but its value is never used"
            #pragma warning disable 414
            private ParameterExpression epParam;
            #pragma warning restore 414

            private ExplicitToObjectArray(ParameterExpression epParam, IList<ParameterExpression> parameters)
            {
                this.parameters = parameters;
                this.epParam = epParam;
            }

            internal static LambdaExpression Rewrite(Expression body, ParameterExpression epParam, IList<ParameterExpression> parameters)
            {
                var visitor = new ExplicitToObjectArray(epParam, parameters);
                return Expression.Lambda(visitor.Visit(body), epParam, visitor.array);                  
            }

            protected override Expression VisitParameter(ParameterExpression p)
            {
                for (int i = 0, n = this.parameters.Count; i < n; i++)
                {
                    if (this.parameters[i] == p)
                    {
                        return Expression.Convert(Expression.ArrayIndex(this.array, Expression.Constant(i)), p.Type);
                    }
                }
                return p;
            }
        }
    }
}
