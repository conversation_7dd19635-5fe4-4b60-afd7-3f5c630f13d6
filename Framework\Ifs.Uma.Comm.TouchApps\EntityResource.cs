﻿using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.KeyMapping;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Comm.TouchApps
{
    internal class EntityResource : AppResource, ICustomResourceSerializer, IQueryStringProvider
    {
        public override string ResourceName => GetResourceUrl();
        public EntityQuery Query { get; set; }
        public int DeviceId { get; set; }
        public IClientKeysMapper ClientKeysMapper { get; set; }
        public string EntitySetName => Query.DataSource.EntitySetName;
        public string ProjectionName => Query.DataSource.ProjectionName;
        protected override string ResourceSection => "entity";

        public string SerializeToJsonString()
        {
            return string.Empty;
        }

        public class EntityData
        {
            [JsonProperty(PropertyName = "@odata.context")]
            public string Context { get; set; }

            [JsonProperty(PropertyName ="Value")]
            public JArray Result { get; set; }
        }

        public object DeserializeJsonString(string jsonString)
        {
            return ResourceUtils.DeserializeJsonString(Query, ClientKeysMapper, jsonString);
        }

        public object EmptyResponseHandling(bool isSuccess)
        {
            return isSuccess ? ExecuteResult.True : ExecuteResult.False;
        }

        public string GetQueryString()
        {
            return ResourceUtils.GetQueryString(Query);
        }

        private string GetResourceUrl()
        {
            string url = ProjectionName + ".svc\\" + EntitySetName;

            if (Query.DataSource is ArrayDataSource array)
            {
                string primaryKeyString = array.ParentKey.ToFormattedKeyRef(ProjectionName);
                primaryKeyString = primaryKeyString.Replace("^", ",");
                primaryKeyString = primaryKeyString.Replace("/", "%2F");
                primaryKeyString = primaryKeyString.Remove(primaryKeyString.Length - 1);
                primaryKeyString = char.ToUpper(primaryKeyString[0]) + primaryKeyString.Substring(1);
                url += "(" + primaryKeyString + ")/" + array.ArrayPropertyName;
            }

            return url;
        }
    }
}
