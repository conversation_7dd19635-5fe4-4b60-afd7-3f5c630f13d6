﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringToUpperTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringToUpperTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("HelloWorld", ExpectedResult = "HELLOWORLD")]
        [TestCase(1, ExpectedResult = "1")]
        [TestCase(1.1, ExpectedResult = "1.1")]
        [TestCase(true, ExpectedResult = "TRUE")]
        [TestCase(null, ExpectedResult = null)]
        public async Task<string> String_ToUpper(object input)
        {
            _params["TextInput"] = input;
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_ToUpper", _params);
            CheckResult(result);

            return result?.Value as string;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
