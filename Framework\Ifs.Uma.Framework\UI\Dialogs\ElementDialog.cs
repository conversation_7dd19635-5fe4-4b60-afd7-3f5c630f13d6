﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.UI.Observables;

namespace Ifs.Uma.Framework.UI.Dialogs
{
    public class ElementDialog : ObservableBase
    {
        private readonly IElementDialogService _dialogService;
        private readonly IMetadata _metadata;
        private readonly ICommandExecutor _commandExecutor;

        public event EventHandler<EventArgs> Closed;

        public CommandBlock Commands { get; }
        public ElementList Elements { get; }
        public PageData Data { get; private set; }
        public object Result { get; private set; }

        private string _title;
        public string Title
        {
            get
            {
                return _title;
            }
            private set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged(nameof(Title));
                }
            }
        }

        public UpdatingState UpdatingState { get; } = new UpdatingState();

        private readonly IExpressionRunner _expressionRunner;
        private readonly IRoamingProfile _roamingProfile;

        public ElementDialog(ElementCreator elementCreator, IMetadata metadata, IElementDialogService dialogService, ICommandExecutor commandExecutor,
            IExpressionRunner expressionRunner, IRoamingProfile roamingProfile)
        {
            _dialogService = dialogService;
            _metadata = metadata;
            _commandExecutor = WrappedCommandExecutor.Create(commandExecutor, BeforeCommand, AfterCommand);
            _expressionRunner = expressionRunner;
            _roamingProfile = roamingProfile;

            Commands = new CommandBlock(UpdatingState, metadata, _commandExecutor, expressionRunner);
            Elements = new ElementList(UpdatingState, elementCreator);
        }

        public async Task<ExecuteResult> ShowAsync(CpiDialog cpiDialog, RecordData record)
        {
            LoadData(cpiDialog, record);
            return await _dialogService.ShowAsync(this);
        }

        private void LoadData(CpiDialog dialog, RecordData record)
        {
            Title = _expressionRunner.InterpolateString(dialog.Label, record);

            Data = new PageData(record);
            Data.PageSettings = _roamingProfile.GetSettings("Dialogs", record.ProjectionName, dialog.Name);
            Data.DefaultViewData.CommandsEnabledOnEmpty = true;
            Data.DefaultViewData.CommandsEnabledOnHasChanges = true;

            Commands.PageData = Data;
            Elements.PageData = Data;
        
            Commands.Load(record.ProjectionName, dialog.CommandGroups, true, true, true);
            Elements.LoadElements(record.ProjectionName, dialog.CommandGroups, dialog.Content, dialog.StateIndicator);
        }

        private async Task<ExecuteResult> BeforeCommand(ViewData data, CpiCommand command, CommandOptions options)
        {
            if (CommandExecutor.IsOkCommand(command) && !await Elements.ValidateAsync())
            {
                return ExecuteResult.None;
            }

            return null;
        }

        private Task<ExecuteResult> AfterCommand(ViewData data, CpiCommand command, CommandOptions options, ExecuteResult result)
        {
            if (result.Failed)
            {
                return Task.FromResult(ExecuteResult.None);
            }

            Result = result;

            Commands.PageData = null;
            Elements.PageData = null;

            Data.DefaultViewData.Record.ResetHasChanges();

            Closed?.Invoke(this, EventArgs.Empty);
            return Task.FromResult(ExecuteResult.None);
        }
    }
}
