﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\NUnit.3.10.1\build\NUnit.props" Condition="Exists('..\packages\NUnit.3.10.1\build\NUnit.props')" />
  <Import Project="..\packages\NUnit3TestAdapter.3.10.0\build\net35\NUnit3TestAdapter.props" Condition="Exists('..\packages\NUnit3TestAdapter.3.10.0\build\net35\NUnit3TestAdapter.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0D5C057A-9D0B-49CB-8A07-8988CBD2CC6F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Ifs.Uma.AppData.Tests</RootNamespace>
    <AssemblyName>Ifs.Uma.AppData.Tests</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\FrameworkRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Release\Ifs.Uma.AppData.Tests.XML</DocumentationFile>
    <CodeAnalysisRuleSet>..\FrameworkRules.Release.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>keyfile.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CommonServiceLocator, Version=2.0.4.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.8.13\lib\net46\CommonServiceLocator.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.OData.Core, Version=7.5.1.20914, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.OData.Core.7.5.1\lib\portable-net45+win8+wpa81\Microsoft.OData.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.OData.Edm, Version=7.5.1.20914, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.OData.Edm.7.5.1\lib\portable-net45+win8+wpa81\Microsoft.OData.Edm.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Spatial, Version=7.5.1.20914, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Spatial.7.5.1\lib\portable-net45+win8+wpa81\Microsoft.Spatial.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NodaTime, Version=3.1.9.0, Culture=neutral, PublicKeyToken=4226afe0d9b296d1, processorArchitecture=MSIL">
      <HintPath>..\packages\NodaTime.3.1.9\lib\netstandard2.0\NodaTime.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework, Version=3.10.1.0, Culture=neutral, PublicKeyToken=2638cd05610744eb, processorArchitecture=MSIL">
      <HintPath>..\packages\NUnit.3.10.1\lib\net45\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="NodaTime, Version=3.1.9.0, Culture=neutral, PublicKeyToken=4226afe0d9b296d1, processorArchitecture=MSIL">
      <HintPath>..\packages\NodaTime.3.1.9\lib\netstandard2.0\NodaTime.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="Prism, Version=7.0.0.396, Culture=neutral, PublicKeyToken=40ee6c3a2184dc59, processorArchitecture=MSIL">
      <HintPath>..\packages\Prism.Core.7.0.0.396\lib\net45\Prism.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.batteries_sqlcipher, Version=1.1.14.520, Culture=neutral, PublicKeyToken=83625171f3d0bf82, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.bundle_sqlcipher.1.1.14\lib\net45\SQLitePCLRaw.batteries_sqlcipher.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.batteries_v2, Version=1.1.14.520, Culture=neutral, PublicKeyToken=8226ea5df37bcae9, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.bundle_sqlcipher.1.1.14\lib\net45\SQLitePCLRaw.batteries_v2.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.core, Version=1.1.14.520, Culture=neutral, PublicKeyToken=1488e028ca7ab535, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.core.1.1.14\lib\net45\SQLitePCLRaw.core.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.provider.sqlcipher, Version=1.1.14.520, Culture=neutral, PublicKeyToken=7bbc99275c710061, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.provider.sqlcipher.net45.1.1.14\lib\net45\SQLitePCLRaw.provider.sqlcipher.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.4.7.0\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.7.1\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.6.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.6.0.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="Unity.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.8.13\lib\net46\Unity.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Configuration, Version=*******, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.8.13\lib\net46\Unity.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Container, Version=********, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.8.13\lib\net46\Unity.Container.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Interception, Version=5.5.5.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.8.13\lib\net46\Unity.Interception.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Interception.Configuration, Version=5.1.7.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.8.13\lib\net46\Unity.Interception.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RegistrationByConvention, Version=2.1.9.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.8.13\lib\net46\Unity.RegistrationByConvention.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ServiceLocation, Version=2.1.2.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.8.13\lib\net46\Unity.ServiceLocation.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Attachments\MediaHandlerTests.cs" />
    <Compile Include="Attachments\DocumentHandlerTests.cs" />
    <Compile Include="Cache\CachExpiryTests.cs" />
    <Compile Include="ContextSubstituionVaribaleTest.cs" />
    <Compile Include="DatabaseControllerTests.cs" />
    <Compile Include="Database\CpiQuerySqlTests.cs" />
    <Compile Include="Database\QueryExpressionSqlTests.cs" />
    <Compile Include="Execution\IfsApiMethodLogicTest.cs" />
    <Compile Include="Execution\Procedures\ExecuteCopyCustomFieldsTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringIndexTest.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringSubStringLengthTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringTokenizeTests.cs" />
    <Compile Include="Execution\Procedures\Functions\Sync\TransactionSessionTests.cs" />
    <Compile Include="Execution\Procedures\SynchronizationEndedEventTests.cs" />
    <Compile Include="Messages\DataChangeMessageInAdvancedTests.cs" />
    <Compile Include="ObjPrimaryKeyTests.cs" />
    <Compile Include="ODataToAttributeExpressionTest.cs" />
    <Compile Include="OfflineQueryTests.cs" />
    <Compile Include="Execution\AttributeExpressionTests.cs" />
    <Compile Include="Execution\Procedures\Functions\Attachment\AttachmentConnectTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringWhereTests.cs" />
    <Compile Include="KeyMapping\KeyMappingTests.cs" />
    <Compile Include="KeyMapping\ClientGeneratedKeyTests.cs" />
    <Compile Include="KeyMapping\ClientKeyMapperTests.cs" />
    <Compile Include="DataContextTest.cs" />
    <Compile Include="EntityDataSourceTests.cs" />
    <Compile Include="EntityQueryApplyTests.cs" />
    <Compile Include="AggregateQuerySqlTests.cs" />
    <Compile Include="Expressions\DynamicValueTests.cs" />
    <Compile Include="EntityQuerySqlTests.cs" />
    <Compile Include="Cache\CachePreparerTests.cs" />
    <Compile Include="EntityQueryTests.cs" />
    <Compile Include="Execution\ExecuteIfTests.cs" />
    <Compile Include="Execution\ExecuteReturnTests.cs" />
    <Compile Include="Execution\ExecuteSetTests.cs" />
    <Compile Include="Database\ExecutionWhereElementTests.cs" />
    <Compile Include="Expressions\InterpolatedStringTests.cs" />
    <Compile Include="Expressions\IfsExpressionFromStringTests.cs" />
    <Compile Include="Execution\JsonLogicTests.cs" />
    <Compile Include="Execution\Procedures\EntityProcedureTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteChangeTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteWhileTests.cs" />
    <Compile Include="Execution\Procedures\Functions\Convert\ConvertToStringTests.cs" />
    <Compile Include="Execution\Procedures\Functions\Convert\ConvertToTimestampTests.cs" />
    <Compile Include="Execution\Procedures\Functions\Convert\ConvertToBooleanTests.cs" />
    <Compile Include="Execution\Procedures\Functions\Convert\ConvertToIntegerTests.cs" />
    <Compile Include="Execution\Procedures\Functions\Convert\ConvertToNumberTests.cs" />
    <Compile Include="Execution\Procedures\Functions\DataTime\DataTimeDifferenceTests.cs" />
    <Compile Include="Execution\Procedures\Functions\DataTime\DataTimeAddTests.cs" />
    <Compile Include="Execution\Procedures\Functions\DataTime\DataTimeCallTests.cs" />
    <Compile Include="Execution\Procedures\Functions\DataTime\DataTimeWhereTests.cs" />
    <Compile Include="Execution\Procedures\Functions\Security\SecurityEntityTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringRegexSubStringTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringRegexCountTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringRegexReplaceTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringReplaceTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringRegexLikeTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringToUpperTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringTrimEndTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringLikeTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringTrimStartTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringTrimTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringToLowerTests.cs" />
    <Compile Include="Execution\Procedures\Functions\String\StringLengthTests.cs" />
    <Compile Include="Execution\Procedures\SystemCallTests.cs" />
    <Compile Include="Execution\Procedures\ListTests.cs" />
    <Compile Include="Execution\Procedures\OnlineDisallowedTests.cs" />
    <Compile Include="Execution\Procedures\PrepareCacheTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteCountTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteCreateTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteLogTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteErrorTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteFetchTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteForTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteProcTests.cs" />
    <Compile Include="Execution\Procedures\ExecuteSuperTests.cs" />
    <Compile Include="Execution\Procedures\ProcedureTest.cs" />
    <Compile Include="Execution\TestExecutor.cs" />
    <Compile Include="Messages\ResponseMessageInTests.cs" />
    <Compile Include="Messages\DataChangeMessageInTests.cs" />
    <Compile Include="Messages\MessageTestContext.cs" />
    <Compile Include="Messages\MessageTests.cs" />
    <Compile Include="Messages\TransactionIdMessageTests.cs" />
    <Compile Include="OData\ODataResourcePathTests.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TestDataContext.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Ifs.Uma.AppData\Ifs.Uma.AppData.csproj">
      <Project>{e0deabf2-bc21-4f34-8199-cdab1c2d4f62}</Project>
      <Name>Ifs.Uma.AppData</Name>
    </ProjectReference>
    <ProjectReference Include="..\Ifs.Uma.Database.SQLite\Ifs.Uma.Database.SQLite.csproj">
      <Project>{9810549e-1753-4b2b-a17e-ebcafb5202ff}</Project>
      <Name>Ifs.Uma.Database.SQLite</Name>
    </ProjectReference>
    <ProjectReference Include="..\Ifs.Uma.Database\Ifs.Uma.Database.csproj">
      <Project>{72950b3d-9b21-402b-8d68-64e1cab0276a}</Project>
      <Name>Ifs.Uma.Database</Name>
    </ProjectReference>
    <ProjectReference Include="..\Ifs.Uma.Data\Ifs.Uma.Data.csproj">
      <Project>{483d4d41-37e8-485b-b99f-47893ea8ad39}</Project>
      <Name>Ifs.Uma.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Ifs.Uma.Metadata\Ifs.Uma.Metadata.csproj">
      <Project>{4b8c7146-9df0-44a7-8cbd-ec919ad09590}</Project>
      <Name>Ifs.Uma.Metadata</Name>
    </ProjectReference>
    <ProjectReference Include="..\Ifs.Uma.Tests\Ifs.Uma.Tests.csproj">
      <Project>{BF751AEB-4744-4FC9-B247-1B8224938040}</Project>
      <Name>Ifs.Uma.Tests</Name>
    </ProjectReference>
    <ProjectReference Include="..\Ifs.Uma.Utility\Ifs.Uma.Utility.csproj">
      <Project>{9260e307-12ee-4622-8cc0-51076348f88f}</Project>
      <Name>Ifs.Uma.Utility</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="EntityQueryData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="EntityQuerySchema.json" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\CommonExecutionSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\EntityProceduresSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteChangeSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteCountSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteCreateSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteErrorSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteFetchSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteForSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteProcSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteSuperSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteLogSchema.json" />
    <EmbeddedResource Include="Cache\CachePreparerSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\PrepareCacheSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\OnlineDisallowedSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ListTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\SystemCallSchema.json" />
    <EmbeddedResource Include="EntityDataSourceSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\DataTime\DataTimeCallTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\DataTime\DataTimeAddTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\DataTime\DataTimeDifferenceTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\DataTime\DataTimeWhereTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteWhileSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\Convert\ConvertToNumberTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\Convert\ConvertToIntegerTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\Convert\ConvertToBooleanTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\Convert\ConvertToTimestampTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\Convert\ConvertToStringTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringToLowerTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringLengthTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringTrimTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringTrimStartTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringTrimEndTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringToUpperTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringLikeTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringRegexLikeTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringReplaceTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringRegexReplaceTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringRegexCountTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringRegexSubStringTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\Security\SecurityEntityTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\Attachment\AttachmentConnectTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringWhereTestsSchema.json" />
    <EmbeddedResource Include="Attachments\DocumentHandlerTestsSchema.json" />
    <EmbeddedResource Include="Attachments\MediaHandlerTestsSchema.json" />
    <EmbeddedResource Include="Database\QuerySchema.json" />
    <EmbeddedResource Include="OfflineQuerySchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringTokenizeTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\ExecuteCopyCustomFieldsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\SynchronizationEndedEventSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\Sync\TransactionSessionTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringSubStringLengthTestsSchema.json" />
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringIndexTestsSchema.json" />
    <None Include="keyfile.snk" />
    <EmbeddedResource Include="KeyMapping\KeyMappingSchema.json" />
    <EmbeddedResource Include="ObjPrimaryKeySchema.json" />
    <EmbeddedResource Include="Messages\DataChangeMessageInAdvancedSchema.json" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\Procedures\CustomerData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.2\analyzers\dotnet\cs\StyleCop.Analyzers.CodeFixes.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.2\analyzers\dotnet\cs\StyleCop.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{82A7F48D-3B50-4B1E-B82E-3ADA8210C358}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\Procedures\Functions\DataTime\DataTimeWhereTestsData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\Procedures\Functions\Security\SecurityEntityTestsData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\Procedures\Functions\Attachment\AttachmentConnectTestsData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\Procedures\Functions\String\StringWhereTestsData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="EntityDataSourceData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Attachments\DocumentHandlerTestsData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Attachments\MediaHandlerTestsData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="OfflineQueryData.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\Procedures\ExecuteCopyCustomFieldsData.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Messages\DataChangeMessageInAdvancedData.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\Procedures\ListTestsData.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Execution\Procedures\Functions\Sync\TransactionSessionTestsData.xml" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\NUnit3TestAdapter.3.10.0\build\net35\NUnit3TestAdapter.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit3TestAdapter.3.10.0\build\net35\NUnit3TestAdapter.props'))" />
    <Error Condition="!Exists('..\packages\NUnit.3.10.1\build\NUnit.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit.3.10.1\build\NUnit.props'))" />
    <Error Condition="!Exists('..\packages\SQLitePCLRaw.lib.sqlcipher.linux.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.linux.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SQLitePCLRaw.lib.sqlcipher.linux.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.linux.targets'))" />
    <Error Condition="!Exists('..\packages\SQLitePCLRaw.lib.sqlcipher.osx.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.osx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SQLitePCLRaw.lib.sqlcipher.osx.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.osx.targets'))" />
    <Error Condition="!Exists('..\packages\SQLitePCLRaw.lib.sqlcipher.windows.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.windows.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SQLitePCLRaw.lib.sqlcipher.windows.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.windows.targets'))" />
  </Target>
  <Import Project="..\packages\SQLitePCLRaw.lib.sqlcipher.linux.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.linux.targets" Condition="Exists('..\packages\SQLitePCLRaw.lib.sqlcipher.linux.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.linux.targets')" />
  <Import Project="..\packages\SQLitePCLRaw.lib.sqlcipher.osx.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.osx.targets" Condition="Exists('..\packages\SQLitePCLRaw.lib.sqlcipher.osx.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.osx.targets')" />
  <Import Project="..\packages\SQLitePCLRaw.lib.sqlcipher.windows.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.windows.targets" Condition="Exists('..\packages\SQLitePCLRaw.lib.sqlcipher.windows.1.1.14\build\net35\SQLitePCLRaw.lib.sqlcipher.windows.targets')" />
</Project>