﻿using System;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = FwDataContext.FwTablePrefix + "cache_status", Class = MetaTableClass.App)]
    public class CacheStatus
    {
        [Column(PrimaryKey = true)]
        public string ProjectionName { get; set; }

        [Column(PrimaryKey = true)]
        public string EntityName { get; set; }

        [Column]
        public bool IsReady { get; set; }

        [Column]
        public DateTime? LastUpdated { get; set; }
    }
}
