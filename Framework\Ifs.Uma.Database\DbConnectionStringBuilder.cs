﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public class DbConnectionStringBuilder
    {
        public string ConnectionString
        {
            get
            {
                return _connectionString;
            }
            set
            {
                if (!string.Equals(_connectionString, value, StringComparison.Ordinal))
                {
                    IDictionary<string, string> values = Parse(value);
                    if (values == null) throw new ArgumentException("Invalid format");
                    IEnumerable<string> allKeys = AllNormalisedKeys;
                    lock (_lock)
                    {
                        if (allKeys != null)
                        {
                            foreach (string key in allKeys)
                            {
                                string newValue;
                                values.TryGetValue(key, out newValue);
                                SetMember(key, newValue);
                            }
                        }
                        _values = values;
                        _connectionString = EncodeString(values);
                    }
                }
            }
        }

        // override if DBMS does not require passwords
        public virtual string Password
        {
            get
            {
                return _password;
            }
            set
            {
                if (!string.Equals(value, _password, StringComparison.Ordinal))
                {
                    _password = value;
                    SetValue(PasswordKey, value);
                }
            }
        }

        // override if DBMS does not require passwords
        public virtual bool RequiresPassword()
        {
            return _password == null;
        }

        public string this[string keyword]
        {
            get
            {
                if (keyword == null) throw new ArgumentNullException("keyword");
                string key = NormaliseKey(keyword);
                if (key == null) throw new KeyNotFoundException(keyword);
                string value;
                GetValue(key, out value);
                return value;
            }
            set
            {
                if (keyword == null) throw new ArgumentNullException("keyword");
                string key = NormaliseKey(keyword);
                if (key == null) throw new KeyNotFoundException(keyword);
                string newValue = NormaliseValue(key, value);
                string oldValue;
                lock (_lock)
                {
                    GetValue(key, out oldValue);
                    if (!string.Equals(newValue, oldValue, StringComparison.Ordinal))
                    {
                        SetMember(key, newValue);
                        SetValue(key, newValue);
                    }
                }
            }
        }

        public bool TryGetValue(string keyword, out string value)
        {
            value = null;
            if (keyword == null) throw new ArgumentNullException("keyword");
            bool result = false;
            string key = NormaliseKey(keyword);
            if (key != null)
            {
                result = GetValue(key, out value);
            }
            return result;
        }

        public DbConnectionStringBuilder()
        {
            _password = null;
            _connectionString = string.Empty;
            _values = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            _lock = new object();
        }

        private bool GetValue(string key, out string value)
        {
            lock (_lock)
            {
                bool result = _values.TryGetValue(key, out value);
                if (!result)
                {
                    // key may have a (non-null) default value.
                    value = GetDefault(key);
                    result = value != null;
                }
                return result;
            }
        }

        private string _connectionString;
        private IDictionary<string, string> _values; // only non-default values
        private string _password;
        private readonly object _lock;

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1031:DoNotCatchGeneralExceptionTypes")]
        private IDictionary<string, string> Parse(string connectionString)
        {
            IDictionary<string, string> result = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            int start = 0;
            int l = connectionString != null ? connectionString.Length : 0;
            while (start < l && result != null)
            {
                string key;
                start = ExtractElement(connectionString, '=', start, true, out key);
                key = NormaliseKey(key);
                string value = null;
                if (key != null)
                {
                    start = ExtractValue(connectionString, start, out value);
                }
                if (key != null && value != null)
                {
                    try
                    {
                        value = NormaliseValue(key, value);
                        if (!string.IsNullOrEmpty(value))
                        {
                            result[key] = value;
                        }
                    }
                    catch (Exception)
                    {
                        result = null;
                    }
                }
                else
                {
                    result = null; // invalid connection string
                }
            }
            return result;
        }

        private static int ExtractElement(string connectionString, char quote, int start, bool trim, out string element)
        {
            element = null;
            int s = start;
            int l = connectionString.Length;
            while (s <= l)
            {
                int i = connectionString.IndexOf(quote, s);
                string t = string.Empty;
                if (i < 0)
                {
                    break;
                }
                else if (i < l && connectionString[i + 1] == quote)
                {
                    // quote is doubled (i.e. escaped)
                    t += connectionString.Substring(start, i - start + 1);
                    start = i + 2;
                    s = start;
                }
                else
                {
                    // key is complete
                    t += connectionString.Substring(start, i - start);
                    element = trim ? t.Trim() : t;
                    start = i + 1;
                    break;
                }
            }
            return start;
        }

        private static int ExtractValue(string connectionString, int start, out string value)
        {
            value = null;
            // skip over any initial white space
            int l = connectionString.Length;
            while (start < l && char.IsWhiteSpace(connectionString, start))
            {
                start++;
            }
            if (start < l)
            {
                char quote = connectionString[start];
                if (quote == '\'' || quote == '"')
                {
                    start = ExtractElement(connectionString, quote, start + 1, false, out value);
                    // value ends after whitespace and semicolon (or end of string)
                    if (value != null)
                    {
                        // skip over white space
                        while (start < l && char.IsWhiteSpace(connectionString, start))
                        {
                            start++;
                        }
                        if (start < l)
                        {
                            if (connectionString[start] == ';')
                            {
                                start++;
                            }
                            else
                            {
                                value = null; // invalid value
                            }
                        }
                    }
                }
                else
                {
                    // unquoted value is up to first semicolon (or end of string)
                    int i = connectionString.IndexOf(';', start);
                    if (i >= 0)
                    {
                        value = connectionString.Substring(start, i - start);
                        start = i + 1;
                    }
                    else
                    {
                        value = connectionString.Substring(start);
                        start = l;
                    }
                    value = value.TrimEnd();
                    // value cannot contain quote characters (it should be quoted if so)
                    if (value.IndexOf('\'') >= 0 || value.IndexOf('"') >= 0)
                    {
                        value = null;
                    }
                }
            }
            else
            {
                value = string.Empty;
            }
            return start;
        }

        [SuppressMessage("SonarLint", "S2068", Justification = "This is a keyword that's used in the DB connection string, not the actual password.")]
        private const string PasswordKey = "Password";

        [SuppressMessage("SonarLint", "S2068", Justification = "This is a keyword that's used in the DB connection string, not the actual password.")]
        private const string AltPasswordKey = "pwd";

        /// <summary>
        /// Call to change a value and the connection string
        /// </summary>
        /// <param name="key">Normalised key (not checked)</param>
        /// <param name="value">Valid value (null to remove)</param>
        protected void SetValue(string key, string value)
        {
            if (key == null) throw new ArgumentNullException("key");
            lock (_lock)
            {
                if (!string.IsNullOrEmpty(value))
                {
                    _values[key] = value;
                }
                else
                {
                    _values.Remove(key);
                }
                _connectionString = EncodeString(_values);
            }
        }

        private static void EncodeKey(StringBuilder sb, string key)
        {
            if (sb != null && !string.IsNullOrEmpty(key))
            {
                sb.Append(key.Replace("=", "=="));
            }
        }

        private static void EncodeValue(StringBuilder sb, string value)
        {
            if (sb != null && !string.IsNullOrEmpty(value))
            {
                bool containsDoubleQuote = value.Contains("\"");
                bool containsSingleQuote = value.Contains("'");
                if (containsDoubleQuote || containsSingleQuote || value.Contains(";"))
                {
                    if (containsDoubleQuote)
                    {
                        if (containsSingleQuote)
                        {
                            // double the double quote characters and quote using double quotes
                            sb.Append("\"");
                            sb.Append(value.Replace("\"", "\"\""));
                            sb.Append("\"");
                        }
                        else
                        {
                            // quote the string using single quotes
                            sb.Append("'");
                            sb.Append(value);
                            sb.Append("'");
                        }
                    }
                    else
                    {
                        // quote the string using double quotes
                        sb.Append("\"");
                        sb.Append(value);
                        sb.Append("\"");
                    }
                }
                else
                {
                    sb.Append(value);
                }
            }
        }

        private static string EncodeString(IEnumerable<KeyValuePair<string, string>> values)
        {
            StringBuilder sb = new StringBuilder();
            if (values != null)
            {
                foreach (KeyValuePair<string, string> kvp in values)
                {
                    if (sb.Length > 0)
                    {
                        sb.Append(" ");
                    }
                    EncodeKey(sb, kvp.Key);
                    sb.Append("=");
                    EncodeValue(sb, kvp.Value);
                    sb.Append(";");
                }
            }
            return sb.ToString();
        }

        /// <summary>
        /// Normalise keys to put in the connection string
        /// This is the place to swap alternate keys for the preferred one and to use a standard casing.
        /// </summary>
        /// <param name="key">Key to normalise</param>
        /// <returns>Normalised key or null if invalid</returns>
        protected virtual string NormaliseKey(string key)
        {
            return string.Equals(key, PasswordKey, StringComparison.OrdinalIgnoreCase) ||
                string.Equals(key, AltPasswordKey, StringComparison.OrdinalIgnoreCase) ? PasswordKey : null;
        }

        /// <summary>
        /// Normalise values to put in the connection string
        /// This is the place to check that integer values are integer and in the correct range.
        /// </summary>
        /// <param name="key">Normalised key</param>
        /// <param name="value">value to normalise</param>
        /// <returns>Normalised value or null if invalid</returns>
        protected virtual string NormaliseValue(string key, string value)
        {
            return string.Equals(key, PasswordKey, StringComparison.OrdinalIgnoreCase) ? value : null;
        }

        /// <summary>
        /// All the normalised keys recognised by this builder - if null then all keys are ok
        /// </summary>
        protected virtual IEnumerable<string> AllNormalisedKeys { get { return new string[] { PasswordKey }; } }

        /// <summary>
        /// Override to set additional members of the builder
        /// </summary>
        /// <param name="key">Normalised key</param>
        /// <param name="value">Normalised value to set (or null if we want the default)</param>
        protected virtual void SetMember(string key, string value)
        {
            if (string.Equals(key, PasswordKey, StringComparison.OrdinalIgnoreCase))
            {
                Password = value;
            }
        }

        protected virtual string GetDefault(string key)
        {
            return null;
        }

        protected static double ValidateDouble(string value, double minValue, double maxValue, double defaultValue)
        {
            if (value == null) return defaultValue;
            double result = ObjectConverter.ToDouble(value);
            if (result < minValue || result > maxValue) throw new ArgumentOutOfRangeException("value");
            return result;
        }

        protected static string NormaliseDouble(double value, double? defaultValue)
        {
            return defaultValue.HasValue && value == defaultValue.Value ? null :
                ObjectConverter.ToString(value);
        }

        protected static int ValidateInt(string value, int minValue, int maxValue, int defaultValue)
        {
            if (value == null) return defaultValue;
            int result = ObjectConverter.ToInt(value);
            if (result < minValue || result > maxValue) throw new ArgumentOutOfRangeException("value");
            return result;
        }

        protected static string NormaliseInt(int value, int? defaultValue)
        {
            return defaultValue.HasValue && value == defaultValue.Value ? null :
                ObjectConverter.ToString(value);
        }

        protected static T ValidateEnum<T>(string value, T defaultValue) where T : struct
        {
            return value == null ? defaultValue : (T)Enum.Parse(typeof(T), value, true);
        }

        protected static string NormaliseEnum<T>(T value, T? defaultValue) where T : struct
        {
            return defaultValue.HasValue && object.Equals(value, defaultValue.Value) ? null : value.ToString();
        }
    }
}
