﻿using System;
using System.Collections.Generic;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData
{
    public sealed class EntityRecord
    {
        public RemoteRow Row { get; }
        public IReadOnlyDictionary<string, RemoteRow> References { get; private set; }

        public EntityRecord(RemoteRow row, IReadOnlyDictionary<string, RemoteRow> references)
        {
            Row = row ?? throw new ArgumentNullException(nameof(row));
            References = references;
        }

        internal void UpdateReference(string refName, RemoteRow row)
        {
            Dictionary<string, RemoteRow> references = new Dictionary<string, RemoteRow>();

            if (References != null)
            {
                foreach (KeyValuePair<string, RemoteRow> item in References)
                {
                    references[item.Key] = item.Value;
                }
            }

            references[refName] = row;
            References = references;
        }

        public object this[AttributePathInfo attribute]
        {
            get
            {
                if (attribute.RefName == null)
                {
                    return Row[attribute.AttributeName];
                }
                
                if (References != null && References.TryGetValue(attribute.RefName, out RemoteRow refRow))
                {
                    return refRow?[attribute.AttributeName];
                }
                
                return null;
            }
        }

        public object this[AttributePath attribute]
        {
            get
            {
                if (attribute.RefName == null)
                {
                    return Row[attribute.AttributeName];
                }

                if (References != null && References.TryGetValue(attribute.RefName, out RemoteRow refRow))
                {
                    return refRow?[attribute.AttributeName];
                }

                return null;
            }
        }
    }
}
