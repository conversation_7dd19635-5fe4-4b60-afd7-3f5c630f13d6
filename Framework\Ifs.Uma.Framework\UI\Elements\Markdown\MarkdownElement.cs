﻿using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Utility;
using Markdig;

namespace Ifs.Uma.Framework.UI.Elements.Markdown
{
    public sealed class MarkdownElement : MarkdownElementBase
    {
        /// <summary>
        /// The server replaces standard comma with raised comma in table data to not conflict with formatting.
        /// </summary>
        private const char RaisedComma = '⸴';

        protected override BindingType BindingPropertyType => BindingType.Reference;

        private readonly IExpressionRunner _expressionRunner;

        public MarkdownElement(IExpressionRunner expressionRunner)
        {
            _expressionRunner = expressionRunner;
        }

        protected override bool OnLoad()
        {
            return !string.IsNullOrWhiteSpace(Content?.MarkdownText?.Text);
        }

        protected override bool CalculateIsVisible()
        {
            return RunCheck(Content?.MarkdownText?.OfflineVisible ?? Content?.MarkdownText?.Visible, true);
        }

        protected override void OnDataChanged()
        {
            base.OnDataChanged();

            Markdown.Text = InterpolateString(Content?.MarkdownText?.Text);

            string emphasis = _expressionRunner.GetEmphasis(Content?.MarkdownText?.OfflineEmphasis ?? Content?.MarkdownText?.Emphasis, ViewData);
            Markdown.Color = UmaColor.FromEmphasis(emphasis);
        }

        public static string GetHtmlText(string mdText)
        {
            if (string.IsNullOrEmpty(mdText))
            {
                return null;
            }

            MarkdownPipeline pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
            return Markdig.Markdown.ToHtml(mdText, pipeline).Replace(RaisedComma, ',');
        }
    }
}
