﻿using Ifs.Uma.Data;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = FwDataContext.FwTablePrefix + "tfa_registry_data", System = true)]
    public class TfaRegistryData 
    {
        [Column]
        public HashMethod Hash { get; set; }

        [Column]
        public int Step { get; set; }

        [Column]
        public int Digits { get; set; }

        [Column]
        public string Pin { get; set; }

        [Column(PrimaryKey = true)]
        public string Secret { get; set; }
    }
}
