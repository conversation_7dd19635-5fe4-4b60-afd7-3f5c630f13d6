using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection;
using NodaTime;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class ApiResolveCsvExpression : IfsApiExpression
    {
        public override IfsApiMethodName ApiMethodName => IfsApiMethodName.Resolvecsv;

        public override IfsApiMethodHandleType ApiMethodHandleType => IfsApiMethodHandleType.Date;

        protected override MethodInfo LogicMethodInfo => typeof(ApiResolveCsvExpression).GetTypeInfo().GetDeclaredMethod(nameof(ResolveCsv));

        public ApiResolveCsvExpression(List<Expression> expressions)
        {
            Parameters = expressions;
        }

        private static object ResolveCsv(List<DynamicValue> parameters)
        {
            object date;
            ContextSubstitutionVariable contextSubstitutionVariable = new ContextSubstitutionVariable();

            if (!contextSubstitutionVariable.GetCsV(parameters[0], out date))
            {                
                throw new ArgumentException("Invalid csv value");
            }

            return date;
        }
    }
}
