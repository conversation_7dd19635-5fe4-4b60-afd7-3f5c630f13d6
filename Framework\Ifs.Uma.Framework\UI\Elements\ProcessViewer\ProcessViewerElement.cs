using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Data;
using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Ifs.Uma.Framework.UI.Elements.Groups;
using System;

namespace Ifs.Uma.Framework.UI.Elements.ProcessViewer
{
    public class ProcessViewerElement : ElementBase
    {
        public const string Current = "CURRENT";
        public const string Complete = "COMPLETE";
        private readonly IMetadata _metadata;
        public IMetadata Metadata { get => _metadata; }
        private readonly IDataHandler _dataHandler;
        public IDataHandler DataHandler { get => _dataHandler; }
        private readonly ILogger _logger;
        public ILogger Logger { get => _logger; }
        public ArrayDataSource ArrayDataSource { get; set; }
        public CpiArray Array { get; set; }
        public PageData ContentPageData { get; set; }
        public int StageIndex { get; set; }
        public int ActiveStage { get; set; }

        public List<KeyValuePair<int, string>> StageNames { get; set; }
        public List<KeyValuePair<int, string>> StageStatuses { get; set; }

        public List<EntityRecord> Records { get; set; }
        public List<ListElement> Lists { get; set; }
        public List<GroupElement> Groups { get; set; }
        public IElementCreator ElementCreator { get; set; }
        public IProcessViewerElement NativeProcessViewerElement { get; set; }
        public string BindingProperty { get; set; }
        protected override BindingType BindingPropertyType => BindingType.Reference;

        private bool _dataLoaded = true;
        public bool DataLoaded
        {
            get => _dataLoaded;
            set => SetProperty(ref _dataLoaded, value);
        }

        public CpiProcessViewer ProcessViewer { get; set; }

        public ProcessViewerElement(IMetadata metadata, ILogger logger, IDataHandler dataHandler)
        {
            _metadata = metadata;
            _logger = logger;
            _dataHandler = dataHandler;
        }

        protected override bool OnInitialize()
        {
            ProcessViewer = _metadata.FindProcessViewer(ProjectionName, Content.ProcessViewer);
            Records = new List<EntityRecord>();
            StageNames = new List<KeyValuePair<int, string>>();
            StageStatuses = new List<KeyValuePair<int, string>>();
            Groups = new List<GroupElement>();
            Lists = new List<ListElement>();

            return ProcessViewer != null;
        }

        protected override bool OnLoad()
        {
            return true;
        }

        public void AddContent(CpiElementContent content)
        {
            ProcessViewer.Content = content.Content;
            BindingProperty = content.Binding.Property;
        }

        public async Task LoadData()
        {
            DataLoaded = false;
            
            if (Array != null)
            {
                HashSet<string> selectAttributes = new HashSet<string>();
                foreach (KeyValuePair<string, string> item in Array.Mapping)
                {
                    selectAttributes.Add(item.Value);
                }

                EntityDataSource datasource = EntityDataSource.FromEntity(PageData.DataSource.Metadata, PageData.DataSource.ProjectionName, PageData.DataSource.EntityName);
                EntityQuery query = new EntityQuery(datasource)
                {
                    SelectAttributes = selectAttributes
                };
                PageData.Filter?.Apply(query);

                RecordData recordData = new RecordData(_logger, _metadata, _dataHandler);
                EntityQueryResult results = await _dataHandler.GetRecordsAsync(query, CancellationToken.None);
                recordData.LoadRecord(ProjectionName, results.Records.FirstOrDefault());
                await recordData.LoadRecordAsync(query, false);

                if (PageData.DataSource.SelectArray(PageData.DefaultViewData.Record.GetRemoteRow(), BindingProperty) is ArrayDataSource source)
                {
                    ArrayDataSource = source;
                }
            }

            if (ArrayDataSource != null)
            {
                EntityQuery query = new EntityQuery(ArrayDataSource);
                EntityQueryResult result = await _dataHandler.GetRecordsAsync(query, CancellationToken.None);
                Records.Clear();

                foreach (EntityRecord item in result.Records)
                {
                    Records.Add(item);

                    RecordData recordData = new RecordData(_logger, _metadata, _dataHandler);
                    PageData pageData = new PageData(recordData);
                    pageData.DefaultViewData.Record.LoadRecord(ProjectionName, item);
                    string currentState = ExpressionRunner.InterpolateString(ProcessViewer.ActiveStage, pageData.DefaultViewData.Record);

                    if (string.Equals(currentState, Current, StringComparison.OrdinalIgnoreCase))
                    {
                        ContentPageData = pageData;
                    }
                }
            }

            DataLoaded = true;
        }

        public Task LoadStage(int stage)
        {
            int counter = 0;

            foreach (EntityRecord item in Records.Where(x => x.Row != null))
            {
                if (counter == stage)
                {
                    RecordData recordData = new RecordData(_logger, _metadata, _dataHandler);
                    PageData pageData = new PageData(recordData);
                    pageData.DefaultViewData.Record.LoadRecord(ProjectionName, item);
                    ContentPageData = pageData;

                    for (int i = 0; i < Groups.Count; i++)
                    {
                        Groups[i].PageData = ContentPageData;
                    }

                    for (int i = 0; i < Lists.Count; i++)
                    {
                        Lists[i].PageData = ContentPageData;
                    }
                }
                counter++;
            }

            return Task.CompletedTask;
        }

        public Task GetStages()
        {
            int counter = 0;
            StageNames.Clear();
            StageStatuses.Clear();

            foreach (EntityRecord item in Records.Where(x => x.Row != null))
            {
                RecordData recordData = new RecordData(_logger, _metadata, _dataHandler);
                PageData pageData = new PageData(recordData);
                pageData.DefaultViewData.Record.LoadRecord(ProjectionName, item);
                string currentState = ExpressionRunner.InterpolateString(ProcessViewer.ActiveStage, pageData.DefaultViewData.Record);
                string stageLabel = ExpressionRunner.InterpolateString(ProcessViewer.Label, pageData.DefaultViewData.Record);

                StageNames.Add(new KeyValuePair<int, string>(counter, stageLabel));
                StageStatuses.Add(new KeyValuePair<int, string>(counter, currentState));

                if (string.Equals(currentState, Current, StringComparison.OrdinalIgnoreCase))
                {
                    StageIndex = counter;
                    ActiveStage = counter;
                }
                counter++;
            }

            return Task.CompletedTask;
        }

        protected override void OnStoredDataChanged(DataChangeSet changeSet)
        {
            base.OnStoredDataChanged(changeSet);
            NativeProcessViewerElement?.LoadAllContent();
        }
    }
}
