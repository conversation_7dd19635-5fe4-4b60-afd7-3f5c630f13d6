﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using System.Collections.Generic;
using Ifs.Uma.UI.Observables;
using System.Linq;

namespace Ifs.Uma.Framework.UI.Pages
{
    public class PageCommandBlock : CommandBlock
    {
        private readonly IMetadata _metadata;

        public PageCommandItem Create { get; }
        public PageCommandItem Delete { get; }

        public PageCommandBlock(UpdatingState parentUpdatingState, IMetadata metadata, ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, Func<Task> onCreate, Func<Task> onDelete) 
            : base(parentUpdatingState, metadata, commandExecutor, expressionRunner)
        {
            _metadata = metadata;

            Create = new PageCommandItem(commandExecutor, expressionRunner, PageCommand.Create, onCreate);
            Delete = new PageCommandItem(commandExecutor, expressionRunner, PageCommand.Delete, onDelete);
        }

        public void Load(IEnumerable<CpiCommandGroup> commandGroups, CpiCrudActions crudActions, bool isCommandPage, string projectionName, string pageName)
        {
            using (CommandGroups.DeferRefresh())
            {
                CommandGroups.Clear();
                Commands.Clear();

                CpiCommand createCommand = GetPageCreateCommand(projectionName, pageName, commandGroups);
                string[] commandsToIgnore = createCommand == null ? null : new string[] { createCommand.Name };

                AddCommands(projectionName, commandGroups, false, isCommandPage, true, commandsToIgnore);

                Create.CrudAction = crudActions?.New;
                Create.CreateCommand = createCommand;
                Create.ProjectionName = projectionName;
                Delete.CrudAction = crudActions?.Delete;
                Delete.ProjectionName = projectionName;

                AddCommand(Create);
                AddCommand(Delete);

                UpdateStates();
            }
        }

        private CpiCommand GetPageCreateCommand(string projectionName, string pageName, IEnumerable<CpiCommandGroup> commandGroups)
        {
            string createCommandName = CommandExecutor.GetPageCreateCommandName(pageName);
            bool hasCreateCommand = commandGroups != null && commandGroups.SelectMany(x => x.CommandNames).Any(x => x == createCommandName);
            CpiCommand createCommand = hasCreateCommand ? _metadata.FindCommand(projectionName, createCommandName) : null;
            return createCommand?.Selection == CpiCommandSelection.Global ? createCommand : null;
        }
    }
}
