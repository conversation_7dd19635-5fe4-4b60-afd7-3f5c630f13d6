﻿using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Cloud.Client.Comm
{
    // this class is based on https://stackoverflow.com/a/18925296
    internal class HttpLoggingHandler : DelegatingHandler
    {
        internal HttpLoggingHandler(HttpMessageHandler innerHandler)
        : base(innerHandler)
        {
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            System.Guid guid = System.Guid.NewGuid();
            string requestId = guid.ToString();

            if (!string.IsNullOrEmpty(requestId) && requestId.Length > 24)
                requestId = requestId.Substring(24).ToUpper(); // take the last group only - we don't need guaranteed uniqueness here

            Logger.Current.Log("BEGIN_REQUEST " + requestId + " -->" + request.ToString(), MessageType.Trace);
            
            if (request.Content != null)
            {
                Logger.Current.Log(await request.Content.ReadAsStringAsync(), MessageType.Trace);
            }

            Logger.Current.Log("<-- END_REQUEST " + requestId, MessageType.Trace);
            HttpResponseMessage response = await base.SendAsync(request, cancellationToken);

            Logger.Current.Log("BEGIN_RESPONSE " + requestId + " -->" + response.ToString(), MessageType.Trace);
            
            if (response.Content != null)
            {
                // try to get some meaningful content from response, and truncate to 1005 characters max
                string responseContent = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrEmpty(responseContent))
                    responseContent = response.Content.Headers.ToString();
                if (string.IsNullOrEmpty(responseContent))
                    responseContent = response.Content.ToString();
                if (string.IsNullOrEmpty(responseContent))
                    responseContent = response.ToString();
                if (string.IsNullOrEmpty(responseContent))
                    responseContent = "<EMPTY>";

                // take 1000 characters at most with <...> separating the first 900 and the last 100
                if (responseContent.Length > 1000)
                {
                    string head = responseContent.Substring(0, 900);
                    string tail = responseContent.Substring(responseContent.Length - 100, 100);
                    responseContent = head + "<...>" + tail;
                } 

                Logger.Current.Log(responseContent, MessageType.Trace);
            }

            Logger.Current.Log("<-- END_RESPONSE " + requestId, MessageType.Trace);

            return response;
        }
    }
}
