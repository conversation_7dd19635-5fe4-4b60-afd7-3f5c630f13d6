﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.Sync
{
    [TestFixture]
    public class TransactionSessionTests : ProcedureTest
    {
        private const string TstCustomerEntityName = "TstCustomer";
        private static readonly string TstCustomerTableName = RemoteNaming.ToTableName(TstCustomerEntityName);
        private const string CustomerNameAttributeName = "CustomerName";

        private IProcedureExecutor _executor;

        [Test]
        public async Task Sync_InitiateTransactionSession()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["SessionId"] = "TEST_SESSION";

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result?.Value as bool?);

            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result.Failed);

            parameters["SessionId"] = "TEST_SESSION_2";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result.Failed);
        }

        [Test]
        public async Task Sync_FinalizeTransactionSession()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["SessionId"] = "TEST_SESSION";

            FwDataContext ctx = CreateDataContext();

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result?.Value as bool?);

            FndTransactionSession session = ctx.TransactionSessions.FirstOrDefault();
            Assert.IsTrue(session.IsOpen);

            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestFinalizeTransactionSession", null);
            Assert.IsTrue(result?.Value as bool?);

            session = ctx.TransactionSessions.FirstOrDefault();
            Assert.IsFalse(session.IsOpen);

            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestFinalizeTransactionSession", null);
            Assert.IsFalse(result?.Value as bool?);
        }

        [Test]
        public async Task Sync_TransactionSessionExists()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["SessionId"] = "TEST_SESSION";

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result?.Value as bool?);

            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestTransactionSessionExists", parameters);
            Assert.IsTrue(result?.Value as bool?);

            parameters["SessionId"] = "DOES_NOT_EXIST";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestTransactionSessionExists", parameters);
            Assert.IsFalse(result?.Value as bool?);
        }

        [Test]
        public async Task Sync_ClearTransactionSession()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["SessionId"] = "TEST_SESSION";

            FwDataContext ctx = CreateDataContext();

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result?.Value as bool?);

            Assert.IsTrue(ctx.TransactionSessions.Any(x => x.IsOpen == true));

            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestFinalizeTransactionSession", null);
            Assert.IsTrue(result?.Value as bool?);

            parameters["SessionId"] = "TEST_SESSION_2";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result?.Value as bool?);

            Assert.AreEqual(2, ctx.TransactionSessions.Count());

            parameters["SessionId"] = "DOES_NOT_EXIST";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestClearTransactionSession", parameters);
            Assert.IsFalse(result?.Value as bool?);

            bool hasTransitions = ctx.TransitionRows.Any(x => x.SessionId == "TEST_SESSION");

            parameters["SessionId"] = "TEST_SESSION";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestClearTransactionSession", parameters);

            // Above returns false if no transitions, so assert accordingly
            bool clearProcResult = result?.Value != null && (bool)result.Value;
            Assert.AreEqual(hasTransitions, clearProcResult);

            Assert.AreEqual(1, ctx.TransactionSessions.Count());
        }

        [Test]
        public async Task Sync_ClearAllTransactionSessions()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["SessionId"] = "TEST_SESSION";

            FwDataContext ctx = CreateDataContext();

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result?.Value as bool?);

            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestFinalizeTransactionSession", null);
            Assert.IsTrue(result?.Value as bool?);

            parameters["SessionId"] = "TEST_SESSION_2";
            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result?.Value as bool?);

            Assert.AreEqual(2, ctx.TransactionSessions.Count());

            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestClearAllTransactionSessions", null);
            Assert.IsTrue(result?.Value as bool?);

            Assert.IsFalse(ctx.TransactionSessions.Any());
            Assert.IsFalse(ctx.TransitionRows.Any(x => x.SessionId != null));
        }

        [Test]
        public async Task Sync_CheckTransactionSessionId()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["SessionId"] = "TEST_SESSION";

            FwDataContext ctx = CreateDataContext();

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestInitiateTransactionSession", parameters);
            Assert.IsTrue(result?.Value as bool?);

            parameters = new Dictionary<string, object>();
            parameters["Input"] = "Foo";
            result = await _executor.CallActionAsync(TestOfflineProjection, "SetCustomerName", parameters);
            Assert.IsFalse(result?.Failed);

            TransitionRow transition = ctx.TransitionRows.FirstOrDefault();
            Assert.AreEqual("TEST_SESSION", transition.SessionId);

            ctx.TransitionRows.DeleteAllOnSubmit(ctx.TransitionRows.ToArray());
            ctx.SubmitChanges(false);

            result = await _executor.CallFunctionAsync(TestOfflineProjection, "TestFinalizeTransactionSession", null);
            Assert.IsTrue(result?.Value as bool?);

            parameters["Input"] = "Bar";
            result = await _executor.CallActionAsync(TestOfflineProjection, "SetCustomerName", parameters);
            Assert.IsFalse(result?.Failed);

            transition = ctx.TransitionRows.FirstOrDefault();
            Assert.AreEqual(string.Empty, transition.SessionId);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.Sync.TransactionSessionTestsSchema", "Execution.Procedures.Functions.Sync.TransactionSessionTestsData");
            _executor = Resolve<IProcedureExecutor>();
        }

        protected override void OnErrorLogged(string message)
        {
            if (!message.Contains("ProcedureException"))
            {
                base.OnErrorLogged(message);
            }
        }
    }
}
