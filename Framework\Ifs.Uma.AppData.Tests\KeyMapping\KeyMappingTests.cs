﻿using System.Collections.Generic;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using NUnit.Framework;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Messages;

namespace Ifs.Uma.AppData.Tests.KeyMapping
{
    [TestFixture]
    public class KeyMappingTests : FrameworkTest
    {
        [Test]
        public void MapByPrimaryKey()
        {
            IMetadata metadata = Resolve<IMetadata>();

            ClientKeysMapper mapper = new ClientKeysMapper(null);
            mapper.Load(metadata.MetaModel, null);

            ClientKeysMap a = new ClientKeysMap();
            a.TableName = "tst_company";
            a.ServerKeys = "HELLO";
            a.ClientKeys = "HEY_BY_PK";
            mapper.RegisterKeys(a);

            MessageTableData newData = new MessageTableData();
            newData.TableName = "tst_company";
            newData.RowData = new MessageRowData();
            Dictionary<string, object> newDataValues = new Dictionary<string, object>();
            newDataValues["company"] = "HELLO";
            newData.RowData.ColumnData = newDataValues;

            MessageTableData result = mapper.MapServerToClientKeys(newData);

            string mappedId = (string)result.RowData.ColumnData["company"];
            Assert.AreEqual("HEY_BY_PK", mappedId);
        }

        [Test]
        public void MapByReference()
        {
            IMetadata metadata = Resolve<IMetadata>();

            ClientKeysMapper mapper = new ClientKeysMapper(null);
            mapper.Load(metadata.MetaModel, null);

            ClientKeysMap a = new ClientKeysMap();
            a.TableName = "tst_company";
            a.ServerKeys = "HELLO";
            a.ClientKeys = "HEY_BY_REF";
            mapper.RegisterKeys(a);

            MessageTableData newData = new MessageTableData();
            newData.TableName = "tst_customer";
            newData.RowData = new MessageRowData();
            Dictionary<string, object> newDataValues = new Dictionary<string, object>();
            newDataValues["company_no"] = "HELLO";
            newData.RowData.ColumnData = newDataValues;

            MessageTableData result = mapper.MapServerToClientKeys(newData);

            string mappedId = (string)result.RowData.ColumnData["company_no"];
            Assert.AreEqual("HEY_BY_REF", mappedId);
        }

        [Test]
        public void MapByArray()
        {
            IMetadata metadata = Resolve<IMetadata>();

            ClientKeysMapper mapper = new ClientKeysMapper(null);
            mapper.Load(metadata.MetaModel, null);

            ClientKeysMap a = new ClientKeysMap();
            a.TableName = "tst_company";
            a.ServerKeys = "HELLO";
            a.ClientKeys = "HEY_BY_ARRAY";
            mapper.RegisterKeys(a);

            MessageTableData newData = new MessageTableData();
            newData.TableName = "tst_site";
            newData.RowData = new MessageRowData();
            Dictionary<string, object> newDataValues = new Dictionary<string, object>();
            newDataValues["company_no"] = "HELLO";
            newData.RowData.ColumnData = newDataValues;

            MessageTableData result = mapper.MapServerToClientKeys(newData);

            string mappedId = (string)result.RowData.ColumnData["company_no"];
            Assert.AreEqual("HEY_BY_ARRAY", mappedId);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("KeyMapping.KeyMappingSchema", null);
        }
    }
}
