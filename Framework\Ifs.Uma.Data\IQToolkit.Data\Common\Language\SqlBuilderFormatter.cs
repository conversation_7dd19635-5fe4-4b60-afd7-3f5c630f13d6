﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace IQToolkit.Data.Common.Language
{
    internal class SqlBuilderFormatter : DbExpressionVisitor
    {
        private const char IndentChar = ' ';

        private int _indent = 2;
        private int _depth;
        private SqlBuilder _sqlBuilder;
        private StringBuilder _sb;
        private Dictionary<TableAlias, string> _aliases;
        private bool _isNested;
        private bool _forDebug;

        private SqlBuilderFormatter(SqlBuilder sqlBuilder, bool forDebug)
        {
            _sqlBuilder = sqlBuilder;
            _sb = new StringBuilder();
            _aliases = new Dictionary<TableAlias, string>();
            _forDebug = forDebug;
        }

        public static string Format(SqlBuilder sqlBuilder, Expression expression)
        {
            return Format(sqlBuilder, expression, false);
        }

        public static string Format(SqlBuilder sqlBuilder, Expression expression, bool forDebug)
        {
            var formatter = new SqlBuilderFormatter(sqlBuilder, forDebug);
            formatter.Visit(expression);
            return formatter.ToString();
        }

        #region String Builder Handling

        public override string ToString()
        {
            return _sb.ToString();
        }

        protected enum Indentation
        {
            Same,
            Inner,
            Outer
        }

        protected void Write(object value)
        {
            _sb.Append(value);
        }

        protected void WriteLine(Indentation style)
        {
            Indent(style);

            if (!_forDebug)
            {
                return;
            }

            _sb.AppendLine();            

            for (int i = 0, n = _depth * _indent; i < n; i++)
            {
                Write(IndentChar);
            }
        }

        protected void Indent(Indentation style)
        {
            if (style == Indentation.Inner)
            {
                _depth++;
            }
            else if (style == Indentation.Outer)
            {
                _depth--;
                System.Diagnostics.Debug.Assert(_depth >= 0);
            }
        }

        #endregion

        #region Write Helpers

        private void WriteParameterName(string name)
        {
            Write(_sqlBuilder.ParameterProlog + name);
        }

        private void WriteVariableName(string name)
        {
            WriteParameterName(name);
        }

        private void WriteAsAliasName(string aliasName)
        {
            Write(_sqlBuilder.AliasSeparator);
            WriteAliasName(aliasName);
        }

        private void WriteAliasName(string aliasName)
        {
            Write(aliasName);
        }

        private string GetAliasName(TableAlias alias)
        {
            if (alias == null)
            {
                if (_forDebug) return "A?";
                throw new ArgumentNullException("alias");
            }
            string name;
            if (!_aliases.TryGetValue(alias, out name))
            {
                name = alias.ToString() + "?";
                _aliases.Add(alias, name);
            }
            return name;
        }

        private void AddAlias(TableAlias alias)
        {
            if (alias == null) return;
            string name;
            if (!_aliases.TryGetValue(alias, out name))
            {
                name = "t" + _aliases.Count;
                _aliases.Add(alias, name);
            }
        }

        private void AddAliases(Expression expr)
        {
            AliasedExpression ax = expr as AliasedExpression;
            if (ax != null)
            {
                AddAlias(ax.Alias);
            }
            else
            {
                JoinExpression jx = expr as JoinExpression;
                if (jx != null)
                {
                    AddAliases(jx.Left);
                    AddAliases(jx.Right);
                }
            }
        }

        private void WriteAsColumnName(string columnName)
        {
            Write(_sqlBuilder.AsClause);
            WriteColumnName(columnName);
        }

        private void WriteColumnName(string columnName)
        {
            Write(_sqlBuilder.DecorateName(columnName));
        }

        private void WriteTableName(string tableName)
        {
            Write(_sqlBuilder.DecorateTableName(tableName));
        }

        private void WriteColumns(ReadOnlyCollection<ColumnDeclaration> columns)
        {
            if (columns == null && !_forDebug) throw new ArgumentNullException("columns");

            if (columns != null && columns.Count > 0)
            {
                for (int i = 0, n = columns.Count; i < n; i++)
                {
                    ColumnDeclaration column = columns[i];
                    if (i > 0)
                    {
                        Write(_sqlBuilder.CommaSeparator);
                    }

                    ColumnExpression c = VisitValue(column.Expression) as ColumnExpression;

                    if (!string.IsNullOrEmpty(column.Name) && (c == null || c.Name != column.Name))
                    {
                        WriteAsColumnName(column.Name);
                    }
                }
            }
            else
            {
                Write(_sqlBuilder.EmptyColumnName);
                if (_isNested)
                {
                    WriteAsColumnName("tmp");
                }
            }
        }

        private void WriteConstant(object value)
        {
            if (value == null || _sqlBuilder.ValueIsNull(value.GetType(), value))
            {
                Write(_sqlBuilder.NullLiteral);
                return;
            }
            if (value.GetType().GetTypeInfo().IsEnum)
            {
                if (_forDebug)
                {
                    Write("?Enum:");
                    Write(value.GetType().Name);
                    Write(".");
                    Write(value.ToString());
                    Write("?");
                    return;
                }
                throw new NotSupportedException("Unsupported enum constant: " + value.ToString());
            }
            switch (TypeHelper.GetTypeCode(value.GetType()))
            {
                case TypeCode.Boolean:
                    Write((bool)value ? _sqlBuilder.TrueLiteral : _sqlBuilder.FalseLiteral);
                    break;
                case TypeCode.Int16:
                    Write(ObjectConverter.ToString((short)value));
                    break;
                case TypeCode.Int32:
                    Write(ObjectConverter.ToString((int)value));
                    break;
                case TypeCode.Int64:
                    Write(ObjectConverter.ToString((long)value));
                    break;
                case TypeCode.Double:
                    string str = ObjectConverter.ToString((double)value);
                    Write(str);
                    if (!str.Contains("."))
                    {
                        Write(".0");
                    }
                    break;
                default:
                    if (_forDebug)
                    {
                        Write("?Constant:");
                        Write(value.ToString());
                        Write("?");
                        break;
                    }
                    throw new NotSupportedException("Unsupported constant: " + value.ToString());
            }
        }

        #endregion

        public override Expression Visit(Expression node)
        {
            if (node == null) return null;

            // check for supported node types first 
            // non-supported ones should not be visited (as they would produce bad SQL)
            switch (node.NodeType)
            {
                case ExpressionType.Negate:
                case ExpressionType.NegateChecked:
                case ExpressionType.Not:
                case ExpressionType.Convert:
                case ExpressionType.ConvertChecked:
                case ExpressionType.UnaryPlus:
                case ExpressionType.Add:
                case ExpressionType.AddChecked:
                case ExpressionType.Subtract:
                case ExpressionType.SubtractChecked:
                case ExpressionType.Multiply:
                case ExpressionType.MultiplyChecked:
                case ExpressionType.Divide:
                case ExpressionType.Modulo:
                case ExpressionType.And:
                case ExpressionType.AndAlso:
                case ExpressionType.Or:
                case ExpressionType.OrElse:
                case ExpressionType.LessThan:
                case ExpressionType.LessThanOrEqual:
                case ExpressionType.GreaterThan:
                case ExpressionType.GreaterThanOrEqual:
                case ExpressionType.Equal:
                case ExpressionType.NotEqual:
                case ExpressionType.Coalesce:
                case ExpressionType.RightShift:
                case ExpressionType.LeftShift:
                case ExpressionType.ExclusiveOr:
                case ExpressionType.Power:
                case ExpressionType.Conditional:
                case ExpressionType.Constant:
                case ExpressionType.MemberAccess:
                case ExpressionType.Call:
                case ExpressionType.New:
                    return base.Visit(node);

                case ExpressionType.Extension:
                    switch (node.GetDbNodeType())
                    {
                        case DbExpressionType.Table:
                        case DbExpressionType.Column:
                        case DbExpressionType.Select:
                        case DbExpressionType.Join:
                        case DbExpressionType.Aggregate:
                        case DbExpressionType.Scalar:
                        case DbExpressionType.Exists:
                        case DbExpressionType.In:
                        case DbExpressionType.AggregateSubquery:
                        case DbExpressionType.IsNull:
                        case DbExpressionType.Between:
                        case DbExpressionType.RowCount:
                        case DbExpressionType.Projection:
                        case DbExpressionType.NamedValue:
                        case DbExpressionType.Block:
                        case DbExpressionType.If:
                        case DbExpressionType.Declaration:
                        case DbExpressionType.Variable:
                        case DbExpressionType.Function:
                            return base.Visit(node);
                    }
                    goto default;

                case ExpressionType.ArrayLength:
                case ExpressionType.Quote:
                case ExpressionType.TypeAs:
                case ExpressionType.ArrayIndex:
                case ExpressionType.TypeIs:
                case ExpressionType.Parameter:
                case ExpressionType.Lambda:
                case ExpressionType.NewArrayInit:
                case ExpressionType.NewArrayBounds:
                case ExpressionType.Invoke:
                case ExpressionType.MemberInit:
                case ExpressionType.ListInit:
                default:
                    if (_forDebug)
                    {
                        Write("?");
                        Write(node.NodeType.ToString());
                        Write("?(");
                        node = base.Visit(node);
                        Write(")");
                        return node;
                    }
                    throw new NotSupportedException("Unsupported node type: " + node.NodeType.ToString());
            }
        }

        #region Projection Expressions

        protected override Expression VisitProjection(ProjectionExpression node)
        {
            if (node == null) return null;
            // treat these like scalar subqueries
            bool isColumn = node.Projector is ColumnExpression;
            if (isColumn || _forDebug)
            {
                if (!isColumn)
                {
                    Write("?ProjectorNotColumn?");
                }
                Write(_sqlBuilder.OpenBracket);
                WriteLine(Indentation.Inner);
                Visit(node.Select);
                Write(_sqlBuilder.CloseBracket);
                Indent(Indentation.Outer);
                return node;
            }
            throw new NotSupportedException("Non-scalar projections cannot be translated to SQL.");
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            if (node == null) return null;
            LimitPosition limitPosition = _sqlBuilder.LimitPlacement;
            AddAliases(node.From);
            if (node.IsDistinct)
            {
                Write(_sqlBuilder.SelectDistinctClause);
            }
            else
            {
                Write(_sqlBuilder.SelectClause);
            }
            if (limitPosition == LimitPosition.LimitOnlyAtStart && node.Take != null)
            {
                VisitLimitClause(node.Take);
            }
            WriteColumns(node.Columns);
            if (node.From != null)
            {
                WriteLine(Indentation.Same);
                Write(_sqlBuilder.FromClause);
                VisitSource(node.From);
            }

            if (node.Where != null)
            {
                WriteLine(Indentation.Same);
                Write(_sqlBuilder.WhereClause);
                VisitPredicate(node.Where);
            }

            if (node.GroupBy != null && node.GroupBy.Count > 0)
            {
                WriteLine(Indentation.Same);
                Write(_sqlBuilder.GroupByClause);
                VisitGroupByClause(node.GroupBy);
            }

            if (node.OrderBy != null && node.OrderBy.Count > 0)
            {
                WriteLine(Indentation.Same);
                Write(_sqlBuilder.OrderByClause);
                VisitOrderByClause(node.OrderBy);
            }

            switch (limitPosition)
            {
                case LimitPosition.LimitFirstAtEnd:
                    if (node.Take != null)
                    {
                        VisitLimitClause(node.Take);
                    }
                    if (node.Skip != null)
                    {
                        VisitOffsetClause(node.Skip);
                    }
                    break;
                case LimitPosition.OffsetFirstAtEnd:
                    if (node.Skip != null)
                    {
                        VisitOffsetClause(node.Skip);
                    }
                    if (node.Take != null)
                    {
                        VisitLimitClause(node.Take);
                    }
                    break;
            }
            return node;
        }

        protected override Expression VisitSource(Expression node)
        {
            if (node == null) return null;

            bool saveIsNested = _isNested;
            _isNested = true;
            switch (node.GetDbNodeType())
            {
                case DbExpressionType.Table:
                    TableExpression table = (TableExpression)node;
                    WriteTableName(table.Name);
                    WriteAsAliasName(GetAliasName(table.Alias));
                    break;
                case DbExpressionType.Select:
                    SelectExpression select = (SelectExpression)node;
                    Write(_sqlBuilder.OpenBracket);
                    WriteLine(Indentation.Inner);
                    Visit(select);
                    WriteLine(Indentation.Same);
                    Write(_sqlBuilder.CloseBracket);
                    WriteAsAliasName(GetAliasName(select.Alias));
                    Indent(Indentation.Outer);
                    break;
                case DbExpressionType.Join:
                    VisitJoin((JoinExpression)node);
                    break;
                default:
                    if (_forDebug)
                    {
                        Write("?Source?(");
                        base.VisitSource(node);
                        Write(")");
                        break;
                    }
                    throw new InvalidOperationException("Select source is not valid type");
            }
            _isNested = saveIsNested;
            return node;
        }

        protected virtual Expression VisitPredicate(Expression expr)
        {
            Visit(expr);
            if (!IsPredicate(expr))
            {
                Write(_sqlBuilder.NotEqualOperator);
                Write(_sqlBuilder.FalseLiteral);
            }
            return expr;
        }

        protected virtual bool IsPredicate(Expression expr)
        {
            if (expr == null) return false;
            switch (expr.NodeType)
            {
                case ExpressionType.And:
                case ExpressionType.AndAlso:
                case ExpressionType.Or:
                case ExpressionType.OrElse:
                    return IsBoolean(((BinaryExpression)expr).Type);
                case ExpressionType.Not:
                    return IsBoolean(((UnaryExpression)expr).Type);
                case ExpressionType.Equal:
                case ExpressionType.NotEqual:
                case ExpressionType.LessThan:
                case ExpressionType.LessThanOrEqual:
                case ExpressionType.GreaterThan:
                case ExpressionType.GreaterThanOrEqual:
                    return true;
                case ExpressionType.Call:
                    return IsBoolean(((MethodCallExpression)expr).Type);
                case ExpressionType.Extension:
                    switch (expr.GetDbNodeType())
                    {
                        case DbExpressionType.IsNull:
                        case DbExpressionType.Between:
                        case DbExpressionType.Exists:
                        case DbExpressionType.In:
                            return true;
                    }
                    goto default;
                default:
                    return false;
            }
        }

        protected virtual bool IsBoolean(Type type)
        {
            return type == typeof(bool) || type == typeof(bool?);
        }

        private void VisitGroupByClause(ReadOnlyCollection<Expression> groupBy)
        {
            if (groupBy == null) return;
            for (int i = 0, n = groupBy.Count; i < n; i++)
            {
                if (i > 0)
                {
                    Write(_sqlBuilder.CommaSeparator);
                }

                VisitValue(groupBy[i]);
            }
        }

        private void VisitOrderByClause(ReadOnlyCollection<OrderExpression> orderBy)
        {
            if (orderBy == null) return;
            for (int i = 0, n = orderBy.Count; i < n; i++)
            {
                OrderExpression exp = orderBy[i];
                if (i > 0)
                {
                    Write(_sqlBuilder.CommaSeparator);
                }

                VisitValue(exp.Expression);

                switch (exp.OrderType)
                {
                    case OrderType.Ascending:
                        Write(_sqlBuilder.AscendingOrder);
                        break;
                    case OrderType.Descending:
                        Write(_sqlBuilder.DescendingOrder);
                        break;
                    default:
                        break;
                }
            }
        }

        protected virtual Expression VisitValue(Expression expr)
        {
            return Visit(expr);
        }

        protected virtual void VisitOffsetClause(Expression expression)
        {
            Write(_sqlBuilder.OffsetProlog);
            Visit(expression);
            Write(_sqlBuilder.OffsetEpilog);
        }

        protected virtual void VisitLimitClause(Expression expression)
        {
            Write(_sqlBuilder.LimitProlog);
            Visit(expression);
            Write(_sqlBuilder.LimitEpilog);
        }

        #endregion

        protected override Expression VisitVariable(VariableExpression node)
        {
            if (node == null) return null;
            WriteVariableName(node.Name);
            return node;
        }

        protected override Expression VisitIsNull(IsNullExpression node)
        {
            if (node == null) return null;
            VisitValue(node.Expression);
            Write(_sqlBuilder.IsNullOperator);
            return node;
        }

        protected override Expression VisitColumn(ColumnExpression node)
        {
            if (node == null) return null;
            if (node.Alias != null)
            {
                WriteAliasName(GetAliasName(node.Alias));
                Write(_sqlBuilder.NameQualifier);
            }
            WriteColumnName(node.Name);
            return node;
        }

        protected override Expression VisitConstant(ConstantExpression node)
        {
            if (node == null) return null;
            WriteConstant(node.Value);
            return node;
        }

        protected override Expression VisitExists(ExistsExpression node)
        {
            if (node == null) return null;
            Write(_sqlBuilder.ExistsOperator);
            Write(_sqlBuilder.OpenBracket);
            WriteLine(Indentation.Inner);
            Visit(node.Select);
            WriteLine(Indentation.Same);
            Write(_sqlBuilder.CloseBracket);
            Indent(Indentation.Outer);
            return node;
        }

        protected override Expression VisitIn(InExpression node)
        {
            if (node == null) return null;
            if (node.Values != null)
            {
                if (node.Values.Count == 0)
                {
                    Write(_sqlBuilder.FalseLiteral);
                    Write(_sqlBuilder.NotEqualOperator);
                    Write(_sqlBuilder.FalseLiteral);
                }
                else
                {
                    VisitValue(node.Expression);
                    Write(_sqlBuilder.InOperator);
                    Write(_sqlBuilder.OpenBracket);
                    for (int i = 0, n = node.Values.Count; i < n; i++)
                    {
                        if (i > 0) Write(_sqlBuilder.CommaSeparator);
                        VisitValue(node.Values[i]);
                    }
                    Write(_sqlBuilder.CloseBracket);
                }
            }
            else
            {
                VisitValue(node.Expression);
                Write(_sqlBuilder.InOperator);
                Write(_sqlBuilder.OpenBracket);
                WriteLine(Indentation.Inner);
                Visit(node.Select);
                WriteLine(Indentation.Same);
                Write(_sqlBuilder.CloseBracket);
                Indent(Indentation.Outer);
            }
            return node;
        }

        protected override Expression VisitBetween(BetweenExpression node)
        {
            if (node == null) return null;
            VisitValue(node.Expression);
            Write(_sqlBuilder.BetweenOperator);
            VisitValue(node.Lower);
            Write(_sqlBuilder.AndOperand);
            VisitValue(node.Upper);
            return node;
        }

        protected override Expression VisitScalar(ScalarExpression node)
        {
            if (node == null) return null;
            Write(_sqlBuilder.OpenBracket);
            WriteLine(Indentation.Inner);
            Visit(node.Select);
            WriteLine(Indentation.Same);
            Write(_sqlBuilder.CloseBracket);
            Indent(Indentation.Outer);
            return node;
        }

        protected override Expression VisitJoin(JoinExpression node)
        {
            if (node == null) return null;
            VisitJoinLeft(node.Left);
            WriteLine(Indentation.Same);
            switch (node.Join)
            {
                case JoinType.InnerJoin:
                    Write(_sqlBuilder.InnerJoinClause);
                    break;
                case JoinType.LeftOuter:
                case JoinType.SingletonLeftOuter:
                    Write(_sqlBuilder.LeftOuterJoinClause);
                    break;
                default:
                    if (_forDebug)
                    {
                        Write(" ?");
                        Write(node.Join.ToString());
                        Write("? ");
                        break;
                    }
                    throw new NotSupportedException("Unsupported JoinType: " + node.Join.ToString());
            }
            VisitJoinRight(node.Right);
            if (node.On != null)
            {
                WriteLine(Indentation.Inner);
                Write(_sqlBuilder.OnClause);
                VisitPredicate(node.On);
                Indent(Indentation.Outer);
            }
            return node;
        }

        protected virtual Expression VisitJoinLeft(Expression node)
        {
            return VisitSource(node);
        }

        protected virtual Expression VisitJoinRight(Expression node)
        {
            return VisitSource(node);
        }

        protected override Expression VisitNamedValue(NamedValueExpression node)
        {
            if (node == null) return null;
            WriteParameterName(node.Name);
            return node;
        }

        protected override Expression VisitFunction(FunctionExpression node)
        {
            if (node == null) return null;
            Write(node.Name);
            if (node.Arguments.Count > 0)
            {
                Write(_sqlBuilder.OpenBracket);
                for (int i = 0, n = node.Arguments.Count; i < n; i++)
                {
                    if (i > 0)
                    {
                        Write(_sqlBuilder.CommaSeparator);
                    }

                    Visit(node.Arguments[i]);
                }
                Write(_sqlBuilder.CloseBracket);
            }
            return node;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        protected override Expression VisitBinary(BinaryExpression node)
        {
            if (node == null) return null;

            string op = GetOperator(node);
            Expression left = node.Left;
            Expression right = node.Right;

            Write(_sqlBuilder.OpenBracket);
            switch (node.NodeType)
            {
                case ExpressionType.And:
                case ExpressionType.AndAlso:
                case ExpressionType.Or:
                case ExpressionType.OrElse:
                    if (IsBoolean(left.Type))
                    {
                        VisitPredicate(left);
                        Write(op);
                        VisitPredicate(right);
                    }
                    else
                    {
                        VisitValue(left);
                        Write(op);
                        VisitValue(right);
                    }
                    break;
                case ExpressionType.Equal:
                    if (CheckNulls(left, right, _sqlBuilder.IsNullOperator)) break;
                    goto case ExpressionType.LessThan;
                case ExpressionType.NotEqual:
                    if (CheckNulls(left, right, _sqlBuilder.IsNotNullOperator)) break;
                    goto case ExpressionType.LessThan;
                case ExpressionType.LessThan:
                case ExpressionType.LessThanOrEqual:
                case ExpressionType.GreaterThan:
                case ExpressionType.GreaterThanOrEqual:
                    // check for special x.CompareTo(y) && type.Compare(x,y)
                    if (left.NodeType == ExpressionType.Call && right.NodeType == ExpressionType.Constant)
                    {
                        MethodCallExpression mc = (MethodCallExpression)left;
                        ConstantExpression ce = (ConstantExpression)right;
                        if (ce.Value != null && ce.Value.GetType() == typeof(int) && ((int)ce.Value) == 0)
                        {
                            if (mc.Method.Name == "CompareTo" && !mc.Method.IsStatic && mc.Arguments.Count == 1)
                            {
                                left = mc.Object;
                                right = mc.Arguments[0];
                            }
                            else if (
                                (mc.Method.DeclaringType == typeof(string) || mc.Method.DeclaringType == typeof(decimal))
                                  && mc.Method.Name == "Compare" && mc.Method.IsStatic && mc.Arguments.Count == 2)
                            {
                                left = mc.Arguments[0];
                                right = mc.Arguments[1];
                            }
                        }
                    }
                    goto case ExpressionType.Add;
                case ExpressionType.ExclusiveOr:
                    if (_sqlBuilder.SupportsXor)
                        goto case ExpressionType.Add;
                    // use equivalent (A ^ B) == ((A & ~B) | (~A & B))
                    Write(_sqlBuilder.OpenBracket);
                    VisitValue(left);
                    Write(_sqlBuilder.BitwiseAndOperator);
                    Write(_sqlBuilder.BitwiseNotOperator);
                    VisitValue(right);
                    Write(_sqlBuilder.CloseBracket);
                    Write(_sqlBuilder.BitwiseOrOperator);
                    Write(_sqlBuilder.OpenBracket);
                    Write(_sqlBuilder.BitwiseNotOperator);
                    VisitValue(left);
                    Write(_sqlBuilder.BitwiseAndOperator);
                    VisitValue(right);
                    Write(_sqlBuilder.CloseBracket);
                    break;
                case ExpressionType.Add:
                case ExpressionType.AddChecked:
                case ExpressionType.Subtract:
                case ExpressionType.SubtractChecked:
                case ExpressionType.Multiply:
                case ExpressionType.MultiplyChecked:
                case ExpressionType.Divide:
                case ExpressionType.Modulo:
                case ExpressionType.LeftShift:
                case ExpressionType.RightShift:
                    VisitValue(left);
                    Write(op);
                    VisitValue(right);
                    break;
                default:
                    if (_forDebug)
                    {
                        Write("?");
                        Write(node.NodeType.ToString());
                        Write("?(");
                        Visit(node.Left);
                        Write(", ");
                        Visit(node.Right);
                        Write(")");
                        return node;
                    }
                    throw new NotSupportedException("Unsupported binary operator:" + node.NodeType.ToString());
            }
            Write(_sqlBuilder.CloseBracket);

            return node;
        }

        private bool CheckNulls(Expression left, Expression right, string nullString)
        {
            bool result = false;
            if (right.NodeType == ExpressionType.Constant)
            {
                ConstantExpression ce = (ConstantExpression)right;
                if (ce.Value == null)
                {
                    Visit(left);
                    Write(nullString);
                    result = true;
                }
            }
            else if (left.NodeType == ExpressionType.Constant)
            {
                ConstantExpression ce = (ConstantExpression)left;
                if (ce.Value == null)
                {
                    Visit(right);
                    Write(nullString);
                    result = true;
                }
            }
            return result;
        }

        protected virtual string GetOperator(BinaryExpression b)
        {
            if (b == null) return "?<null>?";
            switch (b.NodeType)
            {
                case ExpressionType.And:
                case ExpressionType.AndAlso:
                    return (IsBoolean(b.Left.Type)) ? _sqlBuilder.AndOperand : _sqlBuilder.BitwiseAndOperator;
                case ExpressionType.Or:
                case ExpressionType.OrElse:
                    return (IsBoolean(b.Left.Type) ? _sqlBuilder.OrOperand : _sqlBuilder.BitwiseOrOperator);
                case ExpressionType.Equal:
                    return _sqlBuilder.EqualOperator;
                case ExpressionType.NotEqual:
                    return _sqlBuilder.NotEqualOperator;
                case ExpressionType.LessThan:
                    return _sqlBuilder.LessThanOperator;
                case ExpressionType.LessThanOrEqual:
                    return _sqlBuilder.NotGreaterThanOperator;
                case ExpressionType.GreaterThan:
                    return _sqlBuilder.GreaterThanOperator;
                case ExpressionType.GreaterThanOrEqual:
                    return _sqlBuilder.NotLessThanOperator;
                case ExpressionType.Add:
                case ExpressionType.AddChecked:
                    return b.Type == typeof(string) ? _sqlBuilder.ConcatOperator : _sqlBuilder.AddOperator;
                case ExpressionType.Subtract:
                case ExpressionType.SubtractChecked:
                    return _sqlBuilder.SubtractOperator;
                case ExpressionType.Multiply:
                case ExpressionType.MultiplyChecked:
                    return _sqlBuilder.MultiplyOperator;
                case ExpressionType.Divide:
                    return _sqlBuilder.DivideOperator;
                case ExpressionType.Modulo:
                    return _sqlBuilder.ModuloOperator;
                case ExpressionType.ExclusiveOr:
                    return _sqlBuilder.ExclusiveOrOperator;
                case ExpressionType.LeftShift:
                    return _sqlBuilder.LeftShiftOperator;
                case ExpressionType.RightShift:
                    return _sqlBuilder.RightShiftOperator;
                default:
                    if (_forDebug)
                    {
                        return "?" + b.NodeType.ToString() + "?";
                    }
                    throw new NotSupportedException("Unsupported Binary Operator: " + b.NodeType.ToString());
            }
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            if (node == null) return null;

            string op = GetOperator(node);
            switch (node.NodeType)
            {
                case ExpressionType.Not:
                    if (IsBoolean(node.Operand.Type) || op.Length > 1)
                    {
                        Write(op);
                        VisitPredicate(node.Operand);
                    }
                    else
                    {
                        Write(op);
                        VisitValue(node.Operand);
                    }
                    break;
                case ExpressionType.Negate:
                case ExpressionType.NegateChecked:
                    Write(op);
                    VisitValue(node.Operand);
                    break;
                case ExpressionType.UnaryPlus:
                    VisitValue(node.Operand);
                    break;
                case ExpressionType.Convert:
                    // ignore conversions for now
                    Visit(node.Operand);
                    break;
                default:
                    if (_forDebug)
                    {
                        Write("?");
                        Write(node.NodeType.ToString());
                        Write("?(");
                        Visit(node.Operand);
                        Write(")");
                        return node;
                    }
                    throw new NotSupportedException("Unsupported unary operator :" + node.NodeType.ToString());
            }
            return node;
        }

        protected virtual string GetOperator(UnaryExpression u)
        {
            if (u == null) return "?<null>?";
            switch (u.NodeType)
            {
                case ExpressionType.Negate:
                case ExpressionType.NegateChecked:
                    return _sqlBuilder.NegateOperator;
                case ExpressionType.UnaryPlus:
                    return _sqlBuilder.PlusOperator;
                case ExpressionType.Not:
                    return IsBoolean(u.Operand.Type) ? _sqlBuilder.NotOperator : _sqlBuilder.BitwiseNotOperator;
                case ExpressionType.Convert:
                    // ignore conversions for now
                    return string.Empty;
                default:
                    if (_forDebug)
                    {
                        return "?" + u.NodeType.ToString() + "?";
                    }
                    throw new NotSupportedException("Unsupported Unary Operator: " + u.NodeType.ToString());
            }
        }

        protected override Expression VisitAggregate(AggregateExpression node)
        {
            if (node == null) return null;

            WriteAggregateProlog(node.AggregateName);

            if (node.IsDistinct)
            {
                Write(_sqlBuilder.AggregateDistinct);
            }

            if (node.Argument != null)
            {
                VisitValue(node.Argument);
            }
            else if (RequiresAsteriskWhenNoArgument(node.AggregateName))
            {
                Write(_sqlBuilder.AggregateAsterisk);
            }

            WriteAggregateEpilog(node.AggregateName);

            return node;
        }

        protected virtual bool RequiresAsteriskWhenNoArgument(string aggregateName)
        {
            return !string.IsNullOrEmpty(aggregateName) && (aggregateName == "Count" || aggregateName == "LongCount");
        }

        protected virtual void WriteAggregateProlog(string aggregateName)
        {
            if (string.IsNullOrEmpty(aggregateName)) return;
            switch (aggregateName)
            {
                case "Average":
                    Write(_sqlBuilder.AvgFunctionProlog);
                    break;
                case "Count":
                case "LongCount":
                    Write(_sqlBuilder.CountFunctionProlog);
                    break;
                case "Min":
                    Write(_sqlBuilder.MinFunctionProlog);
                    break;
                case "Max":
                    Write(_sqlBuilder.MaxFunctionProlog);
                    break;
                case "Sum":
                    Write(_sqlBuilder.SumFunctionProlog);
                    break;
                default:
                    if (_forDebug)
                    {
                        Write("?");
                        Write(aggregateName);
                        Write("(");
                        break;
                    }
                    throw new NotSupportedException("Unsupported Aggregate " + aggregateName);
            }
        }

        protected virtual void WriteAggregateEpilog(string aggregateName)
        {
            if (string.IsNullOrEmpty(aggregateName)) return;

            switch (aggregateName)
            {
                case "Average":
                    Write(_sqlBuilder.AvgFunctionEpilog);
                    break;
                case "Count":
                case "LongCount":
                    Write(_sqlBuilder.CountFunctionEpilog);
                    break;
                case "Min":
                    Write(_sqlBuilder.MinFunctionEpilog);
                    break;
                case "Max":
                    Write(_sqlBuilder.MaxFunctionEpilog);
                    break;
                case "Sum":
                    Write(_sqlBuilder.SumFunctionEpilog);
                    break;
                default:
                    if (_forDebug)
                    {
                        Write(")");
                        break;
                    }
                    throw new NotSupportedException("Unsupported Aggregate " + aggregateName);
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        protected override Expression VisitMethodCall(MethodCallExpression node)
        {
            if (node == null) return null;
            if (node.Method.DeclaringType == typeof(string))
            {
                switch (node.Method.Name)
                {
                    case nameof(string.StartsWith):
                        Visit(node.Object);
                        Write(_sqlBuilder.LikeClause);
                        Visit(node.Arguments[0]);
                        Write(_sqlBuilder.ConcatOperator);
                        Write(_sqlBuilder.DecorateCharValue(_sqlBuilder.MultipleWildCharacter));
                        return node;
                    case nameof(string.EndsWith):
                        Visit(node.Object);
                        Write(_sqlBuilder.LikeClause);
                        Write(_sqlBuilder.DecorateCharValue(_sqlBuilder.MultipleWildCharacter));
                        Write(_sqlBuilder.ConcatOperator);
                        Visit(node.Arguments[0]);
                        return node;
                    case nameof(string.Contains):
                        Visit(node.Object);
                        Write(_sqlBuilder.LikeClause);
                        Write(_sqlBuilder.DecorateCharValue(_sqlBuilder.MultipleWildCharacter));
                        Write(_sqlBuilder.ConcatOperator);
                        Visit(node.Arguments[0]);
                        Write(_sqlBuilder.ConcatOperator);
                        Write(_sqlBuilder.DecorateCharValue(_sqlBuilder.MultipleWildCharacter));
                        return node;
                }
            }
            else if (node.Method.DeclaringType == typeof(Decimal))
            {
                switch (node.Method.Name)
                {
                    case "Add":
                    case "Subtract":
                    case "Multiply":
                    case "Divide":
                    case "Remainder":

                        Write(_sqlBuilder.OpenBracket);
                        VisitValue(node.Arguments[0]);
                        Write(GetOperator(node.Method.Name));
                        VisitValue(node.Arguments[1]);
                        Write(_sqlBuilder.CloseBracket);

                        return node;
                    case "Negate":

                        Write(_sqlBuilder.NegateOperator);
                        Visit(node.Arguments[0]);

                        return node;
                    case "Compare":
                        Visit(Expression.Condition(
                            Expression.Equal(node.Arguments[0], node.Arguments[1]),
                            Expression.Constant(0),
                            Expression.Condition(
                                Expression.LessThan(node.Arguments[0], node.Arguments[1]),
                                Expression.Constant(-1),
                                Expression.Constant(1)
                                )));
                        return node;
                }
            }
            else if (node.Method.Name == "ToString" && node.Object.Type == typeof(string))
            {
                return Visit(node.Object);
            }
            else if (node.Method.Name == "Equals")
            {
                if (node.Method.IsStatic && node.Method.DeclaringType == typeof(object))
                {
                    Write(_sqlBuilder.OpenBracket);
                    Visit(node.Arguments[0]);
                    Write(_sqlBuilder.EqualOperator);
                    Visit(node.Arguments[1]);
                    Write(_sqlBuilder.CloseBracket);
                    return node;
                }
                else if (!node.Method.IsStatic && node.Arguments.Count == 1 && node.Arguments[0].Type == node.Object.Type)
                {
                    Write(_sqlBuilder.OpenBracket);
                    Visit(node.Object);
                    Write(_sqlBuilder.EqualOperator);
                    Visit(node.Arguments[0]);
                    Write(_sqlBuilder.CloseBracket);
                    return node;
                }
            }

            if (_forDebug)
            {
                if (node.Object != null)
                {
                    Visit(node.Object);
                    Write(_sqlBuilder.NameQualifier);
                }
                Write("?");
                Write(node.Method.Name);
                Write("?");
                Write(_sqlBuilder.OpenBracket);
                for (int i = 0; i < node.Arguments.Count; i++)
                {
                    if (i > 0)
                        Write(_sqlBuilder.CommaSeparator);
                    Visit(node.Arguments[i]);
                }
                Write(_sqlBuilder.CloseBracket);
                return node;
            }
            throw new NotSupportedException("Unsupported method " + node.Method.Name);
        }

        protected virtual string GetOperator(string methodName)
        {
            switch (methodName)
            {
                case "Add": return _sqlBuilder.AddOperator;
                case "Subtract": return _sqlBuilder.SubtractOperator;
                case "Multiply": return _sqlBuilder.MultiplyOperator;
                case "Divide": return _sqlBuilder.DivideOperator;
                case "Negate": return _sqlBuilder.NegateOperator;
                case "Remainder": return _sqlBuilder.ModuloOperator;
                default: return null;
            }
        }

        #region Unsupported Visits

        protected override Expression VisitIf(IfCommand node)
        {
            if (_forDebug)
            {
                Write("?IfCommand?");
                return node;
            }
            throw new NotSupportedException();
        }

        protected override Expression VisitBlock(BlockCommand node)
        {
            if (_forDebug)
            {
                Write("?BlockCommand?");
                return node;
            }
            throw new NotSupportedException();
        }

        protected override Expression VisitDeclaration(DeclarationCommand node)
        {
            if (_forDebug)
            {
                Write("?DeclarationCommand?");
                return node;
            }
            throw new NotSupportedException();
        }

        protected override Expression VisitConditional(ConditionalExpression node)
        {
            if (node == null) return null;
            if (_forDebug)
            {
                Write("?iff?(");
                Visit(node.Test);
                Write(", ");
                Visit(node.IfTrue);
                Write(", ");
                Visit(node.IfFalse);
                Write(")");
                return node;
            }
            throw new NotSupportedException("Conditional expressions not supported");
        }

        protected override Expression VisitNew(NewExpression node)
        {
            if (node == null) return null;
            if (_forDebug)
            {
                Write("?new?");
                Write(node.Type.Name);
                Write("(");
                for (int i = 0; i < node.Arguments.Count; i++)
                {
                    if (i > 0)
                        Write(", ");
                    Visit(node.Arguments[i]);
                }
                Write(")");
                return node;
            }
            throw new NotSupportedException("Unsupported constructor: " + node.Constructor.DeclaringType.FullName);
        }

        protected override Expression VisitRowNumber(RowNumberExpression node)
        {
            if (node == null) return null;
            if (_forDebug)
            {
                Write("?RowNumber?");
                return node;
            }
            throw new NotSupportedException("Unsupported RowNumber");
        }

        /// <summary>
        /// Override this function to add some built-in server-side function calls
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        protected override Expression VisitMember(MemberExpression node)
        {
            if (node == null) return null;
            if (_forDebug)
            {
                Visit(node.Expression);
                Write(".?");
                Write(node.Member.Name);
                Write("?");
                return node;
            }
            throw new NotSupportedException("Unsupported member " + node.Member.Name);
        }
        
        #endregion
    }
}
