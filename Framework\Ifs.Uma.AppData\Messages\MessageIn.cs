﻿using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;

namespace Ifs.Uma.AppData.Messages
{
    public abstract class MessageIn
    {
        public MessageType MessageType { get; }
        public long ServerMessageId { get; set; }
        public long? ClientRelatedMessageId { get; set; }
        public string MessageData { get; set; }
        public long TransactionId { get; set; }

        public string DebugString
        {
            get { return string.Format(CultureInfo.CurrentCulture, "{0} - {1}", ServerMessageId, MessageType); }
        }

        public static MessageIn Create(MessageType messageType)
        {
            switch (messageType)
            {
                case MessageType.INITSTARTED:
                case MessageType.INITDATA:
                case MessageType.INITENDED:
                case MessageType.INITFAILED:
                case MessageType.INITREQUIRED:
                case MessageType.METADATA_REFRESHED:
                    return new InitMessageIn(messageType);

                case MessageType.PRIMARY_KEY_UPDATE:
                    return new PrimaryKeyUpdateMessageIn();

                case MessageType.TRANENDED:
                    return new TransEndedMessageIn();

                case MessageType.INSERT:
                case MessageType.DELETE:
                case MessageType.UPDATE:
                    return new DataChangeMessageIn(messageType);
                case MessageType.OTHER:
                    return new ResponseMessageIn();
            }

            return new DoNothingMessageIn(messageType);
        }

        protected MessageIn(MessageType messageType)
        {
            MessageType = messageType;
        }

        public virtual void Execute(FwDataContext ctx, IClientKeysMapper clientKeysMapper, ILogger logger, DataChangeSet dataChangeSet, bool isInitializing)
        {
            ctx.ExecuteInTransaction((command) =>
            {
                bool executeMessage = TransactionId == 0 || MessageType == MessageType.TRANENDED;

                if (executeMessage)
                {
                    Execute(ctx, command, clientKeysMapper, logger, dataChangeSet, isInitializing);
                }
                else
                {
                    SaveMessageIn(ctx, command);
                }

                InsertMessageInReceipt(ctx, command, ServerMessageId);
            });
        }

        protected internal void Execute(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper, ILogger logger, DataChangeSet dataChangeSet, bool isInitializing)
        {
            IDbRowHandler<TransitionRow> handler = null;
            TransitionRow transitionRow = null;
            if (ClientRelatedMessageId.HasValue)
            {
                handler = ctx.CreateDbRowHandler<TransitionRow>(command);
                transitionRow = new TransitionRow();
                transitionRow.RowId = ClientRelatedMessageId.Value;
                if (!handler.SelectRow(transitionRow))
                {
                    transitionRow = null;
                }
            }

            OnExecute(ctx, command, clientKeysMapper, logger, dataChangeSet, transitionRow, isInitializing);

            if (transitionRow != null)
            {
                TransitionRowSent(ctx, clientKeysMapper, handler, transitionRow);
            }
        }

        protected abstract void OnExecute(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper,
            ILogger logger, DataChangeSet dataChangeSet, TransitionRow transitionRow, bool isInitializing);

        private void SaveMessageIn(FwDataContext ctx, DbCommand command)
        {
            MessageInRow row = new MessageInRow();
            row.ServerMessageId = ServerMessageId;
            row.ClientRelatedMessageId = ClientRelatedMessageId;
            row.MessageType = MessageType;
            row.Message = MessageData;
            row.TransactionId = TransactionId;

            IDbRowHandler<MessageInRow> rowHandler = ctx.CreateDbRowHandler<MessageInRow>(command);
            rowHandler.InsertRow(row);
        }

        protected void InsertMessageInReceipt(FwDataContext ctx, DbCommand command, long serverMessageId)
        {
            IDbRowHandler<MessageInReceipt> rowHandler = ctx.CreateDbRowHandler<MessageInReceipt>(command);
            rowHandler.InsertRow(new MessageInReceipt() { MessageId = serverMessageId });
        }

        public static void TransitionRowSent(FwDataContext ctx, IClientKeysMapper keyMapper, IDbRowHandler<TransitionRow> handler, TransitionRow transitionRow)
        {
            transitionRow.SyncState = SyncState.SentAcknowledged;
            handler.UpdateRow(transitionRow);

            if (transitionRow.Operation == OperationType.Delete)
            {
                // We must delete the client key mapping so future server updates are not mapped
                UnregisterKeyMapping(ctx, handler.Command, keyMapper, transitionRow);
            }

            // check if any rows not successful.
            IEnumerable<TransitionRow> rows = ctx.TransitionRows.Where(x => x.TransitionId == transitionRow.TransitionId).ToArray();

            if (rows.Any(x => x.SyncState != SyncState.SentAcknowledged))
            {
                return;
            }

            IDbRowHandler<TransitionRowField> changeHandler = ctx.CreateDbRowHandler<TransitionRowField>(handler.Command);
            foreach (var tRow in rows)
            {
                foreach (var tChange in ctx.TransitionChanges.Where(x => x.TransitionRowId == tRow.RowId))
                {
                    changeHandler.DeleteRow(tChange);
                }

                handler.DeleteRow(tRow);
            }

            IDbRowHandler<Transition> deleteHandler = ctx.CreateDbRowHandler<Transition>(handler.Command);
            deleteHandler.DeleteRow(new Transition() { TransitionId = transitionRow.TransitionId });
        }

        private static void UnregisterKeyMapping(FwDataContext ctx, DbCommand command, IClientKeysMapper keyMapper, TransitionRow transitionRow)
        {
            IMetaTable table = ctx.Model.GetTable(transitionRow.TableName);
            if (table != null)
            {
                IEnumerable<TransitionRowField> changes = ctx.TransitionChanges
                    .Where(x => x.TransitionRowId == transitionRow.RowId).ToArray();

                ObjPrimaryKey key = ObjPrimaryKey.FromTransitionChanges(table, changes);
                if (key != null)
                {
                    UnregisterKeyMapping(ctx, command, keyMapper, key);
                }
            }
        }

        protected static void UnregisterKeyMapping(FwDataContext ctx, DbCommand command, IClientKeysMapper keyMapper, ObjPrimaryKey key)
        {
            string keys = key.ToKeySeparatedValues();

            ClientKeysMap[] mappingsToUnregister = ctx.ClientKeysMap
                .Where(x => x.TableName == key.Table.TableName && (x.ClientKeys == keys || x.ServerKeys == keys))
                .ToArray();

            if (mappingsToUnregister.Length > 0)
            {
                IDbRowHandler<ClientKeysMap> keyMapRowHandler = ctx.CreateDbRowHandler<ClientKeysMap>(command);
                keyMapRowHandler.DeleteAll(mappingsToUnregister);

                if (keyMapper != null)
                {
                    keyMapper.UnregisterKeys(mappingsToUnregister);
                }
            }
        }

        protected JObject ReadDataAsJson()
        {
            if (string.IsNullOrEmpty(MessageData))
            {
                return null;
            }

            if (MessageData.StartsWith("{"))
            {
                using (StringReader sr = new StringReader(MessageData))
                {
                    return ReadJson(sr);
                }
            }

            return ReadBase64CompressedTextAsJson(MessageData);
        }

        private static JObject ReadBase64CompressedTextAsJson(string messageText)
        {
            byte[] bytes = Convert.FromBase64String(messageText);
            using (MemoryStream ms = new MemoryStream(bytes))
            using (GZipStream gz = new GZipStream(ms, CompressionMode.Decompress, false))
            using (StreamReader sr = new StreamReader(gz, Encoding.UTF8))
            {
                return ReadJson(sr);
            }
        }

        private static JObject ReadJson(TextReader tr)
        {
            JsonReader reader = new JsonTextReader(tr);
            // Make sure the parser doesnt just decide a value is a date because the
            // string is in the format of a date
            reader.DateParseHandling = DateParseHandling.None;
            JsonSerializerSettings settings = new JsonSerializerSettings();
            //Previous version of newtonsoft.json library had 128 as default for max depth. New version has the default value as 64. Manually setting max depth value to 128.
            settings.MaxDepth = 128;
            JsonSerializer jsonSerializer = JsonSerializer.CreateDefault(settings);
            return jsonSerializer.Deserialize<JObject>(reader);
        }
        
        protected IgnoreMessageIn CreateIgnoreMessageInRow(MessageTableData data)
        {
            // When we request a change to a GroupPushed entity on the server we will also get 
            // the change sent back down to the client E.g. 
            //
            //  - client does an insert
            //  - server processes insert, 
            //  - server sends response for insert, 
            //  - server processes group push
            //  - server tells client to do insert
            // 
            // In this situation the client must ignore the insert message from the server. 
            // To do this we record the ObjKey and ObjVersion included in the response so we can then 
            // use this to decide if the update originated from this client and should be ignored

            string objKey = data.GetRowValue(nameof(RemoteRow.ObjKey));
            string objVersion = data.GetRowValue(nameof(RemoteRow.ObjVersion));

            if (!string.IsNullOrEmpty(objKey) && !string.IsNullOrEmpty(objVersion))
            {
                // HACK: TEOFF-2738: MediaLibrary and DocReferenceObject can share ObjKeys between rows. Since they are 
                // not group push entities we can just accept those messages
                if (data.TableName == MediaLibrary.DbTableName || data.TableName == DocReferenceObject.DbTableName)
                {
                    return null;
                }

                IgnoreMessageIn row = new IgnoreMessageIn();
                row.TableName = data.TableName;
                row.ObjKey = objKey;
                row.ObjVersion = objVersion;
                return row;
            }

            return null;
        }
    }
}
