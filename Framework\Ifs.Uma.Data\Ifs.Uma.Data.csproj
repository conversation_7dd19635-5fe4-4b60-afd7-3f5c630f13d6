﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>keyfile.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <CodeAnalysisRuleSet>..\FrameworkRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DocumentationFile>bin\Release\netstandard1.1\Ifs.Uma.Data.XML</DocumentationFile>
    <CodeAnalysisRuleSet>..\FrameworkRules.Release.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\UmaAssemblyInfo.cs" Link="Properties\UmaAssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="StyleCop.Analyzers" Version="1.0.2" />
    <PackageReference Include="System.Dynamic.Runtime" Version="4.3.0" />
    <PackageReference Include="System.Linq.Queryable" Version="4.3.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Ifs.Uma.Database\Ifs.Uma.Database.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Utility\Ifs.Uma.Utility.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Localization\Ifs.Uma.Localization.csproj" />
  </ItemGroup>
</Project>