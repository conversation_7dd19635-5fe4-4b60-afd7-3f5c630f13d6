﻿<?xml version="1.0" encoding="utf-8" ?>
<LinearLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:orientation="vertical">

  <TextView
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:id="@+id/section_header"
    android:paddingLeft="10dp"
    android:textColor="@android:color/black"
    android:gravity="center_vertical"
    android:visibility="gone"/>

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:id="@+id/indicator_top_layout"
    android:layout_marginBottom="4dp"
    android:layout_marginRight="8dp"
    android:layout_marginLeft="8dp"
    android:visibility="gone"
    android:layout_gravity="center_vertical">

    <ImageView
      android:id="@+id/indicator_bullet_top"
      android:layout_width="8dp"
      android:layout_height="8dp"
      android:contentDescription="@string/icon_content_description" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="2dp"
      android:background="@android:color/black"
      android:layout_marginTop="3dp"/>
  </LinearLayout>

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:id="@+id/custom_layout_main"
    android:layout_marginBottom="4dp"
    android:layout_marginRight="8dp"
    android:layout_marginLeft="8dp">

    <View
      android:layout_width="10dp"
      android:layout_height="72dp"
      android:id="@+id/border_stripe"
      android:layout_marginLeft="2dp"
      android:layout_marginTop="2dp"
      android:layout_marginBottom="2dp"/>

    <RelativeLayout
      android:layout_width="match_parent"
      android:layout_height="72dp"
      android:id="@+id/custom_layout_sub"
      android:layout_marginRight="2dp"
      android:layout_marginTop="2dp"
      android:layout_marginBottom="2dp">

      <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:id="@+id/appointment_icon"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="5dp"
        android:layout_marginLeft="8dp"
        />

      <TextView
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:id="@+id/main_text"
        android:ellipsize="end"
        android:maxLines="1"
        android:textSize="16dp"
        android:layout_gravity="left"
        android:layout_marginLeft="6dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="4dp"
        android:layout_toRightOf="@id/appointment_icon"
        android:layout_toLeftOf="@id/appointment_badge"/>

      <TextView
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:id="@+id/sub_text"
        android:ellipsize="end"
        android:maxLines="1"
        android:textSize="16dp"
        android:layout_gravity="left"
        android:layout_marginLeft="6dp"
        android:layout_marginBottom="14dp"
        android:layout_alignParentBottom="true"
        android:layout_toEndOf="@id/appointment_icon"/>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/appointment_badge"
        android:gravity="center_vertical"
        android:layout_alignParentRight="true"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="10dp"
        android:textSize="14dp"/>
    </RelativeLayout>
  </LinearLayout>

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:id="@+id/indicator_bottom_layout"
    android:layout_marginBottom="4dp"
    android:layout_marginRight="8dp"
    android:layout_marginLeft="8dp"
    android:layout_gravity="center_vertical"
    android:visibility="gone">
    <ImageView
      android:id="@+id/indicator_bullet_bottom"
      android:layout_width="8dp"
      android:layout_height="8dp"
      android:contentDescription="@string/icon_content_description"/>
    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="2dp"
      android:background="@android:color/black"
      android:layout_marginTop="3dp"/>
  </LinearLayout>

  <Space
    android:layout_width="match_parent"
    android:layout_height="6dp"
    android:id="@+id/card_space"
    android:visibility="gone"/>
</LinearLayout>

