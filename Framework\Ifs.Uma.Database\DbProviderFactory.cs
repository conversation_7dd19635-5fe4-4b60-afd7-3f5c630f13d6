﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    // Use this attribute to flag the factory class in your dll.
    [AttributeUsage(AttributeTargets.Class, Inherited = false, AllowMultiple = false)]
    public sealed class DbFactoryAttribute : Attribute
    {
        public DbFactoryAttribute()
        {
        }
    }

    /// <summary>
    /// Rather than instantiating a DB Provider by its provider name,
    /// simply create a DLL that contains a public class that derives from DbProviderFactory
    /// and flag it with the [DbFactory] attribute.
    /// Now just pass the DLL path to the DbProviderFactory.Create method.
    /// </summary>
    public abstract class DbProviderFactory : IDisposable
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1031:DoNotCatchGeneralExceptionTypes")]
        public static DbProviderFactory Create(string dllPath, ILogger logger)
        {
            if (string.IsNullOrEmpty(dllPath)) throw new ArgumentNullException("dllPath");
            DbProviderFactory result = null;
            Assembly a = null;
            try
            {
                AssemblyName n = new AssemblyName(dllPath);
                a = Assembly.Load(n);
            }
            catch (Exception ex)
            {
                logger.Error(Strings.LoadDbProviderFactoryAssemblyError,
                    dllPath, ex.FormatXml(false, false));
            }
            if (a != null)
            {
                bool gotOne = false;
                foreach (Type t in a.ExportedTypes)
                {
                    if (t.GetTypeInfo().GetCustomAttributes<DbFactoryAttribute>(false).Any())
                    {
                        gotOne = true;
                        try
                        {
                            result = Activator.CreateInstance(t) as DbProviderFactory;
                            if (result != null)
                            {
                                result.Logger = logger;
                                break;
                            }
                            logger.Error(Strings.DbProviderFactoryWrongType, t.FullName);
                        }
                        catch (Exception ex)
                        {
                            logger.Error(Strings.CreateDbProviderFactoryError,
                                t.FullName, ex.FormatXml(false, false));
                        }
                    }
                }
                if (!gotOne)
                {
                    logger.Error(Strings.MissingDbFactory, dllPath);
                }
            }
            return result;
        }

        public DbConnection CreateConnection(string connectionString, IMapEnumeration enumMapper)
        {
            ICollection<DbConnection> connections = m_connections;
            if (connections == null) throw new ObjectDisposedException("DbProviderFactory");
            DbConnection result;
            try
            {
                result = NewConnection(connectionString, enumMapper);
            }
            catch (Exception ex)
            {
                Logger.Error(Strings.NewConnectionError, ex.FormatXml(false, false));
                throw;
            }
            if (result != null)
            {
                connections.Add(result);
            }
            return result;
        }

        public abstract string ProviderName { get; }
        public abstract SqlBuilder Builder { get; }
        public abstract DbConnectionStringBuilder CreateConnectionStringBuilder();
        public ILogger Logger { get; set; }
        public bool TraceFlag { get; set; }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected DbProviderFactory()
        {
            m_connections = new SynchronisedCollection<DbConnection>();
        }

        ~DbProviderFactory()
        {
            Dispose(false);
        }

        protected virtual void Dispose(bool disposing)
        {
        }

        protected abstract DbConnection NewConnection(string connectionString, IMapEnumeration enumMapper);

        protected void ConnectionDisposed(DbConnection connection)
        {
            ICollection<DbConnection> connections = m_connections;
            if (connection != null && connections != null)
            {
                connections.Remove(connection);
            }
        }

        private ICollection<DbConnection> m_connections;
    }
}
