﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Ifs.Uma.AppData;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.Data
{
    [DataContract]
    public sealed class PageValues
    {
        public string ItemKey
        {
            get
            {
                if (_itemKey == null && _itemKeyBase64 != null)
                {
                    byte[] bytes = Convert.FromBase64String(_itemKeyBase64);
                    _itemKey = Encoding.UTF8.GetString(bytes, 0, bytes.Length);
                }

                return _itemKey;
            }
        }

        // item key may contain invalid characters for XML so we store as base64
        [DataMember]
        private readonly string _itemKeyBase64;

        private string _itemKey;

        [DataMember]
        private readonly Dictionary<string, object> _attributeValues;

        public static PageValues FromPrimaryKey(ObjPrimaryKey key)
        {
            string itemKey = key?.ToKeySeparatedValues();

            if (itemKey == null)
            {
                return null;
            }

            return new PageValues(itemKey);
        }

        public static PageValues FromODataFilter(string filter)
        {
            if (string.IsNullOrEmpty(filter))
            { 
                return null; 
            }
                
            Dictionary<string, object> attributes = filter
                .Split('&')
                .Select(param => param.Split('='))
                .Where(kvp => kvp.Length == 2)
                .ToDictionary(kvp => kvp[0], kvp => (object)kvp[1]);

            return PageValues.FromAttributeValues(attributes);
        }

        public static PageValues FromItemKey(string itemKey)
        {
            return new PageValues(itemKey);
        }

        public static PageValues FromAttributeValues(Dictionary<string, object> values)
        {
            if (values == null || values.Count == 0) return null;
            return new PageValues(values);
        }

        public static PageValues FromRecordData(CpiNavArg[] navArgs, RecordData recordData)
        {
            if (recordData == null)
                throw new ArgumentNullException(nameof(recordData));
            if (navArgs == null || navArgs.Length == 0)
                return null;

            Dictionary<string, object> values = new Dictionary<string, object>();
            foreach (var kvp in navArgs)
            {
                values[kvp.Name] = recordData.ReadParamValue(kvp.Value);
            }

            return new PageValues(values);
        }

        public static PageValues FromColumnValues(Dictionary<string, string> columns, CommandContext context)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (columns == null || columns.Count == 0) return null;

            Dictionary<string, object> values = new Dictionary<string, object>();
            foreach (var kvp in columns)
            {
                values[kvp.Key] = context.ReadParamValue(kvp.Value);
            }

            return new PageValues(values);
        }

        public static PageValues FromColumnValues(Dictionary<string, string> columns, RecordData record)
        {
            if (record == null)
                throw new ArgumentNullException(nameof(record));
            if (columns == null || columns.Count == 0)
                return null;

            Dictionary<string, object> values = new Dictionary<string, object>();
            foreach (var kvp in columns)
            {
                values[kvp.Key] = record.ReadParamValue(kvp.Value);
            }

            return new PageValues(values);
        }

        public static PageValues FromNavArgs(CpiNavArg[] navArgs, CommandContext context)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (navArgs == null || navArgs.Length == 0) return null;

            Dictionary<string, object> values = new Dictionary<string, object>();
            foreach (var kvp in navArgs)
            {
                values[kvp.Name] = context.ReadParamValue(kvp.Value);
            }

            return new PageValues(values);
        }

        public void ExtractFunctionParameters(IReadOnlyDictionary<string, string> mapping, Dictionary<string, object> into)
        {
            if (mapping != null)
            {
                foreach (KeyValuePair<string, string> item in mapping)
                {
                    if (_attributeValues.TryGetValue(item.Value, out object value))
                    {
                        into[item.Key] = value;
                    }
                }
            }
        }

        private PageValues(string itemKey)
        {
            _itemKey = itemKey;
            _itemKeyBase64 = itemKey == null ? null : Convert.ToBase64String(Encoding.UTF8.GetBytes(itemKey));
        }

        private PageValues(Dictionary<string, object> values)
        {
            _attributeValues = values;
        }

        public void Apply(EntityQuery query)
        {
            if (ItemKey != null)
            {
                ObjPrimaryKey key = ObjPrimaryKey.FromKeySeparatedValues(query.DataSource.Table, ItemKey);
                query.SetFilter(key);
            }
            else if (_attributeValues != null)
            {
                query.SetFilter(_attributeValues);
            }
        }

        public Dictionary<string, object> ToDictionary()
        {
            if (_attributeValues == null)
            {
                return new Dictionary<string, object>();
            }

            return new Dictionary<string, object>(_attributeValues);
        }
    }
}
