﻿using System.Linq.Expressions;
using System.Reflection;
using Ifs.Uma.AppData.Expressions;

namespace Ifs.Uma.AppData.StringExpressions
{
    internal sealed class StringValueProviderReplacer : IfsExpressionVisitor
    {
        private static readonly MethodInfo GetValueMethod =
            typeof(StringValueProviderReplacer).GetTypeInfo().GetDeclaredMethod(nameof(GetValue));

        private readonly ParameterExpression _valueProviderParam;

        public static Expression Rewrite(Expression expression, ParameterExpression valueProviderParam)
        {
            IfsExpressionVisitor visitor = new StringValueProviderReplacer(valueProviderParam);
            return visitor.Visit(expression);
        }

        private StringValueProviderReplacer(ParameterExpression valueProviderParam)
        {
            _valueProviderParam = valueProviderParam;
        }

        protected internal override Expression VisitVarAccessExpression(VarAccessExpression exp)
        {
            return Expression.Call(GetValueMethod, _valueProviderParam, Expression.Constant(exp.PropertyPath));
        }

        private static DynamicValue GetValue(IStringExpressionValueProvider valueProvider, string propertyPath)
        {
            object value;
            if (valueProvider != null && valueProvider.TryGetValue(propertyPath, out value))
            {
                return new DynamicValue(value);
            }
            
            return new DynamicValue(null);
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            Expression operand = Visit(node.Operand);

            if (node.NodeType == ExpressionType.Convert && node.Type == operand.Type && node.Method == null)
            {
                return operand;
            }

            // Must override here since the base does some unwanted validation
            return node.Update(operand);
        }
    }
}
