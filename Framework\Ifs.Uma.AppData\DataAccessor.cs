﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.AppData
{
    public class DataAccessor<T> where T : FwDataContext
    {
        private readonly IDataContextProvider _db;
        private readonly ILogger _logger;
        private readonly IPerfLogger _perfLogger;
        private readonly string _name;
        private readonly TaskFactory _taskFactory;

        public DataAccessor(IDataContextProvider db, ILogger logger, IPerfLogger perfLogger)
        {
            if (db == null) throw new ArgumentNullException(nameof(db));

            _db = db;
            _logger = logger;
            _perfLogger = perfLogger;
            _name = this.GetType().Name;
            _taskFactory = Task.Factory;
        }

        protected Task WithDataContextAsync(Action<T> action, [CallerMemberName]string callerName = null)
        {
            return WithDataContextAsync((ctx) =>
                {
                    action(ctx);
                    return true;
                },
                callerName);
        }

        protected Task<K> WithDataContextAsync<K>(Func<T, Task<K>> action, [CallerMemberName] string callerName = null)
        {
            return WithDataContextAsync(ctx => action(ctx).GetAwaiter().GetResult(), callerName);
        }

        protected Task<K> WithDataContextAsync<K>(Func<T, K> action, [CallerMemberName]string callerName = null)
        {
            return _taskFactory.StartNew<K>(() =>
            {
                System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();

                sw.Start();

                T ctx = (T)_db.CreateDataContext();

                K innerResult = action(ctx);

                if (innerResult is IQueryable)
                {
                    throw new InvalidOperationException("WithDataContextAsync action must not return an IQueryable. (Use .ToArray)");
                }

                sw.Stop();

                if (callerName != null)
                {
                    if (_perfLogger != null)
                    {
                        _perfLogger.Log(_name, callerName, (int)sw.ElapsedMilliseconds);
                    }

                    if (_logger != null)
                    {
                        _logger.Trace("{0}: {1} {2}ms", _name, callerName, ((int)sw.ElapsedMilliseconds).ToString());
                    }
                }

                return innerResult;
            });
        }

        protected TRow CloneRow<TRow>(TRow row, bool clearRowId = true) where TRow : RemoteRow
        {
            IMetaModel model = _db.GetMetaModel();

            RemoteRow remoteRow = row as RemoteRow;
            IMetaTable table = remoteRow == null ? model.GetTable(row.GetType()) : model.GetTable(remoteRow.TableName);

            TRow clonedRow = (TRow)table.CloneRow(row);
            clonedRow.EntitySetName = row.EntitySetName;

            if (clearRowId)
            {
                typeof(RowBase).GetRuntimeProperty(nameof(RowBase.RowId)).SetValue(clonedRow, 0);

                RemoteRow remoteClonedRow = clonedRow as RemoteRow;
                if (remoteClonedRow != null)
                {
                    remoteClonedRow.ObjId = null;
                    remoteClonedRow.ObjVersion = null;
                    remoteClonedRow.ObjKey = null;
                }
            }

            return clonedRow;
        }
        
        protected void FireDataChangeEvent(IEventAggregator eventAggregator, RowBase row, string cause = DataChangedCauses.User)
        {
            if (eventAggregator == null) return;
            if (row == null) throw new ArgumentNullException(nameof(row));

            FireDataChangeEvent(eventAggregator, new[] { row });
        }

        protected void FireDataChangeEvent(IEventAggregator eventAggregator, IReadOnlyList<RowBase> rows, string cause = DataChangedCauses.User)
        {
            if (eventAggregator == null) return;
            if (rows == null) throw new ArgumentNullException(nameof(rows));
            
            if (rows.Count > 0)
            {
                IMetaModel model = _db.GetMetaModel();
                DataChangedEventArgs args = DataChangedEventArgs.FromRows(model, rows, cause);

                Task.Run(() =>
                {
                    try
                    {
                        eventAggregator.GetEvent<DataChangedEvent>().Publish(args);
                    }
                    catch (Exception ex)
                    {
                        _logger.HandleException(ExceptionType.Recoverable, ex);
                    }
                });
            }
        }
    }
}
