﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndDynamicAssistStep : RemoteRow
    {
        public const string DbTableName = "fnd_dynamic_assist_step";

        [Column]
        public string Label { get; set; }

        [Column]
        public string StepLabel { get; set; }

        [Column]
        public string Entity { get; set; }

        [Column]
        public string Description { get; set; }

        [Column]
        public string Name { get; set; }

        [Column]
        public string Datatype { get; set; }

        [Column]
        public string ControlType { get; set; }

        [Column]
        public bool? MultiLine { get; set; }

        [Column]
        public string BindAttribute { get; set; }

        [Column]
        public string BindAttributeLabel { get; set; }

        [Column]
        public string SaveAction { get; set; }

        [Column]
        public string SaveActionParameters { get; set; }

        [Column]
        public string ProjectionName { get; set; }

        [Column]
        public string DefaultValue { get; set; }

        [Column]
        public string DefaultValueAction { get; set; }

        [Column]
        public string DefaultValueActionParameters { get; set; }

        [Column]
        public bool? Required { get; set; }

        [Column]
        public string Enumeration { get; set; }

        [Column]
        public string Reference { get; set; }

        [Column]
        public bool? Visible { get; set; }

        [Column]
        public bool? Enabled { get; set; }

        [Column]
        public bool? RemarkNeeded { get; set; }

        [Column]
        public string RemarkAttribute { get; set; }

        [Column]
        public string RemarkAttributeLabel { get; set; }

        [Column]
        public bool? TerminateAllowed { get; set; }

        [Column]
        public string DynamicLovOptions { get; set; }

        [Column]
        public bool? FinishEnabled { get; set; }

        [Column]
        public bool? Editable { get; set; }

        [Column]
        public string DefaultClobValue { get; set; }

        [Column]
        public string BlobUploadCommand { get; set; } // Not used in offline yet

        [Column]
        public int? DynamicLovMaxSelectableOptions { get; set; }

        [Column]
        public string InputMask { get; set; }

        [Column]
        public double? LoopOccurrence { get; set; }

        public FndDynamicAssistStep()
            : base(DbTableName)
        {
        }
    }
}
