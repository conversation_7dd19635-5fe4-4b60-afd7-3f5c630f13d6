﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements
{
    public class FwContactWidget : ContactWidget
    {
        private readonly ILogger _logger;
        private readonly IDataHandler _data;
        private readonly IMetadata _metadata;
        private readonly IExpressionRunner _expressionRunner;
        private readonly TaskTracker _backgroundTasks;
        private readonly CancellingUpdater _loader;

        public CpiContactWidget CpiContactWidget { get; }

        public ViewData ViewData { get; set; }

        public FwContactWidget(CpiContactWidget contactWidget, ILogger logger, IMetadata metadata, IDataHandler data, IExpressionRunner expressionRunner, TaskTracker backgroundTasks)
        {
            CpiContactWidget = contactWidget;

            _logger = logger;
            _metadata = metadata;
            _data = data;
            _expressionRunner = expressionRunner;
            _backgroundTasks = backgroundTasks;

            _loader = new CancellingUpdater(LoadDetailsAsync);
        }

        private CpiContactWidgetMappingDataSource GetContactSource()
        {
            CpiContactSource source = CpiContactSource.Person;

            if (CpiContactWidget.Source != null)
            {
                foreach (CpiContactSource item in CpiContactWidget.Source.Keys)
                {
                    CpiExpression exp = CpiContactWidget.Source[item];
                    if (_expressionRunner.RunCheck(exp, ViewData, false))
                    {
                        source = item;
                        break;
                    }
                }
            }

            string sourceDef = source.ToString().ToLower();

            return _metadata.FindContactWidgetMappingDataSource(sourceDef);
        }

        public void LoadDetails()
        {
            Task task = _loader.UpdateAsync();
            _backgroundTasks?.Add(task);
        }

        private async Task LoadDetailsAsync(CancellationToken token)
        {
            Name = null;
            WorkPhone = null;
            MobileNo = null;
            Email = null;
            Picture = null;

            if (string.IsNullOrEmpty(Id))
            {
                return;
            }

            try
            {
                CpiContactWidgetMappingDataSource source = GetContactSource();

                if (source == null)
                {
                    return;
                }

                EntityDataSource dataSource = EntityDataSource.FromEntitySet(_metadata, source.DatasourceProjection, source.DatasourceEntitySet);

                if (dataSource == null)
                {
                    return;
                }

                EntityQuery query = new EntityQuery(dataSource);
                query.AddFilter(source.Id, Id);

                List<string> selectAttributes = new List<string>();
                
                if (!string.IsNullOrEmpty(source.Name))
                {
                    selectAttributes.Add(source.Name);
                }

                if (!string.IsNullOrEmpty(source.WorkPhone))
                {
                    selectAttributes.Add(source.WorkPhone);
                }

                if (!string.IsNullOrEmpty(source.MobileNo))
                {
                    selectAttributes.Add(source.MobileNo);
                }

                if (!string.IsNullOrEmpty(source.Email))
                {
                    selectAttributes.Add(source.Email);
                }

                if (!string.IsNullOrEmpty(source.Picture))
                {
                    selectAttributes.Add(source.Picture);
                }

                query.SelectAttributes = selectAttributes;

                EntityRecord record = await _data.GetRecordAsync(query, token);

                if (record != null)
                {
                    Name = !string.IsNullOrEmpty(source.Name) ? record.Row?[source.Name]?.ToString() : null;
                    WorkPhone = !string.IsNullOrEmpty(source.WorkPhone) ? record.Row?[source.WorkPhone]?.ToString() : null;
                    MobileNo = !string.IsNullOrEmpty(source.MobileNo) ? record.Row?[source.MobileNo]?.ToString() : null;
                    Email = !string.IsNullOrEmpty(source.Email) ? record.Row?[source.Email]?.ToString() : null;
                    Picture = !string.IsNullOrEmpty(source.Picture) ? record.Row?[source.Picture] as byte[] : null;
                }
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }
    }
}
