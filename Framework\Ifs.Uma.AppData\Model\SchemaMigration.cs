﻿using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;

/////////////////////////////////
// WARNING: Ensure you read the BackwardsCompatibility.md documentation before changing this file
/////////////////////////////////

namespace Ifs.Uma.AppData.Model
{
    internal static class SchemaMigration
    {
        internal static void UpdateDatabase(IDbSchemaUpdater schemaUpdater, int oldVersion)
        {
            // Put database schema change actions here. E.g:
            //if (oldVersion < 2)
            //{
            //    schemaUpdater.AddTable(typeof(NewRowType));
            //    schemaUpdater.AddColumn(typeof(ExistingRowType), nameof(ExistingRowType.NewColumnName));
            //}

            // 11.0 starts at version 0

            // 11.1 starts at version 10

            // 22.1 starts at version 20
#if SIGNATURE_SERVICE
            if (oldVersion < 20)
            {
                schemaUpdater.AddTable(typeof(DigitalSignatureAndDocument));
            }
#endif

            // 22R2 starts at version 30

#if SIGNATURE_SERVICE
            if (oldVersion < 30)
            {
                schemaUpdater.AddColumn(typeof(DigitalSignatureAndDocument), nameof(DigitalSignatureAndDocument.IsSigningFailure));
            }
#endif

            // 23R1 starts at version 40
            if (oldVersion < 41)
            {
                schemaUpdater.AddTable(typeof(FndTransactionSession));
                schemaUpdater.AddColumn(typeof(TransitionRow), nameof(TransitionRow.SessionId));
#if SIGNATURE_SERVICE
                schemaUpdater.AddColumn(typeof(DigitalSignatureAndDocument), nameof(DigitalSignatureAndDocument.SessionId));
#endif
            }

            if (oldVersion < 42)
            {
                schemaUpdater.AddColumn(typeof(DatabaseInfo), nameof(DatabaseInfo.NavigatorEntriesBlob));
            }

            // 23R2 starts at version 50
#if SIGNATURE_SERVICE
            if (oldVersion < 50)
            {
                schemaUpdater.AddColumn(typeof(DigitalSignatureAndDocument), nameof(DigitalSignatureAndDocument.JsonMetaData));
                schemaUpdater.AddColumn(typeof(DigitalSignatureAndDocument), nameof(DigitalSignatureAndDocument.FileName));
            }
#endif
            if (oldVersion < 50)
            {
                schemaUpdater.AddTable(typeof(ServerSyncRule));
            }

            // 24R2 starts at version 60
            if (oldVersion < 60)
            {
                schemaUpdater.AddTable(typeof(ServerSite));
            }

            // 25R1 starts at version 70
            if (oldVersion < 70)
            {
                schemaUpdater.AddTable(typeof(ServerSyncRule));
                schemaUpdater.AddTable(typeof(DocReferenceObject));
            }
        }
    }
}
