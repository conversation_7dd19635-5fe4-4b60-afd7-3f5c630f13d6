﻿using System.Collections.Generic;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Tests;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Tests.Execution
{
    internal class TestExecutionContext : ExecutionContext
    {
        public TestExecutionContext(string projectionName, IMetadata metadata, IExpressionRunner expressionRunner, IReadOnlyDictionary<string, VariableStorage> vars)
            : base(projectionName, metadata, expressionRunner, null, vars)
        { }
        
        protected override ExecutionException NewException(string message)
        {
            return new ProcedureException(message);
        }
    }

    internal class TestExecutor : Executor<TestExecutionContext>
    {
        public TestExecutor(ILogger logger, IExpressionRunner expressionRunner)
            : base(logger, expressionRunner)
        {
        }
        
        public ExecuteResult Call(string projectionName, CpiExecuteCallMethod method, CpiExecuteCallArgs args, IReadOnlyDictionary<string, object> vars)
        {
            TestExecutionContext context = CreateExecutionContext(projectionName, ExpressionRunner, vars);
            return ExecuteCommonCall(context, method, args);
        }
        
        public static TestExecutionContext CreateExecutionContext(string projectionName, IExpressionRunner expressionRunner, IReadOnlyDictionary<string, object> vars)
        {
            IMetadata metadata = FrameworkTest.CreateMetadata<FwDataContext>(typeof(TestExecutor), "Execution.CommonExecutionSchema.json");

            Dictionary<string, VariableStorage> convertedVars = new Dictionary<string, VariableStorage>();
            if (vars != null)
            {
                foreach (var var in vars)
                {
                    var storage = new VariableStorage(metadata, var.Key, null);
                    storage.TrySetValue(var.Value);
                    convertedVars[var.Key] = storage;
                }
            }

            return new TestExecutionContext(projectionName, metadata, expressionRunner, convertedVars);
        }
    }
}
