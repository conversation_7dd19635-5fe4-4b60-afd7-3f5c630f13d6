﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using IQToolkit;
using IQToolkit.Data.Common;

namespace Ifs.Uma.Data
{
    #region Row Tracking

    internal enum SubmitAction
    {
#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
        None,   // not actually used
        Insert, // row flagged for insert
        Delete, // row flagged for delete
        Update  // row flagged for full update
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row
    }

    internal abstract class TrackedRow
    {
        public TrackedRow(int sequence, string projectionName, IMetaTable table)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));

            Sequence = sequence;
            ProjectionName = projectionName;
            Table = table;
        }

        public int Sequence { get; private set; }
        public string ProjectionName { get; private set; }
        public IMetaTable Table { get; private set; }

        public abstract SubmitAction Action { get; }
        public abstract object Current { get; }
        public abstract object Original { get; }
        public abstract IEnumerable<ModifiedMemberInfo> ModifiedMembers { get; }
        public abstract void ModifyRow(DbRowHandler handler, RowValueAccessor accessor);
    }

    internal abstract class TrackedRow<T> : TrackedRow
    {
        public TrackedRow(int sequence, string projectionName, IMetaTable table, T currentRow)
            : base(sequence, projectionName, table)
        {
            if (table.RowType != typeof(T)) throw new ArgumentOutOfRangeException(nameof(table));
            if (currentRow == null) throw new ArgumentNullException(nameof(currentRow));
            CurrentRow = currentRow;
        }

        public T CurrentRow { get; private set; }
        public override object Current { get { return CurrentRow; } }

        public virtual T OriginalRow { get { return CurrentRow; } }
        public override object Original { get { return OriginalRow; } }

        public override IEnumerable<ModifiedMemberInfo> ModifiedMembers { get { return null; } }

        public void OnPropertyChanging(object sender, PropertyChangingEventArgs e)
        {
            OnPropertyChanging((T)sender, e.PropertyName);
        }

        protected virtual void OnPropertyChanging(T row, string propertyName)
        {
        }
    }

    internal class TrackedDelete<T> : TrackedRow<T>
    {
        public TrackedDelete(int sequence, string projectionName, IMetaTable table, T currentRow)
            : base(sequence, projectionName, table, currentRow)
        {
        }

        public override SubmitAction Action { get { return SubmitAction.Delete; } }

        public override void ModifyRow(DbRowHandler handler, RowValueAccessor accessor)
        {
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            handler.Delete(CurrentRow);
        }
    }

    internal class TrackedInsert<T> : TrackedRow<T>
    {
        public TrackedInsert(int sequence, string projectionName, IMetaTable table, T currentRow)
            : base(sequence, projectionName, table, currentRow)
        {
        }

        public override SubmitAction Action { get { return SubmitAction.Insert; } }

        public override void ModifyRow(DbRowHandler handler, RowValueAccessor accessor)
        {
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            handler.Insert(CurrentRow);
        }
    }

    /// <summary>
    /// T implements INotifyPropertyChanging so we can hook this event
    /// and create modified members on the fly without cloning the original row
    /// </summary>
    /// <typeparam name="T">Row Type</typeparam>
    internal class HookedUpdate<T> : TrackedRow<T>
    {
        public HookedUpdate(int sequence, string projectionName, IMetaTable table, T currentRow)
            : base(sequence, projectionName, table, currentRow)
        {
            _modifiedMembers = new Dictionary<string, ModifiedMemberInfo>();
        }

        public override SubmitAction Action { get { return SubmitAction.Update; } }

        public override T OriginalRow
        {
            get
            {
                lock (_modifiedMembers)
                {
                    T result = (T)Table.CloneRow(CurrentRow);
                    foreach (var p in _modifiedMembers)
                    {
                        p.Value.Member.SetValue(result, p.Value.OriginalValue);
                    }
                    return result;
                }
            }
        }

        public override IEnumerable<ModifiedMemberInfo> ModifiedMembers
        {
            get
            {
                lock (_modifiedMembers)
                {
                    return _modifiedMembers.Values.ToArray();
                }
            }
        }

        protected override void OnPropertyChanging(T row, string propertyName)
        {
            lock (_modifiedMembers)
            {
                if (!_modifiedMembers.ContainsKey(propertyName))
                {
                    IMetaDataMember member = Table.DataMembers.First(x => x != null &&
                        propertyName.Equals(x.PropertyName, StringComparison.OrdinalIgnoreCase));
                    object originalValue = member.CloneValue(row);
                    _modifiedMembers.Add(propertyName, new ModifiedMemberInfo(member, originalValue));
                }
            }
        }

        public override void ModifyRow(DbRowHandler handler, RowValueAccessor accessor)
        {
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            if (accessor == null) throw new ArgumentNullException(nameof(accessor));
            lock (_modifiedMembers)
            {
                if (_modifiedMembers.Count > 0)
                {
                    HashSet<IMetaDataMember> found = new HashSet<IMetaDataMember>(handler.Key);
                    found.UnionWith(_modifiedMembers.Select(x => x.Value.Member));
                    accessor.SetFoundMembers(found);
                    try
                    {
                        handler.Update(CurrentRow);
                    }
                    finally
                    {
                        accessor.SetFoundMembers(null);
                    }
                }
            }
        }

        private readonly Dictionary<string, ModifiedMemberInfo> _modifiedMembers;
    }

    /// <summary>
    /// T does not implement INotifyPropertyChanging so we clone the current row
    /// ModifiedMembers are then calculated by comparing the current row to the clone
    /// </summary>
    /// <typeparam name="T">Row Type</typeparam>
    internal class ClonedUpdate<T> : TrackedRow<T>
    {
        public ClonedUpdate(int sequence, string projectionName, IMetaTable table, T currentRow)
            : base(sequence, projectionName, table, currentRow)
        {
            m_original = (T)table.CloneRow(currentRow);
        }

        public override SubmitAction Action { get { return SubmitAction.Update; } }

        public override T OriginalRow { get { return (T)Table.CloneRow(m_original); } }

        public override IEnumerable<ModifiedMemberInfo> ModifiedMembers
        {
            get
            {
                return Table.GetModifications(CurrentRow, m_original);
            }
        }

        public override void ModifyRow(DbRowHandler handler, RowValueAccessor accessor)
        {
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            if (accessor == null) throw new ArgumentNullException(nameof(accessor));
            IEnumerable<ModifiedMemberInfo> modifiedMembers = ModifiedMembers;
            if (modifiedMembers != null && modifiedMembers.Any())
            {
                HashSet<IMetaDataMember> found = new HashSet<IMetaDataMember>(handler.Key);
                found.UnionWith(modifiedMembers.Select(x => x.Member));
                accessor.SetFoundMembers(found);
                try
                {
                    handler.Update(CurrentRow);
                }
                finally
                {
                    accessor.SetFoundMembers(null);
                }
            }
        }

        private T m_original;
    }

    internal class ChangeSet : IChangeSet
    {
        public ChangeSet()
        {
            m_deletes = new List<object>();
            m_inserts = new List<object>();
            m_updates = new List<object>();
        }

        private ICollection<object> m_deletes;
        private ICollection<object> m_inserts;
        private ICollection<object> m_updates;

        public IEnumerable<object> Deletes { get { return m_deletes; } }
        public IEnumerable<object> Inserts { get { return m_inserts; } }

        public IEnumerable<object> Updates { get { return m_updates; } }

        internal void AddChange(TrackedRow track)
        {
            if (track != null)
            {
                switch (track.Action)
                {
                    case SubmitAction.Delete:
                        m_deletes.Add(track.Current);
                        break;
                    case SubmitAction.Insert:
                        m_inserts.Add(track.Current);
                        break;
                    case SubmitAction.Update:
                        m_updates.Add(track.Current);
                        break;
                }
            }
        }
    }

    #endregion

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1010:CollectionsShouldImplementGenericInterface",
        Justification="This collection is not strongly typed")]
    public abstract class Table : ITable
    {
        protected Table(DataContextBase context)
        {
            if (context == null) throw new ArgumentNullException("context");
            ContextBase = context;
        }

        public IEnumerator GetEnumerator()
        {
            return GetEnumeratorInner();
        }

        public DataContext Context { get { return ContextBase as DataContext; } }
        protected DataContextBase ContextBase { get; private set; }

        internal abstract void ClearChanges();
        internal abstract void ResetAutoIncrement(IMetaDataMember autoIncrement);
        public abstract IMetaTable Entity { get; }
        public abstract void Attach(object row);
        public abstract void Attach(string projectionName, object row);
        public abstract void Detach(object row);
        public abstract void DeleteOnSubmit(object row);
        public abstract void DeleteOnSubmit(string projectionName, object row);
        public abstract void InsertOnSubmit(object row);
        public abstract void InsertOnSubmit(string projectionName, object row);
        public abstract object GetOriginalRow(object row);
        public abstract IEnumerable<ModifiedMemberInfo> GetRowModifications(object row);
        public abstract Type ElementType { get; }
        public abstract Expression Expression { get; }
        public abstract IQueryProvider Provider { get; }
        protected abstract IEnumerator GetEnumeratorInner();
        public abstract IEnumerable SortedRows { get; }
    }

    public class Table<T> : Table, ITable<T>
    {
        public override Type ElementType { get { return typeof(T); } }
        public override Expression Expression { get { return _query.Expression; } }
        public override IQueryProvider Provider { get { return _query.Provider; } }
        public override IMetaTable Entity { get { return _metaTable; } }

        private Query<T> _query;
        private IMetaTable _metaTable;
        private Query<T> _sortedQuery;

        public Table(DataContextBase context, IQueryProvider queryProvider, IMetaTable metaTable)
            : base(context)
        {
            _sortedQuery = null;
            Expression initialExpression = Expression.Constant(this, typeof(ITable<T>));
            IMetaDataMember autoIncrement = metaTable.AutoIncrement();
            if (autoIncrement != null)
            {
                ParameterExpression parameter = Expression.Parameter(typeof(T), "x");
                _sortedQuery = new Query<T>(queryProvider, Expression.Call(typeof(Queryable), "OrderBy", new Type[] { typeof(T), autoIncrement.ColumnType },
                    initialExpression, Expression.Lambda(Expression.MakeMemberAccess(parameter, autoIncrement.MemberInfo), parameter)));
            }
            _query = new Query<T>(queryProvider, initialExpression);
            _metaTable = metaTable;
            _trackedRows = new LockedDictionary<T, TrackedRow<T>>();
        }

        public override IEnumerable SortedRows { get { return _sortedQuery != null ? _sortedQuery : _query; } }

        public new IEnumerator<T> GetEnumerator()
        {
            return _query.GetEnumerator();
        }

        protected override IEnumerator GetEnumeratorInner()
        {
            return GetEnumerator();
        }

        /// <summary>
        /// Flags row as an update
        /// If the row implements INotifyPropertyChanging then
        /// the original value of each property is captured at first change
        /// otherwise the whole row must be cloned now
        /// </summary>
        /// <param name="row">original row that is to be updated</param>
        public void Attach(T row)
        {
            Attach(null, row);
        }

        /// <summary>
        /// Flags row as an update
        /// If the row implements INotifyPropertyChanging then
        /// the original value of each property is captured at first change
        /// otherwise the whole row must be cloned now
        /// </summary>
        /// <param name="projectionName">name of the projection this update should go to</param>
        /// <param name="row">original row that is to be updated</param>
        public void Attach(string projectionName, T row)
        {
            if (row == null) throw new ArgumentNullException("row");
            TrackedRow<T> track = _trackedRows.GetOrAdd(row, (k) => CreateTrackedRow(projectionName, k, SubmitAction.Update));
            if (track == null || track.Action != SubmitAction.Update)
                throw new InvalidOperationException("row cannot be updated");
        }

        /// <summary>
        /// Removes row as a possible change (insert, update or delete)
        /// </summary>
        /// <param name="row">row to detach</param>
        public void Detach(T row)
        {
            if (row == null) throw new ArgumentNullException("row");
            TrackedRow<T> track;
            if (_trackedRows.TryRemove(row, out track))
            {
                UnhookRow(row, track, true);
            }
        }

        /// <summary>
        /// Flags row as a delete
        /// </summary>
        /// <param name="row">row to delete</param>
        public void DeleteOnSubmit(T row)
        {
            DeleteOnSubmit(null, row);
        }

        /// <summary>
        /// Flags row as a delete
        /// </summary>
        /// <param name="projectionName">name of the projection this delete should go to</param>
        /// <param name="row">row to delete</param>
        public void DeleteOnSubmit(string projectionName, T row)
        {
            if (row == null) throw new ArgumentNullException("row");
            TrackedRow<T> track = _trackedRows.GetOrAdd(row, (k) => CreateTrackedRow(projectionName, k, SubmitAction.Delete));
            if (track == null || track.Action != SubmitAction.Delete)
                throw new InvalidOperationException("row cannot be deleted");
        }

        /// <summary>
        /// Flags row as an insert
        /// </summary>
        /// <param name="row">row to insert</param>
        public void InsertOnSubmit(T row)
        {
            InsertOnSubmit(null, row);
        }

        /// <summary>
        /// Flags row as an insert
        /// </summary>
        /// <param name="projectionName">name of the projection this insert should go to</param>
        /// <param name="row">row to insert</param>
        public void InsertOnSubmit(string projectionName, T row)
        {
            if (row == null) throw new ArgumentNullException("row");
            TrackedRow<T> track = _trackedRows.GetOrAdd(row, (k) => CreateTrackedRow(projectionName, k, SubmitAction.Insert));
            if (track == null || track.Action != SubmitAction.Insert)
                throw new InvalidOperationException("row cannot be inserted");
        }

        public T GetOriginalRow(T row)
        {
            TrackedRow<T> track = null;
            if (row != null)
            {
                _trackedRows.TryGetValue(row, out track);
            }
            return track != null ? track.OriginalRow : row;
        }

        public IEnumerable<ModifiedMemberInfo> GetRowModifications(T row)
        {
            TrackedRow<T> track = null;
            if (row != null)
            {
                _trackedRows.TryGetValue(row, out track);
            }
            return track != null ? track.ModifiedMembers : null;
        }

        public override void Attach(object row)
        {
            Attach((T)row);
        }

        public override void Attach(string projectionName, object row)
        {
            Attach(projectionName, (T)row);
        }

        public override void Detach(object row)
        {
            Detach((T)row);
        }

        public override void DeleteOnSubmit(object row)
        {
            DeleteOnSubmit((T)row);
        }

        public override void DeleteOnSubmit(string projectionName, object row)
        {
            DeleteOnSubmit(projectionName, (T)row);
        }

        public override void InsertOnSubmit(object row)
        {
            InsertOnSubmit((T)row);
        }

        public override void InsertOnSubmit(string projectionName, object row)
        {
            InsertOnSubmit(projectionName, (T)row);
        }

        public override object GetOriginalRow(object row)
        {
            return GetOriginalRow((T)row);
        }

        public override IEnumerable<ModifiedMemberInfo> GetRowModifications(object row)
        {
            return GetRowModifications((T)row);
        }

        public IEnumerable<T> FindAll(IEnumerable<ObjPrimaryKey> keys)
        {
            return ContextBase.FindAll(keys).Values.OfType<T>().ToArray();
        }

        public T Find(ObjPrimaryKey key)
        {
            object row = ContextBase.Find(key);
            return row is T ? (T)row : default(T);
        }

        internal override void ResetAutoIncrement(IMetaDataMember autoIncrement)
        {
            if (autoIncrement != null)
            {
                foreach (TrackedRow<T> track in _trackedRows.Values)
                {
                    if (track.Action == SubmitAction.Insert)
                    {
                        autoIncrement.SetValue(track.CurrentRow, 0);
                    }
                }
            }
        }

        internal override void ClearChanges()
        {
            // called from DataContext.ClearTransaction so the parent list is already clear.
            // unhook any event handling first
            foreach (KeyValuePair<T, TrackedRow<T>> kvp in _trackedRows)
            {
                UnhookRow(kvp.Key, kvp.Value, false);
            }
            _trackedRows.Clear();
        }

        private void UnhookRow(T row, TrackedRow<T> track, bool setNull)
        {
            if (setNull)
            {
                ContextBase.NullTrackedRow(track.Sequence);
            }
            if (track.Action == SubmitAction.Update)
            {
                INotifyPropertyChanging r = row as INotifyPropertyChanging;
                if (r != null)
                {
                    r.PropertyChanging -= track.OnPropertyChanging;
                }
            }
        }

        private TrackedRow<T> CreateTrackedRow(string projectionName, T row, SubmitAction action)
        {
            TrackedRow<T> result;
            int sequence = ContextBase.NextTrackId();
            switch (action)
            {
                case SubmitAction.Update:
                    INotifyPropertyChanging r = row as INotifyPropertyChanging;
                    if (r != null)
                    {
                        result = new HookedUpdate<T>(sequence, projectionName, Entity, row);
                        r.PropertyChanging += result.OnPropertyChanging;
                    }
                    else
                    {
                        result = new ClonedUpdate<T>(sequence, projectionName, Entity, row);
                    }
                    break;
                case SubmitAction.Insert:
                    result = new TrackedInsert<T>(sequence, projectionName, Entity, row);
                    break;
                case SubmitAction.Delete:
                    result = new TrackedDelete<T>(sequence, projectionName, Entity, row);
                    break;
                default:
                    throw new NotSupportedException("Invalid SubmitAction");
            }
            ContextBase.AddTrackedRow(result);
            return result;
        }

        private LockedDictionary<T, TrackedRow<T>> _trackedRows;
    }

    public static class TableExtensions
    {
        public static void InsertAllOnSubmit<T>(this Table<T> table, IEnumerable<T> rows)
        {
            if (table == null) throw new ArgumentNullException("table");
            if (rows != null)
            {
                foreach (T row in rows)
                {
                    table.InsertOnSubmit(row);
                }
            }
        }

        public static void DeleteAllOnSubmit<T>(this Table<T> table, IEnumerable<T> rows)
        {
            DeleteAllOnSubmit(table, null, rows);
        }

        public static void DeleteAllOnSubmit<T>(this Table<T> table, string projectionName, IEnumerable<T> rows)
        {
            if (table == null) throw new ArgumentNullException("table");
            if (rows != null)
            {
                foreach (T row in rows)
                {
                    table.DeleteOnSubmit(projectionName, row);
                }
            }
        }
    }

    public static class TableQueryable
    {
        [Obsolete("Use ToBatch instead")]
        public static IEnumerable<TSource> ToStream<TSource>(this IQueryable<TSource> source)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));

            MethodInfo method = GetMethodInfoOf(() => ToStream(default(IQueryable<TSource>)));
            Expression exp = Expression.Call(null, method, new Expression[] { source.Expression } );
            return source.Provider.Execute<IEnumerable<TSource>>(exp);
        }

        internal static readonly MethodInfo ToStreamEnumerable = GetMethodInfoOf(() => ToStream(default(IEnumerable<int>))).GetGenericMethodDefinition();
        internal static IEnumerable<TSource> ToStream<TSource>(this IEnumerable<TSource> source)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));
            return source;
        }

        public static IEnumerable<TSource> ToBatch<TSource>(this IQueryable<TSource> source)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));

            int batchSize = 500;

            int skipCount = 0;
            while (true)
            {
                TSource[] batch = source.Skip(skipCount * batchSize).Take(batchSize).ToArray();

                if (batch.Length == 0)
                {
                    break;
                }

                foreach (var item in batch)
                {
                    yield return item;
                }

                skipCount++;
            }
        }

        private static MethodInfo GetMethodInfoOf<T>(Expression<Func<T>> expression)
        {
            return ((MethodCallExpression)expression.Body).Method;
        }

        public static IQueryable<T> WhereSearch<T>(this IQueryable<T> queryable, string searchTerm, params Expression<Func<T, string>>[] propertyExpressions)
        {
            if (string.IsNullOrEmpty(searchTerm))
            {
                return queryable;
            }

            string[] terms = searchTerm.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

            ParameterExpression x = Expression.Parameter(typeof(T));

            MethodInfo containsMethod = typeof(string).GetRuntimeMethod(nameof(string.Contains), new[] { typeof(string) });

            Expression whereExp = null;
            foreach (string term in terms)
            {
                Expression termWhereExp = null;

                Expression termExpression = Expression.Constant(term);
                foreach (Expression<Func<T, string>> exp in propertyExpressions)
                {
                    Expression prop = DbExpressionReplacer.Replace(exp.Body, exp.Parameters.Single(), x);
                    Expression containsExp = Expression.Call(prop, containsMethod, termExpression);
                    termWhereExp = termWhereExp == null ? containsExp : Expression.OrElse(termWhereExp, containsExp);
                }

                if (termWhereExp != null)
                {
                    whereExp = termWhereExp;
                }
            }

            if (whereExp == null)
            {
                return queryable;
            }

            Expression<Func<T, bool>> where = Expression.Lambda<Func<T, bool>>(whereExp, x);
            return queryable.Where(where);
        }
    }
}
