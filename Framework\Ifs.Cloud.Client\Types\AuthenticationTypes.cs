﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  xxxx-xx-xx SUKMLK Created.
//  2011-10-13 PKULLK Modified to be compatible with .NEt4.5 with async calls, etc.
#endregion

namespace Ifs.Cloud.Client.Types
{
    /// <summary>
    /// Represents the authentication type
    /// </summary>
    public enum AuthenticationType
    {
        /// <summary>
        /// Uses Authtoken
        /// </summary>
        Authentication,
        /// <summary>
        /// Uses openid accesstoken
        /// </summary>
        OpenIdAuthentication,
        /// <summary>
        /// Uses Session token
        /// </summary>
        Session
    }
}
