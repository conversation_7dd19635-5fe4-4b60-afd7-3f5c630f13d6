﻿using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Database;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Messages
{
    [TestFixture]
    public class MessageTests : DataContextTest<FwDataContext>
    {
        [Test]
        public void TestMessageInsert()
        {
            FwDataContext ctx = CreateDataContext();
            
            IEnumerable<string> entries = new string[] { "A", "B", "C" };
            IEnumerable<MessageTableData> rows = CreateMessageTestRows(entries);

            ctx.ExecuteInTransaction((cmd) =>
            {
                DbRowHandlerCache cache = ctx.CreateDbRowHandlerCache(cmd, new MessageRowDataAccessor(), KeyChoice.DefaultKey);

                foreach (MessageTableData row in rows)
                {
                    cache.GetHandler(ctx.Model.GetTable(row.TableName)).Upsert(row.RowData);
                }
            });

            var insertedRows = ctx.RoamingProfileValues.ToArray();
            Assert.AreEqual(3, insertedRows.Length);
            Assert.AreEqual("A", insertedRows[0].ProfileEntry);
            Assert.AreEqual("B", insertedRows[1].ProfileEntry);
            Assert.AreEqual("C", insertedRows[2].ProfileEntry);
        }

        private static IEnumerable<MessageTableData> CreateMessageTestRows(IEnumerable<string> entries)
        {
            ICollection<MessageTableData> result = new List<MessageTableData>();
            if (entries != null)
            {
                foreach (string entry in entries)
                {
                    Dictionary<string, object> values = new Dictionary<string, object>();
                    values["profile_section"] = "S";
                    values["profile_entry"] = entry;
                    values["owner"] = true;
                    result.Add(new MessageTableData()
                    {
                        TableName = "fnd$client_profile_value",
                        RowData = new MessageRowData() { ColumnData = values }
                    });
                }
            }
            return result.AsEnumerable();
        }
    }
}
