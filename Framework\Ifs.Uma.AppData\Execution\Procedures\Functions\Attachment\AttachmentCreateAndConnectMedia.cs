﻿using System;
using System.IO;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Localization;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Attachment
{
    internal sealed class AttachmentCreateAndConnectMedia : AttachmentFunction
    {
        public const string FunctionName = "CreateAndConnectMedia";

        public AttachmentCreateAndConnectMedia()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string luName = parameters[0].GetString();
            string keyRef = parameters[1].GetString();

            if (luName == null || keyRef == null)
            {
                return null;
            }

            return CreateAndConnectMedia(context, luName, keyRef, null, null, null);
        }

        internal static object CreateAndConnectMedia(ProcedureContext context, string luName, string keyRef, string name, double? latitude, double? longitude)
        {
            MediaHandler.CreateAndConnectMedia(context.DbDataContext, luName, keyRef, name, name, name, latitude, longitude,
                out MediaLibrary mediaLibrary, out MediaLibraryItem mediaLibraryItem, out MediaItem mediaItem, out bool createdMediaLibrary);

            if (createdMediaLibrary)
            {
                context.DataChangeSet.AddRow(context.Metadata.MetaModel, mediaLibrary);
            }

            context.DataChangeSet.AddRow(context.Metadata.MetaModel, mediaLibraryItem);

            FndMediaKeys keys = new FndMediaKeys();
            //For backward compatibility with backends not supporting connect media
            if (mediaItem != null)
            {
                keys.ItemId = mediaItem.ItemId;
                context.DataChangeSet.AddRow(context.Metadata.MetaModel, mediaItem);
            }
            else
            {
                keys.ItemId = mediaLibraryItem.ItemId;
            }

            return keys;
        }
    }

    internal sealed class AttachmentCreateAndConnectMedia3 : AttachmentFunction
    {
        public AttachmentCreateAndConnectMedia3()
            : base(AttachmentCreateAndConnectMedia.FunctionName, 3)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string luName = parameters[0].GetString();
            string keyRef = parameters[1].GetString();

            if (luName == null || keyRef == null)
            {
                return null;
            }

            string name = parameters[2].GetString();

            return AttachmentCreateAndConnectMedia.CreateAndConnectMedia(context, luName, keyRef, name, null, null);
        }
    }

    internal sealed class AttachmentCreateAndConnectBinaryMedia : AttachmentFunction
    {
        public const string FunctionName = "CreateAndConnectBinaryMedia";

        private readonly IMediaHandler _mediaHandler;

        public AttachmentCreateAndConnectBinaryMedia(IMediaHandler mediaHandler)
            : base(FunctionName, 4)
        {
            _mediaHandler = mediaHandler;
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string luName = parameters[0].GetString();
            string keyRef = parameters[1].GetString();
            byte[] data = parameters[2].GetByteArray();
            string title = parameters[3].GetString();

            if (_mediaHandler == null || luName == null || keyRef == null || data == null)
            {
                return null;
            }

            if (!IsValidTitle(title))
                title = GetTitle();

            string fileName = title.Contains(".") ? title : GetFileName(title);
            MemoryStream stream = new MemoryStream(data);

            return Execute(context, luName, keyRef, title, fileName, stream);
        }

        private object Execute(ProcedureContext context, string luName, string keyRef, string title, string fileName, MemoryStream stream)
        {
            MediaInfo mediaInfo = _mediaHandler.AddBinaryMedia(context.DbDataContext, luName, keyRef, title, title, fileName, stream);
            FndMediaKeys mediaKeys = new FndMediaKeys();
            mediaKeys.ItemId = mediaInfo?.MediaItem?.ItemId;
            return mediaKeys;
        }

        private bool IsValidTitle(string title)
        {
            return !string.IsNullOrEmpty(title);
        }

        private string GetTitle()
        {
            string timestamp = GetTimestamp();
            string title = $"{Strings.Image}_{timestamp}";
            return title;
        }

        private string GetTimestamp()
        {
            return DateTime.Now.ToString("yyyy-dd-MM-HH-mm-ss");
        }

        private string GetFileName(string title)
        {
            return $"{title}.jpg";
        }
    }
}
