﻿using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using Ifs.Uma.AppData.StringExpressions;

namespace Ifs.Uma.AppData.Expressions
{
    public enum IfsExpressionType
    {
        VarAccess,
        In,
        Column,
        Method,
        Like,
        Query,
        UnionSelect,
        From,
        Join,
        Sort,
        ResultColumn,
        DbColumnSpec,
        ApiMethod,
        DbSelectSpec,
        Exists
    }

    public abstract partial class IfsExpression : Expression
    {
        public override ExpressionType NodeType => ExpressionType.Extension;

        public abstract IfsExpressionType IfsNodeType { get; }
        
        public static Expression FromString(string expression)
        {
            if (string.IsNullOrWhiteSpace(expression))
            {
                return null;
            }

            TextReader reader = new StringReader(expression);
            Lexer lexer = new Lexer(reader);
            Parser parser = new Parser(lexer);
            return parser.ToExpession();
        }

        protected override Expression Accept(ExpressionVisitor visitor)
        {
            IfsExpressionVisitor ifsVisitor = visitor as IfsExpressionVisitor;
            return ifsVisitor != null ? Accept(ifsVisitor) : base.Accept(visitor);
        }

        protected virtual Expression Accept(IfsExpressionVisitor visitor)
        {
            return base.Accept(visitor);
        }

        protected static ReadOnlyCollection<T> ToReadOnly<T>(IEnumerable<T> collection)
        {
            ReadOnlyCollection<T> roc = collection as ReadOnlyCollection<T>;
            if (roc == null)
            {
                if (collection == null)
                {
                    roc = EmptyReadOnlyCollection<T>.Empty;
                }
                else
                {
                    roc = new ReadOnlyCollection<T>(collection.ToList());
                }
            }
            return roc;
        }

        private static class EmptyReadOnlyCollection<T>
        {
            internal static readonly ReadOnlyCollection<T> Empty = new ReadOnlyCollection<T>(new List<T>());
        }

        public static Expression Value(object value)
        {
            return Expression.Constant(new DynamicValue(value));
        }
    }
}
