$locationfile = "..\Ifs.Uma.System.Tests\AppInfo.cs"
$patterndomain = '\[SERVER-URL-DO-NOT-MODIFY\]'
$patternusername = '\[USERNAME-DO-NOT-MODIFY]'
$patternpassword = '\[PASSWORD-DO-NOT-MODIFY]'
$patternauthtype = '\[AUTH-TYPE-DO-NOT-MODIFY\]'
$serverurl = "https://$env:DOMAIN_NAME"
$username = "$env:USERNAME"
$password = "$env:PASSWORD"
$authtype = "$env:AUTH_TYPE"

Write-Output "============ Setting Build Parameters ============"

(Get-Content $locationfile) -replace $patterndomain , $serverurl | Set-Content $locationfile
(Get-Content $locationfile) -replace $patternusername , $username | Set-Content $locationfile
(Get-Content $locationfile) -replace $patternpassword , $password | Set-Content $locationfile
(Get-Content $locationfile) -replace $patternauthtype , $authtype | Set-Content $locationfile

Write-Output "============ End - Setting Build Parameters ============"