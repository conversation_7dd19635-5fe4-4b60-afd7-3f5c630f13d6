﻿using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using IQToolkit.Data.Common;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Ifs.Uma.Data.Tests
{
    [TestFixture]
    public class MappingSourceTests
    {
        protected interface IT1
        {
            int T { get; }
        }

        protected class T1 : IT1
        {
            public T1(int t)
            { 
                T = t;
                C++;
            }

            public int T { get; private set; }

            public static int C { get; private set; }
        }

        [Test]
        public void TestTypeInfo()
        {
            Type[] types = new Type[] { typeof(IT1), typeof(T1), typeof(IQueryable), typeof(IOrderedQueryable), typeof(IQueryable<>), typeof(IOrderedQueryable<>) };
            foreach (Type type in types)
            {
                TypeInfo typeInfo = type.GetTypeInfo();
                Type def = typeInfo.IsGenericType ? type.GetGenericTypeDefinition() : null;
            }
        }

        [Test]
        public void TestMappingSourceMappingEntity()
        {
            MappingSource mappingSource = new AttributeMappingSource(null, null, null);
            IMetaModel metaModel = mappingSource.GetModel(typeof(DataContext));
            IMetaTable mappingEntity = metaModel.GetEntity(typeof(Transition));
            var mappedMembers = mappingEntity.DataMembers;
            Assert.IsTrue(mappedMembers.Count() > 0);
        }

        [Test]
        public void TestSql()
        {
            IQueryable<TransitionRow> transitions = new List<TransitionRow>().AsQueryable<TransitionRow>();

            var query = from t in transitions
                    where t.SyncState == SyncState.Sent
                    select t;

            string sql = TestHelpers.GetSql(query);
            Assert.AreEqual(sql, "SELECT t0.row_id, t0.transition_id, t0.modified_row_id, t0.sync_state, " +
                "t0.projection_name, t0.table_name, t0.entity_set_name, t0.array_source, t0.transaction_group, t0.operation, t0.error_code, " +
                "t0.error_message, t0.error_detail, t0.primary_key_string, t0.session_id" +
                " FROM fnd$transition_row t0 WHERE (t0.sync_state=@p0)");
        }

        [Test]
        public void TestLikeSql()
        {
            IQueryable<TransitionRow> transitions = new List<TransitionRow>().AsQueryable<TransitionRow>();

            var query = transitions.Where(x => x.ErrorMessage.StartsWith("hello")).Select(x => x.RowId);
            string sql = TestHelpers.GetSql(query);
            Assert.AreEqual(sql, "SELECT t0.row_id FROM fnd$transition_row t0 WHERE t0.error_message LIKE @p0+'%'");

            query = transitions.Where(x => x.ErrorMessage.EndsWith("hello")).Select(x => x.RowId);
            sql = TestHelpers.GetSql(query);
            Assert.AreEqual(sql, "SELECT t0.row_id FROM fnd$transition_row t0 WHERE t0.error_message LIKE '%'+@p0");

            query = transitions.Where(x => x.ErrorMessage.Contains("hello")).Select(x => x.RowId);
            sql = TestHelpers.GetSql(query);
            Assert.AreEqual(sql, "SELECT t0.row_id FROM fnd$transition_row t0 WHERE t0.error_message LIKE '%'+@p0+'%'");
        }

        [Test]
        public void TestJoinSql()
        {
            IQueryable<TransitionRow> transitions = new List<TransitionRow>().AsQueryable<TransitionRow>();
            IQueryable<TransitionRowField> transitionchanges = new List<TransitionRowField>().AsQueryable<TransitionRowField>();

            var query = from t in transitions
                        join tc in transitionchanges on t.RowId equals tc.TransitionRowId
                        where t.SyncState == SyncState.Sent
                        select new Wrap { A = t, B = tc };

            string sql = TestHelpers.GetSql(query);
            Assert.IsNotNull(sql);
        }

        private class Wrap
        {
            public TransitionRow A { get; set; }
            public TransitionRowField B { get; set; }
        }

        [Test]
        public void TestQuery()
        {
            IQueryable<TransitionRow> transitions = new List<TransitionRow>().AsQueryable<TransitionRow>();
            IQueryable<TransitionRowField> transitionchanges = new List<TransitionRowField>().AsQueryable<TransitionRowField>();

            var query = from t in transitions
                        join tc in transitionchanges on t.RowId equals tc.TransitionRowId
                        where t.SyncState == SyncState.Sent
                        select new Wrap { A = t, B = tc };

            Assert.IsTrue(TestHelpers.Execute(query));
        }

        private class TestSqlBuilder : SqlBuilder
        {
        }

        //[Test]
        //public void TestDataContext()
        //{
        //    TestSqlBuilder sqlBuilder = new TestSqlBuilder();
        //    MappingSource mappingSource = new AttributeMappingSource(null, null, null, null);
        //    IMetaModel metaModel = mappingSource.GetModel(typeof(DataContext));
        //    DataContext ctx = new DataContext(null, mappingSource);
        //    var result = ctx.Transitions.Where(x => x.SynSuccessful == true).ToList();
        //}
    }
} 
