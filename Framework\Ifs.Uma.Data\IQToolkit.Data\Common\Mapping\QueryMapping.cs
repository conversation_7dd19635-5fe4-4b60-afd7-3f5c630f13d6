﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace IQToolkit.Data.Common
{
    internal struct EntityInfo : IEquatable<EntityInfo>
    {
        object instance;
        IMetaTable table;

        public EntityInfo(object instance, IMetaTable table)
        {
            this.instance = instance;
            this.table = table;
        }

        public object Instance
        {
            get { return this.instance; }
        }

        public IMetaTable Table
        {
            get { return this.table; }
        }

        public bool Equals(EntityInfo other)
        {
            return this.table == other.table && object.Equals(this.instance, other.instance);
        }

        public override bool Equals(object obj)
        {
            return obj is EntityInfo && Equals((EntityInfo)obj);
        }

        public override int GetHashCode()
        {
            return (table != null ? table.GetHashCode() : 0) ^ (instance != null ? instance.GetHashCode() : 0);
        }

        public static bool operator ==(EntityInfo a, EntityInfo b)
        {
            return a.Equals(b);
        }

        public static bool operator !=(EntityInfo a, EntityInfo b)
        {
            return !a.Equals(b);
        }
    }

    internal abstract class QueryMapper
    {
        public abstract IMetaModel Model { get; }
        public abstract QueryTranslator Translator { get; }

        /// <summary>
        /// Get a query expression that selects all entities from a table
        /// </summary>
        public abstract ProjectionExpression GetQueryExpression(IMetaTable entity);

        /// <summary>
        /// Gets an expression that constructs an entity instance relative to a root.
        /// The root is most often a TableExpression, but may be any other experssion such as
        /// a ConstantExpression.
        /// </summary>
        /// <param name="root"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public abstract EntityExpression GetEntityExpression(Expression root, IMetaTable entity);

        /// <summary>
        /// Get an expression for a mapped property relative to a root expression. 
        /// The root is either a TableExpression or an expression defining an entity instance.
        /// </summary>
        /// <param name="root"></param>
        /// <param name="entity"></param>
        /// <param name="member"></param>
        /// <returns></returns>
        public abstract Expression GetMemberExpression(Expression root, IMetaTable entity, IMetaDataMember member);

        /// <summary>
        /// Recreate the type projection with the additional members included
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="fnIsIncluded"></param>
        /// <returns></returns>
        public abstract EntityExpression IncludeMembers(EntityExpression entity, Func<IMetaDataMember, bool> fnIsIncluded);

        /// <summary>
        /// </summary>
        /// <returns></returns>
        public abstract bool HasIncludedMembers(EntityExpression entity);

        /// <summary>
        /// Apply mapping to a sub query expression
        /// </summary>
        /// <param name="expression"></param>
        /// <returns></returns>
        public virtual Expression ApplyMapping(Expression expression)
        {
            return QueryBinder.Bind(this, expression);
        }

        /// <summary>
        /// Apply mapping translations to this expression
        /// </summary>
        /// <param name="expression"></param>
        /// <returns></returns>
        public virtual Expression Translate(Expression expression)
        {
            // convert references to LINQ operators into query specific nodes
            expression = QueryBinder.Bind(this, expression);

            // move aggregate computations so they occur in same select as group-by
            expression = AggregateRewriter.Rewrite(expression);

            // do reduction so duplicate association's are likely to be clumped together
            expression = UnusedColumnRemover.Remove(expression);
            expression = RedundantColumnRemover.Remove(expression);
            expression = RedundantSubqueryRemover.Remove(expression);
            expression = RedundantJoinRemover.Remove(expression);

            // convert references to association properties into correlated queries
            var bound = RelationshipBinder.Bind(expression);
            if (bound != expression)
            {
                expression = bound;
                // clean up after ourselves! (multiple references to same association property)
                expression = RedundantColumnRemover.Remove(expression);
                expression = RedundantJoinRemover.Remove(expression);
            }

            // rewrite comparision checks between entities and multi-valued constructs
            expression = ComparisonRewriter.Rewrite(expression);

            return expression;
        }
    }
}
