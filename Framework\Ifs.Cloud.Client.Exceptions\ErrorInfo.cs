﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  2012-09-28 PKULLK Created.
#endregion

using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Ifs.Cloud.Client.Exceptions
{
    /// <summary>
    /// Encapsulates information of an error.
    /// </summary>
    [DataContract]
    public class ErrorInfo : IEquatable<ErrorInfo>
    {
        #region Fields
        private static int idSequence;
        private int _id;
        #endregion

        #region Initialization
        
        /// <summary>
        /// Creates an empty ErrorInfo object.
        /// </summary>
        /// <remarks>
        /// This is only for the serializer and should never be invoked directly.
        /// </remarks>
        public ErrorInfo()
        {
        }

        /// <summary>
        /// Creates an errorInfo object.
        /// </summary>
        /// <param name="message">A user-friendly error message</param>
        /// <param name="directive">Informs user what actions to take upon this error</param>
        /// <param name="cause">Cause of this error</param>
        public ErrorInfo(string message, string directive, string cause)
            : this(message, directive, cause, string.Empty, string.Empty)
        {
        }

        /// <summary>
        /// Creates an errorInfo object.
        /// </summary>
        /// <param name="message">A user-friendly error message</param>
        /// <param name="directive">Informs user what actions to take upon this error</param>
        /// <param name="cause">Cause of this error</param>
        /// <param name="e">The Exception object that represents this error</param>
        public ErrorInfo(string message, string directive, string cause, Exception e)
            : this(message, directive, cause, e.GetType().FullName, e.StackTrace)
        {
        }

        /// <summary>
        /// Creates an errorInfo object.
        /// </summary>
        /// <param name="message">A user-friendly error message</param>
        /// <param name="directive">Informs user what actions to take upon this error</param>
        /// <param name="e">The Exception object that represents this error</param>        
        public ErrorInfo(string message, string directive, Exception e)
            : this(message, directive, GetCause(e), e.GetType().FullName, e.StackTrace)
        {
        }

        /// <summary>
        /// Creates an errorInfo object.
        /// </summary>
        /// <param name="message">A user-friendly error message</param>
        /// <param name="directive">Informs user what actions to take upon this error</param>
        /// <param name="cause">Cause of this error</param>
        /// <param name="errorType">Type of this error</param>
        /// <param name="stackTrace">Stack Trace</param>        
        public ErrorInfo(string message, string directive, string cause, string errorType, string stackTrace)
            : this(message, directive, cause, errorType, stackTrace, idSequence++)
        {
        }
        
        private ErrorInfo(string message, string directive, string cause, string errorType, string stackTrace, int id)
        {
#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
            this.Message           = message;
            this.Directive         = directive;
            this.Cause             = cause;
            this.ErrorType         = errorType;
            this.StackTrace        = stackTrace;            
            this.Time              = DateTime.Now;
            this._id                = id;
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row
        }
        #endregion

        #region Persistent properties
        
        /// <summary>
        /// A user-friendly error message.
        /// </summary>
        [DataMember]
        public string Message { get; set; }

        /// <summary>
        /// Informs user what actions to take upon this error.
        /// </summary>
        [DataMember]
        public string Directive { get; set; }

        /// <summary>
        /// Cause of this error.
        /// </summary>
        [DataMember]
        public string Cause { get; set; }

        /// <summary>
        /// Type of this error.
        /// </summary>
        [DataMember]
        public string ErrorType { get; set; }

        /// <summary>
        /// The Stack Trace.
        /// </summary>
        [DataMember]
        public string StackTrace { get; set; }

        /// <summary>
        /// The time error occured
        /// </summary>
        [DataMember]
        public DateTime Time { get; set; }

        /// <summary>
        /// Indicates whether this error has been read by the user or not.
        /// </summary>
        [DataMember]
        public bool ReadState { get; set; }
      
        #endregion
        
        #region Overrides
        
        /// <summary>
        /// Retrieves a string representation of this error info object.
        /// </summary>
        /// <returns>A string representation of this object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            AppendIfNotEmpty(sb, "Message: ",     Message);
            AppendIfNotEmpty(sb, "Directive: ",   Directive);
            AppendIfNotEmpty(sb, "Cause: ",       Cause);
            AppendIfNotEmpty(sb, "Error Type: ",  ErrorType);
            AppendIfNotEmpty(sb, "Stack Trace: ", StackTrace);
                        
            return sb.ToString();
        }
        #endregion

        #region Implementation methods

        private static void AppendIfNotEmpty(StringBuilder target, string label, string value)
        {
            if (!string.IsNullOrWhiteSpace(value))
            {
                target.Append(label).Append(value).AppendLine();
            }
        }

        private static string GetCause(Exception exception)
        {
            string cause = exception.Message;

            for (Exception e = exception; e != null; e = e.InnerException)
            {
                if (!string.IsNullOrWhiteSpace(e.Message))
                {
                    cause = e.Message;
                }
            }

            return cause;
        }
        #endregion

        #region IEquatable<ErrorInfo> implementation

        /// <summary>
        /// Compares this against another ErrorInfo object.
        /// </summary>
        /// <param name="other">Other ErrorInfo object to be compared against this</param>
        /// <returns>True if both are equal; false otherwise</returns>
        public bool Equals(ErrorInfo other)
        {
            return this._id == other._id;
        }
        #endregion
    }
}