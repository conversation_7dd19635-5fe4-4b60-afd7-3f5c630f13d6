﻿using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.AppData;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.UI.Trees.Nodes
{
    public class NodeState
    {
        private readonly IList<NodeConnection> _remainingConnections = new List<NodeConnection>();

        private readonly IList<NodeConnection> _allConnections = new List<NodeConnection>();

        public NodeState(IMetadata metadata, RecordData record, EntityDataSource dataSource, CpiTree tree, IEnumerable<CpiTreeConnection> connections)
        {
            if (connections == null)
            {
                return;
            }

            foreach (CpiTreeConnection connection in connections)
            {
                NodeConnection c = new NodeConnection(metadata, record, dataSource, tree, connection);
                if (c.IsOnlineOnly)
                {
                    HasOnlineOnlyConnection = true;
                }
                _allConnections.Add(c);
                _remainingConnections.Add(c);
            }

            IncrementConnection();
        }

        public bool HasOnlineOnlyConnection { get; }

        private void IncrementConnection()
        {
            if (_remainingConnections.Any())
            {
                CurrentConnection = _remainingConnections.ElementAt(0);
                _remainingConnections.RemoveAt(0);
            }
            else
            {
                CurrentConnection = null;
            }
        }

        public NodeConnection CurrentConnection { get; private set; }

        private EntityQuery _nextQuery;

        public EntityQuery NextQuery
        {
            get
            {
                return _nextQuery;
            }
            set
            {
                _nextQuery = value;
                if (_nextQuery == null)
                {
                    IncrementConnection();
                }
            }
        }

        public bool AreMoreItemsToLoad
        {
            get
            {
                return NextQuery != null || CurrentConnection != null;
            }
        }

        public bool CouldHaveChildren
        {
            get
            {
                return _allConnections.Count > 0;
            }
        }

        public bool IsOnlineNodeExpandable(Node parentNode)
        {
            //On online entities we should let any nodes which
            //has a rootnode or a shellnode as parentnode expand on click
            //this method will return wheter an node is expandable or not whether depending on its parent
            return (parentNode is RootNode) || (parentNode is ShellNode);
        }
    }
}
