﻿using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Sync
{
    internal sealed class SyncClearTransactionSession : SyncFunction
    {
        public const string FunctionName = "ClearTransactionSession";

        public SyncClearTransactionSession()
            : base(FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string id = parameters[0].GetString();

            if (string.IsNullOrEmpty(id))
            {
                throw new ProcedureException($"SessionId parameter of {FunctionNamespace}.{FunctionName} cannot be null.");
            }

            FndTransactionSession existing = context.DbDataContext.TransactionSessions.FirstOrDefault(x => x.SessionId == id);

            if (existing == null)
            {
                Logger.Current.Error($"Transaction Session with ID '{id}' does not exist.");
                return false;
            }

            context.DbDataContext.TransactionSessions.DeleteOnSubmit(existing);

            Logger.Current.Information($"Transaction Session with ID '{existing.SessionId}' was deleted.");

            IEnumerable<TransitionRow> transitions = context.DbDataContext.TransitionRows.Where(x => x.SessionId == id);
            int counter = 0;
            foreach (TransitionRow transitionRow in transitions)
            {
                context.DbDataContext.TransitionRows.Attach(transitionRow);
                transitionRow.SessionId = null;
                counter++;
            }

            context.DbDataContext.SubmitChanges(false);

            if (counter == 0)
            {
                Logger.Current.Warning($"No Transitions found for Transaction Session '{existing.SessionId}'.");
                return false;
            }
            else
            {
                Logger.Current.Information($"{counter} Transition(s) were cleared from Transaction Session '{existing.SessionId}'.");
            }

            return true;
        }
    }
}
