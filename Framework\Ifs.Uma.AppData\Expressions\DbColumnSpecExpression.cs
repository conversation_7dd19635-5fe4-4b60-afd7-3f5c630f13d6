﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class DbColumnSpecExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.DbColumnSpec;
        public ISelectColumnSpec ColumnSpec { get; }
        public override Type Type => typeof(DynamicValue);

        internal DbColumnSpecExpression(ISelectColumnSpec columnSpec)
        {
            ColumnSpec = columnSpec ?? throw new ArgumentNullException(nameof(columnSpec));
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitDbColumnSpecExpression(this);
        }

        public override string ToString()
        {
            return SqlBuilder.BuildDebugGenericSql(ColumnSpec);
        }
    }

    public partial class IfsExpression
    {
        public static DbColumnSpecExpression DbColumnSpec(ISelectColumnSpec columnSpec)
        {
            return new DbColumnSpecExpression(columnSpec);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitDbColumnSpecExpression(DbColumnSpecExpression exp)
        {
            return exp;
        }
    }
}
