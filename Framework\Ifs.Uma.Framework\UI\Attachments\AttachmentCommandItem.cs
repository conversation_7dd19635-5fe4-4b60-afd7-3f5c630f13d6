﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public sealed class AttachmentCommandItem : CommandItem, IDisposable
    {
        private readonly IDocumentHandler _documentHandler;
        private readonly IMediaHandler _mediaHandler;
        private readonly INavigator _navigator;
        private readonly IExpressionRunner _expressionRunner;

        private PageData _pageData;
        public PageData PageData
        {
            get => _pageData;
            set => SetProperty(ref _pageData, value, OnPageDataChanged);
        }

        private ViewData _viewData;
        public ViewData ViewData
        {
            get => _viewData;
            set => SetProperty(ref _viewData, value, OnViewDataChanged);
        }

        private int _attachmentCount;
        public int AttachmentCount
        {
            get => _attachmentCount;
            private set => SetProperty(ref _attachmentCount, value);
        }

        private bool _calculateCount = true;
        public bool CalculateCount
        {
            get => _calculateCount;
            set => SetProperty(ref _calculateCount, value);
        }

        private CpiAttachments _attachmentsConfig;
        public CpiAttachments AttachmentsConfig
        {
            get => _attachmentsConfig;
            set => SetProperty(ref _attachmentsConfig, value, OnAttachmentsConfigChanged);
        }
        
        public UpdatingState UpdatingState { get; } = new UpdatingState();

        private Tuple<string, string> _loadedKey;

        public AttachmentCommandItem(IDocumentHandler documentHandler, IMediaHandler mediaHandler, INavigator navigator, IExpressionRunner expressionRunner)
        {
            _documentHandler = documentHandler;
            _mediaHandler = mediaHandler;
            _navigator = navigator;
            _expressionRunner = expressionRunner;

            Icon = IconUtils.Attachment;
            ShowAsAction = CommandPriority.Always;

            UpdatingState.IsAnythingUpdatingChanged += UpdatingState_IsAnythingUpdatingChanged;
        }

        private void UpdatingState_IsAnythingUpdatingChanged(object sender, EventArgs e)
        {
            OnDataChanged();
        }

        private void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            if (oldValue != null)
            {
                oldValue.PropertyChanged -= PageData_PropertyChanged;
            }

            if (newValue != null)
            {
                newValue.PropertyChanged += PageData_PropertyChanged;
            }

            ViewData = newValue?.DefaultViewData;
        }

        private void PageData_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PageData.HasChanges))
            {
                UpdateIsEnabled();
            }
        }

        private void OnViewDataChanged(ViewData oldValue, ViewData newValue)
        {
            if (oldValue?.Record != null)
            {
                oldValue.Record.DataChanged -= Record_DataChanged;
            }

            if (newValue?.Record != null)
            {
                newValue.Record.DataChanged += Record_DataChanged;
            }

            OnDataChanged();
        }

        private void Record_DataChanged(object sender, EventArgs e)
        {
            OnDataChanged();
        }

        private void OnAttachmentsConfigChanged(CpiAttachments oldValue, CpiAttachments newValue)
        {
            OnDataChanged();
        }

        private void OnDataChanged()
        {
            bool enabled = _expressionRunner.RunCheck(AttachmentsConfig?.OfflineEnabled ?? AttachmentsConfig?.Enabled, ViewData, true);

            if (!enabled || !RecordData.HasLoadedExistingRecord(ViewData?.Record))
            {
                _loadedKey = null;
                AttachmentCount = 0;
                IsEnabled = false;
                IsVisible = false;
                return;
            }

            UpdateIsEnabled();

            Tuple<string, string> key = GetKey();
            if (!Equals(_loadedKey, key))
            {
                _loadedKey = key;

                _ = UpdateStates();
            }
        }

        private Tuple<string, string> GetKey()
        {
            ObjPrimaryKey key = ViewData?.Record?.ToPrimaryKey();
            string keyRef = key?.ToKeyRef();

            if (keyRef != null)
            {
                string entityName = ViewData.Record.EntityName;

                // Get the name of the LU connected if there is any
                // This can only be set in Marble for queries
                CpiEntity entity = ViewData.Record.Metadata.FindEntity(ViewData.Record.ProjectionName, ViewData.Record.EntityName);
                EntitySyncPolicy syncPolicy = ViewData.Record.Metadata.GetEntitySyncPolicy(ViewData.Record.EntityName);
                if (syncPolicy == EntitySyncPolicy.None && !string.IsNullOrEmpty(entity?.LuName))
                {
                    entityName = entity.LuName;
                }

                return Tuple.Create(entityName, keyRef);
            }

            return null;
        }

        public async Task UpdateStates()
        {
            Tuple<string, string> key = _loadedKey;

            if (key == null)
            {
                IsVisible = false;
                AttachmentCount = 0;
                return;
            }

            try
            {
                bool mediaAllowed = await _mediaHandler.IsEnabledFor(key.Item1);
                bool documentAllowed = await _documentHandler.IsEnabledFor(key.Item1);
                IsVisible = mediaAllowed || documentAllowed;

                UpdateIsEnabled();

                if (CalculateCount)
                {
                    int mediaCount = mediaAllowed ? await _mediaHandler.GetMediaCountAsync(key.Item1, key.Item2) : 0;
                    int documentCount = documentAllowed ? await _documentHandler.GetDocumentCountAsync(key.Item1, key.Item2) : 0;

                    AttachmentCount = mediaCount + documentCount;
                }
                else
                {
                    AttachmentCount = 0;
                }
            }
            catch (Exception)
            {
                IsVisible = false;
                AttachmentCount = 0;
            }
        }

        private void UpdateIsEnabled()
        {
            bool hasChanges = PageData?.HasChanges == true || ViewData.Record.HasChanges;

            IsEnabled = IsVisible && !hasChanges && !ViewData.Record.IsNew() && !UpdatingState.IsAnythingUpdating;
        }

        protected override async Task OnExecuteAsync()
        {
            if (_loadedKey != null && IsEnabled && IsVisible)
            {
                AttachmentNavParam navParam = new AttachmentNavParam(null, _loadedKey.Item1, _loadedKey.Item2, true); // Page name as null will show the page with both media and documents tabs
                await _navigator.NavigateToAsync(FrameworkLocations.Attachments, navParam);
            }
        }

        #region IDisposable Support

        private bool _disposed;

        private void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                // Unsubscribe from events
                UpdatingState.IsAnythingUpdatingChanged -= UpdatingState_IsAnythingUpdatingChanged;

                if (PageData != null)
                {
                    PageData.PropertyChanged -= PageData_PropertyChanged;
                }

                if (ViewData != null)
                {
                    ViewData.Record.DataChanged -= Record_DataChanged;
                }
            }

            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~AttachmentCommandItem()
        {
            Dispose(false);
        }

        #endregion
    }
}
