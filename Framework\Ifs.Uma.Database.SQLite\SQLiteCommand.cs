﻿using System;
using System.Linq;
using System.Threading;
using Ifs.Uma.Utility;
using SQLitePCL;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Database.SQLite
{
    internal class SQLiteCommand : DbCommand
    {
        internal SQLiteCommand(SQLiteConnection parent, DbTransaction transaction, ILogger logger, bool traceFlag)
            : base(logger, traceFlag)
        {
            if (parent == null) throw new ArgumentNullException("parent");
            m_parent = parent;
            m_transaction = transaction;
            m_statements = null;
        }

        private SQLiteConnection m_parent;
        private DbTransaction m_transaction;
        private sqlite3_stmt[] m_statements;

        internal void DisposeReader(SQLiteDataReader reader)
        {
            ReaderDisposed(reader);
        }

        private void FinalizeStatements()
        {
            sqlite3_stmt[] statements = Interlocked.Exchange(ref m_statements, null);
            SQLiteHelper.FinalizeStatements(statements, false);
        }

        public override DbConnection Connection { get { return m_parent; } }
        public override DbTransaction Transaction { get { return m_transaction; } }

        protected override DbParameter NewParameter(string id, TypeCode parameterCode, IMetaEnumeration enumeration, int valueIndex)
        {
            return new SQLiteParameter(id, parameterCode, enumeration, valueIndex);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                SQLiteConnection parent = Interlocked.Exchange(ref m_parent, null);
                if (parent != null)
                {
                    parent.DisposeCommand(this);
                }
            }
            FinalizeStatements();
            base.Dispose(disposing);
        }

        protected override bool DoPrepare()
        {
            bool result = true;
            SQLiteConnection parent = m_parent;
            if (parent == null) throw new ObjectDisposedException("SQLiteCommand");
            FinalizeStatements();
            if (m_transaction == null)
            {
                parent.EnterReadLock();
                try
                {
                    m_statements = parent.PrepareCommand(CommandText);
                    if (TraceFlag)
                    {
                        Logger.Trace(ToString(false));
                    }
                    result = false;
                }
                finally
                {
                    parent.ExitReadLock();
                }
            }
            else
            {
                m_statements = parent.PrepareCommand(CommandText);
            }
            return result;
        }

        protected override void NeedPrepare()
        {
            base.NeedPrepare();
            FinalizeStatements();
        }

        private void BindParameters()
        {
            sqlite3_stmt[] statements = m_statements;
            if (statements != null && Parameters.Any(x => x != null))
            {
                foreach (sqlite3_stmt statement in statements)
                {
                    if (statement == null) continue;
                    foreach (DbParameter p in Parameters)
                    {
                        if (p == null) continue;
                        int index = raw.sqlite3_bind_parameter_index(statement, p.Name);
                        if (index > 0)
                        {
                            BindParameter(statement, p, index);
                        }
                        //JVB: Not finding a parameter name in a statement is valid.
                        //JVB: The name may be found in another part of a multi-statement command.
                        //else
                        //{
                        //    BindParameterError(p, new SQLiteException("index not found"));
                        //}
                    }
                }
            }
        }

        private void BindParameter(sqlite3_stmt statement, DbParameter p, int index)
        {
            int r = raw.SQLITE_OK;
            if (p.Value == null)
            {
                r = raw.sqlite3_bind_null(statement, index);
            }
            else
            {
                switch (p.ValueCode)
                {
                    case TypeCode.String:
                    case TypeCode.Enumeration: // LocalValue already converted to ServerValue string
                        string s = (string)p.Value;
                        r = raw.sqlite3_bind_text(statement, index, s);
                        break;
                    case TypeCode.Int16:
                        r = raw.sqlite3_bind_int(statement, index, (short)p.Value);
                        break;
                    case TypeCode.Int32:
                        r = raw.sqlite3_bind_int(statement, index, (int)p.Value);
                        break;
                    case TypeCode.Int64:
                        r = raw.sqlite3_bind_int64(statement, index, (long)p.Value);
                        break;
                    case TypeCode.Boolean:
                        r = raw.sqlite3_bind_int(statement, index, (bool)p.Value ? 1 : 0);
                        break;
                    case TypeCode.ByteArray:
                        byte[] bytes = (byte[])p.Value;
                        r = raw.sqlite3_bind_blob(statement, index, bytes);
                        break;
                    case TypeCode.DateTime:
                        r = raw.sqlite3_bind_int64(statement, index, ((DateTime)p.Value).Ticks);
                        break;
                    case TypeCode.Decimal:
                        r = raw.sqlite3_bind_double(statement, index, (double)(decimal)p.Value);
                        break;
                    case TypeCode.Double:
                        r = raw.sqlite3_bind_double(statement, index, (double)p.Value);
                        break;
                    case TypeCode.Guid:
                        string guid = ((Guid)p.Value).ToString("D");
                        r = raw.sqlite3_bind_text(statement, index, guid);
                        break;
                    default:
                        BindParameterError(p, new SQLiteException("valueCode not supported"));
                        break;
                }
            }
            if (r != raw.SQLITE_OK)
            {
                BindParameterError(p, new SQLiteException(r));
            }
        }

        protected override int DoExecuteNonQuery()
        {
            SQLiteConnection parent = m_parent;
            sqlite3_stmt[] statements = m_statements;
            if (statements == null || parent == null)
                throw new ObjectDisposedException("SQLiteCommand");
            BindParameters();
            sqlite3_stmt statement = ExecuteInitialStatements(parent, statements);
            return ExecuteStatement(parent, statement, true);
        }

        private sqlite3_stmt ExecuteInitialStatements(SQLiteConnection parent, sqlite3_stmt[] statements)
        {
            sqlite3_stmt result = null;
            if (parent != null && statements != null && statements.Length > 0)
            {
                int n = statements.Length - 1;
                for (int i = 0; i < n; i++)
                {
                    ExecuteStatement(parent, statements[i], false);
                }
                result = statements[n];
            }
            return result;
        }

        private int ExecuteStatement(SQLiteConnection parent, sqlite3_stmt statement, bool getChanges)
        {
            int result = 0;
            if (statement != null && parent != null)
            {
                if (m_transaction == null)
                {
                    parent.EnterReadLock();
                }

                try
                {
                    int r = raw.sqlite3_step(statement);
                    if (getChanges && r == raw.SQLITE_DONE)
                    {
                        result = parent.GetChanges();
                    }

                    raw.sqlite3_reset(statement);

                    if (r != raw.SQLITE_DONE)
                    {
                        throw new SQLiteException(r, parent.GetErrorMessage());
                    }
                }
                finally
                {   
                    if (m_transaction == null)
                    {
                        parent.ExitReadLock();
                    }
                }
            }
            return result;
        }

        protected override DbDataReader DoExecuteReader(string sql)
        {
            SQLiteConnection parent = m_parent;
            sqlite3_stmt[] statements = m_statements;
            if (statements == null || parent == null)
                throw new ObjectDisposedException("SQLiteCommand");
            BindParameters();
            sqlite3_stmt statement = ExecuteInitialStatements(parent, statements);
            DbDataReader result = null;
            if (statement != null)
            {
                Interlocked.CompareExchange(ref m_statements, null, statements);
                SQLiteHelper.FinalizeStatements(statements, true);
                DbDataReader result2 = null;
                try
                {
                    result2 = new SQLiteDataReader(parent, this, statement, Logger, TraceFlag, sql, m_transaction);
                    NeedPrepare();
                    result = result2;
                    result2 = null;
                }
                finally
                {
                    if (result2 != null)
                    {
                        result2.Dispose();
                    }
                }
            }
            return result;
        }

        protected override object DoExecuteScalar()
        {
            SQLiteConnection parent = m_parent;
            sqlite3_stmt[] statements = m_statements;
            if (statements == null || parent == null)
                throw new ObjectDisposedException("SQLiteCommand");
            BindParameters();
            sqlite3_stmt statement = ExecuteInitialStatements(parent, statements);
            if (statement == null) return null;
            object result = null;

            if (m_transaction == null)
            {
                parent.EnterReadLock();
            }

            try
            {
                int r = raw.sqlite3_step(statement);
                if (r == raw.SQLITE_ROW)
                {
                    switch (raw.sqlite3_column_type(statement, 0))
                    {
                        case raw.SQLITE_TEXT:
                            result = raw.sqlite3_column_text(statement, 0);
                            break;
                        case raw.SQLITE_INTEGER:
                            result = raw.sqlite3_column_int64(statement, 0);
                            break;
                        case raw.SQLITE_FLOAT:
                            result = raw.sqlite3_column_double(statement, 0);
                            break;
                        case raw.SQLITE_BLOB:
                            result = raw.sqlite3_column_blob(statement, 0);
                            break;
                    }
                }

                raw.sqlite3_reset(statement);

                if (r != raw.SQLITE_ROW)
                {
                    throw new SQLiteException(r, parent.GetErrorMessage());
                }
            }
            finally
            {
                if (m_transaction == null)
                {
                    parent.ExitReadLock();
                }
            }
            
            return result;
        }

        protected override bool ValidateParameter(DbParameter parameter)
        {
            return parameter != null && parameter is SQLiteParameter;
        }
    }
}
