﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Ifs.Uma.Framework.UI.Elements.HtmlText
{
    public class HtmlTextElement : ElementBase
    {
        public string Html { get; set; }
        protected override BindingType BindingPropertyType => BindingType.Reference;

        protected override bool OnLoad()
        {
            Html = Content?.HtmlText.Text;
            return !string.IsNullOrWhiteSpace(Content?.HtmlText?.Text);
        }

        protected override void OnDataChanged()
        {
            base.OnDataChanged();
            Html = InterpolateString(Content?.HtmlText.Text);
        }
    }
}
