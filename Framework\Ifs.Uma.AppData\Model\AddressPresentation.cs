﻿using Ifs.Uma.Database;
using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_address_presentation", Columns = nameof(CountryCode), Unique = true)]

    public class AddressPresentation : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "address_presentation";

        #region Field Definitions

        private string _displayLayout;
        private string _editLayout;
        private FndBoolean? _defaultDisplayLayout;
        private FndBoolean? _defaultEditLayout;
        private string _countryCode;

        #endregion

        public AddressPresentation()
            : base(DbTableName)
        {
        }

        #region Property Definitions

        [Column(Storage = nameof(_displayLayout))]
        public string DisplayLayout
        {
            get => _displayLayout;
            set => SetProperty(ref _displayLayout, value);
        }

        [Column(Storage = nameof(_editLayout))]
        public string EditLayout
        {
            get => _editLayout;
            set => SetProperty(ref _editLayout, value);
        }

        [Column(Storage = nameof(_defaultDisplayLayout))]
        public FndBoolean? DefaultDisplayLayout
        {
            get => _defaultDisplayLayout;
            set => SetProperty(ref _defaultDisplayLayout, value);
        }

        [Column(Storage = nameof(_defaultEditLayout))]
        public FndBoolean? DefaultEditLayout
        {
            get => _defaultEditLayout;
            set => SetProperty(ref _defaultEditLayout, value);
        }

        [Column(Storage = nameof(_countryCode), Mandatory = true, ServerPrimaryKey = true)]
        public string CountryCode
        {
            get => _countryCode;
            set => SetProperty(ref _countryCode, value);
        }

        #endregion
    }
}
