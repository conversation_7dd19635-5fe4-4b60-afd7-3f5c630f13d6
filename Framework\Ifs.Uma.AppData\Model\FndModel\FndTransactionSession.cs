﻿using System.Linq;
using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndTransactionSession
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "transaction_session";

        [Column(PrimaryKey = true)]
        public string SessionId { get; set; }

        [Column]
        public bool? IsOpen { get; set; }

        public static string GetCurrentOpenSession(FwDataContext ctx)
        {
            return ctx.TransactionSessions.FirstOrDefault(x => x.IsOpen == true)?.SessionId ?? string.Empty;
        }
    }
}
