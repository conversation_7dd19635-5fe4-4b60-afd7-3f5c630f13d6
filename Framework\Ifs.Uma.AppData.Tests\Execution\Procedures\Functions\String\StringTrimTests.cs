﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringTrimTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringTrimTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase(" MyValue ", ExpectedResult = "MyValue")]
        [TestCase("#MyValue ", ExpectedResult = "#MyValue")]
        [TestCase(1, ExpectedResult = "1")]
        [TestCase(1.1, ExpectedResult = "1.1")]
        [TestCase(true, ExpectedResult ="True")]
        [TestCase(null, ExpectedResult = null)]
        public async Task<string> String_Trim(object input)
        {
            _params["TextInput"] = input;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Trim", _params);
            CheckResult(result);
            return result?.Value as string;
        }

        [Test]
        [TestCase(" MyValue ", null, ExpectedResult = null)]
        [TestCase("#MyValue ", "#", ExpectedResult = "MyValue ")]
        [TestCase("#MyValue#", "#", ExpectedResult = "MyValue")]
        [TestCase(1, " ", ExpectedResult = "1")]
        [TestCase(1.1, " ", ExpectedResult = "1.1")]
        [TestCase(true, " ", ExpectedResult = "True")]
        [TestCase(null, null, ExpectedResult = null)]
        public async Task<string> String_Trim2(object input, object trimParam)
        {
            _params["TextInput"] = input;
            _params["TrimParameter"] = trimParam;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Trim2", _params);
            CheckResult(result);
            return result?.Value as string;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
