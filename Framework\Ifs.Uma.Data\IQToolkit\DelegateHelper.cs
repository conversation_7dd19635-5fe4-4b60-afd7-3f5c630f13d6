﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Data.IQToolkit
{
    internal static class DelegateHelper
    {
        private static IReadOnlyDictionary<int, MethodInfo> _funcMethods = InitFuncMethods();

        private static IReadOnlyDictionary<int, MethodInfo> InitFuncMethods()
        {
            return typeof(DelegateHelper).GetRuntimeMethods()
                .Where(x => x.Name == "Func")
                .Where(x => x.IsPublic || x.IsStatic)
                .ToDictionary(m => m.GetGenericArguments().Length);
        }

        public static object CreateFunc(MethodInfo method, Type[] types)
        {
            MethodInfo funcMethod;
            if (_funcMethods.TryGetValue(types.Length, out funcMethod))
            {
                MethodInfo typedFuncMethod = funcMethod.MakeGenericMethod(types);
                return typedFuncMethod.Invoke(null, new object[] { method });
            }
            else
            {
                throw new ArgumentException("No Func method available for given number of types");
            }
        }

        public static Func<R> Func<R>(MethodInfo method)
        {
            return () => (R)method.Invoke(null, null);
        }

        public static Func<T, R> Func<T, R>(MethodInfo method)
        {
            if (method.IsStatic)
            {
                return x => (R)method.Invoke(null, new object[] { x });
            }
            else
            {
                return x => (R)method.Invoke(x, null);
            }
        }

        public static Func<T1, T2, R> Func<T1, T2, R>(MethodInfo method)
        {
            if (method.IsStatic)
            {
                return (x1, x2) => (R)method.Invoke(null, new object[] { x1, x2 });
            }
            else
            {
                if (method.DeclaringType.GetTypeInfo().IsAssignableFrom(typeof(global::IQToolkit.Data.Common.FieldReader).GetTypeInfo()))
                {
                    // Special case here to improve performance - if we are reading rows out of the database
                    // we use the same FieldReader for all rows so it is beneficial to cache a delegate 

                    Type funcType = typeof(Func<T2, R>);
                    return (x1, x2) =>
                    {
                        Func<T2, R> func = (Func<T2, R>)DelegateCache.GetDelegate(x1, method, funcType);
                        return func(x2);
                    };
                }
                else
                {
                    return (x1, x2) =>
                    {
                        return (R)method.Invoke(x1, new object[] { x2 });
                    };
                }
            }
        }

        private class DelegateCache
        {
            private static readonly System.Runtime.CompilerServices.ConditionalWeakTable<object, DelegateCache> _methodCache = new System.Runtime.CompilerServices.ConditionalWeakTable<object, DelegateCache>();
            private ConcurrentDictionary<MethodInfo, Delegate> _cache = new ConcurrentDictionary<MethodInfo, Delegate>();

            public static Delegate GetDelegate(object instance, MethodInfo method, Type type)
            {
                DelegateCache cache = _methodCache.GetValue(instance, obj => new DelegateCache());
                return cache.GetDelegateInner(instance, method, type);
            }

            private Delegate GetDelegateInner(object instance, MethodInfo method, Type type)
            {
                return _cache.GetOrAdd(method, obj =>
                {
                    return method.CreateDelegate(type, instance);
                });
            }
        }

        public static Func<T1, T2, T3, R> Func<T1, T2, T3, R>(MethodInfo method)
        {
            if (method.IsStatic)
            {
                return (x1, x2, x3) => (R)method.Invoke(null, new object[] { x1, x2, x3 });
            }
            else
            {
                return (x1, x2, x3) => (R)method.Invoke(x1, new object[] { x2, x3 });
            }
        }

        public static Func<T1, T2, T3, T4, R> Func<T1, T2, T3, T4, R>(MethodInfo method)
        {
            if (method.IsStatic)
            {
                return (x1, x2, x3, x4) => (R)method.Invoke(null, new object[] { x1, x2, x3, x4 });
            }
            else
            {
                return (x1, x2, x3, x4) => (R)method.Invoke(x1, new object[] { x2, x3, x4 });
            }
        }
    }
}
