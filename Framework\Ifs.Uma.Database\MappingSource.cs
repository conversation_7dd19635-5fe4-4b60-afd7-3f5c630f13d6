﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public interface IMapEnumeration
    {
        IMetaEnumeration GetEnumeration(Type enumType);
    }

    /// <summary>
    /// Each mapping source holds a set of models that it has read
    /// So only create one mapping source, if possible.
    /// </summary>
    public abstract class MappingSource : IMapEnumeration
    {
        protected MappingSource()
        {
            m_models = new LockedDictionary<Type, IMetaModel>();
            m_enums = new LockedDictionary<Type, IMetaEnumeration>();
        }

        public IMetaModel GetModel(Type dataContextType)
        {
            return m_models.GetOrAdd(dataContextType, CreateModel);
        }

        public IMetaEnumeration GetEnumeration(Type enumType)
        {
            return m_enums.GetOrAdd(enumType, CreateEnumeration);
        }

        protected IMetaEnumeration UpdateEnumeration(Type enumType, IMetaEnumeration serverEnumeration)
        {
            if (serverEnumeration == null)
            {
                return GetEnumeration(enumType);
            }

            return m_enums.AddOrUpdate(enumType, 
                (et) =>
                {
                    IMetaEnumeration localEnum = CreateEnumeration(et);
                    return localEnum == null ? null : MetaEnumeration.Create(localEnum, serverEnumeration);
                }, 
                (et, localEnum) => 
                {
                    return localEnum == null ? null : MetaEnumeration.Create(localEnum, serverEnumeration);
                });
        }

        public abstract ISystemData SystemData { get; }

        protected abstract IMetaModel CreateModel(Type dataContextType);
        protected abstract IMetaEnumeration CreateEnumeration(Type enumType);

        private LockedDictionary<Type, IMetaModel> m_models;
        private LockedDictionary<Type, IMetaEnumeration> m_enums;
    }
}
