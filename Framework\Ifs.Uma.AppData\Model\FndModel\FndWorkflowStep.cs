﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndWorkflowStep : RemoteRow
    {
        public const string DbTableName = "fnd_workflow_step";

        [Column]
        public double? Sequence { get; set; }

        [Column]
        public string Name { get; set; }

        public FndWorkflowStep()
            : base(DbTableName)
        {
        }
    }
}
