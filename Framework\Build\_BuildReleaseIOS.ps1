Write-Output "============ iOS"

# https://developer.xamarin.com/guides/cross-platform/ci/jen<PERSON>_walkthrough/
# https://developer.xamarin.com/guides/ios/getting_started/installation/windows/connecting-to-mac/
# https://developer.xamarin.com/guides/ios/deployment,_testing,_and_metrics/app_distribution/ipa_support/

$infoFile = "$($solutionDir)Ifs.Uma.Startup.iOS\Info.plist"
$info = (Get-Content -Raw $infoFile)
$info = $info -replace "(?m)(<key>CFBundleShortVersionString<\/key>[\r\n\s]+<string>)([^<]+)(<\/string>)" , "`${1}$appVersionShortNumber`$3"
$info = $info -replace "(?m)(<key>CFBundleVersion<\/key>[\r\n\s]+<string>)([^<]+)(<\/string>)" , "`${1}$appVersion`$3"
$info | Set-Content -NoNewline $infoFile

$appName = ""
if ($info -match "(?m)<key>CFBundleDisplayName<\/key>[\r\n\s]+<string>([^<]+)<\/string>")
{
    $appName = $matches[1]
}

$bundleIdentifier = ""
if ($info -match "(?m)<key>CFBundleIdentifier<\/key>[\r\n\s]+<string>([^<]+)<\/string>")
{
    $bundleIdentifier = $matches[1]
}

$entitlementsFile = "$($solutionDir)Ifs.Uma.Startup.iOS\Entitlements.plist"
$entitlements = (Get-Content -Raw $entitlementsFile)
$entitlements = $entitlements.Replace("<string>development</string>", "<string>production</string>")
$entitlements | Set-Content -NoNewline $entitlementsFile

$macIp = "nthmacmini1.ifsworld.com" # nthmacmini
$macUsername = "nthmacmini1" #build
$macPassword = "JMJ!&AJvouc3" #P3ngu!nW!ngs1

if ($env:IOS_CHOICES -eq "SIDELOAD" -or $env:IOS_CHOICES -eq "BOTH") 
{
    # Section for building sideload app
	
msbuild $solutionFile /t:Rebuild "/p:Configuration=Ad-Hoc" "/p:Platform=iPhone" `
    /p:ServerAddress=$macIp /p:ServerUser=$macUsername /p:ServerPassword=$macPassword `
    /m /nr:false /v:m /p:RestorePackages=false /p:IpaPackageDir=$deliverablesDir

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "Renaming In-House build..."

$inHouseIpaName =  ($deliverableName + "_" + $appVersion +".ipa")
Rename-Item -Path "$($deliverablesDir)IfsUmaStartupiOS.ipa" -NewName $inHouseIpaName

	# End section for building sideload app
}

if ($env:IOS_CHOICES -eq "STORE" -or $env:IOS_CHOICES -eq "BOTH") 
{
	# Section for building store app
	
if (![string]::IsNullOrEmpty($iOSAppStoreBundleId))
{
    Write-Output "============ AppStore build"
    
    $info = $info -replace "(?m)(<key>CFBundleIdentifier<\/key>[\r\n\s]+<string>)([^<]+)(<\/string>)" , "`${1}$iOSAppStoreBundleId`$3"
    $info | Set-Content -NoNewline $infoFile

    msbuild $solutionFile /t:Rebuild "/p:Configuration=AppStore" "/p:Platform=iPhone" `
    /p:ServerAddress=$macIp /p:ServerUser=$macUsername /p:ServerPassword=$macPassword `
    /m /nr:false /v:m /p:RestorePackages=false /p:IpaPackageDir=$deliverablesDir
    
    if ($LastExitCode -ne 0) { Exit $LastExitCode }
    
    Write-Output "Renaming AppStore build..."
    
    $appStoreIpaName =  ($deliverableName + "_" + $appVersion +"_AppStore.ipa")
    Rename-Item -Path "$($deliverablesDir)IfsUmaStartupiOS.ipa" -NewName $appStoreIpaName
    
    #Set bundle ID to original value so we don't commit that change into the repo. Otherwise the next build will fail
    $info = $info -replace "(?m)(<key>CFBundleIdentifier<\/key>[\r\n\s]+<string>)([^<]+)(<\/string>)" , "`${1}$bundleIdentifier`$3"
    $info | Set-Content -NoNewline $infoFile
    
    Write-Output "============ End - AppStore build"
}

	# End of section for building store app
}

Copy-Item "$($solutionDir)\Ifs.Uma.Startup.iOS\Deployment\*" $deliverablesDir

$installInfoFile = "$($deliverablesDir)App.plist"
$installInfo = (Get-Content -Raw $installInfoFile)
$installInfo = $installInfo -replace "%BUNDLE_VERSION%" , "$appVersion"
$installInfo = $installInfo -replace "%IPA_FOLDER%" , ($publishUrl + $deliverableName + "_" + $appVersion)
$installInfo = $installInfo -replace "%IPA_NAME%" , "$inHouseIpaName"
$installInfo = $installInfo -replace "%BUNDLE_IDENTIFIER%" , "$bundleIdentifier"
$installInfo = $installInfo -replace "%APP_NAME%" , "$appName"
$installInfo | Set-Content -NoNewline $installInfoFile

#Produce template plist so the download link can be auto-generated by the TAS when it's put in the Downloads folder
$templateInfoFile = "$($deliverablesDir)App.template.plist"
$templateInfo = (Get-Content -Raw $templateInfoFile)
$templateInfo = $templateInfo -replace "%BUNDLE_VERSION%" , "$appVersion"
$templateInfo = $templateInfo -replace "%BUNDLE_IDENTIFIER%" , "$bundleIdentifier"
$templateInfo = $templateInfo -replace "%APP_NAME%" , "$appName"
$templateInfo | Set-Content -NoNewline $templateInfoFile
$plistName =  ($deliverableName + "_" + $appVersion +".template.plist")
Rename-Item -Path "$($deliverablesDir)App.template.plist" -NewName $plistName

Write-Output "============ End - iOS"