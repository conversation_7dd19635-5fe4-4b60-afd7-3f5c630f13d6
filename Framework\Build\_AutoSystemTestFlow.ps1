$solutionDir = "../"

Write-Output "============ Flow Test"

Push-Location $solutionDir

packages\NUnit.ConsoleRunner.3.9.0\tools\nunit3-console.exe --x86 --labels=All --workers=1 "--result=TestFlowResult.xml;format=nunit2" `
	"Ifs.Uma.System.Flow.Tests\bin\Release\Ifs.Uma.System.Flow.Tests.dll"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Pop-Location

Write-Output "============ End - Flow Test"

