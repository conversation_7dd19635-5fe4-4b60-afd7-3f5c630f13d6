﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Extended node types for custom expressions
    /// </summary>
    internal enum DbExpressionType
    {
        None,
        Table,
        ClientJoin,
        Column,
        Select,
        Projection,
        Entity,
        Join,
        Aggregate,
        Scalar,
        Exists,
        In,
        Grouping,
        AggregateSubquery,
        IsNull,
        Between,
        RowCount,
        NamedValue,
        OuterJoined,
        Batch,
        Function,
        Block,
        If,
        Declaration,
        Variable
    }

    //JVB: As a good .Net citizen we use Extension nodes as described in the
    // "Expression Trees V2" spec
    // http://www.codeplex.com/Download?ProjectName=dlr&DownloadId=246540
    internal abstract class DbExpression : Expression
    {
        protected DbExpression() { }

        public sealed override ExpressionType NodeType { get { return ExpressionType.Extension; } }
        public abstract DbExpressionType DbNodeType { get; }

        protected string DebugView
        {
            get
            {
#if DEBUG
                return ToString();
#else
                return null;
#endif
            }
        }

        public override string ToString()
        {
            return DbExpressionWriter.WriteToString(this);
        }
    }

    internal abstract class AliasedExpression : DbExpression
    {
        private TableAlias m_alias;
        protected AliasedExpression(TableAlias alias)
        {
            m_alias = alias;
        }
        public TableAlias Alias
        {
            get { return m_alias; }
        }
    }

    /// <summary>
    /// A custom expression node that represents a table reference in a SQL query
    /// </summary>
    internal class TableExpression : AliasedExpression
    {
        private IMetaTable m_entity;

        public TableExpression(TableAlias alias, IMetaTable entity)
            : base(alias)
        {
            if (entity == null) throw new ArgumentNullException("entity");
            m_entity = entity;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Table; } }
        public override Type Type { get { return typeof(void); } }

        public IMetaTable Entity { get { return m_entity; } }
        public string Name { get { return m_entity.TableName; } }

        public override string ToString()
        {
            return "T(" + Name + ")";
        }
    }

    internal class EntityExpression : DbExpression
    {
        private IMetaTable m_entity;
        private Expression m_expression;

        public EntityExpression(IMetaTable entity, Expression expression)
        {
            if (expression == null) throw new ArgumentNullException("expression");
            m_entity = entity;
            m_expression = expression;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Entity; } }
        public override Type Type { get { return m_expression.Type; } }

        public IMetaTable Entity { get { return m_entity; } }
        public Expression Expression { get { return m_expression; } }

        public EntityExpression Update(Expression expression)
        {
            return expression == Expression ? this : new EntityExpression(m_entity, expression);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression expression = visitor.Visit(m_expression);
            return Update(expression);
        }
    }

    /// <summary>
    /// A custom expression node that represents a reference to a column in a SQL query
    /// </summary>
    internal class ColumnExpression : DbExpression, IEquatable<ColumnExpression>
    {
        private TableAlias m_alias;
        private string m_name;
        private Type m_type;

        public ColumnExpression(Type type, TableAlias alias, string name)
        {
            if (type == null) throw new ArgumentNullException("type");
            if (name == null) throw new ArgumentNullException("name");
            m_type = type;
            m_alias = alias;
            m_name = name;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Column; } }
        public override Type Type { get { return m_type; } }

        public TableAlias Alias { get { return m_alias; } }
        public string Name { get { return m_name; } }

        public override string ToString()
        {
            return m_alias.ToString() + ".C(" + m_name + ")";
        }

        public override int GetHashCode()
        {
            return m_alias.GetHashCode() + m_name.GetHashCode();
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as ColumnExpression);
        }

        public bool Equals(ColumnExpression other)
        {
            return other != null
                && (((object)this) == (object)other
                 || (m_alias == other.m_alias && m_name == other.Name));
        }
    }

    internal class TableAlias
    {
        public TableAlias()
        {
        }

        public override string ToString()
        {
            return "A:" + ObjectConverter.ToString(GetHashCode());
        }
    }

    /// <summary>
    /// A declaration of a column in a SQL SELECT expression
    /// </summary>
    internal class ColumnDeclaration
    {
        private string m_name;
        private Expression m_expression;

        public ColumnDeclaration(string name, Expression expression)
        {
            // Empty string is used for the column name if it does not relate
            // to a db column e.g. 'COUNT(*)'
            if (name == null) throw new ArgumentNullException("name");
            if (expression == null) throw new ArgumentNullException("expression");

            m_name = name;
            m_expression = expression;
        }

        public string Name { get { return m_name; } }
        public Expression Expression { get { return m_expression; } }

        public ColumnDeclaration Update(Expression expression)
        {
            return m_expression == expression ? this : new ColumnDeclaration(m_name, expression);
        }

        public ColumnDeclaration VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression expression = visitor.Visit(m_expression);
            return Update(expression);
        }
    }

    /// <summary>
    /// An SQL OrderBy order type 
    /// </summary>
    internal enum OrderType
    {
        Ascending,
        Descending
    }

    /// <summary>
    /// A pairing of an expression and an order type for use in a SQL Order By clause
    /// </summary>
    internal class OrderExpression
    {
        private OrderType m_orderType;
        private Expression m_expression;

        public OrderExpression(OrderType orderType, Expression expression)
        {
            m_orderType = orderType;
            m_expression = expression;
        }

        public OrderType OrderType { get { return m_orderType; } }
        public Expression Expression { get { return m_expression; } }

        public OrderExpression Update(Expression expression)
        {
            return m_expression == expression ? this : new OrderExpression(m_orderType, expression);
        }

        public OrderExpression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression expression = visitor.Visit(m_expression);
            return Update(expression);
        }
    }

    /// <summary>
    /// A custom expression node used to represent a SQL SELECT expression
    /// </summary>
    internal class SelectExpression : AliasedExpression
    {
        private ReadOnlyCollection<ColumnDeclaration> m_columns;
        private bool m_isDistinct;
        private Expression m_from;
        private Expression m_where;
        private ReadOnlyCollection<OrderExpression> m_orderBy;
        private ReadOnlyCollection<Expression> m_groupBy;
        private Expression m_take;
        private Expression m_skip;
        private bool m_reverse;

        public SelectExpression(
            TableAlias alias,
            IEnumerable<ColumnDeclaration> columns,
            Expression from,
            Expression where,
            IEnumerable<OrderExpression> orderBy,
            IEnumerable<Expression> groupBy,
            bool isDistinct,
            Expression skip,
            Expression take,
            bool reverse
            )
            : base(alias)
        {
            m_columns = columns.ToReadOnly();
            m_isDistinct = isDistinct;
            m_from = from;
            m_where = where;
            m_orderBy = orderBy.ToReadOnly();
            m_groupBy = groupBy.ToReadOnly();
            m_take = take;
            m_skip = skip;
            m_reverse = reverse;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Select; } }

        public override Type Type { get { return typeof(void); } }

        public SelectExpression(
            TableAlias alias,
            IEnumerable<ColumnDeclaration> columns,
            Expression from,
            Expression where,
            IEnumerable<OrderExpression> orderBy,
            IEnumerable<Expression> groupBy
            )
            : this(alias, columns, from, where, orderBy, groupBy, false, null, null, false)
        {
        }

        public SelectExpression(
            TableAlias alias, IEnumerable<ColumnDeclaration> columns,
            Expression from, Expression where
            )
            : this(alias, columns, from, where, null, null)
        {
        }

        public ReadOnlyCollection<ColumnDeclaration> Columns { get { return m_columns; } }
        public Expression From { get { return m_from; } }
        public Expression Where { get { return m_where; } }
        public ReadOnlyCollection<OrderExpression> OrderBy { get { return m_orderBy; } }
        public ReadOnlyCollection<Expression> GroupBy { get { return m_groupBy; } }
        public bool IsDistinct { get { return m_isDistinct; } }
        public Expression Skip { get { return m_skip; } }
        public Expression Take { get { return this.m_take; } }
        public bool IsReverse { get { return this.m_reverse; } }

        public SelectExpression Update(IEnumerable<ColumnDeclaration> columns, Expression from, Expression where,
            IEnumerable<OrderExpression> orderBy, IEnumerable<Expression> groupBy, Expression skip, Expression take)
        {
            return m_columns == columns && m_from == from && m_where == where &&
                m_orderBy == orderBy && m_groupBy == groupBy && m_skip == skip && m_take == take ?
                this : new SelectExpression(Alias, columns, from, where, orderBy, groupBy, m_isDistinct, skip, take, m_reverse);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            IEnumerable<ColumnDeclaration> columns = ExpressionVisitor.Visit(m_columns, (x) => x.VisitChildren(visitor));
            Expression from = visitor.Visit(m_from);
            Expression where = visitor.Visit(m_where);
            IEnumerable<OrderExpression> orderBy = ExpressionVisitor.Visit(m_orderBy, (x) => x.VisitChildren(visitor));
            IEnumerable<Expression> groupBy = visitor.Visit(m_groupBy);
            Expression skip = visitor.Visit(m_skip);
            Expression take = visitor.Visit(m_take);
            return Update(columns, from, where, orderBy, groupBy, skip, take);
        }
    }

    /// <summary>
    /// A kind of SQL join
    /// </summary>
    internal enum JoinType
    {
        CrossJoin,
        InnerJoin,
        CrossApply,
        OuterApply,
        LeftOuter,
        SingletonLeftOuter
    }

    /// <summary>
    /// A custom expression node representing a SQL join clause
    /// </summary>
    internal class JoinExpression : DbExpression
    {
        private JoinType m_joinType;
        private Expression m_left;
        private Expression m_right;
        private Expression m_on;

        public JoinExpression(JoinType joinType, Expression left, Expression right, Expression on)
        {
            m_joinType = joinType;
            m_left = left;
            m_right = right;
            m_on = on;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Join; } }
        public override Type Type { get { return typeof(void); } }

        public JoinType Join { get { return m_joinType; } }
        public Expression Left { get { return m_left; } }
        public Expression Right { get { return m_right; } }
        public Expression On { get { return m_on; } }

        public JoinExpression Update(JoinType joinType, Expression left, Expression right, Expression on)
        {
            return joinType == m_joinType && left == m_left && right == m_right && on == m_on ?
                this : new JoinExpression(joinType, left, right, on);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression left = visitor.Visit(m_left);
            Expression right = visitor.Visit(m_right);
            Expression on = visitor.Visit(m_on);
            return Update(m_joinType, left, right, on);
        }
    }

    internal class OuterJoinedExpression : DbExpression
    {
        private Expression m_test;
        private Expression m_expression;

        public OuterJoinedExpression(Expression test, Expression expression)
        {
            if (expression == null) throw new ArgumentNullException("expression");
            m_test = test;
            m_expression = expression;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.OuterJoined; } }
        public override Type Type { get { return m_expression.Type; } }

        public Expression Test { get { return m_test; } }
        public Expression Expression { get { return m_expression; } }

        public OuterJoinedExpression Update(Expression test, Expression expression)
        {
            return m_test == test && m_expression == expression ?
                this : new OuterJoinedExpression(test, expression);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression test = visitor.Visit(m_test);
            Expression expression = visitor.Visit(m_expression);
            return Update(test, expression);
        }
    }

    internal abstract class SubqueryExpression : DbExpression
    {
        private SelectExpression m_select;
        protected SubqueryExpression(SelectExpression select)
        {
            m_select = select;
        }
        public SelectExpression Select { get { return m_select; } }
    }

    internal class ScalarExpression : SubqueryExpression
    {
        private Type m_type;

        public ScalarExpression(Type type, SelectExpression select)
            : base(select)
        {
            m_type = type;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Scalar; } }
        public override Type Type { get { return m_type; } }

        public ScalarExpression Update(SelectExpression select)
        {
            return Select == select ? this : new ScalarExpression(m_type, select);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            SelectExpression select = visitor.VisitAndConvert(Select, "ScalarExpression.VisitChildren");
            return Update(select);
        }
    }

    internal class ExistsExpression : SubqueryExpression
    {
        public ExistsExpression(SelectExpression select)
            : base(select)
        {
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Exists; } }
        public override Type Type { get { return typeof(bool); } }

        public ExistsExpression Update(SelectExpression select)
        {
            return Select == select ? this : new ExistsExpression(select);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            SelectExpression select = visitor.VisitAndConvert(Select, "ExistsExpression.VisitChildren");
            return Update(select);
        }
    }

    internal class InExpression : SubqueryExpression
    {
        private Expression m_expression;
        private ReadOnlyCollection<Expression> m_values;  // either select or expressions are assigned

        public InExpression(Expression expression, SelectExpression select)
            : base(select)
        {
            m_expression = expression;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.In; } }
        public override Type Type { get { return typeof(bool); } }

        public InExpression(Expression expression, IEnumerable<Expression> values)
            : base(null)
        {
            m_expression = expression;
            m_values = values.ToReadOnly();
        }

        public Expression Expression { get { return m_expression; } }
        public ReadOnlyCollection<Expression> Values { get { return m_values; } }

        public InExpression Update(Expression expression, SelectExpression select, IEnumerable<Expression> values)
        {
            if (values == null)
            {
                return Update(expression, select);
            }
            if (select == null)
            {
                return Update(expression, values);
            }
            throw new NotSupportedException();
        }

        public InExpression Update(Expression expression, SelectExpression select)
        {
            if (m_values != null) throw new NotSupportedException();
            return Select == select && expression == m_expression ? this :
                new InExpression(expression, select);
        }

        public InExpression Update(Expression expression, IEnumerable<Expression> values)
        {
            if (Select != null) throw new NotSupportedException();
            return expression == m_expression && values == m_values ? this :
                new InExpression(expression, values);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression expression = visitor.Visit(m_expression);
            if (m_values == null)
            {
                SelectExpression select = visitor.VisitAndConvert(Select, "InExpression.VisitChildren");
                return Update(expression, select);
            }
            if (Select == null)
            {
                IEnumerable<Expression> values = visitor.Visit(m_values);
                return Update(expression, values);
            }
            throw new NotSupportedException();
        }
    }

    internal class AggregateExpression : DbExpression
    {
        private string m_aggregateName;
        private Expression m_argument;
        private bool m_isDistinct;
        private Type m_type;

        public AggregateExpression(Type type, string aggregateName, Expression argument, bool isDistinct)
        {
            if (type == null) throw new ArgumentNullException("type");
            m_aggregateName = aggregateName;
            m_argument = argument;
            m_isDistinct = isDistinct;
            m_type = type;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Aggregate; } }
        public override Type Type { get { return m_type; } }

        public string AggregateName { get { return m_aggregateName; } }
        public Expression Argument { get { return m_argument; } }
        public bool IsDistinct { get { return m_isDistinct; } }

        public AggregateExpression Update(Expression argument)
        {
            return m_argument == argument ? this :
                new AggregateExpression(m_type, m_aggregateName, argument, m_isDistinct);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression argument = visitor.Visit(m_argument);
            return Update(argument);
        }
    }

    internal class AggregateSubqueryExpression : DbExpression
    {
        private TableAlias m_groupByAlias;
        private Expression m_aggregateInGroupSelect;
        private ScalarExpression m_aggregateAsSubquery;

        public AggregateSubqueryExpression(TableAlias groupByAlias, Expression aggregateInGroupSelect, ScalarExpression aggregateAsSubquery)
        {
            m_aggregateInGroupSelect = aggregateInGroupSelect;
            m_groupByAlias = groupByAlias;
            m_aggregateAsSubquery = aggregateAsSubquery;
        }
        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.AggregateSubquery; } }
        public override Type Type { get { return m_aggregateAsSubquery.Type; } }
        public TableAlias GroupByAlias { get { return m_groupByAlias; } }
        public Expression AggregateInGroupSelect { get { return m_aggregateInGroupSelect; } }
        public ScalarExpression AggregateAsSubquery { get { return m_aggregateAsSubquery; } }

        public AggregateSubqueryExpression Update(Expression aggregateInGroupSelect, ScalarExpression aggregateAsSubquery)
        {
            return aggregateInGroupSelect == m_aggregateInGroupSelect && aggregateAsSubquery == m_aggregateAsSubquery ? this :
                new AggregateSubqueryExpression(m_groupByAlias, aggregateInGroupSelect, aggregateAsSubquery);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression aggregateInGroupSelect = visitor.Visit(m_aggregateInGroupSelect);
            ScalarExpression aggregateAsSubquery = visitor.VisitAndConvert(m_aggregateAsSubquery, "AggregateSubqueryExpression.VisitChildren");
            return Update(aggregateInGroupSelect, aggregateAsSubquery);
        }
    }

    /// <summary>
    /// Allows is-null tests against value-types like int and float
    /// </summary>
    internal class IsNullExpression : DbExpression
    {
        private Expression m_expression;

        public IsNullExpression(Expression expression)
        {
            m_expression = expression;
        }
        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.IsNull; } }
        public override Type Type { get { return typeof(bool); } }
        public Expression Expression { get { return m_expression; } }

        public IsNullExpression Update(Expression expression)
        {
            return expression == m_expression ? this : new IsNullExpression(expression);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression expression = visitor.Visit(m_expression);
            return Update(expression);
        }
    }

    internal class BetweenExpression : DbExpression
    {
        private Expression m_expression;
        private Expression m_lower;
        private Expression m_upper;

        public BetweenExpression(Expression expression, Expression lower, Expression upper)
        {
            m_expression = expression;
            m_lower = lower;
            m_upper = upper;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Between; } }
        public override Type Type { get { return m_expression.Type; } }
        public Expression Expression { get { return m_expression; } }
        public Expression Lower { get { return m_lower; } }
        public Expression Upper { get { return m_upper; } }

        public BetweenExpression Update(Expression expression, Expression lower, Expression upper)
        {
            return expression == m_expression && lower == m_lower && upper == m_upper ? this :
                new BetweenExpression(expression, lower, upper);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression expression = visitor.Visit(m_expression);
            Expression lower = visitor.Visit(m_lower);
            Expression upper = visitor.Visit(m_upper);
            return Update(expression, lower, upper);
        }
    }

    internal class RowNumberExpression : DbExpression
    {
        private ReadOnlyCollection<OrderExpression> m_orderBy;

        public RowNumberExpression(IEnumerable<OrderExpression> orderBy)
        {
            m_orderBy = orderBy.ToReadOnly();
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.RowCount; } }
        public override Type Type { get { return typeof(int); } }
        public ReadOnlyCollection<OrderExpression> OrderBy { get { return m_orderBy; } }

        public RowNumberExpression Update(IEnumerable<OrderExpression> orderBy)
        {
            return orderBy == m_orderBy ? this : new RowNumberExpression(orderBy);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            IEnumerable<OrderExpression> orderBy = ExpressionVisitor.Visit(m_orderBy, (x) => x.VisitChildren(visitor));
            return Update(orderBy);
        }
    }

    internal class NamedValueExpression : DbExpression
    {
        private string m_name;
        private Expression m_value;

        public NamedValueExpression(string name, Expression value)
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException("name");
            if (value == null) throw new ArgumentNullException("value");
            m_name = name;
            m_value = value;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.NamedValue; } }
        public override Type Type { get { return m_value.Type; } }
        public string Name { get { return this.m_name; } }
        public Expression Value { get { return this.m_value; } }

        public NamedValueExpression Update(Expression value)
        {
            return value == m_value ? this : new NamedValueExpression(m_name, value);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression value = visitor.Visit(m_value);
            return Update(value);
        }
    }

    /// <summary>
    /// A custom expression representing the construction of one or more result objects from a 
    /// SQL select expression
    /// </summary>
    internal class ProjectionExpression : DbExpression
    {
        private SelectExpression m_select;
        private Expression m_projector;
        private LambdaExpression m_aggregator;
        private Type m_type;

        public ProjectionExpression(SelectExpression source, Expression projector)
            : this(source, projector, null)
        {
        }

        public ProjectionExpression(SelectExpression source, Expression projector, LambdaExpression aggregator)
        {
            if (projector == null) throw new ArgumentNullException("projector");
            m_select = source;
            m_projector = projector;
            m_aggregator = aggregator;
            m_type = aggregator != null ? aggregator.Body.Type : typeof(IEnumerable<>).MakeGenericType(projector.Type);
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Projection; } }
        public override Type Type { get { return m_type; } }
        public SelectExpression Select { get { return m_select; } }
        public Expression Projector { get { return m_projector; } }
        public LambdaExpression Aggregator { get { return m_aggregator; } }
        public bool IsSingleton { get { return m_aggregator != null && m_aggregator.Body.Type == m_projector.Type; } }

        public ProjectionExpression Update(SelectExpression source, Expression projector, LambdaExpression aggregator)
        {
            return source == m_select && projector == m_projector && aggregator == m_aggregator ? this :
                new ProjectionExpression(source, projector, aggregator);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            SelectExpression source = visitor.VisitAndConvert(m_select, "ProjectionExpression.VisitChildren");
            Expression projector = visitor.Visit(m_projector);
            LambdaExpression aggregator = visitor.VisitAndConvert(m_aggregator, "ProjectionExpression.VisitChildren");
            return Update(source, projector, aggregator);
        }
    }

    internal class ClientJoinExpression : DbExpression
    {
        private ProjectionExpression m_projection;
        private ReadOnlyCollection<Expression> m_outerKey;
        private ReadOnlyCollection<Expression> m_innerKey;

        public ClientJoinExpression(ProjectionExpression projection, IEnumerable<Expression> outerKey, IEnumerable<Expression> innerKey)
        {
            m_projection = projection;
            m_outerKey = outerKey.ToReadOnly();
            m_innerKey = innerKey.ToReadOnly();
        }
        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.ClientJoin; } }
        public override Type Type { get { return m_projection.Type; } }
        public ProjectionExpression Projection { get { return m_projection; } }
        public ReadOnlyCollection<Expression> OuterKey { get { return m_outerKey; } }
        public ReadOnlyCollection<Expression> InnerKey { get { return m_innerKey; } }

        public ClientJoinExpression Update(ProjectionExpression projection, IEnumerable<Expression> outerKey, IEnumerable<Expression> innerKey)
        {
            return projection == m_projection && outerKey == m_outerKey && innerKey == m_innerKey ? this :
                new ClientJoinExpression(projection, outerKey, innerKey);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            ProjectionExpression projection = visitor.VisitAndConvert(m_projection, "ClientJoinExpression.VisitChildren");
            IEnumerable<Expression> outerKey = visitor.Visit(m_outerKey);
            IEnumerable<Expression> innerKey = visitor.Visit(m_innerKey);
            return Update(projection, outerKey, innerKey);
        }
    }

    internal class BatchExpression : DbExpression
    {
        private Expression m_input;
        private LambdaExpression m_operation;
        private Expression m_batchSize;
        private Expression m_stream;
        private Type m_type;

        public BatchExpression(Expression input, LambdaExpression operation, Expression batchSize, Expression stream)
        {
            if (operation == null) throw new ArgumentNullException("operation");
            m_input = input;
            m_operation = operation;
            m_batchSize = batchSize;
            m_stream = stream;
            m_type = typeof(IEnumerable<>).MakeGenericType(operation.Body.Type);
        }
        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Batch; } }
        public override Type Type { get { return m_type; } }
        public Expression Input { get { return m_input; } }
        public LambdaExpression Operation { get { return m_operation; } }
        public Expression BatchSize { get { return m_batchSize; } }
        public Expression Stream { get { return m_stream; } }

        public BatchExpression Update(Expression input, LambdaExpression operation, Expression batchSize, Expression stream)
        {
            return m_input == input && m_operation == operation && m_batchSize == batchSize && m_stream == stream ? this :
                new BatchExpression(input, operation, batchSize, stream);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression input = visitor.Visit(m_input);
            LambdaExpression operation = visitor.VisitAndConvert(m_operation, "BatchExpression.VisitChildren");
            Expression batchSize = visitor.Visit(m_batchSize);
            Expression stream = visitor.Visit(m_stream);
            return Update(input, operation, batchSize, stream);
        }
    }

    internal class FunctionExpression : DbExpression
    {
        private string m_name;
        private ReadOnlyCollection<Expression> m_arguments;
        private Type m_type;

        public FunctionExpression(Type type, string name, IEnumerable<Expression> arguments)
        {
            m_name = name;
            m_arguments = arguments.ToReadOnly();
            m_type = type;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Function; } }
        public override Type Type { get { return m_type; } }
        public string Name { get { return m_name; } }
        public ReadOnlyCollection<Expression> Arguments { get { return m_arguments; } }
    
        public FunctionExpression Update(IEnumerable<Expression> arguments)
        {
            return m_arguments == arguments ? this : new FunctionExpression(m_type, m_name, arguments);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            IEnumerable<Expression> arguments = visitor.Visit(m_arguments);
            return Update(arguments);
        }
    }

    internal abstract class CommandExpression : DbExpression
    {
        protected CommandExpression()
        {
        }
    }

    internal class ColumnAssignment
    {
        private ColumnExpression m_column;
        private Expression m_expression;

        public ColumnAssignment(ColumnExpression column, Expression expression)
        {
            m_column = column;
            m_expression = expression;
        }

        public ColumnExpression Column { get { return m_column; } }
        public Expression Expression { get { return m_expression; } }

        public ColumnAssignment Update(ColumnExpression column, Expression expression)
        {
            return m_column == column && m_expression == expression ? this : new ColumnAssignment(column, expression);
        }

        public ColumnAssignment VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            ColumnExpression column = visitor.VisitAndConvert(m_column, "ColumnAssignment.VisitChildren");
            Expression expression = visitor.Visit(m_expression);
            return Update(column, expression);
        }
    }

    internal class IfCommand : CommandExpression
    {
        private Expression m_check;
        private Expression m_ifTrue;
        private Expression m_ifFalse;

        public IfCommand(Expression check, Expression ifTrue, Expression ifFalse)
        {
            m_check = check;
            m_ifTrue = ifTrue;
            m_ifFalse = ifFalse;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.If; } }
        public override Type Type { get { return this.m_ifTrue.Type; } }

        public Expression Check { get { return m_check; } }
        public Expression IfTrue { get { return m_ifTrue; } }
        public Expression IfFalse { get { return m_ifFalse; } }

        public IfCommand Update(Expression check, Expression ifTrue, Expression ifFalse)
        {
            return m_check == check && m_ifTrue == ifTrue && m_ifFalse == ifFalse ? this :
                new IfCommand(check, ifTrue, ifFalse);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression check = visitor.Visit(m_check);
            Expression ifTrue = visitor.Visit(m_ifTrue);
            Expression ifFalse = visitor.Visit(m_ifFalse);
            return Update(check, ifTrue, ifFalse);
        }
    }

    internal class BlockCommand : CommandExpression
    {
        private ReadOnlyCollection<Expression> m_commands;
        private Type m_type;

        public BlockCommand(IEnumerable<Expression> commands)
        {
            if (commands == null) throw new ArgumentNullException("commands");
            if (!commands.Any()) throw new ArgumentOutOfRangeException("commands");
            m_commands = commands.ToReadOnly();
            m_type = commands.Last().Type;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Block; } }
        public override Type Type { get { return m_type; } }
        public ReadOnlyCollection<Expression> Commands { get { return m_commands; } }

        public BlockCommand(params Expression[] commands) 
            : this((IEnumerable<Expression>)commands)
        {
        }

        public BlockCommand Update(IEnumerable<Expression> commands)
        {
            return commands == m_commands ? this : new BlockCommand(commands);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            IEnumerable<Expression> commands = visitor.Visit(m_commands);
            return Update(commands);
        }
    }

    internal class DeclarationCommand : CommandExpression
    {
        private ReadOnlyCollection<VariableDeclaration> m_variables;
        private SelectExpression m_source;

        public DeclarationCommand(IEnumerable<VariableDeclaration> variables, SelectExpression source)
        {
            m_variables = variables.ToReadOnly();
            m_source = source;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Declaration; } }
        public override Type Type { get { return typeof(void); } }

        public ReadOnlyCollection<VariableDeclaration> Variables { get { return m_variables; } }
        public SelectExpression Source { get { return m_source; } }
        public DeclarationCommand Update(IEnumerable<VariableDeclaration> variables, SelectExpression source)
        {
            return variables == m_variables && source == m_source ? this :
                new DeclarationCommand(variables, source);
        }

        protected override Expression VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            IEnumerable<VariableDeclaration> variables = ExpressionVisitor.Visit(m_variables, (x) => x.VisitChildren(visitor));
            SelectExpression source = visitor.VisitAndConvert(m_source, "DeclarationCommand.VisitChildren");
            return Update(variables, source);
        }
    }

    internal class VariableDeclaration
    {
        private string m_name;
        private Expression m_expression;

        public VariableDeclaration(string name, Expression expression)
        {
            m_name = name;
            m_expression = expression;
        }

        public string Name { get { return m_name; } }
        public Expression Expression { get { return m_expression; } }

        public VariableDeclaration Update(Expression expression)
        {
            return expression == m_expression ? this : new VariableDeclaration(m_name, expression);
        }

        public VariableDeclaration VisitChildren(ExpressionVisitor visitor)
        {
            if (visitor == null) throw new ArgumentNullException("visitor");
            Expression expression = visitor.Visit(m_expression);
            return Update(expression);
        }
    }

    internal class VariableExpression : DbExpression
    {
        private string m_name;
        private Type m_type;

        public VariableExpression(string name, Type type)
        {
            m_name = name;
            m_type = type;
        }

        public sealed override DbExpressionType DbNodeType { get { return DbExpressionType.Variable; } }
        public override Type Type { get { return m_type; } }
        public string Name { get { return m_name; } }
    }
}
