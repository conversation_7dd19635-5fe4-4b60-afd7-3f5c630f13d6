﻿using System.Collections.Generic;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.UI.Elements.Calendars
{
    public class CalendarResource
    {
        public string Label { get; set; }

        public string Id { get; set; }

        public string Emphasis { get; set; }

        public bool Filter { get; set; }

        public Dictionary<string, CpiExpression>[] EmphasisDict { get; set; }

        public CalendarResource(string label, bool filter, Dictionary<string, CpiExpression>[] emphasisDict, string key)
        {
            Label = label;
            Filter = filter;
            EmphasisDict = emphasisDict;
            Id = key;
        }

        public CalendarResource(string label, string id, string emphasis)
        {
            Id = id;
            Label = label;
            Emphasis = emphasis;
        }
    }
}
