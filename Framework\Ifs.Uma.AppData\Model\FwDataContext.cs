﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata.Cpi;

/////////////////////////////////
// WARNING: Ensure you read the BackwardsCompatibility.md documentation before changing this file
/////////////////////////////////

namespace Ifs.Uma.AppData.Model
{
    [ColumnSyncRule(RowType = typeof(EdmFile), PropertyName = nameof(EdmFile.AttachmentStatus), Sync = SyncRule.Never)]
    [ColumnSyncRule(RowType = typeof(MediaLibraryItem), PropertyName = nameof(MediaLibraryItem.AttachmentStatus), Sync = SyncRule.Never)]
    [EnumServerNaming(EnumType = typeof(MediaType), Convention = NamingConvention.TitleCase)]
    public class FwDataContext : DataContext
    {
        public const string FwTablePrefix = "fnd$";

        // 11.0 starts at version 0
        // 11.1 starts at version 10
        // 22.1 starts at version 20
        // 22R2 starts at version 30
        // 23R1 starts at version 40
        // 23R2 starts at version 50
        public const int SchemaVersion = 60;

        public const string SaveMobileDeviceLocationProjection = "MobileClientRuntime.svc";
        public const string SaveMobileDeviceLocationMethodName = "MobileDeviceLocations";

        // System Tables - not cleared on init

        public Table<DatabaseInfo> DatabaseInfos => GetTable<DatabaseInfo>();
        public Table<OpenIdData> OpenIdData => GetTable<OpenIdData>();
        public Table<TfaRegistryData> TfaRegistriesData => GetTable<TfaRegistryData>();
        public Table<LocalProfileValue> LocalProfileValues => GetTable<LocalProfileValue>();
        public Table<MessageInReceipt> MessageInReceipts => GetTable<MessageInReceipt>();

        // App tables - cleared on init and do not exist in server

        public Table<ClientKeysMap> ClientKeysMap => GetTable<ClientKeysMap>();
        public Table<ClientGeneratedKey> ClientGeneratedKeys => GetTable<ClientGeneratedKey>();
        public Table<CacheStatus> CacheStatuses => GetTable<CacheStatus>();
        public Table<MessageInRow> MessageIn => GetTable<MessageInRow>();
        public Table<IgnoreMessageIn> IgnoreMessageIn => GetTable<IgnoreMessageIn>();
#if SIGNATURE_SERVICE
        public Table<DigitalSignatureAndDocument> DigitalSignatureAndDocument => GetTable<DigitalSignatureAndDocument>();
#endif

        // App Remote tables - cleared on init data in sync with server

        public Table<RoamingProfileValue> RoamingProfileValues => GetTable<RoamingProfileValue>();
        public Table<MobileClientParam> AppParameters => GetTable<MobileClientParam>();
        public Table<ClientSecurity> ClientSecurities => GetTable<ClientSecurity>();
        public Table<EdmApplication> EdmApplications => GetTable<EdmApplication>();
        public Table<EdmFile> EdmFiles => GetTable<EdmFile>();
        public Table<DocIssue> DocIssues => GetTable<DocIssue>();
        public Table<DocReferenceObject> DocReferenceObjects => GetTable<DocReferenceObject>();
        public Table<MobileDocClass> MobileDocClasses => GetTable<MobileDocClass>();
        public Table<AddressPresentation> AddressPresentations => GetTable<AddressPresentation>();
        public Table<MediaLibraryItem> MediaLibraryItems => GetTable<MediaLibraryItem>();
        public Table<MediaItem> MediaItems => GetTable<MediaItem>();
        public Table<MediaLibrary> MediaLibraries => GetTable<MediaLibrary>();
        public Table<MobileObjectConnectionConfig> ObjectConnectionConfigs => GetTable<MobileObjectConnectionConfig>();
        public Table<ServerSyncRule> ServerSyncRules => GetTable<ServerSyncRule>();
        public Table<ServerSite> ServerSites => GetTable<ServerSite>();
        public Table<FndTransactionSession> TransactionSessions => GetTable<FndTransactionSession>();

        #region Projection Structures

        public Table<FndDocumentKeys> FndDocumentKeys => GetTable<FndDocumentKeys>();
        public Table<FndMediaKeys> FndMediaKeys => GetTable<FndMediaKeys>();
        public Table<FndDynamicAssistStep> FndDynamicAssistSteps => GetTable<FndDynamicAssistStep>();
        public Table<FndDynamicAssistSetup> FndDynamicAssistSetups => GetTable<FndDynamicAssistSetup>();
        public Table<FndDynamicNextStep> FndDynamicNextSteps => GetTable<FndDynamicNextStep>();
        public Table<FndDynamicCommandDef> FndDynamicCommandDefs => GetTable<FndDynamicCommandDef>();
        public Table<FndWorkflowStep> FndWorkflowSteps => GetTable<FndWorkflowStep>();
        public Table<FndDynamicMenuItem> FndDynamicMenuItems => GetTable<FndDynamicMenuItem>();
        public Table<FndRecordRowId> FndRecordRowIds => GetTable<FndRecordRowId>();

        #endregion

        public FwDataContext(DbInternal db)
            : base(db)
        {
        }

        /// <summary>
        /// Removes all rows from the cache table, happens without a call to SubmitChanges
        /// </summary>
        public void ClearCacheTable(IMetaTable table)
        {
            Provider.TransactionA(cmd =>
            {
                IDeleteSpec spec = SqlSpec.CreateDelete(table.TableName, null);
                Builder.BuildSql(cmd, spec);
                cmd.ExecuteNonQuery();
            });
        }

        /// <summary>
        /// Upserts some rows in the cache table, happens without a call to SubmitChanges
        /// </summary>
        public void UpsertCacheTable(IMetaTable table, IEnumerable<RemoteRow> rows)
        {
            Provider.TransactionA(cmd =>
            {
                DbRowHandler rowHandler = new DbRowHandler(cmd, table, Builder, new RowValueAccessor());
                foreach (RemoteRow row in rows)
                {
                    rowHandler.Upsert(row);
                }
            });
        }

        /// <summary>
        /// Upserts some rows in the cache table, happens without a call to SubmitChanges
        /// </summary>
        public IEnumerable<RemoteRow> InsertOrIgnoreCacheTable(IMetaTable table, IEnumerable<RemoteRow> rows)
        {
            List<RemoteRow> inserted = new List<RemoteRow>();

            Provider.TransactionA(cmd =>
            {
                DbRowHandler rowHandler = new DbRowHandler(cmd, table, Builder, new RowValueAccessor());
                foreach (RemoteRow row in rows)
                {
                    if (!rowHandler.Exists(row))
                    {
                        rowHandler.Insert(row);
                        inserted.Add(row);
                    }
                }
            });

            return inserted;
        }

        /// <summary>
        /// Upserts a single row in the cache table, happens without a call to SubmitChanges
        /// </summary>
        public void InsertOrIgnoreCacheTable(IMetaTable table, RemoteRow row)
        {
            Provider.TransactionA(cmd =>
            {
                DbRowHandler rowHandler = new DbRowHandler(cmd, table, Builder, new RowValueAccessor());
                if (!rowHandler.Exists(row))
                {
                    rowHandler.Insert(row);
                }
            });
        }

        public IEnumerable<EntityRecord> Query(EntityQuery query, CancellationToken token = default)
        {
            return Query(query.Prepare(), token);
        }

        internal IEnumerable<EntityRecord> Query(PreparedEntityQuery query, CancellationToken token = default)
        {
            return Provider.CommandF(cmd =>
            {
                EntityQueryHandler handler = new EntityQueryHandler(cmd, Builder, query);
                return handler.Select(token);
            });
        }

        internal long Count(EntityQuery query)
        {
            AggregateQuery countQuery = AggregateQuery.CreateCount(query);
            object result = Aggregate(countQuery);
            return result is long lResult ? lResult : 0;
        }

        public object Aggregate(AggregateQuery query)
        {
            return Aggregate(query.Prepare());
        }

        internal object Aggregate(PreparedAggregateQuery query)
        {
            return Provider.CommandF(cmd =>
            {
                ISelectSpec spec = query.SelectSpec;
                Builder.BuildSql(cmd, spec);
                return cmd.ExecuteScalar();
            });
        }

        public object GenerateClientKey<T>(string columnName)
        {
            Table table = GetTable<T>();
            IMetaDataMember column = table?.Entity.FindMemberByPropertyName(columnName);
            if (table == null || column == null)
            {
                return null;
            }

            return GenerateClientKey(table.Entity, column);
        }

        public object GenerateClientKey(IMetaTable metaTable, IMetaDataMember metaColumn)
        {
            object clientIdValue = null;

            Provider.TransactionA(cmd =>
            {
                ClientGeneratedKey key = ClientGeneratedKeys.FirstOrDefault(x =>
                     x.TableName == metaTable.TableName &&
                     x.ColumnName == metaColumn.ColumnName);

                if (key == null)
                {
                    key = new ClientGeneratedKey();
                    key.TableName = metaTable.TableName;
                    key.ColumnName = metaColumn.ColumnName;
                }

                long clientId = key.ClientId - 1;

                while (true)
                {
                    clientIdValue = metaColumn.ConvertValue(clientId);

                    ISelectSpec spec = SqlSpec.CreateSelect(
                        new[] { ColumnSpec.Create(Builder.AggregateAsterisk, null, null, EColumnFunction.CountFunction) },
                        TableSpec.Create(metaTable.TableName),
                        null,
                        new[] { WhereElement.Create(EOperand.And, metaColumn, EComparisonMethod.Equals, clientIdValue) },
                        null,
                        null,
                        false);

                    Builder.BuildSql(cmd, spec);
                    long count = (long)cmd.ExecuteScalar();

                    if (count == 0)
                    {
                        break;
                    }

                    clientId--;
                }

                key.ClientId = clientId;

                IDbRowHandler<ClientGeneratedKey> rowHandler = DbRowHandler<ClientGeneratedKey>.Create(cmd, Model, Builder);
                rowHandler.UpsertRow(key);
            });

            return clientIdValue;
        }

        public string GetObjKey(ObjPrimaryKey key)
        {
            if (key == null) throw new ArgumentNullException(nameof(key));

            return Provider.CommandF(cmd =>
            {
                IMetaDataMember objKeyMember = key.Table.FindMemberByPropertyName(nameof(RemoteRow.ObjKey));

                List<IWhereElement> where = new List<IWhereElement>();

                foreach (Tuple<IMetaDataMember, object> keyValue in key.Values)
                {
                    where.Add(WhereElement.Create(EOperand.And, keyValue.Item1, EComparisonMethod.Equals, keyValue.Item2));
                }

                ISelectSpec spec = SqlSpec.CreateSelect(
                    new[] { ColumnSpec.CreateSelect(objKeyMember.ColumnName) },
                    TableSpec.Create(key.Table.TableName),
                    null,
                    where,
                    null,
                    null,
                    false);

                Builder.BuildSql(cmd, spec);
                string objKey = (string)cmd.ExecuteScalar();
                return objKey;
            });
        }

        public bool RecordExists(ObjPrimaryKey key)
        {
            if (key == null) throw new ArgumentNullException(nameof(key));

            return Provider.CommandF(cmd =>
            {
                List<IWhereElement> where = new List<IWhereElement>();
                foreach (Tuple<IMetaDataMember, object> keyValue in key.Values)
                {
                    where.Add(WhereElement.Create(EOperand.And, keyValue.Item1, EComparisonMethod.Equals, keyValue.Item2));
                }

                ISelectSpec spec = SqlSpec.CreateSelect(
                    new[] { ColumnSpec.Create(Builder.AggregateAsterisk, null, null, EColumnFunction.CountFunction) },
                    TableSpec.Create(key.Table.TableName),
                    null,
                    where,
                    null,
                    null,
                    false);

                Builder.BuildSql(cmd, spec);
                object result = cmd.ExecuteScalar();

                if (result == null)
                {
                    return false;
                }

                return (long)result > 0;
            });
        }

        public void UpdateDatabase()
        {
            Provider.TransactionA(cmd =>
            {
                int newVersion = FwDataContext.SchemaVersion;
                int oldVersion = DatabaseInfos.Select(x => x.Version).First();

                if (newVersion < oldVersion)
                {
                    throw new InvalidOperationException(Strings.DbVersionLaterThanAppVersion);
                }

                if (newVersion > oldVersion)
                {
                    IDbSchemaUpdater schemaUpdater = DbSchemaUpdater.Create(cmd, Builder, Model);

                    SchemaMigration.UpdateDatabase(schemaUpdater, oldVersion);

                    DatabaseInfo dbInfo = new DatabaseInfo();
                    dbInfo.ActivatedUser = DatabaseInfos.Select(x => x.ActivatedUser).First();
                    DatabaseInfos.Attach(dbInfo);
                    dbInfo.Version = newVersion;
                    SubmitChanges(false);
                }
            });
        }

        public CpiAction FindFwAction(ProcedureContext context, Dictionary<string, object> parameters)
        {
            if (context.ActionName.Equals("SaveClientProfileValue"))
            {
                return RoamingProfileValue.GetRoamingProfileAction(parameters);
            }

            return null;
        }
    }
}
