﻿using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class AddOnDemandEntityResourceBase : BaseResource
    {
        public override string ResourceName => "MobileClientRuntime.svc/AddOnDemandEntity";

        public override bool SingleResponse => true;

        [DataMember]
        public string AppName { get; set; }

        [DataMember]
        public string DeviceId { get; set; }

        [DataMember]
        public string Entity { get; set; }

        [DataMember]
        public string KeyRef { get; set; }
    }
}
