$solutionDir = "../"
$solutionFile = $solutionDir + "Ifs.Uma.sln"

Write-Output "============ Restore NuGet packages"

& "$($solutionDir).nuget/nuget" restore $solutionFile

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Restore NuGet packages"

Write-Output "============ Parse Build Parameters"

& .\_BuildParameters.ps1

Write-Output "============ End - Parse Build Parameters"

Write-Output "============ Build"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

msbuild $solutionFile /m /t:Shared\Ifs_Uma_System_Tests:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=Any CPU" /p:RestorePackages=false
msbuild $solutionFile /m /t:Shared\Ifs_Uma_System_Prepare_Test:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=Any CPU" /p:RestorePackages=false

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Build"

Write-Output "============ Start - Prepare Test Data"

Push-Location $solutionDir

packages\NUnit.ConsoleRunner.3.9.0\tools\nunit3-console.exe --x86 --labels=All "--result=TestResult.xml;format=nunit2" `
	"Ifs.Uma.System.Prepare.Test\bin\Release\Ifs.Uma.System.Prepare.Test.dll"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Pop-Location

Write-Output "============ End - Prepare Test Data"

Write-Output "============ Test"

Push-Location $solutionDir

packages\NUnit.ConsoleRunner.3.9.0\tools\nunit3-console.exe --x86 --labels=All "--result=TestResult.xml;format=nunit2" `
	"Ifs.Uma.System.Tests\bin\Release\Ifs.Uma.System.Tests.dll"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Pop-Location

Write-Output "============ End - Test"
