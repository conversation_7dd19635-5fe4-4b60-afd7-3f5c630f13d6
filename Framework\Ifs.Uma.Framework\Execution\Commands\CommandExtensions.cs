using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.Execution.Commands
{
    public static class CommandExtensions
    {
        public static async Task<bool> TryRunAsync(this CpiCommand command, string projectionName, ViewData viewData, ICommandExecutor commandExecutor, CommandOptions options = null)
        {
            if (viewData == null) throw new ArgumentNullException(nameof(viewData));
            if (commandExecutor == null) throw new ArgumentNullException(nameof(commandExecutor));

            if (command == null)
            {
                return true;
            }

            bool isVisible, isEnabled;
            commandExecutor.GetStates(projectionName, viewData, command, viewData.CommandsEnabledOnEmpty, out isVisible, out isEnabled);

            if (isVisible && isEnabled)
            {
                ExecuteResult result = await commandExecutor.ExecuteAsync(projectionName, viewData, command, options);
                return !result.Failed;
            }

            return false;
        }
    }
}
