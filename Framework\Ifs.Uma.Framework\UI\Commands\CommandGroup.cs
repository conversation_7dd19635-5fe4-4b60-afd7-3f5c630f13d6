﻿using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using Ifs.Uma.UI.Controls;

namespace Ifs.Uma.Framework.UI.Commands
{
    public class CommandGroup : ObservableCollection<CommandItem>
    {
        private bool _isVisible;
        public bool IsVisible
        {
            get
            {
                return _isVisible;
            }
            private set
            {
                if (_isVisible != value)
                {
                    _isVisible = value;
                    PropertyChangedEventArgs args = new PropertyChangedEventArgs(nameof(IsVisible));
                    OnPropertyChanged(args);
                }
            }
        }

        protected override void OnCollectionChanged(NotifyCollectionChangedEventArgs e)
        {
            base.OnCollectionChanged(e);

            if (e.OldItems != null)
            {
                foreach (CommandItem command in e.OldItems)
                {
                    command.PropertyChanged -= Command_PropertyChanged;
                }
            }

            if (e.NewItems != null)
            {
                foreach (CommandItem command in e.NewItems)
                {
                    command.PropertyChanged += Command_PropertyChanged;
                }
            }

            UpdateIsVisible();
        }

        private void Command_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(CommandItem.IsVisible))
            {
                UpdateIsVisible();
            }
        }

        private void UpdateIsVisible()
        {
            IsVisible = this.Any(x => x.IsVisible);
        }
    }
}
