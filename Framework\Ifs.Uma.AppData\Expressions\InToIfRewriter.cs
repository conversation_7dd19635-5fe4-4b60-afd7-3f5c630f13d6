﻿using System.Linq.Expressions;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Expressions
{
    internal sealed class InToIfRewriter : IfsExpressionVisitor
    {
        public static Expression Rewrite(Expression expression)
        {
            IfsExpressionVisitor visitor = new InToIfRewriter();
            return visitor.Visit(expression);
        }

        protected internal override Expression VisitInExpression(InExpression exp)
        {
            Expression left = Visit(exp.Expression);

            Expression result = null;
            foreach (Expression inExpression in exp.InExpressions.Reverse())
            {
                Expression right = ConvertIfNeeded(Visit(inExpression), left.Type);
                Expression check = Expression.MakeBinary(ExpressionType.Equal, left, right);
                result = result == null ? check : Expression.OrElse(check, result);
            }
            return result;
        }
    }
}
