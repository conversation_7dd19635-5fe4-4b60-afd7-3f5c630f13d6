﻿using System.Collections.Generic;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.UI.Elements.Singletons
{
    public sealed class SingletonElement : RecordLoaderElement
    {
        private readonly IMetadata _metadata;

        // Singleton elements are supposed to hold only one record
        // We want singleton elements to check if the query returns only one record and show a warning if it returns more than one
        protected override bool CheckForSingleRecord => true;

        protected override BindingType BindingPropertyType => BindingType.Array;
        
        private CpiSingleton _singleton;

        public SingletonElement(IMetadata metadata)
            : base(metadata)
        {
            _metadata = metadata;
        }

        protected override CpiRecordLoader OnInitializeRecordLoader()
        {
            _singleton = _metadata.FindSingleton(ProjectionName, Content.Singleton);

            if (_singleton != null)
            {
                _singleton.DatasourceEntitySet = Content.DatasourceEntitySet;
            }

            return _singleton;
        }

        protected override bool CalculateIsVisible()
        {
            return false;
        }

        protected override void OnGetSelectAttributes(ICollection<string> attributes)
        {
            base.OnGetSelectAttributes(attributes);
            
            AttributeFinder.FindInSingleton(attributes, _metadata, ProjectionName, _singleton);
        }
    }
}
