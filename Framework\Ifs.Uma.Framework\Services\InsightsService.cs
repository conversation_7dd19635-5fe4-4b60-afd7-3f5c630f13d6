﻿using System;
using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Framework.App;
using Ifs.Uma.MsAppInsights;
using Ifs.Uma.Services;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Services
{
    internal sealed class InsightsService : EventWatcherService<AppParameterChangedEvent, AppParameterChangedEventArgs>
    {
        private const string DevApp_AppInsights_Key = "68a8416f-0f18-49f5-ad04-34a188c170bf"; // Dev Apps should use the instrumentation key for the IFSAurenaNativeInsightsDev Application Insights instance
        private const string SecondaryLogging_AppInsights_Key = "81ad8dcc-0f59-41be-b282-6237cfff3751"; // Prod Apps with the SecondaryLogging capability enabled should use the IFSCloud_AurenaNative_Secondary Application Insights instance

        private readonly object _updateLock = new object();
        private readonly ILoggerManager _loggerManager;
        private readonly ITouchApp _touchApp;
        private readonly IDataContextProvider _db;

        private MsAppInsightsLogger _insightsLogger;

        public InsightsService(ILoggerManager loggerManager, ILogger logger, IEventAggregator eventAggregator, ITouchApp touchApp, IDataContextProvider dataContextProvider)
            : base(logger, eventAggregator, ThreadOption.BackgroundThread)
        {
            _loggerManager = loggerManager;
            _touchApp = touchApp;
            _db = dataContextProvider;
        }

        protected override void OnStart()
        {
            base.OnStart();
            UpdateInsightsLogger();
        }

        protected override void OnStop()
        {
            UpdateInsightsLogger(null);
            base.OnStop();
        }

        protected override void OnEvent(AppParameterChangedEventArgs args)
        {
            if (args.HasChanged(AppParameterNames.ApplicationInsightsKey))
            {
                UpdateInsightsLogger();
            }
        }
        
        private void UpdateInsightsLogger()
        {
            string applicationInsightsKey = GetApplicationInsightsKey();
            UpdateInsightsLogger(applicationInsightsKey);
        }

        private void UpdateInsightsLogger(string applicationInsightsKey)
        {
            lock (_updateLock)
            {
                if (applicationInsightsKey == null || !IsRunning || (_insightsLogger != null && _insightsLogger.InstrumentationKey != applicationInsightsKey))
                {
                    MsAppInsightsLogger insightsLogger = _insightsLogger;
                    _insightsLogger = null;

                    if (insightsLogger != null)
                    {
                        _loggerManager.RemoveLogger(insightsLogger);
                        insightsLogger.Dispose();
                    }
                }

                if (applicationInsightsKey != null && IsRunning && _insightsLogger == null)
                {
                    _insightsLogger = new MsAppInsightsLogger(applicationInsightsKey);
                    
                    TouchAppAccount account = _touchApp.CurrentSession.Account;
                    if (account != null)
                    {
                        bool anonymizeIdentifyingInfo = !(_touchApp.DeveloperMode || _touchApp.SecondaryLoggingEnabled); // we do not anonymize identifying info in DeveloperMode or if secondary logging is enabled
                        _insightsLogger.Identify(account.ServiceUrl, account.SystemId, account.AppName, account.UserName, Convert.ToString(account.DeviceId), anonymizeIdentifyingInfo);
                    }

                    _loggerManager.AddLogger(_insightsLogger);
                }
            }
        }

        private string GetApplicationInsightsKey()
        {
            try
            {
                // If a debug or release app has the secondary logging capability enabled, we should direct App Insights to that
                if (_touchApp != null && _touchApp.SecondaryLoggingEnabled)
                    return InsightsService.SecondaryLogging_AppInsights_Key;

                // but for Dev versions of the app, always use IFSAurenaNativeInsightsDev instrumentation key
                if (_touchApp != null && _touchApp.DeveloperMode)
                    return InsightsService.DevApp_AppInsights_Key;

                FwDataContext ctx = _db.CreateDataContext();
                MobileClientParam appParam = ctx.AppParameters.FirstOrDefault(x => x.Parameter == AppParameterNames.ApplicationInsightsKey);
                return appParam?.Value;
            }
            catch (Exception)
            {
                // Table may not exist at the before init in which case just use the defaults
                return null;
            }
        }
    }
}
