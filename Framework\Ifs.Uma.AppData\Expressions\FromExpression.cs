﻿using System;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class FromExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.From;

        public string Entity { get; }
        public string Alias { get; }

        internal FromExpression(string entity, string alias)
        {
            Entity = entity ?? throw new ArgumentNullException(nameof(entity));
            Alias = alias ?? throw new ArgumentNullException(nameof(alias));
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitFromExpression(this);
        }

        public override string ToString()
        {
            return "FROM " + Entity + " " + Alias;
        }
    }

    public partial class IfsExpression
    {
        public static FromExpression QueryFrom(string entity, string alias)
        {
            return new FromExpression(entity, alias);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitFromExpression(FromExpression exp)
        {
            return exp;
        }
    }
}
