{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<Convert_ToInteger>": {"name": "Convert_ToInteger", "type": "Function", "params": [{"name": "TextInput"}], "layers": [{"vars": [{"name": "Result", "dataType": "Integer"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Convert", "name": "ToInteger", "paramsArray": ["${TextInput}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}