﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <CodeAnalysisRuleSet>..\FrameworkRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DocumentationFile>bin\Release\netstandard1.1\Ifs.Uma.Database.SQLite.XML</DocumentationFile>
    <CodeAnalysisRuleSet>..\FrameworkRules.Release.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\UmaAssemblyInfo.cs" Link="Properties\UmaAssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="SQLitePCLRaw.core" Version="1.1.14" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.0.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Ifs.Uma.Utility\Ifs.Uma.Utility.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Database\Ifs.Uma.Database.csproj" />
  </ItemGroup>
</Project>