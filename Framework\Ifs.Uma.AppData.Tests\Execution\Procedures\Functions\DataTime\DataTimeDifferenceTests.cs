﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.DataTime
{
    [TestFixture]
    public class DataTimeDifferenceTests : ProcedureTest
    {
        [Test]
        public async Task DateTime_DifferenceInDays()
        {
            //procedure Function<DateTime_DifferenceInDays> Timestamp {
            //    parameter MyDateA Timestamp;
            //    parameter MyDateB Timestamp;
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.DifferenceInDays(MyDateA, MyDateB) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDateA"] = new DateTime(2000, 5, 1, 12, 34, 56);
            param["MyDateB"] = new DateTime(2000, 5, 9, 18, 34, 56);

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_DifferenceInDays", param);
            CheckResults(result, 8.25);
        }

        [Test]
        public async Task DateTime_DifferenceInHours()
        {
            //procedure Function<DateTime_DifferenceInHours> Timestamp {
            //    parameter MyDateA Timestamp;
            //    parameter MyDateB Timestamp;
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.DifferenceInHours(MyDateA, MyDateB) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDateA"] = new DateTime(2000, 5, 1, 12, 34, 56);
            param["MyDateB"] = new DateTime(2000, 5, 1, 18, 04, 56);

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_DifferenceInHours", param);
            CheckResults(result, 5.5);
        }

        private static void CheckResults(ExecuteResult result, object value)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
            Assert.AreEqual(value, result.Value);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.DataTime.DataTimeDifferenceTestsSchema", null);
        }
    }
}
