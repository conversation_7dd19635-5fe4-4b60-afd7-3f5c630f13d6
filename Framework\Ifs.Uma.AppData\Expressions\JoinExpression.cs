﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class JoinExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.Join;

        public EJoinType JoinType { get; }
        public string Entity { get; }
        public string Alias { get; }
        public Expression On { get; }

        internal JoinExpression(EJoinType joinType, string entity, string alias, Expression on)
        {
            JoinType = joinType;
            Entity = entity ?? throw new ArgumentNullException(nameof(entity));
            Alias = alias ?? throw new ArgumentNullException(nameof(alias));
            On = on ?? throw new ArgumentNullException(nameof(on));
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitJoinExpression(this);
        }
        
        public override string ToString()
        {
            return JoinType.ToString().ToUpperCaseUnderscore() + " JOIN " + Entity + " " + <PERSON><PERSON> + " ON " + On;
        }

        public JoinExpression Update(EJoinType joinType, string entity, string alias, Expression on)
        {
            if (Entity == entity &&
                Alias == alias &&
                On == on)
            {
                return this;
            }

            return new JoinExpression(joinType, entity, alias, on);
        }
    }

    public partial class IfsExpression 
    {
        public static JoinExpression QueryJoin(EJoinType joinType, string entity, string alias, Expression on)
        {
            return new JoinExpression(joinType, entity, alias, on);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitJoinExpression(JoinExpression exp)
        {
            Expression on = Visit(exp.On);
            return exp.Update(exp.JoinType, exp.Entity, exp.Alias, on);
        }
    }
}
