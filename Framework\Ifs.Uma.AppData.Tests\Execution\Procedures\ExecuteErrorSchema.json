{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<BasicError>": {"name": "BasicError", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"value": "TestErrorValue"}}, "assign": "Var1"}, {"call": {"method": "error", "args": {"msg": "Invalid value '${Var1}'", "parameter": "Var1"}}}]}]}, "Function<SubProcErrorOuter>": {"name": "SubProcErrorOuter", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"value": "TestErrorValue"}}, "assign": "Var1"}, {"call": {"method": "proc", "args": {"name": "SubProcErrorInner"}}}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "SubProcErrorInner": {"name": "SubProcErrorInner", "type": "Utility", "layers": [{"execute": [{"call": {"method": "error", "args": {"msg": "KEY: Error:Message1"}}}]}]}, "Action<TransactionRollback>": {"name": "TransactionRollback", "type": "Action", "layers": [{"vars": [{"name": "Record"}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Record"}, {"call": {"method": "set", "args": {"value": "500"}}, "assign": "Record.CustomerNo"}, {"call": {"method": "saveLocal", "args": {"name": "Record"}}}, {"call": {"method": "error", "args": {"msg": "ErrorMessage2"}}}]}]}, "Function<NoRollback>": {"name": "NoR<PERSON>back", "type": "Function", "layers": [{"vars": [{"name": "Record"}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Record"}, {"call": {"method": "set", "args": {"value": "500"}}, "assign": "Record.CustomerNo"}, {"call": {"method": "saveLocal", "args": {"name": "Record"}}}, {"call": {"method": "error", "args": {"msg": "ErrorMessage2"}}}]}]}}}}