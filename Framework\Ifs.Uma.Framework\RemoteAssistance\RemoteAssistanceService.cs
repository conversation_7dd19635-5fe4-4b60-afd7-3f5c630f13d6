﻿#if REMOTE_ASSISTANCE
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Unity.Attributes;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    public class RemoteAssistanceService : IRemoteAssistanceService
    {
        private const string CreateSessionActionName = "CreateSession";
        private const string AcceptCallActionName = "AcceptCall";
        private const string DeclineCallActionName = "DeclineCall";
        private const string CancelCallActionName = "CancelCall";
        private const string SessionStartedActionName = "SessionStarted";
        private const string SessionEndedActionName = "SessionEnded";
        private const string NativeClientType = "Native";
        public const string IsUserActiveFunctionName = "IsUserActive";

        private IMetadata _metadata;
        private IOnlineDataHandler _onlineDataHandler;
        private IDialogService _dialogService;
        private ITouchApp _touchApp;
        private IMediaHandler _mediaHandler;

        public RemoteAssistanceSessionInfo Session { get; set; }

        public TimeSpan Timeout => TimeSpan.FromSeconds(130);

        public bool IsOnline => PlatformServices.Provider.IsNetworkAvailable();

        public bool IsDialler { get; set; }

        public string CurrentSessionID { get; set; }

        [InjectionMethod]
        public void Intialize(IMetadata metadata, IOnlineDataHandler onlineDataHandler, IDialogService dialogService, ITouchApp touchApp, IMediaHandler mediaHandler)
        {
            _metadata = metadata;
            _onlineDataHandler = onlineDataHandler;
            _dialogService = dialogService;
            _touchApp = touchApp;
            _mediaHandler = mediaHandler;
        }

        public async Task<ExecuteResult> CreateSessionAsync(string fndUserId, string groupId, string refUrl, string luName, string keyRef)
        {
            if (IsOnline)
            {
                string projectionName = _metadata.GetFirstProjectionWithAction(CreateSessionActionName);

                if (_metadata.FindAction(projectionName, CreateSessionActionName) != null)
                {
                    Dictionary<string, object> parameters = new Dictionary<string, object>
                    {
                        { "CalleeFndUserId", fndUserId },
                        { "GroupId", groupId },
                        { "ClientType", NativeClientType },
                        { "DeviceId", _onlineDataHandler.DeviceId.ToString() },
                        { "AppName", _onlineDataHandler.AppName },
                        { "RefUrl", refUrl },
                        { "LuName", luName },
                        { "KeyRef", keyRef }
                    };

                    ExecuteResult result = await _onlineDataHandler.CallActionAsync(projectionName, CreateSessionActionName, parameters, CancellationToken.None);

                    return result;
                }

                return null;
            }

            return null;
        }

        public async Task<AcceptCallStructure> HandleCallAcceptAsync(string requestId, string sessionToken)
        {
            if (IsOnline)
            {
                string projectionName = _metadata.GetFirstProjectionWithAction(AcceptCallActionName);

                if (_metadata.FindAction(projectionName, AcceptCallActionName) != null)
                {
                    Dictionary<string, object> parameters = new Dictionary<string, object>
                    {
                        { "RequestId", requestId },
                        { "SessionToken", sessionToken },
                        { "ClientType", NativeClientType },
                        { "DeviceId", _onlineDataHandler.DeviceId.ToString() },
                        { "AppName", _onlineDataHandler.AppName }
                    };

                    ExecuteResult result = await _onlineDataHandler.CallActionAsync(projectionName, AcceptCallActionName, parameters, CancellationToken.None);

                    if (!result.Failed && result.Value is RemoteRow record)
                    {
                        AcceptCallStructure acceptCallStructure = new AcceptCallStructure(record[nameof(AcceptCallStructure.UserToken)].ToString(),
                                                                                          record[nameof(AcceptCallStructure.SessionToken)].ToString(),
                                                                                          record[nameof(AcceptCallStructure.ServerUrl)].ToString(),
                                                                                          record[nameof(AcceptCallStructure.ApiKey)].ToString(),
                                                                                          record[nameof(AcceptCallStructure.MediaAttachmentLu)].ToString(),
                                                                                          record[nameof(AcceptCallStructure.MediaAttachmentKeyRef)].ToString());

                        return acceptCallStructure;
                    }
                    else if (result.Failed)
                    {
                        await _dialogService.Alert(Strings.RemoteAssistance, Strings.CallAlreadyEnded);
                        Logger.Current.Log("Remote Assistance Error : " + result?.Exception?.Message, MessageType.Error);
                    }
                }

                return null;
            }
            else
            {
                if (_dialogService != null)
                {
                    await _dialogService?.Alert(Strings.Offline, Strings.YouMustBeOnline);
                }
            }

            return null;
        }

        public async Task<ExecuteResult> HandleCallDeclineAsync(string requestId, string sessionToken, string customMessage, string reasonCode)
        {
            if (IsOnline)
            {
                string projectionName = _metadata.GetFirstProjectionWithAction(DeclineCallActionName);

                if (_metadata.FindAction(projectionName, DeclineCallActionName) != null)
                {
                    Dictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "RequestId", requestId },
                    { "SessionToken", sessionToken },
                    { "CustomMessage", customMessage },
                    { "ReasonCode",  reasonCode != string.Empty ? reasonCode : null },
                    { "ClientType", NativeClientType },
                    { "DeviceId", _onlineDataHandler.DeviceId.ToString() },
                    { "AppName", _onlineDataHandler.AppName }
                };

                    ExecuteResult result = await _onlineDataHandler.CallActionAsync(projectionName, DeclineCallActionName, parameters, CancellationToken.None);

                    if (!result.Failed)
                    {
                        return result;
                    }
                    else
                    {
                        Logger.Current.Log("Remote Assistance Error : " + result?.Exception?.Message, MessageType.Error);
                    }
                }

                return ExecuteResult.None;
            }

            return null;
        }

        public async Task<ExecuteResult> HandleCallCancelAsync(bool? isTimeout = false)
        {
            if (IsOnline)
            {
                string projectionName = _metadata.GetFirstProjectionWithAction(CancelCallActionName);

                if (_metadata.FindAction(projectionName, CancelCallActionName) != null && Session != null)
                {
                    Dictionary<string, object> parameters = new Dictionary<string, object>
                    {
                        { "RequestId", Session.RequestId },
                        { "SessionToken", Session.SessionToken },
                        { "IsTimeout", isTimeout }
                    };

                    Session = null;
                    ExecuteResult result = await _onlineDataHandler.CallActionAsync(projectionName, CancelCallActionName, parameters, CancellationToken.None);

                    if (!result.Failed)
                    {
                        return result;
                    }
                    else
                    {
                        Logger.Current.Log("Remote Assistance Error : " + result?.Exception?.Message, MessageType.Error);
                    }
                }
            }

            return ExecuteResult.None;
        }

        public async Task<ExecuteResult> HandleSessionStartedAsync(string callId)
        {
            string projectionName = _metadata.GetFirstProjectionWithAction(SessionStartedActionName);

            if (_metadata.FindAction(projectionName, SessionStartedActionName) != null && Session != null)
            {
                Dictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "RequestId", Session.RequestId },
                    { "CallId", callId },
                    { "ClientType", NativeClientType },
                    { "DeviceId", _onlineDataHandler.DeviceId.ToString() },
                    { "AppName", _onlineDataHandler.AppName }
                };

                ExecuteResult result = await _onlineDataHandler.CallActionAsync(projectionName, SessionStartedActionName, parameters, CancellationToken.None);

                if (!result.Failed)
                {
                    return result;
                }
                else
                {
                    Logger.Current.Log("Remote Assistance Error : " + result?.Exception?.Message, MessageType.Error);
                }
            }

            return ExecuteResult.None;
        }

        public async Task<ExecuteResult> HandleSessionEndedAsync()
        {
            string projectionName = _metadata.GetFirstProjectionWithAction(SessionEndedActionName);

            if (_metadata.FindAction(projectionName, SessionEndedActionName) != null && Session != null)
            {
                Dictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "RequestId", Session.RequestId },
                    { "ClientType", NativeClientType },
                    { "DeviceId", _onlineDataHandler.DeviceId.ToString() },
                    { "AppName", _onlineDataHandler.AppName }
                };

                Session = null;
                ExecuteResult result = await _onlineDataHandler.CallActionAsync(projectionName, SessionEndedActionName, parameters, CancellationToken.None);

                if (!result.Failed)
                {
                    return result;
                }
                else
                {
                    Logger.Current.Log("Remote Assistance Error : " + result?.Exception?.Message, MessageType.Error);
                }
            }

            return ExecuteResult.None;
        }

        public async Task<bool> CheckRemoteAssistanceEnabledAsync()
        {
            TouchAppAccount account = _touchApp?.Accounts.FirstOrDefault();
            if (account != null)
            {
                string projectionName = _metadata.GetFirstProjectionWithFunction(IsUserActiveFunctionName);

                if (_metadata.FindFunction(projectionName, IsUserActiveFunctionName) != null)
                {
                    Dictionary<string, object> parameters = new Dictionary<string, object>
                    {
                        { "FndUserId", account.UserName },
                    };

                    ExecuteResult result = await _onlineDataHandler.CallFunctionAsync(projectionName, IsUserActiveFunctionName, parameters, CancellationToken.None);

                    if (result?.Value is bool isUserActive)
                    {
                        return isUserActive;
                    }
                }
            }

            return false;
        }

        public bool IsEUdatacenter()
        {
            TouchAppAccount account = _touchApp?.Accounts.FirstOrDefault();

            if (!string.IsNullOrEmpty(account?.ServerVersion))
            {
                string[] parts = account.ServerVersion.Split('.');

                if (parts.Length >= 2)
                {
                    string major = parts[0];
                    string minor = new string(parts[1].Where(c => char.IsDigit(c)).ToArray());

                    double serverVersionInDouble = double.Parse(major + "." + minor, CultureInfo.InvariantCulture);

                    // Use HelpLightning EU Data centers from 23R1 (including 23R1)
                    if (serverVersionInDouble >= 23.1)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public async Task SaveScreenCapturesToMediaLibrary(byte[] imageByteArray, string keyRef, string luName)
        {
            try
            {
                string timestamp = DateTime.Now.ToString("yyyy-dd-M--HH-mm-ss");
                string fileName = Strings.RemoteAssistance + " AN_" + timestamp;
                MemoryStream imageStream = new MemoryStream(imageByteArray);

                if (_mediaHandler != null)
                {
                    await _mediaHandler?.AddMediaAsync(luName, keyRef, null,
                    null, fileName, null, null, imageStream);
                }
            }
            catch (Exception e)
            {
                Logger.Current.Log("Failed to Save Screen Capture, Reason :" + e.Message, MessageType.Error);
            }
        }
    }
}
#endif
