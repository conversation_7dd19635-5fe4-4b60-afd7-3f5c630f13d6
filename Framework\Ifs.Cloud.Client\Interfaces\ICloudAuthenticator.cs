﻿using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Cloud.Client.Interfaces
{
    public interface ICloudAuthenticator
    {
        Task LogoutFromIDP(IdentityProvider identityProvider);
        Task<string> GetKeyCloakInfo(string uri);
        string GetSessionID(ContextProvider ctx, string authToken);
        Task<TokenResponseInfo> RefreshAccessToken(IdentityProvider identityProvider);
        string GetAuthenticationToken(string systemId, string userName, string password, string publicKey);
        string GetIdentityProviderInformation();
        bool? GetPinAuthentication();
        string GetUserName();
    }
}
