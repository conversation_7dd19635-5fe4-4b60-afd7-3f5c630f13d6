<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        android:layout_weight="1">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <include
                layout="@layout/app_info_header" />
            <LinearLayout
                android:id="@+id/InternalUseLinearLayout"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:layout_marginBottom="24dp"
                android:gravity="center_vertical|start" >
                <Ifs.Uma.UI.Images.UmaImageView
                    android:id="@+id/MaintenanceIcon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="16dp" />
                <TextView
                    android:id="@+id/InternalUseTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/IfsWhite" 
                    android:textDirection="locale"
                    android:textSize="22sp"/>
            </LinearLayout>
            <Ifs.Uma.UI.Forms.FormLayout
                android:id="@id/Form"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" 
                android:background="@color/GraniteColorBackground"/>
        </LinearLayout>
    </ScrollView>
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/IfsBlue" />
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:paddingBottom="5dp">
        <TextView
            android:id="@+id/Disconnect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerInParent="true"
            android:layout_marginStart="12dp"
            style="@style/SecondaryAction_TextView" />
        <Button
            android:id="@+id/Proceed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerInParent="true"
            android:layout_marginEnd="10dp" />
    </RelativeLayout>
</LinearLayout>
