﻿using System;
using System.Collections.Generic;

namespace Ifs.Uma.Database
{
    public interface IDbSchemaUpdater
    {
        void AddTable(Type rowType);
        void AddColumn(Type rowType, string propertyName);
    }

    public sealed class DbSchemaUpdater : IDbSchemaUpdater
    {
        private readonly DbCommand _command;
        private readonly SqlBuilder _builder;
        private readonly IMetaModel _model;

        private const int TableInfoColumnNameIndex = 1;

        public static IDbSchemaUpdater Create(DbCommand command, SqlBuilder builder, IMetaModel model)
        {
            return new DbSchemaUpdater(command, builder, model);
        }

        private DbSchemaUpdater(DbCommand command, SqlBuilder builder, IMetaModel model)
        {
            _command = command;
            _builder = builder;
            _model = model;
        }

        public void AddTable(Type rowType)
        {
            if (rowType == null) throw new ArgumentNullException(nameof(rowType));

            IMetaTable table = _model.GetTable(rowType);

            if (table == null)
            { 
                throw new ArgumentException($"Unable to find metatable for {rowType}. Schema cannot be updated.", nameof(rowType));
            }

            IEnumerable<string> ddl = _builder.BuildDdl(new[] { table }, DdlChoice.CreateScript);
            DdlStatement.Execute(_command, ddl);
        }

        public void AddColumn(Type rowType, string propertyName)
        {
            if (rowType == null) throw new ArgumentNullException(nameof(rowType));
            if (propertyName == null) throw new ArgumentNullException(nameof(propertyName));

            IMetaTable table = _model.GetTable(rowType);

            if (table == null)
            {
                throw new ArgumentException($"Unable to find metatable for {rowType}. Schema cannot be updated.", nameof(rowType));
            }

            IMetaDataMember member = table.FindMemberByPropertyName(propertyName);

            if (member == null)
            {
                throw new ArgumentException($"Unable to find member {propertyName} for {rowType}. Schema cannot be updated.", nameof(propertyName));
            }

            if (!ColumnDoesExist(table, member))
            {
                IEnumerable<string> ddl = _builder.BuildAddColumnDdl(table, member);
                DdlStatement.Execute(_command, ddl);
            }
        }

        private bool ColumnDoesExist(IMetaTable table, IMetaDataMember member)
        {
            _command.Parameters.Clear();
            _command.CommandText = _builder.BuildTableInfoDdl(table);

            using (DbDataReader reader = _command.ExecuteReader())
            {
                while (reader.Read())
                {
                    if (reader.GetString(TableInfoColumnNameIndex) == member.ColumnName)
                    {
                        return true;
                    }
                }
            }

            return false;
        }
    }
}
