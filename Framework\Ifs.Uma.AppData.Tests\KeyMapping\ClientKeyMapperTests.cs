﻿using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using NUnit.Framework;
using System.Collections.Generic;

namespace Ifs.Uma.AppData.Tests.KeyMapping
{
    [TestFixture]
    public class ClientKeyMapperTests
    {
        [Test]
        public void NumberPrimaryKey()
        {
            IMetaModel metaModel = SetupMetaModel();

            ClientKeysMapper mapper = new ClientKeysMapper(null);
            mapper.Load(metaModel, null);

            ClientKeysMap a = new ClientKeysMap();
            a.TableName = "library";
            a.ServerKeys = "5";
            a.ClientKeys = "-1";
            mapper.RegisterKeys(a);

            MessageTableData newData = new MessageTableData();
            newData.TableName = "library";
            newData.RowData = new MessageRowData();
            Dictionary<string, object> newDataValues = new Dictionary<string, object>();
            newDataValues["library_id"] = 5L;
            newData.RowData.ColumnData = newDataValues;

            MessageTableData result = mapper.MapServerToClientKeys(newData);

            long mappedId = (long)result.RowData.ColumnData["library_id"];
            Assert.AreEqual(-1L, mappedId);
        }

        [Test]
        public void StringPrimaryKey()
        {
            IMetaModel metaModel = SetupMetaModel();

            ClientKeysMapper mapper = new ClientKeysMapper(null);
            mapper.Load(metaModel, null);

            ClientKeysMap a = new ClientKeysMap();
            a.TableName = "city";
            a.ServerKeys = "SERVER1";
            a.ClientKeys = "CLIENT1";
            mapper.RegisterKeys(a);

            MessageTableData newData = new MessageTableData();
            newData.TableName = "city";
            newData.RowData = new MessageRowData();
            Dictionary<string, object> newDataValues = new Dictionary<string, object>();
            newDataValues["city_id"] = "SERVER1";
            newData.RowData.ColumnData = newDataValues;

            MessageTableData result = mapper.MapServerToClientKeys(newData);

            string mappedId = (string)result.RowData.ColumnData["city_id"];
            Assert.AreEqual("CLIENT1", mappedId);
        }

        [Test]
        public void UnregisterKeys()
        {
            IMetaModel metaModel = SetupMetaModel();

            ClientKeysMapper mapper = new ClientKeysMapper(null);
            mapper.Load(metaModel, null);

            ClientKeysMap map = new ClientKeysMap();
            map.TableName = "city";
            map.ServerKeys = "SERVER1";
            map.ClientKeys = "CLIENT1";
            mapper.RegisterKeys(map);

            MessageTableData newData = new MessageTableData();
            newData.TableName = "city";
            newData.RowData = new MessageRowData();
            Dictionary<string, object> newDataValues = new Dictionary<string, object>();
            newDataValues["city_id"] = "SERVER1";
            newData.RowData.ColumnData = newDataValues;

            MessageTableData result = mapper.MapServerToClientKeys(newData);

            string mappedId = (string)result.RowData.ColumnData["city_id"];
            Assert.AreEqual("CLIENT1", mappedId);

            mapper.UnregisterKeys(map);

            result = mapper.MapServerToClientKeys(newData);

            mappedId = (string)result.RowData.ColumnData["city_id"];
            Assert.AreEqual("SERVER1", mappedId);
        }

        [Test]
        public void ReferenceRelation()
        {
            IMetaModel metaModel = SetupMetaModel();

            ClientKeysMapper mapper = new ClientKeysMapper(null);
            mapper.Load(metaModel, null);

            ClientKeysMap a = new ClientKeysMap();
            a.TableName = "city";
            a.ServerKeys = "SERVER1";
            a.ClientKeys = "CLIENT1";
            mapper.RegisterKeys(a);

            MessageTableData newData = new MessageTableData();
            newData.TableName = "library";
            newData.RowData = new MessageRowData();
            Dictionary<string, object> newDataValues = new Dictionary<string, object>();
            newDataValues["city_id"] = "SERVER1";
            newData.RowData.ColumnData = newDataValues;

            MessageTableData result = mapper.MapServerToClientKeys(newData);

            string mappedId = (string)result.RowData.ColumnData["city_id"];
            Assert.AreEqual("CLIENT1", mappedId);
        }

        [Test]
        public void ObjectConnectionRelation()
        {
            IMetaModel metaModel = SetupMetaModel();

            ClientKeysMapper mapper = new ClientKeysMapper(null);
            mapper.Load(metaModel, null);

            ClientKeysMap a = new ClientKeysMap();
            a.TableName = "connectable";
            a.ServerKeys = "5\u001FSERVER1";
            a.ClientKeys = "-1\u001FCLIENT1";
            mapper.RegisterKeys(a);

            MessageTableData newData = new MessageTableData();
            newData.TableName = "object_connection";
            newData.RowData = new MessageRowData();
            Dictionary<string, object> newDataValues = new Dictionary<string, object>();
            newDataValues["lu_name"] = "Connectable";
            newDataValues["key_ref"] = "NUMBER_ID=5^STRING_ID=SERVER1^";
            newData.RowData.ColumnData = newDataValues;

            MessageTableData result = mapper.MapServerToClientKeys(newData);

            string mappedLuName = (string)result.RowData.ColumnData["lu_name"];
            Assert.AreEqual("Connectable", mappedLuName);

            string mappedId = (string)result.RowData.ColumnData["key_ref"];
            Assert.AreEqual("NUMBER_ID=-1^STRING_ID=CLIENT1^", mappedId);
        }

        private IMetaModel SetupMetaModel()
        {
            MappingSource mappingSource = new AttributeMappingSource(null, null, null);
            IMetaModel metaModel = mappingSource.GetModel(typeof(TestDataContext));
            return metaModel;
        }
    }
}
