﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Translates accesses to relationship members into projections or joins
    /// </summary>
    internal class RelationshipBinder : DbExpressionVisitor
    {
        Expression currentFrom;

        private RelationshipBinder()
        {
        }

        public static Expression Bind(Expression expression)
        {
            return new RelationshipBinder().Visit(expression);
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            if (node == null) return null;
            Expression saveCurrentFrom = this.currentFrom;
            this.currentFrom = this.VisitSource(node.From);
            try
            {
                Expression where = this.Visit(node.Where);
                IEnumerable<OrderExpression> orderBy = this.VisitOrderBy(node.OrderBy);
                IEnumerable<Expression> groupBy = this.VisitExpressionList(node.GroupBy);
                Expression skip = this.Visit(node.Skip);
                Expression take = this.Visit(node.Take);
                IEnumerable<ColumnDeclaration> columns = this.VisitColumnDeclarations(node.Columns);
                if (this.currentFrom != node.From
                    || where != node.Where
                    || orderBy != node.OrderBy
                    || groupBy != node.GroupBy
                    || take != node.Take
                    || skip != node.Skip
                    || columns != node.Columns
                    )
                {
                    return new SelectExpression(node.Alias, columns, this.currentFrom, where, orderBy, groupBy, node.IsDistinct, skip, take, node.IsReverse);
                }
                return node;
            }
            finally
            {
                this.currentFrom = saveCurrentFrom;
            }
        }

        protected override Expression VisitMember(MemberExpression node)
        {
            if (node == null) return null;
            Expression source = this.Visit(node.Expression);
            Expression result = QueryBinder.BindMember(source, node.Member);
            MemberExpression mex = result as MemberExpression;
            if (mex != null && mex.Member == node.Member && mex.Expression == node.Expression)
            {
                return node;
            }
            return result;
        }
    }
}
