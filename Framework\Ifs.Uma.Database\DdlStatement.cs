﻿using System.Collections.Generic;

namespace Ifs.Uma.Database
{
    public static class DdlStatement
    {
        public static void Execute(DbCommand command, IEnumerable<string> ddls)
        {
            if (ddls != null)
            {
                command.Parameters.Clear();

                foreach (string ddl in ddls)
                {
                    command.CommandText = ddl;
                    command.ExecuteNonQuery();
                }
            }
        }
    }
}
