﻿using System;
using Ifs.Uma.AppData.Expressions;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Expressions
{
    [TestFixture]
    public class DynamicValueTests
    {
        [Test]
        [TestCase(1, 1)]
        [TestCase(1.0, 1)]
        [TestCase(1.1, 1.1)]
        [TestCase("1", 1)]
        [TestCase("1.0", 1)]
        [TestCase("1.1", 1.1)]
        [TestCase(true, 1)]
        [TestCase("one", 0)]  
        [TestCase(null, 0)]
        public void NumberConversion(object value, double expected)
        {
            DynamicValue dynamicValue = new DynamicValue(value);
            Assert.AreEqual(dynamicValue.ToNumber(), expected);
        }

        [Test]
        [SetCulture("sv-SE")]
        [TestCase("2.3", 2.3)]
        public void NumberConversionSwedish(string value, double expected)
        {
            DynamicValue dynamicValue = new DynamicValue(value);
            Assert.AreEqual(dynamicValue.ToNumber(), expected);
        }

        [Test]
        [TestCase(1, 1)]
        [TestCase(1.1, 1.1)]
        [TestCase(1, 1.0)]
        [TestCase("HelloWorld", "HelloWorld")]
        [TestCase("2018-07-19T09:00:00", "2018-07-19T09:00:00")]
        public void EqualityComparison(object x, object y)
        {
            DynamicValue dynamicValue = new DynamicValue(x);
            DynamicValue dynamicValue2 = new DynamicValue(y);

            Assert.AreEqual(dynamicValue, dynamicValue2);
            Assert.DoesNotThrow(() =>
            {
                if (dynamicValue == dynamicValue2)
                {
                    return;
                }
                else
                {
                    Assert.Fail();
                }
            });
        }

        [Test]
        [TestCase(1, 0)]
        [TestCase("HelloWorld", "Helloworld")]
        [TestCase("HelloWorld", 1)]
        public void InequalityComparison(object x, object y)
        {
            DynamicValue dynamicValue = new DynamicValue(x);
            DynamicValue dynamicValue2 = new DynamicValue(y);

            Assert.AreNotEqual(dynamicValue, dynamicValue2);
            Assert.DoesNotThrow(() =>
            {
                if (dynamicValue != dynamicValue2)
                {
                    return;
                }
                else
                {
                    Assert.Fail();
                }
            });
        }

        [Test]
        public void EqualityComparison_Dates()
        {
            DynamicValue dynamicValue = new DynamicValue(new DateTime(2018, 07, 19, 09, 00, 00));
            DynamicValue dynamicValue2 = new DynamicValue(new DateTime(2018, 07, 19, 09, 00, 00));

            Assert.AreEqual(dynamicValue, dynamicValue2);
            Assert.DoesNotThrow(() =>
            {
                if (dynamicValue.Value == dynamicValue2.Value)
                {
                    return;
                }
            });
        }

        [Test]
        public void Comparison_Dates()
        {
            DynamicValue dynamicValue = new DynamicValue(new DateTime(2018, 07, 19, 09, 00, 00));
            DynamicValue dynamicValue2 = new DynamicValue(new DateTime(2018, 07, 19, 10, 00, 00));

            Assert.IsTrue(dynamicValue < dynamicValue2);
            Assert.IsTrue(dynamicValue <= dynamicValue2);
            Assert.IsTrue(dynamicValue2 > dynamicValue);
            Assert.IsTrue(dynamicValue2 >= dynamicValue);
        }
    }
}
