﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Utility;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Converts user arguments into named-value parameters
    /// </summary>
    internal class Parameterizer : DbExpressionVisitor
    {
        Dictionary<TypeAndValue, NamedValueExpression> map = new Dictionary<TypeAndValue, NamedValueExpression>();
        Dictionary<HashedExpression, NamedValueExpression> pmap = new Dictionary<HashedExpression, NamedValueExpression>();

        private Parameterizer()
        {
        }

        public static Expression Parameterize(Expression expression)
        {
            return new Parameterizer().Visit(expression);
        }

        protected override Expression VisitProjection(ProjectionExpression node)
        {
            if (node == null) return null;
            // don't parameterize (visit) the projector or aggregator!
            SelectExpression select = VisitAndConvert(node.Select, "Parameterizer.VisitProjection");
            return node.Update(select, node.Projector, node.Aggregator);
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            if (node == null) return null;
            if (node.NodeType == ExpressionType.Convert && node.Operand.NodeType == ExpressionType.ArrayIndex)
            {
                var b = (BinaryExpression)node.Operand;
                if (IsConstantOrParameter(b.Left) && IsConstantOrParameter(b.Right))
                {
                    return this.GetNamedValue(node);
                }
            }
            return base.VisitUnary(node);
        }

        private static bool IsConstantOrParameter(Expression e)
        {
            return (e != null && e.NodeType == ExpressionType.Constant) || e.NodeType == ExpressionType.Parameter;
        }

        protected override Expression VisitBinary(BinaryExpression node)
        {
            if (node == null) return null;
            Expression left = this.Visit(node.Left);
            Expression right = this.Visit(node.Right);
            if (left.GetDbNodeType() == DbExpressionType.NamedValue
             && right.GetDbNodeType() == DbExpressionType.Column)
            {
                NamedValueExpression nv = (NamedValueExpression)left;
                left = new NamedValueExpression(nv.Name, nv.Value);  //JVB: isn't this the same thing?
            }
            else if (node.Right.GetDbNodeType() == DbExpressionType.NamedValue
             && node.Left.GetDbNodeType() == DbExpressionType.Column)
            {
                NamedValueExpression nv = (NamedValueExpression)right;
                right = new NamedValueExpression(nv.Name, nv.Value);  //JVB: isn't this the same thing?
            }
            return node.Update(left, node.Conversion, right);
        }

        protected override ColumnAssignment VisitColumnAssignment(ColumnAssignment node)
        {
            node = base.VisitColumnAssignment(node);
            Expression expression = node.Expression;
            NamedValueExpression nv = expression as NamedValueExpression;
            if (nv != null)
            {
                //JVB: Why are we cloning an immutable object?
                // expression could be a subtype of NVE but no such thing exists!
                // same construct repeated elsewhere so probably no good.
                expression = new NamedValueExpression(nv.Name, nv.Value);
            }
            return node.Update(node.Column, expression);
        }

        int iParam = 0;
        protected override Expression VisitConstant(ConstantExpression node)
        {
            if (node == null) return null;
            // MM: Code used to not paramatize numerics
            if (node.Value != null)
            {
                NamedValueExpression nv;
                TypeAndValue tv = new TypeAndValue(node.Type, node.Value);
                if (!this.map.TryGetValue(tv, out nv)) // re-use same name-value if same type & value
                { 
                    string name = "p" + System.Xml.XmlConvert.ToString(iParam++);
                    nv = new NamedValueExpression(name, node);
                    this.map.Add(tv, nv);
                }
                return nv;
            }
            return node;
        }

        protected override Expression VisitParameter(ParameterExpression node) 
        {
            return this.GetNamedValue(node);
        }

        protected override Expression VisitMember(MemberExpression node)
        {
            Expression result = base.VisitMember(node);
            MemberExpression m = result as MemberExpression;
            if (m != null)
            {
                NamedValueExpression nv = m.Expression as NamedValueExpression;
                if (nv != null)
                {
                    Expression x = Expression.MakeMemberAccess(nv.Value, m.Member);
                    return GetNamedValue(x);
                }
            }
            return result;
        }

        private Expression GetNamedValue(Expression e)
        {
            NamedValueExpression nv;
            HashedExpression he = new HashedExpression(e);
            if (!this.pmap.TryGetValue(he, out nv))
            {
                string name = "p" + System.Xml.XmlConvert.ToString(iParam++);
                nv = new NamedValueExpression(name, e);
                this.pmap.Add(he, nv);
            }
            return nv;
        }

        struct TypeAndValue : IEquatable<TypeAndValue>
        {
            Type type;
            object value;
            int hash;

            public TypeAndValue(Type type, object value)
            {
                this.type = type;
                this.value = value;
                this.hash = type.GetHashCode() + (value != null ? value.GetHashCode() : 0);
            }

            public override bool Equals(object obj)
            {
                if (!(obj is TypeAndValue))
                    return false;
                return this.Equals((TypeAndValue)obj);
            }

            public bool Equals(TypeAndValue vt)
            {
                return vt.type == this.type && object.Equals(vt.value, this.value);
            }

            public override int GetHashCode()
            {
                return this.hash;
            }
        }

        struct HashedExpression : IEquatable<HashedExpression>
        {
            Expression expression;
            int hashCode;

            public HashedExpression(Expression expression)
            {
                this.expression = expression;
                this.hashCode = Hasher.ComputeHash(expression);
            }

            public override bool Equals(object obj)
            {
                if (!(obj is HashedExpression))
                    return false;
                return this.Equals((HashedExpression)obj);
            }

            public bool Equals(HashedExpression other)
            {
                return this.hashCode == other.hashCode &&
                    DbExpressionComparer.AreEqual(this.expression, other.expression);
            }

            public override int GetHashCode()
            {
                return this.hashCode;
            }

            class Hasher : DbExpressionVisitor
            {
                int hc;

                internal static int ComputeHash(Expression expression)
                {
                    var hasher = new Hasher();
                    hasher.Visit(expression);
                    return hasher.hc;
                }

                protected override Expression VisitConstant(ConstantExpression c)
                {
                    hc += c != null && c.Value != null ? c.Value.GetHashCode() : 0;
                    return c;
                }
            }
        }
    }
}