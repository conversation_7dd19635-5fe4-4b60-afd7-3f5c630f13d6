﻿using Ifs.Uma.Database;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Ifs.Uma.Data
{
    public class DataChangeSet
    {
        private Dictionary<IMetaTable, HashSet<long>> _rows = new Dictionary<IMetaTable, HashSet<long>>();

        public IEnumerable<IMetaTable> EffectedTables { get { return _rows.Keys; } }

        public bool IsEmpty { get { return !EffectedTables.Any(); } }

        public void AddTable(IMetaTable table)
        {
            HashSet<long> rows;
            if (!_rows.TryGetValue(table, out rows))
            {
                _rows[table] = new HashSet<long>();
            }
        }

        public void AddRow(IMetaTable table, long rowId)
        {
            HashSet<long> rows;
            if (!_rows.TryGetValue(table, out rows))
            {
                rows = new HashSet<long>();
                _rows[table] = rows;
            }

            rows.Add(rowId);
        }

        public void ClearRows()
        {
            _rows.Clear();
        }

        public List<Tuple<string, long>> GetChangedRowIds()
        {
            List<Tuple<string, long>> rowIds = new List<Tuple<string, long>>();
            foreach (KeyValuePair<IMetaTable, HashSet<long>> kvp in _rows)
            {
                foreach (long rowId in kvp.Value)
                {
                    rowIds.Add(new Tuple<string, long>(kvp.Key.TableName, rowId));
                }
            }

            return rowIds;
        }

        public bool HasChanges(MetaTableClass tableClass)
        {
            return EffectedTables.Any(x => x.Classification.IsSet(tableClass));
        }

        public bool HasChanges<T>()
        {
            return HasChanges(typeof(T));
        }

        public bool HasChanges(Type rowType)
        {
            return EffectedTables.FirstOrDefault(x => x.RowType == rowType) != null;
        }

        public bool HasChanges(IMetaTable table)
        {
            return _rows.ContainsKey(table);
        }

        public bool HasChanges<T>(long rowId)
        {
            return HasChanges(typeof(T), rowId);
        }

        public bool HasChanges(Type rowType, long rowId)
        {
            IMetaTable table = EffectedTables.FirstOrDefault(x => x.RowType == rowType);
            return table == null ? false : HasChanges(table, rowId);
        }

        public bool HasChanges(IMetaTable table, long rowId)
        {
            HashSet<long> rows;
            if (!_rows.TryGetValue(table, out rows))
            {
                return false;
            }
            
            return rows.Contains(rowId);
        }

        public void AddChangeSet(DataChangeSet changeSet)
        {
            foreach (var item in changeSet._rows)
            {
                AddTable(item.Key);

                foreach (long rowId in item.Value)
                {
                    AddRow(item.Key, rowId);
                }
            }
        }

        public void AddRows(IMetaModel metaModel, IEnumerable<RowBase> rows)
        {
            foreach (RowBase row in rows)
            {
                AddRow(metaModel, row);
            }
        }

        public void AddRow(IMetaModel metaModel, RowBase row)
        {
            IMetaTable table = metaModel.GetTable(row.GetType());
            if (table != null)
            {
                AddRow(table, row.RowId);
            }
        }
    }
}
