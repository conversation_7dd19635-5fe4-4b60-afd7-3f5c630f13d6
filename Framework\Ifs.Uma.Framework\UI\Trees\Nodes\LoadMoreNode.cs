﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.Localization;

namespace Ifs.Uma.Framework.UI.Trees.Nodes
{
    public class LoadMoreNode : Node
    {
        public LoadMoreNode(Node parent)
            : base(parent, null, null, null)
        {
        }

        public override string Title => Strings.LoadMore;

        public override bool IsPromotableToRoot => false;

        public override bool IsSecondActionVisible => false;

        public async Task LoadMore()
        {
            await Parent.OnlineOnlyLoadChildren();
        }

        protected override Task<IEnumerable<Node>> GetChildNodes()
        {
            return Task.FromResult<IEnumerable<Node>>(new List<Node>());
        }
    }
}
