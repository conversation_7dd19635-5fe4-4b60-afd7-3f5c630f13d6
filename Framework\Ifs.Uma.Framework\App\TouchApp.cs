﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Ifs.Cloud.Client;
using Ifs.Cloud.Client.Exceptions;
using Ifs.Uma.AppData.Attachments;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Framework.MSTeams;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Services.Sync;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Helpers;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;
using Unity;
using Unity.Lifetime;

namespace Ifs.Uma.Framework.App
{
    public sealed class TouchApp : SessionController, ITouchApp
    {
        private const string AccountsSettingsGroup = "Accounts";
        private const string AccountIdsSettingsName = "Ids";
        private const string UserLoggedOutSettingName = "UserLoggedOut";
        private const string SwitchUserFlag = "SwitchUser";
        private const int DemoDatabaseId = 0;
        public bool DeveloperMode { get; set; }
        public bool SecondaryLoggingEnabled { get; private set; }
        public bool DebugBuild { get; set; }
        public bool TryMeModeAvailable => false;

        private int? _attempts = 0;

        public string DisplayName
        {
            get
            {
                if (DeveloperMode)
                {
                    IScreenshotMode screenshotMode;
                    if (TryResolve(out screenshotMode) && screenshotMode.IsEnabled)
                    {
                        return screenshotMode.Title;
                    }
                    IMetadata metadata;
                    if (TryResolve(out metadata))
                    {
                        return metadata?.CpiMetaData?.GetClients()?.FirstOrDefault()?.Name;
                    }
                }
                return Registration.Name;
            }
        }

        private readonly ISettings _accountSettings;
        private readonly IDatabaseController _db;
        private readonly IDialogService _dialogService;

        private LoadingSession _session;
        private ISettings _settings;
        private ISettings _pincodeSettings;

        public static ITouchApp CreateAndRegisterTouchApp(IUnityContainer appContainer, AppRegistration appReg, bool debugBuild)
        {
            appContainer.RegisterType(appReg.DatabaseControllerType, new ContainerControlledLifetimeManager());
            appContainer.RegisterType(typeof(IDatabaseController), appReg.DatabaseControllerType);
            appContainer.RegisterType(typeof(IDataContextProvider), appReg.DatabaseControllerType);
            appContainer.RegisterType<ITouchApp, TouchApp>(new ContainerControlledLifetimeManager());

            TouchApp touchApp = (TouchApp)appContainer.Resolve<ITouchApp>();
            touchApp.DeveloperMode = debugBuild || appReg.AppName == null;
            touchApp.DebugBuild = debugBuild;
            touchApp.LoggerManager.IncludeDebugMessages = touchApp.DeveloperMode;
            touchApp.SecondaryLoggingEnabled = Ifs.Uma.Utility.LoggerManager.SecondaryLoggingEnabled;

            Resolver.Handler = touchApp;

            string appName = appReg.AppName ?? "(DeveloperMode)";
            touchApp.Logger.Log("Application: " + appName + " " + appReg.DisplayVersion, MessageType.Information);
            touchApp.Logger.Log("Device: " + DeviceInfo.OperatingSystem + " " + DeviceInfo.OperatingSystemVersion + " - " + DeviceInfo.Manufacturer + " " + DeviceInfo.Model, MessageType.Information);

            return touchApp;
        }

        public TouchApp(IUnityContainer container, AppRegistration appReg, IDatabaseController db, ILoggerManager loggerManager,
            ISettings appSettings, IDialogService dialogService)
            : base(container, loggerManager, appReg, appSettings)
        {
            _db = db;
            _dialogService = dialogService;
            _accountSettings = appSettings.GetSubGroup(AccountsSettingsGroup);
            _settings = appSettings;

            _pincodeSettings = _settings.GetSubGroup(OfflinePinCodeConfigurations.PinCodeSettingsKey);
            int triesLeft = Convert.ToInt32(_pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeAttemptsLeftKey));
            string state = _pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeStateKey);
            string attempts = _pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeAttemptsKey);

            if (attempts == "*")
            {
                _attempts = null;
            }
            else
            {
                _attempts = triesLeft > 0 && state == string.Empty ? triesLeft : Convert.ToInt32(_pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeAttemptsKey));
            }

            container.RegisterInstance<ITouchApp>(this);
            LoadAccounts();
        }

        #region Touch Apps Accounts

        private readonly List<TouchAppAccount> _accounts = new List<TouchAppAccount>();

        public IEnumerable<TouchAppAccount> Accounts => _accounts.ToArray();

        private void LoadAccounts()
        {
            _accounts.Clear();
            string[] accountIds = _accountSettings.Get(AccountIdsSettingsName, string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (string accountId in accountIds)
            {
                try
                {
                    ISettings settings = _accountSettings.GetSubGroup(accountId);
                    int nAccountId = int.Parse(accountId, CultureInfo.InvariantCulture);
                    bool databaseExists = _db.DoesDatabaseExist(nAccountId);
                    TouchAppAccount account = new TouchAppAccount(databaseExists, settings);
                    _accounts.Add(account);
                }
                catch (Exception ex)
                {
                    Logger.HandleException(ExceptionType.Unexpected, ex);
                }
            }
        }

        private async Task<IIfsConnection> CreateConnection(TouchAppAccount account)
        {
            ClientInfo ci = Registration.GetClientInfo();
            ci.AppName = account.AppName;

            TouchAppsComms comms = new TouchAppsComms(Logger, ci) { DevMode = account.DevScaffold };

            if (account.IdentityProvider != null && !account.DevScaffold)
            {
                await Task.Run(() =>
                {
                    FwDataContext ctx = _db.CreateDataContext();

                    OpenIdData openIdData = ctx?.OpenIdData.FirstOrDefault();
                    if (openIdData != null)
                    {
                        account.IdentityProvider.RefreshToken = openIdData.RefreshToken;
                        account.IdentityProvider.AccessToken = openIdData.AccessToken;
                        account.IdentityProvider.IdToken = openIdData.IdToken;
                    }
                });
            }

            comms.TokensRefreshed += SaveRefreshToken;

            return new IfsConnection(comms);
        }

        private int GetNextAccountId()
        {
            if (!_accounts.Any())
            {
                return 1;
            }
            else
            {
                return _accounts.Max(x => x.AccountId) + 1;
            }
        }

        public async Task<TouchAppAccount> ActivateNewAccountBasic(string serviceUrl, string systemId, string appName, string userName, string password)
        {
            TouchAppAccount account = CreateTouchAppAccount(serviceUrl, systemId, appName, null);

            if (string.IsNullOrEmpty(password?.Trim()))
            {
                throw new InvalidOperationException(Strings.InvalidUsernameOrPassword);
            }

            account.UserName = userName;
            await ActivateBasic(account, password);
            return account;
        }

        public async Task<TouchAppAccount> ActivateNewAccountOpenId(string serviceUrl, string systemId, string appName)
        {
            IdentityProvider identityProvider = await GetIdentityProvider(serviceUrl, appName);
            TouchAppAccount account = CreateTouchAppAccount(serviceUrl, systemId, appName, identityProvider);

            try
            {
                await ActivateOpenId(account);

                if (account.IsActivated)
                {
                    AddTouchAppAccount(account);
                    return account;
                }

                await RemoveAccount(account);
                return null;
            }
            catch (Exception)
            {
                await RemoveAccount(account);

                throw;
            }
        }

        public async Task<TouchAppAccount> ActivateNewAccountDirectAccess(string serviceUrl, string systemId, string appName, string userName, string password)
        {
            IdentityProvider identityProvider = await GetIdentityProvider(serviceUrl, appName);
            TouchAppAccount account = CreateTouchAppAccount(serviceUrl, systemId, appName, identityProvider);

            try
            {
                await ActivateDirectAccess(account, userName, password);

                if (account.IsActivated)
                {
                    AddTouchAppAccount(account);
                    return account;
                }

                await RemoveAccount(account);
                return null;
            }
            catch (Exception)
            {
                await RemoveAccount(account);

                throw;
            }
        }

        public async Task<TouchAppAccount> ActivateNewAccountNoAuth(string serviceUrl, string systemId, string appName, string userName)
        {
            TouchAppAccount account = CreateTouchAppAccount(serviceUrl, systemId, appName, null);
            account.UserName = userName;
            await ActivateNoAuth(account);
            return account;
        }
        private async Task<IdentityProvider> GetIdentityProvider(string serviceUrl, string appName)
        {
            ClientInfo ci = Registration.GetClientInfo();
            ci.AppName = appName;

            IdentityProvider identityProvider;

            using (TouchAppsComms comms = new TouchAppsComms(Logger, ci) { DevMode = false })
            {
                identityProvider = await comms.GetIdentityProviderInformation(serviceUrl, appName);

                if (identityProvider == null)
                {
                    throw new InvalidOperationException(Strings.InvalidEnvironmentUrl);
                }
            }
            return identityProvider;
        }

        private TouchAppAccount CreateTouchAppAccount(string serviceUrl, string systemId, string appName, IdentityProvider identityProvider)
        {
            if (CurrentSession != null)
            {
                throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
            }

            if (string.IsNullOrEmpty(serviceUrl))
            {
                serviceUrl = "https://cloud.ifsworld.com:8080";
            }

            if (Registration.AppName != null)
            {
                appName = Registration.AppName;
            }

            TouchAppAccount account = new TouchAppAccount(GetNextAccountId(), serviceUrl, systemId, appName);
            ISettings settings = _accountSettings.GetSubGroup(account.AccountId.ToString(CultureInfo.InvariantCulture));
            account.SetSettings(settings);
            if (identityProvider != null)
            {
                account.IdentityProvider = identityProvider;
            }

            return account;
        }

        public void AddTouchAppAccount(TouchAppAccount account)
        {
            _accounts.Add(account);
            string accountIds = string.Join(",", _accounts.Select(x => x.AccountId.ToString(CultureInfo.InvariantCulture)));
            _accountSettings.Set(AccountIdsSettingsName, accountIds);
        }

        public async Task Activate(TouchAppAccount account, string password)
        {
            if (account.IdentityProvider == null)
            {
                await ActivateBasic(account, password);
            }
            else
            {
                await ActivateOpenId(account);
            }
        }

        private async Task ActivateBasic(TouchAppAccount account, string password)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));

            if (string.IsNullOrEmpty(password?.Trim()))
            {
                throw new InvalidOperationException(Strings.InvalidUsernameOrPassword);
            }

            if (CurrentSession != null)
            {
                throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
            }

            using (IIfsConnection connection = await CreateConnection(account))
            {
                string username = await connection.TouchAppsComms.Activate(account, password);
                if (!string.IsNullOrEmpty(username))
                {
                    account.UserName = username;
                }
            }

            await Task.Run(() =>
            {
                string dbPassword = PlatformServices.SaltPassword(password);
                _db.CreateDatabaseForUser(account.DatabaseId, account.UserName, dbPassword);
                account.DatabaseExists = true;
            });

            await LoginBasic(account, password);
        }

        private async Task ActivateOpenId(TouchAppAccount account)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));
            IdentityProvider identityProvider = account.IdentityProvider;
            if (identityProvider == null)
                throw new ArgumentException(nameof(account));

            if (CurrentSession != null)
            {
                throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
            }

            IOpenIdAuthenticator openIdAuthenticator = this.Resolve<IOpenIdAuthenticator>();
            TokenResponseInfo authToken = await openIdAuthenticator.AuthenticateAndGetAccessToken(identityProvider);
            await ValidateTokenAndPinAuth(account, identityProvider, authToken);
        }

        private async Task ActivateDirectAccess(TouchAppAccount account, string username, string password)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));

            if (string.IsNullOrEmpty(username?.Trim()) || string.IsNullOrEmpty(password?.Trim()))
            {
                throw new InvalidOperationException(Strings.InvalidUsernameOrPassword);
            }

            IdentityProvider identityProvider = account.IdentityProvider;
            if (identityProvider == null)
                throw new ArgumentException(nameof(account));

            if (CurrentSession != null)
            {
                throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
            }

            IOpenIdAuthenticator openIdAuthenticator = this.Resolve<IOpenIdAuthenticator>();
            TokenResponseInfo authToken = await openIdAuthenticator.GetDirectAccessToken(identityProvider, username, password);
            await ValidateTokenAndPinAuth(account, identityProvider, authToken);
        }

        private async Task ActivateNoAuth(TouchAppAccount account)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));

            if (CurrentSession != null)
            {
                throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
            }

            using (IIfsConnection connection = await CreateConnection(account))
            {
                string username = await connection.TouchAppsComms.Activate(account, string.Empty);
                if (!string.IsNullOrEmpty(username))
                {
                    account.UserName = username;
                }
            }

            await Task.Run(() =>
            {
                _db.CreateDatabaseForUser(account.DatabaseId, account.UserName, string.Empty);
                account.DatabaseExists = true;
            });

            await LoginNoAuth(account);
        }

        private async Task ValidateTokenAndPinAuth(TouchAppAccount account, IdentityProvider identityProvider, TokenResponseInfo authToken)
        {
            if (account == null || identityProvider == null)
                throw new ArgumentNullException(nameof(account));

            if (authToken == null)
            {
                // user cancelled
                return;
            }

            if (authToken.IsEmpty())
            {
                throw new Exception(Strings.TokenResponseError);
            }

            string pinCode = null;
            identityProvider.UpdateAuthTokens(authToken);

            //TO - DO: WE ARE NOT AUTHENTICATING AGAINST WITH THE SERVER NOW
            using (IIfsConnection connection = await CreateConnection(account))
            {
                string username = await connection.TouchAppsComms.Activate(account, authToken.AccessToken);

                if (!string.IsNullOrEmpty(username))
                {
                    account.UserName = username;
                }

                if (account.PinAuthentication)
                {
                    pinCode = await GetUserPin(string.Empty);
                }
            }

            if (!(account.PinAuthentication && pinCode == null))
            {
                await Task.Run(() =>
                {
                    string dbPassword = PlatformServices.SaltPassword(pinCode);
                    _db.CreateDatabaseForUser(account.DatabaseId, account.UserName, dbPassword);
                    account.DatabaseExists = true;

                    _db.Login(account.DatabaseId, account.UserName, dbPassword);

                    OpenIdData openIdData = new OpenIdData();
                    openIdData.Username = account.UserName;
                    openIdData.Ipi = identityProvider.Ipi;
                    openIdData.RefreshToken = authToken.RefreshToken;
                    openIdData.IdToken = authToken.IdToken;
                    openIdData.AccessToken = authToken.AccessToken;
                    FwDataContext ctx = _db.CreateDataContext();
                    ctx.OpenIdData.InsertOnSubmit(openIdData);
                    ctx.SubmitChanges(false);

                    _db.Disconnect();
                });

                await LoginOpenId(account, pinCode);
            }
        }

        private async Task<string> GetUserOldPin()
        {
            IPlatformApplication platformApplication = this.Resolve<IPlatformApplication>();
            return await platformApplication.ShowGetPinCodeDialogAsync(Strings.EnterCurrentPinCode);
        }

        private async Task<string> GetUserPin(string errorMessage = "")
        {
            string complexMessage = OfflinePinCodeConfigurations.PinCodeIsComplex ? Strings.OfflinePinCodeComplexMessage : string.Empty;
            complexMessage = complexMessage.Replace("\\r\\n", System.Environment.NewLine);
            string message = string.IsNullOrEmpty(errorMessage) ? string.Format(Strings.OfflinePinCodeMessage, new string[] { OfflinePinCodeConfigurations.PinCodeMinLength.ToString(), complexMessage }) : errorMessage;

            IPlatformApplication openIdAuthenticator = this.Resolve<IPlatformApplication>();
            string pin = await openIdAuthenticator.ShowGetPinCodeDialogAsync(message);
            if (pin != null)
            {
                string pinReentered = await openIdAuthenticator.ShowGetPinCodeDialogAsync(Strings.ReEnterPinCodeMessage, true);
                if (pin == pinReentered)
                {
                    return pin;
                }
                if (pinReentered == null)
                {
                    return null;
                }
                return await GetUserPin(string.Format(Strings.ReEnterPinCodeMessageMismatch, message));
            }
            return null;
        }

        public async Task Login(TouchAppAccount account, string passwordOrPin)
        {
            await LoginOpenId(account, passwordOrPin);

            if (!_settings.GetBoolean(UserLoggedOutSettingName, false))
            {
                FinalizeTransactionSessions();
            }

            _settings.Clear(UserLoggedOutSettingName);
        }

        private async Task LoginBasic(TouchAppAccount account, string password)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));
            if (account.IdentityProvider != null)
                throw new ArgumentException(nameof(account));

            if (string.IsNullOrEmpty(password?.Trim()))
            {
                throw new InvalidOperationException(Strings.InvalidUsernameOrPassword);
            }

            if (CurrentSession != null)
            {
                throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
            }

            await Task.Run(() =>
            {
                string dbPassword = PlatformServices.SaltPassword(password);
                if (!_db.Login(account.DatabaseId, account.UserName, dbPassword))
                {
                    throw new InvalidOperationException(Strings.InvalidUsernameOrPassword);
                }
            });

            bool anonymizeIdentifyingInfo = !DeveloperMode; // we do not anonymize identifying info in DeveloperMode
            LoggerManager.Insights.Identify(account.ServiceUrl, account.SystemId, account.AppName, account.UserName, Convert.ToString(account.DeviceId), anonymizeIdentifyingInfo);

            IIfsConnection connection = await CreateConnection(account);
            connection.TouchAppsComms.LoginOffline(account, password);

            await StartSessionAsync(account.UserName, connection, account);
        }

        private async Task LoginOpenId(TouchAppAccount account, string pinCode)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));
            IdentityProvider identityProvider = account.IdentityProvider;

            if (CurrentSession != null)
            {
                throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
            }

            bool failedLogin = false;
            if (identityProvider != null && string.IsNullOrEmpty(account.UserName))
            {
                _db.Disconnect();

                identityProvider.AccessToken = null;
                identityProvider.IdToken = null;
                identityProvider.RefreshToken = null;

                await ActivateOpenId(account);
                return;
            }
            else
            {
                await Task.Run(() =>
                {
                    string dbPassword = PlatformServices.SaltPassword(pinCode);
                    if (!_db.Login(account.DatabaseId, account.UserName, dbPassword))
                    {
                        failedLogin = true;
                    }
                });
            }

            if (failedLogin)
            {
                if (_attempts == null)
                {
                    throw new InvalidOperationException(Strings.InvalidPinCode);
                }

                string pinCodeBehavior = _pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeFailBehaviorKey);
                int time = Convert.ToInt32(_pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeLockDurationKey));

                _attempts -= 1;

                if (_attempts > 0)
                {
                    _pincodeSettings.Set(OfflinePinCodeConfigurations.PinCodeAttemptsLeftKey, _attempts);
                    throw new InvalidOperationException(string.Format(Strings.InvalidPinCodeAttempts, _attempts));
                }
                else if (_attempts == 0)
                {
                    if (pinCodeBehavior == OfflinePinCodeConfigurations.PinCodeLock)
                    {
                        _pincodeSettings.Set(OfflinePinCodeConfigurations.PinCodeStateKey, OfflinePinCodeConfigurations.PinCodeStateLocked);
                        _pincodeSettings.Set(OfflinePinCodeConfigurations.PinCodeLockedTimeKey, DateTime.Now);
                        throw new InvalidOperationException(string.Format(Strings.AccountLockedMessage, time));
                    }
                    else if (pinCodeBehavior == OfflinePinCodeConfigurations.PinCodeBlock)
                    {
                        _pincodeSettings.Set(OfflinePinCodeConfigurations.PinCodeStateKey, OfflinePinCodeConfigurations.PinCodeStateBlocked);
                        throw new InvalidOperationException(Strings.AccountBlockedMessage);
                    }
                }
                else
                {
                    throw new InvalidOperationException(Strings.InvalidPinCode);
                }
            }
            else
            {
                OfflinePinCodeConfigurations.ResetPinCodeSettings(_pincodeSettings);

                if (_pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeAttemptsKey) != "*")
                {
                    _attempts = Convert.ToInt32(_pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeAttemptsKey));
                }
            }

            await LoadIdpAndAuthInfo(account);
            identityProvider = account.IdentityProvider;

            if (identityProvider.RefreshTokenExpired)
            {
                try
                {
                    using (IIfsConnection tmpConnection = await CreateConnection(account))
                    {
                        IdentityProvider newIdentityProvider = await ReauthenticateAccount(tmpConnection, account);

                        if (newIdentityProvider == null)
                        {
                            // user cancelled
                            _db.Disconnect();
                            return;
                        }

                        identityProvider = newIdentityProvider;
                    }
                }
                catch (Exception)
                {
                    _db.Disconnect();
                    throw;
                }
            }

            bool anonymizeIdentifyingInfo = !DeveloperMode; // we do not anonymize identifying info in DeveloperMode
            LoggerManager.Insights.Identify(account.ServiceUrl, account.SystemId, account.AppName, account.UserName, Convert.ToString(account.DeviceId), anonymizeIdentifyingInfo);

            if (SecondaryLoggingEnabled)
            {
                Logger.Log("Username: " + account.UserName, MessageType.Trace);
                Logger.Log("ServiceUrl: " + account.ServiceUrl, MessageType.Trace);
                Logger.Log("SystemId: " + account.SystemId, MessageType.Trace);
                Logger.Log("DeviceId: " + account.DeviceId, MessageType.Trace);
            }

            if (Registration.AppName == null)
            {
                Logger.Log("AppName: " + account.AppName, MessageType.Information);
            }

            IIfsConnection connection = await CreateConnection(account);
            connection.TouchAppsComms.LoginOffline(account, account.IdentityProvider.AccessToken);

            await StartSessionAsync(account.UserName, connection, account);
        }

        private async Task LoginNoAuth(TouchAppAccount account)
        {
            await Task.Run(() =>
            {
                if (!_db.Login(account.DatabaseId, account.UserName, string.Empty))
                {
                    throw new InvalidOperationException(Strings.InvalidUsernameOrPassword);
                }
            });

            bool anonymizeIdentifyingInfo = !DeveloperMode; // we do not anonymize identifying info in DeveloperMode
            LoggerManager.Insights.Identify(account.ServiceUrl, account.SystemId, account.AppName, account.UserName, Convert.ToString(account.DeviceId), anonymizeIdentifyingInfo);

            IIfsConnection connection = await CreateConnection(account);
            connection.TouchAppsComms.LoginOffline(account, string.Empty);

            await StartSessionAsync(account.UserName, connection, account);
        }

        private async Task<IdentityProvider> ReauthenticateAccount(IIfsConnection connection, TouchAppAccount account)
        {
            IdentityProvider identityProvider = await connection.TouchAppsComms.GetIdentityProviderInformation(account.ServiceUrl, account.SystemId);

            if (identityProvider == null)
            {
                throw new InvalidOperationException(Strings.InvalidEnvironmentUrl);
            }

            IOpenIdAuthenticator openIdAuthenticator = this.Resolve<IOpenIdAuthenticator>();
            TokenResponseInfo authToken = await openIdAuthenticator.AuthenticateAndGetAccessToken(identityProvider);

            if (authToken == null)
            {
                // user cancelled
                return null;
            }

            if (authToken.IsEmpty())
            {
                throw new InvalidOperationException(Strings.TokenResponseError);
            }

            identityProvider.UpdateAuthTokens(authToken);

            bool validateUser = await connection.TouchAppsComms.TryLogin(account, identityProvider);
            if (!validateUser && !account.DevScaffold)
            {
                CustomButtonsResult result = await _dialogService.CustomButtons(Strings.MismatchUserRefresh, Strings.ReEnterUserRefresh, Strings.Ok, Strings.Later);

                if (result == CustomButtonsResult.Positive)
                {
                    return await ReauthenticateAccount(connection, account);
                }

                return null;
            }

            account.IdentityProvider = identityProvider;

            await SaveAuthTokens(identityProvider);

            return identityProvider;
        }

        public async Task ChangePassword(string newPassword)
        {
            if (CurrentSession?.Account == null)
            {
                throw new InvalidOperationException(Strings.CannotChangePasswordWithoutLoggingIn);
            }

            if (string.IsNullOrEmpty(newPassword?.Trim()))
            {
                throw new InvalidOperationException(Strings.InvalidUsernameOrPassword);
            }

            IIfsConnection connection = this.Resolve<IIfsConnection>();

            await VerifyServerPassword(connection, CurrentSession.Account, newPassword);

            await Task.Run(() =>
            {
                string dbPassword = PlatformServices.SaltPassword(newPassword);
                _db.ChangePassword(dbPassword);
            });

            connection.TouchAppsComms.LoginOffline(CurrentSession.Account, newPassword);

            if (TryResolve(out ISyncController syncController))
            {
                syncController.RequestSync();
            }
        }

        private async Task VerifyServerPassword(IIfsConnection connection, TouchAppAccount account, string newPassword)
        {
            try
            {
                await connection.TouchAppsComms.VerifyPassword(account, newPassword);
            }
            catch (CloudException ex)
            {
                if (ex.StatusCode == HttpStatusCode.Unauthorized
                    && (
                        ex.ErrorType == CloudErrorType.SecurityError
                        ||
                        ex.ErrorType == CloudErrorType.Unknown
                        ||
                        ex.Message.Contains("Invalid username/password")
                    ))
                {
                    throw new InvalidOperationException(Strings.NewPasswordDoesNotMatch);
                }
                else
                {
                    throw;
                }
            }
        }

        public async Task Deactivate(TouchAppAccount account)
        {
            PlatformServices.SimulateNoConnection = false;

            if (account == null)
                throw new ArgumentNullException(nameof(account));
            if (account == CurrentSession?.Account)
                throw new ArgumentException(Strings.CannotDeactivateCurrentAccount, nameof(account));

            if (account.IdentityProvider != null)
            {
                using (IIfsConnection tempConnection = await CreateConnection(account))
                {
                    await tempConnection.TouchAppsComms.LogoutIdpSessionAsync(account);
                }
            }

            int databaseId = account.DatabaseId;

            await Task.Run(() =>
            {
                _db.DeleteDatabase(databaseId);
                account.DatabaseExists = false;
            });
            account.DeviceId = null;
            account.PinAuthentication = false;

            if (TryResolve(out ILocalStorage localStorage))
            {
                try
                {
                    string attachmentsFolderPath = AttachmentFolders.GetAttachmentsFolderPath(databaseId);
                    ILocalFolderInfo folder = await localStorage.PrivateStorage.GetFolderInfoAsync(attachmentsFolderPath);
                    await folder.DeleteAsync();
                }
                catch
                {
                    // ignored
                }

                await localStorage.EmptyTemporaryFolder();
            }

            if (TryResolve(out IThemeService themeService))
            {
                themeService.ResetProfiles();
            }
        }

        public async Task RemoveAccount(TouchAppAccount account)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));
            if (account == CurrentSession?.Account)
                throw new ArgumentException(Strings.CannotDeleteCurrentAccount, nameof(account));

            await Deactivate(account);

            account.Delete();

            _accounts.Remove(account);
            string accountIds = string.Join(",", _accounts.Select(x => x.AccountId.ToString(CultureInfo.InvariantCulture)));
            _accountSettings.Set(AccountIdsSettingsName, accountIds);
        }

        private async void SaveRefreshToken(object sender, EventArgs e)
        {
            await SaveAuthTokens();
        }

        public async Task ChangePinCode()
        {
            string oldPinCode = await GetUserOldPin();
            bool isMatching = false;

            if (!string.IsNullOrEmpty(oldPinCode))
            {
                await Task.Run(() =>
                {
                    isMatching = _db.ValidatePassword(oldPinCode);
                });
            }

            if (isMatching)
            {
                string newPinCode = await GetUserPin();
                if (CurrentSession?.Account == null)
                {
                    throw new InvalidOperationException(Strings.CannotChangePinCodeWithoutLoggingIn);
                }

                if (!string.IsNullOrEmpty(newPinCode?.Trim()))
                {
                    await Task.Run(() =>
                    {
                        string dbPassword = PlatformServices.SaltPassword(newPinCode);
                        _db.ChangePassword(dbPassword);
                    });
                }
            }
            else
            {
                Logger.Error(Strings.OldPinCodeMismatch, null);
                await _dialogService.Alert(Strings.Error, Strings.OldPinCodeMismatch);
            }
        }

        public void SwitchUser()
        {
            LoggerManager.Insights.TrackEvent("SwitchUser", Insights.SystemEventType);

            IDialogService dialogService = this.Resolve<IDialogService>();
            _session = dialogService.ShowLoadingDialog(Strings.UploadingMessages, false);

            IEventAggregator eventAggregator = this.Resolve<IEventAggregator>();
            eventAggregator.GetEvent<SyncServiceStatusChangedEvent>()?.Subscribe(OnSyncServiceStatusChanged, ThreadOption.UIThread);

            // Wait till all pending transactions are sent
            if (TryResolve(out ISyncController syncController))
            {
                syncController.RequestSync(true, false);
            }

            _settings.Set(SwitchUserFlag, true);
            _ = MSAccessTokenProvider.LogOut();
        }

        private void OnSyncServiceStatusChanged(SyncServiceStatusChangedEventArgs args)
        {
            if (!args.Status.IsSyncing && _session != null)
            {
                ITransactionSyncDataHandler syncDataHandler = this.Resolve<ITransactionSyncDataHandler>();

                if (syncDataHandler?.HasUnsentMessages() == false)
                {
                    _session.LoadingFinished();
                    _session = null;

                    IEventAggregator eventAggregator = this.Resolve<IEventAggregator>();
                    eventAggregator.GetEvent<SyncServiceStatusChangedEvent>()?.Unsubscribe(OnSyncServiceStatusChanged);
                    _ = SwitchUserAfterSync();
                }
            }
        }

        private async Task SwitchUserAfterSync()
        {
            PlatformServices.SimulateNoConnection = false;
            CurrentSession.Account.UserName = null;

            if (CurrentSession.Account.IdentityProvider != null)
            {
                using (IIfsConnection tempConnection = await CreateConnection(CurrentSession.Account))
                {
                    await tempConnection.TouchAppsComms.LogoutIdpSessionAsync(CurrentSession.Account);
                }
            }

            await LogoutAsync();
        }

        public async Task LogoutAsync()
        {
            LoggerManager.Insights.TrackEvent("Logout", Insights.SystemEventType);

            await EndSessionAsync();

            _db.Disconnect();

            _settings.Set(UserLoggedOutSettingName, true);
        }

        #endregion

        public async Task<string> GetDatabasePassword()
        {
            {
                if (!CurrentSession.Account.PinAuthentication)
                {
                    return PlatformServices.SaltPassword(null);
                }

                IPlatformApplication openIdAuthenticator = this.Resolve<IPlatformApplication>();
                string pin = await openIdAuthenticator.ShowGetPinCodeDialogAsync(Strings.EnterPinCodeMessage);
                if (pin == null)
                {
                    return null;
                }
                string dbPassword = PlatformServices.SaltPassword(pin);
                if (await Task.Run(() => _db.ValidatePassword(dbPassword)))
                {
                    return dbPassword;
                }
                else
                {
                    return await GetDatabasePassword();
                }
            }
        }

        private async Task SaveAuthTokens()
        {
            IdentityProvider identityProvider = CurrentSession?.Account?.IdentityProvider;
            if (identityProvider != null)
            {
                await SaveAuthTokens(identityProvider);
            }
        }

        private async Task SaveAuthTokens(IdentityProvider identityProvider)
        {
            if (identityProvider == null)
                throw new ArgumentNullException(nameof(identityProvider));

            await Task.Run(() =>
            {
                FwDataContext ctx = _db.CreateDataContext();
                OpenIdData openIdData = ctx?.OpenIdData.FirstOrDefault();
                if (openIdData != null)
                {
                    ctx.OpenIdData.Attach(openIdData);
                    openIdData.Ipi = identityProvider.Ipi;
                    openIdData.RefreshToken = identityProvider.RefreshToken;
                    openIdData.AccessToken = identityProvider.AccessToken;
                    openIdData.IdToken = identityProvider.IdToken;
                    ctx.SubmitChanges(false);
                }
            });
        }

        private async Task LoadIdpAndAuthInfo(TouchAppAccount account)
        {
            await Task.Run(() =>
            {
                FwDataContext ctx = _db.CreateDataContext();
                OpenIdData openIdData = ctx?.OpenIdData.FirstOrDefault();
                if (openIdData != null && account != null)
                {
                    account.IdentityProvider = account.IdentityProvider ?? new IdentityProvider(openIdData.Ipi) { ClientId = "IFS_aurena_native" }; // ClientId is needed for refresh flow
                    account.IdentityProvider.RefreshToken = openIdData.RefreshToken;
                    account.IdentityProvider.AccessToken = openIdData.AccessToken;
                    account.IdentityProvider.IdToken = openIdData.IdToken;
                }
                else if (account != null && account.IdentityProvider != null)
                {
                    account.IdentityProvider.RefreshToken = null;
                    account.IdentityProvider.AccessToken = null;
                    account.IdentityProvider.IdToken = null;
                }
            });
        }

        public async Task RefreshLogin()
        {
            try
            {
                TouchAppAccount account = CurrentSession.Account;
                if (account == null)
                {
                    throw new ArgumentException(nameof(account));
                }

                IIfsConnection connection = this.Resolve<IIfsConnection>();

                IdentityProvider newIdentityProvider = await ReauthenticateAccount(connection, account);

                if (newIdentityProvider == null)
                {
                    // user cancelled
                    return;
                }

                connection.TouchAppsComms.LoginOffline(account, account.IdentityProvider.AccessToken);

                if (TryResolve(out ISyncController syncController))
                {
                    syncController.RequestSync();
                }
            }
            catch (Exception ex)
            {
                Logger.HandleException(ExceptionType.Recoverable, ex);
                await _dialogService.ShowException(ex, title: Strings.AuthFailed);
            }
        }

        public async Task<List<ThemeData>> BrandingData()
        {
            List<ThemeData> data = new List<ThemeData>();
            try
            {
                IIfsConnection connection = this.Resolve<IIfsConnection>();
                data = await connection.TouchAppsComms.GetBrandingThemeData();
                data = data.OrderByDescending(theme => theme.Code).ToList();
                return data;
            }
            catch (Exception ex)
            {
                Logger.HandleException(ExceptionType.Recoverable, ex);
                await _dialogService.ShowException(ex, title: Strings.AuthFailed);
                return null;
            }
        }

        private void FinalizeTransactionSessions()
        {
            FwDataContext ctx = _db.CreateDataContext();

            if (ctx == null)
            {
                return;
            }

            IEnumerable<FndTransactionSession> openSessions = ctx.TransactionSessions.Where(x => x.IsOpen == true);
            List<string> closedSessions = new List<string>();

            foreach (FndTransactionSession session in openSessions)
            {
                ctx.TransactionSessions.Attach(session);
                session.IsOpen = false;
                closedSessions.Add(session.SessionId);
            }

            if (closedSessions.Any())
            {
                ctx.SubmitChanges(false);
                Logger.Warning("Transaction session(s) finalized as the app was unexpectedly closed. Session ID(s): " + string.Join(", ", closedSessions.ToArray()));
            }
        }

        public async Task AddClientLog(string messageText, string type)
        {
            try
            {
                IIfsConnection connection = await CreateConnection(CurrentSession.Account);
                await connection.TouchAppsComms.AddClientLog(CurrentSession.Account, CurrentSession.Account.AppName, messageText, type);
            }
            catch (Exception ex)
            {
                //Error msg should be displayed after implementing MOBOFF-1895 
                Logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }
        #region Demo Mode

        public async Task LoginTryMeMode()
        {
            Assembly assembly = Registration.GetType().GetTypeInfo().Assembly;
            string demoData = null;

            await Task.Run(() =>
            {
                demoData = assembly.LoadResourceAsString("TryMeModeData.xml");

                string developerMetadata = assembly.LoadResourceAsString("DeveloperMetadata.json");

                // remove jsoncallback tags
                developerMetadata = Regex.Replace(developerMetadata, @"\[\s*#\s*\[\s*jsoncallback\s*:\s*[\s\S]*?\]\s*#\s*\]", string.Empty, RegexOptions.None, TimeSpan.FromSeconds(10));

                // remove expression tags
                developerMetadata = Regex.Replace(developerMetadata, @"\[#\[expression:(.*)\]#\]", "$1", RegexOptions.None, TimeSpan.FromSeconds(10));

                // remove callback tags
                developerMetadata = Regex.Replace(developerMetadata, "\".*\"*:*\\[#*\\[callback:.*\\]#\\],", string.Empty, RegexOptions.None, TimeSpan.FromSeconds(10));

                // remove metadatacallback tags
                developerMetadata = Regex.Replace(developerMetadata, @"\[\s*#\s*\[\s*metacallback\s*:\s*[\s\S]*?\]\s*#\s*\]", string.Empty, RegexOptions.None, TimeSpan.FromSeconds(10));

                if (!string.IsNullOrWhiteSpace(developerMetadata))
                {
                    string developerMetadataForProjection = assembly.LoadResourceAsString("DeveloperMetadataForProjection.json");

                    // remove jsoncallback tags
                    developerMetadataForProjection = Regex.Replace(developerMetadataForProjection, @"\[\s*#\s*\[\s*jsoncallback\s*:\s*[\s\S]*?\]\s*#\s*\]", string.Empty, RegexOptions.None, TimeSpan.FromSeconds(10));

                    // remove expression tags
                    developerMetadataForProjection = Regex.Replace(developerMetadataForProjection, @"\[#\[expression:(.*)\]#\]", "$1", RegexOptions.None, TimeSpan.FromSeconds(10));

                    // remove callback tags
                    developerMetadataForProjection = Regex.Replace(developerMetadataForProjection, "\".*\"*:*\\[#*\\[callback:.*\\]#\\],", string.Empty, RegexOptions.None, TimeSpan.FromSeconds(10));

                    // remove metadatacallback tags
                    developerMetadataForProjection = Regex.Replace(developerMetadataForProjection, @"\[\s*#\s*\[\s*metacallback\s*:\s*[\s\S]*?\]\s*#\s*\]", string.Empty, RegexOptions.None, TimeSpan.FromSeconds(10));

                    if (!string.IsNullOrWhiteSpace(developerMetadataForProjection))
                    {
                        Newtonsoft.Json.Linq.JObject clientMetaData = Newtonsoft.Json.Linq.JObject.Parse(developerMetadata);
                        Newtonsoft.Json.Linq.JObject projectionMetaData = Newtonsoft.Json.Linq.JObject.Parse(developerMetadataForProjection);

                        string version = clientMetaData.Value<string>("version");
                        string projectionVersion = version.Substring(version.IndexOf(':') + 1);

                        projectionMetaData.SelectToken("version").Replace(projectionVersion);
                        clientMetaData.SelectToken("projection").Replace(projectionMetaData);
                        developerMetadata = clientMetaData.ToString();
                    }

                    //[#[translatesys:Group Box:FndTstOffline.group.CustomerAssistantGroupContact.$this:$this:WEB:Contact Details]#]
                    //[#[translatesys:Wizard Step:FndTstOffline.assistant.CreateCustomerAssistant.assistantsteps.assistantstep.-1403991667:assistantstep:WEB:Customer Name]#]
                    string translatedData = Regex.Replace(developerMetadata, @"\[#\[translatesys:.*\:WEB\:(.*)\]#\]", "$1", RegexOptions.None, TimeSpan.FromSeconds(10));

                    //[#[translatesys:Iid Element:FndTstOffline.TestRadioEnum.SHORT_ENUMERATION:Text:SVC:Short enumeration]#]
                    translatedData = Regex.Replace(translatedData, @"\[#\[translatesys:Iid Element:.*:Text:SVC:(.*)\]#\]", "$1", RegexOptions.None, TimeSpan.FromSeconds(10));

                    //[#[translateEnum:Tst_Inventory_Part_Type_API.Decode('MANUFACTURED'):Manufactured]#]
                    //[#[translateEnum:Tst_Todo_Item_API.Finite_State_Decode__('COMPLETE'):Complete]#]
                    translatedData = Regex.Replace(translatedData, @"\[#\[translateEnum:.*_API.*Decode.*\('.*'\):(.*)\]#\]", "$1", RegexOptions.None, TimeSpan.FromSeconds(10));

                    MetadataBlob blob = MetadataBlob.FromJson(translatedData);
                    string newMetadataBlob = Convert.ToBase64String(blob.ToByteArray());
                    demoData = Regex.Replace(demoData, "<metadata_blob>.*<\\/metadata_blob>", $"<metadata_blob>{newMetadataBlob}</metadata_blob>", RegexOptions.None, TimeSpan.FromSeconds(10));
                }
            });

            string userName = null;

            await Task.Run(() =>
            {
                if (CurrentSession != null)
                {
                    throw new InvalidOperationException(Strings.CannotLoginWhileASessionIsStillActive);
                }

                _db.CreateAndLoginDatabaseFromData(DemoDatabaseId, demoData, out userName);
            });

            await StartSessionAsync(userName, null, null);
        }

        public void ResetPinCodeAttempt()
        {
            _attempts = Convert.ToInt32(_pincodeSettings.Get(OfflinePinCodeConfigurations.PinCodeAttemptsKey));
        }

        #endregion
    }
}
