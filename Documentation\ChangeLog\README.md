Change log entries should be added with every pull request. Client developers will use this information to check which build has a fix for their bugs and see any new functionality we have added.

**Adding a change log entry**

Change log entries can be created by running the AddChangeLogEntry.bat batch file in Documentation\ChangeLog.

![Command Prompt Output](commandprompt.png "Command Prompt Output")

This will create a new file in the Unreleased folder that should be checked in with your pull request.

When release builds are done all these change log entries are combined together and added under a new section in the ChangeLog.md file.

In the past we had framework developers add the change log entries to the change log file themselves. We decided not to do this since it causes issues when raising pull requests.  Normally when adding to a change log you are likely to be adding at the same line as another developer. This means if there is more than one pull request open at a time and one is merged any other pull request suddenly has merge conflicts. To fix this issue we added the AddChangeLogEntry.bat tool to create change log entry files which will not conflict with other developers.

**Platform considerations**

* If the change is done on only one platform (even with changes in Shared projects), choose the platform when creating the change log file.
* When the changes are only in Shared projects, choose Shared.
* In the case changes are made in multiple platforms. You can either copy-paste the initially generated file and rename + edit content, or generate multiple times with different platforms and edit.
    * Keep the content of all files the same.
    * Be mindful about trailing spaces if you are manually editing. All files should have the same formatting, or otherwise the change log generator may fail.
    * File names and content don't have to match. Feel free to rename by suffixing the platform name, for example.
    * Easiest way would be to duplicate the file, and edit the platform part and file name suffix only. You might want to rename the original file to reflect the platform as well, rather than keeping it without a platform suffix.