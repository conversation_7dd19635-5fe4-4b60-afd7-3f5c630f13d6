﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  2012-09-17 PKULLK Created.
#endregion

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Ifs.Cloud.Client.Exceptions
{
    /// <summary>
    /// Thrown when implementation finds an unsupported value.
    /// Ex: An enumeration value that the code doesn't currently handle.
    /// </summary>
    public class UnsupportedValueException : Exception
    {
        /// <summary>
        /// Creates an UnsupportedValueException object.
        /// </summary>
        /// <param name="message">Message</param>
        /// <param name="value">Value that is not supported</param>
        /// <param name="variable">Name of the variable that contains the unsupported value</param>
        public UnsupportedValueException(string message, object value, string variable)
            : base(message)
        {
            this.Value = value;
            this.Variable = variable;
        }

        /// <summary>
        /// Creates an UnsupportedValueException object.
        /// </summary>
        /// <param name="value">Value that is not supported</param>
        /// <param name="variable">Name of the variable that contains the unsupported value</param>
        public UnsupportedValueException(object value, string variable)
            : this(string.Format("Unsupported Value '{0}' in '{1}'", value, variable), value, variable)
        {
        }

        /// <summary>
        /// Value that is not supported.
        /// </summary>
        public object Value { get; private set; }

        /// <summary>
        /// Name of the variable (or argument) that the value was retrieved.
        /// </summary>
        public string Variable { get; private set; }
    }
}
