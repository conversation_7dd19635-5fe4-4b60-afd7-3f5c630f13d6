﻿using System.Collections.Generic;
using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Messages;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class ClientMetadataResource : AppResource, ICustomResourceSerializer, IQueryStringProvider
    {
        public override string ResourceName => "MobileClientRuntime.svc/ClientMetaData";

        public string Contents { get; set; }

        public string ScopeId { get; set; }

        [DataMember]
        public string ClientName { get; set; }

        public object DeserializeJsonString(string jsonString)
        {
            return new ClientMetadataResource { Contents = jsonString };
        }

        public object EmptyResponseHandling(bool isSuccess)
        {
            return isSuccess ? ExecuteResult.True : ExecuteResult.False;
        }

        public string GetQueryString()
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            return Cloud.Client.Utils.Formatter.ToQueryString(parameters);
        }

        public string SerializeToJsonString()
        {
            JObject jObj = new JObject();
            jObj.Add("ClientName", ClientName);
            jObj.Add("ScopeId", ScopeId);
            return MessageUtils.JObjectToString(jObj);
        }
    }
}
