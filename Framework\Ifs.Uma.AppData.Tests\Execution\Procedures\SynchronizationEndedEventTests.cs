﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Metadata;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class SynchronizationEndedEventTests : ProcedureTest
    {
        [Test]
        public async Task EventSynchronizationEndedAdvanced()
        {
            IMetadata metadata = Resolve<IMetadata>();
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["ChangedEntities"] = new List<string>() { "TstCustomer" };
            List<FndRecordRowId> changedRows = new List<FndRecordRowId>();

            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Customers");
            EntityQuery query = new EntityQuery(source);
            FwDataContext ctx = CreateDataContext();
            EntityRecord[] rows = ctx.Query(query).ToArray();

            FndRecordRowId rec1 = new FndRecordRowId();
            rec1.RecordRowId = rows[0].Row.RowId;
            rec1.RecordTableName = "tst_customer";
            changedRows.Add(rec1);

            FndRecordRowId rec2 = new FndRecordRowId();
            rec2.RecordRowId = rows[1].Row.RowId;
            rec2.RecordTableName = "tst_customer";
            changedRows.Add(rec2);

            parameters["RowIds"] = changedRows;

            ExecuteResult result = await executor.CallEventAsync(TestOfflineProjection, "SynchronizationEnded", parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);
            Assert.AreNotEqual(false, result.Value); // Note that the actual SynchronizationEnded event does not return a value
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.SynchronizationEndedEventSchema", "Execution.Procedures.CustomerData");
        }
    }
}

// Below is the Marble code that runs as the SynchronizationEnded event
// SynchronizationEndedEventSchema.json file contains the JSON interpretation of this

/*
@Overtake Core
procedure Event<SynchronizationEnded> {
   parameter ChangedEntities List<Text>;
   parameter RowIds List<Structure(FndRecordRowId)>;

   variable RowIdStruct Structure(FndRecordRowId);
   variable ListCount Integer;
   variable Counter Integer;
   variable PrimaryKey Text;
   variable TableName Text;
   variable RowId Number;

   execute {
      set Counter = 0;
      call List.Count(RowIds) into ListCount;

      while [Counter < ListCount] {
         call List.Get(RowIds, Counter) into RowIdStruct;
         set TableName = RowIdStruct.RecordTableName;
         set RowId = RowIdStruct.RecordRowId;
         call System.GetPkFromRowId(TableName, RowId) into PrimaryKey;

         if [Counter = 0 and PrimaryKey != "CUSTOMER_NO=500^"] {
            return false;
         }

         if [Counter = 1 and PrimaryKey != "CUSTOMER_NO=501^"] {
            return false;
         }

         set Counter = [Counter+1];
      }
   }
}
*/
