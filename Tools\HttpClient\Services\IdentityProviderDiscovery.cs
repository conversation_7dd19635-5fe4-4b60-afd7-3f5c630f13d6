using System.Text.Json;
using Ifs.Tools.HttpClient.Models;

namespace Ifs.Tools.HttpClient.Services
{
    /// <summary>
    /// Discovers OAuth2/OpenID Connect identity provider information from IFS servers
    /// This replicates the logic from GetIdentityProviderInformation() and GetKeyCloakInfo()
    /// </summary>
    public class IdentityProviderDiscovery
    {
        private readonly System.Net.Http.HttpClient _httpClient;
        private const string IPIEndpointSuffix = "/mob/ifsapplications/projection/v1/MobileClientRuntime.svc/GetIdentityProviderInformation";

        public IdentityProviderDiscovery(System.Net.Http.HttpClient? httpClient = null)
        {
            _httpClient = httpClient ?? new System.Net.Http.HttpClient();
        }

        /// <summary>
        /// Discovers identity provider information from an IFS server
        /// </summary>
        /// <param name="baseUrl">Base URL of the IFS server (e.g., https://server.com)</param>
        /// <param name="appName">Application name (optional)</param>
        /// <returns>Identity provider information or null if not available</returns>
        public async Task<IdentityProvider?> GetIdentityProviderInformation(string baseUrl, string? appName = null)
        {
            try
            {
                // First, try to get the identity provider information from the IFS endpoint
                string ipiEndpoint = baseUrl.TrimEnd('/') + IPIEndpointSuffix;

                var request = new HttpRequestMessage(HttpMethod.Get, ipiEndpoint);
                request.Headers.Add("User-Agent", "IFS-HttpClient-Tool/1.0");

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var ipiString = await response.Content.ReadAsStringAsync();

                    if (!string.IsNullOrEmpty(ipiString))
                    {
                        // Parse the IPI response to extract the OpenID configuration URL
                        var discoveryUrl = ExtractDiscoveryUrl(ipiString);

                        if (!string.IsNullOrEmpty(discoveryUrl))
                        {
                            return await GetOpenIdConfiguration(discoveryUrl);
                        }
                    }
                }

                // If IPI endpoint fails, try common OpenID discovery endpoints
                return await TryCommonDiscoveryEndpoints(baseUrl);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to discover identity provider: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Extracts the OpenID discovery URL from the IPI response
        /// This replicates the logic from GetKeyCloakInfo()
        /// </summary>
        private string? ExtractDiscoveryUrl(string ipiString)
        {
            try
            {
                // The IPI response contains a Bearer realm in the format: Bearer realm="realm@server"
                if (ipiString.Contains("Bearer realm="))
                {
                    string[] ipiValues = ipiString.Split(' ');
                    foreach (var value in ipiValues)
                    {
                        if (value.StartsWith("realm="))
                        {
                            string bearerRealm = value.Substring(7, value.Length - 8); // Remove 'realm="' and '"'
                            string[] bearerRealmValues = bearerRealm.Split('@');

                            if (bearerRealmValues.Length > 1)
                            {
                                return $"https://{bearerRealmValues[1]}/.well-known/openid-configuration";
                            }
                        }
                    }
                }
            }
            catch
            {
                // Ignore parsing errors
            }

            return null;
        }

        /// <summary>
        /// Gets OpenID Connect configuration from a discovery URL
        /// </summary>
        private async Task<IdentityProvider?> GetOpenIdConfiguration(string discoveryUrl)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Get, discoveryUrl);
                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    return IdentityProvider.FromDiscoveryDocument(json);
                }
            }
            catch
            {
                // Ignore errors
            }

            return null;
        }

        /// <summary>
        /// Tries common OpenID discovery endpoints if the IFS-specific endpoint fails
        /// </summary>
        private async Task<IdentityProvider?> TryCommonDiscoveryEndpoints(string baseUrl)
        {
            var commonEndpoints = new[]
            {
                "/.well-known/openid-configuration",
                "/auth/realms/master/.well-known/openid-configuration",
                "/auth/realms/ifs/.well-known/openid-configuration"
            };

            foreach (var endpoint in commonEndpoints)
            {
                try
                {
                    var discoveryUrl = baseUrl.TrimEnd('/') + endpoint;
                    var identityProvider = await GetOpenIdConfiguration(discoveryUrl);

                    if (identityProvider != null)
                    {
                        return identityProvider;
                    }
                }
                catch
                {
                    // Continue to next endpoint
                }
            }

            return null;
        }
    }
}
