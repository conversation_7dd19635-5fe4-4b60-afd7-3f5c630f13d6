<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:orientation="vertical"
  android:layout_width="match_parent"
  android:layout_height="match_parent">

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentTop="true"
    android:orientation="horizontal"
    android:baselineAligned="false"
    android:gravity="right"
    android:id="@+id/resource_button_layout">
  </LinearLayout>

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentTop="true"
    android:layout_weight="1"
    android:orientation="horizontal">

    <RelativeLayout
      android:layout_width="match_parent"
      android:layout_height="match_parent">

      <Com.Syncfusion.Schedule.SfSchedule
        android:id="@+id/calendar_View"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

      <androidx.recyclerview.widget.RecyclerView
        android:layout_width="match_parent"
        android:layout_height="match_parent" 
        android:id="@+id/agenda_recycler_view"
        android:scrollbars="vertical"
        android:visibility="gone"/>

      <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/fab"
        android:layout_alignParentRight="true"
        android:layout_marginRight="4dp"
        android:layout_marginTop="4dp"/>

      <TextView
        android:id="@+id/no_items"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/No_Appointments"
        android:layout_marginTop="300dp"
        android:textAlignment="center"
        android:visibility="gone"/>

    </RelativeLayout>
  </LinearLayout>

  <ScrollView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/card_container"
    android:layout_marginTop="5dp"
    android:visibility="gone">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical">

      <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="38dp">

        <TextView
          android:layout_width="wrap_content"
          android:layout_height="match_parent"
          android:gravity="center_vertical"
          android:layout_alignParentStart="true"
          android:layout_marginLeft="16dp"
          android:id="@+id/card_panel_count_label"/>

        <ImageButton
          android:layout_width="38dp"
          android:layout_height="38dp"
          android:layout_alignParentEnd="true"
          android:background="@android:color/transparent"
          android:id="@+id/card_panel_close_button"/>

      </RelativeLayout>

      <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:id="@+id/card_panel_bottom_row">

        <ImageButton
          android:layout_width="38dp"
          android:layout_height="match_parent"
          android:layout_weight="1"
          android:background="@android:color/transparent"
          android:id="@+id/card_panel_back_button"/>

        <ImageButton
          android:layout_width="38dp"
          android:layout_height="match_parent"
          android:layout_weight="1"
          android:background="@android:color/transparent"
          android:id="@+id/card_panel_forward_button"/>

      </LinearLayout>
    </LinearLayout>
  </ScrollView>
</LinearLayout>
