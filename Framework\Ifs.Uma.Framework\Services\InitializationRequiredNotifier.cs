﻿using System;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Localization;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Services
{
    internal sealed class InitializationRequiredNotifier : InitializeStatusWatcher
    {
        private readonly IDialogService _dialogService;

        public InitializationRequiredNotifier(ILogger logger, ITransactionSyncService transactionSyncService, IDialogService dialogService)
            : base(logger, transactionSyncService, ThreadOption.UIThread)
        {
            _dialogService = dialogService;
        }

        protected override bool OnMatchEventFilter(InitializeStatus status)
        {
            return status == InitializeStatus.Initialized_WithInitRequired && !Service.IsInitializing;
        }

        protected override async void OnEvent(InitializeStatus status)
        {
            try
            {
                await _dialogService.WaitForDialogsToClose(ServiceCancelToken);

                if (IsRunning && !Service.IsInitializing && Service.InitializeStatus == InitializeStatus.Initialized_WithInitRequired)
                {
                    CustomButtonsResult result = await _dialogService.CustomButtons(Strings.Reinitialize, Strings.SchemaChangedMessage, Strings.Initialize, Strings.Later);
                    if (result == CustomButtonsResult.Positive)
                    {
                        Service.RequestInitialization(true);
                    }
                }
            }
            catch (Exception ex)
            {
                LogException(ex);
            }
        }
    }
}
