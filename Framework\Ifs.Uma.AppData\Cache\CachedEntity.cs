﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Cache
{
    internal class CachedEntity : DataAccessor<FwDataContext>, IDisposable
    {
        private static readonly TimeSpan PreferredQueryDuration = TimeSpan.FromSeconds(10);
        internal const int InitialTakeCount = 25;
        private const int MinimumTakeCount = 10;

        private readonly ILogger _logger;
        private readonly IOnlineDataHandler _onlineData;
        private readonly EntityDataSource _dataSource;
        private readonly object _beginUpdateLock = new object();
        private readonly CancellationTokenSource _cts = new CancellationTokenSource();
        private readonly CacheExpiry _cacheExpiry;

        private Task<PrepareCacheResult> _runningUpdate;

        public CachedEntity(IMetadata metadata, IDataContextProvider db, ILogger logger, IPerfLogger perfLogger,
            IOnlineDataHandler onlineData, string projectionName, string entityName)
            : base(db, logger, perfLogger)
        {
            _logger = logger;
            _onlineData = onlineData;
            _dataSource = EntityDataSource.FromEntity(metadata, projectionName, entityName);
            _dataSource.EntitySetName = metadata.GetFirstEntitySet(projectionName, entityName);
            if (_dataSource == null) throw new ExecutionException($"Invalid query set '{projectionName}.{entityName}'");
            _cacheExpiry = CacheExpiry.FromEntity(metadata, logger, projectionName, entityName);
        }

        public async Task<bool> IsReady()
        {
            return await WithDataContextAsync(IsReadyImpl, $"IsCacheReady<{_dataSource.ProjectionName}.{_dataSource.EntityName}>");
        }

        private bool IsReadyImpl(FwDataContext ctx)
        {
            CacheStatus cacheStatus = ctx.CacheStatuses.FirstOrDefault(x => x.ProjectionName == _dataSource.ProjectionName && x.EntityName == _dataSource.EntityName);

            if (cacheStatus == null || !cacheStatus.IsReady || !cacheStatus.LastUpdated.HasValue)
            {
                return false;
            }

            if (_onlineData.IsOnline)
            {
                DateTime lastUpdate = cacheStatus.LastUpdated.Value.ToClientLocalTime();
                return !_cacheExpiry.HasExpired(lastUpdate);
            }

            return true;
        }

        public Task<PrepareCacheResult> Update()
        {
            _cts.Token.ThrowIfCancellationRequested();

            Task<PrepareCacheResult> runningUpdate;

            lock (_beginUpdateLock)
            {
                runningUpdate = _runningUpdate;

                if (runningUpdate == null || runningUpdate.IsCompleted)
                {
                    runningUpdate = WithDataContextAsync(UpdateImpl, $"Update<{_dataSource.ProjectionName}.{_dataSource.EntityName}>");
                    _runningUpdate = runningUpdate;
                }
            }

            return runningUpdate;
        }

        private Task<PrepareCacheResult> UpdateImpl(FwDataContext ctx)
        {
            CacheStatus cacheStatus = PrepareCacheForUpdate(ctx);
            return UpdateRowsAsync(ctx, cacheStatus);
        }

        private CacheStatus PrepareCacheForUpdate(FwDataContext ctx)
        {
            CacheStatus cacheStatus = ctx.CacheStatuses.FirstOrDefault(x => x.ProjectionName == _dataSource.ProjectionName && x.EntityName == _dataSource.EntityName);
            if (cacheStatus == null)
            {
                cacheStatus = new CacheStatus();
                cacheStatus.ProjectionName = _dataSource.ProjectionName;
                cacheStatus.EntityName = _dataSource.EntityName;
                ctx.CacheStatuses.InsertOnSubmit(cacheStatus);
            }
            else
            {
                ctx.CacheStatuses.Attach(cacheStatus);
            }
            cacheStatus.IsReady = false;
            ctx.SubmitChanges(false);
            return cacheStatus;
        }

        private async Task<PrepareCacheResult> UpdateRowsAsync(FwDataContext ctx, CacheStatus cacheStatus)
        {
            _logger.Information($"Updating '{_dataSource.ProjectionName}.{_dataSource.EntityName}' client table cache");

            ctx.ClearCacheTable(_dataSource.Table);

            EntityQuery query = new EntityQuery(_dataSource);

            PrepareCacheResult result = await AddToCacheAsync(ctx, query, _cts.Token);
            if (result == PrepareCacheResult.Offline)
            {
                ctx.ClearCacheTable(_dataSource.Table);
                return PrepareCacheResult.Offline;
            }

            ctx.CacheStatuses.Attach(cacheStatus);
            cacheStatus.IsReady = true;
            cacheStatus.LastUpdated = DateTime.UtcNow;
            ctx.SubmitChanges(false);

            _logger.Information($"Finished updating '{_dataSource.ProjectionName}.{_dataSource.EntityName}' client table cache");

            return result;
        }

        public async Task<PrepareCacheResult> AddToCacheAsync(EntityQuery query, CancellationToken cancelToken)
        {
            query = query.Clone();

            CancellationTokenSource cts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token, cancelToken);

            return await WithDataContextAsync(
                async ctx => await AddToCacheAsync(ctx, query, cts.Token),
                $"AddToCacheAsync<{_dataSource.ProjectionName}.{_dataSource.EntityName}>");
        }

        private async Task<PrepareCacheResult> AddToCacheAsync(FwDataContext ctx, EntityQuery query, CancellationToken cancelToken)
        {
            bool loadAll = !query.Take.HasValue;

            if (!query.Take.HasValue)
            {
                query.Take = InitialTakeCount;
            }

            if (!query.Skip.HasValue)
            {
                query.Skip = 0;
            }

            List<double> durations = new List<double>();

            while (true)
            {
                Stopwatch loopTime = Stopwatch.StartNew();
                Stopwatch dataDownloadTime = Stopwatch.StartNew();

                EntityQueryResult result = await _onlineData.GetRecordsAsync(query, cancelToken).ConfigureAwait(false);

                dataDownloadTime.Stop();

                if (result.DataSourceOffline)
                {
                    return PrepareCacheResult.Offline;
                }

                cancelToken.ThrowIfCancellationRequested();

                RemoteRow[] rows = result.Records.Select(x => x.Row).ToArray();
                ctx.UpsertCacheTable(_dataSource.Table, rows);

                loopTime.Stop();

                // Log the time for data processing of each loop
                _logger.Information($"Cache entity iteration time: {result.Records.Count()} records, {loopTime.ElapsedMilliseconds} ms");
                durations.Add(loopTime.Elapsed.TotalSeconds);

                bool canContinue = result.Records.Any() && loadAll && result.Records.Count() == query.Take;
                if (!canContinue)
                {
                    break;
                }

                // Calculate average duration of the last few iterations
                double avgDuration = durations.Skip(Math.Max(0, durations.Count - 5)).Average();

                // Calculate the ratio of the elapsed time to the PreferredQueryDuration
                // Adjust `Take` based on average duration
                double ratio = avgDuration / PreferredQueryDuration.TotalSeconds;

                if (ratio >= 1)
                {
                    query.Take = Math.Max(MinimumTakeCount, query.Take.Value / 2);
                }
                else if (ratio >= 0.5)
                {
                    // If ratio is between 0.5 and 1, adjust to approach 80% of PreferredQueryDuration
                    query.Take = (int)((query.Take.Value / ratio) * 0.8);
                }
                else
                {
                    query.Take = (int)((query.Take.Value / ratio) * 0.9);
                }

                query.Skip += rows.Length;
            }

            return PrepareCacheResult.Ready;
        }

        public void Dispose()
        {
            _cts.Cancel();
        }
    }
}
