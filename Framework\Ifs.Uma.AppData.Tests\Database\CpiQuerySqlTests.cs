﻿using Ifs.Uma.AppData.Model;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Database
{
    [TestFixture]
    public class CpiQuerySqlTests : FrameworkTest
    {
        [Test]
        public void Basic()
        {
            string sql = GetSql("OfflineQueryBasic");
            Assert.AreEqual("SELECT c.customer_no FROM tst_customer c", sql);
        }

        [Test]
        public void UnionTest()
        {
            string sql = GetSql("OfflineQueryUnion");
            Assert.AreEqual("SELECT customer_no, NULL  AS type_id FROM tst_customer UNION ALL SELECT NULL  " +
                "AS customer_no, type_id FROM tst_customer_type",
                sql);
        }

        [Test]
        public void Full()
        {
            string sql = GetSql("OfflineQueryFull");
            Assert.AreEqual(
                "SELECT DISTINCT ct.type_id, c$CustomerTypeRef.type_description AS description " +
                "FROM tst_customer c " +
                "INNER JOIN tst_customer_type ct ON (ct.type_id=c.customer_type) " +
                "LEFT OUTER JOIN tst_customer_type c$CustomerTypeRef ON c$CustomerTypeRef.type_id=c.customer_type " +
                "WHERE (ct.type_id=@p0) " +
                "@p0='TEST'", sql);
        }

        private string GetSql(string offlineQueryName)
        {
            IMetadata metadata = CreateMetadata<FwDataContext>(typeof(CpiQuerySqlTests), "Database.QuerySchema.json");
            IMetaTable table = metadata.GetTableForEntityName(offlineQueryName);
            ISelectSpec selectSpec = table.CreateViewSelectSpec();
            return SqlBuilder.BuildDebugGenericSql(selectSpec);
        }
    }
}
