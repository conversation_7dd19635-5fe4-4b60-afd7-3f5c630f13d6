﻿using System;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemLogLocation : SystemFunction
    {
        public const string FunctionName = "LogLocation";

        private readonly ILocationLogger _locationLogger;

        public SystemLogLocation(ILocationLogger locationLogger)
            : base(FunctionName, -1)
        {
            _locationLogger = locationLogger;
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string info = null;
            if (parameters.Length >= 1)
            {
                info = parameters[0].GetString();
            }
            
            Action action = () =>
            {
                _locationLogger.LogLocation(info);
            };

            context.AddPostTransactionAction(action);
            return null;
        }
    }
}
