<?xml version='1.0' encoding='utf-8'?>
<dataexport>
  <data>

    <tst_customer>
      <customer_no>1</customer_no>
      <customer_name>A</customer_name>
      <customer_type>TYPE_A</customer_type>
    </tst_customer>

    <tst_customer>
      <customer_no>2</customer_no>
      <customer_name>John</customer_name>
      <customer_type>TYPE_B</customer_type>
      <created_at>2006-07-26T08:40:02</created_at>
    </tst_customer>

    <tst_customer>
      <customer_no>3</customer_no>
      <customer_name>John</customer_name>
      <customer_type>TYPE_A</customer_type>
      <created_at>2006-07-26T12:40:02</created_at>
    </tst_customer>

    <tst_customer>
      <customer_no>4</customer_no>
      <customer_name>D</customer_name>
      <customer_type>TYPE_B</customer_type>
    </tst_customer>

    <tst_customer_type>
      <type_id>TYPE_A</type_id>
      <type_description>Customer Type A</type_description>
    </tst_customer_type>

    <tst_customer_type>
      <type_id>TYPE_B</type_id>
      <type_description>Customer Type B</type_description>
    </tst_customer_type>

    <tst_expand>
      <id>1</id>
      <col_a>tst_1_col_a</col_a>
    </tst_expand>

    <tst_expand_num>
      <id>1</id>
      <col1>tst_col1</col1>
      <col2>tst_col2</col2>
    </tst_expand_num>

    <tst_expand_letter>
      <id>1</id>
      <col_a>tst_col_a</col_a>
      <col_b>tst_col_b</col_b>
    </tst_expand_letter>

  </data>
</dataexport>
