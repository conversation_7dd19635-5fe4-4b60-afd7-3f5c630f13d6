﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Rewrites take &amp; skip expressions into uses of TSQL row_number function
    /// </summary>
    internal class SkipToRowNumberRewriter : DbExpressionVisitor
    {
        private SkipToRowNumberRewriter()
        {
        }

        public static Expression Rewrite(Expression expression)
        {
            return new SkipToRowNumberRewriter().Visit(expression);
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            node = (SelectExpression)base.VisitSelect(node);
            if (node.Skip != null)
            {
                SelectExpression newSelect = node.SetSkip(null).SetTake(null);
                bool canAddColumn = !node.IsDistinct && (node.GroupBy == null || node.GroupBy.Count == 0);
                if (!canAddColumn)
                {
                    newSelect = newSelect.AddRedundantSelect(new TableAlias());
                }
                newSelect = newSelect.AddColumn(new ColumnDeclaration("_rownum", new RowNumberExpression(node.OrderBy)));

                // add layer for WHERE clause that references new rownum column
                newSelect = newSelect.AddRedundantSelect(new TableAlias());
                newSelect = newSelect.RemoveColumn(newSelect.Columns.Single(c => c.Name == "_rownum"));

                var newAlias = ((SelectExpression)newSelect.From).Alias;
                ColumnExpression rnCol = new ColumnExpression(typeof(int), newAlias, "_rownum");
                Expression where;
                if (node.Take != null)
                {
                    where = new BetweenExpression(rnCol, Expression.Add(node.Skip, Expression.Constant(1)), Expression.Add(node.Skip, node.Take));
                }
                else
                {
                    where = rnCol.GreaterThan(node.Skip);
                }
                if (newSelect.Where != null)
                {
                    where = newSelect.Where.And(where);
                }
                newSelect = newSelect.SetWhere(where);

                node = newSelect;
            }
            return node;
        }
    }
}