﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Maps
{
    [DataContract]
    public sealed partial class MapNavParam : NavigationParameter
    {
        public const string MapsUrlPrefix = "ifs-map://";

        private const string QueryLocation = "location?";

        private const string ParamAddress = "address";
        private const string ParamLatitude = "lat";
        private const string ParamLongitude = "long";

        [DataMember]
        public double Latitude { get; private set; }

        [DataMember]
        public double Longitude { get; private set; }

        [DataMember]
        public string Address { get; private set; }

        private MapNavParam() { }

        private MapNavParam(string urlParams)
        {
            Dictionary<string, string> parameters = urlParams.Split('&')
                .ToDictionary(s => s.Split('=')[0], s => (s.Split('=')[1]));

            if (parameters.ContainsKey(ParamAddress))
            {
                Address = parameters[ParamAddress];
            }

            if (parameters.ContainsKey(ParamLatitude))
            {
                Latitude = ObjectConverter.ToDouble(parameters[ParamLatitude]);
            }

            if (parameters.ContainsKey(ParamLongitude))
            {
                Longitude = ObjectConverter.ToDouble(parameters[ParamLongitude]);
            }
        }

        public static MapNavParam Create(string url)
        {
            try
            {
                string query = url.Replace(MapsUrlPrefix, string.Empty);

                if (!query.Contains(QueryLocation))
                {
                    return null;
                }

                string queryParams = query.Replace(QueryLocation, string.Empty);
                MapNavParam navParam = new MapNavParam(queryParams);

                if (!IsValid(navParam))
                {
                    return null;
                }

                return navParam;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private static bool IsValid(MapNavParam navParam)
        {
            return navParam != null && (navParam.Address != null || MapHelperShared.IsValidLatLng(navParam.Latitude, navParam.Longitude));
        }
    }
}
