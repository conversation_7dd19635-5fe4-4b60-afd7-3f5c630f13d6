﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Database;

namespace Ifs.Uma.Data
{
    /// <summary>
    /// Identifies a field or property as a table column in the database
    /// The type of the field or property may be
    /// string, byte[], long?, double?, bool?, long, double, bool, DateTimeOffset?, DateTimeOffset
    /// DateTime, DateTime?, int, int? short, short?
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public sealed class ColumnAttribute : Attribute
    {
        public ColumnAttribute()
        {
            Insertable = true;
            Updateable = true;
        }

        /// <summary>
        /// The name of the database column
        /// By default the Property or Field name converted to LowerCaseUnderscore
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// Properties only.
        /// The name of a private field in the class which holds the actual value.
        /// By default, the SetMethod of the Property is called to set the value.
        /// </summary>
        public string Storage { get; set; }
        /// <summary>
        /// The maximum length of a string in the database (could also apply to bytes)
        /// In Oracle, VARCHAR2 maximum is 4000, NVARCHAR2 maximum is 2000 and RAW maximum is 2000
        /// If unset (i.e. zero) we will assume a large value not a small one.
        /// Also used for precision of numbers
        /// </summary>
        public int MaxLength { get; set; }
        /// <summary>
        /// The number of decimal places of a number
        /// JVB: As in Oracle, if both MaxLength and Scale are zero (unset) then
        /// a number can have ANY amount of decimal places.
        /// </summary>
        public int Scale { get; set; }
        /// <summary>
        /// Identifies the columns that constitute the primary key.
        /// Primary Key columns are always mandatory.
        /// </summary>
        public bool PrimaryKey { get; set; }
        /// <summary>
        /// Identifies the columns that constitute the server primary key.
        /// </summary>
        public bool ServerPrimaryKey { get; set; }
        /// <summary>
        /// Identifies the column as the equivalent of a SQL Server Identity column
        /// </summary>
        public bool AutoIncrement { get; set; }
        /// <summary>
        /// Identifies whether the database column can be null or not
        /// Only required for class fields (string or byte[]) (default false)
        /// Presence or abscence of Nullable works for struct fields
        /// </summary>
        public bool Mandatory { get; set; }
        /// <summary>
        /// Identifies text formatting options for the database column
        /// </summary>
        public TextFormats TextFormat { get; set; }
        /// <summary>
        /// Identifies date formatting options for the database column
        /// </summary>
        public DateFormats DateFormat { get; set; }
        /// <summary>
        /// Identifies number formatting options for the database column
        /// </summary>
        public NumberFormat NumberFormat { get; set; }
        /// <summary>
        /// Identifies whether the column value can be supplied on an insert
        /// </summary>
        public bool Insertable { get; set; }
        /// <summary>
        /// Identifies whether the column value can be changed (updated)
        /// JVB: I personally think this should be called "Mutable" but
        /// this name came from an IFS Apps specification so I'll keep it.
        /// </summary>
        public bool Updateable { get; set; }
    }

    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
    public sealed class ColumnSyncRuleAttribute : Attribute
    {
        public ColumnSyncRuleAttribute() { }
        public Type RowType { get; set; }
        public string PropertyName { get; set; }
        public SyncRule Sync { get; set; }
    }

    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
    public sealed class ColumnNameAttribute : Attribute
    {
        public ColumnNameAttribute() { }
        public Type RowType { get; set; }
        public string PropertyName { get; set; }
        public string ColumnName { get; set; }
    }
}
