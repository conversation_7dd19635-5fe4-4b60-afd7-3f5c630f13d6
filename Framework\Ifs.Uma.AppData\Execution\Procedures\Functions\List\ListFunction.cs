﻿using System.Collections.Generic;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.List
{
    internal abstract class ListFunction : FwFunction
    {
        public const string FunctionNamespace = "List";

        public ListFunction(string name, int argCount)
            : base(FunctionNamespace, name, argCount)
        {
        }

        protected static MarbleList PrepareList(ProcedureContext context, FuncParam listParam)
        {
            string listName = listParam.GetVariableName();
            if (string.IsNullOrEmpty(listName))
            {
                throw context.Fail($"Invalid list '{listParam.RawValue}'");
            }

            CpiParam varInfo = context.GetVarInfo(listName);
            if (varInfo == null || !varInfo.IsCollection)
            {
                throw context.Fail($"Invalid list '{listName}'");
            }
            
            object listValue = context.GetValue(listName);
            
            MarbleList list = listValue as MarbleList;
            if (list == null)
            {
                IEnumerable<object> listToConvert = listValue as IEnumerable<object>;
                list = listToConvert == null ? new MarbleList(varInfo) : new MarbleList(listToConvert, varInfo);
                context.Assign(listName, list);
            }

            return list;
        }
    }
}
