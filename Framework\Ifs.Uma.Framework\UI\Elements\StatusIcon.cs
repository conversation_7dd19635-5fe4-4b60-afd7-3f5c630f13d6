﻿using System.Linq;
using Ifs.Uma.AppData;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using System.Threading.Tasks;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Images;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements
{
    public class StatusIcon : ObservableBase
    {
        private UmaImage _image;
        public UmaImage Image
        {
            get { return _image; }
            set { SetProperty(ref _image, value); }
        }

        public StatusIcon(UmaImage image)
        {
            _image = image;
        }

        public static async Task<StatusIcon> GetStatusIconAsync(IMetadata metadata, IDataHandler data)
        {
            string[] statusIconValues = await data.GetStatusIndicatorValues(metadata);

            if (statusIconValues == null || statusIconValues[0] == null)
            {
                return null;
            }

            UmaImage image = IconUtils.Load(statusIconValues[0]);
            image.Color = UmaColors.White;

            if (!string.IsNullOrEmpty(statusIconValues[1]))
            {
                UmaColor? color = UmaColor.FromEmphasis(statusIconValues[1]);
                if (color != null)
                {
                    image.Color = color;
                }
            }

            return new StatusIcon(image);
        }

        public static bool HasStatusChanged(IMetadata metadata, DataChangeSet changeSet)
        {
            FunctionInfo iconFunction = FunctionInfo.Get(metadata, metadata.GetStatusIndicator()?.IconFunction.Split('.')[0], metadata.GetStatusIndicator()?.IconFunction.Split('.')[1]);
            FunctionInfo colorFunction = FunctionInfo.Get(metadata, metadata.GetStatusIndicator()?.ColorFunction.Split('.')[0], metadata.GetStatusIndicator()?.ColorFunction.Split('.')[1]);
            Database.IMetaTable[] tableName = changeSet.EffectedTables.ToArray();

            if (iconFunction != null)
            {
                for (int i = 0; i < tableName.Length; i++)
                {
                    foreach (string table in iconFunction?.Function.LuDependencies)
                    {
                        if (table.ToLowerCaseUnderscore() == tableName[i].TableName)
                        {
                            return true;
                        }
                    }
                }
            }

            if (colorFunction != null)
            {
                for (int i = 0; i < tableName.Length; i++)
                {
                    foreach (string table in colorFunction?.Function.LuDependencies)
                    {
                        if (table.ToLowerCaseUnderscore() == tableName[i].TableName)
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public static async Task<StatusIcon> GetStatusIconOnChangeAsync(IMetadata metadata, IDataHandler data, DataChangeSet changeSet)
        {
            if (HasStatusChanged(metadata, changeSet))
            {
                return await GetStatusIconAsync(metadata, data);
            }

            return null;
        }
    }
}
