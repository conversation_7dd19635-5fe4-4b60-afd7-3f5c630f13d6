﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = FwDataContext.FwTablePrefix + "local_profile_value", System = true)]
    public class LocalProfileValue
    {
        [Column(MaxLength = 1000, PrimaryKey = true)]
        public string Path { get; set; }

        [Column(MaxLength = 200, PrimaryKey = true)]
        public string Name { get; set; }

        [Column]
        public string Value { get; set; }
    }
}
