﻿using System;
using System.Linq;
using System.Linq.Expressions;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData.Expressions
{
    internal sealed class AttributeFindAndReplacer : IfsExpressionVisitor
    {
        private readonly EntityDataSource _dataSource;
        private readonly string _alias;
        private readonly string _aliasPrefix;

        public static Expression Rewrite(Expression expression, EntityDataSource dataSource, string alias)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));
            if (dataSource == null) throw new ArgumentNullException(nameof(dataSource));

            IfsExpressionVisitor visitor = new AttributeFindAndReplacer(dataSource, alias);
            return visitor.Visit(expression);
        }

        private AttributeFindAndReplacer(EntityDataSource dataSource, string alias)
        {
            _dataSource = dataSource;
            _alias = alias;
            _aliasPrefix = alias == null ? null : alias + ".";
        }

        protected internal override Expression VisitVarAccessExpression(VarAccessExpression exp)
        {
            string path = exp.PropertyPath;
            
            if (_aliasPrefix != null && path.StartsWith(_aliasPrefix))
            {
                path = path.Substring(_aliasPrefix.Length);
            }

            AttributePathInfo attribute = AttributePathInfo.Get(_dataSource.Metadata, _dataSource.ProjectionName, _dataSource.EntityName, path);
            if (attribute != null)
            {
                return IfsExpression.AttributeAccess(attribute);
            }
            
            return base.VisitVarAccessExpression(exp);
        }

        protected internal override Expression VisitQueryExpression(QueryExpression exp)
        {
            if (_alias != null)
            {
                bool remappedAlias = exp.From.Alias == _alias || (exp.Joins?.Any(x => x.Alias == _alias) == true);
                if (remappedAlias)
                {
                    // This nested query has remapped the alias to something else 
                    // so the alias does not apply any more
                    return Rewrite(exp, _dataSource, null);
                }
            }

            return base.VisitQueryExpression(exp);
        }
    }
}
