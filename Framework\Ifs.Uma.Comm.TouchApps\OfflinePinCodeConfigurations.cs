﻿using System;
using System.Text.RegularExpressions;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Comm.TouchApps
{
    public static class OfflinePinCodeConfigurations
    {
        public const string PinCodeSettingsKey = "PinSettings";
        public const string PinCodeAttemptsKey = "Attempts";
        public const string PinCodeLockDurationKey = "LockDuration";
        public const string PinCodeFailBehaviorKey = "FailBehavior";

        public const string PinCodeAttemptsLeftKey = "AttemptsLeft";
        public const string PinCodeStateKey = "State";
        public const string PinCodeLockedTimeKey = "LockedTime";
        public const string PinCodeStateLocked = "LOCKED";
        public const string PinCodeStateBlocked = "BLOCKED";
        public const string PinCodeLock = "LOCK";
        public const string PinCodeBlock = "BLOCK";

        public static int PinCodeMinLength { get; set; }
        public static bool PinCodeIsComplex { get; set; }
        private static Regex _pinCodeComplexRegex = new Regex("^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^.(){}:;<>,/~_+=|&*-])", RegexOptions.None, TimeSpan.FromSeconds(10));

        public static void ResetPinCodeSettings(ISettings settings)
        {
            settings.Set(PinCodeStateKey, string.Empty);
            settings.Set(PinCodeLockedTimeKey, null);
            settings.Set(PinCodeAttemptsLeftKey, null);
        }

        public static bool ValidatePinCode(string pincode)
        {
            if (!string.IsNullOrEmpty(pincode) && (((pincode.Length >= PinCodeMinLength) && !PinCodeIsComplex) || (pincode.Length >= PinCodeMinLength && PinCodeIsComplex && _pinCodeComplexRegex.IsMatch(pincode))))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}
