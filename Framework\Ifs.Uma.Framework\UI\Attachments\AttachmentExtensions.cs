﻿using Ifs.Uma.AppData.Model;
using Ifs.Uma.Localization;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public static class AttachmentExtensions
    {
        public static string ToLocalisedString(this AttachmentStatus status)
        {
            switch (status)
            {
                case AttachmentStatus.Preparing:
                    return Strings.Preparing;
                case AttachmentStatus.RequiresUpload:
                    return Strings.WaitingForUpload;
                case AttachmentStatus.Uploading:
                    return Strings.Uploading;
                case AttachmentStatus.Uploaded:
                    return Strings.Uploaded;
                case AttachmentStatus.UploadFailed:
                    return Strings.UploadFailed;
                case AttachmentStatus.RequiresDownload:
                    return Strings.RequiresDownload;
                case AttachmentStatus.Downloading:
                    return Strings.Downloading;
                case AttachmentStatus.Downloaded:
                    return Strings.Downloaded;
                case AttachmentStatus.DownloadFailed:
                    return Strings.DownloadFailed;
                default:
                    return string.Empty;
            }
        }

        public static bool IsTransferActive(this AttachmentStatus? status)
        {
            return status == AttachmentStatus.Preparing || 
                   status == AttachmentStatus.RequiresUpload ||
                   status == AttachmentStatus.Uploaded ||
                   status == AttachmentStatus.RequiresDownload ||
                   status == AttachmentStatus.Downloading;
        }

        public static bool ShouldDisplay(this AttachmentStatus? status, bool downloadRequested)
        {
            switch (status ?? AttachmentStatus.Unknown)
            {
                case AttachmentStatus.Downloaded:
                    return downloadRequested;
                case AttachmentStatus.Preparing:
                case AttachmentStatus.RequiresUpload:
                case AttachmentStatus.Uploading:
                case AttachmentStatus.UploadFailed:
                case AttachmentStatus.Downloading:
                case AttachmentStatus.RequiresDownload:
                case AttachmentStatus.DownloadFailed:
                    return true;
                default:
                    return false;
            }
        }
    }
}
