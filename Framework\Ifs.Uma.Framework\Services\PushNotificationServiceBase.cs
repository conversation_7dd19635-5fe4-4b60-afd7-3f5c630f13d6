﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.Services.Sync;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Services
{
    public interface IPushNotificationService : IService
    {
        bool IsEnabled { get; set; }
        string GetPushHandle();
    }

    public abstract class PushNotificationServiceBase : Service, IPushNotificationService
    {
        private const string PushRegistrationSettingsName = "PushRegistration";

        private readonly IResolver _resolver;
        private readonly ISettings _pushRegSettings;
        private readonly ISyncController _syncController;
        private readonly IEventAggregator _eventAggregator;
        private static ICommandExecutor _commandExecutor;
        private static IDataHandler _data;
        private static ILogger _logger;
        private bool _isEnabled = true;

        public bool IsEnabled
        {
            get { return _isEnabled; }
            set
            {
                if (_isEnabled != value)
                {
                    _isEnabled = value;
                    EnabledStateChanged.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public static PushNotificationData PushNotificationData { get; } = new PushNotificationData();

        protected event EventHandler EnabledStateChanged;

        protected PushNotificationServiceBase(IResolver resolver, ILogger logger, ILocalProfile localProfile, ISyncController syncController, ICommandExecutor commandExecutor, IDataHandler data, IEventAggregator eventAggregator)
            : base(logger)
        {
            _resolver = resolver;
            _pushRegSettings = localProfile.Settings.GetSubGroup(PushRegistrationSettingsName);
            _syncController = syncController;
            _commandExecutor = commandExecutor;
            _data = data;
            _logger = logger;
            _eventAggregator = eventAggregator;

            _eventAggregator.GetEvent<AppParameterChangedEvent>().Subscribe(OnAppParametersChanged);
        }

        protected virtual void OnAppParametersChanged(AppParameterChangedEventArgs eventArgs)
        {
            // when App parameters change the method will be called
            // The implementaion should be done on each platform since each platform
            // handles push notifications in a diffrent way
        }

        protected override void OnStart()
        {
            base.OnStart();

            _eventAggregator.GetEvent<SyncServiceStatusChangedEvent>().Subscribe(OnSyncServiceStatusChanged);
        }

        protected override void OnStop()
        {
            _eventAggregator.GetEvent<SyncServiceStatusChangedEvent>().Unsubscribe(OnSyncServiceStatusChanged);
            _eventAggregator.GetEvent<AppParameterChangedEvent>().Unsubscribe(OnAppParametersChanged);

            UnregisterForPushNotificationsAsync();

            base.OnStop();
        }

        private void OnSyncServiceStatusChanged(SyncServiceStatusChangedEventArgs args)
        {
            if (!args.Status.IsSyncing)
            {
                OnSyncEnded();
            }
        }

        protected virtual void OnSyncEnded()
        {
        }

        public string GetPushHandle()
        {
            PushRegistration previousRegistration = PushRegistration.Load(_pushRegSettings);
            return previousRegistration?.PnsHandle ?? string.Empty;
        }

        public void PushNotificationReceived()
        {
            _syncController.RequestSync();
        }

        protected async Task RegisterForPushNotificationsAsync(string pnsHandle)
        {
            IIfsConnection connection;
            if (!_resolver.TryResolve(out connection))
            {
                // No registrations can be made without connection
                return;
            }

            try
            {
                PushRegistration previousRegistration = PushRegistration.Load(_pushRegSettings);
                string registeredPnsHandle = (previousRegistration == null || previousRegistration.IsExpired) ? null : previousRegistration.PnsHandle;

                if (registeredPnsHandle != pnsHandle)
                {
                    await TryUnregisterPreviousRegistration(previousRegistration, connection);

                    PushRegistration registration = new PushRegistration();
                    registration.PnsHandle = pnsHandle;
                    List<PushRegistration> newRegistration = await connection.TouchAppsComms.RegisterForPushNotifications(registration);

                    if (!string.IsNullOrEmpty(newRegistration?.FirstOrDefault()?.PnsHandle))
                    {
                        newRegistration?.FirstOrDefault()?.Save(_pushRegSettings);
                    }
                    else
                    {
                        PushRegistration.Delete(_pushRegSettings);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.HandleException(ExceptionType.Recoverable, ex);
            }
        }

        private async Task TryUnregisterPreviousRegistration(PushRegistration previousRegistration, IIfsConnection connection)
        {
            if (previousRegistration != null)
            {
                try
                {
                    PushRegistration.Delete(_pushRegSettings);
                    previousRegistration.PnsHandle = null;
                    await connection.TouchAppsComms.RegisterForPushNotifications(previousRegistration);
                }
                catch (Exception ex)
                {
                    Logger.HandleException(ExceptionType.Recoverable, ex);
                }
            }
        }

        public static async Task DoPushCommandAsync()
        {
            if (PushNotificationData?.ToBeExecuted == true)
            {
                IMetadata metadata = Resolver.Resolve<IMetadata>();
                string projectionName = PushNotificationData.ProjectionName ?? metadata?.CpiMetaData?.GetProjections()?.FirstOrDefault()?.Name;
                CpiCommand command = MetadataExtensions.FindCommand(metadata, projectionName, PushNotificationData.CommandName);

                if (command?.Entity != null)
                {
                    RecordData rData = new RecordData(_logger, metadata, _data);
                    EntityQuery query = new EntityQuery(EntityDataSource.FromEntity(metadata, projectionName, command.Entity));

                    if (PushNotificationData.PushNotificationCmdVars != null)
                    {
                        foreach (KeyValuePair<string, object> kvp in PushNotificationData.PushNotificationCmdVars.Vars)
                        {
                            query.AddFilter(kvp.Key, kvp.Value.ToString());
                        }
                    }

                    await rData.LoadRecordAsync(query, false);
                    PageData pData = new PageData(rData);

                    await _commandExecutor.ExecuteAsync(projectionName, pData.DefaultViewData, command, PushNotificationData.PushNotificationCmdVars);
                }
                
                PushNotificationData.ToBeExecuted = false;
            }
        }

        protected Task UnregisterForPushNotificationsAsync()
        {
            return RegisterForPushNotificationsAsync(null);
        }
    }

    public class PushNotificationData
    {
        private bool _toBeExecuted = false;

        public CommandOptions PushNotificationCmdVars { get; set; }
        public string ProjectionName { get; set; }
        public string CommandName { get; set; }
        public string Message { get; set; }
        public bool PendingToShow { get; set; }
        public bool ToBeExecuted { get => !string.IsNullOrEmpty(CommandName) && _toBeExecuted; set => _toBeExecuted = value; }
    }
}
