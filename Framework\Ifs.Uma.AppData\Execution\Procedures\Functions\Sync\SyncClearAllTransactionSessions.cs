﻿using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Sync
{
    internal sealed class SyncClearAllTransactionSessions : SyncFunction
    {
        public const string FunctionName = "ClearAllTransactionSessions";

        public SyncClearAllTransactionSessions()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            List<FndTransactionSession> sessions = context.DbDataContext.TransactionSessions.ToList();
            context.DbDataContext.TransactionSessions.DeleteAllOnSubmit(sessions);

            Logger.Current.Information($"Cleared {sessions.Count} Transaction Session(s).");

            List<TransitionRow> transitions = context.DbDataContext.TransitionRows.Where(x => x.SessionId != null).ToList();
            foreach (TransitionRow transitionRow in transitions)
            {
                context.DbDataContext.TransitionRows.Attach(transitionRow);
                transitionRow.SessionId = null;
            }

            Logger.Current.Information($"{transitions.Count} Transition(s) were cleared from Transaction Session(s).");

            context.DbDataContext.SubmitChanges(false);

            return true;
        }
    }
}
