﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringRegexReplaceTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringRegexReplaceTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("abcac", "c", "d", ExpectedResult = "abdad")]
        [TestCase("abCac", "c", "d", ExpectedResult = "abCad")]
        [TestCase("abcac", "c", null, ExpectedResult = "aba")]
        [TestCase("abcac", "c", "", ExpectedResult = "aba")]
        [TestCase("abcac", null, "d", ExpectedResult = "abcac")]
        [TestCase("abcac", "", "d", ExpectedResult = "abcac")]
        [TestCase("abcac", null, null, ExpectedResult = "abcac")]
        [TestCase(null, null, null, ExpectedResult = null)]
        public async Task<string> String_RegexReplace(object input, string regexPattern, string replacementString)
        {
            _params["TextInput"] = input;
            _params["RegexPattern"] = regexPattern;
            _params["ReplacementString"] = replacementString;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexReplace", _params);
            CheckResult(result);

            return result?.Value as string;
        }

        [Test]
        [TestCase("abCac", "c", "d", "i", ExpectedResult = "abdad")]
        [TestCase("abcac", null, "d", "i", ExpectedResult = "abcac")]
        [TestCase("abcac", "c", null, "i", ExpectedResult = "aba")]
        [TestCase("abCac", "c", "d", null, ExpectedResult = "abCad")]
        [TestCase("abcac", "c", "", "i", ExpectedResult = "aba")]
        [TestCase(null, null, null, null, ExpectedResult = null)]
        public async Task<string> String_RegexReplace4(object input, string regexPattern, string replacementString, string regexOptions)
        {
            _params["TextInput"] = input;
            _params["RegexPattern"] = regexPattern;
            _params["ReplacementString"] = replacementString;
            _params["RegexOptions"] = regexOptions;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexReplace4", _params);
            CheckResult(result);

            return result?.Value as string;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
