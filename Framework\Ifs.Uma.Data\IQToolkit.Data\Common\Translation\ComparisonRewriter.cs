﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using Ifs.Uma.Database;

namespace IQToolkit.Data.Common
{
    internal class ComparisonRewriter : DbExpressionVisitor
    {
        private ComparisonRewriter()
        {
        }

        public static Expression Rewrite(Expression expression)
        {
            return new ComparisonRewriter().Visit(expression);
        }

        protected override Expression VisitBinary(BinaryExpression node)
        {
            if (node == null) return null;
            switch (node.NodeType)
            {
                case ExpressionType.Equal:
                case ExpressionType.NotEqual:
                    Expression result = Compare(node);
                    if (result == node)
                        goto default;
                    return this.Visit(result);
                default:
                    return base.VisitBinary(node);
            }
        }

        protected static Expression SkipConvert(Expression expression)
        {
            while (expression.NodeType == ExpressionType.Convert)
            {
                expression = ((UnaryExpression)expression).Operand;
            }
            return expression;
        }

        protected static Expression Compare(BinaryExpression bop)
        {
            if (bop == null) throw new ArgumentNullException("bop");
            var e1 = SkipConvert(bop.Left);
            var e2 = SkipConvert(bop.Right);
            EntityExpression entity1 = e1 as EntityExpression;
            EntityExpression entity2 = e2 as EntityExpression;
            bool negate = bop.NodeType == ExpressionType.NotEqual;
            if (entity1 != null)
            {
                // MM: Primary Keys only supported on actual Members
                return MakePredicate(e1, e2, entity1.Entity.PrimaryKey.Select(x => x.MemberInfo).ToArray(), negate);
            }
            else if (entity2 != null)
            {
                // MM: Primary Keys only supported on actual Members
                return MakePredicate(e1, e2, entity2.Entity.PrimaryKey.Select(x => x.MemberInfo).ToArray(), negate);
            }
            var dm1 = GetDefinedMembers(e1);
            var dm2 = GetDefinedMembers(e2);

            if (dm1 == null && dm2 == null)
            {
                // neither are constructed types
                return bop;
            }

            if (dm1 != null && dm2 != null)
            {
                // both are constructed types, so they'd better have the same members declared
                HashSet<string> names1 = new HashSet<string>(dm1.Select(m => m.Name));
                HashSet<string> names2 = new HashSet<string>(dm2.Select(m => m.Name));
                if (names1.IsSubsetOf(names2) && names2.IsSubsetOf(names1)) 
                {
                    return MakePredicate(e1, e2, dm1, negate);
                }
            }
            else if (dm1 != null)
            {
                return MakePredicate(e1, e2, dm1, negate);
            }
            else if (dm2 != null)
            {
                return MakePredicate(e1, e2, dm2, negate);
            }

            throw new InvalidOperationException("Cannot compare two constructed types with different sets of members assigned.");
        }

        protected static Expression MakePredicate(Expression e1, Expression e2, IEnumerable<MemberInfo> members, bool negate)
        {
            var pred = members.Select(m =>
                QueryBinder.BindMember(e1, m).Equal(QueryBinder.BindMember(e2, m))
                ).Join(ExpressionType.And);
            if (negate)
                pred = Expression.Not(pred);
            return pred;
        }

        private static IEnumerable<MemberInfo> GetDefinedMembers(Expression expr)
        {
            MemberInitExpression mini = expr as MemberInitExpression;
            if (mini != null)
            {
                var members = mini.Bindings.Select(b => FixMember(b.Member));
                if (mini.NewExpression.Members != null)
                {
                    members.Concat(mini.NewExpression.Members.Select(m => FixMember(m)));
                }
                return members;
            }
            else
            {
                NewExpression nex = expr as NewExpression;
                if (nex != null && nex.Members != null)
                {
                    return nex.Members.Select(m => FixMember(m));
                }
            }
            return null;
        }

        private static MemberInfo FixMember(MemberInfo member)
        {
            // Properties have a GetMethod and a SetMethod get_<PropertyName> and set_<PropertyName>
            // These are modelled as Methods as well as by a Property
            //  There will be problems here if the get_ is not actually from a property
            //  Also what about the set_ ???
            if (member is MethodInfo && member.Name.StartsWith("get_", StringComparison.Ordinal))
            {
                return member.DeclaringType.GetRuntimeProperty(member.Name.Substring(4));
            }
            return member;
        }
    }
}
