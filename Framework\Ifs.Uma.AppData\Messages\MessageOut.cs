﻿using System.Collections.Generic;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.AppData.Messages
{
    public class MessageOut
    {
        public long ClientMessageId { get; set; }
        public bool Failing { get; set; }
        public string FailingReason { get; set; }
        public MessageType MessageType { get; set; }
        public string Projection { get; set; }
        public string EntitySetName { get; set; }
        public string ArraySource { get; set; }
        public string ObjState { get; set; }
        public MessageTableData Data { get; set; }
        public Dictionary<string, object> LobAttributeTypes { get; set; }
        public bool IsMessageTimeZoneAware { get; set; }
        public string SiteTimeZone { get; set; }

        public string Serialize(bool isBatch, int deviceId)
        {
            JObject jObj = new JObject();

            bool isPerform = MessageType == MessageType.ACTION || MessageType == MessageType.BOUND_ACTION;

            if (!isBatch)
            {
                jObj.Add("DeviceId", deviceId);
            }

            jObj.Add("MessageId", ClientMessageId);

            if (!isPerform)
            {
                jObj.Add("CrudOperation", MessageType.ToString().ToLowerInvariant());
            }

            jObj.Add("Projection", Projection);

            if (isBatch)
            {
                if (MessageType == MessageType.BOUND_ACTION)
                {
                    string[] parts = Data.TableName.Split('.');
                    jObj.Add("Entity", RemoteNaming.ToServerEntityName(parts[0]));
                    jObj.Add("Action", parts[1]);
                }
                else if (MessageType == MessageType.ACTION)
                {
                    jObj.Add("Action", Data.TableName);
                }
                else
                {
                    jObj.Add("Entity", RemoteNaming.ToServerEntityName(Data.TableName));
                }
            }

            JObject data = new JObject();
            foreach (KeyValuePair<string, object> columnValue in Data.RowData.ColumnData)
            {
                if (columnValue.Key.Equals("objstate"))
                {
                    ObjState = columnValue.Value.ToString();
                    continue;
                }

                string attribName = isPerform ? columnValue.Key : RemoteNaming.ToServerAttributeName(columnValue.Key);
                data.Add(attribName, columnValue.Value == null ? null : JToken.FromObject(columnValue.Value));
            }

            jObj.Add("Data", data);

            return MessageUtils.JObjectToString(jObj);
        }
    }
}
