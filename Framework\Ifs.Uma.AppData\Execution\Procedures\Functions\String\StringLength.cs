﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringLength : StringFunction
    {
        public const string FunctionName = "Length";

        public StringLength()
            : base(FunctionName, 1, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify) => stringToModify.Length;
    }
}
