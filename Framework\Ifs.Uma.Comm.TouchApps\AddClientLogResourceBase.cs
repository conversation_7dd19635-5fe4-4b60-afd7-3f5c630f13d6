﻿using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class AddClientLogResourceBase : BaseResource
    {
        public override string ResourceName
        {
            get { return "MobileClientRuntime.svc/AddClientLog"; }
        }

        public override bool SingleResponse
        {
            get { return true; }
        }

        [DataMember(Name = "MobileContext", Order = 0)]
        public MobileContext MobileContext { get; set; }

        [DataMember(Name = "MessageText", Order = 1)]
        public string MessageText { get; set; }

        [DataMember(Name = "Type", Order = 2)]
        public string Type { get; set; }

        public AddClientLogResourceBase(MobileContext mobileContext, string messageText, string type)
        {
            MobileContext = mobileContext;
            MessageText = messageText;
            Type = type;
        }
    }
}
