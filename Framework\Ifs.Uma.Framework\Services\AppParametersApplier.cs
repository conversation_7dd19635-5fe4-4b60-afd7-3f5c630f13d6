﻿using Ifs.Uma.Services;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Services
{
    public sealed class AppParametersApplier : EventWatcherService<AppParameterChangedEvent, AppParameterChangedEventArgs>
    {
        private readonly IAppParameters _appParams;
        private readonly IResolver _resolver;
        private readonly ILoggerManager _loggerManager;

        public AppParametersApplier(ILogger logger, ILoggerManager loggerManager, IEventAggregator eventAggregator, IAppParameters appParams, IResolver resolver)
            : base(logger, eventAggregator, ThreadOption.PublisherThread)
        {
            _appParams = appParams;
            _resolver = resolver;
            _loggerManager = loggerManager;
        }

        protected override void OnStart()
        {
            base.OnStart();

            SetupLoggingLevel();
            SetupBarcodeScanning();
            SetupSiteTimezoneAwareness();

            if (DeviceInfo.OperatingSystem == OperatingSystem.iOS)
            {
                SetupLidarMeasuring();
            }
        }

        protected override void OnStop()
        {
            PlatformServices.BarcodeService = null;

            base.OnStop();
        }

        protected override void OnEvent(AppParameterChangedEventArgs args)
        {
            SetupLoggingLevel();

            if (args.HasChanged(AppParameterNames.BarcodeScanningEnabled))
            {
                SetupBarcodeScanning();
            }
        }

        private void SetupLoggingLevel()
        {
            _loggerManager.LoggingLevel = _appParams.GetLoggingLevel();
        }

        private void SetupBarcodeScanning()
        {
            bool enabled = _appParams.IsBarcodeScanningEnabled();
            if (enabled && _resolver.TryResolve(out IBarcodeService barcodeService))
            {
                PlatformServices.BarcodeService = barcodeService;
            }
            else
            {
                PlatformServices.BarcodeService = null;
            }
        }

        private void SetupSiteTimezoneAwareness()
        {
            PlatformServices.IsSiteTimezoneEnabled = _appParams.IsSiteTimezoneEnabled();
        }

        private void SetupLidarMeasuring()
        {
            if (_resolver.TryResolve(out ILidarService lidarService))
            {
                PlatformServices.LidarService = lidarService;

                if (_appParams.IsLidarAutomaticAttachmentEnabled() && PlatformServices.LidarService != null)
                {
                    PlatformServices.LidarService.AllowAutomaticMediaAttachment = true;
                }
                else
                {
                    PlatformServices.LidarService.AllowAutomaticMediaAttachment = false;
                }
            }
        }
    }
}
