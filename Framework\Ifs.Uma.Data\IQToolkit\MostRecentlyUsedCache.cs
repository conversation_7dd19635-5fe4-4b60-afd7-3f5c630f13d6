﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading;
using Ifs.Uma.Utility;

namespace IQToolkit
{
    /// <summary>
    /// Implements a cache over a most recently used list
    /// </summary>
    /// <typeparam name="T"></typeparam>
    internal class MostRecentlyUsedCache<T>
    {
        int m_maxSize;
        List<T> m_list;
        Func<T, T, bool> m_equals;

        public MostRecentlyUsedCache(int maxSize)
            : this(maxSize, EqualityComparer<T>.Default)
        {
        }

        public MostRecentlyUsedCache(int maxSize, IEqualityComparer<T> comparer)
            : this(maxSize, (x, y) => comparer.Equals(x, y))
        {
        }

        public MostRecentlyUsedCache(int maxSize, Func<T, T, bool> fnEquals)
        {
            if (fnEquals == null) throw new ArgumentNullException("fnEquals");
            if (maxSize <= 0) throw new ArgumentOutOfRangeException("maxSize");
            this.m_list = new List<T>();
            this.m_maxSize = maxSize;
            this.m_equals = fnEquals;
        }

        public int Count { get { lock (m_list) { return m_list.Count; } } }

        public void Clear()
        {
            lock (m_list) { m_list.Clear(); }
        }

        public bool Lookup(T item, bool add, out T cached)
        {
            cached = default(T);
            int index = -1;
            lock (m_list)
            {
                int n = m_list.Count;
                for (int i = 0; i < n; i++)
                {
                    T value = m_list[i];
                    if (m_equals(value, item))
                    {
                        cached = value;
                        if (add && i > 0)
                        {
                            m_list.RemoveAt(i);
                            m_list.Insert(0, value);
                        }
                        index = i;
                        break;
                    }
                }
                if (add && index < 0)
                {
                    cached = item;
                    if (n == m_maxSize)
                    {
                        m_list.RemoveAt(n - 1);
                    }
                    m_list.Insert(0, item);
                }
            }
            return index >= 0;
        }
    }
}
