﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Data.Transitions
{
    public enum OperationType
    {
        Insert,
        Delete,
        Update,
        Perform
    }

    public enum SyncState
    {
        Unsent,
        Sent,
        SentAcknowledged,
        Failed
    }

    internal static class SubmitActionExtensions
    {
        public static OperationType ToOperationType(this SubmitAction action)
        {
            switch (action)
            {
                case SubmitAction.Delete:
                    return OperationType.Delete;
                case SubmitAction.Insert:
                    return OperationType.Insert;
                case SubmitAction.Update:
                    return OperationType.Update;
                default:
                    throw new NotSupportedException("Unsupported SubmitAction");
            }
        }
    }

    [Table(Name = "fnd$transition_row", System = true)]
    public class TransitionRow
    {
        [Column(AutoIncrement=true, PrimaryKey=true)]
        public long RowId { get; set; }
        [Column]
        public long TransitionId { get; set; }
        [Column]
        public long ModifiedRowId { get; set; }
        [Column]
        public SyncState SyncState { get; set; }
        [Column]
        public string ProjectionName { get; set; }
        [Column(MaxLength = 30, Mandatory = true)]
        public string TableName { get; set; }
        [Column(MaxLength = 30)]
        public string EntitySetName { get; set; }
        [Column(MaxLength = 60)]
        public string ArraySource { get; set; }
        [Column]
        public string TransactionGroup { get; set; }
        [Column]
        public OperationType Operation { get; set; }
        [Column(MaxLength = 32)]
        public string ErrorCode { get; set; }
        [Column]
        public string ErrorMessage { get; set; }
        [Column]
        public string ErrorDetail { get; set; }
        [Column]
        public string PrimaryKeyString { get; set; }
        [Column]
        public string SessionId { get; set; }

        public void PrepareForResend()
        {
            SyncState = SyncState.Unsent;
            ErrorCode = null;
            ErrorMessage = null;
            ErrorDetail = null;
        }
    }
}
