﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringTrim : StringFunction
    {
        public const string FunctionName = "Trim";

        public StringTrim()
            : base(FunctionName, 1, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify) => stringToModify.Trim();
    }

    internal sealed class StringTrim2 : StringFunction
    {
        public const string FunctionName = "Trim";

        public StringTrim2()
            : base(FunctionName, 2, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string trimString = parameters[1].GetString();

            if (string.IsNullOrEmpty(trimString))
                return null;

            return stringToModify.Trim(trimString[0]); // Do as Oracle SQL, only take the first character, not the entire string
        }
    }
}
