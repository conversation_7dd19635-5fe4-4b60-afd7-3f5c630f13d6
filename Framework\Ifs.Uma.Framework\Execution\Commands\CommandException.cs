﻿using System;
using Ifs.Uma.AppData.Execution;

namespace Ifs.Uma.Framework.Execution.Commands
{
    public class CommandException : ExecutionException
    {
        public CommandException()
        {
        }

        public CommandException(string message)
            : base(message)
        {
        }

        public CommandException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}
