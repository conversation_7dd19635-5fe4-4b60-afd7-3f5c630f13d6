{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}, "CustomerAddresses": {"name": "CustomerAddresses", "entity": "TstCustomerAddress", "array": true}, "CustomerTypes": {"name": "CustomerTypes", "entity": "TstCustomerType", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}, "CustomerType": {"datatype": "Text", "keygeneration": "User"}, "CustomerTypeDesc": {"datatype": "Text", "keygeneration": "User"}}, "references": {"CustomerTypeRef": {"target": "TstCustomerType", "mapping": {"CustomerType": "TypeId"}, "prefetch": {"CustomerTypeDesc": "TypeDescription"}}}, "arrays": {"CustomerAddressArray": {"target": "TstCustomerAddress", "mapping": {"CustomerNo": "AddressCustomerNo"}}}}, "TstCustomerType": {"name": "TstCustomerType", "hasETag": true, "CRUD": "Read", "luname": "TstCustomerType", "ludependencies": ["TstCustomerType"], "keys": ["TypeId"], "attributes": {"TypeId": {"datatype": "Text", "keygeneration": "User"}, "TypeDescription": {"datatype": "Text", "keygeneration": "User"}}}, "TstCustomerAddress": {"name": "TstCustomerAddress", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomerAddress", "ludependencies": ["TstCustomerAddress"], "keys": ["AddressCustomerNo", "AddressId"], "attributes": {"AddressCustomerNo": {"datatype": "Text", "keygeneration": "User"}, "AddressId": {"datatype": "Text", "keygeneration": "User"}, "AddressLine": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<TestParameter>": {"name": "TestParameter", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"execute": []}]}, "Function<TestVars>": {"name": "TestVars", "type": "Function", "layers": [{"vars": [{"name": "Address", "dataType": "Structure", "subType": "TstCustomerAddress"}], "execute": []}]}, "Function<TestEntitySet>": {"name": "TestEntitySet", "type": "Function", "layers": [{"vars": [{"name": "MyCount"}], "execute": [{"call": {"method": "count", "args": {"entity": "Customers"}}, "assign": "MyCount"}]}]}, "Function<TestArray>": {"name": "TestArray", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "count", "args": {"name": "Customer.CustomerAddressArray"}}, "assign": "Var1"}]}]}, "Function<TestArrayByVar>": {"name": "TestArrayByVar", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "CustomerVar", "dataType": "Structure", "subType": "TstCustomer"}, {"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"name": "Customer"}}, "assign": "CustomerVar"}, {"call": {"method": "count", "args": {"name": "CustomerVar.CustomerAddressArray"}}, "assign": "Var1"}]}]}, "Function<TestWhere>": {"name": "TestWhere", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "count", "args": {"entity": "Customers", "where": {"==": [{"var": "CustomerTypeRef.TypeDescription"}, "TEST_TYPE"]}}}, "assign": "Var1"}]}]}, "Function<TestWhereAliased>": {"name": "TestWhereAliased", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "count", "args": {"entity": "Customers", "alias": "i", "where": {"==": [{"var": "i.CustomerTypeRef.TypeDescription"}, "TEST_TYPE"]}}}, "assign": "Var1"}]}]}, "Function<TestReference>": {"name": "TestReference", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "fetch", "args": {"name": "Customer.CustomerTypeRef"}}, "assign": "Var1"}]}]}, "Function<TestReferenceByVar>": {"name": "TestReferenceByVar", "type": "Function", "params": [{"name": "Customer", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"vars": [{"name": "CustomerVar", "dataType": "Structure", "subType": "TstCustomer"}, {"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"name": "Customer"}}, "assign": "CustomerVar"}, {"call": {"method": "fetch", "args": {"name": "CustomerVar.CustomerTypeRef"}}, "assign": "Var1"}]}]}}}}