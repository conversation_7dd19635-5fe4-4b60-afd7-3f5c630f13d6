﻿using System.Reflection;
using System.Resources;

// General Information about an assembly is controlled through the following 
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.

[assembly: Assembly<PERSON>itle("IFS UMA Cloud Client")]
[assembly: AssemblyDescription("Cloud Client")]
[assembly: AssemblyProduct("Ifs.Cloud.Client")]

[assembly: NeutralResourcesLanguage("en")]

[assembly: AssemblyFileVersion("*******")]