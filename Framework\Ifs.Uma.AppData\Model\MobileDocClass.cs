﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    public enum FndBoolean
    {
        False,
        True,
    }

    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_mobile_doc_class", Columns = "DocClass", Unique = true)]
    public class MobileDocClass : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "mobile_doc_class";

        #region Field Definitions

        private string _docClass;
        private string _docName;
        private string _firstRevision;
        private double? _maxDocSize;
        private FndBoolean? _mobDocClass;
        private FndBoolean? _mobileDefault;
        private FndBoolean? _newInMobile;
        private string _numberGenerator;
        private FndBoolean? _pushToMobile;
        private FndBoolean? _releasedDocuments;

        #endregion

        public MobileDocClass()
            : base(DbTableName)
        {
        }

        #region Property Definitions

        [Column(Storage = nameof(_docClass), Mandatory = true, ServerPrimaryKey = true)]
        public string DocClass
        {
            get => _docClass;
            set => SetProperty(ref _docClass, value);
        }

        [Column(Storage = nameof(_docName))]
        public string DocName
        {
            get => _docName;
            set => SetProperty(ref _docName, value);
        }

        [Column(Storage = nameof(_firstRevision))]
        public string FirstRevision
        {
            get => _firstRevision;
            set => SetProperty(ref _firstRevision, value);
        }

        [Column(Storage = nameof(_maxDocSize))]
        public double? MaxDocSize
        {
            get => _maxDocSize;
            set => SetProperty(ref _maxDocSize, value);
        }

        [Column(Storage = nameof(_mobDocClass))]
        public FndBoolean? MobDocClass
        {
            get => _mobDocClass;
            set => SetProperty(ref _mobDocClass, value);
        }

        [Column(Storage = nameof(_mobileDefault))]
        public FndBoolean? MobileDefault
        {
            get => _mobileDefault;
            set => SetProperty(ref _mobileDefault, value);
        }

        [Column(Storage = nameof(_newInMobile))]
        public FndBoolean? NewInMobile
        {
            get => _newInMobile;
            set => SetProperty(ref _newInMobile, value);
        }

        [Column(Storage = nameof(_numberGenerator))]
        public string NumberGenerator
        {
            get => _numberGenerator;
            set => SetProperty(ref _numberGenerator, value);
        }

        [Column(Storage = nameof(_pushToMobile))]
        public FndBoolean? PushToMobile
        {
            get => _pushToMobile;
            set => SetProperty(ref _pushToMobile, value);
        }

        [Column(Storage = nameof(_releasedDocuments))]
        public FndBoolean? ReleasedDocuments
        {
            get => _releasedDocuments;
            set => SetProperty(ref _releasedDocuments, value);
        }

        #endregion
    }
}
