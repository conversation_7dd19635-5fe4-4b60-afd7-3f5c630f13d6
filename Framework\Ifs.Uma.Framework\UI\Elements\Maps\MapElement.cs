﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Location;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Location;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Framework.UI.Elements.Maps
{
    public class MapElement : ElementWithButtonBase
    {
        private readonly IMetadata _metadata;
        private readonly IDataHandler _data;
        private readonly ILogger _logger;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IBreadcrumbManager _breadcrumbManager;
        private readonly ICardDefCreator _cardDefCreator;
        private readonly IExpressionRunner _expressionRunner;
        private readonly ILocationService _locationService;
        private readonly IAppParameters _appParameters;
        private readonly IDialogService _dialogService;
        private readonly IInsightsLogger _insightsLogger;

        private CpiMap _cpiMap;
        private GpsLocation _location;

        protected override BindingType BindingPropertyType => BindingType.Array;

        public MapElementProvider MapElementProvider { get; }

        public List<MapPin> Pins { get; } = new List<MapPin>();

        public MapPin CurrentCardPin
        {
            get
            {
                if (_currentCardPins == null || _currentCardPins.Count == 0)
                {
                    return null;
                }

                return _currentCardPins[_currentCardPinIndex];
            }
        }

        public string CardCountLabel
        {
            get
            {
                // Only show the card label if there is more than one card to show
                if (_currentCardPins == null || _currentCardPins.Count < 2)
                {
                    return string.Empty;
                }

                return $"{_currentCardPinIndex + 1} / {_currentCardPins.Count}";
            }
        }

        public INativeMapElement NativeMapElement { get; set; }

        public string Html { get; private set; }

        private static readonly Assembly _assembly = typeof(IconUtils).GetTypeInfo().Assembly;

        private const string MappingFilesLocation = "Ifs.Uma.UI.Mapping.";

        private IList<MapPin> _currentCardPins;

        private int _currentCardPinIndex;

        private bool _hasMapPageLoaded;

        private bool _isFirstPins = true;

        private const int MaxPinCount = 250;

        private readonly object _addPinsLock = new object();

        private bool _locationFetchInProgress;

        private IDictionary<MapPin, double> _pinsWithDistanceToCurrentLocation;

        public MapElement(IMetadata metadata, IDataHandler data, ILogger logger, ICommandExecutor commandExecutor, IBreadcrumbManager breadcrumbManager,
            ICardDefCreator cardDefCreator, IExpressionRunner expressionRunner, IAppParameters appParameters, ILocationService locationService, IDialogService dialogService, IInsightsLogger insightsLogger)
        {
            _metadata = metadata;
            _data = data;
            _logger = logger;
            _breadcrumbManager = breadcrumbManager;
            _commandExecutor = WrappedCommandExecutor.Create(commandExecutor, null);
            _cardDefCreator = cardDefCreator;
            _expressionRunner = expressionRunner;
            _appParameters = appParameters;
            _locationService = locationService;
            _dialogService = dialogService;
            _insightsLogger = insightsLogger;

            HasHeader = false;

            // If the app is running in debug mode, force use of Here maps
#if DEBUG
            MapElementProvider = MapElementProvider.Here;
#else
            MapElementProvider = appParameters.GetMapElementProvider();
#endif
        }

        protected override bool OnInitialize()
        {
            _cpiMap = _metadata.FindMap(ProjectionName, Content.Map);
            Label = _cpiMap?.Label;
            return _cpiMap != null;
        }

        protected override bool OnLoad()
        {
            OpenElementButtonCommand = Command.FromMethod(OpenMap, OpenElementButtonCommandCanExecute);
            OpenElementButtonImage = IconUtils.Map;

            UpdateElementButtonItemsCount();

            if (MapElementProvider == MapElementProvider.Disabled)
            {
                return true;
            }

            LoadData();

            SetupMapHtml();
            return true;
        }

        private async Task UpdateCount()
        {
            int count = 0;
            foreach (CpiMapPin cpiMapPin in _cpiMap?.Pins?.Values ?? Enumerable.Empty<CpiMapPin>())
            {
                EntityDataSource dataSource = GetDataSource(cpiMapPin);

                if (!string.IsNullOrEmpty(cpiMapPin.CountFunction))
                {
                    Dictionary<string, object> parameters = null;
                    if (cpiMapPin.CountFunctionParams != null && cpiMapPin.CountFunctionParams.Length > 0)
                    {
                        parameters = new Dictionary<string, object>();
                        foreach (string key in cpiMapPin.CountFunctionParams)
                        {
                            if (ViewData.TryGetValue(key, out object output))
                            {
                                parameters.Add(key, output);
                            }
                        }
                    }

                    ExecuteResult result = await _data.PerformFunctionAsync(cpiMapPin.DatasourceProjection ?? ProjectionName, cpiMapPin.CountFunction, parameters);
                    result.CheckFailure();

                    count += result.Value != null ? Convert.ToInt32(result.Value) : 0;
                }
                else if (dataSource != null)
                {
                    EntityQuery query = new EntityQuery(dataSource);

                    count += await _data.CountRecordsAsync(query) ?? 0;
                }
            }

            ElementButtonItemsCount = count;
        }

        protected override void OnDataChanged()
        {
            base.OnDataChanged();

            bool openMapCommandCanExecute = OpenElementButtonCommandCanExecute();
            if (PreviousOpenElementButtonCommandCanExecuteState != openMapCommandCanExecute)
            {
                OpenElementButtonCommand.IsEnabled = openMapCommandCanExecute;
                OpenElementButtonCommand.RaiseCanExecuteChanged();
                PreviousOpenElementButtonCommandCanExecuteState = openMapCommandCanExecute;
            }
        }

        protected override void OnStoredDataChanged(DataChangeSet changeSet)
        {
            base.OnStoredDataChanged(changeSet);

            if (DisplayState == ElementDisplayState.Normal || DisplayState == ElementDisplayState.Expanded || (DataSource != null && DataSource.IsEffectedByChangeSet(changeSet)))
            {
                LoadData();
            }
        }

        protected override void OnDataSourceChanged()
        {
            base.OnDataSourceChanged();

            LoadData();
        }

        private void LoadData()
        {
            // The map element is disabled on this platform, don't load any data as it won't be shown.
            if (MapElementProvider == MapElementProvider.Disabled || DataSource == null)
            {
                return;
            }

            ExecuteBackgroundTask(LoadDataAsync());
        }

        // This method is only used for unit tests
        public void ForceLoadData()
        {
            if (DataSource == null)
            {
                return;
            }

            _hasMapPageLoaded = true;
            ExecuteBackgroundTask(LoadDataAsync());
        }

        private async Task LoadDataAsync()
        {
            if (DisplayState != ElementDisplayState.Normal && _hasMapPageLoaded)
            {
                if (_location == null && !_locationFetchInProgress)
                {
                    if (_cpiMap.RadiusLimit != null)
                    {
                        using (_dialogService.ShowLoadingDialog(Strings.GettingLocation, false))
                        {
                            await UpdateCurrentLocationAsync();
                        }
                    }
                    else
                    {
                        await UpdateCurrentLocation();
                    }
                }

                using (_dialogService.ShowLoadingDialog(Strings.LoadingMapItems, false))
                {
                    NativeMapElement?.CloseCardPanel();
                    ClearPins();   

                    foreach (CpiMapPin cpiMapPin in _cpiMap?.Pins?.Values ?? Enumerable.Empty<CpiMapPin>())
                    {
                        await LoadDataForPin(cpiMapPin);
                    }

                    if (CurrentCardPin == null)
                    {
                        // Zoom to fit all markers on the map
                        NativeMapElement?.InvokeJsFunction("fitAllMarkers", new string[] { });
                    }

                    ShowCurrentLocation();
                }

                _insightsLogger?.TrackAppFeature(string.Format("Map-{0}-{1}-{2}", ProjectionName, DataSource.EntitySetName, DataSource.EntityName));
            }
            else
            {
                await UpdateCount();
            }
        }

        private async Task UpdateCurrentLocation()
        {
            await UpdateCurrentLocationAsync();
            ShowCurrentLocation();
        }

        private async Task UpdateCurrentLocationAsync()
        {
            _locationFetchInProgress = true;
            _location = await _locationService.GetGpsLocationAsync();
            _locationFetchInProgress = false;
        }

        private async Task LoadDataForPin(CpiMapPin cpiMapPin)
        {
            try
            {
                EntityDataSource dataSource = GetDataSource(cpiMapPin);

                if (dataSource != null)
                {
                    EntityQuery query = new EntityQuery(dataSource)
                    {
                        SelectAttributes = GetMapPinSelectAttributes(cpiMapPin, _cpiMap)
                    };

                    query.Take = 20;

                    if (dataSource == PageData.DataSource)
                    {
                        PageData.Filter?.Apply(query);
                    }

                    await GetPinsForQuery(query, cpiMapPin);

                    //Reloading the Card Panel when the orientation changes.
                    NativeMapElement?.ShowCardPanel();
                }
            }
            catch (Exception e)
            {
                await HandleException(e);
            }
        }

        private async Task GetPinsForQuery(EntityQuery query, CpiMapPin cpiMapPin)
        {
            if (query == null || Pins.Count > MaxPinCount)
            {
                return;
            }

            try
            {
                if (cpiMapPin.DatasourceFunction != null)
                {
                    PageData.Filter?.Apply(query);

                    Dictionary<string, object> parameters = new Dictionary<string, object>();

                    ViewData.Record?.ExtractFunctionParameters(cpiMapPin.DatasourceFunctionParams, parameters);

                    ExecuteResult result = await _data.PerformFunctionAsync(cpiMapPin.DatasourceProjection ?? ProjectionName, cpiMapPin.DatasourceFunction, parameters);
                    result.CheckFailure();

                    List<EntityRecord> records = new List<EntityRecord>();

                    if (result.Value is IEnumerable rows)
                    {
                        foreach (RemoteRow row in rows.OfType<RemoteRow>())
                        {
                            if (row != null)
                            {
                                records.Add(new EntityRecord(row, null));
                            }
                        }
                    }
                    else if (result.Value is RemoteRow row)
                    {
                        records.Add(new EntityRecord(row, null));
                    }

                    AddPinsInsideRadius(records, cpiMapPin);
                }
                else if (cpiMapPin.DatasourceEntitySet != null)
                {
                    EntityQueryResult results = await _data.GetRecordsAsync(query, CancellationToken.None);

                    AddPinsInsideRadius(results.Records, cpiMapPin);

                    if (results.HasMoreResults)
                    {
                        await GetPinsForQuery(results.GetNextQuery(), cpiMapPin);
                    }
                }
            }
            catch (Exception e)
            {
                NativeMapElement?.InvokeJsFunction("initMap", new string[] { });
                await HandleException(e);
            }
        }

        private void AddPinsInsideRadius(IReadOnlyList<EntityRecord> results, CpiMapPin cpiMapPin)
        {
            CpiCard card = cpiMapPin.Card == null ? null : _metadata.FindCard(cpiMapPin.DatasourceProjection ?? ProjectionName, cpiMapPin.Card);
            _pinsWithDistanceToCurrentLocation = new Dictionary<MapPin, double>();

            foreach (EntityRecord entityRecord in results)
            {
                RecordData recordData = new RecordData(_logger, _metadata, _data);
                recordData.LoadRecord(ProjectionName, entityRecord);

                CardData cardData = new CardData(PageData, recordData);
                MapPin pin = MapPin.CreateMapPin(ProjectionName, _metadata, _expressionRunner, cardData, cpiMapPin, card);

                if (pin != null)
                {
                    if (_location == null)
                    {
                        _pinsWithDistanceToCurrentLocation.Add(pin, default);
                    }
                    else if (IsInsideRadius(pin, _location))
                    {
                        CalculateAndStorePinsWithDistances(pin, _location);
                    }
                }
            }

            List<MapPin> pins = (from kvp in _pinsWithDistanceToCurrentLocation select kvp.Key).Distinct().ToList();

            int.TryParse(InterpolateString(_cpiMap.MaxHits, PageData.DefaultViewData.Record), out int maxHits);

            if (pins.Count > 0 && maxHits <= 0)
            {
                Pins.AddRange(pins);
                ElementButtonItemsCount = Pins.Count;
            }
            else if (pins.Count > 0)
            {
                List<KeyValuePair<MapPin, double>> pinsWithDistance = _pinsWithDistanceToCurrentLocation.ToList();
                pinsWithDistance.Sort((pair1, pair2) => pair1.Value.CompareTo(pair2.Value));

                List<MapPin> allPinsSortedByDistance = (from kvp in pinsWithDistance select kvp.Key).Distinct().ToList();

                if (maxHits != 0 && maxHits < pins.Count)
                {
                    Pins.AddRange(allPinsSortedByDistance.GetRange(0, maxHits));
                }
                else
                {
                    Pins.AddRange(pins);
                }

                ElementButtonItemsCount = Pins.Count;
            }

            AddPins(Pins);
            ShowCurrentLocation();
        }

        private EntityDataSource GetDataSource(CpiMapPin pin)
        {
            if (!string.IsNullOrWhiteSpace(pin.DatasourceFunction))
            {
                return FunctionDataSource.Create(_metadata, pin.DatasourceProjection ?? ProjectionName, pin.DatasourceFunction, null);
            }

            if (!string.IsNullOrWhiteSpace(pin.DatasourceEntitySet))
            {
                return EntityDataSource.FromEntitySet(_metadata, pin.DatasourceProjection ?? ProjectionName, pin.DatasourceEntitySet);
            }

            return DataSource ?? null;
        }

        private string[] GetMapPinSelectAttributes(CpiMapPin cpiMapPin, CpiMap cpiMap)
        {
            HashSet<string> attributes = new HashSet<string>();
            AttributeFinder.FindInMap(attributes, _metadata, cpiMap);
            AttributeFinder.FindInMapPin(attributes, _metadata, cpiMapPin.DatasourceProjection ?? ProjectionName, cpiMapPin);
            AttributeFinder.FindInCard(attributes, _metadata, cpiMapPin.DatasourceProjection ?? ProjectionName, _metadata.FindCard(cpiMapPin.DatasourceProjection ?? ProjectionName, cpiMapPin.Card), false);
            return attributes.ToArray();
        }

        public override void GetSelectAttributes(ICollection<string> attributes)
        {
            foreach (CpiMapPin cpiMapPin in _cpiMap?.Pins?.Values ?? Enumerable.Empty<CpiMapPin>())
            {
                string[] pinAttributes = GetMapPinSelectAttributes(cpiMapPin, _cpiMap);
                foreach (string attr in pinAttributes)
                {
                    attributes.Add(attr);
                }
            }
        }

        private void SetupMapHtml()
        {
            string page = GetResourceAsString("index.html");

            foreach (Match match in Regex.Matches(page, @"{{\s*[\w\.\-_]+\s*}}", RegexOptions.None, TimeSpan.FromSeconds(10)))
            {
                string name = match.Value.Trim(new char[] { '{', '}', ' ' });
                page = page.Replace(match.Value, GetResourceAsString(name));
            }

            page = page.Replace("API_KEY", GetResourceAsString("apikey.txt"));

            Html = page;
        }

        private string GetResourceAsString(string name)
        {
            try
            {
                using (Stream stream = _assembly.GetManifestResourceStream(MappingFilesLocation + name))
                {
                    if (stream != null)
                    {
                        using (StreamReader reader = new StreamReader(stream))
                        {
                            return reader.ReadToEnd();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Current.Error(ex.Message);
            }

            return string.Empty;
        }

        public void OnMapPageLoaded()
        {
            NativeMapElement?.InvokeJsFunction("initMap", new string[] { });
            _hasMapPageLoaded = true;
            _isFirstPins = true;
            LoadData();
        }

        private void ShowCurrentLocation()
        {
            double radius = GetRadiusInMeters();

            if (_location != null)
            {
                NumberFormatInfo nfi = new NumberFormatInfo() { NumberDecimalSeparator = "." };
                NativeMapElement?.InvokeJsFunction("addCurrentLocation", new string[] { _location.Latitude.ToString(nfi), _location.Longitude.ToString(nfi), radius.ToString(nfi) });
            }
        }

        private void AddPins(IList<MapPin> pins)
        {
            lock (_addPinsLock)
            {
                if (_isFirstPins)
                {
                    NativeMapElement?.InvokeJsFunction("addPinsAndFit", new string[] { JsonConvert.SerializeObject(pins) });
                    _isFirstPins = false;
                }
                else
                {
                    NativeMapElement?.InvokeJsFunction("addPins", new string[] { JsonConvert.SerializeObject(pins) });
                }
            }
        }

        private void ClearPins()
        {
            Pins.Clear();
            NativeMapElement?.InvokeJsFunction("clearPins", new string[] { });
        }

        public void InvokeNativeMethod(string message)
        {
            if (string.IsNullOrWhiteSpace(message))
            {
                return;
            }

            try
            {
                JObject jo = JObject.Parse(message);

                string method = jo.ContainsKey("method") ? jo.SelectToken("method").Value<string>() : string.Empty;
                string errorMsg = jo.ContainsKey("errorMsg") ? jo.SelectToken("errorMsg").Value<string>() : string.Empty;
                string id = jo.ContainsKey("id") ? jo.SelectToken("id")?.Value<string>() : string.Empty;
                string[] closeMarkers = jo.ContainsKey("closeMarkers") ? (jo["closeMarkers"] as JArray)?.ToObject<string[]>() : new string[] { };

                if (method == "OnMarkerClicked" && !string.IsNullOrWhiteSpace(id))
                {
                    List<MapPin> pins = new List<MapPin>();

                    MapPin pin = Pins.FirstOrDefault(p => p.Id == id);
                    if (pin != null)
                    {
                        pins.Add(pin);
                    }

                    if (closeMarkers?.Length > 0)
                    {
                        foreach (string pinId in closeMarkers)
                        {
                            MapPin closePin = Pins.FirstOrDefault(p => p.Id == pinId);
                            if (closePin != null)
                            {
                                pins.Add(closePin);
                            }
                        }
                    }

                    _currentCardPins = pins;
                    _currentCardPinIndex = 0;
                    NativeMapElement?.ShowCardPanel();
                    NativeMapElement?.InvokeJsFunction("centerOnPin", new string[] { JsonConvert.SerializeObject(CurrentCardPin.Id) });
                }
                else if (method == "LogError" && !string.IsNullOrWhiteSpace(errorMsg))
                {
                    Logger.Current.Error("Map errror: " + errorMsg);
                }
            }
            catch (Exception ex)
            {
                Logger.Current.Error(ex.Message);
            }
        }

        public void BackCardPanel()
        {
            if (_currentCardPins == null || _currentCardPins.Count == 0)
            {
                return;
            }

            if (_currentCardPinIndex == 0)
            {
                _currentCardPinIndex = _currentCardPins.Count - 1;
            }
            else
            {
                _currentCardPinIndex--;
            }

            NativeMapElement?.ShowCardPanel();
            NativeMapElement?.InvokeJsFunction("centerOnPin", new string[] { JsonConvert.SerializeObject(CurrentCardPin.Id) });
        }

        public void ForwardCardPanel()
        {
            if (_currentCardPins == null || _currentCardPins.Count == 0)
            {
                return;
            }

            if (_currentCardPinIndex == _currentCardPins.Count - 1)
            {
                _currentCardPinIndex = 0;
            }
            else
            {
                _currentCardPinIndex++;
            }

            NativeMapElement?.ShowCardPanel();
            NativeMapElement?.InvokeJsFunction("centerOnPin", new string[] { JsonConvert.SerializeObject(CurrentCardPin.Id) });
        }

        public static string ConvertToJSCall(string functionName, string[] args)
        {
            if (string.IsNullOrWhiteSpace(functionName))
            {
                return null;
            }

            string functionCall = functionName + "(";

            for (int i = 0; i < args.Length; i++)
            {
                functionCall += $"JSON.stringify({args[i]})";

                if (i != args.Length - 1)
                {
                    functionCall += ", ";
                }
            }

            functionCall += ");";
            return functionCall;
        }

        private void OpenMap()
        {
            if (_breadcrumbManager != null)
            {
                _breadcrumbManager.PushToBreadcrumbStack();
            }

            DisplayState = ElementDisplayState.FullScreen;
        }

        public CardDef GetCardDefForPin(MapPin pin)
        {
            if (pin.CpiCard == null)
            {
                return null;
            }

            return _cardDefCreator.CreateCardDef(pin.CardData.Record.ProjectionName, pin.CpiCard, null, GetPinItemValue, _commandExecutor);
        }

        private object GetPinItemValue(AttributePath attribute, object dataContext)
        {
            ViewData l = (ViewData)dataContext;
            return l?.Record[attribute];
        }

        protected override void OnDisplayStateChanged()
        {
            base.OnDisplayStateChanged();

            if (HasLoaded)
            {
                LoadData();
            }

            if (DisplayState == ElementDisplayState.Normal)
            {
                _breadcrumbManager?.PopFromBreadcrumbStack();
            }
        }

        private bool IsInsideRadius(MapPin mapPin, GpsLocation location)
        {
            double finalRadius = CalculateFinalRadius();

            double.TryParse(mapPin.Latitude, NumberStyles.Float, CultureInfo.InvariantCulture, out double lat);
            double.TryParse(mapPin.Longitude, NumberStyles.Float, CultureInfo.InvariantCulture, out double lon);

            double dist = CalculateDistance(location.Latitude, location.Longitude, lat, lon);

            return dist <= finalRadius;
        }

        private double CalculateFinalRadius()
        {
            double radius = GetRadiusInMeters();
            double finalRadius = radius != 0 ? radius : double.MaxValue;

            return finalRadius;
        }

        private double CalculateDistance(double lat, double lon, double targetLat, double targetLon)
        {
            double radiusOfEarth = 6371; // Radius of the earth in km
            if (_appParameters.GetEmbeddedMapUnits() == MapUnits.Miles)
            {
                radiusOfEarth = 3958.8; // Radius of the earth in miles
            }
        
            double latDistance = ToRadians(lat - targetLat);
            double lonDistance = ToRadians(lon - targetLon);
            double cal1 = (Math.Sin(latDistance / 2) * Math.Sin(latDistance / 2))
                    + (Math.Cos(ToRadians(lat)) * Math.Cos(ToRadians(targetLat))
                    * Math.Sin(lonDistance / 2) * Math.Sin(lonDistance / 2));
            double cal2 = 2 * Math.Atan2(Math.Sqrt(cal1), Math.Sqrt(1 - cal1));
            double distance = radiusOfEarth * cal2 * 1000; // km to meters

            if (_appParameters.GetEmbeddedMapUnits() == MapUnits.Miles)
            {
                distance = radiusOfEarth * cal2 * 1609.34; // miles to meters
            }

            return distance;
        }

        private void CalculateAndStorePinsWithDistances(MapPin mapPin, GpsLocation location)
        {
            double finalRadius = CalculateFinalRadius();

            double.TryParse(mapPin.Latitude, NumberStyles.Float, CultureInfo.InvariantCulture, out double lat);
            double.TryParse(mapPin.Longitude, NumberStyles.Float, CultureInfo.InvariantCulture, out double lon);

            double distance = CalculateDistance(location.Latitude, location.Longitude, lat, lon);

            //Only stores pins with distance within the radiuslimit from current location
            if (distance <= finalRadius)
            {
                _pinsWithDistanceToCurrentLocation.Add(mapPin, distance);
            }
        }

        private double ToRadians(double val)
        {
            return (val * Math.PI) / 180;
        }

        private double GetRadiusInMeters()
        {
            if (double.TryParse(InterpolateString(_cpiMap.RadiusLimit, PageData.DefaultViewData.Record), NumberStyles.Float, CultureInfo.InvariantCulture, out double radius))
            {
                if (_appParameters.GetEmbeddedMapUnits() == MapUnits.Miles)
                {
                    return radius * 1609.344;
                }
                else
                {
                    return radius * 1000;
                }
            }
            else
            {
                return 0;
            }
        }
    }
}
