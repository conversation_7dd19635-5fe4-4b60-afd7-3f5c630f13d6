﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.Reporting;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Framework.UI.Elements.Lists;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Observables;

namespace Ifs.Uma.Framework.UI.Assistants
{
    public sealed class AssistantStep : ObservableBase
    {
        private readonly IMetadata _metadata;
        public string ProjectionName { get; }

        public AssistantBase Assistant { get; }

        public CpiAssistantStep CpiStep { get; }

        public CommandBlock Commands { get; set; }
        public ElementList Elements { get; }
        public PageData Data { get; }

        public bool IsFirst { get; }
        public bool IsLast { get; }
        public bool IsCompletionStep { get; }
        public bool IsModal { get; set; }
        public bool IsSingleStep
        {
            get { return IsFirst && IsLast; }
        }

        private string _label;
        public string Label
        {
            get { return _label; }
            set
            {
                if (_label != value)
                {
                    _label = value;
                    OnPropertyChanged(nameof(Label));
                }
            }
        }

        private string _description;
        public string Description
        {
            get { return _description; }
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged(nameof(Description));
                }
            }
        }

        private string _htmlContent;
        public string HtmlContent
        {
            get { return _htmlContent; }
            private set
            {
                _htmlContent = value;
                OnPropertyChanged(nameof(HtmlContent));
            }
        }

        public UpdatingState UpdatingState { get; } = new UpdatingState();

        private readonly ElementCreator _elementCreator;
        public ElementCreator ElementCreator
        {
            get { return _elementCreator; }
        }

        private readonly ICommandExecutor _commandExecutor;
        private readonly IExpressionRunner _expressionRunner;

        public AssistantStep(IMetadata metadata, ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, ElementCreator elementCreator,
            string projectionName, PageData data, AssistantBase assistant, CpiAssistantStep step)
        {
            if (metadata == null) throw new ArgumentNullException(nameof(metadata));
            if (commandExecutor == null) throw new ArgumentNullException(nameof(commandExecutor));
            if (expressionRunner == null) throw new ArgumentNullException(nameof(expressionRunner));
            if (elementCreator == null) throw new ArgumentNullException(nameof(elementCreator));
            if (projectionName == null) throw new ArgumentNullException(nameof(projectionName));
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (assistant == null) throw new ArgumentNullException(nameof(assistant));
            if (step == null) throw new ArgumentNullException(nameof(step));

            _metadata = metadata;
            _commandExecutor = commandExecutor;
            _expressionRunner = expressionRunner;
            _elementCreator = elementCreator;

            ProjectionName = projectionName;
            Assistant = assistant;
            
            Data = data;

            Elements = new ElementList(UpdatingState, elementCreator);
            Elements.ElementsLoaded += Elements_ElementsLoaded;
            Elements.PageData = Data;

            CpiStep = step;

            if (assistant.CpiAssistant.Steps != null && assistant.CpiAssistant.Steps.Length > 0)
            {
                IsFirst = Array.IndexOf(assistant.CpiAssistant.Steps, CpiStep) == 0;
                IsLast = Array.IndexOf(assistant.CpiAssistant.Steps, CpiStep) == assistant.CpiAssistant.Steps.Length - 1;
            }

            IsCompletionStep = CpiStep == assistant.CpiAssistant.Cancel || CpiStep == assistant.CpiAssistant.Final;

            Data.DefaultViewData.Record.DataChanged += Data_DataChanged;

            UpdateLabel();
            UpdateDescription();

            Commands = new CommandBlock(UpdatingState, metadata, commandExecutor, expressionRunner);
            Commands.PageData = Data;

            if (CpiStep.CommandGroups != null && CpiStep.CommandGroups.Any(x => x.CommandNames.Length > 0) && !IsSingleStep)
            {
                Commands.Load(ProjectionName, CpiStep.CommandGroups, false, true, true);
            }

            Elements.LoadElements(ProjectionName, CpiStep.CommandGroups, CpiStep.Content, null);

            foreach (RepeatingSectionElement rse in Elements.Elements.Where(x => x is RepeatingSectionElement))
            {
                rse.PropertyChanged += RepeatingSectionElement_PropertyChanged;
            }

            _ = CheckAllElementsLoaded();

            if (IsSingleStep)
            {
                Elements.SetPrimaryElement(null);

                ListElement lastListElement = Elements.Elements.LastOrDefault(x => x is ListElement) as ListElement;
                if (lastListElement != null)
                {
                    lastListElement.DisplayState = ElementDisplayState.Expanded;
                }
            }
        }

        private async void Elements_ElementsLoaded(object sender, EventArgs e)
        {
            List<RepeatingSectionElement> repeatingSections = Elements.Elements.Where(x => x is RepeatingSectionElement).Cast<RepeatingSectionElement>().ToList();

            foreach (RepeatingSectionElement element in repeatingSections)
            {
               await element.LoadData();
            }
        }

        private void Data_DataChanged(object sender, EventArgs e)
        {
            UpdateLabel();
            UpdateDescription();

            if (Elements.Elements.Any(x => x is RepeatingSectionElement))
            {
                Elements.ReloadData();
                _ = CheckAllElementsLoaded();
            }
        }

        public bool IsFirstEnabledStep()
        {
            bool enabled = _expressionRunner.RunCheck(CpiStep.OfflineEnabled ?? CpiStep.Enabled, Data.DefaultViewData, true);

            if (enabled)
            {
                if (IsFirst)
                {
                    return true;
                }

                int index = Array.IndexOf(Assistant.CpiAssistant.Steps, CpiStep);

                for (int i = index - 1; i >= 0; i--)
                {
                    if (_expressionRunner.RunCheck(Assistant.CpiAssistant.Steps[i].OfflineEnabled ?? Assistant.CpiAssistant.Steps[i].Enabled, Data.DefaultViewData, true))
                    {
                        return false;
                    }
                }
            }
            else
            {
                return false;
            }

            return true;
        }

        public bool IsLastEnabledStep()
        {
            bool enabled = _expressionRunner.RunCheck(CpiStep.OfflineEnabled ?? CpiStep.Enabled, Data.DefaultViewData, true);

            if (enabled)
            {
                if (IsLast)
                {
                    return true;
                }

                int index = Array.IndexOf(Assistant.CpiAssistant.Steps, CpiStep);

                for (int i = index + 1; i < Assistant.CpiAssistant.Steps.Length; i++)
                {
                    if (_expressionRunner.RunCheck(Assistant.CpiAssistant.Steps[i].OfflineEnabled ?? Assistant.CpiAssistant.Steps[i].Enabled, Data.DefaultViewData, true))
                    {
                        return false;
                    }
                }
            }
            else
            {
                return false;
            }

            return true;
        }

        private void UpdateLabel()
        {
            Label = _expressionRunner.InterpolateString(CpiStep.Label, Data.DefaultViewData.Record);
        }

        private void UpdateDescription()
        { 
            Description = _expressionRunner.InterpolateString(CpiStep.Description, Data.DefaultViewData.Record);
        }

        public bool CalculateIsValid()
        {
            return _expressionRunner.RunCheck(CpiStep.OfflineValid ?? CpiStep.Valid, Data.DefaultViewData, true);
        }

        public async Task<ExecuteResult> ExecuteNextAsync()
        {
            if (CpiStep.NextCommand != null)
            {
                return await _commandExecutor.ExecuteAsync(ProjectionName, Data.DefaultViewData, CpiStep.NextCommand);
            }

            return ExecuteResult.None;
        }

        private void RepeatingSectionElement_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(RepeatingSectionElement.DataLoaded))
            {
                _ = CheckAllElementsLoaded();
            }
        }

        public async Task CheckAllElementsLoaded()
        {
            foreach (RepeatingSectionElement rse in Elements.Elements.Where(x => x is RepeatingSectionElement))
            {
                if (!rse.DataLoaded)
                {
                    return;
                }
            }

            if (Elements.HasRepeatingSections)
            {
                string logoResource = string.Empty;

#if SIGNATURE_SERVICE
                foreach (CpiCommandGroup group in Assistant.CpiAssistant.CommandGroups)
                {
                    if (group.CommandNames.Any())
                    {
                        CpiCommand command = _metadata.FindCommand(ProjectionName, group.CommandNames[0].ToString());

                        foreach (CpiExecute execute in command.Execute)
                        {
                            if (execute.Call.Method == CpiExecuteCallMethod.Sign)
                            {                                
                                CpiSignCallArgs args = (CpiSignCallArgs)execute.Call.Args;
                                foreach (CpiExecute executes in command.Execute)
                                {
                                    if (executes.Call.Method == CpiExecuteCallMethod.Set && args.HeaderLogo == executes.Assign)
                                    {
                                        logoResource = ((CpiSetCallArgs)executes.Call.Args)?.Value.ToString();
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
#endif
                // Generate HTML once all the repeating section elements have loaded their data
                IReportingService reportingService = Resolver.Resolve<IReportingService>();
                HtmlContent = await reportingService.GenerateHtmlFromContent(Description ?? string.Empty, logoResource, Elements, false, CancellationToken.None);
            }
        }
    }
}
