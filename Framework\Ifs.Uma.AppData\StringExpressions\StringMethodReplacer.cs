﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection;
using Ifs.Uma.AppData.Expressions;

namespace Ifs.Uma.AppData.StringExpressions
{
    internal sealed class StringMethodReplacer : IfsExpressionVisitor
    {
        private static readonly Dictionary<Tuple<string, int>, MethodInfo> _methods = new Dictionary<Tuple<string, int>, MethodInfo>();

        static StringMethodReplacer()
        {
            StringMethods.RegisterMethods(AddMethod);
        }

        private static void AddMethod(string name, string methodName)
        {
            MethodInfo method = typeof(StringMethods).GetTypeInfo().GetDeclaredMethod(methodName);
            if (method != null)
            {
                Tuple<string, int> key = Tuple.Create(name, method.GetParameters().Length);
                _methods[key] = method;
            }
        }
        
        public static Expression Rewrite(Expression expression)
        {
            IfsExpressionVisitor visitor = new StringMethodReplacer();
            return visitor.Visit(expression);
        }
        
        protected internal override Expression VisitMethod(MethodExpression exp)
        {
            exp = (MethodExpression)base.VisitMethod(exp);

            Tuple<string, int> key = Tuple.Create(exp.MethodName, exp.Arguments.Count);

            if (_methods.TryGetValue(key, out MethodInfo method))
            {
                return Expression.Call(method, exp.Arguments);
            }

            return exp;
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            Expression operand = Visit(node.Operand);

            if (node.NodeType == ExpressionType.Convert && node.Type == operand.Type && node.Method == null)
            {
                return operand;
            }

            // Must override here since the base does some unwanted validation
            return node.Update(operand);
        }
    }
}
