﻿using Ifs.Uma.Data;
using Ifs.Uma.Utility;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Database.Tests
{
    using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

    [ServerNaming(NamingConvention.UpperCase)]
    public enum TestEnum
    {
        FirstOne,
        [ServerValue("Second1")]
        SecondOne,
        ThirdOne,
        [ServerValue("NUMBER_FOUR")]
        FourthOne
    }

    [Table]
    public class TestRow
    {
        public TestRow() { }

        [Column(AutoIncrement = true)]
        public int RowId { get; set; }

        [Column(MaxLength = 80)]
        public string Description { get; set; }
    }

    [Table]
    [Index(Name = "uq_name", Unique = true, Columns = "Name")]
    public class TwoColPkRow
    {
        private int _x;
        private int _y;
        private string _name;

        public TwoColPkRow() { }

        [Column(PrimaryKey = true, Storage = "_x")]
        public int X { get { return _x; } set { _x = value; } }
        [Column(PrimaryKey = true, Storage = "_y")]
        public int Y { get { return _y; } set { _y = value; } }
        [Column(Mandatory = true, Storage = "_name")]
        public string Name { get { return _name; } set { _name = value; } }
    }

    [Table]
    [Index(Columns = "AnswerId", Unique = true)]
    public class AppsTestRow : RemoteRow
    {
        private long _answerId;
        private string _description;
        private DateTime? _optionalStamp;
        private TestEnum? _optionalEnum;

        public AppsTestRow()
            : base("apps_test_row") { }

        [Column]
        public long AnswerId
        {
            get { return _answerId; }
            set
            {
                if (_answerId != value)
                {
                    SetProperty(ref _answerId, value, "AnswerId");
                }
            }
        }

        [Column(MaxLength = 100, Mandatory = true)]
        public string Description
        {
            get { return _description; }
            set
            {
                if (value != null && value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (_description != value)
                {
                    SetProperty(ref _description, value, "Description");
                }
            }
        }

        [Column]
        public DateTime? OptionalStamp
        {
            get { return _optionalStamp; }
            set
            {
                if (_optionalStamp != value)
                {
                    SetProperty(ref _optionalStamp, value, "OptionalStamp");
                }
            }
        }

        [Column]
        public TestEnum? OptionalEnum
        {
            get { return _optionalEnum; }
            set
            {
                if (_optionalEnum != value)
                {
                    SetProperty(ref _optionalEnum, value, "OptionalEnum");
                }
            }
        }
    }

    public enum SurveyMobileWorkFlow : byte
    {
        Accept,
        CompletedAfterwizard,
        CompletedBeforewizard,
        Employee,
        [ServerValue("NEW_WORKORDER")]
        NewWorkOrder,
        [ServerValue("OBJECT_INFO")]
        ObjectInformation,
        Onhold,
        Onroute,
        Onsite,
        Reject,
        ReVisit,
        [ServerValue("ROUTEACT_COMPLETE")]
        RouteActionComplete,
        [ServerValue("ROUTEACT_NOTCOMPLETE")]
        RouteActionNotComplete,
        RouteAction,
        Suspend,
        WorkOrder
    }

    [Table]
    [Index(Name = "ix_survey_mobile_details", Columns = "SurveyId", Unique = true)]
    public partial class SurveyMobileDetailsRow : RowBase
    {
        private string _surveyId;
        private SurveyMobileWorkFlow? _mobileWorkflow;
        private string _mchCodeContract;
        private string _mchCode;
        private string _orgCode;

        #region Extensibility Method Definitions

        partial void OnCreated();
        partial void OnSurveyIdChanging(string value);
        partial void OnSurveyIdChanged();
        partial void OnMobileWorkflowChanging(SurveyMobileWorkFlow? value);
        partial void OnMobileWorkflowChanged();
        partial void OnMchCodeContractChanging(string value);
        partial void OnMchCodeContractChanged();
        partial void OnMchCodeChanging(string value);
        partial void OnMchCodeChanged();
        partial void OnOrgCodeChanging(string value);
        partial void OnOrgCodeChanged();

        #endregion

        public SurveyMobileDetailsRow()
        {
            OnCreated();
        }

        [Column(Storage = "_surveyId", Mandatory = true, MaxLength = 20)]
        public string SurveyId
        {
            get { return _surveyId; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_surveyId != value)
                {
                    OnSurveyIdChanging(value);
                    SetProperty(ref _surveyId, value, "SurveyId");
                    OnSurveyIdChanged();
                }
            }
        }

        [Column(Storage = "_mobileWorkflow")]
        public SurveyMobileWorkFlow? MobileWorkflow
        {
            get { return _mobileWorkflow; }
            set
            {
                if (_mobileWorkflow != value)
                {
                    OnMobileWorkflowChanging(value);
                    SetProperty(ref _mobileWorkflow, value, "MobileWorkflow");
                    OnMobileWorkflowChanged();
                }
            }
        }

        [Column(Storage = "_mchCodeContract", TextFormat = TextFormats.Uppercase, MaxLength = 5)]
        public string MchCodeContract
        {
            get { return _mchCodeContract; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_mchCodeContract, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnMchCodeContractChanging(newValue);
                    SetProperty(ref _mchCodeContract, newValue, "MchCodeContract");
                    OnMchCodeContractChanged();
                }
            }
        }

        [Column(Storage = "_mchCode", TextFormat = TextFormats.Uppercase, MaxLength = 100)]
        public string MchCode
        {
            get { return _mchCode; }
            set
            {
                if (value != null && value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_mchCode, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnMchCodeChanging(newValue);
                    SetProperty(ref _mchCode, newValue, "MchCode");
                    OnMchCodeChanged();
                }
            }
        }

        [Column(Storage = "_orgCode", TextFormat = TextFormats.Uppercase, MaxLength = 8)]
        public string OrgCode
        {
            get { return _orgCode; }
            set
            {
                if (value != null && value.Length > 8) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_orgCode, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnOrgCodeChanging(newValue);
                    SetProperty(ref _orgCode, newValue, "OrgCode");
                    OnOrgCodeChanged();
                }
            }
        }
    }

    [Table]
    [Index(Name = "ix_survey", Columns = "SurveyId", Unique = true)]
    public partial class SurveyRow : RowBase
    {
        private string _surveyId;
        private string _surveyName;
        private string _description;

        #region Extensibility Method Definitions

        partial void OnCreated();
        partial void OnSurveyIdChanging(string value);
        partial void OnSurveyIdChanged();
        partial void OnSurveyNameChanging(string value);
        partial void OnSurveyNameChanged();
        partial void OnDescriptionChanging(string value);
        partial void OnDescriptionChanged();

        #endregion

        public SurveyRow()
        {
            OnCreated();
        }

        [Column(Storage = "_surveyId", Mandatory = true, MaxLength = 20)]
        public string SurveyId
        {
            get { return _surveyId; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_surveyId != value)
                {
                    OnSurveyIdChanging(value);
                    SetProperty(ref _surveyId, value, "SurveyId");
                    OnSurveyIdChanged();
                }
            }
        }

        [Column(Storage = "_surveyName", Mandatory = true, MaxLength = 50)]
        public string SurveyName
        {
            get { return _surveyName; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 50) throw new ArgumentOutOfRangeException("value");
                if (_surveyName != value)
                {
                    OnSurveyNameChanging(value);
                    SetProperty(ref _surveyName, value, "SurveyName");
                    OnSurveyNameChanged();
                }
            }
        }

        [Column(Storage = "_description", Mandatory = true, MaxLength = 200)]
        public string Description
        {
            get { return _description; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 200) throw new ArgumentOutOfRangeException("value");
                if (_description != value)
                {
                    OnDescriptionChanging(value);
                    SetProperty(ref _description, value, "Description");
                    OnDescriptionChanged();
                }
            }
        }
    }

    [Table(Name = "client_profile_value", System = true)]
    [Index(Name = "ix_client_profile_value", Columns = "ProfileSection, ProfileEntry, Owner", Unique = true)]
    public class RoamingProfileValue : RowBase
    {
        [Column(MaxLength = 1000)]
        public string ProfileSection { get; set; }

        [Column(MaxLength = 200)]
        public string ProfileEntry { get; set; }

        [Column]
        public bool Owner { get; set; }

        private string _profileValue;
        [Column(Storage = "_profileValue", MaxLength = 4000)]
        public string ProfileValue
        {
            get { return _profileValue; }
            set
            {
                if (_profileValue != value)
                {
                    SetProperty(ref _profileValue, value, "ProfileValue");
                }
            }
        }

        private byte[] _profileBinaryValue;
        [Column(Storage = "_profileBinaryValue", MaxLength = 4000)]
        public byte[] ProfileBinaryValue
        {
            get { return _profileBinaryValue; }
            set
            {
                if (_profileBinaryValue != value)
                {
                    SetProperty(ref _profileBinaryValue, value, "ProfileBinaryValue");
                }
            }
        }

        private string _binaryValueType;
        [Column(Storage = "_binaryValueType", MaxLength = 100)]
        public string BinaryValueType
        {
            get { return _binaryValueType; }
            set
            {
                if (_binaryValueType != value)
                {
                    SetProperty(ref _binaryValueType, value, "BinaryValueType");
                }
            }
        }
    }

    [Table]
    [Index(Name = "ix_equipment_criticality", Columns = "Criticality", Unique = true)]
    public partial class EquipmentCriticalityRow : RowBase
    {
        private string _criticality;
        private string _description;

        #region Extensibility Method Definitions

        partial void OnCreated();
        partial void OnCriticalityChanging(string value);
        partial void OnCriticalityChanged();
        partial void OnDescriptionChanging(string value);
        partial void OnDescriptionChanged();

        #endregion

        public EquipmentCriticalityRow()
        {
            OnCreated();
        }

        [Column(Storage = "_criticality", Mandatory = true, MaxLength = 10)]
        public string Criticality
        {
            get { return _criticality; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_criticality != value)
                {
                    OnCriticalityChanging(value);
                    SetProperty(ref _criticality, value, "Criticality");
                    OnCriticalityChanged();
                }
            }
        }

        [Column(Storage = "_description", MaxLength = 60)]
        public string Description
        {
            get { return _description; }
            set
            {
                if (value != null && value.Length > 60) throw new ArgumentOutOfRangeException("value");
                if (_description != value)
                {
                    OnDescriptionChanging(value);
                    SetProperty(ref _description, value, "Description");
                    OnDescriptionChanged();
                }
            }
        }
    }

    public enum SerialOperationalStatus
    {
        Designed,
        InOperation,
        NotApplicable,
        OutOfOperation,
        PlannedForOperation,
        Renamed,
        Scrapped
    }

    [Table]
    [Index(Name = "ix_equipment_object", Columns = "Contract, MchCode", Unique = true)]
    public partial class EquipmentObjectRow : RowBase
    {
        private string _contract;
        private string _mchCode;
        private string _designStatus;
        private string _supMchCode;
        private string _objLevel;
        private string _manufacturerNo;
        private string _vendorNo;
        private string _serialNo;
        private string _type;
        private string _supContract;
        private string _partNo;
        private SerialOperationalStatus? _operationalStatus;
        private DateTime? _manufacturedDate;
        private string _objectNo;
        private string _mchName;
        private string _mchLoc;
        private string _mchPos;
        private string _mchDoc;
        private double? _purchPrice;
        private DateTime? _purchDate;
        private DateTime? _warrExp;
        private string _note;
        private string _info;
        private string _data;
        private string _company;
        private DateTime? _productionDate;
        private string _hasStructure;
        private string _hasConnection;
        private string _mchCodeKeyValue;
        private string _typeKeyValue;
        private string _equipmentMainPosition;
        private string _groupId;
        private string _mchType;
        private string _costCenter;
        private string _categoryId;
        private string _owner;
        private string _keyRef;
        private string _luName;
        private string _criticality;
        private string _objectType;
        private string _supMchName;
        private string _mchTypeDesc;
        private string _groupDesc;
        private double? _longitude;
        private double? _latitude;

        #region Extensibility Method Definitions

        partial void OnCreated();
        partial void OnContractChanging(string value);
        partial void OnContractChanged();
        partial void OnMchCodeChanging(string value);
        partial void OnMchCodeChanged();
        partial void OnDesignStatusChanging(string value);
        partial void OnDesignStatusChanged();
        partial void OnSupMchCodeChanging(string value);
        partial void OnSupMchCodeChanged();
        partial void OnObjLevelChanging(string value);
        partial void OnObjLevelChanged();
        partial void OnManufacturerNoChanging(string value);
        partial void OnManufacturerNoChanged();
        partial void OnVendorNoChanging(string value);
        partial void OnVendorNoChanged();
        partial void OnSerialNoChanging(string value);
        partial void OnSerialNoChanged();
        partial void OnTypeChanging(string value);
        partial void OnTypeChanged();
        partial void OnSupContractChanging(string value);
        partial void OnSupContractChanged();
        partial void OnPartNoChanging(string value);
        partial void OnPartNoChanged();
        partial void OnOperationalStatusChanging(SerialOperationalStatus? value);
        partial void OnOperationalStatusChanged();
        partial void OnManufacturedDateChanging(DateTime? value);
        partial void OnManufacturedDateChanged();
        partial void OnObjectNoChanging(string value);
        partial void OnObjectNoChanged();
        partial void OnMchNameChanging(string value);
        partial void OnMchNameChanged();
        partial void OnMchLocChanging(string value);
        partial void OnMchLocChanged();
        partial void OnMchPosChanging(string value);
        partial void OnMchPosChanged();
        partial void OnMchDocChanging(string value);
        partial void OnMchDocChanged();
        partial void OnPurchPriceChanging(double? value);
        partial void OnPurchPriceChanged();
        partial void OnPurchDateChanging(DateTime? value);
        partial void OnPurchDateChanged();
        partial void OnWarrExpChanging(DateTime? value);
        partial void OnWarrExpChanged();
        partial void OnNoteChanging(string value);
        partial void OnNoteChanged();
        partial void OnInfoChanging(string value);
        partial void OnInfoChanged();
        partial void OnDataChanging(string value);
        partial void OnDataChanged();
        partial void OnCompanyChanging(string value);
        partial void OnCompanyChanged();
        partial void OnProductionDateChanging(DateTime? value);
        partial void OnProductionDateChanged();
        partial void OnHasStructureChanging(string value);
        partial void OnHasStructureChanged();
        partial void OnHasConnectionChanging(string value);
        partial void OnHasConnectionChanged();
        partial void OnMchCodeKeyValueChanging(string value);
        partial void OnMchCodeKeyValueChanged();
        partial void OnTypeKeyValueChanging(string value);
        partial void OnTypeKeyValueChanged();
        partial void OnEquipmentMainPositionChanging(string value);
        partial void OnEquipmentMainPositionChanged();
        partial void OnGroupIdChanging(string value);
        partial void OnGroupIdChanged();
        partial void OnMchTypeChanging(string value);
        partial void OnMchTypeChanged();
        partial void OnCostCenterChanging(string value);
        partial void OnCostCenterChanged();
        partial void OnCategoryIdChanging(string value);
        partial void OnCategoryIdChanged();
        partial void OnOwnerChanging(string value);
        partial void OnOwnerChanged();
        partial void OnKeyRefChanging(string value);
        partial void OnKeyRefChanged();
        partial void OnLuNameChanging(string value);
        partial void OnLuNameChanged();
        partial void OnCriticalityChanging(string value);
        partial void OnCriticalityChanged();
        partial void OnObjectTypeChanging(string value);
        partial void OnObjectTypeChanged();
        partial void OnSupMchNameChanging(string value);
        partial void OnSupMchNameChanged();
        partial void OnMchTypeDescChanging(string value);
        partial void OnMchTypeDescChanged();
        partial void OnGroupDescChanging(string value);
        partial void OnGroupDescChanged();
        partial void OnLongitudeChanging(double? value);
        partial void OnLongitudeChanged();
        partial void OnLatitudeChanging(double? value);
        partial void OnLatitudeChanged();

        #endregion

        public EquipmentObjectRow()
        {
            OnCreated();
        }

        [Column(Storage = "_contract", Mandatory = true, MaxLength = 5)]
        public string Contract
        {
            get { return _contract; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (_contract != value)
                {
                    OnContractChanging(value);
                    SetProperty(ref _contract, value, "Contract");
                    OnContractChanged();
                }
            }
        }

        [Column(Storage = "_mchCode", Mandatory = true, MaxLength = 100)]
        public string MchCode
        {
            get { return _mchCode; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (_mchCode != value)
                {
                    OnMchCodeChanging(value);
                    SetProperty(ref _mchCode, value, "MchCode");
                    OnMchCodeChanged();
                }
            }
        }

        [Column(Storage = "_designStatus", Insertable = false, MaxLength = 40)]
        public string DesignStatus
        {
            get { return _designStatus; }
            set
            {
                if (value != null && value.Length > 40) throw new ArgumentOutOfRangeException("value");
                if (_designStatus != value)
                {
                    OnDesignStatusChanging(value);
                    SetProperty(ref _designStatus, value, "DesignStatus");
                    OnDesignStatusChanged();
                }
            }
        }

        [Column(Storage = "_supMchCode", TextFormat = TextFormats.Uppercase, MaxLength = 100)]
        public string SupMchCode
        {
            get { return _supMchCode; }
            set
            {
                if (value != null && value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_supMchCode, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnSupMchCodeChanging(newValue);
                    SetProperty(ref _supMchCode, newValue, "SupMchCode");
                    OnSupMchCodeChanged();
                }
            }
        }

        [Column(Storage = "_objLevel", MaxLength = 30)]
        public string ObjLevel
        {
            get { return _objLevel; }
            set
            {
                if (value != null && value.Length > 30) throw new ArgumentOutOfRangeException("value");
                if (_objLevel != value)
                {
                    OnObjLevelChanging(value);
                    SetProperty(ref _objLevel, value, "ObjLevel");
                    OnObjLevelChanged();
                }
            }
        }

        [Column(Storage = "_manufacturerNo", TextFormat = TextFormats.Uppercase, MaxLength = 20)]
        public string ManufacturerNo
        {
            get { return _manufacturerNo; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_manufacturerNo, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnManufacturerNoChanging(newValue);
                    SetProperty(ref _manufacturerNo, newValue, "ManufacturerNo");
                    OnManufacturerNoChanged();
                }
            }
        }

        [Column(Storage = "_vendorNo", TextFormat = TextFormats.Uppercase, MaxLength = 20)]
        public string VendorNo
        {
            get { return _vendorNo; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_vendorNo, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnVendorNoChanging(newValue);
                    SetProperty(ref _vendorNo, newValue, "VendorNo");
                    OnVendorNoChanged();
                }
            }
        }

        [Column(Storage = "_serialNo", TextFormat = TextFormats.Uppercase, MaxLength = 50)]
        public string SerialNo
        {
            get { return _serialNo; }
            set
            {
                if (value != null && value.Length > 50) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_serialNo, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnSerialNoChanging(newValue);
                    SetProperty(ref _serialNo, newValue, "SerialNo");
                    OnSerialNoChanged();
                }
            }
        }

        [Column(Storage = "_type", TextFormat = TextFormats.Uppercase, MaxLength = 30)]
        public string Type
        {
            get { return _type; }
            set
            {
                if (value != null && value.Length > 30) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_type, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnTypeChanging(newValue);
                    SetProperty(ref _type, newValue, "Type");
                    OnTypeChanged();
                }
            }
        }

        [Column(Storage = "_supContract", TextFormat = TextFormats.Uppercase, MaxLength = 5)]
        public string SupContract
        {
            get { return _supContract; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_supContract, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnSupContractChanging(newValue);
                    SetProperty(ref _supContract, newValue, "SupContract");
                    OnSupContractChanged();
                }
            }
        }

        [Column(Storage = "_partNo", TextFormat = TextFormats.Uppercase, MaxLength = 25)]
        public string PartNo
        {
            get { return _partNo; }
            set
            {
                if (value != null && value.Length > 25) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_partNo, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnPartNoChanging(newValue);
                    SetProperty(ref _partNo, newValue, "PartNo");
                    OnPartNoChanged();
                }
            }
        }

        [Column(Storage = "_operationalStatus")]
        public SerialOperationalStatus? OperationalStatus
        {
            get { return _operationalStatus; }
            set
            {
                if (_operationalStatus != value)
                {
                    OnOperationalStatusChanging(value);
                    SetProperty(ref _operationalStatus, value, "OperationalStatus");
                    OnOperationalStatusChanged();
                }
            }
        }

        [Column(Storage = "_manufacturedDate")]
        public DateTime? ManufacturedDate
        {
            get { return _manufacturedDate; }
            set
            {
                if (_manufacturedDate != value)
                {
                    OnManufacturedDateChanging(value);
                    SetProperty(ref _manufacturedDate, value, "ManufacturedDate");
                    OnManufacturedDateChanged();
                }
            }
        }

        [Column(Storage = "_objectNo", MaxLength = 10)]
        public string ObjectNo
        {
            get { return _objectNo; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_objectNo != value)
                {
                    OnObjectNoChanging(value);
                    SetProperty(ref _objectNo, value, "ObjectNo");
                    OnObjectNoChanged();
                }
            }
        }

        [Column(Storage = "_mchName", MaxLength = 200)]
        public string MchName
        {
            get { return _mchName; }
            set
            {
                if (value != null && value.Length > 200) throw new ArgumentOutOfRangeException("value");
                if (_mchName != value)
                {
                    OnMchNameChanging(value);
                    SetProperty(ref _mchName, value, "MchName");
                    OnMchNameChanged();
                }
            }
        }

        [Column(Storage = "_mchLoc", TextFormat = TextFormats.Uppercase, MaxLength = 10)]
        public string MchLoc
        {
            get { return _mchLoc; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_mchLoc, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnMchLocChanging(newValue);
                    SetProperty(ref _mchLoc, newValue, "MchLoc");
                    OnMchLocChanged();
                }
            }
        }

        [Column(Storage = "_mchPos", TextFormat = TextFormats.Uppercase, MaxLength = 15)]
        public string MchPos
        {
            get { return _mchPos; }
            set
            {
                if (value != null && value.Length > 15) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_mchPos, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnMchPosChanging(newValue);
                    SetProperty(ref _mchPos, newValue, "MchPos");
                    OnMchPosChanged();
                }
            }
        }

        [Column(Storage = "_mchDoc", TextFormat = TextFormats.Uppercase, MaxLength = 20)]
        public string MchDoc
        {
            get { return _mchDoc; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_mchDoc, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnMchDocChanging(newValue);
                    SetProperty(ref _mchDoc, newValue, "MchDoc");
                    OnMchDocChanged();
                }
            }
        }

        [Column(Storage = "_purchPrice")]
        public double? PurchPrice
        {
            get { return _purchPrice; }
            set
            {
                if (_purchPrice != value)
                {
                    OnPurchPriceChanging(value);
                    SetProperty(ref _purchPrice, value, "PurchPrice");
                    OnPurchPriceChanged();
                }
            }
        }

        [Column(Storage = "_purchDate")]
        public DateTime? PurchDate
        {
            get { return _purchDate; }
            set
            {
                if (_purchDate != value)
                {
                    OnPurchDateChanging(value);
                    SetProperty(ref _purchDate, value, "PurchDate");
                    OnPurchDateChanged();
                }
            }
        }

        [Column(Storage = "_warrExp")]
        public DateTime? WarrExp
        {
            get { return _warrExp; }
            set
            {
                if (_warrExp != value)
                {
                    OnWarrExpChanging(value);
                    SetProperty(ref _warrExp, value, "WarrExp");
                    OnWarrExpChanged();
                }
            }
        }

        [Column(Storage = "_note", MaxLength = 2000)]
        public string Note
        {
            get { return _note; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_note != value)
                {
                    OnNoteChanging(value);
                    SetProperty(ref _note, value, "Note");
                    OnNoteChanged();
                }
            }
        }

        [Column(Storage = "_info", MaxLength = 2000)]
        public string Info
        {
            get { return _info; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_info != value)
                {
                    OnInfoChanging(value);
                    SetProperty(ref _info, value, "Info");
                    OnInfoChanged();
                }
            }
        }

        [Column(Storage = "_data", MaxLength = 2000)]
        public string Data
        {
            get { return _data; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_data != value)
                {
                    OnDataChanging(value);
                    SetProperty(ref _data, value, "Data");
                    OnDataChanged();
                }
            }
        }

        [Column(Storage = "_company", TextFormat = TextFormats.Uppercase, MaxLength = 20)]
        public string Company
        {
            get { return _company; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_company, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnCompanyChanging(newValue);
                    SetProperty(ref _company, newValue, "Company");
                    OnCompanyChanged();
                }
            }
        }

        [Column(Storage = "_productionDate")]
        public DateTime? ProductionDate
        {
            get { return _productionDate; }
            set
            {
                if (_productionDate != value)
                {
                    OnProductionDateChanging(value);
                    SetProperty(ref _productionDate, value, "ProductionDate");
                    OnProductionDateChanged();
                }
            }
        }

        [Column(Storage = "_hasStructure")]
        public string HasStructure
        {
            get { return _hasStructure; }
            set
            {
                if (_hasStructure != value)
                {
                    OnHasStructureChanging(value);
                    SetProperty(ref _hasStructure, value, "HasStructure");
                    OnHasStructureChanged();
                }
            }
        }

        [Column(Storage = "_hasConnection")]
        public string HasConnection
        {
            get { return _hasConnection; }
            set
            {
                if (_hasConnection != value)
                {
                    OnHasConnectionChanging(value);
                    SetProperty(ref _hasConnection, value, "HasConnection");
                    OnHasConnectionChanged();
                }
            }
        }

        [Column(Storage = "_mchCodeKeyValue", TextFormat = TextFormats.Uppercase, MaxLength = 101)]
        public string MchCodeKeyValue
        {
            get { return _mchCodeKeyValue; }
            set
            {
                if (value != null && value.Length > 101) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_mchCodeKeyValue, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnMchCodeKeyValueChanging(newValue);
                    SetProperty(ref _mchCodeKeyValue, newValue, "MchCodeKeyValue");
                    OnMchCodeKeyValueChanged();
                }
            }
        }

        [Column(Storage = "_typeKeyValue", TextFormat = TextFormats.Uppercase, MaxLength = 31)]
        public string TypeKeyValue
        {
            get { return _typeKeyValue; }
            set
            {
                if (value != null && value.Length > 31) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_typeKeyValue, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnTypeKeyValueChanging(newValue);
                    SetProperty(ref _typeKeyValue, newValue, "TypeKeyValue");
                    OnTypeKeyValueChanged();
                }
            }
        }

        [Column(Storage = "_equipmentMainPosition")]
        public string EquipmentMainPosition
        {
            get { return _equipmentMainPosition; }
            set
            {
                if (_equipmentMainPosition != value)
                {
                    OnEquipmentMainPositionChanging(value);
                    SetProperty(ref _equipmentMainPosition, value, "EquipmentMainPosition");
                    OnEquipmentMainPositionChanged();
                }
            }
        }

        [Column(Storage = "_groupId", TextFormat = TextFormats.Uppercase, MaxLength = 10)]
        public string GroupId
        {
            get { return _groupId; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_groupId, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnGroupIdChanging(newValue);
                    SetProperty(ref _groupId, newValue, "GroupId");
                    OnGroupIdChanged();
                }
            }
        }

        [Column(Storage = "_mchType", TextFormat = TextFormats.Uppercase, MaxLength = 20)]
        public string MchType
        {
            get { return _mchType; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_mchType, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnMchTypeChanging(newValue);
                    SetProperty(ref _mchType, newValue, "MchType");
                    OnMchTypeChanged();
                }
            }
        }

        [Column(Storage = "_costCenter", MaxLength = 10)]
        public string CostCenter
        {
            get { return _costCenter; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_costCenter != value)
                {
                    OnCostCenterChanging(value);
                    SetProperty(ref _costCenter, value, "CostCenter");
                    OnCostCenterChanged();
                }
            }
        }

        [Column(Storage = "_categoryId", TextFormat = TextFormats.Uppercase, MaxLength = 10)]
        public string CategoryId
        {
            get { return _categoryId; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_categoryId, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnCategoryIdChanging(newValue);
                    SetProperty(ref _categoryId, newValue, "CategoryId");
                    OnCategoryIdChanged();
                }
            }
        }

        [Column(Storage = "_owner", TextFormat = TextFormats.Uppercase, MaxLength = 20)]
        public string Owner
        {
            get { return _owner; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_owner, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnOwnerChanging(newValue);
                    SetProperty(ref _owner, newValue, "Owner");
                    OnOwnerChanged();
                }
            }
        }

        [Column(Storage = "_keyRef", MaxLength = 2000)]
        public string KeyRef
        {
            get { return _keyRef; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_keyRef != value)
                {
                    OnKeyRefChanging(value);
                    SetProperty(ref _keyRef, value, "KeyRef");
                    OnKeyRefChanged();
                }
            }
        }

        [Column(Storage = "_luName", MaxLength = 30)]
        public string LuName
        {
            get { return _luName; }
            set
            {
                if (value != null && value.Length > 30) throw new ArgumentOutOfRangeException("value");
                if (_luName != value)
                {
                    OnLuNameChanging(value);
                    SetProperty(ref _luName, value, "LuName");
                    OnLuNameChanged();
                }
            }
        }

        [Column(Storage = "_criticality", MaxLength = 20)]
        public string Criticality
        {
            get { return _criticality; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_criticality != value)
                {
                    OnCriticalityChanging(value);
                    SetProperty(ref _criticality, value, "Criticality");
                    OnCriticalityChanged();
                }
            }
        }

        [Column(Storage = "_objectType", MaxLength = 30)]
        public string ObjectType
        {
            get { return _objectType; }
            set
            {
                if (value != null && value.Length > 30) throw new ArgumentOutOfRangeException("value");
                if (_objectType != value)
                {
                    OnObjectTypeChanging(value);
                    SetProperty(ref _objectType, value, "ObjectType");
                    OnObjectTypeChanged();
                }
            }
        }

        [Column(Storage = "_supMchName", MaxLength = 2000)]
        public string SupMchName
        {
            get { return _supMchName; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_supMchName != value)
                {
                    OnSupMchNameChanging(value);
                    SetProperty(ref _supMchName, value, "SupMchName");
                    OnSupMchNameChanged();
                }
            }
        }

        [Column(Storage = "_mchTypeDesc", Insertable = false, MaxLength = 100)]
        public string MchTypeDesc
        {
            get { return _mchTypeDesc; }
            set
            {
                if (value != null && value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (_mchTypeDesc != value)
                {
                    OnMchTypeDescChanging(value);
                    SetProperty(ref _mchTypeDesc, value, "MchTypeDesc");
                    OnMchTypeDescChanged();
                }
            }
        }

        [Column(Storage = "_groupDesc", Insertable = false, MaxLength = 25)]
        public string GroupDesc
        {
            get { return _groupDesc; }
            set
            {
                if (value != null && value.Length > 25) throw new ArgumentOutOfRangeException("value");
                if (_groupDesc != value)
                {
                    OnGroupDescChanging(value);
                    SetProperty(ref _groupDesc, value, "GroupDesc");
                    OnGroupDescChanged();
                }
            }
        }

        [Column(Storage = "_longitude", Insertable = false)]
        public double? Longitude
        {
            get { return _longitude; }
            set
            {
                if (_longitude != value)
                {
                    OnLongitudeChanging(value);
                    SetProperty(ref _longitude, value, "Longitude");
                    OnLongitudeChanged();
                }
            }
        }

        [Column(Storage = "_latitude", Insertable = false)]
        public double? Latitude
        {
            get { return _latitude; }
            set
            {
                if (_latitude != value)
                {
                    OnLatitudeChanging(value);
                    SetProperty(ref _latitude, value, "Latitude");
                    OnLatitudeChanged();
                }
            }
        }
    }

    public enum MobmgrRouteActionStatus
    {
        Completed,
        NotCompleted
    }

    [Table]
    [Index(Name = "ix_mobile_route_action", Columns = "WoNo, RoundActionSeq", Unique = true)]
    public partial class MobileRouteActionRow : RowBase
    {
        private string _woNo;
        private double _roundActionSeq;
        private double? _pmNo;
        private double? _pmOrderNo;
        private string _mchCode;
        private string _mchName;
        private string _contract;
        private string _workDescr;
        private string _inspNote;
        private string _location;
        private string _testpDescr;
        private string _actionDescr;
        private string _materials;
        private string _opstatDescr;
        private double? _planMen;
        private double? _planHrs;
        private string _identity;
        private string _actionObjId;
        private string _actionObjVersion;
        private MobmgrRouteActionStatus? _mobmgrRouteActionStatus;
        private string _pmRevision;
        private string _testPointId;
        private string _actionCodeId;
        private string _mchCodeContract;
        private string _belongsToObject;
        private string _currentPosition;
        private string _jobDescription;

        #region Extensibility Method Definitions

        partial void OnCreated();
        partial void OnWoNoChanging(string value);
        partial void OnWoNoChanged();
        partial void OnRoundActionSeqChanging(double value);
        partial void OnRoundActionSeqChanged();
        partial void OnPmNoChanging(double? value);
        partial void OnPmNoChanged();
        partial void OnPmOrderNoChanging(double? value);
        partial void OnPmOrderNoChanged();
        partial void OnMchCodeChanging(string value);
        partial void OnMchCodeChanged();
        partial void OnMchNameChanging(string value);
        partial void OnMchNameChanged();
        partial void OnContractChanging(string value);
        partial void OnContractChanged();
        partial void OnWorkDescrChanging(string value);
        partial void OnWorkDescrChanged();
        partial void OnInspNoteChanging(string value);
        partial void OnInspNoteChanged();
        partial void OnLocationChanging(string value);
        partial void OnLocationChanged();
        partial void OnTestpDescrChanging(string value);
        partial void OnTestpDescrChanged();
        partial void OnActionDescrChanging(string value);
        partial void OnActionDescrChanged();
        partial void OnMaterialsChanging(string value);
        partial void OnMaterialsChanged();
        partial void OnOpstatDescrChanging(string value);
        partial void OnOpstatDescrChanged();
        partial void OnPlanMenChanging(double? value);
        partial void OnPlanMenChanged();
        partial void OnPlanHrsChanging(double? value);
        partial void OnPlanHrsChanged();
        partial void OnIdentityChanging(string value);
        partial void OnIdentityChanged();
        partial void OnActionObjIdChanging(string value);
        partial void OnActionObjIdChanged();
        partial void OnActionObjVersionChanging(string value);
        partial void OnActionObjVersionChanged();
        partial void OnMobmgrRouteActionStatusChanging(MobmgrRouteActionStatus? value);
        partial void OnMobmgrRouteActionStatusChanged();
        partial void OnPmRevisionChanging(string value);
        partial void OnPmRevisionChanged();
        partial void OnTestPointIdChanging(string value);
        partial void OnTestPointIdChanged();
        partial void OnActionCodeIdChanging(string value);
        partial void OnActionCodeIdChanged();
        partial void OnMchCodeContractChanging(string value);
        partial void OnMchCodeContractChanged();
        partial void OnBelongsToObjectChanging(string value);
        partial void OnBelongsToObjectChanged();
        partial void OnCurrentPositionChanging(string value);
        partial void OnCurrentPositionChanged();
        partial void OnJobDescriptionChanging(string value);
        partial void OnJobDescriptionChanged();

        #endregion

        public MobileRouteActionRow()
        {
            OnCreated();
        }

        [Column(Storage = "_woNo", Mandatory = true, MaxLength = 20)]
        public string WoNo
        {
            get { return _woNo; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_woNo != value)
                {
                    OnWoNoChanging(value);
                    SetProperty(ref _woNo, value, "WoNo");
                    OnWoNoChanged();
                }
            }
        }

        [Column(Storage = "_roundActionSeq")]
        public double RoundActionSeq
        {
            get { return _roundActionSeq; }
            set
            {
                if (_roundActionSeq != value)
                {
                    OnRoundActionSeqChanging(value);
                    SetProperty(ref _roundActionSeq, value, "RoundActionSeq");
                    OnRoundActionSeqChanged();
                }
            }
        }

        [Column(Storage = "_pmNo")]
        public double? PmNo
        {
            get { return _pmNo; }
            set
            {
                if (_pmNo != value)
                {
                    OnPmNoChanging(value);
                    SetProperty(ref _pmNo, value, "PmNo");
                    OnPmNoChanged();
                }
            }
        }

        [Column(Storage = "_pmOrderNo")]
        public double? PmOrderNo
        {
            get { return _pmOrderNo; }
            set
            {
                if (_pmOrderNo != value)
                {
                    OnPmOrderNoChanging(value);
                    SetProperty(ref _pmOrderNo, value, "PmOrderNo");
                    OnPmOrderNoChanged();
                }
            }
        }

        [Column(Storage = "_mchCode", MaxLength = 40)]
        public string MchCode
        {
            get { return _mchCode; }
            set
            {
                if (value != null && value.Length > 40) throw new ArgumentOutOfRangeException("value");
                if (_mchCode != value)
                {
                    OnMchCodeChanging(value);
                    SetProperty(ref _mchCode, value, "MchCode");
                    OnMchCodeChanged();
                }
            }
        }

        [Column(Storage = "_mchName", MaxLength = 200)]
        public string MchName
        {
            get { return _mchName; }
            set
            {
                if (value != null && value.Length > 200) throw new ArgumentOutOfRangeException("value");
                if (_mchName != value)
                {
                    OnMchNameChanging(value);
                    SetProperty(ref _mchName, value, "MchName");
                    OnMchNameChanged();
                }
            }
        }

        [Column(Storage = "_contract", MaxLength = 5)]
        public string Contract
        {
            get { return _contract; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (_contract != value)
                {
                    OnContractChanging(value);
                    SetProperty(ref _contract, value, "Contract");
                    OnContractChanged();
                }
            }
        }

        [Column(Storage = "_workDescr", MaxLength = 2000)]
        public string WorkDescr
        {
            get { return _workDescr; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_workDescr != value)
                {
                    OnWorkDescrChanging(value);
                    SetProperty(ref _workDescr, value, "WorkDescr");
                    OnWorkDescrChanged();
                }
            }
        }

        [Column(Storage = "_inspNote", MaxLength = 2000)]
        public string InspNote
        {
            get { return _inspNote; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_inspNote != value)
                {
                    OnInspNoteChanging(value);
                    SetProperty(ref _inspNote, value, "InspNote");
                    OnInspNoteChanged();
                }
            }
        }

        [Column(Storage = "_location", MaxLength = 20)]
        public string Location
        {
            get { return _location; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_location != value)
                {
                    OnLocationChanging(value);
                    SetProperty(ref _location, value, "Location");
                    OnLocationChanged();
                }
            }
        }

        [Column(Storage = "_testpDescr", MaxLength = 60)]
        public string TestpDescr
        {
            get { return _testpDescr; }
            set
            {
                if (value != null && value.Length > 60) throw new ArgumentOutOfRangeException("value");
                if (_testpDescr != value)
                {
                    OnTestpDescrChanging(value);
                    SetProperty(ref _testpDescr, value, "TestpDescr");
                    OnTestpDescrChanged();
                }
            }
        }

        [Column(Storage = "_actionDescr", MaxLength = 40)]
        public string ActionDescr
        {
            get { return _actionDescr; }
            set
            {
                if (value != null && value.Length > 40) throw new ArgumentOutOfRangeException("value");
                if (_actionDescr != value)
                {
                    OnActionDescrChanging(value);
                    SetProperty(ref _actionDescr, value, "ActionDescr");
                    OnActionDescrChanged();
                }
            }
        }

        [Column(Storage = "_materials", MaxLength = 60)]
        public string Materials
        {
            get { return _materials; }
            set
            {
                if (value != null && value.Length > 60) throw new ArgumentOutOfRangeException("value");
                if (_materials != value)
                {
                    OnMaterialsChanging(value);
                    SetProperty(ref _materials, value, "Materials");
                    OnMaterialsChanged();
                }
            }
        }

        [Column(Storage = "_opstatDescr", MaxLength = 40)]
        public string OpstatDescr
        {
            get { return _opstatDescr; }
            set
            {
                if (value != null && value.Length > 40) throw new ArgumentOutOfRangeException("value");
                if (_opstatDescr != value)
                {
                    OnOpstatDescrChanging(value);
                    SetProperty(ref _opstatDescr, value, "OpstatDescr");
                    OnOpstatDescrChanged();
                }
            }
        }

        [Column(Storage = "_planMen")]
        public double? PlanMen
        {
            get { return _planMen; }
            set
            {
                if (_planMen != value)
                {
                    OnPlanMenChanging(value);
                    SetProperty(ref _planMen, value, "PlanMen");
                    OnPlanMenChanged();
                }
            }
        }

        [Column(Storage = "_planHrs")]
        public double? PlanHrs
        {
            get { return _planHrs; }
            set
            {
                if (_planHrs != value)
                {
                    OnPlanHrsChanging(value);
                    SetProperty(ref _planHrs, value, "PlanHrs");
                    OnPlanHrsChanged();
                }
            }
        }

        [Column(Storage = "_identity", MaxLength = 20)]
        public string Identity
        {
            get { return _identity; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_identity != value)
                {
                    OnIdentityChanging(value);
                    SetProperty(ref _identity, value, "Identity");
                    OnIdentityChanged();
                }
            }
        }

        [Column(Storage = "_actionObjId", MaxLength = 2000)]
        public string ActionObjId
        {
            get { return _actionObjId; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_actionObjId != value)
                {
                    OnActionObjIdChanging(value);
                    SetProperty(ref _actionObjId, value, "ActionObjId");
                    OnActionObjIdChanged();
                }
            }
        }

        [Column(Storage = "_actionObjVersion", MaxLength = 2000)]
        public string ActionObjVersion
        {
            get { return _actionObjVersion; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_actionObjVersion != value)
                {
                    OnActionObjVersionChanging(value);
                    SetProperty(ref _actionObjVersion, value, "ActionObjVersion");
                    OnActionObjVersionChanged();
                }
            }
        }

        [Column(Storage = "_mobmgrRouteActionStatus")]
        public MobmgrRouteActionStatus? MobmgrRouteActionStatus
        {
            get { return _mobmgrRouteActionStatus; }
            set
            {
                if (_mobmgrRouteActionStatus != value)
                {
                    OnMobmgrRouteActionStatusChanging(value);
                    SetProperty(ref _mobmgrRouteActionStatus, value, "MobmgrRouteActionStatus");
                    OnMobmgrRouteActionStatusChanged();
                }
            }
        }

        [Column(Storage = "_pmRevision", MaxLength = 6)]
        public string PmRevision
        {
            get { return _pmRevision; }
            set
            {
                if (value != null && value.Length > 6) throw new ArgumentOutOfRangeException("value");
                if (_pmRevision != value)
                {
                    OnPmRevisionChanging(value);
                    SetProperty(ref _pmRevision, value, "PmRevision");
                    OnPmRevisionChanged();
                }
            }
        }

        [Column(Storage = "_testPointId", MaxLength = 6)]
        public string TestPointId
        {
            get { return _testPointId; }
            set
            {
                if (value != null && value.Length > 6) throw new ArgumentOutOfRangeException("value");
                if (_testPointId != value)
                {
                    OnTestPointIdChanging(value);
                    SetProperty(ref _testPointId, value, "TestPointId");
                    OnTestPointIdChanged();
                }
            }
        }

        [Column(Storage = "_actionCodeId", MaxLength = 10)]
        public string ActionCodeId
        {
            get { return _actionCodeId; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_actionCodeId != value)
                {
                    OnActionCodeIdChanging(value);
                    SetProperty(ref _actionCodeId, value, "ActionCodeId");
                    OnActionCodeIdChanged();
                }
            }
        }

        [Column(Storage = "_mchCodeContract", MaxLength = 5)]
        public string MchCodeContract
        {
            get { return _mchCodeContract; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (_mchCodeContract != value)
                {
                    OnMchCodeContractChanging(value);
                    SetProperty(ref _mchCodeContract, value, "MchCodeContract");
                    OnMchCodeContractChanged();
                }
            }
        }

        [Column(Storage = "_belongsToObject", MaxLength = 40)]
        public string BelongsToObject
        {
            get { return _belongsToObject; }
            set
            {
                if (value != null && value.Length > 40) throw new ArgumentOutOfRangeException("value");
                if (_belongsToObject != value)
                {
                    OnBelongsToObjectChanging(value);
                    SetProperty(ref _belongsToObject, value, "BelongsToObject");
                    OnBelongsToObjectChanged();
                }
            }
        }

        [Column(Storage = "_currentPosition", MaxLength = 20)]
        public string CurrentPosition
        {
            get { return _currentPosition; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_currentPosition != value)
                {
                    OnCurrentPositionChanging(value);
                    SetProperty(ref _currentPosition, value, "CurrentPosition");
                    OnCurrentPositionChanged();
                }
            }
        }

        [Column(Storage = "_jobDescription", MaxLength = 2000)]
        public string JobDescription
        {
            get { return _jobDescription; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_jobDescription != value)
                {
                    OnJobDescriptionChanging(value);
                    SetProperty(ref _jobDescription, value, "JobDescription");
                    OnJobDescriptionChanged();
                }
            }
        }
    }

    public enum MobmgrSeparateWoState
    {
        [ServerValue("FAULTREPORT")]
        FaultReport,
        [ServerValue("WORKREQUEST")]
        WorkRequest,
        [ServerValue("OBSERVED")]
        Observed,
        [ServerValue("UNDERPREPARATION")]
        UnderPreparation,
        [ServerValue("STARTED")]
        Started,
        [ServerValue("WORKDONE")]
        WorkDone,
        [ServerValue("REPORTED")]
        Reported,
        [ServerValue("END")]
        End,
        [ServerValue("RELEASED")]
        Released,
        [ServerValue("PREPARED")]
        Prepared,
        [ServerValue("ASSIGNED")]
        Assigned,
        [ServerValue("ACCEPTED")]
        Accepted,
        [ServerValue("ONROUTE")]
        OnRoute
    }

    public enum WorkOrderOnHold
    {
        No,
        [ServerValue("OnHold")]
        OnHold
    }

    public enum MobmgrWoReturnType
    {
        Keep,
        Return,
        Returnnew
    }

    public enum MaintConnectionType
    {
        Cro,
        Category,
        Equipment,
        Pld,
        Vim
    }

    public enum MobmgrWoReturnTravel
    {
        Finish,
        Start
    }

    [Table]
    [Index(Name = "ix_mobile_separate_work_order", Columns = "WoNo", Unique = true)]
    public partial class MobileSeparateWorkOrderRow : RowBase
    {
        private MobmgrSeparateWoState _objstate;
        private string _state;
        private string _woNo;
        private string _errDescr;
        private string _workTypeId;
        private string _customerNo;
        private string _customerName;
        private string _contract;
        private DateTime? _planSDate;
        private string _orgCode;
        private string _priorityId;
        private string _woState;
        private string _identity;
        private string _contact;
        private string _phone;
        private string _classc;
        private string _perfactc;
        private string _typec;
        private string _causec;
        private string _workDone;
        private DateTime? _repDate;
        private DateTime? _actStart;
        private DateTime? _actDone;
        private bool? _rejected;
        private string _agreementId;
        private DateTime? _reqSDate;
        private string _pdaWoNo;
        private string _errDescrLo;
        private string _reportedBy;
        private string _mchCode;
        private string _mchName;
        private string _mchCodeContract;
        private double? _objCustWarranty;
        private double? _objSupWarranty;
        private double? _custWarranty;
        private string _custWarrType;
        private double? _supWarranty;
        private string _supWarrType;
        private string _transferErrorDesc;
        private DateTime? _planFDate;
        private string _pdaWoRef;
        private string _workDescrLo;
        private string _errCauseLo;
        private string _woObjid;
        private string _woObjversion;
        private string _woObjstate;
        private string _woObjevents;
        private string _moState;
        private string _vendorNo;
        private string _company;
        private MobmgrWoReturnType? _mobmgrWoReturnType;
        private string _pmRevision;
        private double? _isSynched;
        private string _errDiscoverCode;
        private string _errSymptom;
        private string _objevents;
        private string _note;
        private string _performedActionLo;
        private DateTime? _requiredEndDate;
        private string _workMasterSignId;
        private double? _pmNo;
        private string _workMasterSign;
        private WorkOrderOnHold? _workOrderOnHold;
        private string _contractId;
        private bool? _suspend;
        private double? _lineNo;
        private bool? _revisit;
        private string _reasonCode;
        private string _reasonNote;
        private byte[] _tecSignature;
        private byte[] _custSignature;
        private string _customerSignedBy;
        private double? _poolWorkOrder;
        private double? _onHold;
        private MaintConnectionType? _connectionType;
        private string _repairFlag;
        private string _nonSerialRepairFlag;
        private string _bookStatusFlag;
        private DateTime? _mobileTransDate;
        private DateTime? _scheduledStartDate;
        private DateTime? _scheduledCompletionDate;
        private DateTime? _earliestStartDate;
        private DateTime? _fixedStartDate;
        private string _jobDescription;
        private bool? _insertInStructure;
        private string _parentWoNo;
        private string _parentPdaWoNo;
        private double? _longitude;
        private double? _latitude;
        private MobmgrWoReturnTravel? _returnTravel;
        private double? _calenderSeqNo;
        private string _opsFollowWoStatus;
        private DateTime? _slaRequiredStartDate;
        private DateTime? _slaRequiredEndDate;

        #region Extensibility Method Definitions

        partial void OnCreated();
        partial void OnObjstateChanging(MobmgrSeparateWoState value);
        partial void OnObjstateChanged();
        partial void OnStateChanging(string value);
        partial void OnStateChanged();
        partial void OnWoNoChanging(string value);
        partial void OnWoNoChanged();
        partial void OnErrDescrChanging(string value);
        partial void OnErrDescrChanged();
        partial void OnWorkTypeIdChanging(string value);
        partial void OnWorkTypeIdChanged();
        partial void OnCustomerNoChanging(string value);
        partial void OnCustomerNoChanged();
        partial void OnCustomerNameChanging(string value);
        partial void OnCustomerNameChanged();
        partial void OnContractChanging(string value);
        partial void OnContractChanged();
        partial void OnPlanSDateChanging(DateTime? value);
        partial void OnPlanSDateChanged();
        partial void OnOrgCodeChanging(string value);
        partial void OnOrgCodeChanged();
        partial void OnPriorityIdChanging(string value);
        partial void OnPriorityIdChanged();
        partial void OnWoStateChanging(string value);
        partial void OnWoStateChanged();
        partial void OnIdentityChanging(string value);
        partial void OnIdentityChanged();
        partial void OnContactChanging(string value);
        partial void OnContactChanged();
        partial void OnPhoneChanging(string value);
        partial void OnPhoneChanged();
        partial void OnClasscChanging(string value);
        partial void OnClasscChanged();
        partial void OnPerfactcChanging(string value);
        partial void OnPerfactcChanged();
        partial void OnTypecChanging(string value);
        partial void OnTypecChanged();
        partial void OnCausecChanging(string value);
        partial void OnCausecChanged();
        partial void OnWorkDoneChanging(string value);
        partial void OnWorkDoneChanged();
        partial void OnRepDateChanging(DateTime? value);
        partial void OnRepDateChanged();
        partial void OnActStartChanging(DateTime? value);
        partial void OnActStartChanged();
        partial void OnActDoneChanging(DateTime? value);
        partial void OnActDoneChanged();
        partial void OnRejectedChanging(bool? value);
        partial void OnRejectedChanged();
        partial void OnAgreementIdChanging(string value);
        partial void OnAgreementIdChanged();
        partial void OnReqSDateChanging(DateTime? value);
        partial void OnReqSDateChanged();
        partial void OnPdaWoNoChanging(string value);
        partial void OnPdaWoNoChanged();
        partial void OnErrDescrLoChanging(string value);
        partial void OnErrDescrLoChanged();
        partial void OnReportedByChanging(string value);
        partial void OnReportedByChanged();
        partial void OnMchCodeChanging(string value);
        partial void OnMchCodeChanged();
        partial void OnMchNameChanging(string value);
        partial void OnMchNameChanged();
        partial void OnMchCodeContractChanging(string value);
        partial void OnMchCodeContractChanged();
        partial void OnObjCustWarrantyChanging(double? value);
        partial void OnObjCustWarrantyChanged();
        partial void OnObjSupWarrantyChanging(double? value);
        partial void OnObjSupWarrantyChanged();
        partial void OnCustWarrantyChanging(double? value);
        partial void OnCustWarrantyChanged();
        partial void OnCustWarrTypeChanging(string value);
        partial void OnCustWarrTypeChanged();
        partial void OnSupWarrantyChanging(double? value);
        partial void OnSupWarrantyChanged();
        partial void OnSupWarrTypeChanging(string value);
        partial void OnSupWarrTypeChanged();
        partial void OnTransferErrorDescChanging(string value);
        partial void OnTransferErrorDescChanged();
        partial void OnPlanFDateChanging(DateTime? value);
        partial void OnPlanFDateChanged();
        partial void OnPdaWoRefChanging(string value);
        partial void OnPdaWoRefChanged();
        partial void OnWorkDescrLoChanging(string value);
        partial void OnWorkDescrLoChanged();
        partial void OnErrCauseLoChanging(string value);
        partial void OnErrCauseLoChanged();
        partial void OnWoObjidChanging(string value);
        partial void OnWoObjidChanged();
        partial void OnWoObjversionChanging(string value);
        partial void OnWoObjversionChanged();
        partial void OnWoObjstateChanging(string value);
        partial void OnWoObjstateChanged();
        partial void OnWoObjeventsChanging(string value);
        partial void OnWoObjeventsChanged();
        partial void OnMoStateChanging(string value);
        partial void OnMoStateChanged();
        partial void OnVendorNoChanging(string value);
        partial void OnVendorNoChanged();
        partial void OnCompanyChanging(string value);
        partial void OnCompanyChanged();
        partial void OnMobmgrWoReturnTypeChanging(MobmgrWoReturnType? value);
        partial void OnMobmgrWoReturnTypeChanged();
        partial void OnPmRevisionChanging(string value);
        partial void OnPmRevisionChanged();
        partial void OnIsSynchedChanging(double? value);
        partial void OnIsSynchedChanged();
        partial void OnErrDiscoverCodeChanging(string value);
        partial void OnErrDiscoverCodeChanged();
        partial void OnErrSymptomChanging(string value);
        partial void OnErrSymptomChanged();
        partial void OnObjeventsChanging(string value);
        partial void OnObjeventsChanged();
        partial void OnNoteChanging(string value);
        partial void OnNoteChanged();
        partial void OnPerformedActionLoChanging(string value);
        partial void OnPerformedActionLoChanged();
        partial void OnRequiredEndDateChanging(DateTime? value);
        partial void OnRequiredEndDateChanged();
        partial void OnWorkMasterSignIdChanging(string value);
        partial void OnWorkMasterSignIdChanged();
        partial void OnPmNoChanging(double? value);
        partial void OnPmNoChanged();
        partial void OnWorkMasterSignChanging(string value);
        partial void OnWorkMasterSignChanged();
        partial void OnWorkOrderOnHoldChanging(WorkOrderOnHold? value);
        partial void OnWorkOrderOnHoldChanged();
        partial void OnContractIdChanging(string value);
        partial void OnContractIdChanged();
        partial void OnSuspendChanging(bool? value);
        partial void OnSuspendChanged();
        partial void OnLineNoChanging(double? value);
        partial void OnLineNoChanged();
        partial void OnRevisitChanging(bool? value);
        partial void OnRevisitChanged();
        partial void OnReasonCodeChanging(string value);
        partial void OnReasonCodeChanged();
        partial void OnReasonNoteChanging(string value);
        partial void OnReasonNoteChanged();
        partial void OnTecSignatureChanging(byte[] value);
        partial void OnTecSignatureChanged();
        partial void OnCustSignatureChanging(byte[] value);
        partial void OnCustSignatureChanged();
        partial void OnCustomerSignedByChanging(string value);
        partial void OnCustomerSignedByChanged();
        partial void OnPoolWorkOrderChanging(double? value);
        partial void OnPoolWorkOrderChanged();
        partial void OnOnHoldChanging(double? value);
        partial void OnOnHoldChanged();
        partial void OnConnectionTypeChanging(MaintConnectionType? value);
        partial void OnConnectionTypeChanged();
        partial void OnRepairFlagChanging(string value);
        partial void OnRepairFlagChanged();
        partial void OnNonSerialRepairFlagChanging(string value);
        partial void OnNonSerialRepairFlagChanged();
        partial void OnBookStatusFlagChanging(string value);
        partial void OnBookStatusFlagChanged();
        partial void OnMobileTransDateChanging(DateTime? value);
        partial void OnMobileTransDateChanged();
        partial void OnScheduledStartDateChanging(DateTime? value);
        partial void OnScheduledStartDateChanged();
        partial void OnScheduledCompletionDateChanging(DateTime? value);
        partial void OnScheduledCompletionDateChanged();
        partial void OnEarliestStartDateChanging(DateTime? value);
        partial void OnEarliestStartDateChanged();
        partial void OnFixedStartDateChanging(DateTime? value);
        partial void OnFixedStartDateChanged();
        partial void OnJobDescriptionChanging(string value);
        partial void OnJobDescriptionChanged();
        partial void OnInsertInStructureChanging(bool? value);
        partial void OnInsertInStructureChanged();
        partial void OnParentWoNoChanging(string value);
        partial void OnParentWoNoChanged();
        partial void OnParentPdaWoNoChanging(string value);
        partial void OnParentPdaWoNoChanged();
        partial void OnLongitudeChanging(double? value);
        partial void OnLongitudeChanged();
        partial void OnLatitudeChanging(double? value);
        partial void OnLatitudeChanged();
        partial void OnReturnTravelChanging(MobmgrWoReturnTravel? value);
        partial void OnReturnTravelChanged();
        partial void OnCalenderSeqNoChanging(double? value);
        partial void OnCalenderSeqNoChanged();
        partial void OnOpsFollowWoStatusChanging(string value);
        partial void OnOpsFollowWoStatusChanged();
        partial void OnSlaRequiredStartDateChanging(DateTime? value);
        partial void OnSlaRequiredStartDateChanged();
        partial void OnSlaRequiredEndDateChanging(DateTime? value);
        partial void OnSlaRequiredEndDateChanged();

        #endregion

        public MobileSeparateWorkOrderRow()
        {
            OnCreated();
        }

        [Column(Storage = "_objstate", MaxLength = 50)]
        public MobmgrSeparateWoState Objstate
        {
            get { return _objstate; }
            set
            {
                if (_objstate != value)
                {
                    OnObjstateChanging(value);
                    SetProperty(ref _objstate, value, "Objstate");
                    OnObjstateChanged();
                }
            }
        }

        [Column(Storage = "_state", MaxLength = 200)]
        public string State
        {
            get { return _state; }
            set
            {
                if (value != null && value.Length > 200) throw new ArgumentOutOfRangeException("value");
                if (_state != value)
                {
                    OnStateChanging(value);
                    SetProperty(ref _state, value, "State");
                    OnStateChanged();
                }
            }
        }

        [Column(Storage = "_woNo", Mandatory = true, MaxLength = 20)]
        public string WoNo
        {
            get { return _woNo; }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
                if (value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_woNo != value)
                {
                    OnWoNoChanging(value);
                    SetProperty(ref _woNo, value, "WoNo");
                    OnWoNoChanged();
                }
            }
        }

        [Column(Storage = "_errDescr", MaxLength = 60)]
        public string ErrDescr
        {
            get { return _errDescr; }
            set
            {
                if (value != null && value.Length > 60) throw new ArgumentOutOfRangeException("value");
                if (_errDescr != value)
                {
                    OnErrDescrChanging(value);
                    SetProperty(ref _errDescr, value, "ErrDescr");
                    OnErrDescrChanged();
                }
            }
        }

        [Column(Storage = "_workTypeId", MaxLength = 20)]
        public string WorkTypeId
        {
            get { return _workTypeId; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_workTypeId != value)
                {
                    OnWorkTypeIdChanging(value);
                    SetProperty(ref _workTypeId, value, "WorkTypeId");
                    OnWorkTypeIdChanged();
                }
            }
        }

        [Column(Storage = "_customerNo", MaxLength = 20)]
        public string CustomerNo
        {
            get { return _customerNo; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_customerNo != value)
                {
                    OnCustomerNoChanging(value);
                    SetProperty(ref _customerNo, value, "CustomerNo");
                    OnCustomerNoChanged();
                }
            }
        }

        [Column(Storage = "_customerName", MaxLength = 200)]
        public string CustomerName
        {
            get { return _customerName; }
            set
            {
                if (value != null && value.Length > 200) throw new ArgumentOutOfRangeException("value");
                if (_customerName != value)
                {
                    OnCustomerNameChanging(value);
                    SetProperty(ref _customerName, value, "CustomerName");
                    OnCustomerNameChanged();
                }
            }
        }

        [Column(Storage = "_contract", MaxLength = 5)]
        public string Contract
        {
            get { return _contract; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (_contract != value)
                {
                    OnContractChanging(value);
                    SetProperty(ref _contract, value, "Contract");
                    OnContractChanged();
                }
            }
        }

        [Column(Storage = "_planSDate")]
        public DateTime? PlanSDate
        {
            get { return _planSDate; }
            set
            {
                if (_planSDate != value)
                {
                    OnPlanSDateChanging(value);
                    SetProperty(ref _planSDate, value, "PlanSDate");
                    OnPlanSDateChanged();
                }
            }
        }

        [Column(Storage = "_orgCode", MaxLength = 8)]
        public string OrgCode
        {
            get { return _orgCode; }
            set
            {
                if (value != null && value.Length > 8) throw new ArgumentOutOfRangeException("value");
                if (_orgCode != value)
                {
                    OnOrgCodeChanging(value);
                    SetProperty(ref _orgCode, value, "OrgCode");
                    OnOrgCodeChanged();
                }
            }
        }

        [Column(Storage = "_priorityId", MaxLength = 20)]
        public string PriorityId
        {
            get { return _priorityId; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_priorityId != value)
                {
                    OnPriorityIdChanging(value);
                    SetProperty(ref _priorityId, value, "PriorityId");
                    OnPriorityIdChanged();
                }
            }
        }

        [Column(Storage = "_woState", MaxLength = 253)]
        public string WoState
        {
            get { return _woState; }
            set
            {
                if (value != null && value.Length > 253) throw new ArgumentOutOfRangeException("value");
                if (_woState != value)
                {
                    OnWoStateChanging(value);
                    SetProperty(ref _woState, value, "WoState");
                    OnWoStateChanged();
                }
            }
        }

        [Column(Storage = "_identity", MaxLength = 20)]
        public string Identity
        {
            get { return _identity; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_identity != value)
                {
                    OnIdentityChanging(value);
                    SetProperty(ref _identity, value, "Identity");
                    OnIdentityChanged();
                }
            }
        }

        [Column(Storage = "_contact", MaxLength = 30)]
        public string Contact
        {
            get { return _contact; }
            set
            {
                if (value != null && value.Length > 30) throw new ArgumentOutOfRangeException("value");
                if (_contact != value)
                {
                    OnContactChanging(value);
                    SetProperty(ref _contact, value, "Contact");
                    OnContactChanged();
                }
            }
        }

        [Column(Storage = "_phone", MaxLength = 20)]
        public string Phone
        {
            get { return _phone; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_phone != value)
                {
                    OnPhoneChanging(value);
                    SetProperty(ref _phone, value, "Phone");
                    OnPhoneChanged();
                }
            }
        }

        [Column(Storage = "_classc", MaxLength = 10)]
        public string Classc
        {
            get { return _classc; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_classc != value)
                {
                    OnClasscChanging(value);
                    SetProperty(ref _classc, value, "Classc");
                    OnClasscChanged();
                }
            }
        }

        [Column(Storage = "_perfactc", MaxLength = 10)]
        public string Perfactc
        {
            get { return _perfactc; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_perfactc != value)
                {
                    OnPerfactcChanging(value);
                    SetProperty(ref _perfactc, value, "Perfactc");
                    OnPerfactcChanged();
                }
            }
        }

        [Column(Storage = "_typec", MaxLength = 10)]
        public string Typec
        {
            get { return _typec; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_typec != value)
                {
                    OnTypecChanging(value);
                    SetProperty(ref _typec, value, "Typec");
                    OnTypecChanged();
                }
            }
        }

        [Column(Storage = "_causec", MaxLength = 10)]
        public string Causec
        {
            get { return _causec; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_causec != value)
                {
                    OnCausecChanging(value);
                    SetProperty(ref _causec, value, "Causec");
                    OnCausecChanged();
                }
            }
        }

        [Column(Storage = "_workDone", MaxLength = 60)]
        public string WorkDone
        {
            get { return _workDone; }
            set
            {
                if (value != null && value.Length > 60) throw new ArgumentOutOfRangeException("value");
                if (_workDone != value)
                {
                    OnWorkDoneChanging(value);
                    SetProperty(ref _workDone, value, "WorkDone");
                    OnWorkDoneChanged();
                }
            }
        }

        [Column(Storage = "_repDate")]
        public DateTime? RepDate
        {
            get { return _repDate; }
            set
            {
                if (_repDate != value)
                {
                    OnRepDateChanging(value);
                    SetProperty(ref _repDate, value, "RepDate");
                    OnRepDateChanged();
                }
            }
        }

        [Column(Storage = "_actStart")]
        public DateTime? ActStart
        {
            get { return _actStart; }
            set
            {
                if (_actStart != value)
                {
                    OnActStartChanging(value);
                    SetProperty(ref _actStart, value, "ActStart");
                    OnActStartChanged();
                }
            }
        }

        [Column(Storage = "_actDone")]
        public DateTime? ActDone
        {
            get { return _actDone; }
            set
            {
                if (_actDone != value)
                {
                    OnActDoneChanging(value);
                    SetProperty(ref _actDone, value, "ActDone");
                    OnActDoneChanged();
                }
            }
        }

        [Column(Storage = "_rejected")]
        public bool? Rejected
        {
            get { return _rejected; }
            set
            {
                if (_rejected != value)
                {
                    OnRejectedChanging(value);
                    SetProperty(ref _rejected, value, "Rejected");
                    OnRejectedChanged();
                }
            }
        }

        [Column(Storage = "_agreementId", MaxLength = 10)]
        public string AgreementId
        {
            get { return _agreementId; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_agreementId != value)
                {
                    OnAgreementIdChanging(value);
                    SetProperty(ref _agreementId, value, "AgreementId");
                    OnAgreementIdChanged();
                }
            }
        }

        [Column(Storage = "_reqSDate")]
        public DateTime? ReqSDate
        {
            get { return _reqSDate; }
            set
            {
                if (_reqSDate != value)
                {
                    OnReqSDateChanging(value);
                    SetProperty(ref _reqSDate, value, "ReqSDate");
                    OnReqSDateChanged();
                }
            }
        }

        [Column(Storage = "_pdaWoNo", MaxLength = 100)]
        public string PdaWoNo
        {
            get { return _pdaWoNo; }
            set
            {
                if (value != null && value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (_pdaWoNo != value)
                {
                    OnPdaWoNoChanging(value);
                    SetProperty(ref _pdaWoNo, value, "PdaWoNo");
                    OnPdaWoNoChanged();
                }
            }
        }

        [Column(Storage = "_errDescrLo", MaxLength = 2000)]
        public string ErrDescrLo
        {
            get { return _errDescrLo; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_errDescrLo != value)
                {
                    OnErrDescrLoChanging(value);
                    SetProperty(ref _errDescrLo, value, "ErrDescrLo");
                    OnErrDescrLoChanged();
                }
            }
        }

        [Column(Storage = "_reportedBy", MaxLength = 20)]
        public string ReportedBy
        {
            get { return _reportedBy; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_reportedBy != value)
                {
                    OnReportedByChanging(value);
                    SetProperty(ref _reportedBy, value, "ReportedBy");
                    OnReportedByChanged();
                }
            }
        }

        [Column(Storage = "_mchCode", MaxLength = 100)]
        public string MchCode
        {
            get { return _mchCode; }
            set
            {
                if (value != null && value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (_mchCode != value)
                {
                    OnMchCodeChanging(value);
                    SetProperty(ref _mchCode, value, "MchCode");
                    OnMchCodeChanged();
                }
            }
        }

        [Column(Storage = "_mchName", MaxLength = 200)]
        public string MchName
        {
            get { return _mchName; }
            set
            {
                if (value != null && value.Length > 200) throw new ArgumentOutOfRangeException("value");
                if (_mchName != value)
                {
                    OnMchNameChanging(value);
                    SetProperty(ref _mchName, value, "MchName");
                    OnMchNameChanged();
                }
            }
        }

        [Column(Storage = "_mchCodeContract", MaxLength = 5)]
        public string MchCodeContract
        {
            get { return _mchCodeContract; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (_mchCodeContract != value)
                {
                    OnMchCodeContractChanging(value);
                    SetProperty(ref _mchCodeContract, value, "MchCodeContract");
                    OnMchCodeContractChanged();
                }
            }
        }

        [Column(Storage = "_objCustWarranty")]
        public double? ObjCustWarranty
        {
            get { return _objCustWarranty; }
            set
            {
                if (_objCustWarranty != value)
                {
                    OnObjCustWarrantyChanging(value);
                    SetProperty(ref _objCustWarranty, value, "ObjCustWarranty");
                    OnObjCustWarrantyChanged();
                }
            }
        }

        [Column(Storage = "_objSupWarranty")]
        public double? ObjSupWarranty
        {
            get { return _objSupWarranty; }
            set
            {
                if (_objSupWarranty != value)
                {
                    OnObjSupWarrantyChanging(value);
                    SetProperty(ref _objSupWarranty, value, "ObjSupWarranty");
                    OnObjSupWarrantyChanged();
                }
            }
        }

        [Column(Storage = "_custWarranty")]
        public double? CustWarranty
        {
            get { return _custWarranty; }
            set
            {
                if (_custWarranty != value)
                {
                    OnCustWarrantyChanging(value);
                    SetProperty(ref _custWarranty, value, "CustWarranty");
                    OnCustWarrantyChanged();
                }
            }
        }

        [Column(Storage = "_custWarrType", MaxLength = 20)]
        public string CustWarrType
        {
            get { return _custWarrType; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_custWarrType != value)
                {
                    OnCustWarrTypeChanging(value);
                    SetProperty(ref _custWarrType, value, "CustWarrType");
                    OnCustWarrTypeChanged();
                }
            }
        }

        [Column(Storage = "_supWarranty")]
        public double? SupWarranty
        {
            get { return _supWarranty; }
            set
            {
                if (_supWarranty != value)
                {
                    OnSupWarrantyChanging(value);
                    SetProperty(ref _supWarranty, value, "SupWarranty");
                    OnSupWarrantyChanged();
                }
            }
        }

        [Column(Storage = "_supWarrType", MaxLength = 20)]
        public string SupWarrType
        {
            get { return _supWarrType; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_supWarrType != value)
                {
                    OnSupWarrTypeChanging(value);
                    SetProperty(ref _supWarrType, value, "SupWarrType");
                    OnSupWarrTypeChanged();
                }
            }
        }

        [Column(Storage = "_transferErrorDesc", MaxLength = 2000)]
        public string TransferErrorDesc
        {
            get { return _transferErrorDesc; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_transferErrorDesc != value)
                {
                    OnTransferErrorDescChanging(value);
                    SetProperty(ref _transferErrorDesc, value, "TransferErrorDesc");
                    OnTransferErrorDescChanged();
                }
            }
        }

        [Column(Storage = "_planFDate")]
        public DateTime? PlanFDate
        {
            get { return _planFDate; }
            set
            {
                if (_planFDate != value)
                {
                    OnPlanFDateChanging(value);
                    SetProperty(ref _planFDate, value, "PlanFDate");
                    OnPlanFDateChanged();
                }
            }
        }

        [Column(Storage = "_pdaWoRef", MaxLength = 8)]
        public string PdaWoRef
        {
            get { return _pdaWoRef; }
            set
            {
                if (value != null && value.Length > 8) throw new ArgumentOutOfRangeException("value");
                if (_pdaWoRef != value)
                {
                    OnPdaWoRefChanging(value);
                    SetProperty(ref _pdaWoRef, value, "PdaWoRef");
                    OnPdaWoRefChanged();
                }
            }
        }

        [Column(Storage = "_workDescrLo", MaxLength = 2000)]
        public string WorkDescrLo
        {
            get { return _workDescrLo; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_workDescrLo != value)
                {
                    OnWorkDescrLoChanging(value);
                    SetProperty(ref _workDescrLo, value, "WorkDescrLo");
                    OnWorkDescrLoChanged();
                }
            }
        }

        [Column(Storage = "_errCauseLo", MaxLength = 2000)]
        public string ErrCauseLo
        {
            get { return _errCauseLo; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_errCauseLo != value)
                {
                    OnErrCauseLoChanging(value);
                    SetProperty(ref _errCauseLo, value, "ErrCauseLo");
                    OnErrCauseLoChanged();
                }
            }
        }

        [Column(Storage = "_woObjid", MaxLength = 2000)]
        public string WoObjid
        {
            get { return _woObjid; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_woObjid != value)
                {
                    OnWoObjidChanging(value);
                    SetProperty(ref _woObjid, value, "WoObjid");
                    OnWoObjidChanged();
                }
            }
        }

        [Column(Storage = "_woObjversion", MaxLength = 2000)]
        public string WoObjversion
        {
            get { return _woObjversion; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_woObjversion != value)
                {
                    OnWoObjversionChanging(value);
                    SetProperty(ref _woObjversion, value, "WoObjversion");
                    OnWoObjversionChanged();
                }
            }
        }

        [Column(Storage = "_woObjstate", MaxLength = 30)]
        public string WoObjstate
        {
            get { return _woObjstate; }
            set
            {
                if (value != null && value.Length > 30) throw new ArgumentOutOfRangeException("value");
                if (_woObjstate != value)
                {
                    OnWoObjstateChanging(value);
                    SetProperty(ref _woObjstate, value, "WoObjstate");
                    OnWoObjstateChanged();
                }
            }
        }

        [Column(Storage = "_woObjevents", MaxLength = 253)]
        public string WoObjevents
        {
            get { return _woObjevents; }
            set
            {
                if (value != null && value.Length > 253) throw new ArgumentOutOfRangeException("value");
                if (_woObjevents != value)
                {
                    OnWoObjeventsChanging(value);
                    SetProperty(ref _woObjevents, value, "WoObjevents");
                    OnWoObjeventsChanged();
                }
            }
        }

        [Column(Storage = "_moState", MaxLength = 50)]
        public string MoState
        {
            get { return _moState; }
            set
            {
                if (value != null && value.Length > 50) throw new ArgumentOutOfRangeException("value");
                if (_moState != value)
                {
                    OnMoStateChanging(value);
                    SetProperty(ref _moState, value, "MoState");
                    OnMoStateChanged();
                }
            }
        }

        [Column(Storage = "_vendorNo", MaxLength = 10)]
        public string VendorNo
        {
            get { return _vendorNo; }
            set
            {
                if (value != null && value.Length > 10) throw new ArgumentOutOfRangeException("value");
                if (_vendorNo != value)
                {
                    OnVendorNoChanging(value);
                    SetProperty(ref _vendorNo, value, "VendorNo");
                    OnVendorNoChanged();
                }
            }
        }

        [Column(Storage = "_company", MaxLength = 20)]
        public string Company
        {
            get { return _company; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_company != value)
                {
                    OnCompanyChanging(value);
                    SetProperty(ref _company, value, "Company");
                    OnCompanyChanged();
                }
            }
        }

        [Column(Storage = "_mobmgrWoReturnType")]
        public MobmgrWoReturnType? MobmgrWoReturnType
        {
            get { return _mobmgrWoReturnType; }
            set
            {
                if (_mobmgrWoReturnType != value)
                {
                    OnMobmgrWoReturnTypeChanging(value);
                    SetProperty(ref _mobmgrWoReturnType, value, "MobmgrWoReturnType");
                    OnMobmgrWoReturnTypeChanged();
                }
            }
        }

        [Column(Storage = "_pmRevision", MaxLength = 24)]
        public string PmRevision
        {
            get { return _pmRevision; }
            set
            {
                if (value != null && value.Length > 24) throw new ArgumentOutOfRangeException("value");
                if (_pmRevision != value)
                {
                    OnPmRevisionChanging(value);
                    SetProperty(ref _pmRevision, value, "PmRevision");
                    OnPmRevisionChanged();
                }
            }
        }

        [Column(Storage = "_isSynched", MaxLength = 5)]
        public double? IsSynched
        {
            get { return _isSynched; }
            set
            {
                if (_isSynched != value)
                {
                    OnIsSynchedChanging(value);
                    SetProperty(ref _isSynched, value, "IsSynched");
                    OnIsSynchedChanged();
                }
            }
        }

        [Column(Storage = "_errDiscoverCode", TextFormat = TextFormats.Uppercase, MaxLength = 3)]
        public string ErrDiscoverCode
        {
            get { return _errDiscoverCode; }
            set
            {
                if (value != null && value.Length > 3) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_errDiscoverCode, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnErrDiscoverCodeChanging(newValue);
                    SetProperty(ref _errDiscoverCode, newValue, "ErrDiscoverCode");
                    OnErrDiscoverCodeChanged();
                }
            }
        }

        [Column(Storage = "_errSymptom", TextFormat = TextFormats.Uppercase, MaxLength = 3)]
        public string ErrSymptom
        {
            get { return _errSymptom; }
            set
            {
                if (value != null && value.Length > 3) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_errSymptom, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnErrSymptomChanging(newValue);
                    SetProperty(ref _errSymptom, newValue, "ErrSymptom");
                    OnErrSymptomChanged();
                }
            }
        }

        [Column(Storage = "_objevents")]
        public string Objevents
        {
            get { return _objevents; }
            set
            {
                if (_objevents != value)
                {
                    OnObjeventsChanging(value);
                    SetProperty(ref _objevents, value, "Objevents");
                    OnObjeventsChanged();
                }
            }
        }

        [Column(Storage = "_note", MaxLength = 2000)]
        public string Note
        {
            get { return _note; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_note != value)
                {
                    OnNoteChanging(value);
                    SetProperty(ref _note, value, "Note");
                    OnNoteChanged();
                }
            }
        }

        [Column(Storage = "_performedActionLo", MaxLength = 2000)]
        public string PerformedActionLo
        {
            get { return _performedActionLo; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_performedActionLo != value)
                {
                    OnPerformedActionLoChanging(value);
                    SetProperty(ref _performedActionLo, value, "PerformedActionLo");
                    OnPerformedActionLoChanged();
                }
            }
        }

        [Column(Storage = "_requiredEndDate")]
        public DateTime? RequiredEndDate
        {
            get { return _requiredEndDate; }
            set
            {
                if (_requiredEndDate != value)
                {
                    OnRequiredEndDateChanging(value);
                    SetProperty(ref _requiredEndDate, value, "RequiredEndDate");
                    OnRequiredEndDateChanged();
                }
            }
        }

        [Column(Storage = "_workMasterSignId", TextFormat = TextFormats.Uppercase, MaxLength = 11)]
        public string WorkMasterSignId
        {
            get { return _workMasterSignId; }
            set
            {
                if (value != null && value.Length > 11) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_workMasterSignId, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnWorkMasterSignIdChanging(newValue);
                    SetProperty(ref _workMasterSignId, newValue, "WorkMasterSignId");
                    OnWorkMasterSignIdChanged();
                }
            }
        }

        [Column(Storage = "_pmNo")]
        public double? PmNo
        {
            get { return _pmNo; }
            set
            {
                if (_pmNo != value)
                {
                    OnPmNoChanging(value);
                    SetProperty(ref _pmNo, value, "PmNo");
                    OnPmNoChanged();
                }
            }
        }

        [Column(Storage = "_workMasterSign", TextFormat = TextFormats.Uppercase, MaxLength = 20)]
        public string WorkMasterSign
        {
            get { return _workMasterSign; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_workMasterSign, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnWorkMasterSignChanging(newValue);
                    SetProperty(ref _workMasterSign, newValue, "WorkMasterSign");
                    OnWorkMasterSignChanged();
                }
            }
        }

        [Column(Storage = "_workOrderOnHold")]
        public WorkOrderOnHold? WorkOrderOnHold
        {
            get { return _workOrderOnHold; }
            set
            {
                if (_workOrderOnHold != value)
                {
                    OnWorkOrderOnHoldChanging(value);
                    SetProperty(ref _workOrderOnHold, value, "WorkOrderOnHold");
                    OnWorkOrderOnHoldChanged();
                }
            }
        }

        [Column(Storage = "_contractId", MaxLength = 15)]
        public string ContractId
        {
            get { return _contractId; }
            set
            {
                if (value != null && value.Length > 15) throw new ArgumentOutOfRangeException("value");
                if (_contractId != value)
                {
                    OnContractIdChanging(value);
                    SetProperty(ref _contractId, value, "ContractId");
                    OnContractIdChanged();
                }
            }
        }

        [Column(Storage = "_suspend")]
        public bool? Suspend
        {
            get { return _suspend; }
            set
            {
                if (_suspend != value)
                {
                    OnSuspendChanging(value);
                    SetProperty(ref _suspend, value, "Suspend");
                    OnSuspendChanged();
                }
            }
        }

        [Column(Storage = "_lineNo")]
        public double? LineNo
        {
            get { return _lineNo; }
            set
            {
                if (_lineNo != value)
                {
                    OnLineNoChanging(value);
                    SetProperty(ref _lineNo, value, "LineNo");
                    OnLineNoChanged();
                }
            }
        }

        [Column(Storage = "_revisit")]
        public bool? Revisit
        {
            get { return _revisit; }
            set
            {
                if (_revisit != value)
                {
                    OnRevisitChanging(value);
                    SetProperty(ref _revisit, value, "Revisit");
                    OnRevisitChanged();
                }
            }
        }

        [Column(Storage = "_reasonCode", MaxLength = 20)]
        public string ReasonCode
        {
            get { return _reasonCode; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_reasonCode != value)
                {
                    OnReasonCodeChanging(value);
                    SetProperty(ref _reasonCode, value, "ReasonCode");
                    OnReasonCodeChanged();
                }
            }
        }

        [Column(Storage = "_reasonNote", MaxLength = 2000)]
        public string ReasonNote
        {
            get { return _reasonNote; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_reasonNote != value)
                {
                    OnReasonNoteChanging(value);
                    SetProperty(ref _reasonNote, value, "ReasonNote");
                    OnReasonNoteChanged();
                }
            }
        }

        [Column(Storage = "_tecSignature")]
        public byte[] TecSignature
        {
            get { return _tecSignature; }
            set
            {
                if (_tecSignature != value)
                {
                    OnTecSignatureChanging(value);
                    SetProperty(ref _tecSignature, value, "TecSignature");
                    OnTecSignatureChanged();
                }
            }
        }

        [Column(Storage = "_custSignature")]
        public byte[] CustSignature
        {
            get { return _custSignature; }
            set
            {
                if (_custSignature != value)
                {
                    OnCustSignatureChanging(value);
                    SetProperty(ref _custSignature, value, "CustSignature");
                    OnCustSignatureChanged();
                }
            }
        }

        [Column(Storage = "_customerSignedBy", MaxLength = 100)]
        public string CustomerSignedBy
        {
            get { return _customerSignedBy; }
            set
            {
                if (value != null && value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (_customerSignedBy != value)
                {
                    OnCustomerSignedByChanging(value);
                    SetProperty(ref _customerSignedBy, value, "CustomerSignedBy");
                    OnCustomerSignedByChanged();
                }
            }
        }

        [Column(Storage = "_poolWorkOrder")]
        public double? PoolWorkOrder
        {
            get { return _poolWorkOrder; }
            set
            {
                if (_poolWorkOrder != value)
                {
                    OnPoolWorkOrderChanging(value);
                    SetProperty(ref _poolWorkOrder, value, "PoolWorkOrder");
                    OnPoolWorkOrderChanged();
                }
            }
        }

        [Column(Storage = "_onHold")]
        public double? OnHold
        {
            get { return _onHold; }
            set
            {
                if (_onHold != value)
                {
                    OnOnHoldChanging(value);
                    SetProperty(ref _onHold, value, "OnHold");
                    OnOnHoldChanged();
                }
            }
        }

        [Column(Storage = "_connectionType")]
        public MaintConnectionType? ConnectionType
        {
            get { return _connectionType; }
            set
            {
                if (_connectionType != value)
                {
                    OnConnectionTypeChanging(value);
                    SetProperty(ref _connectionType, value, "ConnectionType");
                    OnConnectionTypeChanged();
                }
            }
        }

        [Column(Storage = "_repairFlag", MaxLength = 5)]
        public string RepairFlag
        {
            get { return _repairFlag; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (_repairFlag != value)
                {
                    OnRepairFlagChanging(value);
                    SetProperty(ref _repairFlag, value, "RepairFlag");
                    OnRepairFlagChanged();
                }
            }
        }

        [Column(Storage = "_nonSerialRepairFlag", MaxLength = 5)]
        public string NonSerialRepairFlag
        {
            get { return _nonSerialRepairFlag; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (_nonSerialRepairFlag != value)
                {
                    OnNonSerialRepairFlagChanging(value);
                    SetProperty(ref _nonSerialRepairFlag, value, "NonSerialRepairFlag");
                    OnNonSerialRepairFlagChanged();
                }
            }
        }

        [Column(Storage = "_bookStatusFlag", MaxLength = 4000)]
        public string BookStatusFlag
        {
            get { return _bookStatusFlag; }
            set
            {
                if (value != null && value.Length > 4000) throw new ArgumentOutOfRangeException("value");
                if (_bookStatusFlag != value)
                {
                    OnBookStatusFlagChanging(value);
                    SetProperty(ref _bookStatusFlag, value, "BookStatusFlag");
                    OnBookStatusFlagChanged();
                }
            }
        }

        [Column(Storage = "_mobileTransDate")]
        public DateTime? MobileTransDate
        {
            get { return _mobileTransDate; }
            set
            {
                if (_mobileTransDate != value)
                {
                    OnMobileTransDateChanging(value);
                    SetProperty(ref _mobileTransDate, value, "MobileTransDate");
                    OnMobileTransDateChanged();
                }
            }
        }

        [Column(Storage = "_scheduledStartDate")]
        public DateTime? ScheduledStartDate
        {
            get { return _scheduledStartDate; }
            set
            {
                if (_scheduledStartDate != value)
                {
                    OnScheduledStartDateChanging(value);
                    SetProperty(ref _scheduledStartDate, value, "ScheduledStartDate");
                    OnScheduledStartDateChanged();
                }
            }
        }

        [Column(Storage = "_scheduledCompletionDate")]
        public DateTime? ScheduledCompletionDate
        {
            get { return _scheduledCompletionDate; }
            set
            {
                if (_scheduledCompletionDate != value)
                {
                    OnScheduledCompletionDateChanging(value);
                    SetProperty(ref _scheduledCompletionDate, value, "ScheduledCompletionDate");
                    OnScheduledCompletionDateChanged();
                }
            }
        }

        [Column(Storage = "_earliestStartDate")]
        public DateTime? EarliestStartDate
        {
            get { return _earliestStartDate; }
            set
            {
                if (_earliestStartDate != value)
                {
                    OnEarliestStartDateChanging(value);
                    SetProperty(ref _earliestStartDate, value, "EarliestStartDate");
                    OnEarliestStartDateChanged();
                }
            }
        }

        [Column(Storage = "_fixedStartDate")]
        public DateTime? FixedStartDate
        {
            get { return _fixedStartDate; }
            set
            {
                if (_fixedStartDate != value)
                {
                    OnFixedStartDateChanging(value);
                    SetProperty(ref _fixedStartDate, value, "FixedStartDate");
                    OnFixedStartDateChanged();
                }
            }
        }

        [Column(Storage = "_jobDescription", MaxLength = 2000)]
        public string JobDescription
        {
            get { return _jobDescription; }
            set
            {
                if (value != null && value.Length > 2000) throw new ArgumentOutOfRangeException("value");
                if (_jobDescription != value)
                {
                    OnJobDescriptionChanging(value);
                    SetProperty(ref _jobDescription, value, "JobDescription");
                    OnJobDescriptionChanged();
                }
            }
        }

        [Column(Storage = "_insertInStructure")]
        public bool? InsertInStructure
        {
            get { return _insertInStructure; }
            set
            {
                if (_insertInStructure != value)
                {
                    OnInsertInStructureChanging(value);
                    SetProperty(ref _insertInStructure, value, "InsertInStructure");
                    OnInsertInStructureChanged();
                }
            }
        }

        [Column(Storage = "_parentWoNo", MaxLength = 20)]
        public string ParentWoNo
        {
            get { return _parentWoNo; }
            set
            {
                if (value != null && value.Length > 20) throw new ArgumentOutOfRangeException("value");
                if (_parentWoNo != value)
                {
                    OnParentWoNoChanging(value);
                    SetProperty(ref _parentWoNo, value, "ParentWoNo");
                    OnParentWoNoChanged();
                }
            }
        }

        [Column(Storage = "_parentPdaWoNo", MaxLength = 100)]
        public string ParentPdaWoNo
        {
            get { return _parentPdaWoNo; }
            set
            {
                if (value != null && value.Length > 100) throw new ArgumentOutOfRangeException("value");
                if (_parentPdaWoNo != value)
                {
                    OnParentPdaWoNoChanging(value);
                    SetProperty(ref _parentPdaWoNo, value, "ParentPdaWoNo");
                    OnParentPdaWoNoChanged();
                }
            }
        }

        [Column(Storage = "_longitude")]
        public double? Longitude
        {
            get { return _longitude; }
            set
            {
                if (_longitude != value)
                {
                    OnLongitudeChanging(value);
                    SetProperty(ref _longitude, value, "Longitude");
                    OnLongitudeChanged();
                }
            }
        }

        [Column(Storage = "_latitude")]
        public double? Latitude
        {
            get { return _latitude; }
            set
            {
                if (_latitude != value)
                {
                    OnLatitudeChanging(value);
                    SetProperty(ref _latitude, value, "Latitude");
                    OnLatitudeChanged();
                }
            }
        }

        [Column(Storage = "_returnTravel")]
        public MobmgrWoReturnTravel? ReturnTravel
        {
            get { return _returnTravel; }
            set
            {
                if (_returnTravel != value)
                {
                    OnReturnTravelChanging(value);
                    SetProperty(ref _returnTravel, value, "ReturnTravel");
                    OnReturnTravelChanged();
                }
            }
        }

        [Column(Storage = "_calenderSeqNo")]
        public double? CalenderSeqNo
        {
            get { return _calenderSeqNo; }
            set
            {
                if (_calenderSeqNo != value)
                {
                    OnCalenderSeqNoChanging(value);
                    SetProperty(ref _calenderSeqNo, value, "CalenderSeqNo");
                    OnCalenderSeqNoChanged();
                }
            }
        }

        [Column(Storage = "_opsFollowWoStatus", TextFormat = TextFormats.Uppercase, MaxLength = 5)]
        public string OpsFollowWoStatus
        {
            get { return _opsFollowWoStatus; }
            set
            {
                if (value != null && value.Length > 5) throw new ArgumentOutOfRangeException("value");
                if (!string.Equals(_opsFollowWoStatus, value, StringComparison.OrdinalIgnoreCase))
                {
                    string newValue = value != null ? value.ToUpperInvariant() : null;
                    OnOpsFollowWoStatusChanging(newValue);
                    SetProperty(ref _opsFollowWoStatus, newValue, "OpsFollowWoStatus");
                    OnOpsFollowWoStatusChanged();
                }
            }
        }

        [Column(Storage = "_slaRequiredStartDate")]
        public DateTime? SlaRequiredStartDate
        {
            get { return _slaRequiredStartDate; }
            set
            {
                if (_slaRequiredStartDate != value)
                {
                    OnSlaRequiredStartDateChanging(value);
                    SetProperty(ref _slaRequiredStartDate, value, "SlaRequiredStartDate");
                    OnSlaRequiredStartDateChanged();
                }
            }
        }

        [Column(Storage = "_slaRequiredEndDate")]
        public DateTime? SlaRequiredEndDate
        {
            get { return _slaRequiredEndDate; }
            set
            {
                if (_slaRequiredEndDate != value)
                {
                    OnSlaRequiredEndDateChanging(value);
                    SetProperty(ref _slaRequiredEndDate, value, "SlaRequiredEndDate");
                    OnSlaRequiredEndDateChanged();
                }
            }
        }
    }

    public class TestDataContext : DataContextBase
    {
        public TestDataContext(DbInternal db)
            : base(db)
        { }

        public Table<TestRow> Tests { get { return GetTable<TestRow>(); } }
        public Table<TwoColPkRow> TwoColPks { get { return GetTable<TwoColPkRow>(); } }
        public Table<AppsTestRow> AppsTests { get { return GetTable<AppsTestRow>(); } }
        public Table<SurveyMobileDetailsRow> SurveyMobileDetailses { get { return GetTable<SurveyMobileDetailsRow>(); } }
        public Table<SurveyRow> Surveys { get { return GetTable<SurveyRow>(); } }
        public Table<RoamingProfileValue> RoamingProfileValues { get { return GetTable<RoamingProfileValue>(); } }
        public Table<EquipmentCriticalityRow> EquipmentCriticalities { get { return GetTable<EquipmentCriticalityRow>(); } }
        public Table<EquipmentObjectRow> EquipmentObjects { get { return GetTable<EquipmentObjectRow>(); } }
        public Table<MobileRouteActionRow> MobileRouteActions { get { return GetTable<MobileRouteActionRow>(); } }
        public Table<MobileSeparateWorkOrderRow> MobileSeparateWorkOrders { get { return GetTable<MobileSeparateWorkOrderRow>(); } }
    }

    public class TestAppsContext : DataContext
    {
        public TestAppsContext(DbInternal db) 
            : base(db) { }

        public Table<AppsTestRow> AppsTests { get { return GetTable<AppsTestRow>(); } }
    }

    public class PlatformServicesTest : IPlatformServices
    {
        private static readonly PlatformServicesTest _instance = new PlatformServicesTest();

        public static void Initialize()
        {
            PlatformServices.Provider = _instance;
        }

        private PlatformServicesTest()
        {
            DataSourceHelper.Instance = new TestDataSourceHelper();
        }

        public string GetAuthenticationToken(string systemId, string userName, string password, string publicKey, string certificationString)
        {
            X509Certificate2 certificate = new X509Certificate2(Convert.FromBase64String(certificationString));
            string result = null;

            if (certificate != null)
            {
                RSACryptoServiceProvider rsa = (RSACryptoServiceProvider)certificate.PublicKey.Key;
                string encryptedPassword = Convert.ToBase64String(rsa.Encrypt(Encoding.UTF8.GetBytes(password), true));
                result = systemId + "^" + userName + "^" + encryptedPassword;
            }

            return result;
        }

        public string ApplicationId
        {
            get
            {
                return "TestApplicationId";
            }
        }

        public string PackageVersion
        {
            get
            {
                return "1.0.0.0";
            }
        }

        public bool IsNetworkAvailable()
        {
            return true;
        }

        public OperatingSystem OperatingSystem
        {
            get { return OperatingSystem.Windows; }
        }

        public string OperatingSystemVersion
        {
            get { return "8"; }
        }

        public Orientation Orientation
        {
            get
            {
                return Orientation.Portrait;
            }
        }

        public Orientation PhysicalOrientation
        {
            get
            {
                return Orientation.Portrait;
            }
        }

        public DeviceType DeviceType
        {
            get
            {
                return DeviceType.Phone;
            }
        }

        public string Manufacturer => string.Empty;

        public string Model => string.Empty;

        public bool HasCamera => false;

        public string HashPassword(string password)
        {
            SHA512 provider = SHA512.Create();
            byte[] bytes = Encoding.UTF8.GetBytes(password);
            byte[] hash = provider.ComputeHash(bytes);
            return Convert.ToBase64String(hash);
        }

        public bool IsReflectionEmitSupported()
        {
            return false;
        }

        public object GetSystemWebProxy()
        {
            return null;
        }

        public IEnumerable<string> GetAssemblyNames(out Assembly appAssembly)
        {
            Assembly mainAssembly = Assembly.GetEntryAssembly();
            appAssembly = mainAssembly;

            List<string> assemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(x => x != mainAssembly)
                .Select(x => x.GetName().Name)
                .ToList();

            return assemblies;
        }

        private HMAC CreateHasher(HashMethod hashMethod, byte[] secretKey)
        {
            switch (hashMethod)
            {
                case HashMethod.HmacSha256:
                    return new HMACSHA256(secretKey);
                case HashMethod.HmacSha512:
                    return new HMACSHA512(secretKey);
                default:
                    return new HMACSHA1(secretKey);
            }
        }

        public byte[] CreateHash(HashMethod hashMethod, byte[] secretKey, byte[] buffer)
        {
            using (HMAC hasher = CreateHasher(hashMethod, secretKey))
            {
                byte[] hash = hasher.ComputeHash(buffer);
                return hash;
            }
        }

        public int NetworkToHostOrder(int network) => IPAddress.NetworkToHostOrder(network);
        public string GetAuthenticationAccessTokenAndKey(string accessToken, string publicKey, string certificationString, out string key)
        {
            throw new NotImplementedException();
        }

        public string GetStoredSalt()
        {
            return string.Empty;
        }

        public string GetAuthenticationTokenBase64(string systemId, string userName, string password, string publicKey, string certificationString)
        {
            throw new NotImplementedException();
        }

        public bool IsGpsEnabledInDevice()
        {
            return false;
        }

        public double GetFreeDiskSpace(string path)
        {
            return 0;
        }
    }

    public class TestDataSourceHelper : IDataSourceHelper
    {
        private static string _dataDir = string.Empty;

        public TestDataSourceHelper()
        {
        }

        public string GetTemporyDir()
        {
            return Path.GetTempPath();
        }

        public bool FileExists(string value)
        {
            if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");

            string path = GetPath(value);
            FileInfo info = new FileInfo(path);
            return info != null && info.Exists;
        }

        public Task DeleteFileAsync(string value)
        {
            if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");

            string path = GetPath(value);

            return Task.Run(() =>
            {
                try
                {
                    if (File.Exists(path))
                    {
                        File.Delete(path);
                    }
                }
                catch (System.IO.FileNotFoundException)
                {
                }
            });
        }

        public string Normalise(string value)
        {
            if (string.IsNullOrEmpty(value)) return value;
            if (value.IndexOfAny(System.IO.Path.GetInvalidFileNameChars()) >= 0 || System.IO.Path.IsPathRooted(value))
                throw new ArgumentOutOfRangeException("value");

            string path = GetPath(value);
            FileInfo fi = new FileInfo(path);
            return fi.Exists ? fi.Name : value;
        }

        public string GetPath(string value)
        {
            if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("value");
            return Path.Combine(_dataDir, value);
        }
    }
}
