﻿using System;
using System.Collections.Generic;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Observables;

namespace Ifs.Uma.Framework.Data
{
    public class ViewData : ObservableBase, IExpressionValueProvider
    {
        public const string RecordPrefix = "record.";
        public const string ParentPrefix = "parent.";
        private const string LuNameKeyword = "luname";
        private const string KeyRefKeyword = "keyref";

        public PageData PageData { get; }
        public ViewData Parent { get; set; }
        public RecordData Record { get; }
        public CpiCrudActions CrudActions { get; set; }
        public bool CanEdit { get; protected set; } = true;

        public bool CommandsEnabledOnEmpty { get; set; }
        public bool CommandsEnabledOnHasChanges { get; set; }

        private Dictionary<string, RecordRefViewData> _refViewDatas;
        private ContextSubstitutionVariable _contextSubstitutionVariable;

        public ViewData(PageData pageData, RecordData record)
        {
            if (pageData == null) throw new ArgumentNullException(nameof(pageData));

            PageData = pageData;
            Record = record;
            _contextSubstitutionVariable = new ContextSubstitutionVariable();
        }

        public bool TryGetValue(string propertyName, out object value)
        {
            value = null;

            propertyName = StripRecordPrefix(propertyName);

            if (propertyName.StartsWith(ParentPrefix))
            {
                string parentName = propertyName.Substring(ParentPrefix.Length);
                return Parent != null && Parent.TryGetValue(parentName, out value);
            }

            if (propertyName.StartsWith(ViewState.ViewStatePrefix))
            {
                return PageData?.ViewState != null && PageData.ViewState.TryGetValue(propertyName, out value);
            }

            if (propertyName.StartsWith("#"))
            {
                return _contextSubstitutionVariable.GetCsV(propertyName, out value);
            }

            if (propertyName == LuNameKeyword)
            {
                if (Record?.EntityName != null)
                {
                    value = Record.EntityName;
                    return true;
                }
                else
                {
                    return false;
                }
            }

            if (propertyName == KeyRefKeyword)
            {
                ObjPrimaryKey pk = Record?.ToPrimaryKey();
                if (pk != null)
                {
                    value = pk.ToKeyRef();
                    return true;
                }
                else
                {
                    return false;
                }
            }

            return Record != null && Record.TryGetRecordValue(propertyName, out value);
        }

        public static string StripRecordPrefix(string name)
        {
            if (name.StartsWith(RecordPrefix))
            {
                // Param values (${Attrib}) come in the format "AttributeName/VarName" rather than the expression
                // attribute references that come in the format "record.AttributeName/VarName"
                // Strip the "record." so the data is read from the correct location
                return name.Substring(RecordPrefix.Length);
            }

            return name;
        }

        public bool TryCallMethod(string methodName, object[] args, out object result)
        {
            result = null;

            if (methodName.StartsWith(RecordPrefix))
            {
                return Record != null && Record.TryCallExpressionMethod(methodName.Substring(RecordPrefix.Length), args, out result);
            }

            return false;
        }

        internal ViewData GetRefViewData(string refName)
        {
            if (refName == null) throw new ArgumentNullException(nameof(refName));

            if (Record == null)
            {
                return null;
            }

            RecordRefViewData viewData;
            if (_refViewDatas != null && _refViewDatas.TryGetValue(refName, out viewData))
            {
                return viewData;
            }

            if (_refViewDatas == null)
            {
                _refViewDatas = new Dictionary<string, RecordRefViewData>();
            }

            RecordRef recordRef = Record.GetReference(refName);
            viewData = new RecordRefViewData(PageData, this, recordRef);
            _refViewDatas[refName] = viewData;
            return viewData;
        }
    }
}
