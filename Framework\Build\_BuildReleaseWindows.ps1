Write-Output "============ Windows"

$winManifestFile = "$($solutionDir)Ifs.Uma.Startup.Win\Package.appxmanifest"

$winManifest = (Get-Content $winManifestFile)
$winManifest = $winManifest -replace " Version=`"[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+`"", " Version=`"$appVersion`""
$winManifest | Set-Content -NoNewline $winManifestFile

msbuild $solutionFile /m /t:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=x86" /p:RestorePackages=false /p:UapAppxPackageBuildMode=StoreUpload

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "==== Windows - Zip and convert to exe"

$packageDir = "$($solutionDir)Ifs.Uma.Startup.Win\AppPackages\Ifs.Uma.Startup.Win_$($appVersion)_Test\"

Copy-Item "$massToolsDir\Win8AppInstaller\Deliverables\*" $packageDir

# Delete old DebugSymbols from previous builds to save space on the server
if (Test-Path -Path $publishDir) { Get-ChildItem $publishDir -Recurse -Filter "DebugSymbols" | Remove-Item -Force -Recurse }

Rename-Item -Path "$($solutionDir)Ifs.Uma.Startup.Win\AppPackages\Symbols_x86.appxsym" -NewName ($deliverableName + "_" + $appVersion +"_x86.appxsym")
Rename-Item -Path "$($solutionDir)Ifs.Uma.Startup.Win\AppPackages\Symbols_x64.appxsym" -NewName ($deliverableName + "_" + $appVersion +"_x64.appxsym")
Rename-Item -Path "$($solutionDir)Ifs.Uma.Startup.Win\AppPackages\Symbols_ARM.appxsym" -NewName ($deliverableName + "_" + $appVersion +"_ARM.appxsym")

if (!(Test-Path -Path ($deliverablesDir + "DebugSymbols"))) { New-Item -ItemType directory -Path ($deliverablesDir + "DebugSymbols") }

Move-Item "$($solutionDir)Ifs.Uma.Startup.Win\AppPackages\*.appxsym" -Destination ($deliverablesDir + "DebugSymbols")

Push-Location $packageDir 
# Delete any APPXSYM files in the packaging folder in case they still remain there
Get-ChildItem . -Recurse -Include *.appxsym | Remove-Item

# Check if all files are present before creating ZIP and EXE
# Copying might be still in progress so wait a few seconds
if (!(Test-Path -Path ("Ifs.Uma.Startup.Win_" + $appVersion +"_x86_x64_arm.appxbundle")) -or
	!(Test-Path -Path ("Ifs.Uma.Startup.Win_" + $appVersion +"_x86_x64_arm.cer")) -or
	!(Test-Path -Path "w8deployer.exe") -or !(Test-Path -Path "w8deployer.exe.config"))
{
	Start-Sleep -Seconds 60
}

# Fail build if still not copied
if (!(Test-Path -Path ("Ifs.Uma.Startup.Win_" + $appVersion +"_x86_x64_arm.appxbundle")) -or
	!(Test-Path -Path ("Ifs.Uma.Startup.Win_" + $appVersion +"_x86_x64_arm.cer")) -or
	!(Test-Path -Path "w8deployer.exe") -or !(Test-Path -Path "w8deployer.exe.config"))
{
	Write-Output "Could not find all required input files for Windows ZIP/EXE"
	Exit -1
}

c:\tools\makezip\makezip -s . -o "Ifs.Uma.Startup.Win.zip"
if ($LastExitCode -ne 0) { Exit $LastExitCode }
Pop-Location

# Sign Exe
$AccessUser = $env:signusername
$AccessPass = $env:signpassword
$buildDestination = "\\CMBPDE460\tosign$"

net use $buildDestination $AccessPass /USER:$AccessUser

$path = "$buildDestination\$deliverableName"

if (!(test-path -PathType container $path))
{
      New-Item -ItemType directory -Path $buildDestination\$deliverableName
}

$buildDir = "$buildDestination\$deliverableName\"

$winExeFileName = "$($packageDir)Ifs.Uma.Startup.Win.exe"
$signedDeliverableName = ($deliverableName + "_" + $appVersion +"_Signed.exe")

Copy-Item $winExeFileName ($buildDir + $deliverableName + "_" + $appVersion +"_Signed.exe")

add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy

$headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
$headers.Add("Authorization", "Basic cm5kbWFzc3RhYnd3czoxMWFiZTZlZjY3YTIzODA1N2FjOGYxOTdiMGY2M2VjZjFk")
$headers.Add("Cookie", "JSESSIONID.1d086deb=node0zkbsn2b2gjgs1w3xajpr53bor9.node0; JSESSIONID.e1b903e5=node0433u9mrv4t2k13npl51pyy4cd12.node0")
$response = Invoke-WebRequest "https://cmbpde460.corpnet.ifsworld.com/job/Sign_EXE_MSI/buildWithParameters?toolname=$deliverableName" -Method 'POST' -Headers $headers

# Get Build QueueID and Build Number
$response = $response.RawContent
$QueueId = ($response | Select-String -Pattern "Location" | ForEach-Object { ([string]$_).Split("/")[6] } )
Write-Output "Signing Jenkins Build QueueID: $QueueId"

$JenkinServer = ($response | Select-String -Pattern "Location" | ForEach-Object { ([string]$_).Split("/")[3] } )
Start-Sleep -Seconds 10

$response = Invoke-RestMethod "https://$JenkinServer/queue/item/$QueueId/api/json?pretty=true" -Method 'GET' -Headers $headers
$BuildNumber = ($response | ConvertTo-Json | jq '.executable.number') -replace """",""
Write-Output "Signing Job Build Number: $BuildNumber"
$BuildUrI = ($response | ConvertTo-Json | jq '.executable.url') -replace """",""
Write-Output "Signing Build URL: $BuildUrI"

Start-Sleep -Seconds 300

# leaving below variables as it is, since in somecases variables are referenced between scripts 
$source = "https://cmbpde460.corpnet.ifsworld.com/job/Sign_EXE_MSI/$BuildNumber/artifact/$signedDeliverableName"
$destination = "$($packageDir)$signedDeliverableName"

# ---- NEW CODE BLOCK ---- download the signed exe file and copy to deliverable location

# Define the command-line arguments for the Python script
$arguments = @(
    "downloadSignedFile.py"
    "--signusername", "$AccessUser"
    "--signpassword", "$AccessPass"
    "--buildNumber", "$BuildNumber"
    "--signedDeliverableName", "$signedDeliverableName"
    "--packageDir", "$packageDir"
    "--deliverablesDir", "$deliverablesDir"
    "--deliverableName", "$deliverableName"
    "--appVersion", "$appVersion"
)

# Run the Python script and wait for it to finish
& "$Env:LOCALAPPDATA\Programs\Python\Python312\python.exe" @arguments

if ($LastExitCode -eq 0) { 
    Write-Output "Python script executed successfully."
} else {
    Write-Output "Python script failed with exit code: $LastExitCode"
    Exit $LastExitCode 
}
# ---- END OF NEW CODE BLOCK ----

Copy-Item "$($solutionDir)Ifs.Uma.Startup.Win\AppPackages\Ifs.Uma.Startup.Win_$($appVersion)_x86_x64_arm_bundle.appxupload" ($deliverablesDir + $deliverableName + "_" + $appVersion +"_Store.appxupload")

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Write-Output "============ End - Windows"