﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution
{
    [TestFixture]
    public class JsonLogicTests
    {
        private const string TestOfflineProjection = "FndTstOffline";

        [Test]
        public void Basic()
        {
            JObject obj = JObject.Parse("{\"var\":\"test.as\"}");
            var expr = IfsExpression.FromJsonLogic(obj);
        }

        [Test]
        public void VarReplace()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("test.a", 2);
            vp.Add("test.b", 3);

            JObject obj = JObject.Parse("{\"+\": [{\"var\":\"test.a\"}, {\"var\":\"test.b\"}]}");
            var expr = IfsExpression.FromJsonLogic(obj);
            expr = ValueReplacer.Rewrite(expr, vp);
            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            var result = method();

            Assert.AreEqual(5, result);
        }

        [Test]
        public void MultiOp()
        {
            JObject obj = JObject.Parse("{\"/\": [20.0, 5.0, 2.0]}");
            var expr = IfsExpression.FromJsonLogic(obj);
            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            var result = method();

            Assert.AreEqual(2, result);
        }

        [Test]
        [TestCase(false, false, false)]
        [TestCase(true, false, false)]
        [TestCase(false, true, false)]
        [TestCase(true, true, true)]
        [TestCase(null, 0, null)]
        [TestCase(null, false, null)]
        [TestCase(null, 1, null)]
        [TestCase(null, true, null)]
        [TestCase(0, null, 0)]
        [TestCase(false, null, false)]
        [TestCase(1, null, null)]
        [TestCase(true, null, null)]
        [TestCase(2, "hi", "hi")]
        [TestCase(0, "hi", 0)]
        [TestCase(false, "hi", false)]
        public void AndOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("and", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(false, false, false)]
        [TestCase(true, false, true)]
        [TestCase(false, true, true)]
        [TestCase(true, true, true)]
        [TestCase(null, 0, 0)]
        [TestCase(null, false, false)]
        [TestCase(null, 1, 1)]
        [TestCase(null, true, true)]
        [TestCase(0, null, null)]
        [TestCase(false, null, null)]
        [TestCase(1, null, 1)]
        [TestCase(true, null, true)]
        [TestCase(2, "hi", 2)]
        [TestCase(0, "hi", "hi")]
        [TestCase(false, "hi", "hi")]
        public void OrOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("or", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(2, null, false)]
        [TestCase(null, 0, false)]
        [TestCase(0, 0, true)]
        [TestCase(null, null, true)]
        [TestCase(true, 0, false)]
        [TestCase(true, 1, true)]
        [TestCase(false, 0, true)]
        [TestCase(false, 1, false)]
        [TestCase("a", "b", false)]
        [TestCase("a", "a", true)]
        [TestCase("a", 1, false)]
        [TestCase(1, "a", false)]
        [TestCase("1", 1, true)]
        [TestCase(1, "1", true)]
        [TestCase(2.2, "2.2", true)]
        public void EqualsOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("==", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(null, 0, true)]
        [TestCase(null, null, false)]
        [TestCase(2, null, true)]
        [TestCase(2, 2, false)]
        [TestCase(2.0, 2.0, false)]
        [TestCase(2.0, 2, false)]
        [TestCase(true, true, false)]
        [TestCase(true, false, true)]
        [TestCase("dsa", "ss", true)]
        [TestCase("aa", "aa", false)]
        public void NotEqualsOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("!=", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(2, null, true)]
        [TestCase(-2, null, false)]
        [TestCase(2, 2, false)]
        [TestCase(0, null, false)]
        [TestCase(0, 0, false)]
        [TestCase(null, null, false)]
        public void GreaterThanOperator(object value1, object value2, bool expectedResult)
        {
            TestBinaryOperator(">", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(2, null, true)]
        [TestCase(-2, null, false)]
        [TestCase(2, 2, true)]
        [TestCase(0, null, true)]
        [TestCase(0, 0, true)]
        [TestCase(null, null, true)]
        public void GreaterThanOrEqualsOperator(object value1, object value2, bool expectedResult)
        {
            TestBinaryOperator(">=", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(2, null, false)]
        [TestCase(-2, null, true)]
        [TestCase(2, 2, false)]
        [TestCase(0, null, false)]
        [TestCase(0, 0, false)]
        [TestCase(null, null, false)]
        public void LessThanOperator(object value1, object value2, bool expectedResult)
        {
            TestBinaryOperator("<", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(2, null, false)]
        [TestCase(-2, null, true)]
        [TestCase(2, 2, true)]
        [TestCase(0, null, true)]
        [TestCase(0, 0, true)]
        [TestCase(null, null, true)]
        public void LessThanOrEqualsOperator(object value1, object value2, bool expectedResult)
        {
            TestBinaryOperator("<=", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(1, 2, 3)]
        [TestCase(1, 2.0, 3.0)]
        [TestCase("asas", 2, "asas2")]
        [TestCase(2, null, 2)]
        [TestCase(null, null, 0.0)]
        public void AddOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("+", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(1, 2, -1)]
        [TestCase(1, 2.0, -1.0)]
        [TestCase("asas", 2, double.NaN)]
        [TestCase(2, null, 2)]
        [TestCase(null, null, 0.0)]
        public void SubtractOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("-", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(1, 2, 0)]
        [TestCase(1, 2.0, 0.5)]
        [TestCase("asas", 2, double.NaN)]
        [TestCase(2, null, double.PositiveInfinity)]
        [TestCase(null, null, double.NaN)]
        public void DivideOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("/", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(1, 2, 2)]
        [TestCase(1, 2.0, 2.0)]
        [TestCase("asas", 2, double.NaN)]
        [TestCase(2, null, 0)]
        [TestCase(2.0, null, 0.0)]
        [TestCase(null, null, 0.0)]
        public void MultiplyOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("*", value1, value2, expectedResult);
        }

        [Test]
        [TestCase(13, 10, 3)]
        [TestCase(10.2, 2.5, 10.2 % 2.5)]
        [TestCase("asas", 2, double.NaN)]
        [TestCase(2, null, double.NaN)]
        [TestCase(2.0, null, double.NaN)]
        [TestCase(null, null, double.NaN)]
        public void ModOperator(object value1, object value2, object expectedResult)
        {
            TestBinaryOperator("%", value1, value2, expectedResult);
        }

        public void TestBinaryOperator(string op, object value1, object value2, object expectedResult)
        {
            // Logic is the same as ran in UXX which is Javascript rules.
            // Null is treated as 0 in the following situations:
            // - prefix / postfix increment and decrement
            // - the unary + and - operators
            // # the + operator if none of the two arguments is a string
            // # subtraction, multiplication, division and modulo operation
            // # relational operators if not both arguments are strings
            // It is not used by the equality operators

            ValueProvider vp = new ValueProvider();
            vp.Add("value1", value1);
            vp.Add("value2", value2);

            JObject obj = JObject.Parse("{\"" + op + "\": [{\"var\":\"value1\"}, {\"var\":\"value2\"}]}");

            var expr = IfsExpression.FromJsonLogic(obj);
            expr = ValueReplacer.Rewrite(expr, vp);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();

            Assert.AreEqual(expectedResult?.GetType(), result?.GetType());
            Assert.AreEqual(expectedResult, result);
        }

        [Test]
        public void AndBool()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("value1", 5);
            vp.Add("value2", 5);
            vp.Add("value3", false);

            JObject obj = JObject.Parse("{\"and\": [ { \"==\": [ {\"var\":\"value1\"},  {\"var\":\"value2\"} ] }, {\"var\":\"value3\"}]}");

            var expr = IfsExpression.FromJsonLogic(obj);
            expr = ValueReplacer.Rewrite(expr, vp);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();
            
            Assert.AreEqual(false, result);
        }

        [Test]
        [TestCase("!", true, false)]
        public void UnaryOperators(string op, object value, object expectedResult)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("value", value);

            JObject obj = JObject.Parse("{\"" + op + "\": {\"var\":\"value\"}}");

            var expr = IfsExpression.FromJsonLogic(obj);
            expr = ValueReplacer.Rewrite(expr, vp);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();

            Assert.AreEqual(expectedResult?.GetType(), result?.GetType());
            Assert.AreEqual(expectedResult, result);
        }

        [Test]
        public void In()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("input", "ahh");

            JObject obj = JObject.Parse("{\"in\": [\"cat\", [\"dog\",\"cat\", \"house\", {\"var\":\"input\"}] ]}");
            var expr = IfsExpression.FromJsonLogic(obj);
            expr = ValueReplacer.Rewrite(expr, vp);
            expr = InToIfRewriter.Rewrite(expr);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();
            Assert.AreEqual(true, result);
        }

        [Test]
        public void InVar()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("inItem", "ahh");
            vp.Add("input", "ahh");

            JObject obj = JObject.Parse("{\"in\": [{\"var\":\"inItem\"}, [\"dog\",\"cat\", \"house\", {\"var\":\"input\"}] ]}");
            var expr = IfsExpression.FromJsonLogic(obj);
            expr = ValueReplacer.Rewrite(expr, vp);
            expr = InToIfRewriter.Rewrite(expr);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();
            Assert.AreEqual(true, result);
        }

        [Test]
        public void FindAttributes()
        {
            List<string> into = new List<string>();
            
            CpiExpression exp = new CpiExpression();
            exp.JsonLogic = JToken.Parse("{\"+\": [{\"var\":\"record.test.a\"}, {\"var\":\"record.test.b\"}]}");
            AttributeFinder.FindInExpression(into, TestOfflineProjection, exp);
            CollectionAssert.AreEquivalent(into, new[] { "test.a", "test.b" });

            into.Clear();
            exp.JsonLogic = JToken.Parse("{\"in\": [{\"var\":\"record.inItem\"}, [\"dog\",\"cat\", \"house\", {\"var\":\"record.input\"}] ]}");
            AttributeFinder.FindInExpression(into, TestOfflineProjection, exp);
            CollectionAssert.AreEquivalent(into, new[] { "inItem", "input" });

            into.Clear();
            exp.JsonLogic = JToken.Parse("{\"not\": {\"var\":\"record.myVar\"} }");
            AttributeFinder.FindInExpression(into, TestOfflineProjection, exp);
            CollectionAssert.AreEquivalent(into, new[] { "myVar", });

            into.Clear();
            exp.JsonLogic = JToken.Parse("\"test\"");
            AttributeFinder.FindInExpression(into, TestOfflineProjection, exp);
            CollectionAssert.AreEquivalent(into, new string[0]);

            into.Clear();
            exp.JsonLogic = JToken.Parse("{ \">\": [ { \"var\": \"record.CreationDate\" }, { \"method\": [ \"MyMethod\", [ \"Arg1\", { \"var\": \"record.Arg2\" } ] ] } ] }");
            AttributeFinder.FindInExpression(into, TestOfflineProjection, exp);
            CollectionAssert.AreEquivalent(into, new[] { "CreationDate", "Arg2" });
        }

        [Test]
        public void MethodNoArgs()
        {
            ValueProvider vp = new ValueProvider();

            CpiExpression exp = new CpiExpression();
            exp.JsonLogic = JObject.Parse("{ \"method\": [ \"record.isNew\" ] }");

            ExpressionRunner runner = new ExpressionRunner(null);
            Assert.AreEqual("isNew_called", runner.Run(exp, vp));
        }

        [Test]
        public void MethodNotSupported()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("CreationDate", DateTime.Now);

            CpiExpression exp = new CpiExpression();
            exp.JsonLogic = JObject.Parse("{ \">\": [ { \"var\": \"CreationDate\" }, { \"method\": [ \"System.DateTime\" ] } ] }");

            ExpressionRunner runner = new ExpressionRunner(null);
            Assert.Throws<ExpressionException>(() => runner.Run(exp, vp));
        }

        private class ValueProvider : IExpressionValueProvider
        {
            private readonly Dictionary<string, object> _values = new Dictionary<string, object>();

            public void Add(string propertyName, object value)
            {
                _values[propertyName] = value;
            }

            public bool TryGetValue(string propertyName, out object value)
            {
                return _values.TryGetValue(propertyName, out value);
            }

            public bool TryCallMethod(string methodName, object[] args, out object result)
            {
                result = null;

                if (methodName == "record.isNew")
                {
                    result = "isNew_called";
                    return true;
                }

                return false;
            }
        }
    }
}
