﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Location;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Permissions;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Reporting;
using Ifs.Uma.Framework.Services;
using Ifs.Uma.Framework.UI.Assistants;
using Ifs.Uma.Framework.UI.Maps;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Metadata.Navigation;
using Ifs.Uma.Services.Location;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Unity.Interception.Utilities;

namespace Ifs.Uma.Framework.Execution.Commands
{
    public interface ICommandExecutor
    {
        Task<ExecuteResult> ExecuteAsync(string projectionName, ViewData data, CpiCommand command, CommandOptions options = null);
        void GetStates(string projectionName, ViewData viewData, CpiCommand command, bool enabledOnEmpty, out bool isVisible, out bool isEnabled);
        PageBase Page { get; set; }
    }

    public sealed class CommandExecutor : Executor<CommandContext>, ICommandExecutor
    {
        private readonly IResolver _resolver;
        private readonly IMetadata _metadata;
        private readonly IDialogService _dialogService;
        private readonly IToastService _toastService;
        private readonly INavigator _navigator;
        private readonly IDataHandler _dataHandler;
        private readonly IOfflineChecker _offlineChecker;
        private readonly IAppPermissions _appPermissions;
        private readonly IDataTransferService _dataTransferService;
        private readonly IReportingService _reportingService;
        private readonly IInsightsLogger _insightsLogger;
        private readonly IPerfLogger _perfLogger;
        private readonly ILocationService _locationService;
        private readonly ILogger _logger;

        public PageBase Page { get; set; }

        public CommandExecutor(IResolver resolver, IMetadata metadata, IDialogService dialogService, IToastService toastService, INavigator navigator,
            ILogger logger, IPerfLogger perfLogger, IInsightsLogger insightsLogger, IDataHandler dataHandler, IExpressionRunner expressionRunner, IOfflineChecker offlineChecker, IDataTransferService dataTransferService,
            IAppPermissions appPermissions, IReportingService reportingService, ILocationService locationService)
            : base(logger, expressionRunner)
        {
            _resolver = resolver;
            _metadata = metadata;
            _dialogService = dialogService;
            _toastService = toastService;
            _navigator = navigator;
            _dataHandler = dataHandler;
            _offlineChecker = offlineChecker;
            _appPermissions = appPermissions;
            _dataTransferService = dataTransferService;
            _reportingService = reportingService;
            _insightsLogger = insightsLogger;
            _perfLogger = perfLogger;
            _locationService = locationService;
            _logger = logger;
        }

        public void GetStates(string projectionName, ViewData viewData, CpiCommand command, bool enabledOnEmpty, out bool isVisible, out bool isEnabled)
        {
            if (command == null) throw new ArgumentNullException(nameof(command));

            CpiExpression visible = command.OfflineVisible ?? command.Visible;
            CpiExpression enabled = command.OfflineEnabled ?? command.Enabled;

            if (viewData == null || !_appPermissions.IsCommandGranted(projectionName, command))
            {
                isVisible = false;
                isEnabled = false;
            }
            else if (!RecordData.HasLoadedRecord(viewData?.Record) && !enabledOnEmpty)
            {
                isVisible = visible == null || visible.QuickCheck(false);
                isEnabled = false;
            }
            else
            {
                isVisible = ExpressionRunner.RunCheck(visible, viewData, true);
                isEnabled = isVisible && ExpressionRunner.RunCheck(enabled, viewData, true);
            }
        }

        public async Task<ExecuteResult> ExecuteAsync(string projectionName, ViewData data, CpiCommand command, CommandOptions options = null)
        {
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (command == null) throw new ArgumentNullException(nameof(command));
            
            ExecuteResult result = await ExecuteCommandAsync(projectionName, data, command, options);

            if (result.Failed)
            {
                if (result.IsOffline)
                {
                    await _dialogService.Alert(string.Empty, Strings.YouMustBeOnline);
                }
                else if (result.Exception?.Message != null)
                {
                    string title = Strings.Error;
                    await _dialogService.ShowException(result.Exception, showDetails: result.Exception.GetType() != typeof(ProcedureErrorException), title: title);
                }
            }

            if (result.Returning)
            {
                result = new ExecuteResult(result.Value);
            }

            return result;
        }

        private async Task<ExecuteResult> ExecuteCommandAsync(string projectionName, ViewData data, CpiCommand command, CommandOptions options)
        {
            _insightsLogger?.TrackAppFeature("Command-" + projectionName + "." + command.Name);
            Logger.Trace("Command: " + command.Name);

            if (command.Execute != null)
            {
                try
                {
                    using (_perfLogger.Track("Command-", projectionName + "." + command.Name))
                    {
                        if (!_appPermissions.IsCommandGranted(projectionName, command))
                        {
                            throw new CommandException(string.Format(Strings.CommandNoPermission, command.Name));
                        }

                        if (!command.IgnoreOfflineCheck && await _offlineChecker.WillFailAsOffline(projectionName, command))
                        {
                            return ExecuteResult.Offline;
                        }

                        Dictionary<string, VariableStorage> commandVars = new Dictionary<string, VariableStorage>();
                        if (command.Vars != null)
                        {
                            foreach (KeyValuePair<string, CpiTypeInfo> var in command.Vars)
                            {
                                commandVars[var.Key] = new VariableStorage(_metadata, var.Key, var.Value);
                            }
                        }
                        
                        CommandContext context = new CommandContext(projectionName, _metadata, ExpressionRunner, data, command.Name, commandVars, options);

                        IReadOnlyDictionary<string, object> vars = options?.Vars;
                        if (vars != null)
                        {
                            foreach (KeyValuePair<string, object> kvp in vars)
                            {
                                context.Assign(kvp.Key, kvp.Value);
                            }
                        }

                        if (data.Record != null)
                        {
                            await data.Record.BackgroundTasks.WaitForCompletion();
                        }

                        return await ExecuteAsync(context, command.Execute);
                    }
                }
                catch (Exception ex)
                {
                    if (!ExecuteResult.IsUserError(ex))
                    {
                        Logger.HandleException(ExceptionType.Unexpected, ex);
                    }

                    return new ExecuteResult(ex);
                }
            }

            return ExecuteResult.None;
        }

        internal async Task<ExecuteResult> ExecuteAsync(CommandContext context, CpiExecute[] executions)
        {
            ExecuteResult ret = ExecuteResult.None;

            foreach (CpiExecute execute in executions)
            {
                ret = await ExecuteAsync(context, execute);

                if (ret.Returning || ret.IsOffline)
                {
                    return ret;
                }
            }

            return ret;
        }

        internal async Task<ExecuteResult> ExecuteAsync(CommandContext context, CpiExecute execute)
        {
            ExecuteResult ret = await ExecuteCallAsync(context, execute.Call, execute.Assign);

            if (ret.IsOffline || ret.Returning)
            {
                return ret;
            }

            if (execute.Result != null && ret.Value is string)
            {
                string resultCode = (string)ret.Value;

                CpiExecute[] resultExecutions;
                if (execute.Result.TryGetValue(resultCode, out resultExecutions) && resultExecutions != null)
                {
                    ExecuteResult subRet = await ExecuteAsync(context, resultExecutions);

                    if (subRet.Returning || subRet.IsOffline)
                    {
                        return subRet;
                    }
                }
            }

            return ret;
        }

        private async Task<ExecuteResult> ExecuteCallAsync(CommandContext context, CpiExecuteCall call, string assign)
        {
            ExecuteResult ret = ExecuteResult.None;

            if (call != null)
            {
                ret = await ExecuteMethodAsync(context, call.Method, call.Args);
            }

            if (ret.IsOffline)
            {
                return ret;
            }

            if (!string.IsNullOrEmpty(assign))
            {
                context.Assign(assign, ret.Value);
            }

            return ret;
        }

        private Task<ExecuteResult> ExecuteMethodAsync(CommandContext context, CpiExecuteCallMethod method, CpiExecuteCallArgs args)
        {
            switch (method)
            {
                case CpiExecuteCallMethod.Assistant:
                    return ExecuteAssistant(context, (CpiAssistantCallArgs)args);
                case CpiExecuteCallMethod.Dialog:
                    return ExecuteDialog(context, (CpiDialogCallArgs)args);
                case CpiExecuteCallMethod.Action:
                    return ExecuteAction(context, (CpiActionCallArgs)args);
                case CpiExecuteCallMethod.Function:
                    return ExecuteFunction(context, (CpiFunctionCallArgs)args);
                case CpiExecuteCallMethod.Navigate:
                    return ExecuteNavigate(context, (CpiNavigateCallArgs)args);
                case CpiExecuteCallMethod.BulkNavigate:
                    return ExecuteNavigate(context, (CpiNavigateCallArgs)args);
                case CpiExecuteCallMethod.Toast:
                    return Task.FromResult(ExecuteToast(context, (CpiToastCallArgs)args));
                case CpiExecuteCallMethod.Confirm:
                    return ExecuteConfirm(context, (CpiConfirmCallArgs)args);
                case CpiExecuteCallMethod.Inquire:
                    return ExecuteInquire(context, (CpiInquireCallArgs)args);
                case CpiExecuteCallMethod.Alert:
                    return ExecuteAlert(context, (CpiAlertCallArgs)args);
                case CpiExecuteCallMethod.Sound:
                    return ExecuteSound((CpiSoundCallArgs)args);
                case CpiExecuteCallMethod.Transfer:
                    return ExecuteTransfer(context, (CpiTransferCallArgs)args);
                case CpiExecuteCallMethod.Receive:
                    return ExecuteReceive(context, (CpiTransferCallArgs)args);
                case CpiExecuteCallMethod.Print:
                    return ExecutePrint(context, (CpiPrintCallArgs)args);
                case CpiExecuteCallMethod.Upload:
                    return ExecuteUpload(context, (CpiUploadCallArgs)args);
                case CpiExecuteCallMethod.Barcode:
                    return ExecuteBarcode(context, (CpiBarcodeCallArgs)args);
                case CpiExecuteCallMethod.Location:
                    return ExecuteLocation(context, (CpiLocationCallArgs)args);
                case CpiExecuteCallMethod.Copy:
                    return Task.FromResult(ExecuteCopy(context, (CpiCopyCallArgs)args));
                case CpiExecuteCallMethod.Refresh:
                    return ExecuteRefresh(context);
#if SIGNATURE_SERVICE
                case CpiExecuteCallMethod.Sign:
                    return ExecuteSign(context, (CpiSignCallArgs)args);
#endif
                default:
                    return Task.FromResult(ExecuteCommonCall(context, method, args));
            }
        }

        private async Task<ExecuteResult> ExecuteRefresh(CommandContext context)
        {
            context?.Data?.PageData?.Page?.OnNavigatedTo(new NavigatedToArgs(NavigationMode.Refresh, null));
            PageData pageData1 = context?.Data?.PageData;
            if (pageData1 != null)
            {
                await pageData1.WaitForBackgroundTasks();
            }
            return ExecuteResult.None;
        }

        private Task<ExecuteResult> ExecuteDialog(CommandContext context, CpiDialogCallArgs args)
        {
            ExecuteDialog exec = _resolver.Resolve<ExecuteDialog>();
            return exec.ExecuteAsync(context, args);
        }

        private Task<ExecuteResult> ExecuteAssistant(CommandContext context, CpiAssistantCallArgs args)
        {
            ExecuteAssistant exec = _resolver.Resolve<ExecuteAssistant>();
            return exec.ExecuteAsync(context, args);
        }

        private async Task<ExecuteResult> ExecuteAction(CommandContext context, CpiActionCallArgs args)
        {
            if (!context.Options.ActionsAllowed)
            {
                throw Fail(context, $"Action '{args.Name}' cannot be executed here");
            }

            Dictionary<string, object> parameters = new Dictionary<string, object>();

            if (args.Params != null)
            {
                foreach (KeyValuePair<string, string> kvp in args.Params)
                {
                    parameters[kvp.Key] = context.ReadParamValue(kvp.Value);
                }
            }

            ExecuteResult result;
            if (args.Bound)
            {
                string actionName = GetBoundActionName(context, args.Name);
                if (string.IsNullOrEmpty(context.Data.Record.GetRemoteRow().EntitySetName) && !string.IsNullOrEmpty(context.Data.PageData?.DataSource?.EntitySetName))
                {
                    context.Data.Record.GetRemoteRow().EntitySetName = context.Data.PageData.DataSource.EntitySetName;
                }

                result = await _dataHandler.PerformBoundActionAsync(args.Projection ?? context.ProjectionName, context.Data.Record.GetRemoteRow(), actionName, parameters);
            }
            else
            {
                result = await _dataHandler.PerformActionAsync(args.Projection ?? context.ProjectionName, args.Name, parameters);
            }

            if (result.IsOffline)
            {
                return result;
            }

            result.CheckFailure();
            return result;
        }

        private static string GetBoundActionName(CommandContext context, string name)
        {
            string[] nameParts = name?.Split('.');

            if (nameParts == null || nameParts.Length < 2)
            {
                throw Fail(context, $"Execute Call: Invalid bound call name '{name}'");
            }

            if (!RecordData.HasLoadedRecord(context.Data.Record))
            {
                throw Fail(context, $"Execute Call: Record must not be null when calling bound call '{name}'");
            }

            string entityName = nameParts[nameParts.Length - 2];
            string recordEntityName = context.Data.Record.EntityName;

            if (recordEntityName != entityName && !context.Metadata.IsDerivedEntity(context.ProjectionName, recordEntityName, entityName))
            {
                throw Fail(context, $"Execute Call: Bound call '{name}' - Record entity '{recordEntityName}' should match call entity '{entityName}' or derive from it");
            }

            return nameParts[nameParts.Length - 1];
        }

        private async Task<ExecuteResult> ExecuteFunction(CommandContext context, CpiFunctionCallArgs args)
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();

            if (args.Params != null)
            {
                foreach (KeyValuePair<string, string> kvp in args.Params)
                {
                    parameters[kvp.Key] = context.ReadParamValue(kvp.Value);
                }
            }

            ExecuteResult result;
            if (args.Bound)
            {
                string functionName = GetBoundFunctionName(context, args.OfflineName);
                result = await _dataHandler.PerformBoundFunctionAsync(args.Projection ?? context.ProjectionName, context.Data.Record.GetRemoteRow(), functionName, parameters);
            }
            else
            {
                result = await _dataHandler.PerformFunctionAsync(args.Projection ?? context.ProjectionName, args.OfflineName, parameters);
            }

            if (result.IsOffline)
            {
                return result;
            }

            result.CheckFailure();
            return result;
        }

        private static string GetBoundFunctionName(CommandContext context, string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                throw Fail(context, $"Execute Call: Invalid bound call name '{name}'");
            }

            if (!RecordData.HasLoadedRecord(context.Data.Record))
            {
                throw Fail(context, $"Execute Call: Record must not be null when calling bound call '{name}'");
            }

            return name;
        }

        private async Task<ExecuteResult> ExecuteNavigate(CommandContext context, CpiNavigateCallArgs args)
        {
            string projectionName = args.Client ?? args.Projection ?? context.ProjectionName; // Assuming 'client' and 'projection' would be the same in the metadata

            if ((args.NavigationMode == CpiNavigationMode.Back || args.NavigationMode == CpiNavigationMode.BackRetainFullscreen) && !string.IsNullOrWhiteSpace(args.PageName))
            {
                NavigationMode navigationMode = args.NavigationMode == CpiNavigationMode.Back
                                                ? NavigationMode.Back
                                                : NavigationMode.BackRetainFullscreen;

                await _navigator.NavigateBackToPageAsync(args.PageName, navigationMode);
            }
            else if (args.PageName != null && args.PageName != args.Url)
            {
                bool found = false;
                PageValues filters = PageValues.FromColumnValues(args.Columns, context);
                MetadataPageNavParam navParam = new MetadataPageNavParam(projectionName, args.PageName, filters);

                foreach (MenuItemData itemData in _metadata.NavigationData.MenuItems)
                {
                    if (navParam.Page == itemData.Target)
                    {
                        await _navigator.NavigateFromRootAsync(FrameworkLocations.MetadataPage, navParam);
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    await _navigator.NavigateToAsync(FrameworkLocations.MetadataPage, navParam);
                }
            }
            else if (args.Page != null)
            {
                PageValues filters = PageValues.FromNavArgs(args.Filters, context);
                MetadataPageNavParam navParam = new MetadataPageNavParam(projectionName, args.Page, filters);
                await _navigator.NavigateToAsync(FrameworkLocations.MetadataPage, navParam);
            }
            else if (args.Assistant != null)
            {
                PageValues actionParams = PageValues.FromNavArgs(args.Parameters, context);
                MetadataPageNavParam navParam = new MetadataPageNavParam(projectionName, args.Assistant, args.Action, actionParams);
                await _navigator.NavigateToAsync(FrameworkLocations.MetadataPage, navParam);
            }
            else if (args.Tree != null)
            {
                bool openSelector = ExpressionRunner.RunCheck(args.OpenSelector, context.Data, false);
                PageValues filters = PageValues.FromNavArgs(args.Filters, context);
                MetadataPageNavParam navParam = new MetadataPageNavParam(projectionName, args.Tree, openSelector, filters);
                await _navigator.NavigateToAsync(FrameworkLocations.MetadataPage, navParam);
            }
            else if (args.Workflow != null)
            {
                CpiWorkflowStep step = null;
                CpiWorkflow workflow = _metadata.FindWorkflow(projectionName, args.Workflow);
                UI.Workflow.Workflow wf = new UI.Workflow.Workflow(_dataHandler, context?.Data, projectionName, workflow);
                List<KeyValuePair<int, CpiWorkflowStep>> steps = await wf.GetAvailableWorkflowSteps();

                int sequence = 0;
                if (args.WorkflowSequence != null && int.TryParse(context?.InterpolateString(args.WorkflowSequence, false), out sequence))
                {
                    step = steps.FirstOrDefault(x => x.Key == sequence).Value;
                }
                else if (args.WorkflowStep != null)
                {
                    KeyValuePair<int, CpiWorkflowStep> stepKvp = steps.FirstOrDefault(x => x.Value.Name == args.WorkflowStep);
                    sequence = stepKvp.Key;
                    step = stepKvp.Value;
                }

                if (step != null)
                {
                    PageValues filter = PageValues.FromPrimaryKey(context?.Data?.Record?.ToPrimaryKey());
                    MetadataPageNavParam navParam = MetadataPageNavParam.FromWorkflow(projectionName, args.Workflow, sequence, filter);
                    await _navigator.NavigateToAsync(FrameworkLocations.MetadataPage, navParam);
                }
                else
                {
                    if (args.WorkflowStep != null)
                    {
                        throw new InvalidOperationException($"The workflow step {args.WorkflowStep} could not be found in workflow {args.Workflow}");
                    }
                    else if (args.WorkflowSequence != null)
                    {
                        throw new InvalidOperationException($"The workflow step with sequence {args.WorkflowSequence} could not be found in workflow {args.Workflow}");
                    }
                }
            }
            else if (args.Url != null)
            {
                if (args.Url == "back" || args.NavigationMode == CpiNavigationMode.Back)
                {
                    await _navigator.NavigateBackAsync();
                }
                else if (args.Url.StartsWith(MapNavParam.MapsUrlPrefix))
                {
                    string url = context?.InterpolateString(args.Url, false);
                    MapNavParam navParam = MapNavParam.Create(url);
                    if (navParam != null)
                    {
                        await _navigator.NavigateToAsync(FrameworkLocations.Maps, navParam);
                    }
                }
                else
                {
                    string interpolatedUrl = context?.InterpolateString(args.Url, false);
                    string authRedirectUri = GetAuthenticationRedirectUri();
                    string baseUri = authRedirectUri?.Replace("auth", string.Empty).TrimEnd('/');

                    // Check if the URL still contains placeholders to see if it needs interpolation again
                    Regex placeholderRegex = new Regex(@"\${[^\s]+?}", RegexOptions.None, TimeSpan.FromSeconds(10));
                    if (placeholderRegex.IsMatch(interpolatedUrl))
                    {
                        interpolatedUrl = context?.InterpolateString(interpolatedUrl, false);
                    }

                    if (interpolatedUrl.StartsWith("https://formsproapp.") || interpolatedUrl.StartsWith("https://ifsadvancedforms."))
                    {
                        string callbackUrl = $"{baseUri}//{context.ProjectionName}/{context.Data.PageData.Page.Name}";
                        Dictionary<string, object> filterValues = context.Data.PageData.Filter.ToDictionary();
                        if (filterValues.Count > 0)
                        {
                            callbackUrl += "?" +
                                           filterValues.JoinStrings("&",
                                               pair => pair.Key + "=" + pair.Value.ToString());
                        }
                        interpolatedUrl += "&CallBackURL=" + Uri.EscapeDataString(callbackUrl);
                    }

                    _logger?.Information("Invoking external integration {0}", interpolatedUrl);
                    await _navigator.NavigateToUrlAsync(interpolatedUrl);
                }
            }

            return ExecuteResult.None;
        }

        private ExecuteResult ExecuteToast(CommandContext context, CpiToastCallArgs args)
        {
            Enum.TryParse(args.Type, true, out ToastType type);

            string message = context.InterpolateString(args.Message, true);
            _toastService.Show(type, message);
            return ExecuteResult.None;
        }

        private async Task<ExecuteResult> ExecuteConfirm(CommandContext context, CpiConfirmCallArgs args)
        {
            string message = context?.InterpolateString(args.Message, true);
            bool result = await _dialogService.Confirm(null, message, Strings.Ok, ConfirmationType.Normal);
            return result ? ExecuteResult.Ok : ExecuteResult.Cancel;
        }

        private async Task<ExecuteResult> ExecuteInquire(CommandContext context, CpiInquireCallArgs args)
        {
            string message = context?.InterpolateString(args.Message, true);
            CustomButtonsResult result = await _dialogService.CustomButtons(null, message, Strings.Yes, Strings.No, Strings.Cancel);
            switch (result)
            {
                case CustomButtonsResult.Positive:
                    return ExecuteResult.Yes;
                case CustomButtonsResult.Negative:
                    return ExecuteResult.No;
                case CustomButtonsResult.Neutral:
                default:
                    return ExecuteResult.Cancel;
            }
        }

        private async Task<ExecuteResult> ExecuteAlert(CommandContext context, CpiAlertCallArgs args)
        {
            string message = context?.InterpolateString(args.Message, true);
            await _dialogService.Alert(null, message);
            return ExecuteResult.None;
        }

#if SIGNATURE_SERVICE
#pragma warning disable
        private async Task<ExecuteResult> ExecuteSign(CommandContext context, CpiSignCallArgs args)
        {
            ITouchApp touchApp = Resolver.Resolve<ITouchApp>();
            TouchAppAccount account = touchApp?.Accounts.FirstOrDefault();
            RemoteRow row = null;
            IMetaDataMember status = null;
            IMetaDataMember guid = null;
            string result = "FAIL";
            bool online = false;

            // Return a responseStructure from 23R2
            if (!string.IsNullOrEmpty(account?.ServerVersion) && PlatformServices.GetServerVersionAsDouble(account?.ServerVersion) > 23.1)
            {
                const string responseStructure = "EsigResponseStructure";
                string projectionName = _metadata.GetFirstProjectionWithStructure(responseStructure);

                IMetaTable metaTable = _metadata.GetTableForEntityName(responseStructure);
                if (metaTable == null)
                    throw Fail(context, $"Failed to prepare response structure '{responseStructure}' when signing.");
                row = (RemoteRow)metaTable.CreateRow();

                List<KeyValuePair<string, CpiAttribute>> fields = _metadata.FindStructure(projectionName, responseStructure)?.Attributes?.ToList();

                foreach (string key in fields.Select(field => field.Key))
                {
                    if (key.Equals("guid", StringComparison.OrdinalIgnoreCase))
                    {
                        guid = metaTable?.FindMemberByPropertyName(key);
                    }
                    else
                    {
                        status = metaTable?.FindMemberByPropertyName(key);
                    }
                }

                row.SetMemberValue(guid, guid.ConvertValue(string.Empty));
                row.SetMemberValue(status, status.ConvertValue("FAIL"));
            }

            if (context.Data.PageData.Page is AssistantPage assistantPage)
            {
                string logoPath = string.Empty;
                string logoRef = string.Empty;

                IReportingService reportingService = Resolver.Resolve<IReportingService>();

                if (args.HeaderLogo != null && context.TryGetValue(args.HeaderLogo, out object logoRefValue) && logoRefValue != null)
                {
                    logoRef = logoRefValue.ToString();
                    IMediaHandler mediaHandler = Resolver.Resolve<IMediaHandler>();
                    ILocalFileInfo file = await mediaHandler.GetLocalFileForLogoAsync("reportLogo.png");
                    
                    if (!string.IsNullOrEmpty(logoRef) && logoRef.Contains(":") && logoRef.Contains("^"))
                    {
                        string[] mediaLibraryRef = logoRef.Split(':');
                        MediaInfo media = null;

                        if (mediaLibraryRef.Length == 2)
                        {
                            IEnumerable<MediaInfo> rows = await mediaHandler.GetMediaAsync(mediaLibraryRef[0], mediaLibraryRef[1]);

                            foreach (MediaInfo mediaItemRow in rows)
                            {
                                if ((bool)mediaItemRow.MediaItem.DefaultMedia)
                                {
                                    media = mediaItemRow;
                                    break;
                                }
                            }
                        }

                        if (media != null)
                        {
                            ILocalFileInfo libraryFile = await mediaHandler.GetLocalFileForMediaAsync(media.MediaItem);
                            logoPath = libraryFile.FullFilePath;
                        }
                    }
                    else
                    {
                        if (Resolver.TryResolve(out IIfsConnection connection) && !connection.TouchAppsComms.IsBroken && !logoRef.Contains(":"))
                        {
                            using (var response = await reportingService.DownloadFile(connection, logoRef, string.Empty))
                            {
                                if (response != null)
                                {
                                    await file.WriteStreamAsync(response.Stream);
                                    logoPath = file.FullFilePath;
                                }
                            }
                        }
                    }
                }

                
                string html = await reportingService.GenerateHtmlFromContent(assistantPage.CurrentStep.Description, logoRef, assistantPage.CurrentStep.Elements, true, CancellationToken.None);
                string jsonContent = reportingService.GenerateJsonFromContent(assistantPage.Name, assistantPage.CurrentStep.Elements, true, CancellationToken.None);

                ObjPrimaryKey key = context.Data?.Record?.ToPrimaryKey();
                string keyRef = key?.ToKeyRef();
                string luName = context.Data?.Record?.EntityName;
                string signingGuid = Guid.NewGuid().ToString();
                string fileName = args.FileName != null ? context.InterpolateString(args.FileName, true) + "-" + DateTime.Now.ToString("yyyy-dd-M-HH-mm-ss") : signingGuid + "-" + DateTime.Now.ToString("yyyy-dd-M-HH-mm-ss");

                PageValues filters = PageValues.FromPrimaryKey(key);
                ILocalFileInfo tempPdfFile = await reportingService.SaveHtmlToPdf(assistantPage.ProjectionName, fileName, filters, html, true);

                string savedPdfPath = tempPdfFile.FullFilePath;

                if (args.LuName != null && context.TryGetValue(args.LuName, out object luNameValue) && luNameValue != null)
                {
                    luName = luNameValue.ToString();
                }

                if (args.KeyRef != null && context.TryGetValue(args.KeyRef, out object keyRefValue) && keyRefValue != null)
                {
                    keyRef = keyRefValue.ToString();
                }

                if (args.OnlineFlow != null && args.OnlineFlow == true)
                {
                    online = true;
                }

                if (keyRef != null && luName != null && Resolver.TryResolve(out IPdfGenerator pdfGenerator) &&
                    await pdfGenerator.GeneratePdf(fileName, jsonContent, luName, keyRef, signingGuid, online, logoPath, savedPdfPath))
                {
                    if (PlatformServices.GetServerVersionAsDouble(account?.ServerVersion) > 23.1)
                    {
                        row.SetMemberValue(guid, guid.ConvertValue(signingGuid.ToString()));
                        row.SetMemberValue(status, status.ConvertValue("SUCCESS"));
                    }
                    else
                    {
                        result = "SUCCESS";
                    }

                }
            }

            return PlatformServices.GetServerVersionAsDouble(account?.ServerVersion) > 23.1 ? new ExecuteResult(row) : new ExecuteResult(result);
        }
#pragma warning restore
#endif
        private async Task<ExecuteResult> ExecuteSound(CpiSoundCallArgs args)
        {
            await _dialogService.PlaySound(args.Name.ToLower());
            return ExecuteResult.None;
        }

        private async Task<ExecuteResult> ExecuteTransfer(CommandContext context, CpiTransferCallArgs args)
        {
            List<string> toTransfer = new List<string>();
            foreach (object parameter in args.ParamsArray)
            {
                object value = context.ReadParamValue(parameter);
                toTransfer.Add(Convert.ToBase64String(BinarySerializerHelper.ObjectToByteArray(value)));
            }

            string data = string.Join(StringExtensions.UnitSeparator, toTransfer);
            Logger.Log($"Initiating transfer of data: {data}", MessageType.Information);
            return await _dataTransferService.TransferDataAsync(data);
        }

        private async Task<ExecuteResult> ExecuteReceive(CommandContext context, CpiTransferCallArgs args)
        {
            string data = await _dataTransferService.ReceiveDataAsync();

            if (!string.IsNullOrWhiteSpace(data))
            {
                string[] toReceive = data.Split(new string[] { StringExtensions.UnitSeparator }, StringSplitOptions.RemoveEmptyEntries);

                if (toReceive == null || toReceive.Length != args.ParamsArray.Length)
                {
                    int receivedCount = toReceive != null ? toReceive.Length : 0;
                    Logger.Log($"Execute Call: The number of values received from data ({receivedCount}) does not match the number of parameters given ({args.ParamsArray.Length})", MessageType.Error);
                    throw Fail(context, Strings.InvalidQrData);
                }

                for (int i = 0; i < args.ParamsArray.Length; i++)
                {
                    byte[] value = Convert.FromBase64String(toReceive[i]);
                    context.Assign(args.ParamsArray[i].ToString(), BinarySerializerHelper.ByteArrayToObject(value));
                }

                return ExecuteResult.Ok;
            }
            else
            {
                return ExecuteResult.Cancel;
            }
        }

        private async Task<ExecuteResult> ExecutePrint(CommandContext context, CpiPrintCallArgs args)
        {
            PageValues filters = PageValues.FromNavArgs(args.Filters, context);
            await _reportingService.ShowPageReportAsync(args.Client ?? context.ProjectionName, args.Page, filters);
            return ExecuteResult.None;
        }

        private async Task<ExecuteResult> ExecuteUpload(CommandContext context, CpiUploadCallArgs args)
        {
            ExecuteUpload exec = _resolver.Resolve<ExecuteUpload>();

            FileSelectorState fileSelectorState = new FileSelectorState(context.Data?.PageData?.ViewState, args.FileSelector);

            IEnumerable<PickedFile> files = fileSelectorState.PickedFiles;
            if (files != null)
            {
                foreach (PickedFile file in files)
                {
                    fileSelectorState.UploadingFile = file.FileName;

                    ExecuteResult callResult = await ExecuteCallAsync(context, args.Call, args.Assign);

                    if (callResult.IsOffline || callResult.Returning)
                    {
                        return callResult;
                    }

                    ExecuteResult uploadResult = await exec.DoUploadAsync(context, args.Url, file);

                    if (uploadResult.IsOffline || uploadResult.Returning)
                    {
                        return uploadResult;
                    }
                }

                fileSelectorState.UploadingFile = null;
            }

            return ExecuteResult.None;
        }

        private async Task<ExecuteResult> ExecuteBarcode(CommandContext context, CpiBarcodeCallArgs args)
        {
            string data = await _dataTransferService.ReceiveDataAsync();

            if (!string.IsNullOrWhiteSpace(data))
            {
                context.Assign(args.Assign, data);
                return ExecuteResult.Ok;
            }
            else
            {
                return ExecuteResult.Cancel;
            }
        }

        private async Task<ExecuteResult> ExecuteLocation(CommandContext context, CpiLocationCallArgs args)
        {
            GpsLocation location = await _locationService.GetGpsLocationAsync();

            if (args.Latitude != null)
            {
                context.Assign(args.Latitude, location?.Latitude);
            }

            if (args.Longitude != null)
            {
                context.Assign(args.Longitude, location?.Longitude);
            }

            return location == null ? ExecuteResult.Cancel : ExecuteResult.Ok;
        }

        private ExecuteResult ExecuteCopy(CommandContext context, CpiCopyCallArgs args)
        {
            RemoteRow from = null, to = null;

            if (args.From == "this")
            {
                from = context.Data.Record.GetRemoteRow();
            }
            else if (context.Vars.TryGetValue(args.From, out VariableStorage source) && source.Value is RemoteRow sourceVar)
            {
                from = sourceVar;
            }

            if (args.To == "this")
            {
                to = context.Data.Record.GetRemoteRow();
            }
            else if (context.Vars.TryGetValue(args.To, out VariableStorage target) && target.Value is RemoteRow targetVar)
            {
                to = targetVar;
            }

            if (from != null && to != null && from.TableName == to.TableName)
            {
                List<KeyValuePair<string, CpiAttribute>> fields = _metadata.FindEntity(context.ProjectionName, from)?.Attributes?.ToList();

                if (fields != null)
                {
                    foreach (string key in fields.Select(field => field.Key))
                    {
                        object value = from[key];

                        if (value != null)
                        {
                            to[key] = value;
                        }
                    }
                }
            }
            else
            {
                Logger.Error("Invalid structures encountered when copying in command {0}", context.DebugName);
            }

            return ExecuteResult.None;
        }

        public static bool IsOkCommand(CpiCommand command)
        {
            if (command == null)
                throw new ArgumentNullException(nameof(command));

            CpiExecute execute = command.Execute?.FirstOrDefault();

            if (execute?.Call == null)
            {
                return false;
            }

            return execute.Call.Method == CpiExecuteCallMethod.Exit && ((CpiExitCallArgs)execute.Call.Args)?.Return == (string)ExecuteResult.Ok.Value;
        }

        public static bool IsRelatedPageCommand(CpiCommand command)
        {
            if (command == null)
                throw new ArgumentNullException(nameof(command));

            if (command.DisplayArea == CommandDisplayArea.NotSpecified)
            {
                CpiExecute execute = command.Execute?.FirstOrDefault();
                return execute?.Call?.Method == CpiExecuteCallMethod.Navigate || execute?.Call?.Method == CpiExecuteCallMethod.BulkNavigate;
            }
            else
            {
                return command.DisplayArea == CommandDisplayArea.RelatedPageList ? true : false;
            }
        }

        public static string GetPageCreateCommandName(string pageName)
        {
            return $"{pageName}_{pageName}_Create";
        }

        public static bool IsPageCreateCommand(string pageName, CpiCommand x)
        {
            string createCommandName = GetPageCreateCommandName(pageName);
            return x.Name == createCommandName;
        }

        public static CpiCommand CreateDynamicCommand(FndDynamicCommandDef command)
        {
            CpiExecuteCallArgs args;
            CpiExecuteCallMethod method;

            switch (command.Method)
            {
                case "alert":
                    args = new CpiAlertCallArgs()
                    {
                        Message = command.ArgValue
                    };
                    method = CpiExecuteCallMethod.Alert;
                    break;
                case "set":
                    args = new CpiSetCallArgs()
                    {
                        Value = command.ArgValue
                    };
                    method = CpiExecuteCallMethod.Set;
                    break;
                case "action":
                    args = new CpiActionCallArgs()
                    {
                        Bound = command.Baund.GetValueOrDefault(),
                        Name = command.Name,
                        Projection = command.Projection,
                        Params = command.Params.SplitToDictionary()
                    };
                    method = CpiExecuteCallMethod.Action;
                    break;
                default:
                    return null;
            }

            CpiCommand cmd = new CpiCommand
            {
                Execute = new[]
                {
                    new CpiExecute
                    {
                        Assign = command.Assign,
                        Call = new CpiExecuteCall
                        {
                            Method = method,
                            Args = args
                        }
                    }
                }
            };

            return cmd;
        }

        private string GetAuthenticationRedirectUri()
        {
            try
            {
                string assemblyName = "Ifs.Uma.Startup";
                string resourceName = "Ifs.Uma.Startup.Resources.AppInfo";

                Assembly assembly = Assembly.Load(assemblyName);
                Type appInfoType = assembly.GetType(resourceName);

                if (appInfoType != null)
                {
                    PropertyInfo propertyInfo = appInfoType.GetProperty("AuthenticationRedirectUri", BindingFlags.Static | BindingFlags.NonPublic | BindingFlags.Public);
                    if (propertyInfo != null)
                    {
                        object value = propertyInfo.GetValue(null, null);
                        return value as string;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.HandleException(ExceptionType.Unexpected, ex);
            }

            return null;
        }
    }
}
