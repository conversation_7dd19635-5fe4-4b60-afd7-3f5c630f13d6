﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public sealed class MediaListItem : AttachmentListItem
    {
        private readonly IMediaHandler _mediaHandler;

        public MediaLibrary Library { get; }
        public MediaLibraryItem MediaItem { get; }

        public MediaListItem(IMediaHandler mediaHandler, MediaLibrary connection, MediaLibraryItem media)
        {
            _mediaHandler = mediaHandler;
            Library = connection;
            MediaItem = media;

            Title = media.Name;
            Description = media.Description;

            if ((MediaItem.AttachmentStatus == null || MediaItem.AttachmentStatus == AttachmentStatus.Unknown) 
                && MediaItem.MediaItemType == MediaType.Video)
            {
                Status = AttachmentStatus.RequiresDownload;
            }
            else
            {
                Status = media.AttachmentStatus ?? AttachmentStatus.Unknown;
            }
        }

        public override Task<ILocalFileInfo> GetFileInfoAsync()
        {
            return _mediaHandler.GetLocalFileForMediaAsync(MediaItem);
        }

        public override Task DownloadFileAsync()
        {
            return _mediaHandler.RequestDownloadAsync(MediaItem);
        }
    }
}
