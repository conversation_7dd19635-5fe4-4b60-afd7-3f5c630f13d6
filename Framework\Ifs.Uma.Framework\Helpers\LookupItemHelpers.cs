﻿using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.Framework.UI.Lookups;

namespace Ifs.Uma.Framework.Helpers
{
    public static class LookupItemHelpers
    {
        public static LookupItemType LookupItemActionsToItemType(IEnumerable<LookupItem.LookupItemAction> actions)
        {
            IList<LookupItem.LookupItemAction> actionList = actions.ToList();
            if (actionList.Contains(LookupItem.LookupItemAction.Unfavorite))
            {
                return LookupItemType.Favorite;
            }
            else if (actionList.Contains(LookupItem.LookupItemAction.Forget))
            {
                return LookupItemType.RecentlyUsed;
            }
            else
            {
                return LookupItemType.Item;
            }
        }
    }
}
