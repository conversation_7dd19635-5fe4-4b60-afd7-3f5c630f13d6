﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal class StringLastIndexOf : StringFunction
    {
        public const string FunctionName = "LastIndexOf";

        public StringLastIndexOf()
            : base(FunctionName, 2, false)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string stringToSearch = (string)parameters[0].GetValue();
            string stringToIndex = (string)parameters[1].GetValue();

            if (stringToSearch == null || stringToIndex == null)
            {
                return -1;
            }

            int index = stringToSearch.LastIndexOf(stringToIndex);

            return index;
        }
    }
}
