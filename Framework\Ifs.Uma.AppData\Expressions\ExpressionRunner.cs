﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Ifs.Uma.AppData.StringExpressions;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Expressions
{
    public interface IExpressionRunner
    {
        object Run(CpiExpression expression, IExpressionValueProvider valueProvider);

        bool RunCheck(CpiExpression expression, IExpressionValueProvider valueProvider, bool nullValue);

        string InterpolateString(string str, IStringExpressionValueProvider valueProvider, bool localize = true, bool isUrl = false, IMetaTable metaTable = null);
    }

    public sealed class ExpressionRunner : IExpressionRunner
    {
        private readonly ILogger _logger;
        private readonly ConcurrentDictionary<string, InterpolatedString> _interpolatedString = new ConcurrentDictionary<string, InterpolatedString>();

        public ExpressionRunner(ILogger logger)
        {
            _logger = logger;
        }

        public object Run(CpiExpression expression, IExpressionValueProvider valueProvider)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));

            Func<IExpressionValueProvider, object> method = expression.ExpressionCache as Func<IExpressionValueProvider, object>;
            if (method == null)
            {
                Expression expr = IfsExpression.FromJsonLogic(expression.JsonLogic);

                ParameterExpression vpParam = Expression.Parameter(typeof(IExpressionValueProvider), "ValueProvider");
                expr = InToIfRewriter.Rewrite(expr);
                expr = ValueProviderReplacer.Rewrite(expr, vpParam);
                expr = MethodCallReplacer.Rewrite(expr, vpParam);
                expr = Expression.Convert(expr, typeof(object));

                DisallowVarChecker.Check(expr);
                DisallowMethodChecker.Check(expr);

                method = Expression.Lambda<Func<IExpressionValueProvider, object>>(expr, vpParam).Compile();
                expression.ExpressionCache = method;
            }

            return method(valueProvider);
        }

        public bool RunCheck(CpiExpression expression, IExpressionValueProvider valueProvider, bool nullValue)
        {
            if (expression == null) return nullValue;
            bool? quickCheck = expression.QuickCheck();
            if (quickCheck.HasValue) return quickCheck.Value;
            if (valueProvider == null) return false;
            
            try
            {
                bool? result = Run(expression, valueProvider) as bool?;
                return result.GetValueOrDefault(false);
            }
            catch (Exception ex)
            {
                _logger?.HandleException(ExceptionType.Unexpected, ex);
                return false;
            }
        }

        public string InterpolateString(string str, IStringExpressionValueProvider valueProvider, bool localize = true, bool isUrl = false, IMetaTable metaTable = null)
        {
            InterpolatedString iStr = _interpolatedString.GetOrAdd(str ?? string.Empty, CreateStringExpression);
            return iStr == null ? str : iStr.GetString(valueProvider, localize, isUrl, metaTable);
        }

        private InterpolatedString CreateStringExpression(string str)
        {
            InterpolatedString iStr = new InterpolatedString(_logger, str);
            return iStr.IsBasicString ? null : iStr;
        }
    }

    public static class ExpressionRunnerExtensions
    {
        public static string GetEmphasis(this IExpressionRunner expressionRunner, Dictionary<string, CpiExpression>[] emphases, IExpressionValueProvider valueProvider)
        {
            if (emphases == null || emphases.Length == 0)
            {
                return null;
            }

            foreach (var emphasisObj in emphases)
            {
                KeyValuePair<string, CpiExpression> emphasis = emphasisObj.FirstOrDefault();
                if (emphasis.Value != null &&
                    expressionRunner.RunCheck(emphasis.Value, valueProvider, false))
                {
                    return emphasis.Key;
                }
            }

            return null;
        }
    }
}
