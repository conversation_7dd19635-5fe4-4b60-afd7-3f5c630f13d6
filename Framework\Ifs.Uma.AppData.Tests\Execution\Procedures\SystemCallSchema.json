{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "CRUD": "Create,Read,Update,Delete", "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<TestLogLocation>": {"name": "TestLogLocation", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Var1", "dataType": "Text", "collection": false}], "execute": [{"call": {"method": "set", "args": {"value": "AAA"}}, "assign": "Var1"}, {"call": {"method": "proc", "args": {"name": "LogLocation", "namespace": "System", "paramsArray": ["Test info ${Var1}"]}}}]}]}, "Action<TestLogLocationBeforeErrorTransaction>": {"name": "TestLogLocationBeforeErrorTransaction", "type": "Action", "params": [], "layers": [{"execute": [{"call": {"method": "proc", "args": {"name": "LogLocation", "namespace": "System", "paramsArray": ["Test info"]}}}, {"call": {"method": "error", "args": {"msg": "ProcedureTestError"}}}]}]}, "Function<TestLogLocationBeforeError>": {"name": "TestLogLocationBeforeError", "type": "Function", "params": [], "layers": [{"execute": [{"call": {"method": "proc", "args": {"name": "LogLocation", "namespace": "System", "paramsArray": ["Test info"]}}}, {"call": {"method": "error", "args": {"msg": "ProcedureTestError"}}}]}]}, "Function<TimeZone>": {"name": "TimeZone", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Result", "dataType": "string"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "System", "name": "TimeZone", "paramsArray": []}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<GenerateGuid>": {"name": "GenerateGuid", "type": "Function", "params": [], "layers": [{"vars": [{"name": "Var1", "dataType": "", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "CreateGuid", "namespace": "System"}}, "assign": "Var1"}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "Function<FnTestHash>": {"name": "FnTestHash", "type": "Function", "params": [{"name": "Input", "datatype": "Text", "collection": false}, {"name": "HashFunction", "datatype": "Text", "collection": false}], "layers": [{"vars": [{"name": "Result", "dataType": "Text", "collection": false}], "execute": [{"call": {"method": "proc", "args": {"name": "Hash", "namespace": "System", "paramsArray": ["${Input}", "${HashFunction}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}