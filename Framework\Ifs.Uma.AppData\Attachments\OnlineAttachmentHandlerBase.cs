﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.AppData.Attachments
{
    public abstract class OnlineAttachmentHandlerBase : DataAccessor<FwDataContext>, IOnlineAttachmentHandler
    {
        private static readonly TimeSpan DownloadSkipPeriod = TimeSpan.FromSeconds(60);

        private readonly ConcurrentDictionary<string, DateTime> _lastDownload = new ConcurrentDictionary<string, DateTime>();
        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private readonly IMetadata _metadata;
        private readonly SemaphoreSlim _downloadLock = new SemaphoreSlim(1);

        public OnlineAttachmentHandlerBase(IDatabaseController db, ILogger logger, IPerfLogger perfLogger, IEventAggregator eventAggregator, IMetadata metadata)
             : base(db, logger, perfLogger)
        {
            _logger = logger;
            _eventAggregator = eventAggregator;
            _metadata = metadata;
        }

        public async Task DownloadAttachmentsIfNeeded(string entityName, string keyRef, bool skipIfAlreadyDownloaded, CancellationToken cancellationToken)
        {
            await _downloadLock.WaitAsync(cancellationToken);
            try
            {
                string dictKey = entityName + ":" + keyRef;
                if (_lastDownload.TryGetValue(dictKey, out DateTime lastUpdate) &&
                    (skipIfAlreadyDownloaded || lastUpdate > DateTime.UtcNow.Subtract(DownloadSkipPeriod)))
                {
                    return;
                }

                bool includeDocuments = false;
                bool includeMedia = false;
                string serverKeyRef = null;

                await WithDataContextAsync(ctx => GetAttachmentsToInclude(entityName, keyRef, ctx, out includeDocuments, out includeMedia, out serverKeyRef), null);

                if (!includeDocuments && !includeMedia)
                {
                    // Nothing to download
                    return;
                }

                OnlineResponse<AttachmentInfo> response = await DownloadAttachments(entityName, keyRef, serverKeyRef, includeDocuments, includeMedia, cancellationToken);

                if (response.IsOffline)
                {
                    return;
                }

                response.CheckFailure();

                // Documents
                EdmFile[] edmFiles = response.Result.Documents.Select(x => x.EdmFile).Where(x => x != null).ToArray();
                DocReferenceObject[] docRefs = response.Result.Documents.Select(x => x.DocumentRevision).ToArray();

                if (edmFiles.Length > 0 || docRefs.Length > 0)
                {
                    List<RemoteRow> changed = new List<RemoteRow>();

                    await WithDataContextAsync(ctx =>
                    {
                        // Use InsertOrIgnore here to make sure we do not override
                        // client changes e.g. AttachmentStatus / MediaFile

                        if (includeDocuments)
                        {
                            IMetaTable edmFileTable = ctx.Model.GetTable(typeof(EdmFile));
                            changed.AddRange(ctx.InsertOrIgnoreCacheTable(edmFileTable, edmFiles));

                            IMetaTable docRevTable = ctx.Model.GetTable(typeof(DocReferenceObject));
                            changed.AddRange(ctx.InsertOrIgnoreCacheTable(docRevTable, docRefs));
                        }
                    });

                    FireDataChangeEvent(_eventAggregator, changed);
                }
                else
                {
                    await WithDataContextAsync(ctx =>
                    {
                        DocReferenceObject[] existingDocRefs = ctx.DocReferenceObjects.Where(x => x.KeyRef == keyRef && x.LuName == entityName).ToArray();
                        if (existingDocRefs.Length > 0)
                        {
                            foreach (DocReferenceObject docRef in existingDocRefs)
                            {
                                ctx.DocReferenceObjects.DeleteOnSubmit(docRef);

                                EdmFile[] existingEdmFiles = ctx.EdmFiles.Where(x => x.DocClass == docRef.DocClass &&
                                                                                     x.DocNo == docRef.DocNo &&
                                                                                     x.DocRev == docRef.DocRev &&
                                                                                     x.DocSheet == docRef.DocSheet).ToArray();
                                foreach (EdmFile edmFile in existingEdmFiles)
                                {
                                    ctx.EdmFiles.DeleteOnSubmit(edmFile);
                                }
                            }

                            ctx.SubmitChanges(false);
                        }
                    });
                }

                // Media
                MediaLibraryItem[] mediaItems = response.Result.Media.Select(x => x.MediaItem).ToArray();
                MediaLibrary[] libaries = response.Result.Media.Select(x => x.Library).ToArray();

                if (mediaItems.Length > 0 || libaries.Length > 0)
                {
                    List<RemoteRow> changed = new List<RemoteRow>();

                    await WithDataContextAsync(ctx =>
                    {
                        // Use InsertOrIgnore here to make sure we do not override
                        // client changes e.g. AttachmentStatus / MediaFile

                        if (includeMedia)
                        {
                            IMetaTable miTable = ctx.Model.GetTable(typeof(MediaLibraryItem));
                            changed.AddRange(ctx.InsertOrIgnoreCacheTable(miTable, mediaItems));

                            IMetaTable mlTable = ctx.Model.GetTable(typeof(MediaLibrary));
                            changed.AddRange(ctx.InsertOrIgnoreCacheTable(mlTable, libaries));
                        }
                    });

                    FireDataChangeEvent(_eventAggregator, changed);
                }
                else
                {
                    await WithDataContextAsync(ctx =>
                    {
                        MediaLibrary[] existingLibaries = ctx.MediaLibraries.Where(x => x.KeyRef == keyRef && x.LuName == entityName).ToArray();
                        if (existingLibaries.Length > 0)
                        {
                            foreach (MediaLibrary lib in existingLibaries)
                            {
                                ctx.DocReferenceObjects.DeleteOnSubmit(lib);

                                MediaLibraryItem[] existingMediaItems = ctx.MediaLibraryItems.Where(x => x.LibraryId == lib.LibraryId).ToArray();
                                foreach (MediaLibraryItem mediaItem in existingMediaItems)
                                {
                                    ctx.MediaLibraryItems.DeleteOnSubmit(mediaItem);
                                }
                            }

                            ctx.SubmitChanges(false);
                        }
                    });
                }

                _lastDownload[dictKey] = DateTime.UtcNow;
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
            }
            finally
            {
                _downloadLock.Release();
            }
        }

        private void GetAttachmentsToInclude(string entityName, string keyRef, FwDataContext ctx, out bool includeDocuments, out bool includeMedia, out string serverKeyRef)
        {
            EntitySyncPolicy syncPolicy = _metadata.GetEntitySyncPolicy(entityName);

            ObjPrimaryKey pk = ObjPrimaryKey.FromKeyRef(ctx.Model, entityName, keyRef);
            if (syncPolicy != EntitySyncPolicy.OnlineOnly)
            {
                string rowObjKey = pk == null ? null : ctx.GetObjKey(pk);
                if (rowObjKey == null)
                {
                    // Only get attachments for records we know the server knows about
                    includeDocuments = false;
                    includeMedia = false;
                    serverKeyRef = null;
                    return;
                }
            }

            includeDocuments = ctx.ObjectConnectionConfigs.Any(x => x.Entity == entityName && x.ProviderName == DocumentHandler.DocReferenceObjectProvider && x.Prefetch != true);
            includeMedia = ctx.ObjectConnectionConfigs.Any(x => x.Entity == entityName && x.ProviderName == MediaHandler.MediaLibraryProvider && x.Prefetch != true);

            // Don't request attachments if we are still syncing attachments to the server
            if (ctx.TransitionRows.Any(x => x.TableName == DocReferenceObject.DbTableName || x.TableName == EdmFile.DbTableName))
            {
                includeDocuments = false;
            }

            if (ctx.TransitionRows.Any(x => x.TableName == MediaLibrary.DbTableName || x.TableName == MediaLibraryItem.DbTableName))
            {
                includeMedia = false;
            }

            string clientKeys = pk.ToKeySeparatedValues();
            ClientKeysMap keysMaps = ctx.ClientKeysMap.FirstOrDefault(x => x.TableName == entityName && x.ClientKeys == clientKeys);
            serverKeyRef = keysMaps == null ? null : ObjPrimaryKey.FromKeySeparatedValues(pk.Table, keysMaps.ServerKeys)?.ToKeyRef();
        }

        protected abstract Task<OnlineResponse<AttachmentInfo>> DownloadAttachments(string entityName, string clientKeyRef, string serverKeyRef, bool includeDocuments, bool includeMedia, CancellationToken cancellationToken);
        
        protected class AttachmentInfo
        {
            public IReadOnlyList<MediaInfo> Media { get; set; }
            public IReadOnlyList<DocRevisionInfo> Documents { get; set; }
        }
    }
}
