﻿using System;
using Ifs.Cloud.Client.Exceptions;
using Ifs.Uma.Services.Transactions;

namespace Ifs.Uma.Comm.TouchApps
{
    public class BrokenCause
    {
        public BrokenReason Reason { get; private set; }
        public CloudException Exception { get; private set; }

        public BrokenCause(BrokenReason reason, CloudException exception)
        {
            Reason = reason;
            Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        }
    }
}
