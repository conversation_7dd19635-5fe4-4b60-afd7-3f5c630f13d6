﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringTrimEnd : StringFunction
    {
        public const string FunctionName = "TrimEnd";

        public StringTrimEnd()
            : base(FunctionName, 1, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify) => stringToModify.TrimEnd();
    }

    internal sealed class StringTrimEnd2 : StringFunction
    {
        public const string FunctionName = "TrimEnd";

        public StringTrimEnd2()
            : base(FunctionName, 2, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string trimString = parameters[1].GetString();

            if (string.IsNullOrEmpty(trimString))
                return null;

            return stringToModify.TrimEnd(trimString[0]);
        }
    }
}
