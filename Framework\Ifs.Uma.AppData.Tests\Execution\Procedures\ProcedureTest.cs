﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.Tests;
using NUnit.Framework;
using Unity;
using Unity.Lifetime;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    public class ProcedureTest : FrameworkTest
    {
        protected override void BeforeTest()
        {
            base.BeforeTest();

            Container.RegisterType<ICachePreparer, TestCachePreparer>(new ContainerControlledLifetimeManager());
            Container.RegisterType<ILocationLogger, TestLocationLogger>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IProcedureExecutor, ProcedureExecutor>(new ContainerControlledLifetimeManager());
            Container.RegisterType<IProcedureExecutor, ProcedureExecutor>(new ContainerControlledLifetimeManager());
        }

        protected async Task<T> CallFunction<T>(string projectionName, string functionName, Dictionary<string, object> parameters = null)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(projectionName, functionName, parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return (T)result.Value;
        }

        private sealed class TestCachePreparer : ICachePreparer
        {
            public Task<PrepareCacheResult> PrepareCacheAsync(string projectionName, string entityName, CancellationToken cancelToken = default(CancellationToken))
            {
                return Task.FromResult(PrepareCacheResult.Ready);
            }

            public Task<PrepareCacheResult> AddToCacheAsync(EntityQuery query, CancellationToken cancelToken = default(CancellationToken))
            {
                return Task.FromResult(PrepareCacheResult.Ready);
            }

            public Task<bool> IsCacheReadyAsync(string projectionName, string entityName)
            {
                return Task.FromResult(true);
            }

            public Task ClearCache()
            {
                return Task.FromResult(true);
            }

            public Task<bool> RefreshCache()
            {
                return Task.FromResult(true);
            }
        }
        
        protected sealed class TestLocationLogger : ILocationLogger
        {
            public string LastInfo { get; set; }

            public void LogLocation(string info)
            {
                LastInfo = info;
            }
        }
    }
}
