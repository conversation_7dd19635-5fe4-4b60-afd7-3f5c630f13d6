﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public class MediaListPage : PageBase
    {
        public const string PageName = "MediaListPage";

        public MediaListData ListData { get; }

        public MediaListPage(IEventAggregator eventAggregator, IDialogService dialogService, IMediaHandler mediaHandler, INavigator navigator)
            : base(eventAggregator, dialogService)
        {
            Name = PageName;
            Classification = PageClassification.MediaList;
            ListData = new MediaListData(eventAggregator, mediaHandler, navigator);
            ListData.IsActive = true;
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter parameter)
        {
            AttachmentNavParam attachmentNavParam = parameter as AttachmentNavParam;
            await ListData.Load(attachmentNavParam);
            Title = ListData.Title;
            return true;
        }
    }
}
