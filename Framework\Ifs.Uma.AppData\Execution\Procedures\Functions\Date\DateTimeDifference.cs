﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Date
{ 
    internal sealed class DateTimeDifferenceInDays : DateTimeFunction
    {
        public const string FunctionName = "DifferenceInDays";

        public DateTimeDifferenceInDays()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime? a = parameters[0].GetTimestamp();
            DateTime? b = parameters[1].GetTimestamp();
            if (a.HasValue && b.HasValue)
            {
                TimeSpan diff = b.Value.Subtract(a.Value);
                return diff.TotalDays;
            }
            else
            {
                return null;
            }
        }
    }

    internal sealed class DateTimeDifferenceInHours : DateTimeFunction
    {
        public const string FunctionName = "DifferenceInHours";

        public DateTimeDifferenceInHours()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            DateTime? a = parameters[0].GetTimestamp();
            DateTime? b = parameters[1].GetTimestamp();
            if (a.<PERSON>al<PERSON> && b.<PERSON>alue)
            {
                TimeSpan diff = b.Value.Subtract(a.Value);
                return diff.TotalHours;
            }
            else
            {
                return null;
            }
        }
    }
}
