﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Ifs.Cloud.Client.Utils;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using static Ifs.Uma.Comm.TouchApps.EntityResource;

namespace Ifs.Uma.Comm.TouchApps
{
    public static class ResourceUtils
    {
        public static object DeserializeJsonString(EntityQuery query, IClientKeysMapper clientKeysMapper, string jsonString)
        {
            EntityData entityData;
            using (StringReader sr = new StringReader(jsonString))
            using (JsonReader reader = new JsonTextReader(sr) { DateParseHandling = DateParseHandling.None })
            {
                JsonSerializer serializer = new JsonSerializer();
                entityData = serializer.Deserialize<EntityData>(reader);
            }

            List<EntityRecord> records = new List<EntityRecord>();
            if (entityData.Result != null)
            {
                List<Tuple<string, IMetaTable>> refs = GetReferences(query);
                foreach (JObject jRow in entityData.Result)
                {
                    EntityRecord record = ReadRecord(query, clientKeysMapper, jRow, refs);
                    if (record != null)
                    {
                        records.Add(record);
                    }
                }
            }

            return new EntityQueryResult(query, records, records.Count >= query.Take.GetValueOrDefault(EntityQueryString.DefaultTakeCount));
        }

        private static List<Tuple<string, IMetaTable>> GetReferences(EntityQuery query)
        {
            Dictionary<string, CpiReference> refs = query.DataSource.Metadata.FindReferences(query.DataSource.ProjectionName, query.DataSource.EntityName);
            if (refs == null || refs.Count == 0)
            {
                return new List<Tuple<string, IMetaTable>>();
            }

            List<Tuple<string, IMetaTable>> references = new List<Tuple<string, IMetaTable>>();

            foreach (KeyValuePair<string, CpiReference> reference in refs)
            {
                IMetaTable refTable = query.DataSource.Metadata.GetTableForEntityName(reference.Value.Target);
                if (refTable != null)
                {
                    references.Add(new Tuple<string, IMetaTable>(reference.Key, refTable));
                }
            }

            return references;
        }

        private static EntityRecord ReadRecord(EntityQuery query, IClientKeysMapper clientKeysMapper, JObject jRow, List<Tuple<string, IMetaTable>> refs)
        {
            IMetaTable table = query.DataSource.Table;
            RemoteRow row = MessageUtils.JObjectToRow(table, clientKeysMapper, jRow);

            Dictionary<string, RemoteRow> refRows = null;
            if (refs != null)
            {
                foreach (string refName in refs.Select(x => x.Item1))
                {
                    string jsonRefName = RemoteNaming.ToServerEntityReferenceName(refName);
                    JToken jRef = jRow[jsonRefName];
                    if (jRef != null)
                    {
                        if (jRef.Type == JTokenType.Null)
                        {
                            if (refRows == null)
                            {
                                refRows = new Dictionary<string, RemoteRow>();
                            }

                            refRows[refName] = null;
                        }
                        else if (jRow[jsonRefName] is JObject refJRow)
                        {
                            IEnumerable<IMetaRelation> relations = table.Model.GetRelations(table);
                            IMetaRelation relation = relations.FirstOrDefault(x => x.ReferenceName == refName);

                            RemoteRow refRow = MessageUtils.JObjectToRow(relation?.ReferencedTable, clientKeysMapper, refJRow);
                            if (refRow != null)
                            {
                                if (refRows == null)
                                {
                                    refRows = new Dictionary<string, RemoteRow>();
                                }

                                refRows[refName] = refRow;
                            }
                        }
                    }
                }
            }

            return new EntityRecord(row, refRows);
        }

        public static string GetQueryString(EntityQuery query)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();

            EntityQueryString.Build(query, parameters);

            string url = Formatter.ToQueryString(parameters);
            return url;
        }
    }
}
