﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.Convert
{
    [TestFixture]
    public class ConvertToStringTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.Convert.ConvertToStringTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase((long)505, ExpectedResult = "505")]
        [TestCase(502, ExpectedResult = "502")]
        [TestCase(505.7, ExpectedResult = "505.7")]
        [TestCase(true, ExpectedResult = "True")]
        [TestCase("2018-07-28T14:22:00", ExpectedResult = "2018-07-28T14:22:00")]
        [TestCase("2018-07-98T14:22:00.99345", ExpectedResult = "2018-07-98T14:22:00.99345")]
        [TestCase(null, ExpectedResult = null)]
        public async Task<string> Convert_ToString(object input)
        {
            _params["InputVariable"] = input;
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToString", _params);

            CheckResults(result);
            return result?.Value as string;
        }

        [Test]
        public async Task Convert_ToString_TimestampParameter()
        {
            _params["InputVariable"] = DateTime.ParseExact("2018-07-28T14:22:00", "s", CultureInfo.InvariantCulture, DateTimeStyles.None);
            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "Convert_ToString_Timestamp", _params);

            CheckResults(result);
            Assert.IsTrue(result.Value is string);
            Assert.AreEqual("2018-07-28T14:22:00.0000000", ((string)result.Value).ToUpperInvariant());
        }

        private static void CheckResults(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
