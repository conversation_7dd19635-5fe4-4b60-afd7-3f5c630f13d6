Write-Output "============ Tests"

Push-Location $solutionDir

msbuild "UnitTests.sln" /m /t:Rebuild /nr:false /v:m /p:Configuration=Release /p:RestorePackages=false

if ($LastExitCode -ne 0) { Exit $LastExitCode }

packages\NUnit.ConsoleRunner.3.9.0\tools\nunit3-console.exe --inprocess --workers=1 "--result=TestResult.xml;format=nunit2" `
	"Ifs.Uma.Utility.Tests\bin\Release\Ifs.Uma.Utility.Tests.dll" `
	"Ifs.Uma.UI.Tests\bin\Release\Ifs.Uma.UI.Tests.dll" `
	"Ifs.Uma.Framework.Tests\bin\Release\Ifs.Uma.Framework.Tests.dll" `
	"Ifs.Uma.AppData.Tests\bin\Release\Ifs.Uma.AppData.Tests.dll" `
	"Ifs.Uma.Data.Tests\bin\Release\Ifs.Uma.Data.Tests.dll" `
	"Ifs.Uma.Database.Tests\bin\Release\Ifs.Uma.Database.Tests.dll"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Pop-Location

Write-Output "============ End - Tests"
