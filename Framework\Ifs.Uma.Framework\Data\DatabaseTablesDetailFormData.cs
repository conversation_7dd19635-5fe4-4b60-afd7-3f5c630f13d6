﻿using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;

namespace Ifs.Uma.Framework.Data
{
    public class DatabaseTablesDetailFormData : FormData
    {
        public const string FormName = "Database Table - Row Details";

        private readonly Dictionary<string, string> _rowData;

        public DatabaseTablesDetailFormData(Dictionary<string, string> rowData)
        {
            _rowData = rowData;
            SetupForm();
        }

        protected override Form OnSetupForm()
        {
            Form form = new Form();

            var fieldsToAdd = new List<TextField>();

            if (_rowData != null && _rowData.Count > 0)
            {
                foreach (var column in _rowData)
                {
                    var field = new TextField
                    {
                        Name = column.Key,
                        Value = column.Value,
                        IsReadOnly = true
                    };
                    form.AllFields.Add(field);
                    fieldsToAdd.Add(field);
                }

                foreach (var layoutVariant in new[] { Form.LayoutVariants.Compact, Form.LayoutVariants.Regular })
                {
                    FormLayoutData layout = form.GetLayout(layoutVariant);
                    layout.SetDefaultLayout(form.AllFields.Select(x => new[] { x.Id }).ToArray());
                }
            }
            form.LayoutVariant = Form.LayoutVariants.Compact;
            return form;
        }
    }
}
