﻿using System;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Execution
{
    public sealed class VariableStorage
    {
        public string VariableName { get; }
        public CpiDataType? DataType { get; }
        public string SubType { get; }
        public bool IsCollection { get; }

        public object Value { get; private set; }
        
        private readonly IMetadata _metadata;
        
        public VariableStorage(IMetadata metadata, string variableName, CpiTypeInfo typeInfo)
        {
            if (metadata == null) throw new ArgumentNullException(nameof(metadata));
            if (variableName == null) throw new ArgumentNullException(nameof(variableName));

            if (typeInfo != null)
            {
                DataType = typeInfo.DataType;
                SubType = typeInfo.SubType;
                IsCollection = typeInfo.IsCollection;
            }

            _metadata = metadata;
            VariableName = variableName;
        }

        public bool TrySetValue(object value)
        {
            if (Equals(value, string.Empty))
            {
                value = null;
            }

            if (DataType.HasValue && !ExecutionUtils.TryConvert(value, DataType.Value, SubType, IsCollection, out value))
            {
                return false;
            }

            Value = value;
            return true;
        }

        public bool TrySetStructureValue(string attribute, object value)
        {
            if (attribute == null) throw new ArgumentNullException(nameof(attribute));

            RemoteRow row = Value as RemoteRow;
            if (row == null)
            {
                return false;
            }
            
            IMetaTable table = _metadata.MetaModel.GetTable(row.TableName);
            IMetaDataMember member = table?.FindMemberByPropertyName(attribute);
            if (member != null)
            {
                if (Equals(value, string.Empty))
                {
                    value = null;
                }

                row.SetMemberValue(member, member.ConvertValue(value));
                return true;
            }

            return false;
        }

        public bool TryGetStructureValue(string attribute, out object value)
        {
            value = null;

            if (attribute == null) throw new ArgumentNullException(nameof(attribute));

            RemoteRow row = Value as RemoteRow;
            if (row == null)
            {
                return false;
            }
            
            IMetaTable table = _metadata.MetaModel.GetTable(row.TableName);
            IMetaDataMember member = table?.FindMemberByPropertyName(attribute);
            if (member != null)
            {
                value = row.GetMemberValue(member);
                return true;
            }

            return false;
        }
    }
}
