<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/assistant_title"
        android:text="Title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/IfsDarkGreen"
        android:textColor="@color/IfsWhite"
        android:gravity="center"
        android:padding="5dp" />
    <LinearLayout
        android:id="@+id/AssistantStepContent"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:id="@+id/assistant_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/IfsBlack"
            android:textAppearance="@style/TextAppearance.AppCompat.Subhead"
            android:textDirection="locale"
            android:padding="10dp" />
        <Ifs.Uma.Framework.UI.Elements.ElementListView
            android:id="@+id/ElementList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />
        <WebView
            android:id="@+id/webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent" 
            android:visibility="gone" />
    </LinearLayout>
</LinearLayout>
