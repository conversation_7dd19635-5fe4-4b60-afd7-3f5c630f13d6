﻿using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Model;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Attachment
{
    internal sealed class AttachmentCreateAndConnectDoc : AttachmentFunction
    {
        public const string FunctionName = "CreateAndConnectDoc";

        public AttachmentCreateAndConnectDoc()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string luName = parameters[0].GetString();
            string keyRef = parameters[1].GetString();

            if (luName == null || keyRef == null)
            {
                return null;
            }

            return CreateAndConnectDoc(context, luName, keyRef, null);
        }

        internal static object CreateAndConnectDoc(ProcedureContext context, string luName, string keyRef, string title)
        {
            DocumentHandler.CreateAndConnectDoc(context.DbDataContext, luName, keyRef, title,
                            out DocIssue docIssue, out DocReferenceObject docRef);

            context.DataChangeSet.AddRow(context.Metadata.MetaModel, docIssue);
            context.DataChangeSet.AddRow(context.Metadata.MetaModel, docRef);

            FndDocumentKeys keys = new FndDocumentKeys();
            keys.DocClass = docRef.DocClass;
            keys.DocNo = docRef.DocNo;
            keys.DocSheet = docRef.DocSheet;
            keys.DocRev = docRef.DocRev;
            return keys;
        }
    }

    internal sealed class AttachmentCreateAndConnectDoc3 : AttachmentFunction
    {
        public AttachmentCreateAndConnectDoc3()
            : base(AttachmentCreateAndConnectDoc.FunctionName, 3)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string luName = parameters[0].GetString();
            string keyRef = parameters[1].GetString();

            if (luName == null || keyRef == null)
            {
                return null;
            }

            string title = parameters[2].GetString();

            return AttachmentCreateAndConnectDoc.CreateAndConnectDoc(context, luName, keyRef, title);
        }
    }
}
