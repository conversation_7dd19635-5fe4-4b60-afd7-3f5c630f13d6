﻿using Ifs.Uma.Utility;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    internal class EnumRewriter : ExpressionVisitor
    {
        public static Expression Rewrite(Expression expression)
        {
            return new EnumRewriter().Visit(expression);
        }

        protected override Expression VisitBinary(BinaryExpression node)
        {
            if (node == null) return null;
            switch (node.NodeType)
            {
                case ExpressionType.Equal:
                case ExpressionType.NotEqual:

                    Type enumType = FindConvertibleEnumType(node.Left, node.Right);

                    if (enumType != null)
                    {
                        BinaryExpression newExp = Expression.MakeBinary(node.NodeType, ConvertToEnumType(enumType, node.Left), ConvertToEnumType(enumType, node.Right));
                        return base.VisitBinary(newExp);
                    }

                    return base.VisitBinary(node);
                default:
                    return base.VisitBinary(node);
            }
        }

        private static Type FindConvertibleEnumType(params Expression[] expressions)
        {
            Type enumType = null;
            Type nnEnumType = null;
            foreach (Expression exp in expressions)
            {
                Type expType = TypeHelper.GetNonNullableType(exp.Type);
                if (exp.NodeType == ExpressionType.Constant && TypeHelper.EnumUnderlyingType(expType))
                {
                    // Ok to convert into an enum
                }
                else if (exp.NodeType == ExpressionType.Convert)
                {
                    Type unaryType = ((UnaryExpression)exp).Operand.Type;
                    Type convertType = TypeHelper.GetNonNullableType(unaryType);
                    if (convertType.GetTypeInfo().IsEnum)
                    {
                        if (nnEnumType != null && nnEnumType != convertType)
                        {
                            // Different enum types
                            return null;
                        }
                        else 
                        {
                            if (enumType == null || TypeHelper.IsNullableType(unaryType))
                            {
                                // If any of the expressions are nullable we must convert to nullable
                                enumType = unaryType;
                            }

                            nnEnumType = convertType;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    // Cannot convert
                    return null;
                }
            }

            return enumType;
        }

        private static Expression ConvertToEnumType(Type enumType, Expression exp)
        {
            Type expType = TypeHelper.GetNonNullableType(exp.Type);
            if (exp.NodeType == ExpressionType.Constant && TypeHelper.EnumUnderlyingType(expType))
            {
                ConstantExpression cex = ((ConstantExpression)exp);
                if (cex.Value == null)
                {
                    return Expression.Constant(null, enumType);
                }
                Type nnEnumType = TypeHelper.GetNonNullableType(enumType);
                object enumValue = Enum.ToObject(nnEnumType, cex.Value);
                return Expression.Constant(enumValue, enumType);
            }
            else if (exp.NodeType == ExpressionType.Convert)
            {
                // Remove the conversion from enum to int
                UnaryExpression uex = (UnaryExpression)exp;
                if (uex.Operand.Type == enumType)
                {
                    return uex.Operand;
                }
                else
                {
                    return Expression.Convert(uex.Operand, enumType);
                }
            }

            throw new InvalidOperationException("Cannot convert expression to an enum");
        }
    }
}
