﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Sync;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.AppData.Messages
{
    internal sealed class DataChangeMessageIn : MessageIn
    {
        // INSERT: try update data based on server pk (we may have a pre-created row in the client), insert if not
        // UPDATE: update based on obj_key if provided if not then based on server pk. exclude update server pk
        // DELETE: delete based on obj_key if provided if not then based on server pk

        public DataChangeMessageIn(MessageType messageType)
            : base(messageType)
        {
        }

        public void ExecuteInOwnTransaction(FwDataContext ctx, IClientKeysMapper clientKeysMapper, ILogger logger, DataChangeSet dataChangeSet, bool isInitializing)
        {
            IDbRowHandler<TransitionRow> handler = null;
            TransitionRow transitionRow = null;
            if (ClientRelatedMessageId.HasValue)
            {
                ctx.ExecuteInTransaction((command) =>
                {
                    handler = ctx.CreateDbRowHandler<TransitionRow>(command);
                    transitionRow = new TransitionRow();
                    transitionRow.RowId = ClientRelatedMessageId.Value;
                    if (!handler.SelectRow(transitionRow))
                    {
                        transitionRow = null;
                    }
                });
            }

            OnExecute(ctx, null, clientKeysMapper, logger, dataChangeSet, transitionRow, isInitializing);

            if (transitionRow != null)
            {
                ctx.ExecuteInTransaction((command) =>
                {
                    handler = ctx.CreateDbRowHandler<TransitionRow>(command);
                    TransitionRowSent(ctx, clientKeysMapper, handler, transitionRow);
                });
            }
        }

        protected override void OnExecute(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper,
            ILogger logger, DataChangeSet dataChangeSet, TransitionRow transitionRow, bool isInitializing)
        {
            InitializeStatus status = ctx.DatabaseInfos.Select(x => x.InitializeStatus).FirstOrDefault();
            bool initializing = status < InitializeStatus.Initialized;

            MessageData messageData = GetMessageData(ctx.Model, logger);
            if (messageData == null)
            {
                // Nothing to update
                return;
            }

            if (command != null)
            {
                DbRowHandlerCache cacheServerPk = ctx.CreateDbRowHandlerCache(command, new MessageRowDataAccessor(), KeyChoice.DefaultKey);
                DbRowHandlerCache cacheObjKey = ctx.CreateDbRowHandlerCache(command, new MessageRowDataAccessor(exludeServerPrimaryKey: true), KeyChoice.ObjKey);
                IDbRowHandler<IgnoreMessageIn> ignoreMessageInRowHandler = ctx.CreateDbRowHandler<IgnoreMessageIn>(command);

                foreach (MessageTableData data in messageData.TableData)
                {
                    if (ShouldProcessMessageIn(ignoreMessageInRowHandler, data))
                    {
                        SyncRowData(logger, ctx, clientKeysMapper, cacheServerPk, cacheObjKey, data, dataChangeSet, isInitializing);
                    }
                }
            }
            else
            {
                foreach (MessageTableData data in messageData.TableData)
                {
                    ctx.ExecuteInTransaction((command2) =>
                    {
                        DbRowHandlerCache cacheServerPk = ctx.CreateDbRowHandlerCache(command2, new MessageRowDataAccessor(), KeyChoice.DefaultKey);
                        DbRowHandlerCache cacheObjKey = ctx.CreateDbRowHandlerCache(command2, new MessageRowDataAccessor(exludeServerPrimaryKey: true), KeyChoice.ObjKey);
                        IDbRowHandler<IgnoreMessageIn> ignoreMessageInRowHandler = ctx.CreateDbRowHandler<IgnoreMessageIn>(command2);

                        if (ShouldProcessMessageIn(ignoreMessageInRowHandler, data))
                        {
                            SyncRowData(logger, ctx, clientKeysMapper, cacheServerPk, cacheObjKey, data, dataChangeSet, isInitializing);
                        }
                    });
                }
            }
        }

        private MessageData GetMessageData(IMetaModel metaModel, ILogger logger)
        {
            MessageData data = new MessageData();

            JObject tableData = ReadDataAsJson();

            if (tableData != null)
            {
                tableData = tableData["sync"] as JObject;
            }

            if (tableData == null)
            {
                return null;
            }

            data.TableData = MessageUtils.ExtractTableData(metaModel, tableData, logger);

            if (data.TableData == null)
            {
                return null;
            }

            return data;
        }

        private bool ShouldProcessMessageIn(IDbRowHandler<IgnoreMessageIn> rowHandler, MessageTableData data)
        {
            IgnoreMessageIn row = CreateIgnoreMessageInRow(data);
            if (row != null && rowHandler.Exists(row))
            {
                rowHandler.DeleteRow(row);
                return false;
            }

            return true;
        }

        private void SyncRowData(ILogger logger, FwDataContext ctx, IClientKeysMapper keyMapper, DbRowHandlerCache cacheServerPk, DbRowHandlerCache cacheObjKey, MessageTableData data, DataChangeSet dataChangeSet, bool isInitializing)
        {
            if (keyMapper != null)
            {
                data = keyMapper.MapServerToClientKeys(data);
            }
            //temp-fix for table name
            string result = string.Concat(data.TableName.ToCharArray().Select((x, i) => i > 0 && char.IsUpper(x) ? "_" + x.ToString() : x.ToString()));
            data.TableName = result;
            IMetaTable table = ctx.Model.GetTable(data.TableName);

            switch (MessageType)
            {
                case MessageType.INSERT:
                    SyncInsertRowData(logger, table, cacheServerPk, data, ctx, isInitializing);
                    break;
                case MessageType.UPDATE:
                    SyncUpdateRowData(logger, table, cacheServerPk, cacheObjKey, data, ctx, isInitializing);
                    break;
                case MessageType.DELETE:
                    SyncDeleteRowData(logger, table, cacheServerPk, cacheObjKey, data, ctx, keyMapper);
                    break;
                default:
                    throw new ArgumentException(string.Format(CultureInfo.InvariantCulture, Strings.InvalidMessageType, MessageType));
            }

            if (dataChangeSet != null)
            {
                if (data.RowData.SyncRowId.HasValue)
                {
                    dataChangeSet.AddRow(table, data.RowData.SyncRowId.Value);
                }
                else
                {
                    dataChangeSet.AddTable(table);
                }
            }
        }

        private void SyncInsertRowData(ILogger logger, IMetaTable table, DbRowHandlerCache cacheServerPk, MessageTableData data, FwDataContext ctx, bool isInitializing)
        {
            ObjPrimaryKey key = ObjPrimaryKey.FromPrimaryKey(table, data.RowData.ColumnData);
            if (key != null)
            {
                if (!isInitializing)
                {
                    // This will make sure that we update any reference columns with client keys, which will preserve references
                    data.RowData.ColumnData = CheckAndMapReferencedClientKeys(ctx, table, data.RowData.ColumnData);
                }
                
                DbRowHandler rowHandler = cacheServerPk.GetHandler(table);
                rowHandler.Upsert(data.RowData);
            }
            else
            {
                logger.Warning($"SyncInsert missing primary key for {table.TableName} record. Ignoring message.");
            }
        }

        private IReadOnlyDictionary<string, object> CheckAndMapReferencedClientKeys(FwDataContext ctx, IMetaTable table, IReadOnlyDictionary<string, object> input)
        {
            Dictionary<string, object> columnData = input.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            List<IMetaRelation> relations = table.Model.GetRelations(table).Where(x => x.RelationType == RelationType.Reference).ToList();

            foreach (IMetaRelation relation in relations.Where(x => x != null))
            {
                // Enumerate through two IEnumerables using the Zip function and add the value pairs to a dictionary
                Dictionary<string, string> mappedAttributePairs = 
                    relation.ReferencedColumns.Zip(relation.Columns, (refCol, col) => new KeyValuePair<string, string>(refCol.ColumnName, col.ColumnName)).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                Dictionary<string, object> refKeys = new Dictionary<string, object>();
                foreach (KeyValuePair<string, string> kvp in mappedAttributePairs)
                {
                    // Extract the values from the current record, but add them with the column names of the parent record
                    if (columnData.ContainsKey(kvp.Value))
                    {
                        refKeys.Add(kvp.Key, columnData[kvp.Value]);
                    }
                }

                string parentKeyRef = ObjPrimaryKey.FromPrimaryKey(relation.ReferencedTable, refKeys)?.ToKeySeparatedValues();

                if (parentKeyRef == null || relation.ReferencedTable == null)
                {
                    continue;
                }

                ClientKeysMap mapping = ctx.ClientKeysMap.FirstOrDefault(x => x.TableName == MessageUtils.ClientTableNameToServerTableName(relation.ReferencedTable.TableName) && x.ServerKeys == parentKeyRef);

                if (mapping != null)
                {
                    ObjPrimaryKey clientKeys = ObjPrimaryKey.FromKeySeparatedValues(relation.ReferencedTable, mapping.ClientKeys);

                    foreach (Tuple<IMetaDataMember, object> keyAttribute in clientKeys.Values)
                    {
                        if (mappedAttributePairs.ContainsKey(keyAttribute.Item1.ColumnName))
                        {
                            string refColName = mappedAttributePairs[keyAttribute.Item1.ColumnName];
                            columnData[refColName] = keyAttribute.Item2;
                        }
                        else
                        {
                            Logger.Current.Warning($"Could not map key '${keyAttribute.Item1.ColumnName} for reference when inserting a new record into '{table.TableName}'");
                        }
                    }
                }
            }

            return columnData;
        }

        private void SyncUpdateRowData(ILogger logger, IMetaTable table, DbRowHandlerCache cacheServerPk, DbRowHandlerCache cacheObjKey, MessageTableData data, FwDataContext ctx, bool isInitializing)
        {
            ObjPrimaryKey key = GetPrimaryKey(table, ctx, data, out bool usingObjKey);
            if (key != null)
            {
                RemoteRow row = ctx.Find(key) as RemoteRow;

                if (row == null)
                {
                    // It could be a server key sent, so check with client keys too
                    string clientKeyRef = ctx.ClientKeysMap.Where(x => x.TableName == MessageUtils.ClientTableNameToServerTableName(table.TableName) && x.ServerKeys == key.ToKeySeparatedValues()).Select(x => x.ClientKeys).FirstOrDefault();
                    row = clientKeyRef != null ? ctx.Find(ObjPrimaryKey.FromKeySeparatedValues(table, clientKeyRef)) as RemoteRow : null;
                }

                if (!isInitializing && row != null)
                {
                    // This will make sure that we don't update any reference columns with server keys, which will break the reference
                    data.RowData.ColumnData = RemoveReferenceColumnData(ctx, table, data.RowData.ColumnData, row);
                }

                data.RowData.SyncRowId = row?.RowId;
                DbRowHandler rowHandler = usingObjKey ? cacheObjKey.GetHandler(table) : cacheServerPk.GetHandler(table);
                rowHandler.Update(data.RowData);
            }
            else
            {
                logger.Warning($"SyncUpdate missing primary key for {table.TableName} record. Ignoring message.");
            }
        }

        private IReadOnlyDictionary<string, object> RemoveReferenceColumnData(FwDataContext ctx, IMetaTable table, IReadOnlyDictionary<string, object> input, RemoteRow row)
        {
            Dictionary<string, object> columnData = new Dictionary<string, object>();

            foreach (KeyValuePair<string, object> kvp in input)
            {
                IMetaDataMember column = table.DataMembers.Where(x => x.ColumnName == kvp.Key).FirstOrDefault();

                bool add = true;
                if (column != null && column.IsReferenceSource && kvp.Value != null)
                {
                    // Store current value before replacing with incoming value
                    object currentColumnValue = row[column.PropertyName];

                    // Replace the value in the existing row
                    row[column.PropertyName] = kvp.Value;

                    // This source column is part of one or more references
                    // Therefore, find all the references that it is relevant to
                    foreach (IMetaRelation relation in table.Model.GetRelations(table).Where(x => x.RelationType == RelationType.Reference && x.Columns.Any(y => y.ColumnName == kvp.Key)))
                    {
                        // Get the primary key of the record at the destination of the reference
                        ObjPrimaryKey pk = ObjPrimaryKey.FromRelation(table.Model, relation, row);

                        // Check if there's an entry in the client key map for this primary key
                        if (ctx.ClientKeysMap.Any(x => x.TableName == MessageUtils.ClientTableNameToServerTableName(relation.ReferencedTable.TableName) && x.ServerKeys == pk.ToKeySeparatedValues()))
                        {
                            // If there is an entry, that means most likely the destination record has been created in the client
                            // and updating this column would break that reference, so we ignore updates for that column
                            add = false;
                            break;
                        }
                    }
                }

                if (add)
                {
                    columnData.Add(kvp.Key, kvp.Value);
                }
            }

            return new ReadOnlyDictionary<string, object>(columnData);
        }

        private void SyncDeleteRowData(ILogger logger, IMetaTable table, DbRowHandlerCache cacheServerPk, DbRowHandlerCache cacheObjKey, MessageTableData data, FwDataContext ctx, IClientKeysMapper keyMapper)
        {
            ObjPrimaryKey key = GetPrimaryKey(table, ctx, data, out bool usingObjKey);
            if (key != null)
            {
                DbRowHandler rowHandler = usingObjKey ? cacheObjKey.GetHandler(table) : cacheServerPk.GetHandler(table);
                rowHandler.Delete(data.RowData);
                UnregisterKeyMapping(ctx, rowHandler.Command, keyMapper, key);
            }
            else
            {
                logger.Warning($"SyncDelete missing primary key for {table.TableName} record. Ignoring message.");
            }
        }

        private static ObjPrimaryKey GetPrimaryKey(IMetaTable table, FwDataContext ctx, MessageTableData data, out bool usingObjKey)
        {
            string objKey = data.GetRowValue(nameof(RemoteRow.ObjKey));
            if (!string.IsNullOrEmpty(objKey))
            {
                // Objkey found in the payload and try to fetch data from the table
                ObjPrimaryKey key = GetPrimaryKeyFromObjKey(ctx, table, objKey);
                if (key != null)
                {
                    usingObjKey = true;
                    return key;
                }
            }

            // Obj Key not found either in the payload or in the local table.
            usingObjKey = false;
            return ObjPrimaryKey.FromPrimaryKey(table, data.RowData.ColumnData);
        }

        private static ObjPrimaryKey GetPrimaryKeyFromObjKey(FwDataContext ctx, IMetaTable table, string objKey)
        {
            IQueryable<RemoteRow> dataTable = ctx.GetTable(table) as IQueryable<RemoteRow>;
            RemoteRow rowToDelete = dataTable == null ? null : dataTable.FirstOrDefault(x => x.ObjKey == objKey);
            return rowToDelete != null ? ObjPrimaryKey.FromPrimaryKey(table, rowToDelete) : null;
        }
    }
}
