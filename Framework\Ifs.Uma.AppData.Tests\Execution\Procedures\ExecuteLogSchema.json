{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<DoLog>": {"name": "DoLog", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"value": "TestValue1"}}, "assign": "Var1"}, {"call": {"method": "log", "args": {"msg": "Log value '${Var1}'"}}}]}]}, "Function<DoDebug>": {"name": "DoDebug", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"value": "TestValue2"}}, "assign": "Var1"}, {"call": {"method": "debug", "args": {"msg": "Debug value '${Var1}'"}}}]}]}, "Function<DoTrace>": {"name": "DoTrace", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "set", "args": {"value": "TestValue3"}}, "assign": "Var1"}, {"call": {"method": "trace", "args": {"msg": "Trace value '${Var1}'"}}}]}]}}}}