<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>keyfile.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <CodeAnalysisRuleSet>..\FrameworkRules.ruleset</CodeAnalysisRuleSet>
    <DefineConstants>DEBUG;TRACE;REMOTE_ASSISTANCE;SIGNATURE_SERVICE;LIDAR_SERVICE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DocumentationFile>bin\Release\netstandard1.3\Ifs.Uma.Framework.XML</DocumentationFile>
    <CodeAnalysisRuleSet>..\FrameworkRules.Release.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DefineConstants>TRACE;REMOTE_ASSISTANCE;SIGNATURE_SERVICE;LIDAR_SERVICE</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\UmaAssemblyInfo.cs" Link="Properties\UmaAssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Licenses.txt" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="itext7.bouncy-castle-adapter" Version="8.0.2" />
    <PackageReference Include="itext7.pdfhtml" Version="5.0.2" />
    <PackageReference Include="Microsoft.ApplicationInsights.DependencyCollector" Version="2.22.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.PerfCounterCollector" Version="2.22.0" />
    <PackageReference Include="Prism.Core" Version="7.0.0.396" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.0.2" />
    <PackageReference Include="System.Runtime.Serialization.Primitives" Version="4.3.0" />
    <PackageReference Include="Unity" Version="5.8.13" />
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.22.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Ifs.Uma.AppData\Ifs.Uma.AppData.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Comm.TouchApps\Ifs.Uma.Comm.TouchApps.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Data\Ifs.Uma.Data.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Metadata\Ifs.Uma.Metadata.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Reporting\Ifs.Uma.Reporting.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Services\Ifs.Uma.Services.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Signing\Ifs.Uma.Signing.csproj" />
    <ProjectReference Include="..\Ifs.Uma.UI\Ifs.Uma.UI.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Utility\Ifs.Uma.Utility.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Localization\Ifs.Uma.Localization.csproj" />
  </ItemGroup>
</Project>