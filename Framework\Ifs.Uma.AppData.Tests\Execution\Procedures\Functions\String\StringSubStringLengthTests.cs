﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    internal class StringSubStringLengthTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringSubStringLengthTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("HelloWorld", 1, 10, ExpectedResult = "HelloWorld")]
        [TestCase("HelloWorld", 1, 3, ExpectedResult = "Hel")]
        [TestCase("HelloWorld", 6, 8, ExpectedResult = "Wor")]
        [TestCase("HelloWorld", 6, 10, ExpectedResult = "World")]
        [TestCase("HelloWorld", 6, 11, ExpectedResult = "World")]
        [TestCase("HelloWorld", 0, 3, ExpectedResult = "HelloWorld")]
        [TestCase("HelloWorld", -1, 3, ExpectedResult = "HelloWorld")]
        [TestCase("HelloWorld", 1, 11, ExpectedResult = "HelloWorld")]
        [TestCase("HelloWorld", 3, 2, ExpectedResult = "HelloWorld")]
        [TestCase("ABCDEFG", 1, 3, ExpectedResult = "ABC")]
        [TestCase(null, 1, 3, ExpectedResult = null)]
        public async Task<string> String_SubStringLength(object input, int startPos, int endPos)
        {
            _params["TextInput"] = input;
            _params["StartPos"] = startPos;
            _params["EndPos"] = endPos;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_SubStringLength", _params);
            CheckResult(result);
            return result.Value?.ToString();
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
