﻿using System;
using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData
{
    public sealed class EntityQuery
    {
        public EntityDataSource DataSource { get; private set; }
        public IEnumerable<string> SelectAttributes { get; set; }
        public IEnumerable<string> Expand { get; set; }
        public IList<AttributeFilter> Filters { get; } = new List<AttributeFilter>();
        public IList<AttributeSort> Sorts { get; } = new List<AttributeSort>();
        internal JsonWhereExpression JsonWhereExpression { get; set; }
        public AttributeExpression FilterExpression { get; set; }
        public AttributeSearch Search { get; set; }
        public string EntitySetName { get; set; }
        public int? Skip { get; set; }
        public int? Take { get; set; }

        public EntityQuery(EntityDataSource dataSource)
        {
            if (dataSource == null) throw new ArgumentNullException(nameof(dataSource));

            DataSource = dataSource;
        }

        public void Select(params string[] members)
        {
            SelectAttributes = members.ToArray();
        }
        
        public void SetFilter(ObjPrimaryKey key)
        {
            if (key == null) throw new ArgumentNullException(nameof(key));
            if (key.Table != DataSource.Table) throw new ArgumentException(nameof(key) + " must be from the same table as the DataSource", nameof(key));

            SetFilter(key.Values);
        }

        public void SetFilter(IEnumerable<Tuple<IMetaDataMember, object>> filter)
        {
            if (filter == null) throw new ArgumentNullException(nameof(filter));

            Filters.Clear();
            
            foreach (Tuple<IMetaDataMember, object> keyValue in filter)
            {
                Filters.Add(new AttributeFilter(keyValue.Item1.PropertyName, keyValue.Item2));
            }
        }

        public void SetFilter(IReadOnlyDictionary<string, object> attibuteValues)
        {
            if (attibuteValues == null) throw new ArgumentNullException(nameof(attibuteValues));

            Filters.Clear();

            foreach (var kvp in attibuteValues)
            {
                Filters.Add(new AttributeFilter(kvp.Key, kvp.Value));
            }
        }

        public void AddFilter(string attributeName, object value)
        {
            Filters.Add(new AttributeFilter(attributeName, value));
        }

        public void AddSort(string attributeName, ESortOrder sortOrder)
        {
            Sorts.Add(new AttributeSort(attributeName, sortOrder));
        }

        public void AddSorts(IEnumerable<CpiKeyValue> keyValues)
        {
            foreach (CpiKeyValue cpiKeyValue in keyValues)
            {
                AddSort(cpiKeyValue.Key, string.Equals(cpiKeyValue.Value, "desc", StringComparison.OrdinalIgnoreCase) ? ESortOrder.Descending : ESortOrder.Ascending);
            }
        }

        public IReadOnlyList<string> GetRequiredExpands()
        {
            List<string> expands = new List<string>();
            if (Expand != null)
            {
                expands.AddRange(Expand);
            }

            if (SelectAttributes != null)
            {
                foreach (string attribute in SelectAttributes)
                {
                    string[] refParts = attribute.Split('.');
                    if (refParts.Length == 2 && !expands.Contains(refParts[0]))
                    {
                        expands.Add(refParts[0]);
                    }
                }
            }

            return expands;
        }

        internal PreparedEntityQuery Prepare(int? defaultTake = null)
        {
            return new PreparedEntityQuery(this, defaultTake, true);
        }

        public EntityQueryResult ApplyTo(IEnumerable<EntityRecord> records)
        {
            List<EntityRecord> result = new List<EntityRecord>();

            AttributeExpression whereExpression = GetWhereExpression();

            foreach (EntityRecord record in records)
            {
                if (record.Row.TableName != DataSource.Table.TableName)
                {
                    continue;
                }

                if (whereExpression == null || whereExpression.Match(DataSource, record))
                {
                    result.Add(record);
                }
            }

            if (Sorts.Count > 0)
            {
                result.Sort((x, y) =>
                {
                    foreach (AttributeSort sort in Sorts)
                    {
                        int compare = sort.Compare(DataSource, x, y);
                        if (compare != 0)
                        {
                            return compare;
                        }
                    }

                    return 0;
                });
            }

            int skip = Skip.GetValueOrDefault(0);
            int take = Take.GetValueOrDefault(result.Count);
            bool hasMoreResults = (skip + take) < result.Count;

            EntityRecord[] queryResult = result.Skip(skip).Take(take).ToArray();
            return new EntityQueryResult(this, queryResult, hasMoreResults);
        }

        public AttributeExpression GetWhereExpression()
        {
            AttributeExpression expression = null;

            foreach (AttributeFilter filter in Filters)
            {
                AttributeExpression filterExpression = filter.ToAttributeExpression();
                expression = expression == null ? filterExpression : AttributeExpression.And(expression, filterExpression);
            }

            if (Search != null && !string.IsNullOrWhiteSpace(Search.SearchTerm))
            {
                AttributeExpression searchExpression = Search.ToAttributeExpression(DataSource);
                expression = expression == null ? searchExpression : AttributeExpression.And(expression, searchExpression);
            }

            if (FilterExpression != null)
            {
                expression = expression == null ? FilterExpression : AttributeExpression.And(expression, FilterExpression);
            }

            return expression;
        }

        public EntityQuery Clone()
        {
            EntityQuery query = new EntityQuery(DataSource);
            query.SelectAttributes = SelectAttributes?.ToArray();
            query.Expand = Expand?.ToArray();
            query.FilterExpression = FilterExpression;
            query.JsonWhereExpression = JsonWhereExpression;
            query.Search = Search;

            foreach (var item in Filters)
            {
                query.Filters.Add(item);
            }

            foreach (var item in Sorts)
            {
                query.Sorts.Add(item);
            }

            query.Skip = Skip;
            query.Take = Take;
            query.EntitySetName = DataSource.EntitySetName ?? EntitySetName;
            return query;
        }

        public void ChangeDataSource(FunctionDataSource dataSource)
        {
            DataSource = dataSource;
        }
    }
}
