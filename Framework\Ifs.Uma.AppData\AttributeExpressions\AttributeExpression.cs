﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.AttributeExpressions
{
    public abstract partial class AttributeExpression
    {
        public abstract AttributeExpressionType Type { get; }
        
        public static AttributeCompareExpression Compare(string attributeName, AttributeCompareOperator comparison, object value)
        {
            return new AttributeCompareExpression(attributeName, comparison, value);
        }

        public static AttributeLikeExpression Like(string attributeName, string value)
        {
            return new AttributeLikeExpression(attributeName, value);
        }

        public static AttributeLogicalExpression Or(AttributeExpression left, AttributeExpression right)
        {
            return new AttributeLogicalExpression(left, AttributeLogicalOperator.Or, right);
        }

        public static AttributeLogicalExpression And(AttributeExpression left, AttributeExpression right)
        {
            return new AttributeLogicalExpression(left, AttributeLogicalOperator.And, right);
        }

        public static AttributeExpression FromObjPrimaryKey(ObjPrimaryKey objPrimaryKey)
        {
            AttributeExpression itemAttributeExpression = null;
            foreach (Tuple<IMetaDataMember, object> primaryKeyValue in objPrimaryKey.Values)
            {
                AttributeCompareExpression primaryKeyValueExpression = Compare(primaryKeyValue.Item1.PropertyName, AttributeCompareOperator.Equals, primaryKeyValue.Item2);
                if (itemAttributeExpression == null)
                {
                    itemAttributeExpression = primaryKeyValueExpression;
                }
                else
                {
                    // AND the primary key values
                    itemAttributeExpression = And(itemAttributeExpression, primaryKeyValueExpression);
                }
            }

            return itemAttributeExpression;
        }

        public static AttributeExpression FromObjPrimaryKeys(IEnumerable<ObjPrimaryKey> objPrimaryKeys)
        {
            AttributeExpression attributeExpression = null;

            foreach (ObjPrimaryKey objPrimaryKey in objPrimaryKeys)
            {
                AttributeExpression primaryKeyAttributeExpression = FromObjPrimaryKey(objPrimaryKey);

                if (attributeExpression == null)
                {
                    attributeExpression = primaryKeyAttributeExpression;
                }
                else
                {
                    // OR the primary keys
                    attributeExpression = Or(attributeExpression, primaryKeyAttributeExpression);
                }
            }

            return attributeExpression;
        }

        internal abstract bool Match(EntityDataSource dataSource, EntityRecord record);
        
        internal abstract Expression ToExpression(EntityDataSource dataSource);
    }
}
