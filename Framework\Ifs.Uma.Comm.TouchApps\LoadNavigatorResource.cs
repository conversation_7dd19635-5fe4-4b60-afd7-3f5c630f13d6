﻿using System.Runtime.Serialization;
using Ifs.Cloud.Client.Entities;

namespace Ifs.Uma.Comm.TouchApps
{
    [DataContract]
    public class LoadNavigatorResource : BaseResource
    {
        protected virtual string ResourceSection { get; } = "system";
        public override string ResourceName => $"MobileClientRuntime.svc/LoadNavigator(AppName='{AppName}',ScopeId='{ScopeId}')";
        public override bool SingleResponse { get { return true; } }
        public string AppName { get; set; }
        public string ScopeId { get; set; }

        [DataMember(Name = "@odata.context")]
        public string Context { get; set; }

        [DataMember(Name = "value")]
        public string Value { get; set; }
    }
}
