﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Observables;

namespace Ifs.Uma.Framework.UI.Assistants
{
    public interface IAssistantRunner
    {
        Task<bool> ExecuteAsync(AssistantCommand command);
    }

    public class AssistantCommandBlock : CommandBlock
    {
        public AssistantCommandItem PreviousCommandItem { get; }
        public AssistantCommandItem NextCommandItem { get; }
        public AssistantCommandItem FinishCommandItem { get; private set; }
        public AssistantCommandItem CancelCommandItem { get; private set; }
        public AssistantCommandItem RestartCommandItem { get; }

        private readonly ICommandExecutor _commandExecutor;
        private readonly IAssistantRunner _assistantRunner;
        private readonly IExpressionRunner _expressionRunner;

        public AssistantCommandBlock(UpdatingState parentUpdatingState, IMetadata metadata, IAssistantRunner assistantRunner, ICommandExecutor commandExecutor, IExpressionRunner expressionRunner)
            : base(parentUpdatingState, metadata, null, expressionRunner)
        {
            _commandExecutor = commandExecutor;
            _assistantRunner = assistantRunner;
            _expressionRunner = expressionRunner;

            PreviousCommandItem = new AssistantCommandItem(AssistantCommand.Previous, _assistantRunner, _expressionRunner, _commandExecutor);
            NextCommandItem = new AssistantCommandItem(AssistantCommand.Next, _assistantRunner, _expressionRunner, _commandExecutor);
            RestartCommandItem = new AssistantCommandItem(AssistantCommand.Restart, _assistantRunner, _expressionRunner, _commandExecutor);
        }

        public void Load(CpiAssistant assistant)
        {
            if (assistant == null) throw new ArgumentNullException(nameof(assistant));

            using (CommandGroups.DeferRefresh())
            {
                CommandGroups.Clear();
                Commands.Clear();

                bool hasCustomCommands = assistant.CommandGroups != null && assistant.CommandGroups.Any(x => x.CommandNames.Length > 0);

                if (assistant.IsDynamic || (assistant.Steps.Count() >= 1 && !hasCustomCommands))
                {
                    if (assistant.IsDynamic)
                    {
                        PreviousCommandItem.IsDynamicAssistant = true;
                        NextCommandItem.IsDynamicAssistant = true;
                        RestartCommandItem.IsDynamicAssistant = true;
                    }

                    AddCommand(PreviousCommandItem);
                    AddCommand(NextCommandItem);

                    FinishCommandItem = new AssistantCommandItem(AssistantCommand.Finish, _assistantRunner, _expressionRunner, _commandExecutor, assistant.FinishCommand);
                    FinishCommandItem.IsDynamicAssistant = assistant.IsDynamic;
                    OnPropertyChanged(nameof(FinishCommandItem));
                    AddCommand(FinishCommandItem);

                    CancelCommandItem = new AssistantCommandItem(AssistantCommand.Cancel, _assistantRunner, _expressionRunner, _commandExecutor, assistant.CancelCommand);
                    CancelCommandItem.IsDynamicAssistant = assistant.IsDynamic;
                    OnPropertyChanged(nameof(CancelCommandItem));
                    AddCommand(CancelCommandItem);

                    AddCommand(RestartCommandItem);

                    CommandGroup group = new CommandGroup();
                    foreach (FwCommandItem command in Commands)
                    {
                        group.Add(command);
                    }

                    CommandGroups.Add(group);

                    UpdateStates();
                }
                else
                {
                    PreviousCommandItem.IsVisible = false;
                    NextCommandItem.IsVisible = false;
                    RestartCommandItem.IsVisible = false;
                }
            }
        }

        public void LoadStep(AssistantStep step)
        {
            foreach (AssistantCommandItem command in Commands.OfType<AssistantCommandItem>())
            {
                command.CurrentStep = step;
            }

            UpdateStates();
        }

        public bool IsVisible(AssistantCommand assistantCommand)
        {
            return Commands.OfType<AssistantCommandItem>().FirstOrDefault(x => x.CommandType == assistantCommand)?.IsVisible ?? false;
        }

        public bool IsEnabled(AssistantCommand assistantCommand)
        {
            return Commands.OfType<AssistantCommandItem>().FirstOrDefault(x => x.CommandType == assistantCommand)?.IsEnabled ?? false;
        }

        public bool IsVisibleAndEnabled(AssistantCommand assistantCommand)
        {
            var item = Commands.OfType<AssistantCommandItem>().FirstOrDefault(x => x.CommandType == assistantCommand);
            if (item != null)
            {
                return item.IsVisible && item.IsEnabled;
            }

            return false;
        }

        protected override void UpdateStates()
        {
            if (FinishCommandItem?.CurrentStep?.Assistant.CpiAssistant.FinishCommand?.Label != null)
            {
                FinishCommandItem.Text = _expressionRunner.InterpolateString(FinishCommandItem.CurrentStep.Assistant.CpiAssistant.FinishCommand.Label, FinishCommandItem.CurrentStep.Data.DefaultViewData.Record);
            }

            base.UpdateStates();
        }
    }
}
