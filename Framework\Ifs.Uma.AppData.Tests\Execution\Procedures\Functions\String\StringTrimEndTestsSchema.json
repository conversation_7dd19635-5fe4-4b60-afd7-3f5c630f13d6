{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_TrimEnd>": {"name": "String_TrimEnd", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "TrimEnd", "paramsArray": ["${TextInput}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<String_TrimEnd2>": {"name": "String_TrimEnd", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "TrimParameter", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Text"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "TrimEnd", "paramsArray": ["${TextInput}", "${TrimParameter}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}