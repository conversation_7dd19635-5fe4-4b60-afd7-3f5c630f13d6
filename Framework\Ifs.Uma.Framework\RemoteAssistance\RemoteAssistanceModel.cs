﻿#if REMOTE_ASSISTANCE
using System.Runtime.Serialization;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    public class RemoteAssistanceNotification
    {
        public string RequestId { get; set; }
        public string SessionId { get; set; }
        public string SessionToken { get; set; }
        public CallerInfo FromUserInfo { get; set; }
        public RemoteAssistanceNotificationCategory NotificationCategory { get; set; }
        public RemoteAssistanceReasonCode ReasonCode { get; set; }
        public string RefUrl { get; set; }
        public string Message { get; set; }
    }

    public class RemoteAssistanceSessionInfo
    {
        public string SessionId { get; }
        public string SessionToken { get; }
        public string RequestId { get; }
        public string UserToken { get; }
        public string ServerUrl { get; }
        public string ApiKey { get; }
        public string MediaAttachmentLu { get; }
        public string MediaAttachmentKeyRef { get; }

        public RemoteAssistanceSessionInfo(string sessionId, string sessionToken, string requestId, string userToken, string serverUrl, string apiKey, string mediaAttachmentLu, string mediaAttachmentKeyRef)
        {
            SessionId = sessionId;
            SessionToken = sessionToken;
            RequestId = requestId;
            UserToken = userToken;
            ServerUrl = serverUrl;
            ApiKey = apiKey;
            MediaAttachmentLu = mediaAttachmentLu;
            MediaAttachmentKeyRef = mediaAttachmentKeyRef;
        }
    }

    public class AcceptCallStructure
    {
        public string UserToken { get; }
        public string SessionToken { get; }
        public string ServerUrl { get; }
        public string ApiKey { get; }
        public string MediaAttachmentLu { get; }
        public string MediaAttachmentKeyRef { get; }

        public AcceptCallStructure(string userToken, string sessionToken, string serverUrl, string apiKey, string mediaAttachmentLu, string mediaAttachmentKeyRef)
        {
            UserToken = userToken;
            SessionToken = sessionToken;
            ServerUrl = serverUrl;
            ApiKey = apiKey;
            MediaAttachmentLu = mediaAttachmentLu;
            MediaAttachmentKeyRef = mediaAttachmentKeyRef;
        }
    }

    public class CallerInfo
    {
        public string FndUserId { get; set; }
        public string FndUserName { get; set; }
    }

    public enum RemoteAssistanceNotificationCategory
    {
        [EnumMember(Value = "VIDEO_REQUESTED")]
        VideoRequested,
        [EnumMember(Value = "VIDEO_REQUEST_CANCELLED")]
        VideoRequestCancelled,
        [EnumMember(Value = "VIDEO_REQUEST_DECLINED")]
        VideoRequestDeclined,
        [EnumMember(Value = "VIDEO_REQUEST_ACCEPTED")]
        VideoRequestAccepted
    }

    public enum RemoteAssistanceReasonCode
    {
        [EnumMember(Value = "DECLINED")]
        Declined,
        [EnumMember(Value = "BUSY")]
        Busy,
        [EnumMember(Value = "NOT_ANSWERED")]
        NotAnswered,
        [EnumMember(Value = "EXPERT_NOT_AVAILABLE")]
        ExpertNotAvailable
    }
}
#endif
