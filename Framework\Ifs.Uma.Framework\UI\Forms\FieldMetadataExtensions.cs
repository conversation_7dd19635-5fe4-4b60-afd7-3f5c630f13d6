﻿using Ifs.Uma.Database;
using Ifs.Uma.Framework.UI.Forms;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Model;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;
using System;
using System.Globalization;
using System.Linq;

namespace Ifs.Uma.Framework.UI
{
    public static class FieldMetadataExtensions
    {
        public static Field CreateFieldType(this IMetadata metadata, string projectionName, CpiField fieldDef, int id)
        {
            Field field = CreateField(metadata, projectionName, fieldDef);
            if (fieldDef.Attribute != null)
            {
                field.Id = fieldDef.Attribute + "_" + id;
                field.Attribute = fieldDef.Attribute;
            }

#if SIGNATURE_SERVICE
            if (fieldDef.ExcludeFromSignatureMapping.HasValue)
            {
                field.ExcludeFromSignatureMapping = (bool)fieldDef.ExcludeFromSignatureMapping;
            }

            if (fieldDef.SignatureMappingAttribute != null)
            {
                field.SignatureMappingAttribute = fieldDef.SignatureMappingAttribute;
            }
#endif

            if (metadata.GetEntityTimeZoneType(projectionName, fieldDef.Entity) == TimezoneRefType.Site)
            {
                field.IsSiteItem = true;
            }
            else
            {
                field.IsSiteItem = false;
            }        

            return field;
        }

        private static Field CreateField(this IMetadata metadata, string projectionName, CpiField fieldDef)
        {
            if (fieldDef == null)
                throw new ArgumentNullException(nameof(fieldDef));

            if (fieldDef.Control == CpiControlType.Badge)
            {
                return new BadgeField();
            }

            if (fieldDef.Control == CpiControlType.Image)
            {
                return new MediaPickerField()
                {
                    IsImageField = true
                };
            }

            if (fieldDef.Control == CpiControlType.Signature)
            {
                return new SignatureField();
            }

            if (fieldDef.Control == CpiControlType.ComputedField)
            {
                return new ComputedField();
            }

            if (fieldDef.Control == CpiControlType.ItemPicker)
            {
                return new ItemPickerField();
            }

            if (fieldDef.Control == CpiControlType.AddressField)
            {
                return new AddressField();
            }

            if (fieldDef.Datatype == CpiDataType.Lookup || fieldDef.Control == CpiControlType.Lookup)
            {
                return new LovField();
            }

            switch (fieldDef.Datatype)
            {
                case CpiDataType.Boolean:
                    BoolField booleanField = new BoolField();

                    if (fieldDef.Required != null && fieldDef.Required.QuickCheck(false))
                    {
                        booleanField.IsRequired = true;
                    }
                    else
                    {
                        booleanField.IsRequired = false;
                    }

                    if (!string.IsNullOrEmpty(fieldDef.TrueLabel))
                    {
                        booleanField.TrueLabel = fieldDef.TrueLabel;
                    }

                    if (!string.IsNullOrEmpty(fieldDef.FalseLabel))
                    {
                        booleanField.FalseLabel = fieldDef.FalseLabel;
                    }

                    return booleanField;
                case CpiDataType.Timestamp:
                    if (metadata.IsEntityKnownTimeZone(fieldDef.Entity, projectionName) || metadata.IsStructureKnownTimeZone(fieldDef.Entity, projectionName))
                    {
                        return new KnownDateTimeField();
                    }
                    else
                    {
                        return new DateTimeField();
                    }
                case CpiDataType.Date:
                    return new DateField();
                case CpiDataType.Time:
                    return new TimeField();
                case CpiDataType.TimestampUtc:
                    return new DateTimeFieldUtc();
                case CpiDataType.Integer:
                    LongField integerField = new LongField()
                    {
                        // Default to signed field
                        IsSigned = true
                    };
                    return integerField;
                case CpiDataType.Number:
                    DoubleField numberField = new DoubleField
                    {
                        // Default to signed field
                        IsSigned = true
                    };

                    var attribute = metadata.FindAttribute(projectionName, fieldDef.Entity, fieldDef.Attribute);
                    if (attribute != null && attribute.Scale.HasValue)
                    {
                        numberField.DecimalPlaces = attribute.Scale.Value;
                    }

                    if (fieldDef.Format.HasValue)
                    {
                        switch (fieldDef.Format.Value)
                        {
                            case CpiFormat.IfsCurrency:
                                numberField.NumberFormat = NumberFormat.Currency;
                                break;
                            case CpiFormat.Percentage:
                                numberField.NumberFormat = NumberFormat.Percentage;
                                break;
                            case CpiFormat.Decimal:
                                numberField.NumberFormat = NumberFormat.Decimal;
                                break;
                        }
                    }
                    return numberField;
                case CpiDataType.Enumeration:
                    SelectableItem<object>[] enumerationValues = new SelectableItem<object>[0];

                    if (fieldDef.Entity != null && fieldDef.Attribute != null)
                    {
                        IMetaTable table = metadata.GetTableForEntityName(fieldDef.Entity);
                        IMetaDataMember member = table?.FindMemberByPropertyName(fieldDef.Attribute);
                        if (member != null && member.Enumeration != null)
                        {
                            enumerationValues = member.Enumeration.Values.Where(k => !k.ClientOnly)
                                .Select(x => new SelectableItem<object>(
                                    string.IsNullOrEmpty(x.DisplayName) ? string.Format(CultureInfo.InvariantCulture, "[{0}]", x.LocalValue) : x.DisplayName,
                                    x.LocalValue))
                                .ToArray();
                        }
                    }

                    if (fieldDef.Control == CpiControlType.RadioGroup)
                    {
                        RadioGroupField radio = new RadioGroupField();
                        radio.ItemsSource = enumerationValues;
                        return radio;
                    }
                    else
                    {
                        ComboField combo = new ComboField();
                        combo.ItemsSource = enumerationValues;
                        return combo;
                    }
            }

            // Default to text field
            bool longText = fieldDef.Multiline || fieldDef.Datatype == CpiDataType.LongText;
            TextField field = longText ? new LargeTextEdit() : new TextField();

            if (fieldDef.Maxlength.HasValue)
            {
                field.MaxLength = fieldDef.Maxlength.Value;
            }

            if (fieldDef.HasHtmlText.HasValue)
            {
                field.HasHtmlText = fieldDef.HasHtmlText.HasValue;
                return new HtmlField();
            }

            if (fieldDef.Format.HasValue)
            {
                switch (fieldDef.Format.Value)
                {
                    case CpiFormat.Uppercase:
                        field.TextFormat = TextFormats.Uppercase;
                        break;
                    case CpiFormat.Lowercase:
                        field.TextFormat = TextFormats.Lowercase;
                        break;
                }
            }

            if (fieldDef.Hint.HasValue)
            {
                switch (fieldDef.Hint)
                {
                    case CpiHint.Email:
                        field.ContentType = ContentType.Email;
                        break;
                    case CpiHint.Name:
                        field.ContentType = ContentType.Name;
                        break;
                    case CpiHint.Url:
                        field.ContentType = ContentType.Url;
                        break;
                    case CpiHint.PhoneNumber:
                    case CpiHint.MobileNumber:
                        field.ContentType = ContentType.PhoneNumber;
                        break;
                    case CpiHint.UndialablePhoneNumber:
                        field.ContentType = ContentType.UndialablePhoneNumber;
                        break;
                    case CpiHint.Text:
                        field.ContentType = ContentType.Text;
                        break;
                    default:
                        field.ContentType = ContentType.Paragraph;
                        break;
                }
            }
            else if (field.TextFormat == TextFormats.None)
            {
                field.ContentType = ContentType.Paragraph;
            }

            return field;
        }

        public static void SetFieldEditability(this Field field, CpiCrudType allowedCrud, bool isPrimaryKey)
        {
            if (allowedCrud.CanUse(CpiCrudType.CreateAndUpdate) && !isPrimaryKey)
            {
                field.Editability = FieldEditability.InsertingOrUpdating;
            }
            else if (allowedCrud.CanUse(CpiCrudType.Create))
            {
                field.Editability = FieldEditability.Inserting;
            }
            else if (allowedCrud.CanUse(CpiCrudType.Update) && !isPrimaryKey)
            {
                field.Editability = FieldEditability.Updating;
            }
            else
            {
                field.Editability = FieldEditability.Never;
            }
        }

        public static bool IsLidarMediaAttributeSet(this Field field)
        {
            return !string.IsNullOrEmpty(field.LidarMediaAttribute);
        }

        public static byte[] GetLidarImage(this Field field)
        {
            byte[] image = null;
            string fieldValue = field.Value != null ? field.Value.ToString() : string.Empty;

            if (!string.IsNullOrEmpty(fieldValue) && field.LidarImage != null && field.LidarImage.ContainsKey(fieldValue))
            {
                field.LidarImage.TryGetValue(fieldValue, out image);
            }

            field.LidarImage = null;

            return image;
        }
    }
}
