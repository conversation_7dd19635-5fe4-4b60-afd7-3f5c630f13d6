{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_RegexLike>": {"name": "String_RegexLike", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "LikePattern", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexLike", "paramsArray": ["${TextInput}", "${LikePattern}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<String_RegexLike3>": {"name": "String_RegexLike", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "LikePattern", "dataType": "Text"}, {"name": "RegexOption", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "RegexLike", "paramsArray": ["${TextInput}", "${LikePattern}", "${RegexOption}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}