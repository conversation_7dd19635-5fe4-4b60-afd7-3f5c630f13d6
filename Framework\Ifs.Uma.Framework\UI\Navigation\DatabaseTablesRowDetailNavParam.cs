﻿using System.Collections.Generic;
using System.Runtime.Serialization;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Navigation
{
    [DataContract]
    public class DatabaseTablesRowDetailNavParam : NavigationParameter
    {
        [DataMember]
        public Dictionary<string, string> RowData { get; private set; }

        public DatabaseTablesRowDetailNavParam(Dictionary<string, string> rowData)
        {
            RowData = rowData;
        }
    }
}
