{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<Convert_ToNumber>": {"name": "Convert_ToNumber", "type": "Function", "params": [{"name": "TextInput"}], "layers": [{"vars": [{"name": "Result", "dataType": "Number"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Convert", "name": "ToNumber", "paramsArray": ["${TextInput}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}