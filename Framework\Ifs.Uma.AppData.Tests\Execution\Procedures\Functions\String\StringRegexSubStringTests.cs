﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringRegexSubStringTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringRegexSubStringTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("a,b,c", "[^,]+", ExpectedResult = "a")]
        [TestCase("a,b,c", null, ExpectedResult = null)]
        [TestCase("a,b,c", "", ExpectedResult = null)]
        [TestCase(null, null, ExpectedResult = null)]
        public async Task<string> String_RegexSubString(object input, string regexPattern)
        {
            _params["TextInput"] = input;
            _params["RegexPattern"] = regexPattern;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexSubString", _params);
            CheckResult(result);

            return result?.Value as string;
        }

        [Test]
        [TestCase("a,b,c", "[^,]+", 0, 1, ExpectedResult = "b")]
        [TestCase("a,b,c", "[^,]+", 2, 0, ExpectedResult = "b")]
        [TestCase("a,b,c", null, 0, 0, ExpectedResult = null)]
        [TestCase("a,b,c", "", 0, 0, ExpectedResult = null)]
        [TestCase("a,b,c", "[^,]", 0, 8, ExpectedResult = null)]
        [TestCase("a,b,c", "[^,]", -1, 0, ExpectedResult = null)]
        [TestCase(null, null, null, null, ExpectedResult = null)]
        public async Task<string> String_RegexSubString4(object input, string regexPattern, int position, int occurrence)
        {
            _params["TextInput"] = input;
            _params["RegexPattern"] = regexPattern;
            _params["Position"] = position;
            _params["Occurrence"] = occurrence;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexSubString4", _params);
            CheckResult(result);

            return result?.Value as string;
        }

        [Test]
        [TestCase("abc", "[B]", 0, 0, "in", ExpectedResult = "b")]
        [TestCase("abc", "[B]", 0, 0, null, ExpectedResult = null)]
        [TestCase("abcaBc", "[B]", 0, 0, null, ExpectedResult = "B")]
        [TestCase("abc", "", 0, 0, "in", ExpectedResult = null)]
        [TestCase("abc", null, 0, 0, "in", ExpectedResult = null)]
        [TestCase("abc", "[B]", -1, 0, "in", ExpectedResult = null)]
        [TestCase("abc", "[B]", 0, -1, "in", ExpectedResult = null)]
        [TestCase(null, null, null, null, null, ExpectedResult = null)]
        public async Task<string> String_RegexSubString5(object input, string regexPattern, int position, int occurrence, string regexOptions)
        {
            _params["TextInput"] = input;
            _params["RegexPattern"] = regexPattern;
            _params["Position"] = position;
            _params["Occurrence"] = occurrence;
            _params["RegexOptions"] = regexOptions;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexSubString5", _params);
            CheckResult(result);

            return result?.Value as string;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
