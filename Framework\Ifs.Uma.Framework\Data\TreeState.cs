﻿using System.Runtime.CompilerServices;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.Data
{
    public sealed class TreeState
    {
        private readonly ViewState _viewState;

        private readonly string _treeName;

        public TreeState(ViewState viewState, string treeName)
        {
            _viewState = viewState ?? new ViewState();
            _treeName = treeName;
        }

        public bool? IsTreeOpened
        {
            get
            {
                _viewState.TryGetValue(StateName("isTreeOpened"), out object value);

                if (MetadataExtensions.TryConvertToType(CpiDataType.Boolean, value, out object converted))
                {
                    return (bool)converted;
                }

                return null;
            }
            set => _viewState.Assign(StateName("isTreeOpened"), value);
        }

        public string StateName([CallerMemberName]string propertyName = null)
        {
            return ViewState.ViewStatePrefix + _treeName + "." + propertyName;
        }
    }
}
