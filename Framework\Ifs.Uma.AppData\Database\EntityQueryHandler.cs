﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData.Database
{
    internal class EntityQueryHandler
    {
        public EntityQueryHandler(DbCommand command, SqlBuilder builder, PreparedEntityQuery query)
        {
            if (command == null) throw new ArgumentNullException(nameof(command));
            if (query == null) throw new ArgumentNullException(nameof(query));
            if (builder == null) throw new ArgumentNullException(nameof(builder));

            _command = command;
            _query = query;
            _builder = builder;
        }

        private readonly DbCommand _command;
        private readonly PreparedEntityQuery _query;
        private readonly SqlBuilder _builder;

        public IEnumerable<EntityRecord> Select(CancellationToken token)
        {
            IReadOnlyList<EntityQueryColumn> queryColumns = _query.QueryColumns;
            ISelectSpec spec = _query.SelectSpec;
            _builder.BuildSql(_command, spec, ParameterUsage.Create);

            EntityQueryRow[] rows = queryColumns.GroupBy(x => x.RefName)
                .Select(x => new EntityQueryRow(x.Key, x))
                .ToArray();

            List<EntityRecord> result = new List<EntityRecord>();
            using (DbDataReader reader = _command.ExecuteReader())
            {
                while (reader.Read())
                {
                    if (token.IsCancellationRequested)
                    {
                        break;
                    }

                    EntityRecord record = ReadRecord(reader, rows);
                    result.Add(record);
                }
            }

            return result.AsEnumerable();
        }

        private EntityRecord ReadRecord(DbDataReader reader, EntityQueryRow[] queryRows)
        {
            RemoteRow mainRow = null;
            Dictionary<string, RemoteRow> refs = null;
            foreach (EntityQueryRow queryRow in queryRows)
            {
                RemoteRow row = queryRow.Read(reader);
                if (queryRow.RefName == null)
                {
                    mainRow = row;
                }
                else
                {
                    if (refs == null)
                    {
                        refs = new Dictionary<string, RemoteRow>();
                    }

                    refs[queryRow.RefName] = row;
                }
            }

            return new EntityRecord(mainRow, refs);
        }
    }

    internal sealed class EntityQueryColumn
    {
        public string RefName { get; }
        public IMetaTable Table { get; }
        public IMetaDataMember Member { get; }
        public int SelectIndex { get; }

        public EntityQueryColumn(string refName, IMetaTable table, IMetaDataMember member, int selectIndex)
        {
            RefName = refName;
            Member = member;
            SelectIndex = selectIndex;
            Table = table;
        }

        public override string ToString()
        {
            return RefName == null ? Member.PropertyName : RefName + "." + Member.PropertyName;
        }
    }

    internal sealed class EntityQueryRow
    {
        public string RefName { get; }

        private readonly EntityQueryColumn[] _queryColumns;
        private readonly EntityQueryColumn _pkColumn;

        public EntityQueryRow(string refName, IEnumerable<EntityQueryColumn> queryColumns)
        {
            RefName = refName;
            _queryColumns = queryColumns.ToArray();
            _pkColumn = _queryColumns.FirstOrDefault(x => x.Member.PrimaryKey);

            if (_pkColumn == null)
            {
                _pkColumn = _queryColumns.FirstOrDefault(x => x.Member.ServerPrimaryKey);
            }

            if (_pkColumn == null)
            {
                throw new InvalidMetadataException("Failed to primary key column when executing query");
            }
        }

        public RemoteRow Read(DbDataReader reader)
        {
            if (reader.IsDBNull(_pkColumn.SelectIndex))
            {
                return null;
            }

            RemoteRow row = (RemoteRow)_pkColumn.Table.CreateRow();

            foreach (EntityQueryColumn column in _queryColumns)
            {
                object value = column.Member.ConvertValue(reader.GetValue(column.SelectIndex));
                column.Member.SetValue(row, value);
            }

            return row;
        }
    }
}
