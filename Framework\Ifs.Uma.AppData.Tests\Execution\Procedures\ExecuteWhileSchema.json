{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "procedures": {"Function<FnTestWhile>": {"name": "FnTestWhile", "type": "Function", "params": [], "layers": [{"vars": [{"name": "WhileIndex", "dataType": "Integer", "collection": false}, {"name": "MaxIndex", "dataType": "Integer", "collection": false}], "execute": [{"call": {"method": "set", "args": {"value": 1}}, "assign": "WhileIndex"}, {"call": {"method": "set", "args": {"value": 3}}, "assign": "MaxIndex"}, {"call": {"method": "while", "args": {"expression": {"<=": [{"var": "WhileIndex"}, {"var": "MaxIndex"}]}}}, "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [1, {"var": "WhileIndex"}]}}}, "assign": "WhileIndex"}]}}, {"call": {"method": "return", "args": {"value": "OK"}}}]}]}, "Function<FnTestWhileNoReturn>": {"name": "FnTestWhile", "type": "Function", "params": [], "layers": [{"vars": [{"name": "WhileIndex", "dataType": "Integer", "collection": false}, {"name": "MaxIndex", "dataType": "Integer", "collection": false}], "execute": [{"call": {"method": "set", "args": {"value": 1}}, "assign": "WhileIndex"}, {"call": {"method": "set", "args": {"value": 3}}, "assign": "MaxIndex"}, {"call": {"method": "while", "args": {"expression": {"<=": [{"var": "WhileIndex"}, {"var": "MaxIndex"}]}}}, "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [1, {"var": "WhileIndex"}]}}}, "assign": "WhileIndex"}]}}]}]}, "Function<FnBreakWhileLoop>": {"name": "FnBreakWhileLoop", "type": "Function", "params": [], "layers": [{"vars": [{"name": "i", "dataType": "Integer", "collection": false}], "execute": [{"call": {"method": "set", "args": {"value": 0}}, "assign": "i"}, {"call": {"method": "while", "args": {"expression": {"<": [{"var": "i"}, 10]}}}, "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"var": "i"}, 1]}}}, "assign": "i"}, {"call": {"method": "if", "args": {"expression": {"==": [{"var": "i"}, 5]}}}, "result": {"TRUE": [{"call": {"method": "break"}}]}}]}}, {"call": {"method": "return", "args": {"name": "i"}}}]}]}}}}