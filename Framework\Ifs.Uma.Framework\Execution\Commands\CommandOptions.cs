﻿using System;
using System.Collections.Generic;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.Framework.Execution.Commands
{
    public sealed class CommandOptions
    {
        public IReadOnlyDictionary<string, object> Vars { get; set; }
        public bool ActionsAllowed { get; set; } = true;
        public string CrudOperation { get; private set; }

        public void SetCrudOperation(CpiCrudType operation)
        {
            switch (operation)
            {
                case CpiCrudType.Create:
                    CrudOperation = "create";
                    break;
                case CpiCrudType.Update:
                    CrudOperation = "update";
                    break;
                case CpiCrudType.Delete:
                    CrudOperation = "delete";
                    break;
                case CpiCrudType.None:
                    CrudOperation = null;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(operation));
            }
        }
    }
}
