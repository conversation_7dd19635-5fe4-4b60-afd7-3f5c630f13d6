﻿using System;
using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Database
{
    public interface IAccessValue
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1007:UseGenericsWhereAppropriate",
            Justification="object is what is returned from reflection")]
        bool GetValue(IMetaDataMember member, object row, out object value);
        bool AlwaysFound { get; }
        bool ReadOnly { get; }
        void SetValue(IMetaDataMember member, object row, object value);
    }

    public class RowValueAccessor : IAccessValue
    {
        public RowValueAccessor()
        {
            m_found = null;
        }

        public bool GetValue(IMetaDataMember member, object row, out object value)
        {
            if (member == null) throw new ArgumentNullException("member");
            if (row == null) throw new ArgumentNullException("row");
            if (m_found == null || m_found.Contains(member))
            {
                value = member.GetValue(row);
                return true;
            }
            value = null;
            return false;
        }

        public bool AlwaysFound { get { return m_found == null; } }
        public bool ReadOnly { get { return false; } }

        public void SetValue(IMetaDataMember member, object row, object value)
        {
            if (member == null) throw new ArgumentNullException("member");
            if (row == null) throw new ArgumentNullException("row");
            member.SetValue(row, value);
        }

        public void SetFoundMembers(IEnumerable<IMetaDataMember> members)
        {
            m_found = members;
        }

        private IEnumerable<IMetaDataMember> m_found;
    }

    public class DbRowHandler
    {
        public DbRowHandler(DbCommand command, IMetaTable table, SqlBuilder builder)
            : this(command, table, builder, null, null)
        {
        }

        public DbRowHandler(DbCommand command, IMetaTable table, SqlBuilder builder, IAccessValue accessor)
            : this(command, table, builder, accessor, null)
        {
        }

        public DbRowHandler(DbCommand command, IMetaTable table, SqlBuilder builder, IAccessValue accessor,
            IEnumerable<IMetaDataMember> key)
        {
            if (command == null) throw new ArgumentNullException("command");
            if (table == null) throw new ArgumentNullException("table");
            if (builder == null) throw new ArgumentNullException("builder");
            Command = command;
            Builder = builder;
            TableName = table.TableName;
            Table = table;
            m_accessor = accessor != null ? accessor : new RowValueAccessor();
            m_autoIncrement = table.AutoIncrement();
            m_key = GetKey(table, key, m_autoIncrement);
            m_nonKey = table.DataMembers.Where(x => !x.AutoIncrement && !m_key.Contains(x)).ToArray();
            if (m_autoIncrement != null && !m_key.Contains(m_autoIncrement))
            {
                m_idSelectColumn = ColumnSpec.CreateSelect(m_autoIncrement.ColumnName);
            }
            m_parameters = new Dictionary<int, DbParameter>();
        }

        public DbCommand Command { get; private set; }
        public IEnumerable<IMetaDataMember> Key { get { return m_key; } }

        public bool Update(object row)
        {
            if (row == null) throw new ArgumentNullException("row");
            if (!ChooseColumns(ColumnChoice.Keys).Any()) throw new InvalidOperationException("No unique key for row update");
            if (!m_accessor.AlwaysFound || string.IsNullOrEmpty(m_updateCommand)) // if we may be updating partial columns
            {
                IUpdateSpec spec = SqlSpec.CreateUpdate(TableName, UpdateColumns(row, ColumnChoice.NonKeys, false), WhereClause(row));
                m_updateCommand = CreateCommand(ColumnChoice.All, spec);
            }
            else
            {
                SetCommandParameters(ColumnChoice.All);
                Command.CommandText = m_updateCommand;
                SetParameterValues(row, ColumnChoice.Keys, false);
                SetParameterValues(row, ColumnChoice.NonKeys, false);
            }
            bool result = Command.ExecuteNonQuery() > 0;
            if (result && m_idSelectColumn != null && GetIdentity(row) == 0 && !m_accessor.ReadOnly)
            {
                // we don't know the autoIncrement value of the row we updated so set it (if allowed)
                if (string.IsNullOrEmpty(m_selectIdCommand))
                {
                    ISelectSpec spec = SqlSpec.CreateSelect(SelectColumns(ColumnChoice.None),
                        TableSpec.Create(TableName), null, WhereClause(row), null, null, false);
                    m_selectIdCommand = CreateCommand(ColumnChoice.Keys, spec);
                }
                else
                {
                    SetCommandParameters(ColumnChoice.Keys);
                    Command.CommandText = m_selectIdCommand;
                    SetParameterValues(row, ColumnChoice.Keys, false);
                }
                object oResult = Command.ExecuteScalar();
                if (oResult != null)
                {
                    SetIdentity(row, ObjectConverter.ToLong(oResult));
                }
            }
            return result;
        }

        public void Insert(object row)
        {
            if (row == null) throw new ArgumentNullException("row");
            if (string.IsNullOrEmpty(m_insertCommand))
            {
                // we are not interested in the AutoIncrement value if we cannot set it
                IInsertSpec spec = SqlSpec.CreateInsert(TableName,
                    UpdateColumns(row, ColumnChoice.NoAutoIncrementKeys, true)
                        .Concat(UpdateColumns(row, ColumnChoice.NonKeys, true)), // done separately to allow nulls in non-key values
                    m_autoIncrement != null && !m_accessor.ReadOnly ? m_autoIncrement.ColumnName : null, null);
                m_insertCommand = CreateCommand(ColumnChoice.NoAutoIncrementAll, spec);
                if (m_autoIncrement != null && !m_accessor.ReadOnly)
                {
                    m_scopeIdCommand = Builder.ScopeIdentityCommand(spec);
                }
            }
            else
            {
                SetCommandParameters(ColumnChoice.NoAutoIncrementAll);
                Command.CommandText = m_insertCommand;
                SetParameterValues(row, ColumnChoice.NoAutoIncrementKeys, true);
                SetParameterValues(row, ColumnChoice.NonKeys, true);
            }
            PerformInsert(row);
        }

        public void Upsert(object row)
        {
            if (row == null) throw new ArgumentNullException("row");
            if (!ChooseColumns(ColumnChoice.Keys).Any()) throw new InvalidOperationException("No unique key for row upsert");
            //If the key is AutoIncrement and the value is not supplied then can only Insert
            object value;
            if (m_autoIncrement != null && !m_accessor.AlwaysFound &&
                !ChooseColumns(ColumnChoice.NoAutoIncrementKeys).Any() &&
                (!m_accessor.GetValue(m_autoIncrement, row, out value) || value == null))
            {
                Insert(row);
            }
            else if (!Builder.SupportsUpsert)
            {
                if (!Update(row))
                {
                    Insert(row);
                }
            }
            else
            {
                if (string.IsNullOrEmpty(m_upsertCommand))
                {
                    IInsertSpec spec = SqlSpec.CreateInsert(TableName,
                        UpdateColumns(row, ColumnChoice.NoAutoIncrementKeys, true)
                            .Concat(UpdateColumns(row, ColumnChoice.NonKeys, true)), // done separately to allow nulls in non-key values
                        m_autoIncrement != null && !m_accessor.ReadOnly ? m_autoIncrement.ColumnName : null,
                        ChooseColumns(ColumnChoice.Keys).Select(x => x.ColumnName)); // key contains auto increment or not
                    m_upsertCommand = CreateCommand(ColumnChoice.NoAutoIncrementAll, spec);
                    if (m_autoIncrement != null && !m_accessor.ReadOnly)
                    {
                        m_scopeIdCommand = Builder.ScopeIdentityCommand(spec);
                    }
                }
                else
                {
                    SetCommandParameters(ColumnChoice.NoAutoIncrementAll);
                    Command.CommandText = m_upsertCommand;
                    SetParameterValues(row, ColumnChoice.NoAutoIncrementKeys, true);
                    SetParameterValues(row, ColumnChoice.NonKeys, true);
                }
                PerformInsert(row);
            }
        }

        public bool Delete(object row)
        {
            if (row == null) throw new ArgumentNullException("row");
            if (!ChooseColumns(ColumnChoice.Keys).Any()) throw new InvalidOperationException("No unique key for row delete");
            if (string.IsNullOrEmpty(m_deleteCommand))
            {
                IDeleteSpec spec = SqlSpec.CreateDelete(TableName, WhereClause(row));
                m_deleteCommand = CreateCommand(ColumnChoice.Keys, spec);
            }
            else
            {
                SetCommandParameters(ColumnChoice.Keys);
                Command.CommandText = m_deleteCommand;
                SetParameterValues(row, ColumnChoice.Keys, false);
            }
            return Command.ExecuteNonQuery() > 0;
        }

        public bool Select(object row)
        {
            bool result = false;
            if (row == null) throw new ArgumentNullException("row");
            if (m_accessor.ReadOnly) throw new InvalidOperationException("Cannot select with a read-only accessor");
            if (!ChooseColumns(ColumnChoice.Keys).Any()) throw new InvalidOperationException("No unique key for row select");
            if (string.IsNullOrEmpty(m_selectCommand))
            {
                IEnumerable<ISelectColumnSpec> selectColumns = SelectColumns(ColumnChoice.NonKeys);
                if (!selectColumns.Any())
                {
                    // Nothing to select - just see if the row exists
                    return Exists(row);
                }

                ISelectSpec spec = SqlSpec.CreateSelect(selectColumns, TableSpec.Create(TableName),
                    null, WhereClause(row), null, null, false);
                m_selectCommand = CreateCommand(ColumnChoice.Keys, spec);
            }
            else
            {
                SetCommandParameters(ColumnChoice.Keys);
                Command.CommandText = m_selectCommand;
                SetParameterValues(row, ColumnChoice.Keys, false);
            }
            using (DbDataReader reader = Command.ExecuteReader())
            {
                if (reader.Read())
                {
                    ReadRow(row, reader, ColumnChoice.NonKeys);
                    result = true;
                }
            }
            return result;
        }

        public bool Exists(object row)
        {
            if (row == null) throw new ArgumentNullException(nameof(row));
            
            if (string.IsNullOrEmpty(m_existsCommand))
            {
                if (!ChooseColumns(ColumnChoice.Keys).Any()) throw new InvalidOperationException("No unique key for row select");

                var selectColumns = new[] { ColumnSpec.Create(Builder.AggregateAsterisk, null, null, EColumnFunction.CountFunction) };
                ISelectSpec spec = SqlSpec.CreateSelect(selectColumns, TableSpec.Create(TableName),
                    null, WhereClause(row), null, null, false);
                m_existsCommand = CreateCommand(ColumnChoice.Keys, spec);
            }
            else
            {
                SetCommandParameters(ColumnChoice.Keys);
                Command.CommandText = m_existsCommand;
                SetParameterValues(row, ColumnChoice.Keys, false);
            }
            
            return (long)Command.ExecuteScalar() > 0;
        }

        #region Implementation

        protected string CreateCommand(ColumnChoice choice, ISqlSpec spec)
        {
            ParameterUsage usage = SetCommandParameters(choice);
            IEnumerable<DbParameter> parameters = Builder.BuildSql(Command, spec, usage);
            foreach (DbParameter parameter in parameters)
            {
                m_parameters[parameter.ValueIndex] = parameter;
            }
            return Command.CommandText;
        }

        protected void PerformInsert(object row)
        {
            if (m_autoIncrement != null && !m_accessor.ReadOnly)
            {
                if (!string.IsNullOrEmpty(m_scopeIdCommand))
                {
                    Command.ExecuteNonQuery();
                    Command.CommandText = m_scopeIdCommand;
                }
                object oResult = Command.ExecuteScalar();
                if (oResult != null)
                {
                    SetIdentity(row, ObjectConverter.ToLong(oResult));
                }
            }
            else
            {
                Command.ExecuteNonQuery();
            }
        }

        protected long GetIdentity(object row)
        {
            if (row != null && m_autoIncrement != null)
            {
                object value;
                if (m_accessor.GetValue(m_autoIncrement, row, out value) && value != null)
                {
                    switch (TypeHelper.GetTypeCode(m_autoIncrement.ColumnType))
                    {
                        case TypeCode.Int64:
                            return (long)value;
                        case TypeCode.Int32:
                            return (int)value;
                    }
                }
            }
            return -1;
        }

        protected void SetIdentity(object row, long id)
        {
            if (row != null && m_autoIncrement != null)
            {
                switch (TypeHelper.GetTypeCode(m_autoIncrement.ColumnType))
                {
                    case TypeCode.Int64:
                        m_accessor.SetValue(m_autoIncrement, row, id);
                        break;
                    case TypeCode.Int32:
                        m_accessor.SetValue(m_autoIncrement, row, (int)id);
                        break;
                }
            }
        }

        protected void ReadRow(object row, DbDataReader reader, ColumnChoice choice)
        {
            if (row == null) throw new ArgumentNullException("row");
            if (reader == null) throw new ArgumentNullException("reader");
            int i = 0;
            if (m_idSelectColumn != null)
            {
                SetIdentity(row, ObjectConverter.ToLong(reader.GetValue(i)));
                i++;
            }
            foreach (IMetaDataMember member in ChooseColumns(choice))
            {
                m_accessor.SetValue(member, row, member.ConvertValue(reader.GetValue(i)));
                i++;
            }
        }

        protected void ReadRow(object row, DbDataReader reader, IEnumerable<IMetaDataMember> columns)
        {
            if (row == null) throw new ArgumentNullException(nameof(row));
            if (reader == null) throw new ArgumentNullException(nameof(reader));
            int i = 0;
            foreach (IMetaDataMember member in columns)
            {
                if (member == m_autoIncrement)
                {
                    SetIdentity(row, ObjectConverter.ToLong(reader.GetValue(i)));
                }
                else
                {
                    m_accessor.SetValue(member, row, member.ConvertValue(reader.GetValue(i)));
                }
                i++;
            }
        }

        [Flags]
        protected enum ColumnChoice
        {
            None,
            NonKeys = 1,
            Keys = 2,
            All = NonKeys | Keys,
            NoAutoIncrement = 4,
            NoAutoIncrementKeys = NoAutoIncrement | Keys,
            NoAutoIncrementAll = NoAutoIncrement | All
        }

        protected IEnumerable<IMetaDataMember> ChooseColumns(ColumnChoice choice)
        {
            IEnumerable<IMetaDataMember> result = null;
            if ((choice & ColumnChoice.Keys) == ColumnChoice.Keys)
            {
                result = (choice & ColumnChoice.NoAutoIncrement) == ColumnChoice.NoAutoIncrement ?
                    m_key.Where(x => !x.AutoIncrement) : m_key;
            }
            if ((choice & ColumnChoice.NonKeys) == ColumnChoice.NonKeys)
            {
                result = result != null ? result.Concat(m_nonKey) : m_nonKey;
            }
            return result != null ? result : Enumerable.Empty<IMetaDataMember>();
        }

        protected IEnumerable<ISelectColumnSpec> SelectColumns(ColumnChoice choice)
        {
            ICollection<ISelectColumnSpec> result = new List<ISelectColumnSpec>();
            if (m_idSelectColumn != null)
            {
                result.Add(m_idSelectColumn);
            }
            foreach (IMetaDataMember member in ChooseColumns(choice))
            {
                result.Add(ColumnSpec.CreateSelect(member.ColumnName));
            }
            return result.AsEnumerable();
        }

        protected IEnumerable<IColumnUpdateSpec> UpdateColumns(object row, ColumnChoice choice, bool insert)
        {
            if (row == null) throw new ArgumentNullException("row");
            ICollection<IColumnUpdateSpec> result = new List<IColumnUpdateSpec>();
            foreach (IMetaDataMember member in ChooseColumns(choice))
            {
                object value;
                bool found = m_accessor.GetValue(member, row, out value);
                if (choice != ColumnChoice.NonKeys && (!found || value == null))
                {
                    ThrowKeyValueNotFound(member);
                }

                if (found || insert)
                {
                    result.Add(ColumnSpec.Create(member, value, member.Index));
                }
            }
            if (!insert && !result.Any())
            {
                // we are updating and no columns are changed: just change an arbitrary column to itself
                IMetaDataMember member = m_nonKey.Any() ? m_nonKey.First() :
                    m_key.Where(x => !x.AutoIncrement).FirstOrDefault();
                if (member != null)
                {
                    result.Add(ColumnSpec.Create(member.ColumnName, EColumnExpression.IncrementColumn, 0));
                }
            }
            return result.AsEnumerable();
        }

        protected IEnumerable<IWhereElement> WhereClause(object row)
        {
            if (row == null) throw new ArgumentNullException("row");
            ICollection<IWhereElement> result = new List<IWhereElement>();
            foreach (IMetaDataMember key in ChooseColumns(ColumnChoice.Keys))
            {
                object value;
                if (!m_accessor.GetValue(key, row, out value) || value == null)
                {
                    ThrowKeyValueNotFound(key);
                }

                result.Add(WhereElement.Create(EOperand.And, key, EComparisonMethod.Equals, value, key.Index));
            }
            return result.AsEnumerable();
        }

        protected void SetParameterValues(object row, ColumnChoice choice, bool insert)
        {
            foreach (IMetaDataMember member in ChooseColumns(choice))
            {
                object value;
                bool found = m_accessor.GetValue(member, row, out value);
                if (choice != ColumnChoice.NonKeys && (!found || value == null))
                {
                    ThrowKeyValueNotFound(member);
                }

                if (found || insert)
                {
                    m_parameters[member.Index].Value = value;
                }
            }
        }

        private void ThrowKeyValueNotFound(IMetaDataMember member)
        {
            string message = string.Format(Strings.KeyRequiresValueError, TableName + "." + member.ColumnName);
            throw new DbException(message, null, DbExceptionType.KeyValueNotFound);
        }

        protected ParameterUsage SetCommandParameters(ColumnChoice choice)
        {
            ParameterUsage result = ParameterUsage.None;
            ICollection<DbParameter> parameters = new List<DbParameter>();
            bool resetParameters = false;
            foreach (IMetaDataMember member in ChooseColumns(choice))
            {
                DbParameter parameter;
                if (m_parameters.TryGetValue(member.Index, out parameter))
                {
                    parameters.Add(parameter);
                    if (!resetParameters)
                    {
                        resetParameters = !Command.Parameters.Contains(parameter);
                    }
                }
                else
                {
                    resetParameters = true;
                    result = ParameterUsage.Create;
                }
            }
            if (resetParameters)
            {
                Command.Parameters.Clear();
                foreach (DbParameter parameter in parameters)
                {
                    Command.Parameters.Add(parameter);
                }
            }
            return result;
        }

        protected SqlBuilder Builder { get; private set; }
        protected string TableName { get; private set; }
        protected IMetaTable Table { get; private set; }

        private IAccessValue m_accessor;
        private IEnumerable<IMetaDataMember> m_key;
        private IEnumerable<IMetaDataMember> m_nonKey;
        private IMetaDataMember m_autoIncrement;
        private ISelectColumnSpec m_idSelectColumn;
        private IDictionary<int, DbParameter> m_parameters;
        private string m_insertCommand;
        private string m_updateCommand;
        private string m_deleteCommand;
        private string m_scopeIdCommand;
        private string m_selectIdCommand;
        private string m_selectCommand;
        private string m_existsCommand;
        private string m_upsertCommand;

        private static IEnumerable<IMetaDataMember> GetKey(IMetaTable table,
            IEnumerable<IMetaDataMember> key, IMetaDataMember autoIncrement)
        {
            if (table == null) throw new ArgumentNullException("table");
            if (key != null && key.Any(x => x != null))
            {
                // validate that these key members come from the table
                if (key.Any(x => x != null && !table.DataMembers.Contains(x)))
                    throw new ArgumentOutOfRangeException("key");
                return key.Where(x => x != null).Distinct().ToArray();
            }
            // choose the key members from the table
            if (autoIncrement != null)
            {
                // look for a unique key other than the AutoIncrement column
                IMetaIndex unique = table.FirstUniqueIndex();
                if (unique != null)
                {
                    return unique.Columns;
                }
            }
            // use the primary key if nothing better (this could be the AutoIncrement column)
            return table.PrimaryKey.ToArray();
        }

        #endregion
    }

    public enum MultipleResult
    {
#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
        Success,        // everything worked
        PartialFailure, // some failures, some successes
        Failure         // everything failed
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row
    }

    public interface IDbRowHandler<T>
    {
        DbCommand Command { get; }
        IEnumerable<IMetaDataMember> Key { get; }
        bool UpdateRow(T row);
        void InsertRow(T row);
        void UpsertRow(T row);
        bool DeleteRow(T row);
        bool SelectRow(T row);
        bool Exists(T row);
        IEnumerable<T> SelectAll(IEnumerable<IWhereElement> where, Action<T> action);
        IEnumerable<T> SelectAllUsingSpec(ISelectSpec spec, Action<T> action);
        IEnumerable<T> SelectAllDeferred(IEnumerable<IWhereElement> where);
        MultipleResult InsertAll(IEnumerable<T> rows, bool throwOnError);
        MultipleResult UpdateAll(IEnumerable<T> rows, bool throwOnError);
        MultipleResult UpsertAll(IEnumerable<T> rows, bool throwOnError);
        MultipleResult DeleteAll(IEnumerable<T> rows, bool throwOnError);
    }

    /// <summary>
    /// Create a Row Handler to handle multiple instances of the same row type efficiently.
    /// </summary>
    /// <typeparam name="T">The row type to handle</typeparam>
    public class DbRowHandler<T> : DbRowHandler, IDbRowHandler<T>
    {
        public static IDbRowHandler<T> Create(DbCommand command, IMetaModel model, SqlBuilder builder)
        {
            return Create(command, model != null ? model.GetTable(typeof(T)) : null, builder, null);
        }

        public static IDbRowHandler<T> Create(DbCommand command, IMetaTable table, SqlBuilder builder, IAccessValue accessor)
        {
            return new DbRowHandler<T>(command, table, builder, accessor);
        }

        public bool UpdateRow(T row)
        {
            return Update(row);
        }

        public void InsertRow(T row)
        {
            Insert(row);
        }

        public void UpsertRow(T row)
        {
            Upsert(row);
        }

        public bool DeleteRow(T row)
        {
            return Delete(row);
        }

        public bool SelectRow(T row)
        {
            return Select(row);
        }

        public bool Exists(T row)
        {
            return base.Exists(row);
        }

        public IEnumerable<T> SelectAll(IEnumerable<IWhereElement> where, Action<T> action)
        {
            ICollection<T> result = new List<T>();
            // no point in saving the SelectAll command as it's not worth using again with this command
            ISelectSpec spec = SqlSpec.CreateSelect(SelectColumns(ColumnChoice.All),
                TableSpec.Create(TableName), null, where, null, null, false);
            CreateCommand(ColumnChoice.None, spec);
            using (DbDataReader reader = Command.ExecuteReader())
            {
                while (reader.Read())
                {
                    T row = Activator.CreateInstance<T>();
                    ReadRow(row, reader, ColumnChoice.All);
                    if (action != null)
                    {
                        action(row);
                    }
                    result.Add(row);
                }
            }
            return result.AsEnumerable();
        }

        public IEnumerable<T> SelectAllUsingSpec(ISelectSpec spec, Action<T> action)
        {
            ICollection<T> result = new List<T>();
            CreateCommand(ColumnChoice.All, spec);
            IEnumerable<IMetaDataMember> columns = spec.Columns.Select(x => Table.FindMemberByColumnName(x.ColumnAlias ?? x.ColumnName)).ToArray();
            using (DbDataReader reader = Command.ExecuteReader())
            {
                while (reader.Read())
                {
                    T row = (T)Table.CreateRow();
                    ReadRow(row, reader, columns);
                    action?.Invoke(row);
                    result.Add(row);
                }
            }
            return result.AsEnumerable();
        }

        public IEnumerable<T> SelectAllDeferred(IEnumerable<IWhereElement> where)
        {
            // no point in saving the SelectAll command as it's not worth using again with this command
            ISelectSpec spec = SqlSpec.CreateSelect(SelectColumns(ColumnChoice.All),
                TableSpec.Create(TableName), null, where, null, null, false);
            CreateCommand(ColumnChoice.None, spec);
            using (DbDataReader reader = Command.ExecuteReader())
            {
                while (reader.Read())
                {
                    T row = Activator.CreateInstance<T>();
                    ReadRow(row, reader, ColumnChoice.All);
                    yield return row;
                }
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1031:DoNotCatchGeneralExceptionTypes",
            Justification="Nothing better to catch")]
        public MultipleResult InsertAll(IEnumerable<T> rows, bool throwOnError)
        {
            bool oneSucceeded = false;
            bool oneFailed = false;
            if (rows != null)
            {
                foreach (T row in rows.Where(x => x != null))
                {
                    try
                    {
                        Insert(row);
                        oneSucceeded = true;
                    }
                    catch
                    {
                        if (throwOnError) throw;
                        oneFailed = true;
                    }
                }
            }
            return oneFailed ? oneSucceeded ? MultipleResult.PartialFailure : MultipleResult.Failure : MultipleResult.Success;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1031:DoNotCatchGeneralExceptionTypes",
            Justification = "Nothing better to catch")]
        public MultipleResult UpdateAll(IEnumerable<T> rows, bool throwOnError)
        {
            bool oneSucceeded = false;
            bool oneFailed = false;
            if (rows != null)
            {
                foreach (T row in rows.Where(x => x != null))
                {
                    try
                    {
                        if (Update(row))
                        {
                            oneSucceeded = true;
                        }
                        else
                        {
                            oneFailed = true;
                        }
                    }
                    catch
                    {
                        if (throwOnError) throw;
                        oneFailed = true;
                    }
                }
            }
            return oneFailed ? oneSucceeded ? MultipleResult.PartialFailure : MultipleResult.Failure : MultipleResult.Success;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1031:DoNotCatchGeneralExceptionTypes",
            Justification = "Nothing better to catch")]
        public MultipleResult UpsertAll(IEnumerable<T> rows, bool throwOnError)
        {
            bool oneSucceeded = false;
            bool oneFailed = false;
            if (rows != null)
            {
                foreach (T row in rows.Where(x => x != null))
                {
                    try
                    {
                        Upsert(row);
                        oneSucceeded = true;
                    }
                    catch
                    {
                        if (throwOnError) throw;
                        oneFailed = true;
                    }
                }
            }
            return oneFailed ? oneSucceeded ? MultipleResult.PartialFailure : MultipleResult.Failure : MultipleResult.Success;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1031:DoNotCatchGeneralExceptionTypes",
            Justification = "Nothing better to catch")]
        public MultipleResult DeleteAll(IEnumerable<T> rows, bool throwOnError)
        {
            bool oneSucceeded = false;
            bool oneFailed = false;
            if (rows != null)
            {
                foreach (T row in rows.Where(x => x != null))
                {
                    try
                    {
                        Delete(row);
                        oneSucceeded = true;
                    }
                    catch
                    {
                        if (throwOnError) throw;
                        oneFailed = true;
                    }
                }
            }
            return oneFailed ? oneSucceeded ? MultipleResult.PartialFailure : MultipleResult.Failure : MultipleResult.Success;
        }

        protected DbRowHandler(DbCommand command, IMetaTable table, SqlBuilder builder, IAccessValue accessor)
            : base(command, table, builder, accessor)
        {
        }
    }

    public enum KeyChoice { DefaultKey, AutoIncrementKey, ObjKey }

    public class DbRowHandlerCache
    {
        public DbRowHandlerCache(DbCommand command, IMetaModel model, SqlBuilder builder, IAccessValue accessor, KeyChoice key)
        {
            if (command == null) throw new ArgumentNullException("command");
            if (model == null) throw new ArgumentNullException("model");
            if (builder == null) throw new ArgumentNullException("builder");
            Command = command;
            Model = model;
            m_builder = builder;
            m_accessor = accessor;
            m_key = key;
            m_cache = new Dictionary<IMetaTable, DbRowHandler>();
        }

        public DbRowHandler GetHandler(IMetaTable table)
        {
            if (table == null) throw new ArgumentNullException("table");
            // something using one command must be single threaded.
            DbRowHandler result;
            if (!m_cache.TryGetValue(table, out result))
            {
                result = new DbRowHandler(Command, table, m_builder, m_accessor, GetKey(table));
                m_cache[table] = result;
            }
            return result;
        }

        public DbCommand Command { get; private set; }
        public IMetaModel Model { get; private set; }
        public bool ReadOnlyRow { get { return m_accessor != null && m_accessor.ReadOnly; } }

        private SqlBuilder m_builder;
        private IAccessValue m_accessor;
        private IDictionary<IMetaTable, DbRowHandler> m_cache;
        private KeyChoice m_key;

        private IEnumerable<IMetaDataMember> GetKey(IMetaTable table)
        {
            switch (m_key)
            {
                case KeyChoice.AutoIncrementKey:
                    return new IMetaDataMember[] { table.AutoIncrement() };
                case KeyChoice.ObjKey:
                    return new IMetaDataMember[] { table.DataMembers.First(x => x.PropertyName == "ObjKey") };
                default:
                    return null;
            }
        }
    }

    public static class DbRowHandlerExtensions
    {
        public static IEnumerable<T> SelectAll<T>(this IDbRowHandler<T> handler, IEnumerable<IWhereElement> where)
        {
            if (handler == null) throw new ArgumentNullException("handler");
            return handler.SelectAll(where, null);
        }

        public static IEnumerable<T> SelectAll<T>(this IDbRowHandler<T> handler)
        {
            if (handler == null) throw new ArgumentNullException("handler");
            return handler.SelectAll(null, null);
        }

        public static IEnumerable<T> SelectAllDeferred<T>(this IDbRowHandler<T> handler)
        {
            if (handler == null) throw new ArgumentNullException("handler");
            return handler.SelectAllDeferred(null);
        }

        public static void InsertRow(this DbRowHandlerCache cache, object row)
        {
            if (cache == null) throw new ArgumentNullException("cache");
            if (row == null) throw new ArgumentNullException("row");
            cache.GetHandler(cache.Model.GetTable(row.GetType())).Insert(row);
        }

        public static void SelectRow(this DbRowHandlerCache cache, object row)
        {
            if (cache == null) throw new ArgumentNullException("cache");
            if (row == null) throw new ArgumentNullException("row");
            if (cache.ReadOnlyRow) throw new InvalidOperationException("cannot select with read-only row");
            cache.GetHandler(cache.Model.GetTable(row.GetType())).Select(row);
        }

        public static void UpdateRow(this DbRowHandlerCache cache, object row)
        {
            if (cache == null) throw new ArgumentNullException("cache");
            if (row == null) throw new ArgumentNullException("row");
            cache.GetHandler(cache.Model.GetTable(row.GetType())).Update(row);
        }

        public static void DeleteRow(this DbRowHandlerCache cache, object row)
        {
            if (cache == null) throw new ArgumentNullException("cache");
            if (row == null) throw new ArgumentNullException("row");
            cache.GetHandler(cache.Model.GetTable(row.GetType())).Delete(row);
        }

        public static MultipleResult InsertAll<T>(this IDbRowHandler<T> handler, IEnumerable<T> rows)
        {
            if (handler == null) throw new ArgumentNullException("handler");
            return handler.InsertAll(rows, false);
        }

        public static MultipleResult UpdateAll<T>(this IDbRowHandler<T> handler, IEnumerable<T> rows)
        {
            if (handler == null) throw new ArgumentNullException("handler");
            return handler.UpdateAll(rows, false);
        }

        public static MultipleResult UpsertAll<T>(this IDbRowHandler<T> handler, IEnumerable<T> rows)
        {
            if (handler == null) throw new ArgumentNullException("handler");
            return handler.UpsertAll(rows, false);
        }

        public static MultipleResult DeleteAll<T>(this IDbRowHandler<T> handler, IEnumerable<T> rows)
        {
            if (handler == null) throw new ArgumentNullException("handler");
            return handler.DeleteAll(rows, false);
        }
    }
}
