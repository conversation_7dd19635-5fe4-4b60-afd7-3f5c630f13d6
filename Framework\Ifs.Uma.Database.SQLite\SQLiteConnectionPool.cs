﻿using System;
using System.Collections.Generic;
using System.Threading;
using Ifs.Uma.Database.SQLite.CustomFunctions;
using Ifs.Uma.Utility;
using SQLitePCL;

namespace Ifs.Uma.Database.SQLite
{
    internal class SQLiteConnectionPool : DbConnectionPool<SQLiteConnectionData>
    {
        internal SQLiteConnectionPool()
        {
        }

        protected override void CloseDb(SQLiteConnectionData value)
        {
            if (value != null)
            {
                SafeNativeMethods.SQLiteClose(value.Db);
            }
        }
    }

    internal class SQLiteConnectionData
    {
        internal SQLiteConnectionData(sqlite3 db, IReadOnlyCollection<SQLiteCustomFunction> functions, ReaderWriterLockSlim rwLock)
        {
            Db = db;
            Functions = functions;
            RwLock = rwLock;
        }

        public sqlite3 Db { get; }
        public IReadOnlyCollection<SQLiteCustomFunction> Functions { get;  }
        public ReaderWriterLockSlim RwLock { get; }
    }

    internal class SQLitePoolData : DbPoolData<SQLiteConnectionData>
    {
        internal SQLitePoolData(int maxPoolSize, TimeSpan connectTimeout,
            string path, string key, int openFlags, int busyMilliseconds)
            : base(maxPoolSize, connectTimeout)
        {
            m_path = path;
            m_key = key;
            m_openFlags = openFlags;
            m_busyMilliseconds = busyMilliseconds;
            m_rwLock = new ReaderWriterLockSlim();
        }

        private string m_path;
        private string m_key;
        private int m_openFlags;
        private int m_busyMilliseconds;
        private ReaderWriterLockSlim m_rwLock;
        private bool _hasSetKey = false;

        protected override string GetDbPath() { return m_path; }

        protected override SQLiteConnectionData OpenDb(bool create)
        {
            sqlite3 db;
            int r = raw.sqlite3_open_v2(m_path, out db,
                (int)(create ? m_openFlags | raw.SQLITE_OPEN_CREATE : m_openFlags), null);

            if (r == raw.SQLITE_OK)
            {
                r = SetKey(db);
            }

            IReadOnlyCollection<SQLiteCustomFunction> functions = null;
            if (r == raw.SQLITE_OK)
            {
                r = LoadCustomFunctions(db, out functions);
            }

            if (r != raw.SQLITE_OK)
            {
                // close anything returned as an error.
                SafeNativeMethods.SQLiteClose(db);
                throw new SQLiteException(r, "Cannot open database " + m_path);
            }

            // set the busy timeout for the new connection
            raw.sqlite3_busy_timeout(db, m_busyMilliseconds);

            return new SQLiteConnectionData(db, functions, m_rwLock);
        }

        private int SetKey(sqlite3 db)
        {
            // If we are connecting to a SQLCipher database with a password 
            // we need to tell the connection

            if (string.IsNullOrEmpty(m_key))
            {
                return raw.SQLITE_OK;
            }

            if (_hasSetKey && ((m_openFlags & raw.SQLITE_OPEN_SHAREDCACHE) > 0))
            {
                // We have already set the key when opening a previous connection.
                // Setting the key again may cause an access violation if another 
                // shared cache connection is in use
                return raw.SQLITE_OK;
            }

            string cleanKey = m_key.Replace("'", "''");
            int r = raw.sqlite3_exec(db, $"PRAGMA key = '{cleanKey}';");

            if (r == raw.SQLITE_OK)
            {
                _hasSetKey = true;
            }

            return r;
        }

        private int LoadCustomFunctions(sqlite3 db, out IReadOnlyCollection<SQLiteCustomFunction> functions)
        {
            functions = SQLiteCustomFunction.CreateFunctions();

            int ret = raw.SQLITE_OK;
            foreach (SQLiteCustomFunction function in functions)
            {
                ret = function.Register(db);

                if (ret != raw.SQLITE_OK)
                {
                    break;
                }
            }

            return ret;
        }

        protected override void CloseDb(SQLiteConnectionData value)
        {
            if (value != null)
            {
                SafeNativeMethods.SQLiteClose(value.Db);
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2213:DisposableFieldsShouldBeDisposed", MessageId = "m_rwLock",
            Justification = "False positive")]
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                UsefulExtensions.ThreadSafeDispose(ref m_rwLock);
            }
            base.Dispose(disposing);
        }
    }

    internal static class SQLiteConnectionStringExtensions
    {
        internal static DbPoolData<SQLiteConnectionData> CreatePoolData(this SQLiteConnectionStringBuilder builder)
        {
            if (builder == null) throw new ArgumentNullException("builder");
            return new SQLitePoolData(
                builder.MaxPoolSize,
                TimeSpan.FromSeconds(builder.ConnectTimeout),
                DataSourceHelper.Instance.GetPath(builder.DataSource),
                builder.Password,
                raw.SQLITE_OPEN_READWRITE | (builder.Cache == CacheMode.Shared ?
                    raw.SQLITE_OPEN_SHAREDCACHE : raw.SQLITE_OPEN_PRIVATECACHE),
                (int)(builder.BusyTimeout * 1000));
        }
    }
}
