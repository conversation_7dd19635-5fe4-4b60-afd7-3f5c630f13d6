﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Uma.UI.Controls
{
    public static class SyncFusionPickerHelper
    {
        public const int PickerHeight = 400;
        public const int PickerWidth = 300;
        public const int ColumnHeaderHeight = 70;

        public static ObservableCollection<object> GetTodayCollection(SyncFusionPickerType pickerType)
        {
            //Set today as selected date
            ObservableCollection<object> todayCollection = new ObservableCollection<object>();

            if (pickerType == SyncFusionPickerType.Date || pickerType == SyncFusionPickerType.DateTime)
            {
                todayCollection.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetAbbreviatedMonthName(DateTime.Now.Date.Month));
                if (DateTime.Now.Date.Day < 10)
                {
                    todayCollection.Add("0" + DateTime.Now.Date.Day);
                }
                else
                {
                    todayCollection.Add(DateTime.Now.Date.Day.ToString());
                }

                todayCollection.Add(DateTime.Now.Date.Year.ToString());
            }

            todayCollection.Add(DateTime.Now.Hour.ToString());
            todayCollection.Add(DateTime.Now.Minute.ToString());

            return todayCollection;
        }

        public static ObservableCollection<string> GetHeaders(SyncFusionPickerType pickerType)
        {
            ObservableCollection<string> headers = new ObservableCollection<string>();

            if (pickerType == SyncFusionPickerType.DateTime)
            {
                headers.Add(Strings.Month);
                headers.Add(Strings.Day);
                headers.Add(Strings.Year);
            }

            headers.Add(Strings.Hour);
            headers.Add(Strings.Minute);

            return headers;
        }

        public static string GetHeaderText(SyncFusionPickerType pickerType)
        {
            switch (pickerType)
            {
                case SyncFusionPickerType.DateTime:
                    return Strings.SelectADateAndTime;
                case SyncFusionPickerType.Date:
                    return Strings.SelectADate;
                case SyncFusionPickerType.Time:
                    return Strings.SelectATime;
                default:
                    return Strings.SelectADateAndTime;
            }
        }

        public static ObservableCollection<object> PopulateDateTimeCollections(SyncFusionPickerType pickerType)
        {
            ObservableCollection<object> date = new ObservableCollection<object>();
            ObservableCollection<object> year = new ObservableCollection<object>();
            ObservableCollection<object> month = new ObservableCollection<object>();
            Dictionary<string, string> months = new Dictionary<string, string>();
            ObservableCollection<object> day = new ObservableCollection<object>();
            ObservableCollection<object> hour = new ObservableCollection<object>();
            ObservableCollection<object> minute = new ObservableCollection<object>();

            //Populate months
            for (int i = 1; i <= 12; i++)
            {
                if (!months.ContainsKey(CultureInfo.CurrentCulture.DateTimeFormat.GetAbbreviatedMonthName(i)))
                {
                    months.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetAbbreviatedMonthName(i), CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(i));
                }

                month.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetAbbreviatedMonthName(i));
            }

            // Populate year
            for (int i = 1900; i < 2050; i++)
            {
                year.Add(i.ToString());
            }

            // Populate Days
            for (int i = 1; i <= DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month); i++)
            {
                if (i < 10)
                {
                    day.Add("0" + i);
                }
                else
                {
                    day.Add(i.ToString());
                }
            }

            // Populate Hours
            for (int i = 0; i < 24; i++)
            {
                if (i < 10)
                {
                    hour.Add("0" + i.ToString());
                }
                else
                {
                    hour.Add(i.ToString());
                }
            }

            // Populate Minutes
            for (int j = 0; j < 60; j++)
            {
                if (j < 10)
                {
                    minute.Add("0" + j);
                }
                else
                {
                    minute.Add(j.ToString());
                }
            }

            switch (pickerType)
            {
                case SyncFusionPickerType.DateTime:
                    date.Add(month);
                    date.Add(day);
                    date.Add(year);
                    date.Add(hour);
                    date.Add(minute);
                    break;
                case SyncFusionPickerType.Date:
                    date.Add(month);
                    date.Add(day);
                    date.Add(year);
                    break;
                case SyncFusionPickerType.Time:
                    date.Add(hour);
                    date.Add(minute);
                    break;
                default:
                    break;
            }

            return date;
        }

        private static Dictionary<string, string> PopulateMonths()
        {
            Dictionary<string, string> months = new Dictionary<string, string>();

            for (int i = 1; i <= 12; i++)
            {
                if (!months.ContainsKey(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(i)))
                {
                    months.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetAbbreviatedMonthName(i), CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(i));
                }
            }

            return months;
        }

        public static ObservableCollection<object> UpdateDays(ObservableCollection<object> date, object oldValue, object newValue)
        {
            try
            {
                bool update = false;

                if (oldValue != null && newValue != null && (oldValue as IList).Count > 0 && (newValue as IList).Count > 0)
                {
                    // Check if there are any changes in the Month or Year columns
                    if ((oldValue as IList)[0] != (newValue as IList)[0]
                        || (oldValue as IList)[2] != (newValue as IList)[2])
                    {
                        update = true;
                    }
                }

                if (update)
                {
                    ObservableCollection<object> days = new ObservableCollection<object>();
                    int month = DateTime.ParseExact((newValue as IList)[0].ToString(), "MMM", CultureInfo.CurrentCulture).Month;
                    int year = int.Parse((newValue as IList)[2].ToString());

                    for (int j = 1; j <= DateTime.DaysInMonth(year, month); j++)
                    {
                        if (j < 10)
                        {
                            days.Add("0" + j);
                        }
                        else
                        {
                            days.Add(j.ToString());
                        }
                    }

                    if (days.Count > 0)
                    {
                        date.RemoveAt(1);
                        date.Insert(1, days);
                        return date;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Current.Error(ex.Message);
            }

            return date;
        }

        public static ObservableCollection<object> SetSelectedDate(DateTime date, SyncFusionPickerType pickerType)
        {
            ObservableCollection<object> selectedCollection = new ObservableCollection<object>();

            if (pickerType == SyncFusionPickerType.Date || pickerType == SyncFusionPickerType.DateTime)
            {
                selectedCollection.Add(date.ToString("MMM"));
                if (date.Day < 10)
                {
                    selectedCollection.Add("0" + date.Day);
                }
                else
                {
                    selectedCollection.Add(date.Day.ToString());
                }

                selectedCollection.Add(date.Year.ToString());
            }

            if (date.Hour < 10)
            {
                selectedCollection.Add("0" + date.Hour);
            }
            else
            {
                selectedCollection.Add(date.Hour.ToString());
            }

            if (date.Minute < 10)
            {
                selectedCollection.Add("0" + date.Minute);
            }
            else
            {
                selectedCollection.Add(date.Minute.ToString());
            }

            return selectedCollection;
        }
    }

    public enum SyncFusionPickerType
    {
        DateTime,
        Date,
        Time
    }
}
