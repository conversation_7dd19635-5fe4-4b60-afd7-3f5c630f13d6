﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml;
using Ifs.Uma.Utility;
using SQLitePCL;
using OperatingSystem = Ifs.Uma.Utility.OperatingSystem;

namespace Ifs.Uma.Database.SQLite
{
    public class SQLiteException : DbException, IWriteAttributes
    {
        public int Result { get; private set; }

        public SQLiteException()
            : this(raw.SQLITE_ERROR, null, null)
        { }

        public SQLiteException(string message)
            : this(raw.SQLITE_ERROR, message, null)
        { }

        public SQLiteException(string message, Exception inner)
            : this(raw.SQLITE_ERROR, message, inner)
        { }

        public SQLiteException(int r)
            : this(r, null, null)
        { }

        public SQLiteException(int r, string message)
            : this(r, message, null)
        { }

        public SQLiteException(int r, string message, Exception inner)
            : base(message, inner)
        {
            Result = r;

            string needle = "NOT NULL constraint failed: ";
            if (message.StartsWith(needle))
            {
                ExceptionType = DbExceptionType.NotNullConstraintFailed;
            }
        }

        public void WriteAttributes(XmlWriter writer)
        {
            if (writer != null)
            {
                writer.WriteAttributeString("result", Result.ToString(CultureInfo.InvariantCulture));
            }
        }
    }

    internal static class SafeNativeMethods
    {
        public static int SQLiteClose(sqlite3 db)
        {
            if (DeviceInfo.OperatingSystem == OperatingSystem.Windows)
            {
                return raw.sqlite3_close_v2(db);
            }
            else
            {
                // iOS ships with on older version of SQLite which does not include sqlite3_close_v2
                return raw.sqlite3_close(db);
            }
        }
    }

    internal static class SQLiteHelper
    {
        public static sqlite3_stmt[] PrepareCommand(sqlite3 db, string sql)
        {
            // a SQL command may contain many statements
            IList<sqlite3_stmt> result = new List<sqlite3_stmt>();
            int r;
            sqlite3_stmt statement;
            string zTail;
            for (; ;)
            {
                r = raw.sqlite3_prepare_v2(db, sql, out statement, out zTail);
                if (r != raw.SQLITE_OK)
                {
                    break;
                }
                if (statement != null)
                {
                    result.Add(statement);
                }
                if (string.IsNullOrWhiteSpace(zTail))
                {
                    break;
                }
                // loop back for the next statement
                sql = zTail;
            }
            if (r != raw.SQLITE_OK)
            {
                string errMsg = raw.sqlite3_errmsg(db);
                FinalizeStatements(result, false);
                throw new SQLiteException(r, errMsg);
            }
            if (!result.Any()) throw new SQLiteException("No statements in command");
            return result.ToArray();
        }

        public static void FinalizeStatements(IList<sqlite3_stmt> statements, bool exceptLast)
        {
            if (statements != null)
            {
                int n = exceptLast ? statements.Count - 1 : statements.Count;
                for (int i = 0; i < n; i++)
                {
                    sqlite3_stmt statement = statements[i];
                    if (statement != null)
                    {
                        raw.sqlite3_finalize(statement);
                    }
                }
            }
        }
    }
}
