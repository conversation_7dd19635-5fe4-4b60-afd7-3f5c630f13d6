﻿using Ifs.Uma.Database;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.Data
{
    public class AttributeMappingSource : MappingSource
    {
        public AttributeMappingSource(ISystemData systemData, ILogger logger, Type enumAttributeClass)
        {
            m_systemData = systemData;
            m_logger = logger;

            if (enumAttributeClass != null)
            {
                TypeInfo enumAttributeInfo = enumAttributeClass.GetTypeInfo();
                m_enumConventions = enumAttributeInfo.GetCustomAttributes<EnumServerNamingAttribute>()
                    .Where(x => x.EnumType != null)
                    .ToSafeDictionary(k => k.EnumType, v => v.Convention);
                m_enumServerNames = enumAttributeInfo.GetCustomAttributes<EnumServerValueAttribute>()
                    .Where(x => !string.IsNullOrEmpty(x.ServerName))
                    .ToSafeDictionary(k => k.EnumValue, v => v.ServerName);
                m_syncRules = enumAttributeInfo.GetCustomAttributes<ColumnSyncRuleAttribute>()
                    .Where(x => x.RowType != null && !string.IsNullOrEmpty(x.PropertyName))
                    .ToArray();
                m_columnNames = enumAttributeInfo.GetCustomAttributes<ColumnNameAttribute>()
                    .Where(x => x.RowType != null && !string.IsNullOrEmpty(x.PropertyName) && !string.IsNullOrEmpty(x.ColumnName))
                    .ToArray();
            }
        }

        public override ISystemData SystemData { get { return m_systemData; } }

        protected override IMetaModel CreateModel(Type dataContextType)
        {
            // validate the DataContext type
            return dataContextType != null &&
                typeof(DataContextBase).GetTypeInfo().IsAssignableFrom(dataContextType.GetTypeInfo()) ?
                new AttributeMetaModel(dataContextType, this) : null; 
        }

        protected override IMetaEnumeration CreateEnumeration(Type enumType)
        {
            // validate the enum type
            return enumType != null && enumType.GetTypeInfo().IsEnum ?
                MetaEnumeration.Create(enumType, GetEnumValues(enumType)) : null;
        }

        private static string GetRowTypeTableName(Type rowType, TableAttribute table)
        {
            string rowTypeName = rowType.Name.RemoveSuffix("Row");
            string tableName = table.Name;
            if (string.IsNullOrEmpty(tableName))
            {
                tableName = rowTypeName.ToLowerCaseUnderscore();
            }

            return tableName;
        }

        /// <summary>
        /// Looking for all the properties with a public get method in the contextType
        ///   that are a kind of Table&lt;T&gt; and returning all the Ts
        /// </summary>
        /// <param name="contextType">The context type to search</param>
        /// <returns>All the row types identified by properties of the context type</returns>
        private IDictionary<string, Type> GetRowTypes(Type contextType)
        {
            if (contextType == null) throw new ArgumentNullException("contextType");
            IDictionary<string, Type> result = new Dictionary<string, Type>();
            foreach (PropertyInfo rowProperty in contextType.GetRuntimeProperties())
            {
                // find the rowtype - must be a kind of IEnumerable<rowType>
                Type rowType = TypeHelper.GetElementType2(rowProperty.PropertyType);
                if (rowType != null)
                {
                    // the rowType must have a Table attribute
                    TableAttribute tableAttrib = rowType.GetTypeInfo().GetCustomAttribute<TableAttribute>();
                    if (tableAttrib != null)
                    {
                        Type tableType = typeof(Table<>).MakeGenericType(rowType);
                        // is the property type a kind of Table<rowType>
                        if (tableType.GetTypeInfo().IsAssignableFrom(rowProperty.PropertyType.GetTypeInfo()))
                        {
                            string tableName = GetRowTypeTableName(rowType, tableAttrib);
                            result[tableName] = rowType;
                        }
                    }
                }
            }
            if (!result.Any())
            {
                m_logger.Warning(Strings.NoTablesInDataContext, contextType.FullName);
            }
            return result;
        }

        /// <summary>
        /// Returns MetaTable information about the given rowType
        /// The row type need not have an associated "Table" property in the data context.
        /// It must have a Table attribute and some properties with a Column Attribute
        /// </summary>
        /// <param name="rowType">row type to inspect</param>
        /// <param name="model">parent model</param>
        /// <returns>Meta Table information or null if invalid</returns>
        private IMetaTable GetMetaTable(Type rowType, IMetaModel model)
        {
            if (rowType == null) throw new ArgumentNullException("rowType");
            if (model == null) throw new ArgumentNullException("model");
            // rowType must have a Table attribute and some fields/properties with Column attributes
            IMetaTable result = null;
            TypeInfo rowInfo = rowType.GetTypeInfo();
            TableAttribute table = rowInfo.GetCustomAttribute<TableAttribute>();
            if (table != null)
            {
                string tableName = GetRowTypeTableName(rowType, table);
                MetaTableClass classification = table.Class;
                IEnumerable<IMetaDataMember> dataMembers = Columns(rowType);
                if (dataMembers != null && dataMembers.Any() && !dataMembers.Any(x => x == null))
                {
                    int numAutoIncrement = dataMembers.Count(x => x.AutoIncrement);
                    int numPrimaryKey = dataMembers.Count(x => x.PrimaryKey);
                    if (numAutoIncrement <= 1)
                    {
                        if (numAutoIncrement == 0 || numPrimaryKey == 1)
                        {
                            IEnumerable<IMetaIndex> indexes = GetIndexes(rowInfo, tableName, dataMembers);
                            dataMembers = dataMembers.ToArray();

                            result = MetaTable.Create(tableName, null, classification, TableImplementation.Table, model, rowType, dataMembers, indexes, null, null);
                        }
                        else
                        {
                            m_logger.Warning(Strings.AutoIncrementNotSolePrimaryKey, rowType.FullName);
                        }
                    }
                    else
                    {
                        m_logger.Warning(Strings.TooManyAutoIncrementColumns, rowType.FullName);
                    }
                }
                else
                {
                    m_logger.Warning(Strings.NoColumnsForRowType, rowType.FullName);
                }
            }
            else
            {
                m_logger.Warning(Strings.InvalidRowType, rowType.FullName);
            }
            return result;
        }

        private IEnumerable<IMetaDataMember> Columns(Type rowType)
        {
            if (rowType == null) throw new ArgumentNullException("rowType");
            PropertyInfo stringIndexer = TypeHelper.GetPropertyInfo(rowType, "Item");

            ICollection<IMetaDataMember> result = new List<IMetaDataMember>();

            IDictionary<string, SyncRule> syncRules = m_syncRules != null ? m_syncRules
                .Where(x => x.RowType == rowType)
                .ToSafeDictionary(k => k.PropertyName, v => v.Sync) : null;
            IDictionary<string, string> columnNames = m_columnNames != null ? m_columnNames
                .Where(x => x.RowType == rowType)
                .ToSafeDictionary(k => k.PropertyName, v => v.ColumnName) : null;
            Columns(rowType, result, 0, syncRules, columnNames);
            return result.AsEnumerable();
        }

        private int Columns(Type rowType, ICollection<IMetaDataMember> result, int startIndex,
            IDictionary<string, SyncRule> syncRules, IDictionary<string, string> columnNames)
        {
            TypeInfo rowInfo = rowType.GetTypeInfo();
            Type baseType = rowInfo.BaseType;
            int index = startIndex;
            if (baseType != null)
            {
                index = Columns(baseType, result, index, syncRules, columnNames);
            }
            foreach (PropertyInfo prop in rowInfo.DeclaredProperties)
            {
                ColumnAttribute ca = prop.GetCustomAttribute<ColumnAttribute>(true);
                if (ca != null)
                {
                    string propertyName = prop.Name;
                    Type propertyType = prop.PropertyType;
                    Type nonNullType = TypeHelper.GetNonNullableType(propertyType);
                    if (TypeHelper.ValidateDbType(nonNullType) != TypeCode.Object)
                    {
                        string columnName;
                        if (columnNames == null || !columnNames.TryGetValue(propertyName, out columnName))
                        {
                            columnName = string.IsNullOrEmpty(ca.Name) ? propertyName.ToLowerCaseUnderscore() : ca.Name;
                        }

                        SyncRule sync = SyncRule.Modified;
                        if (syncRules != null)
                        {
                            syncRules.TryGetValue(propertyName, out sync);
                        }

                        // If we have an existing member the field must be a custom field
                        IMetaDataMember existingMember = result.FirstOrDefault(x => x.ColumnName == columnName);
                        IMetaDataMember mdm;
                        if (existingMember == null)
                        {
                            mdm = CreateDataMember(rowType, index, prop, ca, propertyName, propertyType, nonNullType,
                                columnName, sync);
                            index++;
                        }
                        else
                        {
                            result.Remove(existingMember);
                            mdm = UpdateDataMember(rowType, prop, ca, propertyName, propertyType, nonNullType,
                                columnName, sync, existingMember);
                        }
                        result.Add(mdm);
                    }
                    else
                    {
                        m_logger.Warning(Strings.InvalidColumnType,
                            propertyName, propertyType.FullName, rowType.FullName);
                    }
                }
            }
            return index;
        }

        private IMetaDataMember CreateDataMember(Type rowType, int index, PropertyInfo prop, ColumnAttribute ca, string propertyName, Type propertyType, Type nonNullType, string columnName, SyncRule sync)
        {
            bool mandatory = ColumnIsMandatory(ca, propertyType);
            bool insertable = ca.Insertable;
            bool updatable = ca.Updateable;
            int maxLength = ca.MaxLength;
            int scale = ca.Scale;
            FieldInfo fi = ValidateStorage(rowType, ca.Storage, propertyName, propertyType);
            bool autoIncrement = ValidateAutoIncrement(rowType, ca.AutoIncrement, propertyName, propertyType);
            bool primaryKey = mandatory && (ca.PrimaryKey || autoIncrement);
            if (ca.PrimaryKey && !mandatory)
            {
                m_logger.Warning(Strings.InvalidPrimaryKey,
                    propertyName, rowType.FullName);
            }
            TextFormats tf = ca.TextFormat;
            DateFormats df = ca.DateFormat;
            NumberFormat nf = ca.NumberFormat;

            IMetaEnumeration enumeration = nonNullType.GetTypeInfo().IsEnum ? GetEnumeration(nonNullType) : null;

            return MetaDataMember.Create(columnName, null, mandatory, insertable,
                updatable, maxLength, scale, fi, prop, autoIncrement, primaryKey, ca.ServerPrimaryKey, tf, df, nf, enumeration,
                index, null, null, null, null, sync, false, false, false);
        }

        private IMetaDataMember UpdateDataMember(Type rowType, PropertyInfo prop, ColumnAttribute ca, string propertyName,
            Type propertyType, Type nonNullType, string columnName, SyncRule sync, IMetaDataMember existingMember)
        {
            // Create a new meta data member base on the custom field data
            FieldInfo fi = ValidateStorage(rowType, ca.Storage, propertyName, propertyType);
            ValidateClientTypeMatchesServerType(rowType, fi, propertyName, existingMember.ColumnType);

            IMetaEnumeration serverEnumeration = existingMember.Enumeration;
            IMetaEnumeration enumeration = nonNullType.GetTypeInfo().IsEnum ? UpdateEnumeration(nonNullType, serverEnumeration) : serverEnumeration;

            return MetaDataMember.Create(columnName, existingMember.DisplayName, existingMember.Mandatory, 
                existingMember.Insertable, existingMember.Updateable, existingMember.MaxLength, existingMember.Scale,
                fi, prop, existingMember.AutoIncrement, existingMember.PrimaryKey, existingMember.ServerPrimaryKey, existingMember.TextFormat,
                existingMember.DateFormat, existingMember.NumberFormat, enumeration,
                existingMember.Index, existingMember.ServerIndex, null, null, null, sync, false, false, false);
        }
   
        private static void ValidateClientTypeMatchesServerType(Type rowType, FieldInfo fi, string propertyName, Type serverType)
        {
            Type fieldType = TypeHelper.GetNonNullableType(fi.FieldType);

            bool matchesType = fieldType == TypeHelper.GetNonNullableType(serverType);
            bool isEnum = fieldType.GetTypeInfo().IsEnum && serverType == typeof(string);
            bool isBool = fieldType == typeof(bool) && serverType == typeof(string);
            if (!matchesType && !isEnum && !isBool)
            {
                throw new InvalidOperationException(
                    string.Format(Strings.Culture, Strings.ColumnTypeMismatch, 
                    rowType.FullName, propertyName, fi.FieldType.FullName, serverType.FullName));
            }
        }

        private static bool ColumnIsMandatory(ColumnAttribute ca, Type propertyType)
        {
            bool result;
            TypeInfo propTypeInfo = propertyType.GetTypeInfo();
            if (propTypeInfo.IsGenericType && propertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                // all nullable types are not mandatory
                result = false;
            }
            else if (propTypeInfo.IsValueType)
            {
                // all value types are mandatory
                result = true;
            }
            else
            {
                // otherwise it depends on the Column attribute
                result = ca.Mandatory || ca.PrimaryKey;
            }
            return result;
        }

        private bool ValidateAutoIncrement(Type rowType, bool autoIncrement, string propertyName, Type propertyType)
        {
            bool result = autoIncrement && (propertyType == typeof(long) || propertyType == typeof(int));
            if (autoIncrement && !result)
            {
                m_logger.Warning(Strings.InvalidAutoIncrement,
                    propertyName, rowType.FullName);
            }
            return result;
        }

        private FieldInfo ValidateStorage(Type rowType, string storage, string propertyName, Type propertyType)
        {
            FieldInfo fi = null;
            if (!string.IsNullOrEmpty(storage))
            {
                fi = TypeHelper.GetField(rowType, storage);
                if (fi != null)
                {
                    if (fi.FieldType != propertyType)
                    {
                        fi = null;
                        m_logger.Warning(Strings.StorageTypeMismatch,
                            storage, propertyName, rowType.FullName);
                    }
                }
                else
                {
                    m_logger.Warning(Strings.StorageNotFound,
                        storage, propertyName, rowType.FullName);
                }
            }
            return fi;
        }

        private IEnumerable<IMetaIndex> GetIndexes(TypeInfo rowInfo, string tableName, IEnumerable<IMetaDataMember> columns)
        {
            if (rowInfo == null) throw new ArgumentNullException("rowInfo");
            if (string.IsNullOrEmpty(tableName)) throw new ArgumentNullException("tableName");
            if (columns == null) throw new ArgumentNullException("columns");
            ICollection<IMetaIndex> result = new List<IMetaIndex>();
            int idx = 0;
            IEnumerable<IndexAttribute> indexes = rowInfo.GetCustomAttributes<IndexAttribute>(true);
            if (indexes != null)
            {
                foreach (IndexAttribute index in indexes.Where(x => x != null))
                {
                    string indexName = index.Name;
                    bool unique = index.Unique;
                    if (string.IsNullOrEmpty(indexName))
                    {
                        // generate a default name
                        idx++;
                        indexName = (unique ? "uq_" : "ix_") + tableName + "_" + ObjectConverter.ToString(idx);
                    }
                    IEnumerable<IMetaDataMember> indexColumns = columns.GetMembersForProperties(index.Columns, rowInfo.FullName, m_logger);
                    if (indexColumns != null && indexColumns.Any() && !indexColumns.Any(x => x == null))
                    {
                        result.Add(MetaIndex.Create(indexName, indexColumns, unique));
                    }
                    else
                    {
                        m_logger.Warning(Strings.InvalidColumnsForIndex, indexName, rowInfo.FullName);
                    }
                }
            }

            IMetaDataMember[] serverKeyColumns = columns.Where(x => x.ServerPrimaryKey).ToArray();
            if (serverKeyColumns.Length > 0)
            {
                bool indexAlreadyAdded = result.Any(x => 
                    x.Columns.Count() == serverKeyColumns.Length && 
                    !x.Columns.Except(serverKeyColumns).Any());

                if (!indexAlreadyAdded)
                {
                    idx++;
                    string indexName = "uq_" + tableName + "_" + ObjectConverter.ToString(idx);
                    result.Add(MetaIndex.Create(indexName, serverKeyColumns, true));
                }
            }

            return result.Any() ? result.AsEnumerable() : null;
        }

        private IEnumerable<IMetaEnumValue> GetEnumValues(Type enumType)
        {
            if (enumType == null) throw new ArgumentNullException("enumType");
            ICollection<IMetaEnumValue> values = null;
            TypeInfo enumInfo = enumType.GetTypeInfo();
            if (enumType != null && enumInfo != null && enumInfo.IsEnum)
            {
                NamingConvention convention = GetConvention(enumType);
                values = new List<IMetaEnumValue>();
                foreach (object enumValue in Enum.GetValues(enumType))
                {
                    string serverValue = GetServerValue(enumInfo, enumValue, convention);
                    values.Add(MetaEnumValue.Create(serverValue, null, enumValue, false));
                }
            }
            else
            {
                m_logger.Warning(Strings.InvalidEnumType, enumType.FullName);
            }
            return values.AsEnumerable();
        }

        private NamingConvention GetConvention(Type enumType)
        {
            // enumAttributeClass setting takes precedence
            NamingConvention convention;
            if (m_enumConventions != null && m_enumConventions.TryGetValue(enumType, out convention))
            {
                return convention;
            }
            // look for a local attribute on the enumType
            ServerNamingAttribute sn = enumType.GetTypeInfo().GetCustomAttribute<ServerNamingAttribute>();
            if (sn != null)
            {
                return sn.Convention;
            }
            return NamingConvention.UpperCaseUnderscore;
        }

        private string GetServerValue(TypeInfo enumInfo, object enumValue, NamingConvention convention)
        {
            string serverName;
            if (m_enumServerNames != null && m_enumServerNames.TryGetValue(enumValue, out serverName))
            {
                    return serverName;
            }
            string enumName = enumValue.ToString();
            ServerValueAttribute sv = enumInfo.GetDeclaredField(enumName)
                .GetCustomAttribute<ServerValueAttribute>();
            if (sv != null)
            {
                if (!string.IsNullOrEmpty(sv.Name))
                {
                    return sv.Name;
                }
                m_logger.Warning(Strings.InvalidServerValue, enumInfo.FullName);
            }
            return convention.Convert(enumName);
        }

        private ISystemData m_systemData;
        private ILogger m_logger;

        private IDictionary<Type, NamingConvention> m_enumConventions;
        private IDictionary<object, string> m_enumServerNames;
        private IEnumerable<ColumnSyncRuleAttribute> m_syncRules;
        private IEnumerable<ColumnNameAttribute> m_columnNames;

        private class AttributeMetaModel : MetaModel
        {
            public AttributeMetaModel(Type contextType, AttributeMappingSource source)
                : base(contextType)
            {
                if (source == null) throw new ArgumentNullException(nameof(source));

                _source = source;
            }

            public override MappingSource Source => _source;

            protected override IMetaTable CreateTable(string tableName)
            {
                SetupTableNames();

                Type rowType;
                if (_tableTypes.TryGetValue(tableName, out rowType))
                {
                    return _source.GetMetaTable(rowType, this);
                }

                return null;
            }

            public override IEnumerable<string> GetTableNames()
            {
                SetupTableNames();
                return _tableTypes.Keys.ToArray();
            }

            public override string GetTableName(Type rowType)
            {
                SetupTableNames();
                string tableName;
                if (_typeTables.TryGetValue(rowType, out tableName))
                {
                    return tableName;
                }
                return null;
            }

            protected override IEnumerable<IMetaRelation> CreateRelations(IMetaTable table)
            {
                TypeInfo rowTypeInfo = table.RowType.GetTypeInfo();
                List<IMetaRelation> relations = new List<IMetaRelation>();
                
                foreach (ObjConnectionAttribute attrib in rowTypeInfo.GetCustomAttributes<ObjConnectionAttribute>())
                {
                    // Passing null on Reference Name parameter as it is not possible to read it here.
                    IMetaRelation relation = ObjConnectionRelation.Create(table, attrib.LuNameColumn, attrib.KeyRefColumn, null);
                    if (relations != null)
                    {
                        relations.Add(relation);
                    }
                }
                
                foreach (RelationAttribute relAttrib in rowTypeInfo.GetCustomAttributes<RelationAttribute>())
                {
                    IMetaTable referencedTable = GetTable(relAttrib.ReferencedRowType);
                    if (referencedTable != null)
                    {
                        // Passing null on Reference Name parameter as it is not possible to read it here.
                        IMetaRelation relation = ReferenceRelation.Create(table, referencedTable, relAttrib.Columns, relAttrib.ReferencedColumns, null);
                        if (relations != null)
                        {
                            relations.Add(relation);
                        }
                    }
                }

                return relations.ToArray();
            }

            private void SetupTableNames()
            {
                if (_tableTypes == null)
                {
                    _tableTypes = _source.GetRowTypes(ContextType);
                    Dictionary<Type, string> typeNames = new Dictionary<Type, string>();
                    foreach (var kvp in _tableTypes)
                    {
                        typeNames[kvp.Value] = typeNames.ContainsKey(kvp.Value) ? null : kvp.Key;
                    }
                    _typeTables = typeNames;
                }
            }
            private AttributeMappingSource _source;
            private IDictionary<string, Type> _tableTypes;
            private IDictionary<Type, string> _typeTables;

            private sealed class ObjConnectionRelation : IMetaRelation
            {
                public static IMetaRelation Create(IMetaTable table, string luNameColumn, string keyRefColumn, string referenceName)
                {
                    if (luNameColumn == null || keyRefColumn == null)
                    {
                        return null;
                    }

                    IMetaDataMember luNameMember = table.FindMemberByPropertyName(luNameColumn);
                    IMetaDataMember keyRefMember = table.FindMemberByPropertyName(keyRefColumn);

                    if (luNameMember == null || keyRefMember == null)
                    {
                        return null;
                    }

                    return new ObjConnectionRelation(table, luNameMember, keyRefMember, referenceName);
                }

                private ObjConnectionRelation(IMetaTable table, IMetaDataMember luNameMember, IMetaDataMember keyRefMember, string referenceName)
                {
                    Table = table;
                    Columns = new[] { luNameMember, keyRefMember };
                    ReferenceName = referenceName;
                }

                public RelationType RelationType => RelationType.ObjectConnection;
                public IMetaTable Table { get; }
                public IEnumerable<IMetaDataMember> Columns { get; }
                public IMetaTable ReferencedTable { get; }
                public IEnumerable<IMetaDataMember> ReferencedColumns { get; }
                public string ReferenceName { get; }
            }

            private sealed class ReferenceRelation : IMetaRelation
            {
                public static IMetaRelation Create(IMetaTable table, IMetaTable referencedTable, string columns, string referencedColumns, string referenceName)
                {
                    IMetaDataMember[] members = table.DataMembers.GetMembersForProperties(columns, null, null)?.ToArray();
                    IMetaDataMember[] referencedMembers = referencedTable.DataMembers.GetMembersForProperties(referencedColumns, null, null)?.ToArray();

                    if (members == null || referencedMembers == null || members.Length != referencedMembers.Length)
                    {
                        return null;
                    }

                    return new ReferenceRelation(table, members, referencedTable, referencedMembers, referenceName);
                }

                private ReferenceRelation(
                    IMetaTable table, IEnumerable<IMetaDataMember> columns,
                    IMetaTable referencedTable, IEnumerable<IMetaDataMember> referencedColumns, string referenceName)
                {
                    Table = table;
                    Columns = columns;
                    ReferencedTable = referencedTable;
                    ReferencedColumns = referencedColumns;
                    ReferenceName = referenceName;
                }

                public RelationType RelationType => RelationType.Reference;
                public IMetaTable Table { get; }
                public IEnumerable<IMetaDataMember> Columns { get; }
                public IMetaTable ReferencedTable { get; }
                public IEnumerable<IMetaDataMember> ReferencedColumns { get; }
                public string ReferenceName { get; }

                public override string ToString()
                {
                    string cols = string.Join(", ", Columns.Select(x => x.ColumnName));
                    string refCols = string.Join(", ", ReferencedColumns.Select(x => x.ColumnName));

                    return $"{RelationType}: {Table.TableName} ({cols}) => {ReferencedTable.TableName} ({refCols})";
                }
            }
        }
    }
}
