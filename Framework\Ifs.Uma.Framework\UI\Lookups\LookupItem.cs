﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Ifs.Uma.Localization;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Images;
using Ifs.Uma.UI.Observables;

namespace Ifs.Uma.Framework.UI.Lookups
{
    public enum LookupItemType
    {
        None,
        Unknown,
        Search,
        Item,
        RecentlyUsed,
        Favorite
    }

    public class LookupItem : ObservableBase
    {
        public enum LookupItemAction
        {
            Favorite,
            Unfavorite,
            Forget
        }

        public UmaImage SelectedImage => IconUtils.Check;

        private bool _isSelected;
        public bool IsSelected
        {
            get { return _isSelected; }
            set { SetProperty(ref _isSelected, value); }
        }

        public static readonly LookupItem None = new LookupItem(LookupItemType.None, Strings.None);

        public LookupItemType ItemType { get; }

        private ObservableCollection<LookupItemAction> _actions = new ObservableCollection<LookupItemAction>();
        public IReadOnlyList<LookupItemAction> Actions => _actions;

        public object Item { get; }

        private string _text = null;
        public string Text
        {
            get { return _text; }
            set { SetProperty(ref _text, value); }
        }

        public event EventHandler ActionsChanged;

        public LookupItem(LookupItemType type, object item)
        {
            ItemType = type;
            Item = item;

            switch (type)
            {
                case LookupItemType.Favorite:
                    SetFavoriteActions();
                    break;
                case LookupItemType.RecentlyUsed:
                    SetRecentlyUsedActions();
                    break;
                case LookupItemType.Item:
                    SetDefaultActions();
                    break;
            }

            _actions.CollectionChanged += Actions_CollectionChanged;
        }

        public LookupItem(LookupItemType type, string text)
        {
            ItemType = type;
            Text = text;

            switch (type)
            {
                case LookupItemType.Favorite:
                    SetFavoriteActions();
                    break;
                case LookupItemType.RecentlyUsed:
                    SetRecentlyUsedActions();
                    break;
                case LookupItemType.Item:
                    SetDefaultActions();
                    break;
            }

            _actions.CollectionChanged += Actions_CollectionChanged;
        }

        private void Actions_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            ActionsChanged?.Invoke(sender, e);
        }

        public override string ToString()
        {
            return Text?.ToString();
        }

        public void SetFavoriteActions()
        {
            ClearAllActions();
            _actions.Add(LookupItemAction.Unfavorite);
            OnPropertyChanged(nameof(Actions));
        }

        public void SetRecentlyUsedActions()
        {
            ClearAllActions();
            _actions.Add(LookupItemAction.Favorite);
            _actions.Add(LookupItemAction.Forget);
            OnPropertyChanged(nameof(Actions));
        }

        public void SetDefaultActions()
        {
            ClearAllActions();
            _actions.Add(LookupItemAction.Favorite);
            OnPropertyChanged(nameof(Actions));
        }

        public void ClearAllActions()
        {
            _actions.Clear();
            OnPropertyChanged(nameof(Actions));
        }

        public static int? TryCompareLookupItemTypes(LookupItemType x, LookupItemType y)
        {
            if (x == LookupItemType.None && y == LookupItemType.Item)
            {
                return -1;
            }

            if (x == LookupItemType.None && y == LookupItemType.Favorite)
            {
                return -1;
            }

            if (x == LookupItemType.None && y == LookupItemType.RecentlyUsed)
            {
                return -1;
            }

            if (x == LookupItemType.None && y == LookupItemType.None)
            {
                return 0;
            }

            if (x == LookupItemType.Item && y == LookupItemType.None)
            {
                return 1;
            }

            if (x == LookupItemType.Item && y == LookupItemType.Favorite)
            {
                return 1;
            }

            if (x == LookupItemType.Item && y == LookupItemType.RecentlyUsed)
            {
                return 1;
            }

            if (x == LookupItemType.Item && y == LookupItemType.Item)
            {
                return 0;
            }

            if (x == LookupItemType.Favorite && y == LookupItemType.None)
            {
                return 1;
            }

            if (x == LookupItemType.Favorite && y == LookupItemType.Item)
            {
                return -1;
            }

            if (x == LookupItemType.Favorite && y == LookupItemType.RecentlyUsed)
            {
                return -1;
            }

            if (x == LookupItemType.Favorite && y == LookupItemType.Favorite)
            {
                return 0;
            }

            if (x == LookupItemType.RecentlyUsed && y == LookupItemType.None)
            {
                return 1;
            }

            if (x == LookupItemType.RecentlyUsed && y == LookupItemType.Item)
            {
                return -1;
            }

            if (x == LookupItemType.RecentlyUsed && y == LookupItemType.Favorite)
            {
                return 1;
            }

            if (x == LookupItemType.RecentlyUsed && y == LookupItemType.RecentlyUsed)
            {
                return 0;
            }

            return null;
        }
    }
}
