﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ListTests : ProcedureTest
    {
        [Test]
        public async Task AddItem()
        {
            //procedure Function<TestListAdd> List<Structure(TstCustomer)> {
            //    variable Result List<Structure(TstCustomer)>;
            //    variable Customer Structure(TstCustomer);
            //    execute {
            //        create Customer;
            //        set Customer.CustomerNo = "CA";
            //        call List.Add(Result, Customer);
            //        create Customer;
            //        set Customer.CustomerNo = "CB";
            //        call List.Add(Result, Customer);
            //        return Result;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestListAdd", null);
            CheckResults(result, new[] { "CA", "CB" });
        }

        [Test]
        public async Task RemoveItem()
        {
            //procedure Function<TestListRemove> List<Structure(TstCustomer)> {
            //    variable Result List<Structure(TstCustomer)>;
            //    variable Customer Structure(TstCustomer);
            //    execute {
            //        create Customer;
            //        set Customer.CustomerNo = "CA";
            //        call List.Add(Result, Customer);
            //        create Customer;
            //        set Customer.CustomerNo = "CB";
            //        call List.Add(Result, Customer);
            //        call List.Remove(Result, Customer);
            //        return Result;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestListRemove", null);
            CheckResults(result, new[] { "CA" });
        }

        [Test]
        public async Task Clear()
        {
            //procedure Function<TestListClear> List<Structure(TstCustomer)> {
            //    variable Result List<Structure(TstCustomer)>;
            //    variable Customer Structure(TstCustomer);
            //    execute {
            //        create Customer;
            //        set Customer.CustomerNo = "CA";
            //        call List.Add(Result, Customer);
            //        call List.Add(Result, Customer);
            //        call List.Clear(Result);
            //        return Result;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestListClear", null);
            CheckResults(result, new string[0]);
        }

        [Test]
        public async Task Count()
        {
            //procedure Function<TestListCount> List<Structure(TstCustomer)> {
            //    variable TheList List<Structure(TstCustomer)>;
            //    variable Customer Structure(TstCustomer);
            //    variable ListCount Number;
            //    execute {
            //        create Customer;
            //        set Customer.CustomerNo = "CA";
            //        call List.Add(TheList, Customer);
            //        call List.Add(TheList, Customer);
            //        call List.Add(TheList, Customer);
            //        call List.Count(TheList) into ListCount;
            //        return ListCount;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestListCount", null);
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            Assert.AreEqual(3, result.Value as long?);
        }

        [Test]
        public async Task Get()
        {
            //procedure Function<TestListGet> List<Structure(TstCustomer)> {
            //    variable Result List<Structure(TstCustomer)>;
            //    variable Customer Structure(TstCustomer);
            //    variable Customer1 Structure(TstCustomer);
            //    execute {
            //        create Customer;
            //        set Customer.CustomerNo = "CA";
            //        call List.Add(Result, Customer);
            //        create Customer;
            //        set Customer.CustomerNo = "CB";
            //        call List.Add(Result, Customer);
            //        call List.Get(Result, 1) into Customer1;
            //        return Customer1;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestListGet", null);

            RemoteRow row = result.Value as RemoteRow;
            Assert.IsNotNull(row);
            Assert.AreEqual(row["CustomerNo"], "CB");
        }

        [Test]
        public async Task IndexOf()
        {
            //procedure Function<TestListIndexOf> List<Structure(TstCustomer)> {
            //    variable Result List<Structure(TstCustomer)>;
            //    variable Customer Structure(TstCustomer);
            //    variable ListIndex Number;
            //    execute {
            //        create Customer;
            //        set Customer.CustomerNo = "CA";
            //        call List.Add(Result, Customer);
            //        create Customer;
            //        set Customer.CustomerNo = "CB";
            //        call List.Add(Result, Customer);
            //        call List.IndexOf(Result, Customer) into Index;
            //        return Index;
            //    }
            //}

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestListIndexOf", null);
            Assert.IsNotNull(result);

            int index = Convert.ToInt32(result.Value);
            Assert.AreEqual(index, 1);
        }

        [Test]
        public async Task IndexOfAdvanced()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            // Check index of a record with objkey (something already existing in the DB)
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "TestListIndexOfAdvanced1", null);
            Assert.IsNotNull(result);

            int index = Convert.ToInt32(result.Value);
            Assert.AreEqual(index, 2);

            // Check index of a record without objkey - using primary key string (something created within the procedure)
            result = await executor.CallFunctionAsync(TestOfflineProjection, "TestListIndexOfAdvanced2", null);
            Assert.IsNotNull(result);

            index = Convert.ToInt32(result.Value);
            Assert.AreEqual(index, 6);
        }

        private static void CheckResults(ExecuteResult result, string[] customerNos)
        {
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            MarbleList list = result.Value as MarbleList;
            Assert.IsNotNull(list);
            Assert.AreEqual(customerNos.Length, list.Count);

            RemoteRow[] rows = list.Cast<RemoteRow>().ToArray();

            for (int i = 0; i < customerNos.Length; i++)
            {
                Assert.AreEqual(customerNos[i], rows[i]["CustomerNo"]);
            }
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ListTestsSchema", "Execution.Procedures.ListTestsData");
        }
    }
}
