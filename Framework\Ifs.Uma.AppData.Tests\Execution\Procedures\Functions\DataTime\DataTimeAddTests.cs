﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.DataTime
{
    [TestFixture]
    public class DataTimeAddTests : ProcedureTest
    {
        [Test]
        public async Task DateTime_AddYears()
        {
            //procedure Function<DateTime_AddYears> Timestamp {
            //    parameter MyDate Timestamp;
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.AddYears(MyDate, 2) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDate"] = new DateTime(2000, 7, 9, 12, 34, 56);

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_AddYears", param);
            CheckResults(result, new DateTime(2002, 7, 9, 12, 34, 56));
        }

        [Test]
        public async Task DateTime_AddMonths()
        {
            //procedure Function<DateTime_AddMonths> Timestamp {
            //    parameter MyDate Timestamp;
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.AddMonths(MyDate, 3) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDate"] = new DateTime(2000, 7, 9, 12, 34, 56);

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_AddMonths", param);
            CheckResults(result, new DateTime(2000, 10, 9, 12, 34, 56));
        }

        [Test]
        public async Task DateTime_AddDays()
        {
            //procedure Function<DateTime_AddDays> Timestamp {
            //    parameter MyDate Timestamp;
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.AddDays(MyDate, 1.5) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDate"] = new DateTime(2000, 7, 9, 12, 34, 56);

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_AddDays", param);
            CheckResults(result, new DateTime(2000, 7, 11, 0, 34, 56));
        }

        [Test]
        public async Task DateTime_AddHours()
        {
            //procedure Function<DateTime_AddHours> Timestamp {
            //    parameter MyDate Timestamp;
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.AddHours(MyDate, 1.5) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDate"] = new DateTime(2000, 7, 9, 12, 34, 56);

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_AddHours", param);
            CheckResults(result, new DateTime(2000, 7, 9, 14, 4, 56));
        }

        [Test]
        public async Task DateTime_AddMinutes()
        {
            //procedure Function<DateTime_AddMinutes> Timestamp {
            //    parameter MyDate Timestamp;
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.AddMinutes(MyDate, 1.5) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDate"] = new DateTime(2000, 7, 9, 12, 34, 56);

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_AddMinutes", param);
            CheckResults(result, new DateTime(2000, 7, 9, 12, 36, 26));
        }

        [Test]
        public async Task DateTime_AddSeconds()
        {
            //procedure Function<DateTime_AddSeconds> Timestamp {
            //    parameter MyDate Timestamp;
            //    variable Result Timestamp;
            //    execute {
            //        call DateTime.AddSeconds(MyDate, 65) into Result;
            //        return Result;
            //    }
            //}

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["MyDate"] = new DateTime(2000, 7, 9, 12, 34, 56);

            IProcedureExecutor executor = Resolve<IProcedureExecutor>();
            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "DateTime_AddSeconds", param);
            CheckResults(result, new DateTime(2000, 7, 9, 12, 36, 1));
        }

        private static void CheckResults(ExecuteResult result, object value)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
            Assert.AreEqual(value, result.Value);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.DataTime.DataTimeAddTestsSchema", null);
        }
    }
}
