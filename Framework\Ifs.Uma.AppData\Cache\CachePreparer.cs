﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Cache
{
    public interface ICachePreparer
    {
        Task<PrepareCacheResult> PrepareCacheAsync(string projectionName, string entityName, CancellationToken cancelToken = default(CancellationToken));
        Task<PrepareCacheResult> AddToCacheAsync(EntityQuery query, CancellationToken cancelToken = default(CancellationToken));
        Task<bool> IsCacheReadyAsync(string projectionName, string entityName);
        Task ClearCache();
        Task<bool> RefreshCache();
    }

    public sealed class CachePreparer : ICachePreparer, IDisposable
    {
        private readonly IMetadata _metadata;
        private readonly IOnlineDataHandler _onlineData;
        private readonly IDataContextProvider _db;
        private readonly ILogger _logger;
        private readonly IPerfLogger _perfLogger;
        private readonly ConcurrentDictionary<string, CachedEntity> _cachedEntities = new ConcurrentDictionary<string, CachedEntity>();
        private readonly object _updateTasksLock = new object();
        private readonly SemaphoreSlim _updateLock = new SemaphoreSlim(1);
        private readonly List<CachedEntityUpdateTask> _updateTasks = new List<CachedEntityUpdateTask>();
        private bool _disposed;

        public CachePreparer(IMetadata metadata, IOnlineDataHandler onlineData, IDataContextProvider db, ILogger logger, IPerfLogger perfLogger)
        {
            _onlineData = onlineData;
            _db = db;
            _logger = logger;
            _perfLogger = perfLogger;
            _metadata = metadata;
        }

        public async Task<PrepareCacheResult> PrepareCacheAsync(string projectionName, string entityName, CancellationToken cancelToken)
        {
            EntityInfo entity = EntityInfo.Get(_metadata, projectionName, entityName);

            if (entity != null)
            {
                foreach (string depEntity in entity.GetCacheDependencies())
                {
                    if (_metadata.GetEntitySyncPolicy(depEntity) != EntitySyncPolicy.ClientCache)
                    {
                        continue;
                    }

                    CachedEntity cachedEntity = GetCachedEntity(projectionName, depEntity);

                    if (await cachedEntity.IsReady())
                    {
                        continue;
                    }

                    PrepareCacheResult result = await PrepareCacheAsync(cachedEntity, cancelToken);
                    if (result != PrepareCacheResult.Ready)
                    {
                        return result;
                    }
                }
            }

            return PrepareCacheResult.Ready;
        }

        private async Task<PrepareCacheResult> PrepareCacheAsync(CachedEntity cachedEntity, CancellationToken cancelToken)
        {
            Task<PrepareCacheResult> updateTask = UpdateEntityCache(cachedEntity);

            TaskCompletionSource<bool> tcs = new TaskCompletionSource<bool>();
            cancelToken.Register(s => ((TaskCompletionSource<bool>)s).SetResult(true), tcs);
            await Task.WhenAny(updateTask, tcs.Task);

            cancelToken.ThrowIfCancellationRequested();

            return await updateTask;
        }

        public async Task<PrepareCacheResult> AddToCacheAsync(EntityQuery query, CancellationToken cancelToken = default(CancellationToken))
        {
            CachedEntity cachedEntity = GetCachedEntity(query.DataSource.ProjectionName, query.DataSource.EntityName);
            return await cachedEntity.AddToCacheAsync(query, cancelToken);
        }

        public async Task<bool> IsCacheReadyAsync(string projectionName, string entityName)
        {
            EntityInfo entity = EntityInfo.Get(_metadata, projectionName, entityName);

            if (entity != null)
            {
                foreach (string depEntity in entity.GetCacheDependencies())
                {
                    if (_metadata.GetEntitySyncPolicy(depEntity) != EntitySyncPolicy.ClientCache)
                    {
                        continue;
                    }

                    CachedEntity cachedEntity = GetCachedEntity(projectionName, depEntity);

                    if (!await cachedEntity.IsReady())
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        public async Task ClearCache()
        {
            _logger.Information("Clearing client table data cache");

            CleanupCachedEntities();

            await _updateLock.WaitAsync();
            try
            {
                await Task.Run((Action)ClearCachedData);
            }
            catch (Exception ex)
            {
                _logger.HandleException(ExceptionType.Recoverable, ex);
                throw;
            }
            finally
            {
                _updateLock.Release();
            }
        }

        private void ClearCachedData()
        {
            FwDataContext ctx = _db.CreateDataContext();
            List<IMetaTable> tablesToClear = new List<IMetaTable>();

            foreach (CacheStatus status in ctx.CacheStatuses)
            {
                ctx.CacheStatuses.Attach(status);
                status.IsReady = false;

                IMetaTable table = _metadata.GetTableForEntityName(status.EntityName);
                if (table != null)
                {
                    tablesToClear.Add(table);
                }
            }
            ctx.SubmitChanges(false);

            foreach (IMetaTable table in tablesToClear)
            {
                ctx.ClearCacheTable(table);
            }
        }

        public async Task<bool> RefreshCache()
        {
            await ClearCache();
            bool refreshed = false;
            FwDataContext ctx = _db.CreateDataContext();

            foreach (CacheStatus status in ctx.CacheStatuses)
            {
                _logger.Information("Preparing cache for: " + status.ProjectionName + "." + status.EntityName);

                PrepareCacheResult result = await PrepareCacheAsync(status.ProjectionName, status.EntityName, CancellationToken.None);
                if (result.Equals(PrepareCacheResult.Ready))
                {
                    refreshed = true;
                }
            }

            return refreshed;
        }

        private Task<PrepareCacheResult> UpdateEntityCache(CachedEntity cachedEntity)
        {
            CachedEntityUpdateTask updateTask = null;

            lock (_updateTasksLock)
            {
                foreach (CachedEntityUpdateTask entityUpdateTask in _updateTasks)
                {
                    if (entityUpdateTask.CachedEntity == cachedEntity)
                    {
                        updateTask = entityUpdateTask;
                        break;
                    }
                }

                if (updateTask == null)
                {
                    updateTask = new CachedEntityUpdateTask(cachedEntity);
                    _updateTasks.Add(updateTask);
                }
            }

            _ = Task.Run(async () => await RunUpdates());

            return updateTask.CompletionSource.Task;
        }

        private async Task RunUpdates()
        {
            bool didUpdate = false;

            await _updateLock.WaitAsync();
            try
            {
                CachedEntityUpdateTask entityToUpdate;
                lock (_updateTasksLock)
                {
                    entityToUpdate = _updateTasks.FirstOrDefault();
                }

                if (entityToUpdate != null)
                {
                    didUpdate = true;

                    try
                    {
                        PrepareCacheResult result = await entityToUpdate.CachedEntity.Update().ConfigureAwait(false);
                        entityToUpdate.CompletionSource.SetResult(result);
                    }
                    catch (OperationCanceledException)
                    {
                        entityToUpdate.CompletionSource.SetCanceled();
                    }
                    catch (Exception ex)
                    {
                        entityToUpdate.CompletionSource.SetException(ex);
                    }

                    lock (_updateTasksLock)
                    {
                        _updateTasks.Remove(entityToUpdate);
                    }
                }
            }
            finally
            {
                _updateLock.Release();
            }

            if (didUpdate)
            {
                await RunUpdates();
            }
        }

        private CachedEntity GetCachedEntity(string projectionName, string entityName)
        {
            if (_disposed) throw new OperationCanceledException();
            return _cachedEntities.GetOrAdd(entityName, en => new CachedEntity(_metadata, _db, _logger, _perfLogger, _onlineData, projectionName, en));
        }

        private void CleanupCachedEntities()
        {
            CachedEntity[] cachedEntities = _cachedEntities.Values.ToArray();
            _cachedEntities.Clear();

            foreach (CachedEntity cachedEntity in cachedEntities)
            {
                cachedEntity.Dispose();
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                CleanupCachedEntities();
            }
        }

        private sealed class CachedEntityUpdateTask
        {
            public TaskCompletionSource<PrepareCacheResult> CompletionSource { get; } = new TaskCompletionSource<PrepareCacheResult>();
            public CachedEntity CachedEntity { get; }

            public CachedEntityUpdateTask(CachedEntity cachedEntity)
            {
                CachedEntity = cachedEntity;
            }
        }
    }

    public static class CachePreparerExtensions
    {
        public static async Task<PrepareCacheResult> PrepareCacheAsync(this ICachePreparer cache, IMetadata metadata, string projectionName, string procName, CancellationToken cancelToken = default(CancellationToken))
        {
            CpiProc proc = metadata.FindProcedure(projectionName, procName);
            if (proc != null)
            {
                string[] entityNames = EntityFinder.FindInProcedure(metadata, projectionName, proc);
                foreach (string entityName in entityNames)
                {
                    PrepareCacheResult result = await cache.PrepareCacheAsync(projectionName, entityName, cancelToken);

                    if (result == PrepareCacheResult.Offline)
                    {
                        return PrepareCacheResult.Offline;
                    }
                }
            }

            return PrepareCacheResult.Ready;
        }

        public static async Task<PrepareCacheResult> PrepareCacheAsync(this ICachePreparer cache, EntityDataSource dataSource, CancellationToken cancelToken = default(CancellationToken))
        {
            return await cache.PrepareCacheAsync(dataSource.ProjectionName, dataSource.EntityName, cancelToken);
        }

        internal static async Task<PrepareCacheResult> PrepareCacheAsync(this ICachePreparer cache, PreparedEntityQuery preparedQuery, CancellationToken cancelToken = default(CancellationToken))
        {
            foreach (string entityName in preparedQuery.UsedEntities)
            {
                if (await cache.PrepareCacheAsync(preparedQuery.ProjectionName, entityName, cancelToken) == PrepareCacheResult.Offline)
                {
                    return PrepareCacheResult.Offline;
                }
            }

            return PrepareCacheResult.Ready;
        }

        internal static async Task<bool> IsCacheReadyAsync(this ICachePreparer cache, PreparedEntityQuery preparedQuery)
        {
            foreach (string entityName in preparedQuery.UsedEntities)
            {
                if (!await cache.IsCacheReadyAsync(preparedQuery.ProjectionName, entityName))
                {
                    return false;
                }
            }

            return true;
        }
    }
}
