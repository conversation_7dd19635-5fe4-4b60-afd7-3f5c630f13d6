﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringToLower : StringFunction
    {
        public const string FunctionName = "ToLower";

        public StringToLower()
            : base(FunctionName, 1, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify) => stringToModify.ToLowerInvariant();
    }
}
