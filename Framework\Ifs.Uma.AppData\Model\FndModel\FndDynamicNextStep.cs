﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndDynamicNextStep : RemoteRow
    {
        public const string DbTableName = "fnd_dynamic_next_step";

        [Column]
        public double? NextStep { get; set; }

        [Column]
        public string DefaultValue { get; set; }

        [Column]
        public string DefaultClobValue { get; set; }

        [Column]
        public string TerminateInfo { get; set; }

        [Column]
        public double? LoopOccurrence { get; set; }

        public FndDynamicNextStep()
            : base(DbTableName)
        {
        }
    }
}
