﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Expressions;

namespace Ifs.Uma.AppData
{
    public sealed class AggregateQuery
    {
        public EntityQuery From { get; }
        public AggregateType AggregateType { get; set; }
        public Expression AggregateExpression { get; set; }

        public static AggregateQuery CreateCount(EntityQuery query)
        {
            return new AggregateQuery(query, AggregateType.Count, (Expression)null);
        }

        public AggregateQuery(EntityQuery query, AggregateType aggregateType, string attributeName)
        {
            if (query == null) throw new ArgumentNullException(nameof(query));
            if (attributeName == null) throw new ArgumentNullException(nameof(attributeName));

            From = query.Clone();
            AggregateType = aggregateType;
            AggregateExpression = IfsExpression.VarAccess(attributeName);
        }

        public AggregateQuery(EntityQuery query, AggregateType aggregateType, Expression aggregateExpression)
        {
            if (query == null) throw new ArgumentNullException(nameof(query));

            From = query.Clone();
            AggregateType = aggregateType;
            AggregateExpression = aggregateExpression;
        }

        public AggregateQuery(EntityDataSource dataSource, AggregateType aggregateType, Expression aggregateExpression)
        {
            if (dataSource == null) throw new ArgumentNullException(nameof(dataSource));

            From = new EntityQuery(dataSource);
            AggregateType = aggregateType;
            AggregateExpression = aggregateExpression;
        }
        
        internal PreparedAggregateQuery Prepare()
        {
            return new PreparedAggregateQuery(this);
        }

        public AggregateQuery Clone()
        {
            return new AggregateQuery(From, AggregateType, AggregateExpression);
        }
    }
}
