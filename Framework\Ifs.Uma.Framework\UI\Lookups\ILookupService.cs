﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics.SymbolStore;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Data;
using Ifs.Uma.UI.Lists;
using Ifs.Uma.UI.Summaries;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Lookups
{
    public interface ILookupService
    {
        Task<LookupResult<T>> OpenLookup<T>(LookupRequest<T> request);
    }

    public class LookupRequest
    {
        public string Title { get; set; }
        public string LayoutName { get; set; }
        public CardDef CardDef { get; set; }
        public SummaryDef SummaryDef { get; set; }

        public bool AllowNull { get; set; }
        public bool AllowUnknown { get; set; }

        public string UnknownItem { get; set; }

        public IEnumerable Items { get; set; }
        public IEnumerable<LookupFilter> Filters { get; set; }
        public object SelectedItem { get; set; }
        public Type Type { get; protected set; }

        public Func<object, string> GroupSelector { get; set; }

        public Func<LookupQuery, IDataLoader> DataProvider { get; set; }

        public Predicate<object> SelectedItemPredicate { get; set; }

        public bool QueryOnSearch { get; set; }

        public bool QuerySearchOnly { get; set; }

        public bool QueryOnFilter { get; set; }

        public IEnumerable<ListSortOption> SortOptions { get; set; }
        public QuickItemSettings QuickItemSettings { get; set; }
        public ISettings Settings { get; set; }

        public Func<IEnumerable<string>, CancellationToken, Task<IEnumerable>> ItemRetriever { get; set; }

        public Func<object, string> SelectedItemIdRetriever { get; set; }

        public Func<object, string> ItemIdRetriever { get; set; }

        public bool IsOnDemandSearchEnabled { get; set; }

        public bool IsOnDemandDownloadEnabled { get; set; }

        public Command SearchModeChanged { get; set; }

        public string EntitySetName { get; set; }

        protected LookupRequest()
        {
            SelectedItemPredicate = (x) => x == SelectedItem; 
        }
    }

    public class LookupFilter
    {
        public string Name { get; set; }
        public Predicate<object> Predicate { get; set; }
        public object Tag { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }

    public class LookupFilter<T> : LookupFilter
    {
        public new Predicate<T> Predicate
        {
            get
            {
                return base.Predicate == null ? null : new Predicate<T>(x =>
                {
                    return base.Predicate(x);
                });
            }
            set
            {
                base.Predicate = value == null ? null : new Predicate<object>(x =>
                {
                    return (x is T) ? value((T)x) : false;
                });
            }
        }

        public static LookupFilter<T> Create(string name, Predicate<T> predicate)
        {
            LookupFilter<T> filter = new LookupFilter<T>();
            filter.Name = name;
            filter.Predicate = predicate;
            return filter;
        }
    }

    public class LookupQuery
    {
        public string SearchTerm { get; set; }
        public LookupFilter Filter { get; set; }

        public IList<ListSortOption> SortOptions { get; } = new List<ListSortOption>();
    }

    public class LookupRequest<T> : LookupRequest
    {
        public new IEnumerable<T> Items
        {
            get
            {
                return base.Items == null ? null : base.Items.Cast<T>();
            }
            set
            {
                base.Items = value;
            }
        }
        public new IEnumerable<LookupFilter<T>> Filters
        {
            get
            {
                return base.Filters == null ? null : base.Filters.Cast<LookupFilter<T>>();
            }
            set
            {
                base.Filters = value;
            }
        }

        public new T SelectedItem
        {
            get
            {
                return (T)base.SelectedItem;
            }
            set
            {
                base.SelectedItem = value;
            }
        }

        public new Func<T, string> GroupSelector
        {
            get
            {
                if (base.GroupSelector == null)
                {
                    return null;
                }
                else
                {
                    return (x) =>
                    {
                        return base.GroupSelector(x);
                    };
                }
            }
            set
            {
                base.GroupSelector = (x) =>
                    {
                        if (x is T)
                        {
                            T typedObject = (T)x;
                            return value(typedObject);
                        }
                        return null;
                    };
            }
        }

        public new Func<LookupQuery, IDataLoader<T>> DataProvider
        {
            get
            {
                if (base.DataProvider == null)
                {
                    return null;
                }
                else
                {
                    return (x) =>
                    {
                        return (IDataLoader<T>)base.DataProvider(x);
                    };
                }
            }
            set
            {
                base.DataProvider = (query) => value(query);
            }
        }

        public new Predicate<T> SelectedItemPredicate
        {
            get
            {
                return base.SelectedItemPredicate == null ? null : new Predicate<T>(x =>
                {
                    return base.SelectedItemPredicate(x);
                });
            }
            set
            {
                base.SelectedItemPredicate = value == null ? null : new Predicate<object>(x =>
                {
                    return (x == null || x is T) ? value((T)x) : false;
                });
            }
        }

        public new Func<IEnumerable<string>, CancellationToken, Task<IEnumerable<T>>> ItemRetriever
        {
            get
            {
                if (base.ItemRetriever == null)
                {
                    return null;
                }
                else
                {
                    return async (x, cancelToken) =>
                    {
                        IEnumerable items = await base.ItemRetriever(x, cancelToken);
                        return items.Cast<T>();
                    };
                }
            }
            set
            {
                base.ItemRetriever = async (x, cancelToken) =>
                {
                    Task<IEnumerable<T>> items = value(x, cancelToken);
                    return await items;
                };
            }
        }

        public new Func<T, string> SelectedItemIdRetriever
        {
            get
            {
                if (base.SelectedItemIdRetriever == null)
                {
                    return null;
                }
                else
                {
                    return (x) => base.SelectedItemIdRetriever(x);
                }
            }
            set
            {
                base.SelectedItemIdRetriever = (x) => value((T)x);
            }
        }

        public new Func<T, string> ItemIdRetriever
        {
            get
            {
                if (base.ItemIdRetriever == null)
                {
                    return null;
                }
                else
                {
                    return (x) => base.ItemIdRetriever(x);
                }
            }
            set
            {
                base.ItemIdRetriever = (x) => value((T)x);
            }
        }

        public string SearchBarPlaceholder { get; set; }

        public LookupRequest(string title)
        {
            Title = title;
            Type = typeof(T);
        }

        public LookupRequest(string title, IEnumerable<T> items)
        {
            Title = title;
            Items = items;
            Type = typeof(T);
            DataProvider = q => new DataLoader<T>(items);
        }

        public LookupRequest(string title, IEnumerable<T> items, T selectedItem) 
            : this(title, items)
        {
            SelectedItem = selectedItem;
        }
    }

    public class LookupResult<T>
    {
        public bool Canceled { get; private set; }
        public bool IsUnknown { get; private set; }
        public T Item { get; private set; }
        public string UnknownItem { get; private set; }
        public LookupFilter<T> SelectedFilter { get; private set; }

        private LookupResult(bool canceled, bool isUnknown, T item, string unknownItem, LookupFilter<T> selectedFilter)
        {
            Canceled = canceled;
            IsUnknown = isUnknown;
            Item = item;
            UnknownItem = unknownItem;
            SelectedFilter = selectedFilter;
        }

        public LookupResult<M> Map<M>(Func<T, M> mapFunc)
        {
            if (Canceled)
            {
                return LookupResult<M>.CreateCanceled();
            }

            if (IsUnknown)
            {
                return LookupResult<M>.CreateUnknown(UnknownItem);
            }

            if (Item == null)
            {
                return new LookupResult<M>(false, false, default(M), null, null);
            }

            return new LookupResult<M>(false, false, mapFunc(Item), null, null);
        }

        public static LookupResult<T> CreateCanceled()
        {
            return new LookupResult<T>(true, false, default(T), null, null);
        }

        public static LookupResult<T> CreateUnknown(string unknownItem)
        {
            return new LookupResult<T>(false, true, default(T), unknownItem, null);
        }

        public static LookupResult<T> Create(T item)
        {
            return Create(item, null);
        }

        public static LookupResult<T> Create(T item, LookupFilter<T> selectedFilter)
        {
            return new LookupResult<T>(false, false, item, null, selectedFilter);
        }
    }
}
