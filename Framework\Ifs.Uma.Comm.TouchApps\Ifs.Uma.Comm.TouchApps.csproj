﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>keyfile.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <CodeAnalysisRuleSet>..\FrameworkRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DocumentationFile>bin\Release\netstandard1.1\Ifs.Uma.Comm.TouchApps.XML</DocumentationFile>
    <CodeAnalysisRuleSet>..\FrameworkRules.Release.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\UmaAssemblyInfo.cs" Link="Properties\UmaAssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.0.2" />
    <PackageReference Include="Prism.Core" Version="7.0.0.396" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Ifs.Cloud.Client.Exceptions\Ifs.Cloud.Client.Exceptions.csproj" />
    <ProjectReference Include="..\Ifs.Cloud.Client\Ifs.Cloud.Client.csproj" />
    <ProjectReference Include="..\Ifs.Uma.AppData\Ifs.Uma.AppData.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Database\Ifs.Uma.Database.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Data\Ifs.Uma.Data.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Metadata\Ifs.Uma.Metadata.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Services\Ifs.Uma.Services.csproj" />
    <ProjectReference Include="..\Ifs.Uma.Utility\Ifs.Uma.Utility.csproj" />
  </ItemGroup>
</Project>