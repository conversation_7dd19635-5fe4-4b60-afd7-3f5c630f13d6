﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Services;
using Prism.Events;

namespace Ifs.Uma.Framework.UI.Attachments
{
    public class DocumentListPage : PageBase
    {
        public const string PageName = "DocumentListPage";
        public const string NoRevPageName = "DocumentListNoRevisionPage";

        public DocumentsListData ListData { get; }

        public DocumentListPage(IEventAggregator eventAggregator, IDialogService dialogService, IDocument<PERSON><PERSON><PERSON> docHandler, INavigator navigator)
            : base(eventAggregator, dialogService)
        {
            Name = PageName;
            Classification = PageClassification.DocumentList;
            ListData = new DocumentsListData(eventAggregator, docHandler, navigator);
        }

        public override async Task<bool> LoadPageAsync(NavigationParameter parameter)
        {
            AttachmentNavParam attachmentNavParam = parameter as AttachmentNavParam;
            await ListData.Load(attachmentNavParam);
            Title = ListData.Title;
            return true;
        }
    }
}
