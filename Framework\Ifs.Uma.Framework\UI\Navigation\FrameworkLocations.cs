﻿using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Navigation
{
    public static class FrameworkLocations
    {
        public static readonly NavigationLocation SyncMonitor = new FrameworkLocation();
        public static readonly NavigationLocation UpdateMetadata = new FrameworkLocation();
        public static readonly NavigationLocation Login = new FrameworkLocation();
        public static readonly NavigationLocation Logout = new FrameworkLocation();
        public static readonly NavigationLocation AppHome = new FrameworkLocation();
        public static readonly NavigationLocation Home = new FrameworkLocation();
        public static readonly NavigationLocation Settings = new FrameworkLocation();
        public static readonly NavigationLocation About = new FrameworkLocation();
        public static readonly NavigationLocation BugReporter = new FrameworkLocation();
        public static readonly NavigationLocation LogCleaner = new FrameworkLocation();
        public static readonly NavigationLocation DatabaseViewer = new FrameworkLocation();
        public static readonly NavigationLocation DatabaseViewerRow = new FrameworkLocation();
        public static readonly NavigationLocation DeveloperTools = new FrameworkLocation();
        public static readonly NavigationLocation MetadataPage = new FrameworkLocation();
        public static readonly NavigationLocation DocumentDetails = new FrameworkLocation();
        public static readonly NavigationLocation MediaDetails = new FrameworkLocation();
        public static readonly NavigationLocation Attachments = new FrameworkLocation();
        public static readonly NavigationLocation Maps = new FrameworkLocation();
        public static readonly NavigationLocation External = new FrameworkLocation();
        public static readonly NavigationLocation SwitchUser = new FrameworkLocation();

#if REMOTE_ASSISTANCE
        public static readonly NavigationLocation RemoteAssistance = new FrameworkLocation();
#endif

        private class FrameworkLocation : NavigationLocation
        {
        }
    }
}
