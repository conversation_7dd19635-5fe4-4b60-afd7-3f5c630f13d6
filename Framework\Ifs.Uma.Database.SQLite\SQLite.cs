﻿using Ifs.Uma.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Uma.Database.SQLite
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces",
        Justification="Hardly used and shipped")]
    public static class SQLite
    {
        /// <summary>
        /// This does nothing but should be called from iOS to ensure the compiler doesn't remove this library
        /// </summary>
        public static void Initialize()
        {
        }
    }
}
