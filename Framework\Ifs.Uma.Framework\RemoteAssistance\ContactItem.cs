﻿#if REMOTE_ASSISTANCE
using System;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    public class ContactItem
    {
        public string Id { get; }
        public string FullName { get; }
        public int ActiveUserCount { get; set; }
        public RemoteAssistContactType? ContactTypeDb { get; set; } = RemoteAssistContactType.User;
        public int Ordinal { get; }
        public RemoteAssistAvailability? AvailabilityDb { get; } = RemoteAssistAvailability.Available;

        public ContactItem(string id, string fullName, string activeUserCount = null, string availability = null, string ordinal = null, string contactType = null)
        {
            Id = id;
            FullName = fullName;

            if (Enum.TryParse(availability, out RemoteAssistAvailability availabilityResult))
            {
                AvailabilityDb = availabilityResult;
            }

            if (int.TryParse(activeUserCount, out int activeUserCountResult))
            {
                ActiveUserCount = activeUserCountResult;
            }

            if (int.TryParse(ordinal, out int ordinalResult))
            {
                Ordinal = ordinalResult;
            }
            
            if (Enum.TryParse(contactType, out RemoteAssistContactType contactTypeResult))
            {
                ContactTypeDb = contactTypeResult;
            }
        }        
    }

    public enum RemoteAssistContactType
    {
        User,
        Group
    }

    public enum RemoteAssistAvailability
    {
        DoNotDisturb,
        DirectCallsOnly,
        Available
    }
}
#endif
