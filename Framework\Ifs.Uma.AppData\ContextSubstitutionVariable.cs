﻿using System;
using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData
{
    public class ContextSubstitutionVariable
    {
        private const string InnerOffsetPattern = @"#\w+([+-][1-9])#";
        private const string OuterOffsetPattern = @"#\w+#([+-][1-9])";
        private const string InnerCsvPattern = @"(#\w+)(\+|-)\d+#";
        private const string OuterCsvPattern = @"(#\w+#)(\+|-)\d+";

        public bool GetCsV(string param, out object value)
        {
            if (ContainsOffset(param))
            {
                return ParseDateOffset(param, out value);
            }

            switch (param)
            {
                case "#TODAY#":
                    value = DateTime.Today;
                    break;
                case "#TOMORROW#":
                    value = DateTime.Today.AddDays(1);
                    break;
                case "#YESTERDAY#":
                    value = DateTime.Today.AddDays(-1);
                    break;
                case "#START_OF_THIS_WEEK#":
                    value = StartOfWeek(DateTime.Today, DayOfWeek.Sunday); // Assuming week starts on Sunday
                    break;
                case "#START_OF_THIS_MONTH#":
                    value = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                    break;
                case "#START_OF_THIS_YEAR#":
                    value = new DateTime(DateTime.Today.Year, 1, 1);
                    break;
                case "#END_OF_THIS_WEEK#":
                    value = EndOfWeek(DateTime.Today, DayOfWeek.Saturday); // Assuming week ends on Saturday
                    break;
                case "#END_OF_THIS_MONTH#":
                    value = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.DaysInMonth(DateTime.Today.Year, DateTime.Today.Month));
                    break;
                case "#END_OF_THIS_YEAR#":
                    value = new DateTime(DateTime.Today.Year, 12, 31);
                    break;
                case "#START_OF_LAST_WEEK#":
                    value = StartOfWeek(DateTime.Today.AddDays(-7), DayOfWeek.Monday);
                    break;
                case "#START_OF_LAST_MONTH#":
                    value = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(-1);
                    break;
                case "#START_OF_LAST_YEAR#":
                    value = new DateTime(DateTime.Today.Year - 1, 1, 1);
                    break;
                case "#END_OF_LAST_WEEK#":
                    value = EndOfWeek(DateTime.Today.AddDays(-7), DayOfWeek.Sunday);
                    break;
                case "#END_OF_LAST_MONTH#":
                    var lastMonth = DateTime.Today.AddMonths(-1);
                    value = new DateTime(lastMonth.Year, lastMonth.Month, DateTime.DaysInMonth(lastMonth.Year, lastMonth.Month));
                    break;
                case "#END_OF_LAST_YEAR#":
                    value = new DateTime(DateTime.Today.Year - 1, 12, 31);
                    break;
                case "#START_OF_NEXT_WEEK#":
                    value = StartOfWeek(DateTime.Today.AddDays(7), DayOfWeek.Monday);
                    break;
                case "#START_OF_NEXT_MONTH#":
                    value = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(1);
                    break;
                case "#START_OF_NEXT_YEAR#":
                    value = new DateTime(DateTime.Today.Year + 1, 1, 1);
                    break;
                case "#END_OF_NEXT_WEEK#":
                    value = EndOfWeek(DateTime.Today.AddDays(7), DayOfWeek.Sunday);
                    break;
                case "#END_OF_NEXT_MONTH#":
                    var nextMonth = DateTime.Today.AddMonths(1);
                    value = new DateTime(nextMonth.Year, nextMonth.Month, DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month));
                    break;
                case "#END_OF_NEXT_YEAR#":
                    value = new DateTime(DateTime.Today.Year + 1, 12, 31);
                    break;
                default:
                    value = null; 
                    return false;
            }

            return true;
        }

        private DateTime StartOfWeek(DateTime dt, DayOfWeek startOfWeek)
        {
            int diff = (7 + (dt.DayOfWeek - startOfWeek)) % 7;
            return dt.AddDays(-1 * diff).Date;
        }

        private DateTime EndOfWeek(DateTime dt, DayOfWeek endOfWeek)
        {
            int diff = (7 - (int)dt.DayOfWeek + (int)endOfWeek) % 7;
            return dt.AddDays(diff).Date;
        }

        private static bool ContainsOffset(string param)
        {
            return IsInnerOffset(param) || IsOuterOffset(param);
        }

        private static bool IsInnerOffset(string param)
        {
            return Regex.IsMatch(param, InnerOffsetPattern);
        }

        private static bool IsOuterOffset(string param)
        {
            return Regex.IsMatch(param, OuterOffsetPattern);
        }

        private static string ExtractCsvValue(string param)
        {
            string pattern = IsInnerOffset(param) ? InnerCsvPattern : OuterCsvPattern;

            Match match = Regex.Match(param, pattern);
            if (match.Success)
            {
                return match.Groups[1].Value;
            }

            return string.Empty;
        }

        private bool ParseDateOffset(string param, out object value)
        {
            value = null;

            if (string.IsNullOrEmpty(param))
            {
                return false;
            }

            string csvValue = ExtractCsvValue(param);         

            string offsetString = param.Replace(csvValue, " ").Replace("#", " ").Trim();

            if (IsInnerOffset(param))
            {
                csvValue = $"{csvValue}#";
            }

            if (GetCsV(csvValue, out object baseValue) && baseValue is DateTime dateTime)
            {
                if (int.TryParse(offsetString, out int offset))
                {  
                    value = dateTime.AddDays(offset);
                    return true;
                }
                else
                {
                    throw new ArgumentException("Invalid offset value");
                }
            }

            return false;
        }
    }
}
