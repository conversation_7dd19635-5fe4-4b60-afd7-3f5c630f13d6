﻿using System;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemTimeZone : SystemFunction
    {
        public const string FunctionName = "TimeZone";

        public SystemTimeZone()
            : base(FunctionName, 0)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return PlatformServices.TimezoneService.Local;
        }
    }
}
