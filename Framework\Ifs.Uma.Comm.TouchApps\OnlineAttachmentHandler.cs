﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Exceptions;
using Ifs.Cloud.Client.Utils;
using Ifs.Uma.AppData.Attachments;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Comm.TouchApps
{
    public sealed class OnlineAttachmentHandler : OnlineAttachmentHandlerBase
    {
        private readonly ILogger _logger;
        private readonly IPerfLogger _perfLogger;
        private readonly IClientKeysMapper _clientKeysMapper;
        private readonly TouchAppsComms _comms;
        private readonly IMetadata _metadata;

        public OnlineAttachmentHandler(IDatabaseController db,
           ILogger logger, IPerfLogger perfLogger, IEventAggregator eventAggregator, 
           IIfsConnection connection, IClientKeysMapper clientKeysMapper, IMetadata metadata)
           : base(db, logger, perfLogger, eventAggregator, metadata)
        {
            _clientKeysMapper = clientKeysMapper;
            _comms = connection.TouchAppsComms;
            _logger = logger;
            _perfLogger = perfLogger;
            _metadata = metadata;
        }

        protected override async Task<OnlineResponse<AttachmentInfo>> DownloadAttachments(string entityName, string clientKeyRef, string serverKeyRef, bool includeDocuments, bool includeMedia, CancellationToken cancellationToken)
        {
            if (!_comms.IsAvailable())
            {
                return OnlineResponse<AttachmentInfo>.Offline;
            }

            string name = "DownloadAttachments";
            using (_perfLogger.Track(nameof(OnlineDataHandler), name))
            {
                AttachmentResource resource = new AttachmentResource();
                resource.AppName = _comms.ClientInfo.AppName;
                resource.DeviceId = _comms.DeviceId;
                resource.LuName = entityName;
                resource.KeyRef = serverKeyRef ?? clientKeyRef;
                resource.IncludeMedia = includeMedia;
                resource.IncludeDocuments = includeDocuments;

                try
                {
                    AttachmentResource response = (AttachmentResource)await _comms.PostCustomResourceAsync(resource, 0, 0, cancellationToken);
                    AttachmentInfo attachmentInfo = ConvertReponseToAttachmentInfo(includeDocuments, includeMedia, response);
                    return new OnlineResponse<AttachmentInfo>(attachmentInfo);
                }
                catch (CloudException ex) when (ex.InnerException is OperationCanceledException)
                {
                    return new OnlineResponse<AttachmentInfo>(ex.InnerException);
                }
                catch (Exception ex)
                {
                    return new OnlineResponse<AttachmentInfo>(ex);
                }
            }
        }

        private AttachmentInfo ConvertReponseToAttachmentInfo(bool includeDocuments, bool includeMedia, AttachmentResource response)
        {
            AttachmentInfo attachmentInfo = new AttachmentInfo();

                attachmentInfo.Documents = !includeDocuments || response?.Documents == null ? new DocRevisionInfo[0] : ConvertDocuments(response.Documents);
                attachmentInfo.Media = !includeMedia || response?.Media == null ? new MediaInfo[0] : ConvertMedia(response.Media);
            return attachmentInfo;
        }

        private DocRevisionInfo[] ConvertDocuments(string downloadedDocsSerialized)
        {
            AttachmentResource.DocumentData[] downloadedDocs;
            using (MemoryStream ms = new MemoryStream(Encoding.UTF8.GetBytes(downloadedDocsSerialized)))
            {
                DataContractJsonSerializer serializer =
                                new DataContractJsonSerializer(typeof(AttachmentResource.DocumentData[]));
                downloadedDocs = (AttachmentResource.DocumentData[])serializer.ReadObject(ms);
            }
                List<DocRevisionInfo> docs = new List<DocRevisionInfo>();
            
            foreach (AttachmentResource.DocumentData downloadDoc in downloadedDocs)
            {
                try
                {
                    docs.Add(ConvertDocument(downloadDoc));
                }
                catch (Exception ex)
                {
                    _logger.HandleException(ExceptionType.Recoverable, ex);
                }
            }
            return docs.ToArray();
        }

        private DocRevisionInfo ConvertDocument(AttachmentResource.DocumentData data)
        {
            DocRevisionInfo docRev = new DocRevisionInfo();

            DocReferenceObject docRef = new DocReferenceObject();
            docRef.DocClass = data.DocClass;
            docRef.DocNo = data.DocNo;
            docRef.DocSheet = data.DocSheet;
            docRef.DocRev = data.DocRev;
            docRef.KeyRef = data.KeyRef;
            docRef.LuName = data.LuName;
            docRef.Title = data.DocTitle;
            docRef.DocumentAccess = data.UserAdminAccess == "TRUE" ? DocumentAccess.Admin : (data.UserViewAccess == "TRUE" ? DocumentAccess.View : DocumentAccess.Edit);
            docRef.DocIssueObjstate = data.DocIssueObjstate;
            docRev.DocumentRevision = docRef;

            _clientKeysMapper.MapServerToClientKeys(docRef);

            if (!string.IsNullOrEmpty(data.DocType))
            {
                EdmFile file = new EdmFile();
                file.DocClass = data.DocClass;
                file.DocNo = data.DocNo;
                file.DocSheet = data.DocSheet;
                file.DocRev = data.DocRev;
                file.DocType = data.DocType;
                file.FileName = data.FileName ?? "Document." + data.FileExt;
                file.FileNo = data.FileNo;
                file.FileType = data.FileType;
                docRev.EdmFile = file;

                _clientKeysMapper.MapServerToClientKeys(file);
            }
            
            return docRev;
        }

        private MediaInfo[] ConvertMedia(string downloadedMediaSerialized)
        {
            AttachmentResource.MediaData[] downloadedMedia;

            List<MediaInfo> media = new List<MediaInfo>();
            downloadedMedia = ContentSerializationHelper.DeserializeJsonBinary<AttachmentResource.MediaData[]>(Encoding.UTF8.GetBytes(downloadedMediaSerialized));
            foreach (AttachmentResource.MediaData downloadedMediaItem in downloadedMedia)
            {
                try
                {
                    media.Add(ConvertMediaItem(downloadedMediaItem));
                }
                catch (Exception ex)
                {
                    _logger.HandleException(ExceptionType.Recoverable, ex);
                }
            }
            return media.ToArray();
        }

        private MediaInfo ConvertMediaItem(AttachmentResource.MediaData data)
        {
            MediaLibrary lib = new MediaLibrary();
            lib.LuName = data.LuName;
            lib.KeyRef = data.KeyRef;
            lib.LibraryId = data.LibraryId;
            lib.MainLibrary = data.MainLibrary == true;

            MediaLibraryItem item = new MediaLibraryItem();
            item.ItemId = data.ItemId;
            item.LibraryId = data.LibraryId;
            item.LibraryItemId = ObjectConverter.ToLong(data.LibraryItemId);
            item.Name = data.Name;
            item.Description = data.Description;
            item.Latitude = data.Latitude;
            item.Longitude = data.Longitude;
            item.MediaFile = data.MediaFile;
            item.PrivateMediaItem = data.PrivateMediaItem == "TRUE";
         //   item.DefaultMedia = data.DefaultMedia == "TRUE";
            item.MediaItemType = (MediaType)Enum.Parse(typeof(MediaType), data.MediaItemType, true);

            _clientKeysMapper.MapServerToClientKeys(lib);
            _clientKeysMapper.MapServerToClientKeys(item);

            MediaInfo mediaInfo = new MediaInfo();
            mediaInfo.Library = lib;
            mediaInfo.MediaItem = item;
            return mediaInfo;
        }
    }
}
