<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginLeft="@dimen/activity_horizontal_margin"
    android:layout_marginRight="@dimen/activity_horizontal_margin">                
        <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_alignParentTop="true"
        android:layout_above="@+id/bottom_button_container"
        android:layout_marginBottom="@dimen/activity_horizontal_margin"
        android:layout_marginTop="10dip"
        android:layout_weight="1">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">         
             <ImageView
                android:id="@+id/sync_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="fitCenter"
                android:adjustViewBounds="true"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/activity_horizontal_margin"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:srcCompat="@drawable/sync_indicator_icon" />
            <TextView
                android:id="@+id/sync_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_horizontal"
                android:textColor="@color/IfsGrayDark"
                android:textSize="18sp"
                android:layout_weight = "1"
                tools:text="Synchronizing" />
            <TextView
                android:id="@+id/sync_details"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:textColor="@color/IfsGrayMedium"
                android:layout_weight = "1"
                android:textSize="15sp"
                tools:text="Some strange activity" />
            <TextView
                android:id="@+id/sync_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight = "1"
                android:gravity="center_horizontal"
                android:textColor="@color/IfsRed"
                android:textSize="15sp"
                tools:text="Something went wrong trying to communicate with IFS Cloud - &quot;Error: ConnectFailure (Connection timed out)&quot;. Please contact administrator" />            
              <TextView
                    android:id="@+id/sync_warnings"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="vertical"
                    android:gravity="center_horizontal"
                    android:layout_weight="1"
                    tools:text="Initialization process is taking longer than usual. Consider optimizing your projection to improve performance." />
        </LinearLayout> 
        </ScrollView>
        <LinearLayout
                android:id="@+id/bottom_button_container"
                style="?android:attr/buttonBarStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:orientation="vertical"
                android:gravity="center_horizontal">              
                <Button
                    android:id="@+id/button_refreshLogin"
                    style="?android:attr/buttonBarButtonStyle"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    tools:text="Log in again"/>
                <Button
                    android:id="@+id/button_initialize"
                    style="?android:attr/buttonBarButtonStyle"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    tools:text="Initialize" />
                <Button
                    android:id="@+id/button_refresh_cache"
                    style="?android:attr/buttonBarButtonStyle"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    tools:text="Refresh Cache" />
            </LinearLayout>
</RelativeLayout>
