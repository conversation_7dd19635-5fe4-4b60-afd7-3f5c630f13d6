﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.MSTeams;
using Ifs.Uma.Framework.UI.Elements;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Helpers;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Cards
{
    public sealed class CardDefItem : ObservableBase
    {
        public const string HeaderLabelId = "_Label";
        public const string ParentAttribute = "parent";

        public enum ControlTypes
        {
            Field,
            Markdown,
            Badge,
            Image,
            HeaderLabel,
            Address,
            LoV
        }

        private ControlTypes? _controlType;
        private IBindValueConverter _converter;
        private object _converterParameter;
        private CpiField _field;
        private string _id;
        private CpiMarkdownText _markdownText;

        private readonly IMetadata _metadata;
        private readonly IExpressionRunner _expressionRunner;
        private readonly IMediaHandler _mediaHandler;
        private readonly IDataHandler _dataHandler;
        private readonly ILogger _logger;
        private readonly IAppParameters _appParameters;

        private AddressPresentation[] _addressPresentations;
        public AddressPresentation[] AddressPresentations
        {
            get => _addressPresentations;
            set
            {
                if (_addressPresentations != value)
                {
                    _addressPresentations = value;
                }
            }
        }

        public CardDefItem(IMetadata metadata, IExpressionRunner expressionRunner, IMediaHandler mediaHandler, IDataHandler dataHandler, ILogger logger, IAppParameters appParameters)
        {
            _metadata = metadata;
            _expressionRunner = expressionRunner;
            _mediaHandler = mediaHandler;
            _dataHandler = dataHandler;
            _logger = logger;
            _appParameters = appParameters;
        }

        public bool TimeStampIsInUtc { get; set; }

        public Func<object, object> PrimaryTimeZoneProvider { get; set; }

        public List<Func<object, object>> AdditionalTimeZoneProviders { get; } = new List<Func<object, object>>();

        public ControlTypes ControlType
        {
            get
            {
                if (_controlType.HasValue)
                {
                    return _controlType.Value;
                }

                if (_markdownText != null)
                {
                    _controlType = ControlTypes.Markdown;
                }
                else if (ImageViewer != null)
                {
                    _controlType = ControlTypes.Image;
                }
                else if (Field?.Control == CpiControlType.Badge)
                {
                    _controlType = ControlTypes.Badge;
                }
                else if (Field?.Control == CpiControlType.Image)
                {
                    _controlType = ControlTypes.Image;
                }
                else if (Field?.Control == CpiControlType.AddressField)
                {
                    _controlType = ControlTypes.Address;
                }
                else if (Field?.Control == CpiControlType.Lookup)
                {
                    _controlType = ControlTypes.LoV;
                }
                else if (Id == HeaderLabelId)
                {
                    _controlType = ControlTypes.HeaderLabel;
                }
                else
                {
                    _controlType = ControlTypes.Field;
                }

                return _controlType.Value;
            }
        }

        public string Id
        {
            get { return _id; }
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        public string Label
        {
            get
            {
                string name = IsLabelDynamic() ? _dynamicLabel : Name;
                return name == null ? null : string.Format(CultureInfo.CurrentCulture, Strings.Label, name);
            }
        }

        public bool ShowLabel { get; set; } = true;

        public string ProjectionName { get; set; }

        public string Name
        {
            get
            {
                if ((ControlType == ControlTypes.Badge || ControlType == ControlTypes.Field || ControlType == ControlTypes.Address) && _field != null)
                {
                    return string.IsNullOrWhiteSpace(_field.Label) ? null : _field.Label;
                }

                return null;
            }
        }

        public bool IsVisible { get; private set; }

        public bool IsSiteItem()
        {
            TimezoneRefType tzRefType = _metadata.GetEntityTimeZoneType(ProjectionName, Field.Entity);
            return tzRefType == TimezoneRefType.Site;
        }

        public void UpdateIsVisible(IExpressionValueProvider dataContext)
        {
            if (ControlType == ControlTypes.HeaderLabel)
            {
                IsVisible = true;
            }
            else if (ControlType == ControlTypes.Markdown)
            {
                IsVisible = _expressionRunner.RunCheck(_markdownText.OfflineVisible ?? _markdownText.Visible, dataContext, true);
            }
            else if (Visibility != null)
            {
                IsVisible = _expressionRunner.RunCheck(Visibility, dataContext, true);
            }
            else
            {
                IsVisible = Field?.Visible != null ? _expressionRunner.RunCheck(Field?.OfflineVisible ?? Field?.Visible, dataContext, true) : _expressionRunner.RunCheck(Field?.OfflineVisible ?? Field?.ColumnVisible, dataContext, true);
            }
        }

        private string _dynamicLabel;

        public bool IsLabelDynamic()
        {
            if (Field != null)
            {
                return Field.Label != null && Field.Label.Contains("${");
            }

            return false;
        }

        public bool IsEditable(IExpressionValueProvider dataContext)
        {
            if (Field?.Control == CpiControlType.ComputedField)
            {
                return false;
            }

            return _expressionRunner.RunCheck(Field?.OfflineEditable ?? Field?.Editable, dataContext, false);
        }

        public CpiField Field
        {
            get { return _field; }
            set
            {
                if (_field != value)
                {
                    _field = value;
                    _controlType = null;
                    OnPropertyChanged(nameof(Field));
                }
            }
        }

        public CpiMarkdownText MarkdownText
        {
            set
            {
                if (_markdownText != value)
                {
                    _markdownText = value;
                }
            }
        }

        public CpiImageViewer ImageViewer { get; set; }

        public CpiExpression Visibility { get; set; }

        public MarkdownData GetMarkdownData(ViewData cardData)
        {
            if (ControlType != ControlTypes.Markdown)
            {
                return null;
            }

            MarkdownData markdown = new MarkdownData();
            UpdateMarkdownData(cardData, markdown);
            return markdown;
        }

        public void UpdateMarkdownData(ViewData cardData, MarkdownData markdown)
        {
            if (cardData == null || ControlType != ControlTypes.Markdown)
            {
                markdown.Text = null;
                markdown.Color = null;
            }
            else
            {
                bool timezoneAware = _metadata.IsEntityKnownTimeZone(RemoteNaming.ToEntityName(cardData.Record.EntityName), ProjectionName);
                IMetaTable metaTable = _metadata.MetaModel.GetTable(cardData.Record.GetRemoteRow().TableName);

                markdown.Text = timezoneAware ? _expressionRunner.InterpolateString(_markdownText.Text, cardData.Record, true, false, metaTable) : _expressionRunner.InterpolateString(_markdownText.Text, cardData.Record);
                string emphasis = _expressionRunner.GetEmphasis(_markdownText.OfflineEmphasis ?? _markdownText.Emphasis, cardData);
                markdown.Color = UmaColor.FromEmphasis(emphasis);
            }
        }

        public CpiBadge CreateBadge()
        {
            if (ControlType != ControlTypes.Badge)
            {
                return null;
            }

            return new CpiBadge(_metadata, _expressionRunner, ProjectionName, Field);
        }

        public CpiAddress CreateAddress()
        {
            if (ControlType != ControlTypes.Address)
            {
                return null;
            }

            return new CpiAddress(Field.AddressField);
        }

        public CpiAddress GetAddress(ViewData cardData)
        {
            CpiAddress address = CreateAddress();

            if (address != null)
            {
                address.Update(cardData, AddressPresentations);
            }

            return address;
        }

        public Field CreateDateTimeUtcField(CpiField cpiField)
        {
            return FieldMetadataExtensions.CreateFieldType(_metadata, ProjectionName, cpiField, 0);
        }

        public CpiBadge GetBadge(ViewData cardData)
        {
            CpiBadge badge = CreateBadge();

            if (badge != null)
            {
                UpdateBadge(cardData, badge);
            }

            return badge;
        }

        public void UpdateBadge(ViewData cardData, CpiBadge badge)
        {
            badge.Text = cardData == null ? null : GetDisplayValue(cardData);
            badge.UpdateStates(cardData);
        }

        public async Task<Stream> GetImage(ViewData cardData, CancellationToken cancelToken)
        {
            if (ControlType != ControlTypes.Image || cardData?.Record == null)
            {
                return null;
            }

            if (Field?.Attribute != null)
            {
                return await cardData.Record.GetBinaryDataAsync(Field.Attribute, cancelToken);
            }

            if (ImageViewer != null)
            {
                bool showMedia = _expressionRunner.RunCheck(Visibility, cardData, true) &&
                                 _expressionRunner.RunCheck(ImageViewer.Media?.OfflineEnabled ?? ImageViewer.Media?.Enabled, cardData, false);
                if (showMedia)
                {
                    // Get default media item;
                    ObjPrimaryKey key = cardData.Record.ToPrimaryKey();
                    if (key != null)
                    {
                        ILocalFileInfo file = await _mediaHandler.GetMediaFileForDisplayAsync(cardData.Record.EntityName, key.ToKeyRef(), cancelToken);
                        if (file != null && await file.ExistsAsync())
                        {
                            return await file.OpenStreamAsync(LocalFileMode.OpenOrCreate, LocalFileAccess.Read);
                        }
                    }
                }
            }

            return null;
        }

        public FwContactWidget GetContactWidget(ViewData cardData)
        {
            if (_field?.ContactWidget != null && cardData != null)
            {
                FwContactWidget fwcw = new FwContactWidget(_field.ContactWidget, _logger, _metadata, _dataHandler, _expressionRunner, cardData.PageData?.BackgroundTasks)
                {
                    IsEnabled = !string.IsNullOrEmpty(GetDisplayValue(cardData)) && _expressionRunner.RunCheck(_field.ContactWidget.OfflineEnabled ?? _field.ContactWidget.Enabled, cardData, true)
                };

                fwcw.IsMsTeamsEnabled = _appParameters.IsMsTeamsEnabled();
                MSAccessTokenProvider.ClientId = _appParameters.GetMsTeamsClientId();
                string columnName = fwcw.CpiContactWidget.Key;
                string input = GetDisplayValue(cardData);

                if (!string.IsNullOrEmpty(columnName) && cardData.TryGetValue(columnName, out object result))
                {
                    input = result?.ToString();
                }

                fwcw.Id = input;
                return fwcw;
            }

            return null;
        }

        public IBindValueConverter Converter
        {
            get { return _converter; }
            set
            {
                if (_converter != value)
                {
                    _converter = value;
                    OnPropertyChanged(nameof(Converter));
                }
            }
        }

        public object ConverterParameter
        {
            get { return _converterParameter; }
            set
            {
                if (_converterParameter != value)
                {
                    _converterParameter = value;
                    OnPropertyChanged(nameof(ConverterParameter));
                }
            }
        }

        public Func<object, object> ValueProvider { get; set; }

        private object GetValue(object dataContext)
        {
            if (ValueProvider == null || dataContext == null)
            {
                return null;
            }

            object value = ValueProvider(dataContext);

            if (Converter != null)
            {
                value = Converter.Convert(value, typeof(object), ConverterParameter, null);
            }

            return value;
        }

        private object GetValueForKnownTimeZoneField(object dataContext)
        {
            if (ValueProvider == null || dataContext == null)
            {
                return null;
            }

            object value = ValueProvider(dataContext);
            if (value != null)
            {
                DateTime dateTime;
                if (DateTime.TryParse(value.ToString(), out dateTime))
                {
                    string timeZone = GetPrimaryTimeZoneForField(dataContext);
                    dateTime = PlatformServices.TimezoneService.ToTimezone(dateTime, timeZone, PlatformServices.TimezoneService.Utc);
                    return Converter.Convert(dateTime, typeof(object), ConverterParameter, null);
                }
                else
                {
                    return value.ToString();
                }
            }

            return null;
        }

        private object GetClientValueForCustomEnum(object dataContext)
        {
            if (ValueProvider == null || dataContext == null)
            {
                return null;
            }

            object value = ValueProvider(dataContext);

            if (value != null)
            {
                CpiEnumeration enumeration = _metadata.FindEnumeration(ProjectionName, Field.Enumeration);

                return EnumClientValueConverter.ServerToClientValue(value, enumeration);
            }

            return null;
        }

        public string GetPrimaryTimeZoneForField(object dataContext)
        {
            if (dataContext == null || PrimaryTimeZoneProvider == null)
            {
                return PlatformServices.TimezoneService.Local;
            }

            string timeZone = PrimaryTimeZoneProvider(dataContext)?.ToString() ?? PlatformServices.TimezoneService.Local;

            return timeZone;
        }

        public string GetPrimaryTimeZoneAbbreviation(object dataContext)
        {   
            string timeZone = GetPrimaryTimeZoneForField(dataContext);
            ITimezoneService timezoneService = PlatformServices.TimezoneService;
            TimezoneDetails otherDetails = timezoneService?.GetTimezoneDetails(DateTime.UtcNow, timeZone ?? PlatformServices.TimezoneService.Local);

            return otherDetails.Abbreviation;
        }

        private List<string> GetAdditionalTimeZonesAsStrings(object dataContext)
        {
            List<string> timeZoneStrings = new List<string>();

            string primaryZone = GetPrimaryTimeZoneForField(dataContext);
            timeZoneStrings.Add(primaryZone);

            foreach (Func<object, object> additionalTimeZoneProvider in AdditionalTimeZoneProviders)
            {
                string value = (string)additionalTimeZoneProvider(dataContext);
                if (value != null)
                {
                    timeZoneStrings.Add(value);
                }      
            }

            return timeZoneStrings;
        }

        public List<TimeZoneUtcInfo> GetTimeZoneUtcInformationForDialog(object dataContext)
        {
            string dateString = GetDisplayValue(dataContext) ?? DateTime.UtcNow.ToString();

            if (DateTime.TryParseExact(dateString, DateTimeFieldUtc.DateFormats, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDateTime))
            {
                List<string> timeZonesToDisplay = GetAdditionalTimeZonesAsStrings(dataContext);
                KnownDateTimeField knownDateTimeField = new KnownDateTimeField()
                {
                    DisplayTimeZonesValues = timeZonesToDisplay,
                };

                return knownDateTimeField.GetOtherTimeZoneInformation(parsedDateTime);
            }

            return new List<TimeZoneUtcInfo>();
        }

        public string GetDisplayValue(object dataContext)
        {
            if (_field?.Control == CpiControlType.ComputedField)
            {
                string value = null;

                if (dataContext is ViewData viewData)
                {
                    value = _expressionRunner.InterpolateString(_field.Expression, viewData.Record);
                }

                return value;
            }
            else if (_field.Datatype == CpiDataType.Timestamp && TimeStampIsInUtc)
            {
                object value = GetValueForKnownTimeZoneField(dataContext);
                return value != null ? value.ToString() : string.Empty;
            }
            else if (_field.Datatype == CpiDataType.Enumeration && _field.Enumeration.StartsWith("Cf"))
            {
                object value = GetClientValueForCustomEnum(dataContext);
                return value != null ? value.ToString() : string.Empty;
            }
            else
            {
                object value = GetValue(dataContext);
                return value != null ? value.ToString() : string.Empty;
            }
        }

        public string GetLabelValue(object dataContext)
        {
            if (_field?.Control == CpiControlType.Field || _field?.Control == CpiControlType.Static)
            {
                string value = null;

                if (dataContext is ViewData viewData)
                {
                    if (_field.Label.Contains(ParentAttribute))
                    {
                        bool timezoneAware = _metadata.IsEntityKnownTimeZone(RemoteNaming.ToEntityName(viewData.Parent.Record.EntityName), ProjectionName);
                        IMetaTable metaTable = _metadata.MetaModel.GetTable(viewData.Parent.Record.GetRemoteRow().TableName);

                        string substr = _field.Label.Substring(_field.Label.IndexOf(".") + 1);
                        string label = "${" + substr;
                        value = timezoneAware ? _expressionRunner.InterpolateString(label, viewData.Parent.Record, true, false, metaTable) : _expressionRunner.InterpolateString(label, viewData.Parent.Record);
                    }
                    else
                    {
                        bool timezoneAware = _metadata.IsEntityKnownTimeZone(RemoteNaming.ToEntityName(viewData.Record.EntityName), ProjectionName);
                        IMetaTable metaTable = _metadata.MetaModel.GetTable(viewData.Record.GetRemoteRow().TableName);
                        value = timezoneAware ? _expressionRunner.InterpolateString(_field.Label, viewData.Record, true, false, metaTable) : _expressionRunner.InterpolateString(_field.Label, viewData.Record);
                    }
                }

                return value;
            }

            return _field?.Label?.ToString() ?? null;
        }

        public bool MatchFilter(string filter, object dataContext)
        {
            if (string.IsNullOrEmpty(filter))
            {
                return true;
            }

            string value = GetDisplayValue(dataContext);
            return value?.IndexOf(filter, StringComparison.CurrentCultureIgnoreCase) >= 0;
        }

        public void UpdateDynamicLabel(CardData cardData)
        {
            _dynamicLabel = GetLabelValue(cardData);
        }
    }
}
