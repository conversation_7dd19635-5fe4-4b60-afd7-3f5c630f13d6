﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal class StringSpecifiedIndexOf : StringFunction
    {
        public const string FunctionName = "SpecifiedIndexOf";

        public StringSpecifiedIndexOf()
            : base(FunctionName, 3, false)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string stringToSearch = (string)parameters[0].GetValue();
            string stringToIndex = (string)parameters[1].GetValue();
            long occurrence = (long)parameters[2].GetValue();

            if (stringToSearch == null || stringToIndex == null || stringToIndex == null)
            {
                return -1;
            }
            
            int index = -1;

            for (int i = 0; i < occurrence; i++)
            {
                index = stringToSearch.IndexOf(stringToIndex, index + 1);
            }

            return index;
        }
    }
}
