﻿using System;
using System.Collections.Generic;
using Ifs.Uma.Database;

namespace Ifs.Uma.Data.Transitions
{
    [Table(Name= "fnd$transition_grouping", System = true)]
    public class Transition
    {
        public Transition()
        {
        }

        [Column(PrimaryKey = true, AutoIncrement=true)]
        public long TransitionId { get; set; }
        [Column]
        public DateTime Timestamp { get; set; }
    }
}
