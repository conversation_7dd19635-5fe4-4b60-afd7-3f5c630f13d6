﻿using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using Ifs.Uma.Tests.TestClasses;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class EntityQuerySqlTests : FrameworkTest
    {
        [Test]
        public void Take()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA");
            query.Take = 1;

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a" +
                                 " FROM tst_expand t0" +
                                 " ORDER BY t0.id ASC" +
                                 " FETCH FIRST 1 ROWS ONLY");
        }

        [Test]
        public void Skip()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA");
            query.Skip = 20;

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a" +
                                 " FROM tst_expand t0" +
                                 " ORDER BY t0.id ASC" +
                                 " OFFSET 20 ROWS");
        }

        [Test]
        public void SkipAndTake()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA");
            query.Skip = 30;
            query.Take = 10;

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a" +
                                 " FROM tst_expand t0" +
                                 " ORDER BY t0.id ASC" +
                                 " OFFSET 30 ROWS" +
                                 " FETCH FIRST 10 ROWS ONLY");
        }

        [Test]
        public void Search()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA");
            query.Search = new AttributeSearch(new[] { "ColA" }, "test");

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                                 " t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a" +
                                 " FROM tst_expand t0" +
                                 " WHERE (t0.col_a LIKE @p0)" +
                                 " ORDER BY t0.id ASC" +
                                 " @p0='%test%'");
        }

        [Test]
        public void SearchTimestamp()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Customers");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA");
            query.Search = new AttributeSearch(new[] { "CreatedAt" }, "11:55");

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.customer_no" +
                " FROM tst_customer t0" +
                " WHERE (IfsDateTimeFormatFunction(t0.created_at, @p0) LIKE @p1)" +
                " ORDER BY t0.customer_no ASC" +
                " @p0='g', @p1='%11:55%'");
        }

        [Test]
        public void ExpandSearch()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA");
            query.Search = new AttributeSearch(new[] { "ExpandNumRef.Col1" }, "test");

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a" +
                " FROM tst_expand t0" +
                " LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id" +
                " WHERE (t0$ExpandNumRef.col1 LIKE @p0)" +
                " ORDER BY t0.id ASC @p0='%test%'");
        }

        [Test]
        public void ExpandOnSelect()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA", "ExpandNumRef.Col1");
            
            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a," +
                " t0$ExpandNumRef.row_id AS expand_num_ref$row_id, t0$ExpandNumRef.obj_id AS expand_num_ref$obj_id, t0$ExpandNumRef.obj_version AS expand_num_ref$obj_version, t0$ExpandNumRef.id AS expand_num_ref$id, t0$ExpandNumRef.col1 AS expand_num_ref$col1" +
                " FROM tst_expand t0" +
                " LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id" +
                " ORDER BY t0.id ASC");
        }

        [Test]
        public void Expand()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Expand = new[] { "ExpandNumRef", "ExpandLetterRef" };

            string sql = GetSql(query);

            Assert.AreEqual(sql,
                    "SELECT t0.row_id, t0.obj_id, t0.obj_version, t0.obj_key, t0.id, t0.col_a," +
                    " t0$ExpandNumRef.row_id AS expand_num_ref$row_id, t0$ExpandNumRef.obj_id AS expand_num_ref$obj_id, t0$ExpandNumRef.obj_version AS expand_num_ref$obj_version, t0$ExpandNumRef.obj_key AS expand_num_ref$obj_key, t0$ExpandNumRef.id AS expand_num_ref$id, t0$ExpandNumRef.col1 AS expand_num_ref$col1, t0$ExpandNumRef.col2 AS expand_num_ref$col2," +
                    " t0$ExpandLetterRef.row_id AS expand_letter_ref$row_id, t0$ExpandLetterRef.obj_id AS expand_letter_ref$obj_id, t0$ExpandLetterRef.obj_version AS expand_letter_ref$obj_version, t0$ExpandLetterRef.obj_key AS expand_letter_ref$obj_key, t0$ExpandLetterRef.id AS expand_letter_ref$id, t0$ExpandLetterRef.col_a AS expand_letter_ref$col_a, t0$ExpandLetterRef.col_b AS expand_letter_ref$col_b" +
                    " FROM tst_expand t0" +
                    " LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id" +
                    " LEFT OUTER JOIN tst_expand_letter t0$ExpandLetterRef ON t0$ExpandLetterRef.id=t0.id" +
                    " ORDER BY t0.id ASC");
        }

        [Test]
        public void ExpandPlusSelect()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA", "ExpandNumRef.Col1", "ExpandLetterRef.ColA");
            query.Expand = new[] { "ExpandNumRef", "ExpandLetterRef" };

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a," +
                " t0$ExpandNumRef.row_id AS expand_num_ref$row_id, t0$ExpandNumRef.obj_id AS expand_num_ref$obj_id, t0$ExpandNumRef.obj_version AS expand_num_ref$obj_version, t0$ExpandNumRef.id AS expand_num_ref$id, t0$ExpandNumRef.col1 AS expand_num_ref$col1," +
                " t0$ExpandLetterRef.row_id AS expand_letter_ref$row_id, t0$ExpandLetterRef.obj_id AS expand_letter_ref$obj_id, t0$ExpandLetterRef.obj_version AS expand_letter_ref$obj_version, t0$ExpandLetterRef.id AS expand_letter_ref$id, t0$ExpandLetterRef.col_a AS expand_letter_ref$col_a" +
                " FROM tst_expand t0" +
                " LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id" +
                " LEFT OUTER JOIN tst_expand_letter t0$ExpandLetterRef ON t0$ExpandLetterRef.id=t0.id" +
                " ORDER BY t0.id ASC");
        }

        [Test]
        public void ExpandEntitySetRefFilter()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "ExpandsWithRefFilter");
            EntityQuery query = new EntityQuery(source);
            query.SelectAttributes = new[] { "ColA" };

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a" +
                " FROM tst_expand t0" +
                " LEFT OUTER JOIN tst_expand_letter t0$ExpandLetterRef ON t0$ExpandLetterRef.id=t0.id" +
                " WHERE (t0$ExpandLetterRef.col_a=@p0)" +
                " ORDER BY t0.id ASC" +
                " @p0='John'");
        }

        [Test]
        public void ExpandWhereRefFilter()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "ExpandsWithRefFilter");
            EntityQuery query = new EntityQuery(source);
            query.SelectAttributes = new[] { "ColA" };
            query.JsonWhereExpression = new JsonWhereExpression(source, JObject.Parse("{ \"==\": [ { \"var\": \"ExpandNumRef.Col1\" }, \"Paul\" ] }"));

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a" +
                " FROM tst_expand t0" +
                " LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id" +
                " LEFT OUTER JOIN tst_expand_letter t0$ExpandLetterRef ON t0$ExpandLetterRef.id=t0.id" +
                " WHERE ((t0$ExpandNumRef.col1=@p0) AND (t0$ExpandLetterRef.col_a=@p1))" +
                " ORDER BY t0.id ASC @p0='Paul', @p1='John'");
        }

        [Test]
        public void ExpandAttributeFilter()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.AddFilter("ExpandLetterRef.ColA", "Cat");

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.obj_key, t0.id, t0.col_a" +
                " FROM tst_expand t0" +
                " LEFT OUTER JOIN tst_expand_letter t0$ExpandLetterRef ON t0$ExpandLetterRef.id=t0.id" +
                " WHERE (t0$ExpandLetterRef.col_a=@p0)" +
                " ORDER BY t0.id" +
                " ASC @p0='Cat'");
        }

        [Test]
        public void ExpandAttributeSort()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.AddSort("ExpandLetterRef.ColA", ESortOrder.Ascending);

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.obj_key, t0.id, t0.col_a" +
                " FROM tst_expand t0" +
                " LEFT OUTER JOIN tst_expand_letter t0$ExpandLetterRef ON t0$ExpandLetterRef.id=t0.id" +
                " ORDER BY t0$ExpandLetterRef.col_a ASC");
        }

        [Test]
        public void Array()
        {
            IMetadata metadata = Resolve<IMetadata>();
            RemoteRow row = new RemoteRow("tst_customer");
            row["CustomerNo"] = "AAA";

            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Customers");
            source = source.SelectArray(row, "ExpandsArray");

            EntityQuery query = new EntityQuery(source);
            query.SelectAttributes = new[] { "Id" };

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT t0.row_id, t0.obj_id, t0.obj_version, t0.id" +
                                 " FROM tst_expand t0" +
                                 " WHERE (t0.col_a=@p0)" +
                                 " ORDER BY t0.id ASC" +
                                 " @p0='AAA'");
        }

        [Test]
        public void ArrayFiltered()
        {
            IMetadata metadata = Resolve<IMetadata>();
            RemoteRow row = new RemoteRow("tst_customer");
            row["CustomerNo"] = "AAA";

            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Customers");
            source = source.SelectArray(row, "ExpandsFilteredArray");

            EntityQuery query = new EntityQuery(source);
            query.SelectAttributes = new[] { "Id" };

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT t0.row_id, t0.obj_id, t0.obj_version, t0.id" +
                                 " FROM tst_expand t0" +
                                 " WHERE ((t0.col_a=@p0) AND (t0.id=@p1))" +
                                 " ORDER BY t0.id ASC" +
                                 " @p0='AAA', @p1='John'");
        }

        [Test]
        public void AttributeWhereExpression()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntitySet(metadata, TestOfflineProjection, "Expands");
            EntityQuery query = new EntityQuery(source);
            query.Select("ColA");
            
            AttributeExpression exp = 
                AttributeExpression.And(
                    AttributeExpression.Or(
                        AttributeExpression.Compare("ExpandLetterRef.ColA", AttributeCompareOperator.Equals, "Hello"),
                        AttributeExpression.Compare("ExpandNumRef.Col1", AttributeCompareOperator.LessThan, 8)),
                    AttributeExpression.Compare("ColA", AttributeCompareOperator.NotEquals, "cat"));

            query.FilterExpression = exp;

            string sql = GetSql(query);

            Assert.AreEqual(sql, "SELECT" +
                " t0.row_id, t0.obj_id, t0.obj_version, t0.id, t0.col_a" +
                " FROM tst_expand t0" +
                " LEFT OUTER JOIN tst_expand_letter t0$ExpandLetterRef ON t0$ExpandLetterRef.id=t0.id" +
                " LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id" +
                " WHERE (((t0$ExpandLetterRef.col_a=@p0) OR (t0$ExpandNumRef.col1<@p1)) AND (t0.col_a<>@p2))" +
                " ORDER BY t0.id ASC" +
                " @p0='Hello', @p1='8', @p2='cat'");
        }

        private static string GetSql(EntityQuery query)
        {
            PreparedEntityQuery preparedQuery = query.Prepare();
            return TestSqlBuilder.WriteSql(preparedQuery.SelectSpec);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("EntityQuerySchema", "EntityQueryData");
        }
    }
}
