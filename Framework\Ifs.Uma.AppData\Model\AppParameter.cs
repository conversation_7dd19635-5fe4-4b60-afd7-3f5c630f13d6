﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_" + DbTableName, Columns = nameof(Parameter), Unique = true)]
    public class MobileClientParam : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "mobile_client_param";

        [Column(ServerPrimaryKey = true)]
        public string Parameter { get; set; }

        [Column]
        public string Value { get; set; }

        [Column]
        public string ValueType { get; set; }

        public MobileClientParam()
            : base(DbTableName)
        {
        }
    }
}
