﻿using Ifs.Uma.AppData.Online;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemGetOnlineState : SystemFunction
    {
        public const string FunctionName = "GetOnlineState";
        private readonly IOnlineDataHandler _onlineDataHandler;

        public SystemGetOnlineState(IOnlineDataHandler onlineDataHandler)
            : base(FunctionName, 0)
        {
            _onlineDataHandler = onlineDataHandler;
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return _onlineDataHandler?.IsOnline;
        }
    }
}
