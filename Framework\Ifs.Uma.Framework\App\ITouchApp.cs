﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.App
{
    public interface ITouchApp : IResolver
    {
        AppRegistration Registration { get; }
        Session CurrentSession { get; }
        bool DeveloperMode { get; }
        bool DebugBuild { get; }
        bool TryMeModeAvailable { get; }
        string DisplayName { get; }
        bool SecondaryLoggingEnabled { get; }

        IEnumerable<TouchAppAccount> Accounts { get; }
        Task<TouchAppAccount> ActivateNewAccountBasic(string serviceUrl, string systemId, string appName, string userName, string password);
        Task<TouchAppAccount> ActivateNewAccountOpenId(string serviceUrl, string systemId, string appName);
        Task<TouchAppAccount> ActivateNewAccountDirectAccess(string serviceUrl, string systemId, string appName, string userName, string password);
        Task<TouchAppAccount> ActivateNewAccountNoAuth(string serviceUrl, string systemId, string appName, string userName);
        Task Activate(TouchAppAccount account, string password);
        Task Login(TouchAppAccount account, string passwordOrPin);
        Task ChangePassword(string newPassword);
        Task<string> GetDatabasePassword();
        Task ChangePinCode();
        Task Deactivate(TouchAppAccount account);
        Task RemoveAccount(TouchAppAccount account);
        Task LogoutAsync();
        void SwitchUser();
        Task LoginTryMeMode();
        Task RefreshLogin();
        Task NavigateToApp();
        void ResetPinCodeAttempt();
        Task<List<ThemeData>> BrandingData();
        Task AddClientLog(string messageText, string type);
        void ProcessLaunchArguments(string uri);
        NavigationParameter ResolveLaunchArguments();
    }
}
