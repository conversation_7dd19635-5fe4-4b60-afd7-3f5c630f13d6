﻿using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Sync
{
    internal sealed class SyncInitiateTransactionSession : SyncFunction
    {
        public const string FunctionName = "InitiateTransactionSession";

        public SyncInitiateTransactionSession()
            : base(FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string id = parameters[0].GetString();

            if (string.IsNullOrEmpty(id))
            {
                throw new ProcedureException($"SessionId parameter of {FunctionNamespace}.{FunctionName} cannot be null.");
            }

            if (context.DbDataContext.TransactionSessions.Any(x => x.SessionId == id))
            {
                throw new ProcedureException($"Transaction Session with ID '{id}' already exists.");
            }

            if (context.DbDataContext.TransactionSessions.Any(x => x.IsOpen == true))
            {
                throw new ProcedureException($"There is already another open Transaction Session.");
            }

            FndTransactionSession session = new FndTransactionSession();
            session.SessionId = id;
            session.IsOpen = true;

            context.DbDataContext.TransactionSessions.InsertOnSubmit(session);
            context.DbDataContext.SubmitChanges(false);

            Logger.Current.Information($"Transaction Session with ID '{session.SessionId}' was initiated.");

            return true;
        }
    }
}
