﻿using System.Runtime.Serialization;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Attachments
{
    [DataContract]
    public sealed class MediaNavParam : NavigationParameter
    {
        [DataMember]
        public string EntityName { get; private set; }

        [DataMember]
        public string KeyRef { get; private set; }

        [DataMember]
        public long? MediaRowId { get; private set; }

        public MediaNavParam(string entityName, string keyRef)
        {
            EntityName = entityName;
            KeyRef = keyRef;
        }

        public MediaNavParam(string entityName, string keyRef, long? mediaRowId)
        {
            EntityName = entityName;
            KeyRef = keyRef;
            MediaRowId = mediaRowId;
        }
    }
}
