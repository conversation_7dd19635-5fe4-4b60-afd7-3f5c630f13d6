﻿using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Address;
using Ifs.Uma.AppData.Attachments;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.AppData.Permissions;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.Reporting;
using Ifs.Uma.Framework.Services;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata;
using Ifs.Uma.Services.Attachments;
using Ifs.Uma.Services.Attachments.Documents;
using Ifs.Uma.Services.Attachments.Media;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.Services.Permissions;
using Ifs.Uma.Services.Profiles;
using Ifs.Uma.Services.Sync;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.Utility;
using Prism.Events;
using Unity;
using Unity.Lifetime;

namespace Ifs.Uma.Framework.App
{
    public interface IRegistrar
    {
        void ApplySessionRegistrations(IUnityContainer container);
        void ApplyInitializedRegistrations(IUnityContainer container);
    }

    public class Registrar : IRegistrar
    {
        private const bool ForceOffline = false;

        public void ApplySessionRegistrations(IUnityContainer container)
        {
            OnApplySessionRegistrations(container);
        }

        protected virtual void OnApplySessionRegistrations(IUnityContainer container)
        {
            IResolver resolver = container.Resolve<IResolver>();

            container.RegisterType<IMetadata>(new MetadataManager(resolver));

            // Create EventAggregatorhere to ensure it is created on the UI Thread
            IEventAggregator eventAggregator = new EventAggregator();
            container.RegisterInstance(eventAggregator);

            container.RegisterType<IClientKeysMapper, ClientKeysMapper>(new ContainerControlledLifetimeManager());
            container.RegisterType<ISyncController, SyncController>(new ContainerControlledLifetimeManager());
            container.RegisterType<ITransactionFailedEvent, TransactionFailedEvent>(new ContainerControlledLifetimeManager());
            container.RegisterService<InsightsService>();
            container.RegisterService<ITransactionSyncDataHandler, TransactionSyncDataHandler>();

            if (resolver.TryResolve(out IIfsConnection connection) && !ForceOffline)
            {
                SyncCommsProvider commsProvider = new SyncCommsProvider(connection.TouchAppsComms, resolver.Resolve<ILogger>());
                container.RegisterInstance<ITransactionSyncCommsProvider>(commsProvider);
                container.RegisterService<ITransactionSyncService, TransactionSyncService>();
                container.RegisterService<TransactionSyncDataChangedNotifier>();
                container.RegisterService<TransactionSyncInsightsTracker>();
                container.RegisterService<SwitchToSyncMonitorService>();
                container.RegisterService<InvalidCredentialsWatcher>();
            }

            container.RegisterService<ILocalProfile, LocalProfile>();
        }

        public void ApplyInitializedRegistrations(IUnityContainer container)
        {
            OnApplyInitializedRegistrations(container);
        }

        protected virtual void OnApplyInitializedRegistrations(IUnityContainer container)
        {
            IResolver resolver = container.Resolve<IResolver>();

            container.RegisterService<IAppPermissions, AppPermissions>();
            container.RegisterService<IAppParameters, AppParameters>();
            container.RegisterService<AppParametersApplier>();
            container.RegisterService<IRoamingProfile, RoamingProfile>();

            bool hasConnection = resolver.TryResolve(out IIfsConnection _);
            if (hasConnection)
            {
                container.RegisterType<ITransactionWaiter, TransactionWaiter>(new ContainerControlledLifetimeManager());
                container.RegisterType<IOnlineDataHandler, OnlineDataHandler>(new ContainerControlledLifetimeManager());
                container.RegisterType<IOnlineAttachmentHandler, OnlineAttachmentHandler>(new ContainerControlledLifetimeManager());
            }
            else
            {
                container.RegisterType<ITransactionWaiter, AlwaysOfflineTransactionWaiter>(new ContainerControlledLifetimeManager());
                container.RegisterType<IOnlineDataHandler, AlwaysOfflineOnlineDataHandler>(new ContainerControlledLifetimeManager());
                container.RegisterType<IOnlineAttachmentHandler, AlwaysOfflineOnlineAttachmentHandler>(new ContainerControlledLifetimeManager());
            }

            container.RegisterType<ICachePreparer, CachePreparer>(new ContainerControlledLifetimeManager());
            container.RegisterType<IDataHandler, DataHandler>(new ContainerControlledLifetimeManager());
            container.RegisterType<IOnDemandDataHandler, OnDemandDataHandler>(new ContainerControlledLifetimeManager());
            container.RegisterType<IExpressionRunner, ExpressionRunner>(new ContainerControlledLifetimeManager());
            container.RegisterType<ICommandExecutor, CommandExecutor>(new ContainerControlledLifetimeManager());
            container.RegisterType<IProcedureExecutor, ProcedureExecutor>(new ContainerControlledLifetimeManager());
            container.RegisterType<ILovService, LovService>(new ContainerControlledLifetimeManager());
            container.RegisterType<IDocumentHandler, DocumentHandler>(new ContainerControlledLifetimeManager());
            container.RegisterType<IAddressHandler, AddressHandler>(new ContainerControlledLifetimeManager());
            container.RegisterType<IMediaHandler, MediaHandler>(new ContainerControlledLifetimeManager());
            container.RegisterType<IOfflineChecker, OfflineChecker>(new ContainerControlledLifetimeManager());
            container.RegisterType<IPageCreator, PageCreator>(new ContainerControlledLifetimeManager());
            container.RegisterType<ICardDefCreator, CardDefCreator>(new ContainerControlledLifetimeManager());
            container.RegisterType<IReportingService, ReportingService>(new ContainerControlledLifetimeManager());
            container.RegisterType<IHomePageItems, HomePageItems>(new ContainerControlledLifetimeManager());

#if REMOTE_ASSISTANCE
            container.RegisterType<RemoteAssistance.IRemoteAssistanceService, RemoteAssistance.RemoteAssistanceService>(new ContainerControlledLifetimeManager());
#endif

            if (hasConnection)
            {
                container.RegisterService<SyncFailingWatcher>();
                container.RegisterService<SyncOnDataChangeTriggerer>();
                container.RegisterService<InitializationRequiredNotifier>();
                container.RegisterService<MetadataRefreshRequiredNotifier>();
                container.RegisterService<SynchronizationEndedEvent>();
                container.RegisterService<CommsBrokenWatcher>();
                container.RegisterType<IAttachmentComms, AttachmentComms>(new ContainerControlledLifetimeManager());
                container.RegisterService<IDocumentSyncService, DocumentSyncService>();
                container.RegisterService<IMediaSyncService, MediaSyncService>();
            }
        }

        private class MetadataManager : LifetimeManager
        {
            private readonly IResolver _resolver;

            public MetadataManager(IResolver resolver)
            {
                _resolver = resolver;
            }

            public override object GetValue(ILifetimeContainer container = null)
            {
                if (_resolver.TryResolve(out IDatabaseController dbController))
                {
                    return dbController.GetMetadata();
                }

                return null;
            }

            public override void SetValue(object newValue, ILifetimeContainer container = null)
            {
            }

            public override void RemoveValue(ILifetimeContainer container = null)
            {
            }

            protected override LifetimeManager OnCreateLifetimeManager()
            {
                return new MetadataManager(_resolver);
            }
        }
    }
}
