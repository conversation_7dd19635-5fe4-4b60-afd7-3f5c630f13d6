﻿using System.Linq.Expressions;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Database
{
    [TestFixture]
    public class QueryExpressionSqlTests : FrameworkTest
    {
        [Test]
        public void Basic()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    null,
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerNo")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual("SELECT c.customer_no FROM tst_customer c", sql);
        }

        [Test]
        public void Distinct()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    null,
                    null,
                    true,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerNo")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual("SELECT DISTINCT c.customer_no FROM tst_customer c", sql);
        }

        [Test]
        public void SelectExpression()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    null,
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn(
                            Expression.MakeBinary(ExpressionType.Add, IfsExpression.VarAccess("c.CustomerNo"), IfsExpression.VarAccess("c.CustomerName")),
                            "CustomerInfo")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual("SELECT (c.customer_no+c.customer_name) AS customer_info FROM tst_customer c", sql);
        }

        [Test]
        public void Where()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    Expression.MakeBinary(ExpressionType.Equal, IfsExpression.VarAccess("c.CustomerName"), IfsExpression.Value("John")),
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerNo")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual("SELECT c.customer_no FROM tst_customer c WHERE (c.customer_name=@p0) @p0='John'", sql);
        }

        [Test]
        public void Sort()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    null,
                    new[]
                    {
                        IfsExpression.QuerySort("c.CustomerNo", ESortOrder.Descending),
                        IfsExpression.QuerySort("c.CustomerName", ESortOrder.Ascending)
                    },
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerNo")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual("SELECT c.customer_no FROM tst_customer c ORDER BY c.customer_no DESC, c.customer_name ASC", sql);
        }

        [Test]
        public void LeftJoin()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    new[]
                    {
                        IfsExpression.QueryJoin(EJoinType.LeftOuter, "TstCustomerType", "ct",
                            Expression.MakeBinary(ExpressionType.Equal, IfsExpression.VarAccess("c.CustomerType"), IfsExpression.VarAccess("ct.TypeId")))
                    },
                    null, 
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerNo"),
                        IfsExpression.QueryResultColumn("ct.TypeDescription"),
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c.customer_no, ct.type_description " +
                "FROM tst_customer c " +
                "LEFT OUTER JOIN tst_customer_type ct ON (c.customer_type=ct.type_id)", sql);
        }

        [Test]
        public void InnerJoin()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    new[]
                    {
                        IfsExpression.QueryJoin(EJoinType.Inner, "TstCustomerType", "ct",
                            Expression.MakeBinary(ExpressionType.Equal, IfsExpression.VarAccess("c.CustomerType"), IfsExpression.VarAccess("ct.TypeId")))
                    },
                    null,
                    null,
                    true,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("ct.TypeDescription")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT DISTINCT ct.type_description " +
                "FROM tst_customer c " +
                "INNER JOIN tst_customer_type ct ON (c.customer_type=ct.type_id)", sql);
        }

        [Test]
        public void MultiJoin()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    new[]
                    {
                        IfsExpression.QueryJoin(EJoinType.LeftOuter, "TstCustomerType", "ct",
                            Expression.MakeBinary(ExpressionType.Equal, IfsExpression.VarAccess("c.CustomerType"), IfsExpression.VarAccess("ct.TypeId"))),
                         IfsExpression.QueryJoin(EJoinType.Inner, "TstCustomerType", "ct2",
                            Expression.MakeBinary(ExpressionType.Equal, IfsExpression.VarAccess("ct.TypeId"), IfsExpression.VarAccess("ct2.TypeId"))),
                    },
                    null,
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerNo")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c.customer_no FROM tst_customer c " +
                "LEFT OUTER JOIN tst_customer_type ct ON (c.customer_type=ct.type_id) " +
                "INNER JOIN tst_customer_type ct2 ON (ct.type_id=ct2.type_id)", sql);
        }

        [Test]
        public void ImplicitJoinViaSelect()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    null,
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerTypeRef.TypeDescription")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c$CustomerTypeRef.type_description " +
                "FROM tst_customer c " +
                "LEFT OUTER JOIN tst_customer_type c$CustomerTypeRef ON c$CustomerTypeRef.type_id=c.customer_type", sql);
        }

        [Test]
        public void ImplicitJoinViaWhere()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    Expression.MakeBinary(ExpressionType.Equal, IfsExpression.VarAccess("c.CustomerTypeRef.TypeDescription"), IfsExpression.Value("NEW")),
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerNo")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c.customer_no FROM tst_customer c " +
                "LEFT OUTER JOIN tst_customer_type c$CustomerTypeRef ON c$CustomerTypeRef.type_id=c.customer_type " +
                "WHERE (c$CustomerTypeRef.type_description=@p0) @p0='NEW'", sql);
        }

        [Test]
        public void ImplicitJoinViaSort()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    null,
                    new[]
                    {
                        IfsExpression.QuerySort("c.CustomerTypeRef.TypeDescription", ESortOrder.Descending),
                    },
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerNo")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c.customer_no FROM tst_customer c " +
                "LEFT OUTER JOIN tst_customer_type c$CustomerTypeRef ON c$CustomerTypeRef.type_id=c.customer_type " +
                "ORDER BY c$CustomerTypeRef.type_description DESC", sql);
        }

        [Test]
        public void ImplicitJoinViaWhereAndSelect()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    Expression.MakeBinary(ExpressionType.Equal, IfsExpression.VarAccess("c.CustomerTypeRef.TypeDescription"), IfsExpression.Value("NEW")),
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("c.CustomerTypeRef.TypeDescription")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c$CustomerTypeRef.type_description " +
                "FROM tst_customer c " +
                "LEFT OUTER JOIN tst_customer_type c$CustomerTypeRef ON c$CustomerTypeRef.type_id=c.customer_type " +
                "WHERE (c$CustomerTypeRef.type_description=@p0) " +
                "@p0='NEW'", sql);
        }

        [Test]
        public void ImplicitAndExplicitJoin()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    new[]
                    {
                        IfsExpression.QueryJoin(EJoinType.LeftOuter, "TstCustomerType", "ct",
                            Expression.MakeBinary(ExpressionType.Equal, IfsExpression.VarAccess("c.CustomerType"), IfsExpression.VarAccess("ct.TypeId")))
                    },
                    null,
                    null,
                    false,
                    new[]
                    {
                        IfsExpression.QueryResultColumn("ct.TypeDescription"),
                        IfsExpression.QueryResultColumn("c.CustomerTypeRef.TypeDescription")
                    }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT ct.type_description, c$CustomerTypeRef.type_description " +
                "FROM tst_customer c " +
                "LEFT OUTER JOIN tst_customer_type ct ON (c.customer_type=ct.type_id) " +
                "LEFT OUTER JOIN tst_customer_type c$CustomerTypeRef ON c$CustomerTypeRef.type_id=c.customer_type", sql);
        }

        [Test]
        public void QueryScopeParent()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    IfsExpression.Exists(
                        IfsExpression.Query(
                            IfsExpression.QueryFrom("TstCustomerType", "ct"),
                            null,
                            IfsExpression.FromString("ct.TypeId == c.CustomerType"),
                            null,
                            false,
                            new[] { IfsExpression.QueryResultColumn("ct.TypeDescription") }
                    )),
                    null,
                    false,
                    new[] { IfsExpression.QueryResultColumn("c.CustomerNo") }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c.customer_no FROM tst_customer c " +
                "WHERE  EXISTS (" +
                    "SELECT ct.type_description " +
                    "FROM tst_customer_type ct " +
                    "WHERE (ct.type_id=c.customer_type))", sql);
        }

        [Test]
        public void QueryScopeParentOfParent()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "cA"),
                    null,
                    IfsExpression.Exists(
                        IfsExpression.Query(
                            IfsExpression.QueryFrom("TstCustomerType", "cB"),
                            null,
                            IfsExpression.Exists(
                                IfsExpression.Query(
                                    IfsExpression.QueryFrom("TstCustomerType", "cC"),
                                    null,
                                    IfsExpression.FromString("cC.TypeId == cA.CustomerType"),
                                    null,
                                    false,
                                    new[] { IfsExpression.QueryResultColumn("cC.TypeDescription") }
                            )),
                            null,
                            false,
                            new[] { IfsExpression.QueryResultColumn("cB.TypeDescription") }
                    )),
                    null,
                    false,
                    new[] { IfsExpression.QueryResultColumn("cA.CustomerNo") }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT cA.customer_no " +
                "FROM tst_customer cA " +
                "WHERE  EXISTS (" +
                    "SELECT cB.type_description " +
                    "FROM tst_customer_type cB " +
                    "WHERE  EXISTS (" +
                        "SELECT cC.type_description " +
                        "FROM tst_customer_type cC " +
                        "WHERE (cC.type_id=cA.customer_type)))", sql);
        }

        [Test]
        public void QueryScopeSiblingSameAlias()
        {
            QueryExpression q1 = IfsExpression.Query(
                                    IfsExpression.QueryFrom("TstCustomerType", "ct"),
                                    null,
                                    IfsExpression.FromString("ct.TypeId == c.CustomerType"),
                                    null,
                                    false,
                                    new[] { IfsExpression.QueryResultColumn("ct.TypeDescription") }
                                );

            QueryExpression q2 = IfsExpression.Query(
                                    IfsExpression.QueryFrom("TstCustomer", "ct"),
                                    null,
                                    IfsExpression.FromString("ct.CustomerType == c.CustomerType"),
                                    null,
                                    false,
                                    new[] { IfsExpression.QueryResultColumn("ct.CustomerName") }
                                );

            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    Expression.AndAlso(IfsExpression.Exists(q1), IfsExpression.Exists(q2)),
                    null,
                    false,
                    new[] { IfsExpression.QueryResultColumn("c.CustomerNo") }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c.customer_no " +
                "FROM tst_customer c " +
                "WHERE ( EXISTS (" +
                    "SELECT ct.type_description " +
                    "FROM tst_customer_type ct " +
                    "WHERE (ct.type_id=c.customer_type)) " +
                "AND  EXISTS (" +
                    "SELECT ct.customer_name " +
                    "FROM tst_customer ct " +
                    "WHERE (ct.customer_type=c.customer_type)))", sql);
        }

        [Test]
        public void QueryScopeParentSameAlias()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    IfsExpression.Exists(
                        IfsExpression.Query(
                            IfsExpression.QueryFrom("TstCustomerType", "c"),
                            null,
                            IfsExpression.FromString("c.TypeId == 'TEST'"),
                            null,
                            false,
                            new[] { IfsExpression.QueryResultColumn("c.TypeDescription") }
                    )),
                    null,
                    false,
                    new[] { IfsExpression.QueryResultColumn("c.CustomerNo") }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c.customer_no FROM tst_customer c " +
                "WHERE  EXISTS (" +
                    "SELECT c.type_description " +
                    "FROM tst_customer_type c " +
                    "WHERE (c.type_id=@p0)) @p0='TEST'", sql);
        }

        [Test]
        public void QueryScopeImplicitInParent()
        {
            QueryExpression exp = IfsExpression.Query(
                    IfsExpression.QueryFrom("TstCustomer", "c"),
                    null,
                    IfsExpression.Exists(
                        IfsExpression.Query(
                            IfsExpression.QueryFrom("TstCustomerType", "ct"),
                            null,
                            IfsExpression.FromString("c.CustomerTypeRef.TypeId == ct.TypeId"),
                            null,
                            false,
                            new[] { IfsExpression.QueryResultColumn("ct.TypeDescription") }
                    )),
                    null,
                    false,
                    new[] { IfsExpression.QueryResultColumn("c.CustomerNo") }
                );

            string sql = GetSql(exp);
            Assert.AreEqual(
                "SELECT c.customer_no FROM tst_customer c " +
                "LEFT OUTER JOIN tst_customer_type c$CustomerTypeRef ON c$CustomerTypeRef.type_id=c.customer_type " +
                "WHERE  EXISTS (" +
                    "SELECT ct.type_description " +
                    "FROM tst_customer_type ct " +
                    "WHERE (c$CustomerTypeRef.type_id=ct.type_id))", sql);
        }

        private string GetSql(QueryExpression expression)
        {
            IMetadata metadata = CreateMetadata<FwDataContext>(typeof(QueryExpressionSqlTests), "Database.QuerySchema.json");
            ISelectSpec selectSpec = QuerySelectSpec.Create(expression, new QueryScope(TestOfflineProjection, "TstCustomer", metadata));
            return SqlBuilder.BuildDebugGenericSql(selectSpec);
        }
    }
}
