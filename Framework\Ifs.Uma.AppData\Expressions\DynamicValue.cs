﻿using System;
using System.Collections.Concurrent;
using System.Globalization;
using System.Linq;
using System.Reflection;
using Ifs.Uma.Utility;

// ReSharper disable CompareOfFloatsByEqualityOperator

namespace Ifs.Uma.AppData.Expressions
{
    internal struct DynamicValue
    {
        internal static readonly DynamicValue NullValue = new DynamicValue(null);

        internal static readonly MethodInfo ConvertToObjectMethod = typeof(DynamicValue).GetTypeInfo().GetDeclaredMethods(nameof(ConvertToObject)).Single();
        internal static readonly MethodInfo ConvertToBooleanMethod = typeof(DynamicValue).GetTypeInfo().GetDeclaredMethods(nameof(ConvertToBoolean)).Single();

        private static readonly ConcurrentDictionary<Type, object> TypeDefaults = new ConcurrentDictionary<Type, object>();

        public object Value { get; }

        public DynamicValue(object value)
        {
            if (Equals(value, string.Empty))
            {
                value = null;
            }

            Value = value;
        }

        public static implicit operator DynamicValue(string value)
        {
            return new DynamicValue(value);
        }

        public static implicit operator string(DynamicValue value)
        {
            return ObjectConverter.ToString(value.Value);
        }

        private static object ConvertToObject(DynamicValue value)
        {
            return value.Value;
        }

        private static bool ConvertToBoolean(DynamicValue value)
        {
            return value.ToBool();
        }

        public static bool operator ==(DynamicValue x, DynamicValue y)
        {
            if (x.Value == null || y.Value == null || x.Value.GetType() == y.Value.GetType())
            {
                return Equals(x.Value, y.Value);
            }

            double xVal = x.ToNumber();
            double yVal = y.ToNumber();

            return xVal == yVal;
        }

        public static bool operator !=(DynamicValue x, DynamicValue y)
        {
            return !(x == y);
        }

        public static bool operator !(DynamicValue x)
        {
            return !x.ToBool();
        }

        public static DynamicValue operator |(DynamicValue x, DynamicValue y)
        {
            return x.ToBool() ? x : y;
        }

        public static DynamicValue operator &(DynamicValue x, DynamicValue y)
        {
            return x.ToBool() ? y : x;
        }

        public static bool operator true(DynamicValue x)
        {
            return x.ToBool();
        }

        public static bool operator false(DynamicValue x)
        {
            return !x.ToBool();
        }

        public static bool operator >(DynamicValue x, DynamicValue y)
        {
            double xVal = x.ToNumber();
            double yVal = y.ToNumber();
            return xVal > yVal;
        }

        public static bool operator <(DynamicValue x, DynamicValue y)
        {
            double xVal = x.ToNumber();
            double yVal = y.ToNumber();
            return xVal < yVal;
        }

        public static bool operator >=(DynamicValue x, DynamicValue y)
        {
            double xVal = x.ToNumber();
            double yVal = y.ToNumber();
            return xVal > yVal || xVal == yVal;
        }

        public static bool operator <=(DynamicValue x, DynamicValue y)
        {
            double xVal = x.ToNumber();
            double yVal = y.ToNumber();
            return xVal < yVal || xVal == yVal;
        }

        public static DynamicValue operator +(DynamicValue x, DynamicValue y)
        {
            if (x.Value is string || y.Value is string)
            {
                string strX = ObjectConverter.ToString(x.Value);
                string strY = ObjectConverter.ToString(y.Value);

                return new DynamicValue(strX + strY);
            }

            return DoNumberOperator(x, y, (xV, yV) => xV + yV);
        }

        public static DynamicValue operator -(DynamicValue x, DynamicValue y)
        {
            if (x.Value is string || y.Value is string)
            {
                return new DynamicValue(double.NaN);
            }

            return DoNumberOperator(x, y, (xV, yV) => xV - yV);
        }

        public static DynamicValue operator /(DynamicValue x, DynamicValue y)
        {
            if (x.Value is string || y.Value is string)
            {
                return new DynamicValue(double.NaN);
            }

            if (x.Value == null && y.Value == null)
            {
                return new DynamicValue(double.NaN);
            }

            return DoNumberOperator(x, y, (xV, yV) => xV / yV);
        }

        public static DynamicValue operator *(DynamicValue x, DynamicValue y)
        {
            if (x.Value is string || y.Value is string)
            {
                return new DynamicValue(double.NaN);
            }

            return DoNumberOperator(x, y, (xV, yV) => xV * yV);
        }

        public static DynamicValue operator %(DynamicValue x, DynamicValue y)
        {
            if (x.Value is string || y.Value is string)
            {
                return new DynamicValue(double.NaN);
            }

            if (x.Value == null && y.Value == null)
            {
                return new DynamicValue(double.NaN);
            }

            return DoNumberOperator(x, y, (xV, yV) => xV % yV);
        }

        private static DynamicValue DoNumberOperator(DynamicValue x, DynamicValue y, Func<double, double, double> func)
        {
            if (x.Value == null && y.Value == null)
            {
                return new DynamicValue(0.0);
            }
            
            Type type = FindCommonType(x, y);
            double xVal = x.ToNumber();
            double yVal = y.ToNumber();

            double value = func(xVal, yVal);

            if (double.IsInfinity(value) || double.IsNaN(value))
            {
                return new DynamicValue(value);
            }

            return new DynamicValue(Convert.ChangeType(value, type));
        }

        private static Type FindCommonType(DynamicValue left, DynamicValue right)
        {
            Type leftType = left.Value?.GetType();
            Type rightType = right.Value?.GetType() ?? leftType;

            var leftTypeCode = TypeHelper.GetTypeCode(leftType);
            var rightTypeCode = TypeHelper.GetTypeCode(rightType);

            return leftTypeCode >= rightTypeCode ? leftType : rightType;
        }

        public DateTime? ToDateTime()
        {
            if (Value == null)
            {
                return null;
            }

            try
            {
                return ObjectConverter.ToDateTime(Value);
            }
            catch
            {
                return null;
            }
        }

        public bool ToBool()
        {
            if (Value == null)
            {
                return false;
            }

            Type type = Value.GetType();
            object defaultValue = type.GetTypeInfo().IsValueType
                ? TypeDefaults.GetOrAdd(type, Activator.CreateInstance)
                : null;

            return !Equals(Value, defaultValue);
        }

        public double ToNumber()
        {
            if (Value == null)
            {
                return 0;
            }

            if (Value is double)
            {
                return (double)Value;
            }

            if (Value is DateTime dateTime)
            {
                // Same as JavaScript Date.getTime()
                TimeSpan t = dateTime.ToClientUniversalTime() - new DateTime(1970, 1, 1);
                return t.TotalMilliseconds;
            }

            if (Value is string)
            {
                double.TryParse((string)Value, NumberStyles.Any, CultureInfo.InvariantCulture, out double result);
                return result;
            }
            
            return Convert.ToDouble(Value);
        }

        private bool Equals(DynamicValue other)
        {
            // Do the same as the equality operator above
            return this == other;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            return obj is DynamicValue && Equals((DynamicValue)obj);
        }

        public override int GetHashCode()
        {
            return (Value != null ? Value.GetHashCode() : 0);
        }

        public override string ToString()
        {
            if (Value == null)
            {
                return "null";
            }

            if (Value is string str)
            {
                return "'" + str + "'";
            }
            
            return ObjectConverter.ToString(Value);
        }

        public string GetCleanString()
        {
            if (Value == null)
            {
                return null;
            }
            return ObjectConverter.ToString(Value);
        }
    }
}
