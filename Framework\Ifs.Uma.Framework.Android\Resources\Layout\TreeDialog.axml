<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:theme="@style/Theme.Ifs.Dialog">
    
    <android.support.design.widget.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/Theme.Ifs.AppBarOverlay">
        <android.support.v7.widget.Toolbar
            android:id="@id/Toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/Theme.Ifs.PopupOverlay" />
    </android.support.design.widget.AppBarLayout>
    
    <TextView
        android:id="@+id/OfflineWarning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:paddingTop="5dp"
        android:paddingEnd="10dp"
        android:paddingBottom="5dp"
        android:layout_gravity="center_vertical|center_horizontal"
        android:textDirection="locale"
        android:textSize="18sp" />

    <Ifs.Uma.UI.Controls.MaxHeightFrameLayout
        android:id="@+id/BreadCrumbsHeightLimiter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <ImageView
                android:id="@+id/AncestorButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:layout_gravity="center_horizontal" />
            <android.support.v7.widget.RecyclerView
            android:id="@+id/BreadCrumbsList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="vertical"/>
            </LinearLayout>        
    </Ifs.Uma.UI.Controls.MaxHeightFrameLayout>

    <TextView
        android:id="@+id/ChildObjectsLabel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingTop="8dp"
        android:paddingEnd="16dp"
        android:paddingBottom="8dp"
        android:textSize="18sp" />
    
    <RelativeLayout
        android:id="@+id/TreeItemsContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <android.support.v7.widget.RecyclerView
            android:id="@+id/RecyclerList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="vertical" />
        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:id="@+id/progressbar_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:indeterminateTint="?attr/colorPrimary"
            android:visibility="gone"
            android:padding="8dp"
            android:layout_marginBottom="64dp" />
        <android.support.v7.widget.Toolbar
            android:id="@+id/FilterBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/colorPrimary"
            android:layout_alignParentBottom="true">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/FilterButton"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center_vertical"
                    android:tint="@android:color/white"
                    android:visibility="gone"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:srcCompat="@drawable/ic_action_filter" />
                <TextView
                    android:id="@+id/AppliedFilters"
                    android:ellipsize="middle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_margin="5dp"
                    android:maxLines="1"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    tools:text="Filter names go here" />
            </LinearLayout>
        </android.support.v7.widget.Toolbar>
    </RelativeLayout>
    
</LinearLayout>
