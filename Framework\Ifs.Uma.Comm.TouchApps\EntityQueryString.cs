﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Comm.TouchApps
{
    internal sealed class EntityQueryString
    {
        public const int DefaultTakeCount = 50;

        private readonly EntityQuery _query;
        private const string IfsAppPrefix = "IfsApp";

        public static void Build(EntityQuery query, Dictionary<string, string> into)
        {
            EntityQueryString qs = new EntityQueryString(query);
            qs.GetQueryString(into);
        }

        private EntityQueryString(EntityQuery query)
        {
            _query = query;
        }

        private void GetQueryString(Dictionary<string, string> into)
        {
            bool isArray = _query.DataSource is ArrayDataSource;

            string expands = GetExpands();
            if (!string.IsNullOrEmpty(expands))
            {
                into["expand"] = expands;
            }

            if (!isArray) // Arrays already add the parent key to the URL
            {
                string filter = GetFilter();
                if (!string.IsNullOrEmpty(filter))
                {
                    into["filter"] = filter;
                }
            }

            if (_query.Sorts.Count != 0)
            {
                into["orderby"] = GetOrderBy();
            }

            string select = GetSelect();
            if (!string.IsNullOrEmpty(select))
            {
                into["select"] = select;
            }

            into["skip"] = _query.Skip.GetValueOrDefault(0).ToString();
            into["top"] = _query.Take.GetValueOrDefault(DefaultTakeCount).ToString();
        }

        private string GetExpands()
        {
            IReadOnlyList<string> expands = _query.GetRequiredExpands();
            if (expands.Count == 0)
            {
                return null;
            }

            StringBuilder sb = new StringBuilder();

            if (expands.Count > 0)
            {
                foreach (string refName in expands)
                {
                    CpiReference reference = _query.DataSource.Metadata.FindReference(_query.DataSource.ProjectionName, _query.DataSource.EntityName, refName);

                    if (reference == null)
                    {
                        continue;
                    }

                    if (_query.DataSource.Metadata.GetEntitySyncPolicy(reference.Target) != EntitySyncPolicy.OnlineOnly && !_query.DataSource.Metadata.IsEntityOnDemandEnabled(_query.DataSource.EntityName))
                    {
                        continue;
                    }

                    if (sb.Length > 0)
                    {
                        sb.Append(",");
                    }

                    sb.Append(RemoteNaming.ToServerEntityReferenceName(refName));
                }
            }

            return sb.ToString();
        }

        private string GetFilter()
        {
            // Server supported ops:
            // eq - Equals
            // ne - Not equals 
            // eq_ic - Equals ignore case 
            // gt - Greater than 
            // ge - Greater than or equal 
            // lt - Less than 
            // le - Less than or equal 
            // like - Like(SQL syntax with % in the value)
            // like_ic - Like ignore case
            // and
            // or

            List<string> filters = new List<string>();
            string array = GetArrayFilter();
            if (array != null)
            {
                filters.Add(array);
            }
            string where = GetWhereExpression();
            if (where != null)
            {
                filters.Add(where);
            }

            if (filters.Count == 0)
            {
                return null;
            }

            if (filters.Count == 1)
            {
                return filters[0];
            }

            return "(" + string.Join(") and (", filters) + ")";
        }

        private void OutputCompare(StringBuilder sb, string attributeName, string op, object value)
        {
            AttributePathInfo attribute = AttributePathInfo.Get(_query.DataSource.Metadata, _query.DataSource.ProjectionName, _query.DataSource.EntityName, attributeName);
            CpiAttribute cpiAttribute = attribute.Attribute.CpiAttribute;

            sb.Append(GetFormattedAttributeName(attribute));
            sb.Append(' ');
            sb.Append(op);
            sb.Append(' ');

            if (value == null)
            {
                sb.Append("null");
            }
            else
            {
                switch (cpiAttribute.Datatype)
                {
                    case CpiDataType.Text:
                        sb.Append("'");
                        sb.Append(value);
                        sb.Append("'");
                        break;
                    case CpiDataType.Boolean:
                        sb.Append(value.ToString().ToLower());
                        break;
                    case CpiDataType.Enumeration:
                        sb.Append(IfsAppPrefix);
                        sb.Append(".");
                        sb.Append(_query.DataSource.ProjectionName);
                        sb.Append(".");
                        sb.Append(cpiAttribute.Subtype);
                        sb.Append("'");
                        sb.Append(value);
                        sb.Append("'");
                        break;
                    case CpiDataType.Timestamp:
                    case CpiDataType.TimestampUtc:
                        DateTime dt = (DateTime)value;
                        sb.Append(dt.ToString(ObjectConverter.DateTimeZoneFormat));
                        break;
                    default:
                        sb.Append(value);
                        break;
                }
            }
        }

        private string GetArrayFilter()
        {
            ArrayDataSource array = _query.DataSource as ArrayDataSource;
            if (array == null)
            {
                return null;
            }

            StringBuilder sb = new StringBuilder();

            foreach (Tuple<IMetaDataMember, object> keyValue in array.ArrayKey.Values)
            {
                if (sb.Length > 0)
                {
                    sb.Append(" and ");
                }

                CpiEntity entity = array.Metadata.FindEntity(_query.DataSource.ProjectionName, array.EntityName);
                KeyValuePair<string, CpiAttribute> attribute = entity.Attributes.FirstOrDefault(x => x.Key.Equals(keyValue.Item1.PropertyName));
                OutputCompare(sb, keyValue.Item1.PropertyName, "eq", keyValue.Item2);
            }

            return sb.ToString();
        }

        private string GetWhereExpression()
        {
            AttributeExpression filterExpression = _query.GetWhereExpression();
            if (filterExpression == null)
            {
                return null;
            }

            return GetExpressionFilter(filterExpression);
        }

        private string GetExpressionFilter(AttributeExpression exp)
        {
            switch (exp.Type)
            {
                case AttributeExpressionType.Compare:
                    return GetCompareExpressionFilter((AttributeCompareExpression)exp);
                case AttributeExpressionType.Logical:
                    return GetLogicExpressionFilter((AttributeLogicalExpression)exp);
                case AttributeExpressionType.Like:
                    return GetLikeExpressionFilter((AttributeLikeExpression)exp);
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        private string GetCompareExpressionFilter(AttributeCompareExpression exp)
        {
            string op;
            switch (exp.Comparison)
            {
                case AttributeCompareOperator.Equals:
                    op = "eq";
                    break;
                case AttributeCompareOperator.NotEquals:
                    op = "ne";
                    break;
                case AttributeCompareOperator.GreaterThan:
                    op = "gt";
                    break;
                case AttributeCompareOperator.GreaterThanOrEqual:
                    op = "ge";
                    break;
                case AttributeCompareOperator.LessThan:
                    op = "lt";
                    break;
                case AttributeCompareOperator.LessThanOrEqual:
                    op = "le";
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            StringBuilder sb = new StringBuilder();
            OutputCompare(sb, exp.AttributeName, op, exp.Value);
            return sb.ToString();
        }

        private string GetLogicExpressionFilter(AttributeLogicalExpression exp)
        {
            string left = GetExpressionFilter(exp.Left);
            string right = GetExpressionFilter(exp.Right);

            if (left == null)
                return right;
            if (right == null)
                return left;

            string join = exp.Operator == AttributeLogicalOperator.Or ? "or" : "and";
            return $"({left}) {join} ({right})";
        }

        private string GetLikeExpressionFilter(AttributeLikeExpression exp)
        {
            AttributePathInfo attribute = AttributePathInfo.Get(_query.DataSource.Metadata, _query.DataSource.ProjectionName, _query.DataSource.EntityName, exp.AttributeName);

            if (attribute == null)
            {
                return null;
            }

            StringBuilder sb = new StringBuilder();

            if (attribute.Member.ColumnType == typeof(string))
            {
                string term = exp.Value.Replace("'", "''");
                string searchQuery = $"(contains(tolower({GetFormattedAttributeName(attribute)}), tolower('{term}')))";
                sb.Append(searchQuery);
            }
            else
            {
                try
                {
                    object value = attribute.Member.ConvertValue(exp.Value.Trim('%'));
                    OutputCompare(sb, exp.AttributeName, "eq", value);
                }
                catch
                {
                    return null;
                }
            }

            return sb.ToString();
        }

        private string GetFormattedAttributeName(AttributePathInfo attribute)
        {
            string attributeName;

            if (attribute.RefName != null)
            {
                // If the attribute is coming from a related entity, we need to add / between the refname and the attribute name
                // eg. refname = "CustomerRef", attributename = "Name"  =>  CustomerRef/Name
                // https://learn.microsoft.com/en-us/odata/concepts/queryoptions-overview
                attributeName = attribute.RefName + "/" + RemoteNaming.ToServerColumnName(attribute.AttributeName); 
            }
            else
            {
                attributeName = RemoteNaming.ToServerColumnName(attribute.AttributeName);
            }

            return attributeName;
        }

        private string GetOrderBy()
        {
            StringBuilder sb = new StringBuilder();
            if (_query.Sorts.Count == 0)
            {
                foreach (IMetaDataMember member in _query.DataSource.Table.DataMembers)
                {
                    if (member.ServerPrimaryKey)
                    {
                        if (sb.Length > 0)
                        {
                            sb.Append(",");
                        }
                        sb.Append(member.PropertyName);
                    }
                }
                return sb.ToString();
            }

            foreach (AttributeSort attributeSort in _query.Sorts)
            {
                if (sb.Length > 0)
                {
                    sb.Append(",");
                }
                sb.Append(attributeSort.AttributeName);
                if (attributeSort.SortOrder == ESortOrder.Descending)
                {
                    sb.Append(" desc");
                }
            }
            return sb.ToString();
        }

        private string GetSelect()
        {
            if (_query.SelectAttributes == null || !_query.SelectAttributes.Any())
            {
                return null;
            }

            HashSet<string> selectAttributes = new HashSet<string>(_query.SelectAttributes);

            StringBuilder sb = new StringBuilder();
            foreach (IMetaDataMember member in _query.DataSource.Table.DataMembers)
            {
                if (member.PropertyName != "ObjKey" && selectAttributes.Contains(member.PropertyName))
                {
                    if (sb.Length > 0)
                    {
                        sb.Append(",");
                    }
                    sb.Append(member.PropertyName);
                }
            }

            return sb.ToString();
        }
    }
}
