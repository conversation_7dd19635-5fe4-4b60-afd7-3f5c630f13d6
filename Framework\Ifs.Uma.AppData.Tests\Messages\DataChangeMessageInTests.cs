﻿using System.Linq;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;
using MessageType = Ifs.Uma.AppData.Messages.MessageType;

namespace Ifs.Uma.AppData.Tests.Messages
{
    [TestFixture]
    public partial class DataChangeMessageInTests : DataContextTest<FwDataContext>
    {
        private const string InsertAppParamMessage = @"
{
  ""sync"": {
    ""fnd$mobile_client_param"": [
      {
        ""parameter"": ""Param1"",
        ""value"": ""Value1"",
        ""obj_id"": ""id1"",
        ""obj_key"": ""k1"",
        ""obj_version"": ""v1""
      }
    ]
  }
}";

        [Test]
        public void Insert()
        {
            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            ctx.ExecuteMessage(MessageType.INSERT, @"
{
  ""sync"": {
    ""fnd$mobile_client_param"": [
      {
        ""parameter"": ""Param1"",
        ""value"": ""Value1"",
        ""obj_id"": ""id1"",
        ""obj_key"": ""k1"",
        ""obj_version"": ""v1""
      },
      {
        ""parameter"": ""Param2"",
        ""value"": ""Value2"",
        ""obj_id"": ""o2"",
        ""obj_key"": ""k2"",
        ""obj_version"": ""v1""
      }
    ]
  }
}");

            MobileClientParam[] appParams = db.AppParameters.OrderBy(x => x.Parameter).ToArray();

            Assert.AreEqual(2, appParams.Length);
            Assert.AreEqual("Param1", appParams[0].Parameter);
            Assert.AreEqual("Value1", appParams[0].Value);
            Assert.AreEqual("Param2", appParams[1].Parameter);
            Assert.AreEqual("Value2", appParams[1].Value);
        }

        [Test]
        public void UpdateByObjKey()
        {
            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);
            
            ctx.ExecuteMessage(MessageType.INSERT, InsertAppParamMessage);           
            
            ctx.ExecuteMessage(MessageType.UPDATE, @"
{
  ""sync"": {
    ""fnd$mobile_client_param"": [
      {
        ""obj_key"": ""k1"",
        ""value"": ""Value2""
      }
    ]
  }
}");

            MobileClientParam[] appParams = db.AppParameters.OrderBy(x => x.Parameter).ToArray();

            Assert.AreEqual(1, appParams.Length);
            Assert.AreEqual("Param1", appParams[0].Parameter);
            Assert.AreEqual("Value2", appParams[0].Value);
        }

        [Test]
        public void UpdateByServerKey()
        {
            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            ctx.ExecuteMessage(MessageType.INSERT, InsertAppParamMessage);

            ctx.ExecuteMessage(MessageType.UPDATE, @"
{
  ""sync"": {
    ""fnd$mobile_client_param"": [
      {
        ""parameter"": ""Param1"",
        ""value"": ""Value2""
      }
    ]
  }
}");

            MobileClientParam[] appParams = db.AppParameters.OrderBy(x => x.Parameter).ToArray();

            Assert.AreEqual(1, appParams.Length);
            Assert.AreEqual("Param1", appParams[0].Parameter);
            Assert.AreEqual("Value2", appParams[0].Value);
        }

        [Test]
        public void DeleteByObjKey()
        {
            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            ctx.ExecuteMessage(MessageType.INSERT, InsertAppParamMessage);

            ctx.ExecuteMessage(MessageType.DELETE, @"
{
  ""sync"": {
    ""fnd$mobile_client_param"": [
      {
        ""obj_key"": ""k1""
      }
    ]
  }
}");

            MobileClientParam[] appParams = db.AppParameters.OrderBy(x => x.Parameter).ToArray();
            Assert.AreEqual(0, appParams.Length);
        }

        [Test]
        public void DeleteByServerKey()
        {
            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            ctx.ExecuteMessage(MessageType.INSERT, InsertAppParamMessage);

            ctx.ExecuteMessage(MessageType.DELETE, @"
{
  ""sync"": {
    ""fnd$mobile_client_param"": [
      {
        ""parameter"": ""Param1""
      }
    ]
  }
}");

            MobileClientParam[] appParams = db.AppParameters.OrderBy(x => x.Parameter).ToArray();
            Assert.AreEqual(0, appParams.Length);
        }

        [Test]
        public void IgnoreMessageIn()
        {
            FwDataContext db = CreateDataContext();
            MessageTestContext ctx = new MessageTestContext(db);

            IgnoreMessageIn ignoreMessageIn = new IgnoreMessageIn();
            ignoreMessageIn.TableName = "fnd$mobile_client_param";
            ignoreMessageIn.ObjKey = "k1";
            ignoreMessageIn.ObjVersion = "v1";
            db.IgnoreMessageIn.InsertOnSubmit(ignoreMessageIn);
            db.SubmitChanges(false);

            Assert.AreEqual(1, db.IgnoreMessageIn.Count());

            ctx.ExecuteMessage(MessageType.INSERT, InsertAppParamMessage);
            
            Assert.AreEqual(0, db.AppParameters.Count());
            Assert.AreEqual(0, db.IgnoreMessageIn.Count());
        }
    }
}
