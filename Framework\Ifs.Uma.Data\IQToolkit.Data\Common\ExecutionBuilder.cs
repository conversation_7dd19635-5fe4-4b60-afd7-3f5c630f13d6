﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using Ifs.Uma.Data;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Builds an execution plan for a query expression
    /// </summary>
    internal class ExecutionBuilder : DbExpressionVisitor
    {
        QueryPolicy policy;
        QueryLinguist linguist;
        Expression executor;
        Scope scope;
        bool isTop = true;
        MemberInfo receivingMember;
        int nReaders = 0;
        List<ParameterExpression> variables = new List<ParameterExpression>();
        List<Expression> initializers = new List<Expression>();
        Dictionary<string, Expression> variableMap = new Dictionary<string, Expression>();

        private ExecutionBuilder(QueryLinguist linguist, QueryPolicy policy, Expression executor)
        {
            this.linguist = linguist;
            this.policy = policy;
            this.executor = executor;
        }

        public static Expression Build(QueryLinguist linguist, QueryPolicy policy, Expression expression, Expression provider)
        {
            var executor = Expression.Parameter(typeof(QueryExecutor), "executor");
            var builder = new ExecutionBuilder(linguist, policy, executor);
            builder.variables.Add(executor);
            builder.initializers.Add(Expression.Call(Expression.Convert(provider, typeof(ICreateExecutor)), "CreateExecutor", null, null));
            var result = builder.Build(expression);
            return result;
        }

        private Expression Build(Expression expression)
        {
            expression = this.Visit(expression);
            expression = this.AddVariables(expression);
            return expression;
        }

        private Expression AddVariables(Expression expression)
        {
            // add variable assignments up front
            if (this.variables.Count > 0)
            {
                List<Expression> exprs = new List<Expression>();
                for (int i = 0, n = this.variables.Count; i < n; i++)
                {
                    exprs.Add(Expression.Assign(this.variables[i], this.initializers[i]));
                }

                exprs.Add(expression);

                expression = Expression.Block(variables, exprs);
            }

            return expression;
        }

        private Expression BuildInner(Expression expression)
        {
            var eb = new ExecutionBuilder(this.linguist, this.policy, this.executor);
            eb.scope = this.scope;
            eb.receivingMember = this.receivingMember;
            eb.nReaders = this.nReaders;
            eb.nLookup = this.nLookup;
            eb.variableMap = this.variableMap;
            return eb.Build(expression);
        }

        protected override MemberBinding VisitMemberBinding(MemberBinding node)
        {
            if (node == null) return null;
            var save = this.receivingMember;
            this.receivingMember = node.Member;
            var result = base.VisitMemberBinding(node);
            this.receivingMember = save;
            return result;
        }

        int nLookup = 0;

        private static Expression MakeJoinKey(IList<Expression> key)
        {
            if (key.Count == 1)
            {
                return key[0];
            }
            else
            {
                return Expression.New(
                    typeof(CompoundKey).GetTypeInfo().DeclaredConstructors.First(),
                    Expression.NewArrayInit(typeof(object), key.Select(k => (Expression)Expression.Convert(k, typeof(object))))
                    );
            }
        }

        protected override Expression VisitClientJoin(ClientJoinExpression node)
        {
            if (node == null) return null;
            // convert client join into a up-front lookup table builder & replace client-join in tree with lookup accessor

            // 1) lookup = query.Select(e => new KVP(key: inner, value: e)).ToLookup(kvp => kvp.Key, kvp => kvp.Value)
            Expression innerKey = MakeJoinKey(node.InnerKey);
            Expression outerKey = MakeJoinKey(node.OuterKey);

            ConstructorInfo kvpConstructor = typeof(KeyValuePair<,>).MakeGenericType(innerKey.Type, node.Projection.Projector.Type)
                .GetTypeInfo().DeclaredConstructors.Where(x => x.HasArgs(new Type[] { innerKey.Type, node.Projection.Projector.Type })).First();
            Expression constructKVPair = Expression.New(kvpConstructor, innerKey, node.Projection.Projector);
            ProjectionExpression newProjection = new ProjectionExpression(node.Projection.Select, constructKVPair);

            int iLookup = ++nLookup;
            Expression execution = this.ExecuteProjection(newProjection, false, false);

            ParameterExpression kvp = Expression.Parameter(constructKVPair.Type, "kvp");

            // filter out nulls
            if (node.Projection.Projector.GetDbNodeType() == DbExpressionType.OuterJoined)
            {
                LambdaExpression pred = Expression.Lambda(
                    Expression.PropertyOrField(kvp, "Value").NotEqual(TypeHelper.GetNullConstant(node.Projection.Projector.Type)),
                    kvp
                    );
                execution = Expression.Call(typeof(Enumerable), "Where", new Type[] { kvp.Type }, execution, pred);
            }

            // make lookup
            LambdaExpression keySelector = Expression.Lambda(Expression.PropertyOrField(kvp, "Key"), kvp);
            LambdaExpression elementSelector = Expression.Lambda(Expression.PropertyOrField(kvp, "Value"), kvp);
            Expression toLookup = Expression.Call(typeof(Enumerable), "ToLookup", new Type[] { kvp.Type, outerKey.Type, node.Projection.Projector.Type }, execution, keySelector, elementSelector);

            // 2) agg(lookup[outer])
            ParameterExpression lookup = Expression.Parameter(toLookup.Type, "lookup" + iLookup);
            PropertyInfo property = lookup.Type.GetRuntimeProperty("Item");
            Expression access = Expression.Call(lookup, property.GetMethod, this.Visit(outerKey));
            if (node.Projection.Aggregator != null)
            {
                // apply aggregator
                access = DbExpressionReplacer.Replace(node.Projection.Aggregator.Body, node.Projection.Aggregator.Parameters[0], access);
            }

            this.variables.Add(lookup);
            this.initializers.Add(toLookup);

            return access;
        }

        protected override Expression VisitProjection(ProjectionExpression node)
        {
            if (this.isTop)
            {
                this.isTop = false;
                return this.ExecuteProjection(node, this.scope != null, false);
            }
            else
            {
                return this.BuildInner(node);
            }
        }

        protected override Expression VisitMethodCall(MethodCallExpression node)
        {
            if (node.Method.DeclaringType == typeof(TableQueryable) &&
                node.Method.Name == nameof(TableQueryable.ToStream) && 
                isTop)
            {
                ProjectionExpression projection = (ProjectionExpression)node.Arguments[0];
                this.isTop = false;
                return ExecuteProjection(projection, this.scope != null, true);
            }
            else
            {
                return base.VisitMethodCall(node);
            }
        }

        protected virtual Expression Parameterize(Expression expression)
        {
            if (this.variableMap.Count > 0)
            {
                expression = VariableSubstitutor.Substitute(this.variableMap, expression);
            }
            return this.linguist.Parameterize(expression);
        }

        private Expression ExecuteProjection(ProjectionExpression projection, bool okayToDefer, bool stream)
        {
            // parameterize query
            projection = (ProjectionExpression)this.Parameterize(projection);

            if (this.scope != null)
            {
                // also convert references to outer alias to named values!  these become SQL parameters too
                projection = (ProjectionExpression)OuterParameterizer.Parameterize(this.scope.Alias, projection);
            }

            string commandText = this.linguist.Format(projection.Select);
            ReadOnlyCollection<NamedValueExpression> namedValues = NamedValueGatherer.Gather(projection.Select);
            QueryCommand command = new QueryCommand(commandText, namedValues.Select(v => new QueryParameter(v.Name, v.Type)));
            Expression[] values = namedValues.Select(v => Expression.Convert(this.Visit(v.Value), typeof(object))).ToArray();

            return this.ExecuteProjection(projection, okayToDefer, stream, command, values);
        }

        private Expression ExecuteProjection(ProjectionExpression projection, bool okayToDefer, bool stream, QueryCommand command, Expression[] values)
        {
            okayToDefer &= (this.receivingMember != null && this.policy.IsDeferLoaded(this.receivingMember));

            var saveScope = this.scope;
            ParameterExpression reader = Expression.Parameter(typeof(FieldReader), "r" + nReaders++);
            this.scope = new Scope(this.scope, reader, projection.Select.Alias, projection.Select.Columns);
            LambdaExpression projector = Expression.Lambda(this.Visit(projection.Projector), reader);
            this.scope = saveScope;

            bool defer = okayToDefer || stream;

            string methExecute = defer
                ? "ExecuteDeferred" 
                : "Execute";

            // call low-level execute directly on supplied DbQueryProvider
            Expression result = Expression.Call(this.executor, methExecute, new Type[] { projector.Body.Type },
                Expression.Constant(command),
                projector,
                Expression.NewArrayInit(typeof(object), values)
                );

            if (projection.Aggregator != null)
            {
                // apply aggregator
                result = DbExpressionReplacer.Replace(projection.Aggregator.Body, projection.Aggregator.Parameters[0], result);
            }
            return result;
        }

        protected override Expression VisitBatch(BatchExpression node)
        {
            if (node == null) return null;
            if (this.linguist.Language.AllowsMultipleCommands || !IsMultipleCommands(node.Operation.Body as CommandExpression))
            {
                return this.BuildExecuteBatch(node);
            }
            else
            {
                var source = this.Visit(node.Input);
                var op = this.Visit(node.Operation.Body);
                var fn = Expression.Lambda(op, node.Operation.Parameters[1]);
                return Expression.Call(this.GetType(), "Batch", new Type[] { TypeHelper.GetElementType(source.Type), node.Operation.Body.Type }, source, fn, node.Stream);
            }
        }

        protected virtual Expression BuildExecuteBatch(BatchExpression batch)
        {
            if (batch == null) throw new ArgumentNullException("batch");
            // parameterize query
            Expression operation = this.Parameterize(batch.Operation.Body);

            string commandText = this.linguist.Format(operation);
            var namedValues = NamedValueGatherer.Gather(operation);
            QueryCommand command = new QueryCommand(commandText, namedValues.Select(v => new QueryParameter(v.Name, v.Type)));
            Expression[] values = namedValues.Select(v => Expression.Convert(this.Visit(v.Value), typeof(object))).ToArray();

            Expression paramSets = Expression.Call(typeof(Enumerable), "Select", new Type[] { batch.Operation.Parameters[1].Type, typeof(object[]) },
                batch.Input,
                Expression.Lambda(Expression.NewArrayInit(typeof(object), values), new[] { batch.Operation.Parameters[1] })
                );

            Expression plan = null;

            ProjectionExpression projection = ProjectionFinder.FindProjection(operation);
            if (projection != null)
            {
                var saveScope = this.scope;
                ParameterExpression reader = Expression.Parameter(typeof(FieldReader), "r" + nReaders++);
                this.scope = new Scope(this.scope, reader, projection.Select.Alias, projection.Select.Columns);
                LambdaExpression projector = Expression.Lambda(this.Visit(projection.Projector), reader);
                this.scope = saveScope;

                command = new QueryCommand(command.CommandText, command.Parameters);

                plan = Expression.Call(this.executor, "ExecuteBatch", new Type[] { projector.Body.Type },
                    Expression.Constant(command),
                    paramSets,
                    projector,
                    batch.BatchSize,
                    batch.Stream
                    );
            }
            else
            {
                plan = Expression.Call(this.executor, "ExecuteBatch", null,
                    Expression.Constant(command),
                    paramSets,
                    batch.BatchSize,
                    batch.Stream
                    );
            }

            return plan;
        }

        protected override Expression VisitCommand(CommandExpression node)
        {
            if (this.linguist.Language.AllowsMultipleCommands || !IsMultipleCommands(node))
            {
                return this.BuildExecuteCommand(node);
            }
            else
            {
                return base.VisitCommand(node);
            }
        }

        protected virtual bool IsMultipleCommands(CommandExpression command)
        {
            if (command == null)
                return false;
            return true;
        }
        
        protected override Expression VisitBlock(BlockCommand node)
        {
            if (node == null) return null;
            return Expression.Block(VisitExpressionList(node.Commands));
        }

        protected override Expression VisitIf(IfCommand node)
        {
            if (node == null) return null;
            var test = 
                Expression.Condition(
                    node.Check, 
                    node.IfTrue, 
                    node.IfFalse != null 
                        ? node.IfFalse 
                        : node.IfTrue.Type == typeof(int) 
                            ? (Expression)Expression.Property(this.executor, "RowsAffected") 
                            : (Expression)Expression.Constant(TypeHelper.GetDefault(node.IfTrue.Type), node.IfTrue.Type)
                            );
            return this.Visit(test);
        }

        protected override Expression VisitFunction(FunctionExpression node)
        {
            if (this.linguist.Language.IsRowsAffectedExpressions(node))
            {
                return Expression.Property(this.executor, "RowsAffected");
            }
            return base.VisitFunction(node);
        }

        protected override Expression VisitExists(ExistsExpression node)
        {
            if (node == null) return null;
            // how did we get here? Translate exists into count query
            var newSelect = node.Select.SetColumns(
                new[] { new ColumnDeclaration("value", new AggregateExpression(typeof(int), "Count", null, false)) }
                );

            var projection = 
                new ProjectionExpression(
                    newSelect,
                    new ColumnExpression(typeof(int), newSelect.Alias, "value"),
                    Aggregator.GetAggregator(typeof(int), typeof(IEnumerable<int>))
                    );

            var expression = projection.GreaterThan(Expression.Constant(0));

            return this.Visit(expression);
        }

        protected override Expression VisitDeclaration(DeclarationCommand node)
        {
            if (node == null) return null;
            if (node.Source != null)
            {
                // make query that returns all these declared values as an object[]
                var projection = new ProjectionExpression(
                    node.Source,
                    Expression.NewArrayInit(
                        typeof(object),
                        node.Variables.Select(v => v.Expression.Type.GetTypeInfo().IsValueType
                            ? Expression.Convert(v.Expression, typeof(object))
                            : v.Expression).ToArray()
                        ),
                    Aggregator.GetAggregator(typeof(object[]), typeof(IEnumerable<object[]>))
                    );

                // create execution variable to hold the array of declared variables
                var vars = Expression.Parameter(typeof(object[]), "vars");
                this.variables.Add(vars);
                this.initializers.Add(Expression.Constant(null, typeof(object[])));

                // create subsitution for each variable (so it will find the variable value in the new vars array)
                for (int i = 0, n = node.Variables.Count; i < n; i++)
                {
                    var v = node.Variables[i];
                    NamedValueExpression nv = new NamedValueExpression(
                        v.Name,
                        Expression.Convert(Expression.ArrayIndex(vars, Expression.Constant(i)), v.Expression.Type)
                        );
                    this.variableMap.Add(v.Name, nv);
                }

                // make sure the execution of the select stuffs the results into the new vars array
                return Expression.Assign(vars, this.Visit(projection));
            }

            // probably bad if we get here since we must not allow mulitple commands
            throw new InvalidOperationException("Declaration query not allowed for this langauge");
        }
        
        protected virtual Expression BuildExecuteCommand(CommandExpression command)
        {
            // parameterize query
            var expression = this.Parameterize(command);

            string commandText = this.linguist.Format(expression);
            ReadOnlyCollection<NamedValueExpression> namedValues = NamedValueGatherer.Gather(expression);
            QueryCommand qc = new QueryCommand(commandText, namedValues.Select(v => new QueryParameter(v.Name, v.Type)));
            Expression[] values = namedValues.Select(v => Expression.Convert(this.Visit(v.Value), typeof(object))).ToArray();

            ProjectionExpression projection = ProjectionFinder.FindProjection(expression);
            if (projection != null)
            {
                return this.ExecuteProjection(projection, false, false, qc, values);
            }

            Expression plan = Expression.Call(this.executor, "ExecuteCommand", null,
                Expression.Constant(qc),
                Expression.NewArrayInit(typeof(object), values)
                );

            return plan;
        }

        protected override Expression VisitEntity(EntityExpression node)
        {
            if (node == null) return null;
            return Visit(node.Expression);
        }

        protected override Expression VisitOuterJoined(OuterJoinedExpression node)
        {
            if (node == null) return null;
            Expression expr = this.Visit(node.Expression);
            ColumnExpression column = (ColumnExpression)node.Test;
            ParameterExpression reader;
            int iOrdinal;
            if (this.scope.TryGetValue(column, out reader, out iOrdinal))
            {
                return Expression.Condition(
                    Expression.Call(reader, "IsDbNull", null, Expression.Constant(iOrdinal)),
                    Expression.Constant(TypeHelper.GetDefault(node.Type), node.Type),
                    expr
                    );
            }
            return expr;
        }

        protected override Expression VisitColumn(ColumnExpression node)
        {
            if (node == null) return null;
            ParameterExpression fieldReader;
            int iOrdinal;
            if (this.scope != null && this.scope.TryGetValue(node, out fieldReader, out iOrdinal))
            {
                MethodInfo method = FieldReader.GetReaderMethod(node.Type);
                return Expression.Call(fieldReader, method, Expression.Constant(iOrdinal));
            }
            else
            {
                System.Diagnostics.Debug.Assert(false, "column not in scope: " + node.ToString());
            }
            return node;
        }

        class Scope
        {
            Scope outer;
            ParameterExpression m_fieldReader;
            internal TableAlias Alias { get; private set; }
            Dictionary<string, int> nameMap;

            internal Scope(Scope outer, ParameterExpression fieldReader, TableAlias alias, IEnumerable<ColumnDeclaration> columns)
            {
                this.outer = outer;
                this.m_fieldReader = fieldReader;
                this.Alias = alias;
                this.nameMap = columns.Select((c, i) => new { c, i }).ToDictionary(x => x.c.Name, x => x.i);
            }

            internal bool TryGetValue(ColumnExpression column, out ParameterExpression fieldReader, out int ordinal)
            {
                for (Scope s = this; s != null; s = s.outer)
                {
                    if (column.Alias == s.Alias && this.nameMap.TryGetValue(column.Name, out ordinal))
                    {
                        fieldReader = this.m_fieldReader;
                        return true;
                    }
                }
                fieldReader = null;
                ordinal = 0;
                return false;
            }
        }

        /// <summary>
        /// columns referencing the outer alias are turned into special named-value parameters
        /// </summary>
        class OuterParameterizer : DbExpressionVisitor
        {
            int iParam;
            TableAlias outerAlias;
            Dictionary<ColumnExpression, NamedValueExpression> map = new Dictionary<ColumnExpression, NamedValueExpression>();

            internal static Expression Parameterize(TableAlias outerAlias, Expression expr)
            {
                OuterParameterizer op = new OuterParameterizer();
                op.outerAlias = outerAlias;
                return op.Visit(expr);
            }

            protected override Expression VisitProjection(ProjectionExpression node)
            {
                if (node == null) return null;
                SelectExpression select = VisitAndConvert(node.Select, "ExecutionBuilder.OuterParameterizer.VisitProjection");
                return node.Update(select, node.Projector, node.Aggregator);
            }

            protected override Expression VisitColumn(ColumnExpression column)
            {
                if (column == null) throw new ArgumentNullException("column");
                if (column.Alias == this.outerAlias)
                {
                    NamedValueExpression nv;
                    if (!this.map.TryGetValue(column, out nv)) 
                    {
                        nv = new NamedValueExpression("n" + (iParam++), column);
                        this.map.Add(column, nv);
                    }
                    return nv;
                }
                return column;
            }
        }

        //class ColumnGatherer : DbExpressionVisitor
        //{
        //    Dictionary<string, ColumnExpression> columns = new Dictionary<string, ColumnExpression>();

        //    internal static IEnumerable<ColumnExpression> Gather(Expression expression)
        //    {
        //        var gatherer = new ColumnGatherer();
        //        gatherer.Visit(expression);
        //        return gatherer.columns.Values;
        //    }

        //    protected override Expression VisitColumn(ColumnExpression column)
        //    {
        //        if (!this.columns.ContainsKey(column.Name))
        //        {
        //            this.columns.Add(column.Name, column);
        //        }
        //        return column;
        //    }
        //}

        class ProjectionFinder : DbExpressionVisitor
        {
            ProjectionExpression found = null;

            internal static ProjectionExpression FindProjection(Expression expression)
            {
                var finder = new ProjectionFinder();
                finder.Visit(expression);
                return finder.found;
            }

            protected override Expression VisitProjection(ProjectionExpression proj)
            {
                this.found = proj;
                return proj;
            }
        }

        class VariableSubstitutor : DbExpressionVisitor
        {
            Dictionary<string, Expression> map;

            private VariableSubstitutor(Dictionary<string, Expression> map)
            {
                this.map = map;
            }

            public static Expression Substitute(Dictionary<string, Expression> map, Expression expression)
            {
                return new VariableSubstitutor(map).Visit(expression);
            }

            protected override Expression VisitVariable(VariableExpression vex)
            {
                if (vex == null) throw new ArgumentNullException("vex");
                Expression sub;
                if (this.map.TryGetValue(vex.Name, out sub))
                {
                    return sub;
                }
                return vex;
            }
        }
    }
}