﻿using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Icons;

namespace Ifs.Uma.Framework.UI.RelatedPages
{
    public class RelatedPage : CpiCommandItem
    {
        public RelatedPage(ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, string projectionName, CpiCommand command)
            : base(commandExecutor, expressionRunner, projectionName, command)
        {
            if (Icon == null)
            {
                Icon = IconUtils.CaretForward;
            }
        }
    }
}
