﻿using System.Threading.Tasks;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Navigation;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Framework.UI.RelatedPages;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Workflow
{
    public sealed class WorkflowRelatedPage : RelatedPage
    {
        private readonly INavigator _navigator;
        private readonly CpiWorkflow _workflow;
        private readonly CpiWorkflowStep _step;
        private readonly int _sequence;

        public WorkflowRelatedPage(ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, INavigator navigator,
            string projectionName, CpiCommand command, CpiWorkflow workflow, CpiWorkflowStep step, int sequence)
            : base(commandExecutor, expressionRunner, projectionName, command)
        {
            _workflow = workflow;
            _step = step;
            _sequence = sequence;
            _navigator = navigator;

            IsEnabled = true;
        }

        protected override async Task OnExecuteAsync()
        {
            await base.OnExecuteAsync();
            await NavigateToWorkflowStep();
        }

        private async Task NavigateToWorkflowStep()
        {
            if (_step != null)
            {
                PageValues filter = PageValues.FromPrimaryKey(PageData.DefaultViewData?.Record?.ToPrimaryKey());
                MetadataPageNavParam navParam = MetadataPageNavParam.FromWorkflow(ProjectionName, _workflow.Name, _sequence, filter);
                await _navigator.NavigateToAsync(FrameworkLocations.MetadataPage, navParam);
            }
        }
    }
}
