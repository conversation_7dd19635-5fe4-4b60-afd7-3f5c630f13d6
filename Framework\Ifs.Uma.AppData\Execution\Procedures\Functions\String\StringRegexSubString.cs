﻿using System;
using System.Text.RegularExpressions;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringRegexSubString : StringFunction
    {
        public const string FunctionName = "RegexSubString";

        public StringRegexSubString()
            : base(FunctionName, 2, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();
            if (string.IsNullOrEmpty(regexPattern))
            {
                return null;
            }

            Match match = Regex.Match(stringToModify, regexPattern, RegexOptions.None, TimeSpan.FromSeconds(10));
            if (match.Success)
            {
                string matchValue = match.Groups[0].Value;
                return matchValue == string.Empty ? null : matchValue;
            }
            else
            {
                return null;
            }
        }
    }

    internal sealed class StringRegexSubString4 : StringFunction
    {
        public const string FunctionName = "RegexSubString";

        public StringRegexSubString4()
            : base(FunctionName, 4, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();
            if (string.IsNullOrEmpty(regexPattern))
            {
                return null;
            }

            long? position = parameters[2].GetInteger();
            if (!position.HasValue || position.Value < 0)
            {
                return null;
            }

            long? occurence = parameters[3].GetInteger();
            if (!occurence.HasValue || occurence.Value < 0)
            {
                return null;
            }

            Regex regex = new Regex(regexPattern, RegexOptions.None, TimeSpan.FromSeconds(10));
            MatchCollection matchCollection = regex.Matches(stringToModify, (int)position);

            if (matchCollection.Count > 0 && matchCollection.Count > (int)occurence.Value)
            {
                return matchCollection[(int)occurence.Value].Value;
            }
            else
            {
                return null;
            }
        }
    }

    internal sealed class StringRegexSubString5 : StringFunction
    {
        public const string FunctionName = "RegexSubString";

        public StringRegexSubString5()
            : base(FunctionName, 5, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            string regexPattern = parameters[1].GetString();
            if (string.IsNullOrEmpty(regexPattern))
            {
                return null;
            }

            long? position = parameters[2].GetInteger();
            if (!position.HasValue || position.Value < 0)
            {
                return null;
            }

            long? occurence = parameters[3].GetInteger();
            if (!occurence.HasValue || occurence.Value < 0)
            {
                return null;
            }

            string regexOptionsParam = parameters[4].GetString();
            RegexOptions regexOptions = StringToRegexOptions(regexOptionsParam);

            Regex regex = new Regex(regexPattern, regexOptions, TimeSpan.FromSeconds(10));
            MatchCollection matchCollection = regex.Matches(stringToModify, (int)position);

            if (matchCollection.Count > 0 && matchCollection.Count > (int)occurence.Value)
            {
                return matchCollection[(int)occurence.Value].Value;
            }
            else
            {
                return null;
            }
        }
    }
}
