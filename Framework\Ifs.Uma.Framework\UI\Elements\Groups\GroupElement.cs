﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Address;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.MSTeams;
using Ifs.Uma.Framework.UI.Forms;
using Ifs.Uma.Framework.UI.Lookups;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Forms;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Elements.Groups
{
    public sealed class GroupElement : ElementBase
    {
        private readonly IMetadata _metadata;
        private readonly ILovService _lovService;
        private readonly IFileService _fileService;
        private readonly IAppParameters _appParameters;
        private readonly ILogger _logger;
        private readonly IDataHandler _dataHandler;
        private readonly IAddressHandler _addressHandler;

        protected override BindingType BindingPropertyType => BindingType.Reference;

        private CpiGroup _group;
        private ItemGroup _fieldGroup;

        public string Name => _group?.Name;

        private Form _form;
        public Form Form
        {
            get => _form;
            private set
            {
                if (_form != value)
                {
                    _form = value;
                    OnPropertyChanged(nameof(Form));
                }
            }
        }

        private AddressPresentation[] _addressPresentations;
        public AddressPresentation[] AddressPresentations
        {
            get => _addressPresentations;
            private set
            {
                if (_addressPresentations != value)
                {
                    _addressPresentations = value;
                    UpdateAddressData();
                }
            }
        }

        private readonly List<CpiBadge> _badges = new List<CpiBadge>();
        private readonly List<CpiAddress> _cpiAddresses = new List<CpiAddress>();
        private readonly List<LovField> _lovFields = new List<LovField>();
        private readonly List<ComputedField> _computedFields = new List<ComputedField>();
        private readonly List<ItemPickerField> _itemPickerFields = new List<ItemPickerField>();
        private readonly List<Field> _contactWidgetFields = new List<Field>();
        private readonly List<DateTimeFieldUtc> _dateTimeFieldUtcFields = new List<DateTimeFieldUtc>();

        public GroupElement(IMetadata metadata, ILovService lovService, IFileService fileService,
            IAppParameters appParameters, ILogger logger, IDataHandler dataHandler, IAddressHandler addressHandler)
        {
            _metadata = metadata;
            _lovService = lovService;
            _fileService = fileService;
            _appParameters = appParameters;
            _logger = logger;
            _dataHandler = dataHandler;
            _addressHandler = addressHandler;

            AlwaysLoad = true;
        }

        public async Task LoadAddressPresentation()
        {
            AddressPresentations = await _addressHandler.GetAllAddressPresentations();
        }

        protected override bool OnInitialize()
        {
            _group = _metadata.FindGroup(ProjectionName, Content.Group);
            _fieldGroup = _group != null ? new ItemGroup(_group.Name, null) : null;
            Label = _group?.Label;
            return _group != null;
        }

        protected override bool OnLoad()
        {
            _ = LoadAddressPresentation();
            SetupForm();
            return true;
        }

        private void SetupForm()
        {
            _badges.Clear();
            _lovFields.Clear();
            _cpiAddresses.Clear();

            FormBuilder<GroupElement> fb = new FormBuilder<GroupElement>(_metadata, this, _dataHandler);

            int i = 0;
            foreach (CpiFieldContent groupContent in _group.Content.Where(x => x.Field != null))
            {
                i++;

                CpiField fieldDef = groupContent.Field;

                Field field = fb.AddField(i, ProjectionName, fieldDef, fieldDef.Attribute == null ? null : GetAttribute(fieldDef.Attribute));
                field.Tag = fieldDef;
                field.Group = _fieldGroup;

                if (field is MediaPickerField imagePickerField)
                {
                    imagePickerField.FileService = _fileService;
                    imagePickerField.ImageOptions = new ImageOptions(_appParameters.GetPictureMaxDimension(), _appParameters.GetPictureMaxBytes(), _appParameters.IsExistingDeviceMediaAllowed());
                    imagePickerField.ValueType = FilePickerValueType.ByteArray;

                    if (_appParameters.IsEnhancedMediaEnabled())
                    {
                        imagePickerField.MaxVideoBytes = _appParameters.GetVideoMaxMb() != 0 ? Utils.ConvertMbToBytes(_appParameters.GetVideoMaxMb()) : Utils.ConvertMbToBytes(MediaHandler.DefaultVideoMaxMb);
                    }
                }
                else if (field is LovField lookupField)
                {
                    lookupField.RefName = fieldDef.Update?.Item;

                    CpiExpression hideKeyExpression = fieldDef.View?.OfflineHideKey ?? fieldDef.View?.HideKey;
                    bool hideKey = hideKeyExpression?.QuickCheck() ?? false;
                    if (!hideKey)
                    {
                        lookupField.Key = "${" + fieldDef.Attribute + "}";
                    }
                    lookupField.Description = fieldDef.View?.Description;
                    lookupField.ExpressionRunner = ExpressionRunner;
                    lookupField.Command = Command.FromAsyncMethod(() => OpenLookup(lookupField, fieldDef));
                    _lovFields.Add(lookupField);
                }
                else if (field is ItemPickerField itemPickerField)
                {
                    if (!string.IsNullOrEmpty(fieldDef.ItemPicker.DatasourceEntitySet) || !string.IsNullOrEmpty(fieldDef.ItemPicker.DatasourceFunction))
                    {
                        itemPickerField.Metadata = _metadata;
                        itemPickerField.DataHandler = _dataHandler;
                        itemPickerField.Logger = _logger;
                        itemPickerField.ExpressionRunner = ExpressionRunner;
                        itemPickerField.CpiItemPicker = fieldDef.ItemPicker;
                    }

                    _itemPickerFields.Add(itemPickerField);
                }
                else if (field is BadgeField badgeField)
                {
                    CpiBadge badge = new CpiBadge(_metadata, ExpressionRunner, ProjectionName, fieldDef);
                    _badges.Add(badge);
                    badgeField.Badge = badge;
                }
                else if (field is ComputedField computedField)
                {
                    ComputedString computedString = new ComputedString(_logger, _dataHandler, PageData?.BackgroundTasks, fieldDef.Expression);
                    computedString.Update(Record);
                    computedField.ComputedString = computedString;
                    _computedFields.Add(computedField);
                }
                else if (field is AddressField addressField)
                {
                    CpiAddress cpiAddress = new CpiAddress(fieldDef.AddressField);
                    cpiAddress.Update(ViewData, AddressPresentations);
                    addressField.Address = cpiAddress;
                    _cpiAddresses.Add(cpiAddress);
                }
                else if (fieldDef.ContactWidget != null)
                {
                    field.ContactWidget = new FwContactWidget(fieldDef.ContactWidget, _logger, _metadata, _dataHandler, ExpressionRunner, PageData?.BackgroundTasks);
                    field.InteractionFinished += Field_InteractionFinished;
                    _contactWidgetFields.Add(field);
                }
                else if (field is ComboField comboField)
                {
                    if (fieldDef.EnumerationFilter != null)
                    {
                        CpiFilter[] cpiFilters = fieldDef.EnumerationFilter;
                        foreach (CpiFilter filter in cpiFilters)
                        {
                            bool enabledValue = filter.Enabled.QuickCheck(false);

                            if (enabledValue && ExpressionRunner.RunCheck(filter.Case, ViewData, false))
                            {
                                // The first filter which evaluates to true will be used, others will be ignored.
                                SelectableItem<object>[] enumerationValues = comboField.ItemsSource as SelectableItem<object>[];
                                string[] displayValues = filter.Value.Split(';');

                                if (displayValues[0].StartsWith("CfEnum_"))
                                {
                                    // Extract the Custom Enum
                                    string[] cfEnumValues = displayValues.Where(value => value.StartsWith("CfEnum_")).ToArray();
                                    comboField.ItemsSource = enumerationValues?.Where(x => x.Value != null && cfEnumValues.Contains("CfEnum_" + x.Value.ToString())).ToArray();
                                }
                                else
                                {
                                    comboField.ItemsSource = enumerationValues?.Where(x => x.Value != null && displayValues.Contains(x.Value.ToString())).ToArray();
                                }
                                break;
                            }
                        }
                    }
                }
                else if (field is DateTimeFieldUtc utcField && fieldDef.DisplayTimeZones != null && fieldDef.DisplayTimeZones.Any())
                {
                    utcField.DisplayTimeZonesAttributes = fieldDef.DisplayTimeZones;
                    _dateTimeFieldUtcFields.Add(utcField);
                }
#if LIDAR_SERVICE
                else if (fieldDef.LengthMeasure != null && DeviceInfo.OperatingSystem == Utility.OperatingSystem.iOS && PlatformServices.LidarService != null && PlatformServices.LidarService.IsLidarCapableDevice())
                {
                    field.ValueChanged += Field_ValueChanged;
                }
#endif

                if (fieldDef.ValidateCommand != null)
                {
                    field.ValidationRequired += Field_ValidationRequired;
                }

                if (fieldDef.CustomValidations != null)
                {
                    field.HandleFieldCustomValidation += Field_CustomerValidationRequired;
                }

                if (fieldDef.DefaultValues != null)
                {
                    field.HandleDefaultValueForField += Field_DefaultValueRequired;
                }
            }

            fb.AutoSetDefaultLayouts();

            Form = fb.Form;
        }

        #region IDisposable Support

        protected override void Dispose(bool disposing)
        {
            if (disposing) 
            {
                foreach (Field field in Form.AllFields)
                {
                    field.ValidationRequired -= Field_ValidationRequired;
                    field.HandleFieldCustomValidation -= Field_CustomerValidationRequired;
                    field.HandleDefaultValueForField -= Field_DefaultValueRequired;
                    field.InteractionFinished -= Field_InteractionFinished;
                    field.ValueChanged -= Field_ValueChanged;
                }

                _badges.Clear();
                _lovFields.Clear();
                _cpiAddresses.Clear();
                _computedFields.Clear();
                _itemPickerFields.Clear();
                _contactWidgetFields.Clear();
                _dateTimeFieldUtcFields.Clear();
            }
            base.Dispose(disposing);
        }

        #endregion

        private void Field_DefaultValueRequired(CpiField cpiField, Field field)
        {
            foreach (CpiDefaultValue defaultValue in cpiField.DefaultValues)
            {
                object value;
                if (defaultValue.Enable && RunCheck(defaultValue.Case, false))
                {
                    if (defaultValue.Value.StartsWith(ViewData.RecordPrefix) || defaultValue.Value.StartsWith(ViewData.ParentPrefix) || defaultValue.Value.StartsWith("#"))
                    {
                        ViewData.TryGetValue(defaultValue.Value, out value);
                    }
                    else if (defaultValue.Value.StartsWith("${"))
                    {
                        value = ExpressionRunner.InterpolateString(defaultValue.Value, Record);
                    }
                    else
                    {
                        value = defaultValue.Value;
                    }

                    if (cpiField.Datatype == CpiDataType.Timestamp)
                    {
                        field.Value = value;
                    }
                    else if (cpiField.Datatype == CpiDataType.Boolean)
                    {
                        field.Value = string.Equals((string)value, "true", StringComparison.OrdinalIgnoreCase);
                    }
                    else if (cpiField.Datatype == CpiDataType.Integer || cpiField.Datatype == CpiDataType.Number)
                    {
                        DataTable table = new DataTable();
                        field.Value = table.Compute((string)value, string.Empty);
                    }
                    else
                    {
                        field.Value = value;
                    }
                }
            }
        }

        private bool Field_CustomerValidationRequired(CpiField cpiField, Field field)
        {        
            foreach (CpiCustomValidation validation in cpiField.CustomValidations)
            {
                if (validation.Enable && RunCheck(validation.Case, false))
                {
                    if (!RunCheck(validation.ValidationLogic, true))
                    {
                        Form.SetFieldInvalid(field, validation.Message);
                        return false;
                    }
                }
            }
            return true;
        }

        private void Field_InteractionFinished(object sender, System.EventArgs e)
        {
            Field field = (Field)sender;
            UpdateContactWidget(field);
        }

        private void UpdateContactWidget(Field field)
        {
            if (field != null && field.ContactWidget is FwContactWidget fwcw)
            {
                CpiField fieldDef = (CpiField)field.Tag;
                string columnName = fwcw.CpiContactWidget?.Key ?? fieldDef.Attribute;

                if (ViewData.TryGetValue(columnName, out object result))
                {
                    fwcw.Id = result?.ToString();
                }

                fwcw.LoadDetails();
            }
        }

        private void UpdateDateTimeUtcFields(DateTimeFieldUtc field)
        {
            if (field != null && field.DisplayTimeZonesAttributes.Any())
            {
                field.DisplayTimeZonesValues = new List<string>();

                foreach (CpiDisplayTimeZone tz in field.DisplayTimeZonesAttributes)
                {
                    if (tz.TimeZoneAttribute != null && ViewData.TryGetValue(tz.TimeZoneAttribute, out object result))
                    {
                        field.DisplayTimeZonesValues.Add(result?.ToString());
                        if (field.DisplayTimeZonesValues.Count == 1)
                        {
                            field.OnListUpdated();
                        }                        
                    }
                    else if (tz.TimeZoneAttribute == null)
                    {
                        field.IgnoreFirstTimeZone = true;
                    }
                }
            }
        }

#if SIGNATURE_SERVICE
        public bool HasSignatureChanges()
        {
            return RunCheck(_group.DigitalSignature?.HashContent, false);
        }

        public string GetSignatureEntity()
        {
            return _group.DigitalSignature?.Entity;
        }
#endif

        private void Field_ValidationRequired(object sender, System.EventArgs e)
        {
            Field field = (Field)sender;
            CpiField fieldDef = (CpiField)field.Tag;
            ExecuteBackgroundCommand(fieldDef.ValidateCommand);
        }

        private async Task OpenLookup(LookupField field, CpiField fieldDef)
        {
            try
            {
                bool selected = await _lovService.OpenLovAsync(field, fieldDef, ViewData);

                if (selected)
                {
                    field.NotifyValidationRequired();
                }
            }
            catch (Exception ex)
            {
                await HandleException(ex);
            }
        }

        protected override void OnRecordLoaded()
        {
            base.OnRecordLoaded();

            foreach (ComputedField field in _computedFields)
            {
                field.ComputedString.Update(Record);
            }

            foreach (Field cwField in _contactWidgetFields)
            {
                UpdateContactWidget(cwField);
            }

            foreach (DateTimeFieldUtc utcField in _dateTimeFieldUtcFields)
            {
                UpdateDateTimeUtcFields(utcField);
            }
        }

        protected override void OnViewDataChanged(ViewData oldValue, ViewData newValue)
        {
            if (_group.SelectAttributes != null && newValue?.Record != null)
            {
                foreach (string attribute in _group.SelectAttributes)
                {
                    AttributePath path = AttributePath.Create(attribute);
                    if (!string.IsNullOrEmpty(path?.RefName))
                    {
                        // Preload this reference so it is available to the command
                        newValue.Record.GetReference(path.RefName);
                    }
                }
            }

            foreach (Field field in _contactWidgetFields)
            {
                if (field.ContactWidget is FwContactWidget fwcw)
                {
                    fwcw.ViewData = newValue;
                }
            }

            base.OnViewDataChanged(oldValue, newValue);
        }

        protected override void OnDataChanged()
        {
            if (CanEdit())
            {
                Form.EditState = Record.IsNew() ? FieldEditState.Insert : FieldEditState.Update;
            }
            else
            {
                Form.EditState = FieldEditState.ReadOnly;
            }

            foreach (Field field in Form.AllFields)
            {
                if (field.Tag is CpiField fieldDef)
                {
#if LIDAR_SERVICE
                    // LiDAR fields should be setup at earliest. Otherwise when UI recreate based on visibility condition, there are no any LiDAR data set to the field
                    // Hence LiDAR button in the field doesn't appear.
                    if (fieldDef.LengthMeasure != null && DeviceInfo.OperatingSystem == Utility.OperatingSystem.iOS && PlatformServices.LidarService != null && PlatformServices.LidarService.IsLidarCapableDevice())
                    {
                        field.IsLidarEnabled = RunCheck(fieldDef.LengthMeasure.Enabled, true);
                        if (field.IsLidarEnabled)
                        {
                            if (!string.IsNullOrEmpty(fieldDef.LengthMeasure.MeasureUnit))
                            {
                                field.LidarMeasureUnit = InterpolateString(fieldDef.LengthMeasure.MeasureUnit, ViewData?.Record);
                            }

                            if (!string.IsNullOrEmpty(fieldDef.LengthMeasure.MediaAttribute))
                            {
                                field.LidarMediaAttribute = fieldDef.LengthMeasure.MediaAttribute;
                            }
                        }
                    }
#endif

                    field.IsVisible = fieldDef.Visible != null ? RunCheck(fieldDef.OfflineVisible ?? fieldDef.Visible, true) : RunCheck(fieldDef.OfflineVisible ?? fieldDef.ColumnVisible, true);
                    field.Label = InterpolateString(fieldDef.Label, ViewData?.Record);

                    if (field.IsVisible)
                    {
                        field.IsRequired = RunCheck(fieldDef.OfflineRequired ?? fieldDef.Required, false);
                        field.IsReadOnly = !RunCheck(fieldDef.OfflineEditable ?? fieldDef.Editable, fieldDef.Control != CpiControlType.Static);
                    }

                    field.HasInitialFocus = field.IsVisible && !field.IsReadOnly && RunCheck(fieldDef.InitialFocus, false);
                }
            }

            foreach (CpiBadge badge in _badges)
            {
                badge.UpdateStates(ViewData);
            }

            foreach (LovField lovField in _lovFields)
            {
                lovField.UpdateStates(Record);
            }

            foreach (CpiAddress cpiAddress in _cpiAddresses)
            {
                cpiAddress.Update(ViewData, AddressPresentations);
            }

            foreach (ItemPickerField itemPickerField in _itemPickerFields)
            {
                _ = itemPickerField.UpdateStates(ViewData);
            }

            foreach (ComputedField field in _computedFields)
            {
                field.ComputedString.UpdateString();
            }

            foreach (Field cwField in _contactWidgetFields)
            {
                FwContactWidget contactWidget = cwField.ContactWidget as FwContactWidget;
                cwField.ContactWidget.IsEnabled = !string.IsNullOrEmpty(cwField.Id) && RunCheck(contactWidget.CpiContactWidget.OfflineEnabled ?? contactWidget.CpiContactWidget.Enabled, true);
                cwField.ContactWidget.IsMsTeamsEnabled = _appParameters.IsMsTeamsEnabled();
                MSAccessTokenProvider.ClientId = _appParameters.GetMsTeamsClientId();
            }
        }

        private void UpdateAddressData()
        {
            foreach (CpiAddress cpiAddress in _cpiAddresses)
            {
                cpiAddress.Update(ViewData, AddressPresentations);
            }
        }

        protected override bool OnValidate()
        {
            return Form.ValidateRequiredFields() && Form.ValidateFieldsWithCustomValidations();
        }

        protected override void OnClearValidations()
        {
            Form.ClearValidation();
        }

        protected override bool CalculateIsVisible()
        {
            CpiExpression visible = Content?.Override?.OfflineVisible ?? Content?.Override?.Visible;
            if (visible != null)
            {
                return base.CalculateIsVisible();
            }

            return RunCheck(_group.OfflineVisible ?? _group.Visible, true);
        }

        protected override bool OnNotifyFieldInvalid(string fieldName, string message)
        {
            Field[] fields = Form.AllFields
                .Where(x => x.Tag is CpiField field && field.Attribute == fieldName)
                .ToArray();

            if (fields.Length > 0)
            {
                foreach (Field field in fields)
                {
                    Form.SetFieldInvalid(field, message);
                }

                return true;
            }

            return false;
        }

        protected override void OnGetSelectAttributes(ICollection<string> attributes)
        {
            base.OnGetSelectAttributes(attributes);

            AttributeFinder.FindInGroup(attributes, _metadata, ProjectionName, _group);

            foreach (CpiFieldContent groupContent in _group.Content.Where(x => x.Field != null))
            {
                CpiField fieldDef = groupContent.Field;
                if (fieldDef.Datatype == CpiDataType.Lookup || fieldDef.Control == CpiControlType.Lookup)
                {
                    if (fieldDef.Lov?.Selector != null && fieldDef.Update?.Item != null)
                    {
                        CpiSelector selector = _metadata.FindSelector(ProjectionName, fieldDef.Lov.Selector);
                        if (selector?.SelectAttributes != null)
                        {
                            foreach (string attribute in selector.SelectAttributes)
                            {
                                attributes.Add(fieldDef.Update.Item + "." + attribute);
                            }
                        }
                    }
                }
            }
        }

        private void Field_ValueChanged(object sender, ValueChangedEventArgs e)
        {
            Field field = (Field)sender;

            if (field.IsLidarMediaAttributeSet())
            {
                Record?.Assign(field.LidarMediaAttribute, field.GetLidarImage());
            }
        }
    }
}
