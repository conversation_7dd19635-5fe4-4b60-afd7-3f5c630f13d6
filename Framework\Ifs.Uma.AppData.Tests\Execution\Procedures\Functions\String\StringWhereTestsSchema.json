{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<GetCustomers>": {"name": "GetCustomers", "type": "Function", "params": [{"name": "MySearch", "dataType": "Text"}], "layers": [{"vars": [{"name": "Var1"}, {"name": "Record"}], "execute": [{"call": {"method": "for", "args": {"entity": "Customers", "where": {"method": ["String.Like", [{"var": "CustomerNo"}, {"var": "MySearch"}]]}}}, "assign": "Record", "result": {"TRUE": [{"call": {"method": "set", "args": {"expression": {"+": [{"+": [{"var": "Var1"}, "#"]}, {"var": "Record.CustomerNo"}]}}}, "assign": "Var1"}]}}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}}}}