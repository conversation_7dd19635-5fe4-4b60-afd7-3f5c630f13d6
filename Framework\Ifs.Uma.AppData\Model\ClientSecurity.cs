﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_" + DbTableName, 
        Columns = nameof(Projection) + "," + nameof(GrantType) + "," + nameof(ObjectName),
        Unique = true)]
    public class ClientSecurity : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "mobile_client_security";

        [Column(ServerPrimaryKey = true)]
        public string Projection { get; set; }

        [Column(ServerPrimaryKey = true)]
        public string GrantType { get; set; }

        [Column(ServerPrimaryKey = true)]
        public string ObjectName { get; set; }

        [Column]
        public string AppName { get; set; }

        [Column]
        public string AppVersion { get; set; }

        [Column]
        public bool Granted { get; set; }

        [Column]
        public string Activity { get; set; }

        public ClientSecurity()
            : base(DbTableName)
        {
        }
    }
}
