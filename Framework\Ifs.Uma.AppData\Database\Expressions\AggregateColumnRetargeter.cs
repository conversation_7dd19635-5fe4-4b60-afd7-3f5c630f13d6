﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Database.Expressions
{
    internal sealed class AggregateColumnRetargeter : IfsExpressionVisitor
    {
        private readonly IReadOnlyList<EntityQueryColumn> _queryColumns;
        private readonly IReadOnlyList<ISelectColumnSpec> _columns;
        private readonly string _aggregateTableAlias;

        public static Expression Retarget(Expression expression, IReadOnlyList<EntityQueryColumn> queryColumns, ISelectSpec spec, string aggregateTableAlias)
        {
            AggregateColumnRetargeter visitor = new AggregateColumnRetargeter(queryColumns, spec, aggregateTableAlias);
            return visitor.Visit(expression);
        }

        private AggregateColumnRetargeter(IReadOnlyList<EntityQueryColumn> queryColumns, ISelectSpec spec, string aggregateTableAlias)
        {
            _queryColumns = queryColumns;
            _columns = spec.Columns.ToArray();
            _aggregateTableAlias = aggregateTableAlias;
        }

        protected internal override Expression VisitAttributeAccessExpression(AttributeAccessExpression exp)
        {
            EntityQueryColumn queryColumn = _queryColumns.First(x => x.RefName == exp.Attribute.RefName && x.Member.PropertyName == exp.Attribute.AttributeName);
            if (queryColumn != null)
            {
                ISelectColumnSpec subColumnSpec = _columns[queryColumn.SelectIndex];
                if (_aggregateTableAlias != null)
                {
                    string name = subColumnSpec.ColumnAlias ?? subColumnSpec.ColumnName;
                    return IfsExpression.DbColumnSpec(ColumnSpec.CreateSelect(name, _aggregateTableAlias));
                }
                else
                {
                    return IfsExpression.DbColumnSpec(ColumnSpec.CreateSelect(subColumnSpec.ColumnName, subColumnSpec.TableAlias));
                }
            }
            else
            {
                return base.VisitAttributeAccessExpression(exp);
            }
        }
    }
}
