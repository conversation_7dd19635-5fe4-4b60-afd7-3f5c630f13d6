﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection;

namespace Ifs.Uma.AppData.Expressions
{
    public enum IfsApiMethodName
    {
        Datediff,
        Match,
        Contains,
        Resolvecsv
    }

    public enum IfsApiMethodHandleType
    {
        String,
        Date,
        Aggregate,
        Number
    }

    public abstract partial class IfsApiExpression : IfsExpression
    {
        protected abstract MethodInfo LogicMethodInfo { get; }
        public override IfsExpressionType IfsNodeType => IfsExpressionType.ApiMethod;
        public abstract IfsApiMethodName ApiMethodName { get; }
        public abstract IfsApiMethodHandleType ApiMethodHandleType { get; }
        protected List<Expression> Parameters { get; set; }

        public virtual Expression CreateCallableExpression()
        {
            NewArrayExpression parameterList = NewArrayInit(typeof(DynamicValue), Parameters);

            Type listType = typeof(List<DynamicValue>);
            ConstructorInfo listConstructor = listType.GetConstructor(new[] { typeof(IEnumerable<DynamicValue>) });
            NewExpression listInit = New(listConstructor, parameterList);

            return Call(LogicMethodInfo, listInit);
        }
    }
}
