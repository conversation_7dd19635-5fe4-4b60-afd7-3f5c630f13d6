﻿using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Convert
{
    internal sealed class ConvertToInteger : FwFunction
    {
        public const string FunctionNamespace = "Convert";
        public const string FunctionName = "ToInteger";
        public ConvertToInteger()
            : base(FunctionNamespace, FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return parameters[0].GetInteger();
        }
    }
}
