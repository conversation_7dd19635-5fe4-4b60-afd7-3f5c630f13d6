﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Permissions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Metadata.Navigation;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Navigation
{
    public class HomePageItems : IHomePageItems
    {
        public ViewableCollection<AppMenuItem> DashboardItems { get; } = new ViewableCollection<AppMenuItem>();
        public ViewableCollection<AppMenuItem> AppMenuItems { get; } = new ViewableCollection<AppMenuItem>();
        private readonly ViewableCollection<AppMenuItem> _items = new ViewableCollection<AppMenuItem>();

        private readonly IMetadata _metadata;
        private readonly IAppPermissions _permissions;
        private readonly IDataHandler _dataHandler;
        private readonly ISettings _settings;

        private readonly CancellingUpdater _updater;
        private readonly CancellingUpdater _countersUpdater;
        private DataChangeSet _updateChangeSet;

        public event EventHandler<EventArgs> Loaded;
        public event EventHandler<EventArgs> CountersUpdated;
        public event EventHandler<EventArgs> StatusIndicatorChanged;

        private bool _loadingCompleted;

        public HomePageItems(IMetadata metadata, IAppPermissions permissions, IDataHandler dataHandler, ISettings settings)
        {
            _metadata = metadata;
            _permissions = permissions;
            _dataHandler = dataHandler;
            _settings = settings;

            _updater = new CancellingUpdater(LoadHomePageItems);
            _countersUpdater = new CancellingUpdater(UpdateCounters);
        }

        private bool ShouldReloadMenuItems(DataChangeSet changeSet = null)
        {
            foreach (MenuItemData menuItemData in _metadata.NavigationData.MenuItems)
            {
                if (_permissions.IsNavigatorEntryGranted(menuItemData.ProjectionName, menuItemData.NavigatorEntry) && menuItemData.VisibleFunction != null)
                {
                    FunctionInfo function = FunctionInfo.Get(_metadata, menuItemData.ProjectionName, menuItemData.VisibleFunction);

                    if (function != null && changeSet?.EffectedTables != null)
                    {
                        foreach (IMetaTable effectedTable in changeSet?.EffectedTables)
                        {
                            if (function.Function.LuDependencies.Any(x => x.ToLowerCaseUnderscore() == effectedTable.TableName))
                            {
                                return true;
                            }
                        }
                    }
                }
            }

            return false;
        }

        private async Task LoadMenuItems(CancellationToken token)
        {
            int i = 0;

            UmaColor[] colors = UmaColors.Complementary;

            foreach (MenuItemData menuItemData in _metadata.NavigationData.MenuItems)
            {
                if (token.IsCancellationRequested)
                {
                    _loadingCompleted = false;
                    _items.Clear();
                    return;
                }

                if (_permissions.IsNavigatorEntryGranted(menuItemData.ProjectionName, menuItemData.NavigatorEntry))
                {
                    if (menuItemData.VisibleFunction != null)
                    {
                        FunctionInfo function = FunctionInfo.Get(_metadata, menuItemData.ProjectionName, menuItemData.VisibleFunction);

                        if (function != null)
                        {
                            ExecuteResult result = await _dataHandler.PerformFunctionAsync(menuItemData.ProjectionName, menuItemData.VisibleFunction, null, token);
                            if (result.Value is bool visible && visible == false)
                            {
                                continue;
                            }
                        }
                    }

                    AppMenuItem appMenuItem = new AppMenuItem()
                    {
                        Label = menuItemData.Label,
                        Image = IconUtils.LoadOrDefault(menuItemData.Icon),
                        ProjectionName = menuItemData.ProjectionName,
                        CountFunction = menuItemData.CountFunction,
                        CountEntitySet = menuItemData.CountEntitySet,
                        VisibleFunction = menuItemData.VisibleFunction,
                        //If dynamic menu item has null ordinal...we give it totalcount value so it appears at the bottom ( As in Dynamic Menu Items )
                        ItemOrder = menuItemData.SortOrder == 0 ? _metadata.NavigationData.MenuItems.Count() : menuItemData.SortOrder
                    };

                    Resolver.TryResolve(out IThemeService themeService);

                    if (themeService.CurrentBrandingProfile != null && themeService.CurrentBrandingProfile.ColorScheme != ColorScheme.IFS && 
                        themeService.CurrentBrandingProfile.ElementColorDetails.ContainsKey(BrandElement.DashboardIconColor))
                    {
                        if (themeService.IsDarkModeEnabled)
                        {
                            appMenuItem.Color = UmaColors.IfsGrayDark;
                        }
                        else
                        {
                            appMenuItem.Color = UmaColors.IfsWhiteLight;
                        }
                    }
                    else
                    {
                        appMenuItem.Color = colors[i % colors.Length];
                    }

                    if (menuItemData.Type == NavigationType.External)
                    {
                        appMenuItem.NavParam = ExternalNavParam.Create(menuItemData.Target);
                        appMenuItem.Location = FrameworkLocations.External;
                    }
                    else
                    {
                        string page = menuItemData.Target.Contains('?')
                                        ? menuItemData.Target.Split('?').FirstOrDefault()
                                        : menuItemData.Target;

                        PageValues filter = menuItemData.Target.Contains('?')
                            ? PageValues.FromODataFilter(menuItemData.Target.Split('?').ElementAtOrDefault(1))
                            : null;

                        appMenuItem.NavParam = new MetadataPageNavParam(menuItemData.ProjectionName, page, filter);
                        appMenuItem.Location = FrameworkLocations.MetadataPage;
                    }

                    _items.Add(appMenuItem);
                }

                i++;
            }

            _items.Sort = (x, y) => Comparer<object>.Default.Compare(x.ItemOrder, y.ItemOrder);

            foreach (AppMenuItem item in _items)
            {
                DashboardItems.Add(item);
                AppMenuItems.Add(item);
            }
        }

        public async Task UpdateDynamicItems(DataChangeSet changeSet = null)
        {
            await CancelDynamicItemUpdateAsync();

            if (changeSet == null)
            {
                _updateChangeSet = null;
            }
            else if (_updateChangeSet != null)
            {
                DataChangeSet merged = new DataChangeSet();
                merged.AddChangeSet(changeSet);
                merged.AddChangeSet(_updateChangeSet);
                _updateChangeSet = merged;
            }
            else
            {
                _updateChangeSet = changeSet;
            }

            if (!_loadingCompleted || (changeSet != null && ShouldReloadMenuItems(changeSet)))
            {
                _items.Clear();
                DashboardItems.Clear();
                AppMenuItems.Clear();
            }

            await _updater.UpdateAsync();
        }

        public async void CancelDynamicItemUpdate()
        {
            await CancelDynamicItemUpdateAsync();
        }

        public async Task CancelDynamicItemUpdateAsync()
        {
            if (_updater.IsUpdating)
            {
                await _updater.CancelAsync();
            }

            if (_countersUpdater.IsUpdating)
            {
                await _countersUpdater.CancelAsync();
            }
        }

        private async Task LoadHomePageItems(CancellationToken token)
        {
            if (!_items.Any())
            {
                await LoadMenuItems(token);
                await LoadDynamicMenuItems(token);
            }

            if (token.IsCancellationRequested)
            {
                return;
            }

            _loadingCompleted = true;
            Loaded?.Invoke(this, EventArgs.Empty);

            await StartCountersUpdater();

            _updateChangeSet = null;
            StatusIndicatorChanged?.Invoke(this, EventArgs.Empty);
        }

        private async Task StartCountersUpdater()
        {
            await _countersUpdater.UpdateAsync();
        }

        private async Task UpdateCounters(CancellationToken token)
        {
            foreach (AppMenuItem item in _items)
            {
                await item.UpdateCounter(_metadata, _dataHandler, _updateChangeSet, token);
            }

            CountersUpdated?.Invoke(this, EventArgs.Empty);
        }

        private async Task LoadDynamicMenuItems(CancellationToken token)
        {
            int i = 0;
            int totalCount = _items.Count;
            UmaColor[] colors = UmaColors.Complementary;

            List<CpiLayout> layouts = new List<CpiLayout>();
            List<FndDynamicMenuItem> dynamicMenuItems = new List<FndDynamicMenuItem>();

            foreach (CpiClientMetadata clientMeta in _metadata.CpiMetaData.GetClients())
            {
                CpiDynamicMenuFunction menuItemsFunction = clientMeta.Layout?.DynamicMenuFunctions?.Values?.FirstOrDefault();

                if (!string.IsNullOrEmpty(menuItemsFunction?.Name))
                {
                    ExecuteResult menuItem = await _dataHandler.PerformFunctionAsync(clientMeta.Projection.Name, menuItemsFunction.Name, null, token);

                    if (menuItem.Value != null && menuItem.Value is IList fndDynamicMenuItems)
                    {
                        dynamicMenuItems = fndDynamicMenuItems.Cast<FndDynamicMenuItem>().ToList();
                        totalCount += dynamicMenuItems.Count;

                        foreach (FndDynamicMenuItem item in dynamicMenuItems)
                        {
                            if (token.IsCancellationRequested)
                            {
                                _loadingCompleted = false;
                                _items.Clear();
                                return;
                            }

                            if (_permissions.IsNavigatorEntryGranted(clientMeta.Projection.Name, item.Name))
                            {
                                AppMenuItem appMenuItem = new AppMenuItem();
                                appMenuItem.ProjectionName = clientMeta.Projection.Name;
                                appMenuItem.IsDynamic = true;
                                appMenuItem.Label = item.Label.Contains(":") ? item.Label.Substring(item.Label.IndexOf(":") + 1) : item.Label;
                                appMenuItem.Label = appMenuItem.Label?.Trim();
                                appMenuItem.Image = item.Icon == null ? IconUtils.Link : IconUtils.Load(item.Icon);
                                appMenuItem.ShowIn = item.ShowIn;

                                Resolver.TryResolve(out IThemeService themeService);

                                if (themeService.CurrentBrandingProfile != null && themeService.CurrentBrandingProfile.ColorScheme != ColorScheme.IFS &&
                                    themeService.CurrentBrandingProfile.ElementColorDetails.ContainsKey(BrandElement.DashboardIconColor))
                                {
                                    if (themeService.IsDarkModeEnabled)
                                    {
                                        appMenuItem.Color = UmaColors.IfsGrayDark;
                                    }
                                    else
                                    {
                                        appMenuItem.Color = UmaColors.IfsWhiteLight;
                                    }
                                }
                                else
                                {
                                    appMenuItem.Color = colors[i % colors.Length];
                                }

                                //If dynamic menu item has null ordinal...we give it totalcount value so it appears at the bottom
                                appMenuItem.ItemOrder = item.Ordinal == null ? appMenuItem.ItemOrder = totalCount : appMenuItem.ItemOrder = item.Ordinal;

                                if (item.Type == "external")
                                {
                                    appMenuItem.NavParam = ExternalNavParam.Create(item.Item);
                                    appMenuItem.Location = FrameworkLocations.External;
                                }
                                else
                                {
                                    appMenuItem.NavParam = new MetadataPageNavParam(clientMeta.Projection.Name, item.Item);
                                    appMenuItem.Location = FrameworkLocations.MetadataPage;
                                }

                                _items.Add(appMenuItem);
                            }
                            
                            i++;
                        }
                    }
                }
            }

            if (dynamicMenuItems.Any())
            {
                DashboardItems.Clear();
                AppMenuItems.Clear();

                //After adding dynamic menu items we sort the combined list of static and dynamic items
                _items.Sort = (x, y) => Comparer<object>.Default.Compare(x.ItemOrder, y.ItemOrder);

                foreach (AppMenuItem item in _items)
                {
                    if (item.ShowIn == "AppMenu")
                    {
                        AppMenuItems.Add(item);
                    }
                    else if (item.ShowIn == "Dashboard")
                    {
                        DashboardItems.Add(item);
                    }
                    else
                    {
                        AppMenuItems.Add(item);
                        DashboardItems.Add(item);
                    }
                }
            }
        }

#if REMOTE_ASSISTANCE
        public async Task CheckRemoteAssistanceEnabledAsync()
        {
            Resolver.TryResolve(out RemoteAssistance.IRemoteAssistanceService remoteAssistanceService);
            if (remoteAssistanceService != null)
            {
                bool isRemoteAssistanceEnabled = await remoteAssistanceService.CheckRemoteAssistanceEnabledAsync();
                _settings.Set(SettingsExtensions.RemoteAssistanceEnabled, isRemoteAssistanceEnabled);
            }
        }
#endif
    }
}
