﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Data
{
    [AttributeUsage(AttributeTargets.Field)]
    public sealed class ServerValueAttribute : Attribute
    {
        public ServerValueAttribute(string name) { m_name = name; }
        public string Name { get { return m_name; } }
        private string m_name;
    }

    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
    public sealed class EnumServerValueAttribute : Attribute
    {
        public EnumServerValueAttribute() { }
        public object EnumValue { get; set; }
        public string ServerName { get; set; }
    }

    public enum NamingConvention
    {
        UpperCaseUnderscore,
        UpperCase,
        TitleCaseUnderscore,
        TitleCase,
        TitleCaseSpace,
        LowerCaseUnderscore,
        LowerCase,
        CamelCase,
        PascalCase
    }

    public static class NamingConventionExtensions
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Globalization", "CA1308:NormalizeStringsToUppercase",
            Justification="Lower case is specified")]
        public static string Convert(this NamingConvention convention, string source)
        {
            switch (convention)
            {
                case NamingConvention.UpperCase:
                    return source != null ? source.ToUpperInvariant() : null;
                case NamingConvention.TitleCaseUnderscore:
                    return source.ToTitleCaseUnderscore();
                case NamingConvention.TitleCase:
                    return source.ToTitleCase();
                case NamingConvention.TitleCaseSpace:
                    return source.ToTitleCaseSpace();
                case NamingConvention.LowerCaseUnderscore:
                    return source.ToLowerCaseUnderscore();
                case NamingConvention.LowerCase:
                    return source != null ? source.ToLowerInvariant() : null;
                case NamingConvention.CamelCase:
                    return source.ToCamelCase();
                case NamingConvention.PascalCase:
                    return source.ToPascalCase();
                default:
                    return source.ToUpperCaseUnderscore();
            }
        }
    }

    [AttributeUsage(AttributeTargets.Enum)]
    public sealed class ServerNamingAttribute : Attribute
    {
        public ServerNamingAttribute(NamingConvention convention) { m_convention = convention; }
        public NamingConvention Convention { get { return m_convention; } }
        private NamingConvention m_convention;
    }

    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
    public sealed class EnumServerNamingAttribute : Attribute
    {
        public EnumServerNamingAttribute() { }
        public Type EnumType { get; set; }
        public NamingConvention Convention { get; set; }
    }
}
