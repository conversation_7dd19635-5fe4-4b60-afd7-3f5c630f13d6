{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<DateTime_DifferenceInDays>": {"name": "DateTime_DifferenceInDays", "type": "Function", "params": [{"name": "MyDateA", "dataType": "Timestamp"}, {"name": "MyDateB", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Number"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "DifferenceInDays", "paramsArray": ["${MyDateA}", "${MyDateB}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<DateTime_DifferenceInHours>": {"name": "DateTime_DifferenceInHours", "type": "Function", "params": [{"name": "MyDateA", "dataType": "Timestamp"}, {"name": "MyDateB", "dataType": "Timestamp"}], "layers": [{"vars": [{"name": "Result", "dataType": "Number"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "DateTime", "name": "DifferenceInHours", "paramsArray": ["${MyDateA}", "${MyDateB}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}