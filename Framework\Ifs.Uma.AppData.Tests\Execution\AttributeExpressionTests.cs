﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.AppData.AttributeExpressions;
using Ifs.Uma.AppData.Expressions;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution
{
    [TestFixture]
    public class AttributeExpressionTests
    {
        [Test]
        public void NullValueProvider()
        {
            JObject obj = JObject.Parse("{\"==\": [{\"attributeName\": \"Company\"},{\"var\": \"record.CurrentCompanyNo\"}]}");
            var expr = AttributeExpression.FromJsonLogic(obj, null);
            Assert.AreEqual(null, expr);
        }

        [Test]
        public void CompareEqualString()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("record.CurrentCompanyNo", "\"COM1\"");

            JObject obj = JObject.Parse("{\"==\": [{\"attributeName\": \"Company\"},{\"var\": \"record.CurrentCompanyNo\"}]}");
            string parsed = AttributeExpression.FromJsonLogic(obj, vp).ToString();
            Assert.AreEqual("Company Equals \"COM1\"", parsed);
        }

        [Test]
        public void CompareLessThanInt()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("record.CurrentCompanyNo", 5);

            JObject obj = JObject.Parse("{\">\": [{\"attributeName\": \"Company\"},{\"var\": \"record.CurrentCompanyNo\"}]}");
            string parsed = AttributeExpression.FromJsonLogic(obj, vp).ToString();
            Assert.AreEqual("Company GreaterThan 5", parsed);
        }

        [Test]
        public void CompareGreaterThanInt()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("record.CurrentCompanyNo", 5);

            JObject obj = JObject.Parse("{\"<=\": [{\"attributeName\": \"Company\"},5]}");
            string parsed = AttributeExpression.FromJsonLogic(obj, vp).ToString();
            Assert.AreEqual("Company LessThanOrEqual 5", parsed);
        }

        [Test]
        public void CompareNotEqualString()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("record.CurrentCompanyNo", 5);

            JObject obj = JObject.Parse("{\"!=\": [{\"attributeName\": \"Company\"},\"COM1\"]}");
            string parsed = AttributeExpression.FromJsonLogic(obj, vp).ToString();
            Assert.AreEqual("Company NotEquals COM1", parsed);
        }

        [Test]
        public void LogicalAnd()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("record.CurrentCompanyNo", "\"COM1\"");

            JObject obj = JObject.Parse("{\"and\": [{\"!=\": [{\"attributeName\": \"Company\"},\"COM1\"]},{\"!=\": [{\"attributeName\": \"Company\"},\"COM2\"]}]}");
            string parsed = AttributeExpression.FromJsonLogic(obj, vp).ToString();
            Assert.AreEqual("(Company NotEquals COM1 And Company NotEquals COM2)", parsed);
        }

         [Test]
         public void LogicalOr()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("record.CurrentCompanyNo", "\"COM1\"");

            JObject obj = JObject.Parse("{\"or\": [{\"==\": [{\"attributeName\": \"Company\"},\"COM1\"]},{\"==\": [{\"attributeName\": \"Company\"},\"COM2\"]}]}");
            string parsed = AttributeExpression.FromJsonLogic(obj, vp).ToString();
            Assert.AreEqual("(Company Equals COM1 Or Company Equals COM2)", parsed);
        }

        [Test]
        public void LogicalAndOr()
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("record.CurrentCompanyNo", "\"COM1\"");

            JObject obj = JObject.Parse("{\"and\": [{\"!=\": [{\"attributeName\": \"Company\"},\"COM1\"]},{\"or\": [{\"!=\": [{\"attributeName\": \"Company\"},\"COM2\"]},{\"!=\": [{\"attributeName\": \"Company\"},\"COM3\"]}]}]}");
            string parsed = AttributeExpression.FromJsonLogic(obj, vp).ToString();
            Assert.AreEqual("(Company NotEquals COM1 And (Company NotEquals COM2 Or Company NotEquals COM3))", parsed);
        }

        private class ValueProvider : IExpressionValueProvider
        {
            private readonly Dictionary<string, object> _values = new Dictionary<string, object>();

            public void Add(string propertyName, object value)
            {
                _values[propertyName] = value;
            }

            public bool TryGetValue(string propertyName, out object value)
            {
                return _values.TryGetValue(propertyName, out value);
            }

            public bool TryCallMethod(string methodName, object[] args, out object result)
            {
                throw new NotImplementedException();
            }
        }
    }
}
