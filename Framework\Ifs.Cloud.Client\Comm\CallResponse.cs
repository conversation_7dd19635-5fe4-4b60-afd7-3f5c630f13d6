﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  2011-10-13 PKULLK Modified to be compatible with .NEt4.5 with async calls, etc.
#endregion

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Entities;

namespace Ifs.Cloud.Client.Comm
{
    internal sealed class CallResponse : IDisposable
    {
        #region Fields
        private HttpResponseMessage _source;
        private HttpClientHandler _handler;
        #endregion

        #region Properties
        /// <summary>
        /// Response headers
        /// </summary>
        public Dictionary<string, List<string>> Headers { get; }

        /// <summary>
        /// Response body
        /// </summary>        
        #endregion

        #region Constructor
        public CallResponse(HttpResponseMessage source, HttpClientHandler handler)
        {
            _source = source;
            _handler = handler;

            // extract header values
            var headers = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);

            foreach (KeyValuePair<string, IEnumerable<string>> entry in source.Headers)
            {
                var values = new List<string>();
                foreach (string item in entry.Value)
                {
                    values.Add(item);
                }

                // We consider only the header entries which have only one value.
                headers[entry.Key] = values;
            }
            Headers = headers;
        }
        #endregion

        #region Methods

        public Task<byte[]> GetBinaryContent()
        {
            return _source.Content.ReadAsByteArrayAsync();
        }

        public bool IsStatusSuccess()
        {
            return _source.IsSuccessStatusCode;
        }

        public async Task<CallResponseStream> GetContentAsStream()
        {
            return new CallResponseStream(await _source.Content.ReadAsStreamAsync(), this);
        }
        
        public async Task<string> ConsumeContentAsStringAsync()
        {
            var data = await _source.Content.ReadAsByteArrayAsync().ConfigureAwait(false);
            return Encoding.UTF8.GetString(data, 0, data.Length);
        }
        #endregion

        #region IDisposable

        public void Dispose()
        {
            Dispose(true);
        }

        private void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_source != null)
                {
                    _source.Dispose();
                    _source = null;
                }

                if (_handler != null)
                {
                    _handler.Dispose();
                    _handler = null;
                }
            }
        }

        #endregion
    }
}
