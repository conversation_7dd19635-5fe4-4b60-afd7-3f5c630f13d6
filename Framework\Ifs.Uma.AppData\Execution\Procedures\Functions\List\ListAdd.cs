﻿using System;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.List
{
    internal sealed class ListAdd : ListFunction
    {
        public const string FunctionName = "Add";

        public ListAdd()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            MarbleList list = PrepareList(context, parameters[0]);
            object value = parameters[1].GetValue();
            
            if (!list.TryConvertAndAdd(value))
            {
                string strValue = ExecutionUtils.ValueToPreviewString(value);
                throw context.Fail($"Failed to add to list '{parameters[0].RawValue}'. Item type does not match list datatype.{Environment.NewLine}Value = {strValue}");
            }

            return null;
        }
    }
}
