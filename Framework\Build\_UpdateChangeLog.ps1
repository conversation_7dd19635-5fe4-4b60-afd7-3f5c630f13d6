Write-Output "============ Update Change Log"

Push-Location "$PSScriptRoot\..\..\Tools\ChangeLogGenerator"


msbuild "ChangeLogGenerator.sln" /m /t:Restore /nr:false /v:m

if ($LastExitCode -ne 0) { Exit $LastExitCode }

msbuild "ChangeLogGenerator.sln" /m /t:Rebuild /nr:false /v:m /p:Configuration=Release "/p:Platform=Any CPU"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Push-Location "bin\Release"

if ([String]::IsNullOrWhiteSpace($appVersion)) 
{
    .\ChangeLogGenerator.exe
}
else {
    .\ChangeLogGenerator.exe --section "$appVersion"
}

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Pop-Location

Pop-Location

Write-Output "============ End -  Update Change Log"