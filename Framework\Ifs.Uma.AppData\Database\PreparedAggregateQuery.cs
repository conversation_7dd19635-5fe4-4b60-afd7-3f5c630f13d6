﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Database
{
    internal class PreparedAggregateQuery
    {
        private const string AggregateTableAlias = "tA";

        public AggregateQuery Query { get; }
        public ISelectSpec SelectSpec { get; }
        public IEnumerable<string> UsedEntities { get; }

        public PreparedAggregateQuery(AggregateQuery query)
        {
            Query = query.Clone();

            string[] usedEntities;
            SelectSpec = ToSelectSpec(out usedEntities);
            UsedEntities = usedEntities;
        }
        
        private ISelectSpec ToSelectSpec(out string[] usedEntities)
        {
            // Find and replace the attributes in the aggregate expression with column accesses
            Expression aggrExp = Query.AggregateExpression ?? Expression.Constant(1);
            aggrExp = AttributeFindAndReplacer.Rewrite(aggrExp, Query.From.DataSource, null);
            string[] selectAttributes = AttributePathFinder.Find(aggrExp);
            selectAttributes = selectAttributes.Length == 0 ? GetDefaultSelectAttributes(Query.From.DataSource) : selectAttributes;

            // Create a query that selects the attributes needed in the aggregate expression
            EntityQuery fromQuery = Query.From.Clone();
            fromQuery.SelectAttributes = selectAttributes;
            fromQuery.Expand = null;

            PreparedEntityQuery preparedFromQuery = new PreparedEntityQuery(fromQuery, null, false);
            ISelectSpec spec = preparedFromQuery.SelectSpec;
            usedEntities = preparedFromQuery.UsedEntities.ToArray();

            if (spec.OrderBy == null || (spec.OrderBy.Offset == 0 && spec.OrderBy.Limit == 0))
            {
                // Order by does not change the results of the aggregate
                // Just switch the query to select the aggregate value
                aggrExp = AggregateColumnRetargeter.Retarget(aggrExp, preparedFromQuery.QueryColumns, spec, null);

                return SqlSpec.CreateSelect(
                    new[] { ColumnSpec.CreateAggregate(GetColumnFunction(), SqlExpression.Create(aggrExp), null) },
                    spec.From,
                    spec.Joins,
                    spec.Where,
                    spec.GroupBy,
                    null,
                    false);
            }
            else
            {
                // If the query has an offset and a limit then we must put it in a sub select
                aggrExp = AggregateColumnRetargeter.Retarget(aggrExp, preparedFromQuery.QueryColumns, spec, AggregateTableAlias);

                return SqlSpec.CreateSelect(
                    new[] { ColumnSpec.CreateAggregate(GetColumnFunction(), SqlExpression.Create(aggrExp), null) },
                    TableSpec.Create(spec, AggregateTableAlias),
                    null,
                    null,
                    null,
                    null,
                    false);
            }
        }

        private static string[] GetDefaultSelectAttributes(EntityDataSource datasource)
        {
            return datasource.Table.DataMembers.Where(x => x.PrimaryKey).Select(x => x.PropertyName).ToArray();
        }

        private EColumnFunction GetColumnFunction()
        {
            switch (Query.AggregateType)
            {
                case AggregateType.Count:
                    return EColumnFunction.CountFunction;
                case AggregateType.Sum:
                    return EColumnFunction.SumFunction;
                case AggregateType.Maximum:
                    return EColumnFunction.MaxFunction;
                case AggregateType.Minimum:
                    return EColumnFunction.MinFunction;
                case AggregateType.Average:
                    return EColumnFunction.AvgFunction;
                default:
                    return EColumnFunction.CountFunction;
            }
        }
    }
}
