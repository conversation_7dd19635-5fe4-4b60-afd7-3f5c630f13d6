﻿using System;

namespace Ifs.Uma.AppData.Expressions
{
    public class ExpressionException : Exception
    {
        public ExpressionException()
        {
        }

        public ExpressionException(string message)
            : base(message)
        {
        }

        public ExpressionException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}
