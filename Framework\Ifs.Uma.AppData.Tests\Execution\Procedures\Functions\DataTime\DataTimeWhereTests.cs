﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.DataTime
{
    [TestFixture]
    public class DataTimeWhereTests : ProcedureTest
    {
        [Test]
        public async Task ForEntitySet()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["MyDate"] = new DateTime(2000, 3, 15, 12, 0, 0);
            string result = await CallFunction<string>(TestOfflineProjection, "GetCustomers", parameters);
            Assert.AreEqual("#502#503#504", result);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>(
                "Execution.Procedures.Functions.DataTime.DataTimeWhereTestsSchema",
                "Execution.Procedures.Functions.DataTime.DataTimeWhereTestsData");
        }
    }
}
