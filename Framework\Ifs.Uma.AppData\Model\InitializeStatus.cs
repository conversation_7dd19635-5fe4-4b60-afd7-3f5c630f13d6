﻿namespace Ifs.Uma.Data.Sync
{
    public enum InitializeStatus
    {
        Unknown = 0,
        UpdateMetadata_AfterMessagesSent = 1,
        UpdateMetadata = 2,
        Failed = 4,
        Required_AfterMessagesSent = 5,
        Required = 10,
        SentRequest = 20,
        InProgressSystem = 30,
        InProgressData = 40,
        Initialized = 50,
        Initialized_WithInitRequired = 60,
        Initialized_WithMetaRefreshRequired = 70
    }
}
