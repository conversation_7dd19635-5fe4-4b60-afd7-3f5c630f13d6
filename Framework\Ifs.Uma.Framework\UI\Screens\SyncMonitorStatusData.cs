﻿using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Cache;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Localization;
using Ifs.Uma.Services;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Services.Sync;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;
using Prism.Events;
using Unity.Attributes;
using Ifs.Uma.UI.Services;

namespace Ifs.Uma.Framework.UI.Screens
{
    public sealed class SyncMonitorStatusData : ViewModelBase
    {
        public event EventHandler StatusChanged;

        private bool _isSyncing;
        public bool IsSyncing
        {
            get => _isSyncing;
            private set => SetProperty(ref _isSyncing, value);
        }

        private bool _isInitializing;
        public bool IsInitializing
        {
            get => _isInitializing;
            private set => SetProperty(ref _isInitializing, value);
        }

        private bool _isRefreshingCache;
        public bool IsRefreshingCache
        {
            get => _isRefreshingCache;
            private set
            {
                SetProperty(ref _isRefreshingCache, value);
                UpdateSyncStatus();
            }
        }

        public string RefreshText => Strings.LoginAgain;

        private bool _hasFailed;
        public bool HasFailed
        {
            get => _hasFailed;
            private set => SetProperty(ref _hasFailed, value);
        }

        private bool _canShowSendDeviceLogs;
        public bool CanShowSendDeviceLogs
        {
            get => _canShowSendDeviceLogs;
            private set => SetProperty(ref _canShowSendDeviceLogs, value);
        }

        private string _status = string.Empty;
        public string Status
        {
            get => _status;
            private set => SetProperty(ref _status, value);
        }

        private string _statusDetail = string.Empty;
        public string StatusDetail
        {
            get => _statusDetail;
            private set => SetProperty(ref _statusDetail, value);
        }

        private string _failingMessage = string.Empty;
        public string FailingMessage
        {
            get => _failingMessage;
            set => SetProperty(ref _failingMessage, value);
        }

        public string WarningText
        {
            get => string.Join(Environment.NewLine, _warnings);
        }

        private IIfsConnection _connection;
        private readonly INavigator _navigator;

        private ObservableCollection<string> _warnings = new ObservableCollection<string>();

        private bool _canTriggerForceInitialize;
        public bool CanTriggerForceInitialize
        {
            get => _canTriggerForceInitialize;
            private set => SetProperty(ref _canTriggerForceInitialize, value);
        }

        public Command TriggerInitialize { get; }
        public Command TriggerSync { get; }
        public Command TriggerLogoutAsync { get; }
        public Command TriggerRefreshLogin { get; }
        public Command TriggerRefreshCache { get; }

        private UmaColor _color;
        public UmaColor Color
        {
            get => _color;
            set => SetProperty(ref _color, value);
        }

        private readonly IDataContextProvider _db;
        private readonly IServiceManager _serviceManager;
        private readonly ITouchApp _app;
        private readonly IEventAggregator _eventAggregator;
        private readonly ISyncController _syncController;
        private readonly SynchronizationContext _uiContext;

        private ICachePreparer _cache;
        private IResolver _resolver;
        private readonly IToastService _toastService;

        private ITransactionSyncService _service;
        private int _pendingTransactionsCount;
        private TransactionSyncEventArgs _lastTransactionSyncEvent;
        private Stopwatch _sw;

        public SyncMonitorStatusData(IDataContextProvider db, IServiceManager serviceManager, IEventAggregator eventAggregator, INavigator navigator, ITouchApp app,
            ISyncController syncController, [OptionalDependency] IIfsConnection connection, IToastService toast, IResolver resolver)
        {
            _db = db;
            _app = app;
            _serviceManager = serviceManager;
            _eventAggregator = eventAggregator;
            _connection = connection;
            _navigator = navigator;
            _syncController = syncController;
            _toastService = toast;
            _resolver = resolver;
            _uiContext = SynchronizationContext.Current;

            HasFailed = false;
            TriggerSync = Command.FromMethod(OnSyncCommand);
            TriggerInitialize = Command.FromMethod(OnInitializeCommand);
            TriggerLogoutAsync = Command.FromAsyncMethod(LogoutAsyncCommand);
            TriggerRefreshLogin = Command.FromAsyncMethod(RefreshLogin);
            TriggerRefreshCache = Command.FromMethod(OnRefreshCacheCommand);

            _warnings.CollectionChanged += Warnings_CollectionChanged;
        }

        private void Warnings_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            OnPropertyChanged(nameof(WarningText));
        }

        private async Task LogoutAsyncCommand()
        {
            if (_app != null)
            {
                await _app.LogoutAsync();
            }
        }

        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                Resolver.TryResolve(out _service);

                if (_service != null)
                {
                    _service.SyncEvent += Service_SyncEvent;
                }

                _lastTransactionSyncEvent = _service?.LastSyncEvent;

                _eventAggregator.GetEvent<SyncServiceStatusChangedEvent>().Subscribe(OnSyncServiceStatusChanged, ThreadOption.UIThread);

                _syncController.RequestSync(true, false);
            }
            else
            {
                if (_service != null)
                {
                    _service.SyncEvent -= Service_SyncEvent;
                    _service = null;
                }

                _eventAggregator.GetEvent<SyncServiceStatusChangedEvent>().Unsubscribe(OnSyncServiceStatusChanged);

                _lastTransactionSyncEvent = null;
            }

            UpdateSyncStatus();
        }

        private void OnSyncServiceStatusChanged(SyncServiceStatusChangedEventArgs args)
        {
            UpdateSyncStatus();
        }

        private void Service_SyncEvent(object sender, TransactionSyncEventArgs e)
        {
            if (e.EventType == SyncEventType.SyncEnded ||
                (e.EventType == SyncEventType.SyncTrace && e.TraceType == SyncTraceType.UploadedMessages))
            {
                UpdatePendingMessageCount();
            }

            _uiContext.Post(state =>
            {
                _lastTransactionSyncEvent = (TransactionSyncEventArgs)state;
                UpdateSyncStatus();
            }, e);
        }

        private void UpdateSyncStatus()
        {
            SyncServiceStatus status = GetCombinedSyncStatus();
            IsInitializing = _service != null && _service.IsInitializing;
            bool internetAvailable = PlatformServices.IsNetworkAvailable();

            TriggerInitialize.IsEnabled = internetAvailable;
            TriggerRefreshCache.IsEnabled = _service != null && internetAvailable && !status.IsSyncing && !IsInitializing;
            TriggerSync.IsEnabled = internetAvailable;
            TriggerRefreshLogin.IsEnabled = false;
            CanTriggerForceInitialize = IsInitializing && TriggerInitialize.IsEnabled;
            CanShowSendDeviceLogs = IsInitializing && HasFailed;

            if (_service == null || !internetAvailable)
            {
                Color = UmaColor.FromRgb(0x99, 0x99, 0x99);
                Status = IsInitializing ? Strings.OfflineUnableToInitialize : Strings.Offline;
                StatusDetail = string.Empty;
                IsSyncing = false;
                FailingMessage = string.Empty;
            }
            else if (_connection?.TouchAppsComms?.IsBroken == true)
            {
                Status = Strings.LoginRequired;
                Color = UmaColors.IfsOrangeDark;
                IsSyncing = false;
                TriggerInitialize.IsEnabled = false;
                TriggerRefreshCache.IsEnabled = false;
                TriggerRefreshLogin.IsEnabled = true;
            }
            else
            {
                StringBuilder sb = new StringBuilder();

                if (status.IsFailing)
                {
                    HasFailed = true;
                    FailingMessage = status.LastFailMessage;
                    sb.AppendLine(Strings.SyncFailing);
                }
                else
                {
                    FailingMessage = string.Empty;
                }

                if (_pendingTransactionsCount == 1)
                {
                    sb.AppendLine(string.Format(Strings.PendingTransaction, _pendingTransactionsCount));
                }
                else if (_pendingTransactionsCount > 0)
                {
                    sb.AppendLine(string.Format(Strings.PendingTransactions, _pendingTransactionsCount));
                }

                string transDetail = GetTransactionDetail();
                if (!string.IsNullOrEmpty(transDetail))
                {
                    sb.AppendLine(transDetail);
                }

                if (!string.IsNullOrEmpty(status.Detail))
                {
                    sb.AppendLine(status.Detail);
                }

                StatusDetail = sb.ToString();

                if (_service.InitializeStatus == Uma.Data.Sync.InitializeStatus.Initialized_WithInitRequired &&
                    !_warnings.Contains(Strings.SchemaChangedWarning))
                {
                    _warnings.Add(Strings.SchemaChangedWarning);
                }

                if (_service.InitializeStatus == Uma.Data.Sync.InitializeStatus.Initialized_WithMetaRefreshRequired &&
                    !_warnings.Contains(Strings.AppConfigurationChangedWarning))
                {
                    _warnings.Add(Strings.AppConfigurationChangedWarning);
                }

                if (status.IsFailing)
                {
                    Color = UmaColors.IfsRed;
                }
                else if (_warnings.Any())
                {
                    Color = UmaColors.IfsOrange;
                }
                else
                {
                    Color = UmaColors.IfsGreenLight;
                }

                if (IsInitializing)
                {
                    string statusMessage = Strings.Initializing;

                    if (_service.InitializeStatus == Uma.Data.Sync.InitializeStatus.Required_AfterMessagesSent ||
                        _service.InitializeStatus == Uma.Data.Sync.InitializeStatus.UpdateMetadata_AfterMessagesSent)
                    {
                        statusMessage = Strings.Uploading;
                    }

                    Status = statusMessage;

                    if (_app.DeveloperMode)
                    {
                        if (_sw == null)
                        {
                            _sw = Stopwatch.StartNew();
                        }
                        else if (!status.IsFailing && _sw.Elapsed.Minutes >= 1 && !_warnings.Contains(Strings.InitializationProcessIsTakingLongTime))
                        {
                            _warnings.Add(Strings.InitializationProcessIsTakingLongTime);
                        }
                    }
                }
                else if (IsRefreshingCache)
                {
                    TriggerInitialize.IsEnabled = false;
                    TriggerRefreshCache.IsEnabled = false;

                    Status = Strings.RefreshingCache;
                    StopInitTimer();
                }
                else if (status.IsSyncing)
                {
                    Status = Strings.Syncing;
                    StopInitTimer();
                }
                else
                {
                    Status = Strings.Idle;
                    StopInitTimer();
                }

                bool initFailed = _service.InitializeStatus == Uma.Data.Sync.InitializeStatus.Failed;
                IsSyncing = (IsInitializing && !initFailed) || status.IsSyncing || IsRefreshingCache;
            }

            FireStateChanged();
        }

        private void StopInitTimer()
        {
            if (_app.DeveloperMode && _sw != null && _sw.IsRunning)
            {
                _sw.Stop();
                _sw = null;
            }
        }

        public string GetTransactionDetail()
        {
            if (_service != null && _service.IsWaitingForServer)
            {
                return Strings.WaitingForServer;
            }

            TransactionSyncEventArgs transactionSyncEvent = _lastTransactionSyncEvent;
            if (transactionSyncEvent?.TraceType == null)
                return string.Empty;

            SyncTraceType syncTraceType = transactionSyncEvent.TraceType.Value;
            string tableName = transactionSyncEvent.LastUpdatedTable;

            if (IsInitializing && syncTraceType == SyncTraceType.ProcessingMessage && !string.IsNullOrEmpty(tableName))
            {
                IMetaModel metaModel = _db.GetMetaModel();
                IMetaTable table = metaModel.GetTable(tableName);
                return string.Format(Strings.UpdatingTable, table?.DisplayName ?? tableName?.ToTitleCaseSpace()?.Replace('$', ' '));
            }

            if (syncTraceType == SyncTraceType.ProcessingMessage && !string.IsNullOrEmpty(transactionSyncEvent.Counter))
            {
                return Strings.ProcessingMessages + " " + transactionSyncEvent.Counter;
            }

            switch (syncTraceType)
            {
                case SyncTraceType.RequestingInitialization:
                    return Strings.RequestingInitialization;
                case SyncTraceType.UpdatingMetadata:
                    return Strings.UpdatingMetadata;
                case SyncTraceType.DownloadedMessages:
                    return Strings.DownloadedMessages;
                case SyncTraceType.DownloadingMessages:
                    return Strings.DownloadingMessages;
                case SyncTraceType.NoMessagesToUpload:
                    return Strings.NoMessagesToUpload;
                case SyncTraceType.ProcessingMessage:
                    return Strings.ProcessingMessages;
                case SyncTraceType.UploadedMessages:
                    return Strings.UploadedMessages;
                case SyncTraceType.UploadingMessages:
                    return Strings.UploadingMessages;
                case SyncTraceType.UploadingMessagesFailed:
                    return Strings.UploadingMessagesFailed;
                default:
                    return string.Empty;
            }
        }

        private SyncServiceStatus GetCombinedSyncStatus()
        {
            var statuses = _serviceManager.GetSyncServiceStatuses();

            if (statuses.Count == 0)
            {
                return new SyncServiceStatus(string.Empty, false, null, false, null);
            }

            if (statuses.Count == 1)
            {
                return statuses[0];
            }

            bool syncing = statuses.Any(x => x.IsSyncing);
            bool failing = statuses.Any(x => x.IsFailing);
            string failMessage = null;

            if (failing)
            {
                StringBuilder sb = new StringBuilder();
                foreach (SyncServiceStatus status in statuses)
                {
                    if (status.IsFailing && !string.IsNullOrWhiteSpace(status.LastFailMessage))
                    {
                        if (sb.Length > 0)
                        {
                            sb.AppendLine();
                        }

                        sb.Append(status.LastFailMessage);
                    }
                }

                if (sb.Length > 0)
                {
                    failMessage = sb.ToString();
                }
            }

            StringBuilder sbDetail = new StringBuilder();
            foreach (SyncServiceStatus status in statuses)
            {
                if (!string.IsNullOrWhiteSpace(status.Detail))
                {
                    sbDetail.AppendLine(status.Detail);
                }
            }

            return new SyncServiceStatus(string.Empty, syncing, sbDetail.ToString(), failing, failMessage);
        }

        private void OnSyncCommand()
        {
            _syncController.RequestSync(true, true);
        }

        public async void OnRefreshCacheCommand()
        {
            IsRefreshingCache = true;

            if (!await _cache.RefreshCache())
            {
                _toastService.Show(ToastType.Warning, Strings.RefreshCacheError);
            }

            IsRefreshingCache = false;
        }

        private void OnInitializeCommand()
        {
            if (_service != null)
            {
                if (_service.IsInitializing)
                {
                    _pendingTransactionsCount = 0;
                    _service.RequestInitialization(false);
                }
                else
                {
                    _service.RequestInitialization(true);
                }
            }
        }

        private void UpdatePendingMessageCount()
        {
            try
            {
                var ctx = _db.CreateDataContext();

                if (_resolver.TryResolve(out ICachePreparer cacheService))
                {
                    _cache = cacheService;
                }

                _pendingTransactionsCount = ctx.TransitionRows.Count(x => x.SyncState == SyncState.Unsent);
            }
            catch (Exception)
            {
                // ignored
            }
        }

        private void FireStateChanged()
        {
            StatusChanged?.Invoke(this, EventArgs.Empty);
        }

        private async Task RefreshLogin()
        {
            await _app.RefreshLogin();
        }
    }
}
