﻿using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Attachments.Documents
{
    public interface IDocumentHandler
    {
        Task<ILocalFileInfo> GetLocalFileForDocumentAsync(EdmFile edmFile);
        Task CleanupOldDocuments();
        Task<MobileDocClass[]> GetSelectableDocumentClassesAsync();
        Task<MobileDocClass> GetDocumentClassAsync(string docClass);
        Task<MobileDocClass> GetDefaultDocumentClassAsync();
        Task<IEnumerable<DocRevisionInfo>> GetDocumentInfosAsync(string entityName, string keyRef);
        Task<int> GetDocumentCountAsync(string entityName, string keyRef);
        Task<DocRevisionInfo> GetDocumentInfoAsync(long docRevRowId);
        Task<IEnumerable<EdmApplication>> GetEdmApplicationsAsync();
        Task<long> NewDocumentAsync(DocReferenceObject docRef, EdmFile edmFile, Stream dataStream);
        Task UpdateDocumentReferenceAsync(DocReferenceObject docRef, IEnumerable<string> changedMembers);
        Task<long> NewDocumentRevisionAsync(DocReferenceObject docRef, EdmFile edmFile, Stream dataStream);
        Task UploadDocumentAsync(string docClass, string docNo, string docSheet, string docRev,
            string fileName, Stream dataStream);
        Task RequestDownloadAsync(EdmFile edmFile);
        Task RequestUploadAsync(EdmFile edmFile);
        Task<bool> IsEnabledFor(string entityName);
        Task<bool> CanCreateNew(string entityName, string keyRef);
        Task<bool> CanReviseDocument(string entityName, string keyRef);
    }

    public class DocRevisionInfo
    {
        public DocReferenceObject DocumentRevision { get; set; }
        public EdmFile EdmFile { get; set; }
        public MobileDocClass DocumentClass { get; set; }
    }
}
