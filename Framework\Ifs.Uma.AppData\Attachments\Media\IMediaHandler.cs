﻿using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Attachments.Media
{
    public interface IMediaHandler
    {
        Task<ILocalFileInfo> GetLocalFileForMediaAsync(MediaLibraryItem mediaItem);
        Task<MediaInfo> GetMediaAsync(long mediaRowId);
        Task<IEnumerable<MediaInfo>> GetMediaAsync(string entityName, string keyRef);
        Task<int> GetMediaCountAsync(string entityName, string keyRef);
        Task<long> AddMediaAsync(string entityName, string keyRef, string title, string description, string fileName, double? latitude, double? longitude, Stream dataStream);
        Task AddMediaFile(string fileName, Stream dataStream);
        MediaInfo AddBinaryMedia(FwDataContext context, string entityName, string keyRef, string title, string description, string fileName, Stream dataStream);
        Task RemoveMediaAsync(long mediaRowId);
        Task DisconnectMediaAsync(MediaLibraryItem mediaItem);
        Task UploadMediaAsync(long mediaItemId, string fileName, Stream dataStream);
        Task CleanupOldMedia();
        Task<ILocalFileInfo> GetMediaFileForDisplayAsync(string entityName, string keyRef, CancellationToken cancelToken);
        Task RequestDownloadAsync(MediaLibraryItem mediaItem);
        Task RequestUploadAsync(MediaLibraryItem mediaItem);
        Task CancelUploadAsync(MediaLibraryItem mediaItem);
        Task<bool> IsEnabledFor(string entityName);
        Task<bool> CanCreateNew(string entityName, string keyRef);
        Task<bool> CanReplaceMedia(string entityName, string keyRef);
        Task<ILocalFileInfo> GetLocalFileForLogoAsync(string fileName);
    }

    public class MediaInfo
    {
        public MediaLibrary Library { get; set; }
        public MediaLibraryItem MediaItem { get; set; }
    }
}
