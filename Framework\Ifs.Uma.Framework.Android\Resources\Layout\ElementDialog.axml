<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusableInTouchMode="true"
    android:focusable="true" >
    <android.support.design.widget.AppBarLayout
        android:id="@+id/AppBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:theme="@style/Theme.Ifs.AppBarOverlay">
      <android.support.v7.widget.Toolbar
          android:id="@id/Toolbar"
          android:layout_width="match_parent"
          android:layout_height="?attr/actionBarSize"
          android:background="?attr/colorPrimary"
          app:popupTheme="@style/Theme.Ifs.PopupOverlay" />
    </android.support.design.widget.AppBarLayout>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        android:layout_weight="1">
        <Ifs.Uma.Framework.UI.Elements.ElementListView
            android:id="@+id/ElementList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />
    </ScrollView>
    <Ifs.Uma.Framework.UI.Commands.CommandBlockView
        android:id="@+id/CommandButtonPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end" />
</LinearLayout>
