{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<Convert_ToTimestamp>": {"name": "Convert_ToTimestamp", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Convert", "name": "ToTimestamp", "paramsArray": ["${TextInput}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<Convert_ToTimestampBySet>": {"name": "Convert_ToTimestampBySet", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Timestamp"}], "execute": [{"call": {"method": "set", "args": {"name": "TextInput"}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}