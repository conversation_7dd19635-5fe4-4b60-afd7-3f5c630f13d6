﻿using Ifs.Uma.AppData.Messages;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.KeyMapping
{
    public interface IClientKeysMapper
    {
        void Load(IMetaModel metaModel, ClientKeysMap[] keys);
        MessageTableData MapServerToClientKeys(MessageTableData data);
        void MapServerToClientKeys(RemoteRow row);
        void RegisterKeys(params ClientKeysMap[] keys);
        void UnregisterKeys(params ClientKeysMap[] keys);        
    }
}
