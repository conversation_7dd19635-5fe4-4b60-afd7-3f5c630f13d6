﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.AppData.Expressions
{
    public partial class IfsExpression
    {
        public static Expression FromJsonLogic(JToken json)
        {
            if (json == null)
            {
                return null;
            }

            Expression exp = JsonLogic.ConvertExpression(json);

            if (exp.Type == typeof(DynamicValue))
            {
                exp = Expression.Convert(exp, typeof(object), DynamicValue.ConvertToObjectMethod);
            }

            return exp;
        }

        private static class JsonLogic
        {
            private static readonly Dictionary<string, ExpressionType> BinaryOperations = new Dictionary<string, ExpressionType>
            {
                ["=="] = ExpressionType.Equal,
                ["!="] = ExpressionType.NotEqual,
                ["or"] = ExpressionType.OrElse,
                ["and"] = ExpressionType.AndAlso,
                
                [">"] = ExpressionType.GreaterThan,
                [">="] = ExpressionType.GreaterThanOrEqual,
                ["<"] = ExpressionType.LessThan,
                ["<="] = ExpressionType.LessThanOrEqual,

                ["+"] = ExpressionType.Add,
                ["-"] = ExpressionType.Subtract,
                ["*"] = ExpressionType.Multiply,
                ["/"] = ExpressionType.Divide,
                ["%"] = ExpressionType.Modulo
            };

            public static Expression ConvertExpression(JToken jToken)
            {
                if (jToken.Type != JTokenType.Object)
                {
                    return Expression.Constant(new DynamicValue(jToken.ToObject<object>()));
                }
                else
                {
                    JObject json = (JObject)jToken;
                    return ConvertJsonLogicOperation(json);
                }
            }

            private static Expression ConvertJsonLogicOperation(JObject json)
            {
                if (json != null)
                {
                    JProperty property = json.Properties().FirstOrDefault();
                    ExpressionType binaryOp;

                    if (property != null)
                    {
                        JToken token = json[property.Name];

                        if (BinaryOperations.TryGetValue(property.Name, out binaryOp))
                        {
                            return ConvertBinary(binaryOp, token);
                        }

                        switch (property.Name)
                        {
                            case "var":
                                return ConvertVar(token);
                            case "!":
                                return ConvertNot(token);
                            case "in":
                                return ConvertIn(token);
                            case "method":
                                return ConvertMethod(token);
                            default:
                                throw new NotSupportedException($"JsonLogic operator '{property.Name}' is not supported.");
                        }
                    }
                }

                return null;
            }

            private static Expression ConvertBinary(ExpressionType op, JToken json)
            {
                JArray array = (JArray)json;

                Expression left = ConvertExpression(array[0]);

                for (int i = 1; i < array.Count; i++)
                {
                    Expression right = ConvertExpression(array[i]);

                    if (left.Type != right.Type)
                    {
                        left = ConvertToDynamicValue(left);
                        right = ConvertToDynamicValue(right);
                    }

                    left = Expression.MakeBinary(op, left, right);
                }

                return left;
            }

            private static Expression ConvertVar(JToken json)
            {
                string propertyPath = (string)json;
                return IfsExpression.VarAccess(propertyPath);
            }

            private static Expression ConvertNot(JToken json)
            {
                if (json is JArray jsonArray)
                {
                    return Not(ConvertExpression(jsonArray[0]));
                }

                return Expression.Not(ConvertExpression(json));
            }

            private static Expression ConvertIn(JToken token)
            {
                JArray array = (JArray)token;
                Expression left = ConvertExpression(array[0]);

                JArray inArray = (JArray)array[1];
                List<Expression> ins = new List<Expression>();
                foreach (JToken inToken in inArray)
                {
                    Expression inExp = ConvertExpression(inToken);
                    ins.Add(inExp);
                }
                
                return IfsExpression.In(left, ins);
            }
            
            private static Expression ConvertMethod(JToken token)
            {
                JArray array = (JArray)token;
                string methodName = (string)array[0];

                List<Expression> args = new List<Expression>();
                if (array.Count > 1)
                {
                    JArray argsArray = (JArray)array[1];
                    foreach (JToken arg in argsArray)
                    {
                        Expression argExp = ConvertExpression(arg);
                        args.Add(argExp);
                    }
                }

                return methodName.StartsWith("api.") ? ApiMethodFactory.CreateMethod(methodName, args) : IfsExpression.Method(methodName, args);
            }

            private static Expression ConvertToDynamicValue(Expression exp)
            {
                if (exp.Type == typeof(DynamicValue))
                {
                    return exp;
                }
                else if (exp.Type == typeof(bool))
                {
                    return Convert(exp, typeof(DynamicValue), ConvertToDynamicValueFromBoolMethod);
                }
                else
                {
                    return Convert(Convert(exp, typeof(object)), typeof(DynamicValue), ConvertToDynamicValueMethod);
                }
            }

            private static readonly MethodInfo ConvertToDynamicValueMethod = typeof(JsonLogic).GetTypeInfo().GetDeclaredMethods(nameof(ConvertToDynamicValueFromObject)).Single();
            private static readonly MethodInfo ConvertToDynamicValueFromBoolMethod = typeof(JsonLogic).GetTypeInfo().GetDeclaredMethods(nameof(ConvertToDynamicValueFromBool)).Single();
            
            private static DynamicValue ConvertToDynamicValueFromObject(object value)
            {
                return new DynamicValue(value);
            }

            private static DynamicValue ConvertToDynamicValueFromBool(bool value)
            {
                return new DynamicValue(value);
            }
        }
    }
}
