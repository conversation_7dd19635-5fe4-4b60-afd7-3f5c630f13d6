﻿using System;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Database
{
    public abstract class DbDataReader : IDisposable
    {
        public bool TraceFlag { get; set; }

        public bool Read()
        {
            bool result;
            try
            {
                result = DoRead();
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadError, Sql);
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFunc, "Read", result.ToString());
            }
            return result;
        }

        public bool IsDBNull(int i)
        {
            bool result;
            try
            {
                result = DoIsDBNull(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "IsDBNull", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "IsDbNull",
                    ObjectConverter.ToString(i), result.ToString());
            }
            return result;
        }

        public bool GetBool(int i)
        {
            bool result;
            try
            {
                result = DoGetBool(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetBool", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "GetBool",
                    ObjectConverter.ToString(i), result.ToString());
            }
            return result;
        }

        public byte[] GetBytes(int i)
        {
            byte[] result;
            try
            {
                result = DoGetBytes(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetBytes",
                    ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                if (result != null)
                {
                    Logger.Trace(Strings.TraceReaderFuncIndex, "GetBytes",
                        ObjectConverter.ToString(i), ObjectConverter.ToPrettyString(result));
                }
                else
                {
                    Logger.Trace(Strings.TraceReaderFuncIndexNull, "GetBytes",
                        ObjectConverter.ToString(i));
                }
            }
            return result;
        }

        public DateTime GetDateTime(int i)
        {
            DateTime result;
            try
            {
                result = DoGetDateTime(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetDateTime", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "GetDateTime",
                    ObjectConverter.ToString(i), ObjectConverter.ToString(result));
            }
            return result;
        }

        public long GetInt64(int i)
        {
            long result;
            try
            {
                result = DoGetInt64(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetInt64", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "GetInt64",
                    ObjectConverter.ToString(i), ObjectConverter.ToString(result));
            }
            return result;
        }

        public string GetString(int i)
        {
            string result;
            try
            {
                result = DoGetString(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetString", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                if (result != null)
                {
                    Logger.Trace(Strings.TraceReaderFuncIndex, "GetString",
                        ObjectConverter.ToString(i), result);
                }
                else
                {
                    Logger.Trace(Strings.TraceReaderFuncIndexNull, "GetString",
                        ObjectConverter.ToString(i));
                }
            }
            return result;
        }

        public double GetDouble(int i)
        {
            double result;
            try
            {
                result = DoGetDouble(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetDouble", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "GetDouble",
                    ObjectConverter.ToString(i), ObjectConverter.ToString(result));
            }
            return result;
        }

        public int FieldCount
        {
            get
            {
                int result;
                try
                {
                    result = DoFieldCount();
                }
                catch (Exception)
                {
                    Logger.Error(Strings.ReadCountError);
                    throw;
                }
                if (TraceFlag)
                {
                    Logger.Trace(Strings.TraceReaderFunc, "FieldCount",
                        ObjectConverter.ToString(result));
                }
                return result;
            }
        }

        public Type GetFieldType(int i)
        {
            Type result;
            try
            {
                result = DoGetFieldType(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetFieldType", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                if (result != null)
                {
                    Logger.Trace(Strings.TraceReaderFuncIndex, "GetFieldType",
                        ObjectConverter.ToString(i), result.FullName);
                }
                else
                {
                    Logger.Trace(Strings.TraceReaderFuncIndexNull, "GetFieldType",
                        ObjectConverter.ToString(i));
                }
            }
            return result;
        }

        public object GetValue(int i)
        {
            object result;
            try
            {
                result = DoGetValue(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetValue", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                if (result != null)
                {
                    Logger.Trace(Strings.TraceReaderFuncIndex, "GetValue",
                        ObjectConverter.ToString(i), ObjectConverter.ToPrettyString(result));
                }
                else
                {
                    Logger.Trace(Strings.TraceReaderFuncIndexNull, "GetValue",
                        ObjectConverter.ToString(i));
                }
            }
            return result;
        }

        public decimal GetDecimal(int i)
        {
            decimal result;
            try
            {
                result = DoGetDecimal(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetDecimal", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "GetDecimal",
                    ObjectConverter.ToString(i), ObjectConverter.ToString(result));
            }
            return result;
        }

        public int GetInt32(int i)
        {
            int result;
            try
            {
                result = DoGetInt32(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetInt32", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "GetInt32",
                    ObjectConverter.ToString(i), ObjectConverter.ToString(result));
            }
            return result;
        }

        public short GetInt16(int i)
        {
            short result;
            try
            {
                result = DoGetInt16(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetInt16", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "GetInt16",
                    ObjectConverter.ToString(i), ObjectConverter.ToString(result));
            }
            return result;
        }

        public Guid GetGuid(int i)
        {
            Guid result;
            try
            {
                result = DoGetGuid(i);
            }
            catch (Exception)
            {
                Logger.Error(Strings.ReadFunctionError, "GetGuid", ObjectConverter.ToString(i));
                throw;
            }
            if (TraceFlag)
            {
                Logger.Trace(Strings.TraceReaderFuncIndex, "GetGuid",
                    ObjectConverter.ToString(i), ObjectConverter.ToString(result));
            }
            return result;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected DbDataReader(ILogger logger, bool traceFlag, string sql)
        {
            Logger = logger;
            TraceFlag = traceFlag;
            Sql = sql;
        }

        protected ILogger Logger { get; private set; }
        protected string Sql { get; private set; }

        protected abstract bool DoRead();
        protected abstract bool DoIsDBNull(int i);
        protected abstract bool DoGetBool(int i);
        protected abstract byte[] DoGetBytes(int i);
        protected abstract DateTime DoGetDateTime(int i);
        protected abstract long DoGetInt64(int i);
        protected abstract string DoGetString(int i);
        protected abstract double DoGetDouble(int i);
        protected abstract int DoFieldCount();
        protected abstract Type DoGetFieldType(int i);
        protected abstract object DoGetValue(int i);
        protected abstract decimal DoGetDecimal(int i);
        protected abstract int DoGetInt32(int i);
        protected abstract short DoGetInt16(int i);
        protected abstract Guid DoGetGuid(int i);

        ~DbDataReader()
        {
            Dispose(false);
        }

        protected virtual void Dispose(bool disposing)
        {
        }
    }
}
