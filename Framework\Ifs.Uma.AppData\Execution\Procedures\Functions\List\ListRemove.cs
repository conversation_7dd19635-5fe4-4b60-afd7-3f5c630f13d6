﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.List
{
    internal sealed class ListRemove : ListFunction
    {
        public const string FunctionName = "Remove";

        public ListRemove()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            MarbleList list = PrepareList(context, parameters[0]);
            object value = parameters[1].GetValue();
            int index = list.GetIndex(context, value);

            if (index >= 0)
            {
                list.RemoveAt(index);
                return true;
            }

            return false;
        }
    }
}
