﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Metadata.OData;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.Execution.Commands
{
    internal class ExecuteUpload
    {
        private readonly ILogger _logger;
        private readonly IDataHandler _data;
        private readonly IMediaHandler _mediaHandler;
        private readonly IDocumentHandler _documentHandler;
        private readonly IMetadata _metadata;

        public ExecuteUpload(ILogger logger, IDataHandler data, IMediaHandler mediaHandler, IDocumentHandler documentHandler, IMetadata metadata)
        {
            _logger = logger;
            _data = data;
            _mediaHandler = mediaHandler;
            _documentHandler = documentHandler;
            _metadata = metadata;
        }

        public Task<ExecuteResult> DoUploadAsync(CommandContext context, string url, PickedFile file)
        {
            ODataResourcePath resourcePath = GetResourcePath(context, url);

            if (IsMediaUpload(resourcePath))
            {
                return UploadMedia(context, resourcePath, file);
            }
            else if (IsDocUpload(resourcePath))
            {
                return UploadDoc(context, resourcePath, file);
            }
            else
            {
                return Upload(context, resourcePath, file);
            }
        }

        private static bool IsMediaUpload(ODataResourcePath resourcePath)
        {
            return resourcePath.Segments.Count == 3 &&
                   resourcePath.Segments[1].ResourceName == "MediaItemSet" &&
                   resourcePath.Segments[2].ResourceName == "MediaObject";
        }

        private async Task<ExecuteResult> UploadMedia(CommandContext context, ODataResourcePath resourcePath, PickedFile file)
        {
            IReadOnlyList<KeyValuePair<string, object>> key = resourcePath.Segments[1].ResourceKey;
            object itemIdValue = key.Where(x => x.Key == nameof(MediaLibraryItem.ItemId)).Select(x => x.Value).FirstOrDefault();

            if (itemIdValue == null)
            {
                throw context.Fail(Strings.InvalidUploadLocation);
            }

            long itemId = ObjectConverter.ToLong(itemIdValue);

            using (Stream stream = await file.ReadAsync())
            {
                await _mediaHandler.UploadMediaAsync(itemId, file.FileName, stream);
            }

            return ExecuteResult.None;
        }

        private static bool IsDocUpload(ODataResourcePath resourcePath)
        {
            return resourcePath.Segments.Count == 4 &&
                   resourcePath.Segments[1].ResourceName == "DocIssueSet" &&
                   resourcePath.Segments[2].ResourceName == "EdmFileReferenceArray" &&
                   resourcePath.Segments[3].ResourceName == "FileData";
        }

        private async Task<ExecuteResult> UploadDoc(CommandContext context, ODataResourcePath resourcePath, PickedFile file)
        {
            IReadOnlyList<KeyValuePair<string, object>> key = resourcePath.Segments[2].ResourceKey;

            string docClass = key.Where(x => x.Key == nameof(EdmFile.DocClass)).Select(x => x.Value).FirstOrDefault() as string;
            string docNo = key.Where(x => x.Key == nameof(EdmFile.DocNo)).Select(x => x.Value).FirstOrDefault() as string;
            string docSheet = key.Where(x => x.Key == nameof(EdmFile.DocSheet)).Select(x => x.Value).FirstOrDefault() as string;
            string docRev = key.Where(x => x.Key == nameof(EdmFile.DocRev)).Select(x => x.Value).FirstOrDefault() as string;

            if (docClass == null || docNo == null || docSheet == null || docRev == null)
            {
                throw context.Fail(Strings.InvalidUploadLocation);
            }

            using (Stream stream = await file.ReadAsync())
            {
                await _documentHandler.UploadDocumentAsync(docClass, docNo, docSheet, docRev, file.FileName, stream);
            }

            return ExecuteResult.None;
        }

        private async Task<ExecuteResult> Upload(CommandContext context, ODataResourcePath resourcePath, PickedFile file)
        {
            if (resourcePath.Segments.Count < 3)
            {
                return ExecuteResult.None;
            }

            string entityName = GetEntity(resourcePath);
            ObjPrimaryKey primaryKey = GetObjPrimaryKey(entityName, resourcePath.Segments[resourcePath.Segments.Count - 2]);
            string attribute = resourcePath.Segments[resourcePath.Segments.Count - 1].ResourceName;
            string projectionName = resourcePath.Segments[0].ResourceName.Substring(0, resourcePath.Segments[0].ResourceName.IndexOf('.'));

            if (primaryKey == null || attribute == null)
            {
                throw context.Fail(Strings.InvalidUploadLocation);
            }

            using (Stream stream = await file.ReadAsync())
            {
                ExecuteResult result = await _data.SetBinaryDataAsync(projectionName, primaryKey, attribute, stream, CancellationToken.None);
                result.CheckFailure();
                return result;
            }
        }

        private string GetEntity(ODataResourcePath resourcePath)
        {
            string[] resourceNames = resourcePath.Segments.Skip(1).Take(resourcePath.Segments.Count - 2).Select(x => x.ResourceName).ToArray();
            string projectionName = resourcePath.Segments[0].ResourceName.Substring(0, resourcePath.Segments[0].ResourceName.IndexOf('.'));

            string entityName = null;
            foreach (string resourceName in resourceNames)
            {
                if (entityName == null)
                {
                    CpiContains contains = _metadata.FindContains(projectionName, resourceName);

                    if (contains?.Entity == null)
                    {
                        return null;
                    }

                    entityName = contains.Entity;
                }
                else
                {
                    CpiArray array = _metadata.FindArray(projectionName, entityName, resourceName);

                    if (array?.Target == null)
                    {
                        return null;
                    }

                    entityName = array.Target;
                }
            }

            return entityName;
        }

        private ObjPrimaryKey GetObjPrimaryKey(string entityName, ODataResourcePathSegment segment)
        {
            IMetaTable table = _metadata.GetTableForEntityName(entityName);

            if (table == null)
            {
                return null;
            }

            IReadOnlyList<KeyValuePair<string, object>> keys = segment.ResourceKey;
            bool ordered = keys.All(x => string.IsNullOrEmpty(x.Key));

            IMetaDataMember[] pkMembers = table.DataMembers.Where(x => x.ServerPrimaryKey).ToArray();

            if (pkMembers.Length != keys.Count)
            {
                return null;
            }

            Dictionary<string, object> values = new Dictionary<string, object>();

            if (ordered)
            {
                for (int i = 0; i < pkMembers.Length; i++)
                {
                    IMetaDataMember member = pkMembers[i];
                    object value = keys[i].Value;
                    values[member.ColumnName] = member.ConvertValue(value);
                }
            }
            else
            {
                foreach (IMetaDataMember member in pkMembers)
                {
                    object value = keys.Where(x => x.Key == member.PropertyName).Select(x => x.Value).FirstOrDefault();
                    values[member.ColumnName] = member.ConvertValue(value);
                }
            }

            return ObjPrimaryKey.FromPrimaryKey(table, values);
        }

        private static ODataResourcePath GetResourcePath(CommandContext context, string url)
        {
            string uploadUrl = ODataUtils.ReplaceParams(url, x => context.GetValue(x));
            return new ODataResourcePath(uploadUrl);
        }
    }
}
