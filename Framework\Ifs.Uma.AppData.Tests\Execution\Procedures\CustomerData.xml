<?xml version='1.0' encoding='utf-8'?>
<dataexport>
  <data>

    <tst_customer_type>
      <type_id>TYPE_A</type_id>
      <type_description>Customer Type A</type_description>
    </tst_customer_type>

    <tst_customer_type>
      <type_id>TYPE_B</type_id>
      <type_description>Customer Type B</type_description>
    </tst_customer_type>

    
    <tst_customer>
      <customer_no>500</customer_no>
      <customer_name>Test Customer 1</customer_name>
      <customer_type>TYPE_A</customer_type>
    </tst_customer>

    <tst_customer_address>
      <address_customer_no>500</address_customer_no>
      <address_id>HOME</address_id>
      <address_line>Test Address 1</address_line>
    </tst_customer_address>

    <tst_customer_address>
      <address_customer_no>500</address_customer_no>
      <address_id>WORK</address_id>
      <address_line>Test Address 2</address_line>
    </tst_customer_address>

    
    <tst_customer>
      <customer_no>501</customer_no>
      <customer_name>Test Customer 2</customer_name>
      <customer_type>TYPE_B</customer_type>
    </tst_customer>

    <tst_customer_address>
      <address_customer_no>501</address_customer_no>
      <address_id>HOME</address_id>
      <address_line>Test Address 3</address_line>
    </tst_customer_address>

    <tst_customer_address>
      <address_customer_no>501</address_customer_no>
      <address_id>WORK</address_id>
      <address_line>Test Address 4</address_line>
    </tst_customer_address>

    <tst_customer_address>
      <address_customer_no>501</address_customer_no>
      <address_id>WORK2</address_id>
      <address_line>Test Address 5</address_line>
    </tst_customer_address>
    
  </data>
</dataexport>