{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "transactionGroup": "C: ${CustomerNo}", "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}, "actions": {"DoPerformBoundAction": {"name": "DoPerformBoundAction", "transactionGroup": "BA: ${TestParam}, ${CustomerNo}"}}, "functions": {"DoPerformBoundFunction": {"name": "DoPerformBoundFunction"}}}, "TstCustomerOfflineQuery": {"name": "TstCustomerOfflineQuery", "CRUD": "Read", "luname": "TstCustomerOfflineQuery", "syncpolicy": {"type": "None"}, "offlinequery": {"from": {"entity": "TstCustomer", "alias": "c"}, "select": {"columns": [{"name": "c.<PERSON><PERSON><PERSON><PERSON>"}]}}, "keys": ["CustomerNo"], "transactionGroup": "C: ${CustomerNo}", "attributes": {"CustomerNo": {"datatype": "Text"}}}}, "actions": {"DoPerformAction": {"name": "DoPerformAction", "transactionGroup": "A: ${TestParam}"}}, "procedures": {"Function<DoSaveLocal>": {"name": "DoSaveLocal", "type": "Function", "params": [{"name": "Record"}], "layers": [{"execute": [{"call": {"method": "saveLocal", "args": {"name": "Record"}}}]}]}, "Function<DoSaveAndSend>": {"name": "DoSaveAndSend", "type": "Function", "params": [{"name": "Record"}], "layers": [{"execute": [{"call": {"method": "saveAndSend", "args": {"name": "Record"}}}]}]}, "Function<DoDeleteLocal>": {"name": "DoDeleteLocal", "type": "Function", "params": [{"name": "Record"}], "layers": [{"execute": [{"call": {"method": "deleteLocal", "args": {"name": "Record"}}}]}]}, "Function<DoDeleteAndSend>": {"name": "DoDeleteAndSend", "type": "Function", "params": [{"name": "Record"}], "layers": [{"execute": [{"call": {"method": "deleteAndSend", "args": {"name": "Record"}}}]}]}, "Action<DoPerformAction>": {"name": "DoPerformAction", "type": "Action", "params": [{"name": "TestParam"}], "layers": [{"execute": [{"call": {"method": "performAction"}}]}]}, "Action<TstCustomer.DoPerformBoundAction>": {"name": "TstCustomer.DoPerformBoundAction", "type": "Action", "params": [{"name": "Record"}, {"name": "TestParam"}], "layers": [{"execute": [{"call": {"method": "performAction"}}]}]}, "Action<TstCustomerOfflineQuery.DoPerformBoundAction>": {"name": "TstCustomerOfflineQuery.DoPerformBoundAction", "type": "Action", "params": [{"name": "Record"}, {"name": "TestParam"}], "layers": [{"execute": [{"call": {"method": "performAction"}}]}]}, "Function<TstCustomer.DoPerformBoundFunction>": {"name": "TstCustomer.DoPerformBoundFunction", "type": "Function", "params": [{"name": "Record"}, {"name": "TestParam"}], "layers": [{"execute": [{"call": {"method": "return", "args": {"return": {"+": [{"var": "TestParam"}, "|", {"var": "Record.CustomerNo"}, "|end"]}}}}]}]}, "Function<TstCustomerOfflineQuery.DoPerformBoundFunction>": {"name": "TstCustomerOfflineQuery.DoPerformBoundFunction", "type": "Function", "params": [{"name": "Record"}, {"name": "TestParam"}], "layers": [{"execute": [{"call": {"method": "return", "args": {"return": {"+": [{"var": "TestParam"}, "|", {"var": "Record.CustomerNo"}, "|end"]}}}}]}]}}}}