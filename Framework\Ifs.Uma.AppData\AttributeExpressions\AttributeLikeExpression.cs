﻿using System;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;
using Ifs.Uma.Utility.Formatters;
using TypeCode = Ifs.Uma.Utility.TypeCode;

namespace Ifs.Uma.AppData.AttributeExpressions
{
    public sealed class AttributeLikeExpression : AttributeExpression
    {
        public override AttributeExpressionType Type => AttributeExpressionType.Like;
        public string AttributeName { get; }
        public string Value { get; }

        private Regex _matchRegex;

        internal AttributeLikeExpression(string attributeName, string value)
        {
            if (attributeName == null) throw new ArgumentNullException(nameof(attributeName));
            if (value == null) throw new ArgumentNullException(nameof(value));

            AttributeName = attributeName;
            Value = value;
        }

        internal override Expression ToExpression(EntityDataSource dataSource)
        {
            AttributePathInfo attribute = AttributePathInfo.Get(dataSource.Metadata, dataSource.ProjectionName, dataSource.EntityName, AttributeName);
            Expression columnAccess = attribute == null ? IfsExpression.VarAccess(AttributeName) : (Expression)IfsExpression.AttributeAccess(attribute);

            if (attribute?.Member.ColumnType == typeof(DateTime?))
            {
                Expression formatString = Expression.Constant(DateFormatter.DateFormatToFormatString(attribute.Member.DateFormat));
                columnAccess = IfsExpression.Method(InternalFunctions.DbFormatDateTimeFunctionName, new[] { columnAccess, formatString });
            }
            
            Expression valueExp = Expression.Constant(new DynamicValue(Value));
            return IfsExpression.Like(columnAccess, valueExp);
        }

        internal override bool Match(EntityDataSource dataSource, EntityRecord record)
        {
            if (dataSource == null) throw new ArgumentNullException(nameof(dataSource));
            if (record == null) throw new ArgumentNullException(nameof(record));

            AttributePathInfo attribute = AttributePathInfo.Get(dataSource.Metadata, dataSource.ProjectionName, dataSource.EntityName, AttributeName);
            if (attribute != null && CanLikeOnAttribute(attribute))
            {
                object recordValue = record[attribute];

                string strRecordValue = null;
                if (recordValue is DateTime)
                {
                    IValueFormatter formatter = AttributeFormatter.For(attribute.Member);
                    strRecordValue = formatter.Format(recordValue);
                }
                else
                {
                    strRecordValue = ObjectConverter.ToString(recordValue);
                }
                
                if (strRecordValue != null)
                {
                    return MatchRecordValue(strRecordValue);
                }
            }

            return false;
        }

        private bool MatchRecordValue(string toSearch)
        {
            if (_matchRegex == null)
            {
                string escaped = Regex.Escape(Value);
                string converted = escaped.Replace('_', '.').Replace("%", ".*");
                _matchRegex = new Regex(@"\A" + converted + @"\z", RegexOptions.Singleline | RegexOptions.IgnoreCase, TimeSpan.FromSeconds(10));
            }

            return _matchRegex.IsMatch(toSearch);
        }

        private static bool CanLikeOnAttribute(AttributePathInfo attribute)
        {
            switch (TypeHelper.ValidateDbType(TypeHelper.GetNonNullableType(attribute.Member.ColumnType)))
            {
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Enumeration:
                case TypeCode.String:
                case TypeCode.DateTime:
                    return true;
                default:
                    return false;
            }
        }

        public override string ToString()
        {
            return $"{AttributeName} LIKE '{Value}'";
        }
    }
}
