﻿using System.Runtime.Serialization;
using Ifs.Uma.UI.Navigation;

namespace Ifs.Uma.Framework.UI.Navigation
{
    [DataContract]
    public class SyncMonitorNavParams : NavigationParameter
    {
        [DataMember]
        public SyncPageTab SelectedSyncPageTab { get; set; }

        public SyncMonitorNavParams(SyncPageTab selectedSyncPageTab)
        {
            SelectedSyncPageTab = selectedSyncPageTab;
        }
    }

    public enum SyncPageTab
    {
        SyncMonitor,
        Transactions
    }
}
