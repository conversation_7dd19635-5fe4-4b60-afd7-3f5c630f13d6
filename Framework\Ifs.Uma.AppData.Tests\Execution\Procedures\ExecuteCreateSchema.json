{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {"Function<CreateCustomer>": {"name": "CreateCustomer", "type": "Function", "layers": [{"vars": [{"name": "Var1"}], "execute": [{"call": {"method": "create", "args": {"entity": "TstCustomer"}}, "assign": "Var1"}, {"call": {"method": "return", "args": {"name": "Var1"}}}]}]}, "EntityPrepare<TstCustomer>": {"name": "TstCustomer", "type": "EntityPrepare", "params": [{"name": "Record", "dataType": "Structure", "subType": "TstCustomer"}], "layers": [{"execute": [{"call": {"method": "set", "args": {"value": "TestPrepareValue"}}, "assign": "Record.CustomerName"}, {"call": {"method": "return", "args": {"name": "Record"}}}]}]}}}}