// Shared
Platforms=iOS,Android
Name=IFS CRM Companion
AppName=CrmCompanionApp
RedirectUri=ifscrmcompanion
RemoteAssistance=false
SignatureService=false
LocationEnabled=false
LidarService=false
PushNotification=true

// iOS
iOSDisplayName=IFS CRM Companion
// Below must be the one having the InHouse suffix, the build script will remove it when building the store app
BundleIdentifier=com.ifs.cloud.CRMCompanion.InHouse
BundleName=IFS CRM Companion

// NOTE: iOS usage descriptions are always needed since we don't remove code for things like media attachments and location APIs
// So it's okay to add some dummy text for apps that don't really use those features
NSLocationWhenInUseUsageDescription=This application requires location services to work.
NSLocationAlwaysAndWhenInUseUsageDescription=This application requires location services to work.
NSCameraUsageDescription=This application requires access to the camera to capture media attachments.
NSPhotoLibraryUsageDescription=This app needs access to photos to pick media attachments.
NSPhotoLibraryAddUsageDescription=This app needs access to save media and documents.
NSMicrophoneUsageDescription=This is used for remote assistance calls.

// Android
AndroidDisplayName=IFS CRM Companion
AndroidPackageName=com.ifs.cloud.CRMCompanion

// Windows
WindowsDisplayName=IFS CRM Companion
WindowsDescription=IFS CRM Companion
WindowsShortName=IFS CRM Companion
IdentityName=IFS.IFSCRMCompanion
PhoneProductId=8416fb11-8671-4035-8637-09fa5d140701
// Below are in Package.StoreAssociation and Package.xml
ReservedName=IFS Aurena Native 10 IBS
LandingUrl=https://developer.microsoft.com/dashboard/Application?appId=9NT07CQC9MHN