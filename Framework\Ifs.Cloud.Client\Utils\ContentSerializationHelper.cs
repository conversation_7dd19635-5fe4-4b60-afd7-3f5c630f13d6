﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  2012-02-02 PKULLK Created.
#endregion

using Ifs.Cloud.Client.Comm;
using System.IO;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ifs.Cloud.Client.Utils
{
    /// <summary>
    /// Serializes/deserializes between a given type and json
    /// </summary>
    public static class ContentSerializationHelper
    {
        private static readonly DataContractJsonSerializer StringJsonSerializer = new DataContractJsonSerializer(typeof(byte[]));        

        internal static async Task<T> DeserializeResponseAsync<T>(CallResponse response)
        {
            if (response != null)
            {
                var content = await response.GetBinaryContent();

                //TODO: dispose of this properly at some point, but for a workaround dispose manually here
                response.Dispose();

                return DeserializeJsonBinary<T>(content);
            }
            else
            {
                return default(T);
            }
        }

        internal static T DeserializeJsonString<T>(string content)
        {
            using (MemoryStream contentStream = new MemoryStream(Encoding.UTF8.GetBytes(content)))
            {
                contentStream.Position = 0;

                // contents are json-wrapped as a string because of a WCF limitation at cloud side
                string graphSource = StringJsonSerializer.ReadObject(contentStream) as string;                

                DataContractJsonSerializer tJsonSerializer = new DataContractJsonSerializer((typeof(T)));
                using (MemoryStream graphSourceStream = new MemoryStream(Encoding.UTF8.GetBytes(graphSource)))
                {
                    graphSourceStream.Position = 0;
                    return (T)tJsonSerializer.ReadObject(graphSourceStream);
                }
            }
        }

        public static T DeserializeJsonBinary<T>(byte[] content)
        {
            using (MemoryStream contentStream = new MemoryStream(content))
            {
                contentStream.Position = 0;
                DataContractJsonSerializer tJsonSerializer = new DataContractJsonSerializer((typeof(T)));                
                return (T)tJsonSerializer.ReadObject(contentStream);
            }
        }
    }
}
