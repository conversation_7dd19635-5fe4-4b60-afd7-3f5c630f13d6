<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent">
  <Ifs.Uma.Framework.UI.Commands.CommandBlockView
    android:id="@+id/CommandButtonPanel"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_alignParentTop="true" 
    android:gravity="end" />
  <LinearLayout
    android:id="@+id/PageContent"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:animateLayoutChanges="true"
    android:layout_alignParentBottom="true"
    android:layout_below="@id/CommandButtonPanel" />
  <ProgressBar
    style="?android:attr/progressBarStyle"
    android:id="@+id/progressbar_loading"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_centerHorizontal="true"
    android:layout_alignParentBottom="true"
    android:indeterminateTint="?attr/colorPrimary"
    android:visibility="gone"
    android:padding="8dp"
    android:layout_marginBottom="64dp" />
</RelativeLayout>
