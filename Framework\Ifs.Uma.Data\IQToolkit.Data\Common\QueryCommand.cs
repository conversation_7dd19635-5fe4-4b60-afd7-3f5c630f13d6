﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace IQToolkit.Data.Common
{
    internal class QueryCommand
    {
        string commandText;
        ReadOnlyCollection<QueryParameter> parameters;

        public QueryCommand(string commandText, IEnumerable<QueryParameter> parameters)
        {
            this.commandText = commandText;
            this.parameters = parameters.ToReadOnly();
        }

        public string CommandText
        {
            get { return this.commandText; }
        }

        public ReadOnlyCollection<QueryParameter> Parameters
        {
            get { return this.parameters; }
        }

        public override string ToString()
        {
            return "'" + CommandText + "'";
        }
    }

    internal class QueryParameter
    {
        string name;
        Type type;

        public QueryParameter(string name, Type type)
        {
            this.name = name;
            this.type = type;
        }

        public string ValueName
        {
            get { return this.name; }
        }

        public Type ValueType
        {
            get { return this.type; }
        }
    }
}
