﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Utility;
using NUnit.Framework;

namespace Ifs.Uma.Database.Tests
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Maintainability", "CA1506:AvoidExcessiveClassCoupling")]
    [TestFixture]
    public class DatabaseTests : IDisposable
    {
        static DatabaseTests()
        {
            PlatformServicesTest.Initialize();
        }

        public DatabaseTests()
        {
            _logger = new DebugLogger();
        }

        [SetUp]
        protected virtual void BeforeTest()
        {
            const string DllPath = "Ifs.Uma.Database.SQLite, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null";
            Environment.CurrentDirectory = TestContext.CurrentContext.TestDirectory;

            // select your DBMS
            _factory = DbProviderFactory.Create(DllPath, _logger);
            Assert.IsNotNull(_factory);
            // Select your mapping source (no custom fields)
            _source = new AttributeMappingSource(null, _logger, null);
            Assert.IsNotNull(_source);

            // some housekeeping
            _model = _source.GetModel(typeof(TestDataContext));
            Assert.IsNotNull(_model);
            _table = _model.GetTable(typeof(TestRow));
            Assert.IsNotNull(_table);
            _descCol = _table.DataMembers.FirstOrDefault(x => x.PropertyName == "Description");
            Assert.IsNotNull(_descCol);
            _idCol = _table.DataMembers.FirstOrDefault(x => x.PropertyName == "RowId");
            Assert.IsNotNull(_idCol);
            Assert.IsTrue(_idCol.AutoIncrement);
        }

        [TearDown]
        protected virtual void AfterTest()
        {
            _factory.Dispose();
        }

        // include some invalid attributes (ignored) 
        // and repeat attributes (last one wins - JVB: actually it looks like an arbitrary one wins)
        [EnumServerNaming(Convention = NamingConvention.TitleCase)]
        [EnumServerNaming(EnumType = typeof(TestEnum), Convention = NamingConvention.CamelCase)]
        [EnumServerNaming(EnumType = typeof(TestEnum), Convention = NamingConvention.TitleCaseUnderscore)]
        [EnumServerValue(EnumValue = TestEnum.SecondOne)]
        [EnumServerValue(EnumValue = TestEnum.ThirdOne, ServerName = "#3")]
        [EnumServerValue(EnumValue = TestEnum.ThirdOne, ServerName = "numberThree")]
        [EnumServerValue(EnumValue = TestEnum.FourthOne, ServerName = "FOURTH_1")]
        [ColumnSyncRule(RowType=typeof(SurveyRow), PropertyName="SurveyId", Sync=SyncRule.Never)]
        [ColumnSyncRule(RowType = typeof(SurveyRow), PropertyName = "Description", Sync = SyncRule.Always)]
        [ColumnName(RowType = typeof(SurveyRow), PropertyName = "SurveyId", ColumnName = "id_for_survey")]
        [ColumnName(RowType = typeof(SurveyRow), PropertyName = "Description", ColumnName = "descr_one")]
        private static class EnumOverride
        { }

        private const string TestConnectionString = "Transaction Timeout=15;data source=t.db";
        private ILogger _logger;
        private DbProviderFactory _factory;
        private MappingSource _source;
        private IMetaModel _model;
        private IMetaTable _table;
        private IMetaDataMember _descCol;
        private IMetaDataMember _idCol;

        [Test]
        public void TestConnectionStringBuilder()
        {
            DbConnectionStringBuilder cs = _factory.CreateConnectionStringBuilder();
            Assert.IsNotNull(cs);
            cs["Data Source"] = "test.db";
            Assert.AreEqual(cs.ConnectionString, "Data Source=test.db;");
            Assert.AreEqual(cs["data source"], "test.db");
            Assert.AreEqual(cs.Password, null);
            cs.Password = null;
            cs.Password = string.Empty;
            cs.Password = "open sesame";
            Assert.AreEqual(cs["password"], "open sesame");
            Assert.AreEqual(cs["connect timeout"], "15");
            cs["connect timeout"] = "20";
            Assert.AreEqual(cs.ConnectionString, "Data Source=test.db; Password=open sesame; Connect Timeout=20;");
            Assert.Throws<FormatException>(() => { cs["Connect Timeout"] = "Wotcha"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["Connect Timeout"] = "0"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["Connect Timeout"] = "50000"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["Connect Timeout"] = "-1"; });
            Assert.AreEqual(cs["connect timeout"], "20");
            cs["connect Timeout"] = null;
            cs["Data Source"] = null;
            cs["password"] = null;
            Assert.AreEqual(cs["Cache"], "Shared");
            cs["cache"] = "shared";
            Assert.AreEqual(cs.ConnectionString, string.Empty);
            cs["cache"] = "private";
            Assert.AreEqual(cs.ConnectionString, "Cache=Private;");
            cs.ConnectionString = null;
            Assert.AreEqual(cs["busy timeout"], "0.1");
            cs["busy timeout"] = "0.5";
            Assert.Throws<FormatException>(() => { cs["Busy Timeout"] = "Wotcha"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["Busy Timeout"] = "0.00999"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["Busy Timeout"] = "30.0001"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["Busy Timeout"] = "-1"; });
            Assert.AreEqual(cs["busy timeout"], "0.5");
            Assert.AreEqual(cs.ConnectionString, "Busy Timeout=0.5;");
            Assert.AreEqual(cs["max pool size"], "10");
            cs["max pool size"] = "15";
            Assert.Throws<FormatException>(() => { cs["Max pool size"] = "Wotcha"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["max Pool size"] = "0"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["max POol size"] = "101"; });
            Assert.Throws<ArgumentOutOfRangeException>(() => { cs["max pool Size"] = "-1"; });
            Assert.AreEqual(cs.ConnectionString, "Busy Timeout=0.5; Max Pool Size=15;");
            Assert.Throws<ArgumentException>(() => { cs.ConnectionString = "xxx"; });

            cs.ConnectionString = "Data Source=temp.db";
            Assert.AreEqual(cs.ConnectionString, "Data Source=temp.db;");

            cs.ConnectionString = " CACHE  =  PRIVATE  ;   DATA SOURCE  =  ;    Max Pool Size = 5 ;  Busy Timeout=3";
            Assert.AreEqual(cs.ConnectionString, "Cache=Private; Max Pool Size=5; Busy Timeout=3;");
        }

        [Test]
        public void TestMappingSource()
        {
            IMetaEnumeration metaEnum = _source.GetEnumeration(typeof(TestEnum));
            Assert.IsNotNull(metaEnum);
            Assert.AreEqual(metaEnum.ServerValue(TestEnum.FirstOne), "FIRSTONE");
            Assert.AreEqual(metaEnum.ServerValue(TestEnum.SecondOne), "Second1");
            Assert.AreEqual(metaEnum.ServerValue(TestEnum.ThirdOne), "THIRDONE");
            Assert.AreEqual(metaEnum.ServerValue(TestEnum.FourthOne), "NUMBER_FOUR");
            IMetaTable tab1 = _source.GetModel(typeof(TestDataContext)).GetTable(typeof(SurveyRow));
            Assert.AreEqual(tab1.DataMembers.First(x => x.PropertyName == "SurveyId").ColumnName, "survey_id");
            Assert.AreEqual(tab1.DataMembers.First(x => x.PropertyName == "Description").ColumnName, "description");

            MappingSource source = new AttributeMappingSource(null, _logger, typeof(EnumOverride));
            metaEnum = source.GetEnumeration(typeof(TestEnum));
            Assert.IsNotNull(metaEnum);
            string t1 = metaEnum.ServerValue(TestEnum.FirstOne);
            Assert.IsTrue(t1 == "firstOne" || t1 == "First_One");
            Assert.AreEqual(metaEnum.ServerValue(TestEnum.SecondOne), "Second1");
            string t3 = metaEnum.ServerValue(TestEnum.ThirdOne);
            Assert.IsTrue(t3 == "numberThree" || t3 == "#3");
            Assert.AreEqual(metaEnum.ServerValue(TestEnum.FourthOne), "FOURTH_1");

            IMetaTable table = source.GetModel(typeof(TestDataContext)).GetTable(typeof(SurveyRow));
            IMetaDataMember mId = table.DataMembers.First(x => x.PropertyName == "SurveyId");
            IMetaDataMember mDesc = table.DataMembers.First(x => x.PropertyName == "Description");
            IMetaDataMember mName = table.DataMembers.First(x => x.PropertyName == "SurveyName");
            Assert.AreEqual(mId.Sync, SyncRule.Never);
            Assert.AreEqual(mDesc.Sync, SyncRule.Always);
            Assert.AreEqual(mName.Sync, SyncRule.Modified);
            Assert.AreEqual(mId.ColumnName, "id_for_survey");
            Assert.AreEqual(mDesc.ColumnName, "descr_one");
            Assert.AreEqual(mName.ColumnName, "survey_name");

            IEnumerable<string> ddl = _factory.Builder.BuildDdl(_model);
            Assert.IsNotNull(ddl);
            foreach (string cmd in ddl)
            {
                _logger.Trace("\r\n" + cmd);
            }
        }

        [Test]
        public void TestCreateDatabase()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            db.CreateDatabase(_model);
            Assert.AreEqual(db.GetStatus(), DbStatus.Valid);
            IEnumerable<string> descs = new string[] { null, "hello", "world", "wotcha", "cock" };
            IEnumerable<long> ids = db.TransactionF((cmd) => { return InsertDescs(cmd, descs); });
            Assert.IsNotNull(ids);
            long expectedId = 0;
            foreach (long id in ids)
            {
                expectedId++;
                Assert.AreEqual(id, expectedId);
            }
            CheckDescs(db, descs);
            TestDataContext dc = new TestDataContext(db);
            IEnumerable<TestRow> rows = dc.Tests.Where(x => x.RowId > 2).ToArray();
            Assert.IsNotNull(rows);
            foreach (TestRow r in rows)
            {
                Assert.IsNotNull(r);
                _logger.Trace("\r\nId: {0} Desc: {1}", ObjectConverter.ToString(r.RowId), r.Description);
            }
            IEnumerable<long> rowIds = new long[] { 1, 2, 5, 7 };
            IEnumerable<TestRow> tests = from t in dc.Tests where rowIds.Contains(t.RowId) select t;
            IEnumerable<TestRow> tests2 = tests.ToArray();
            foreach (TestRow r in tests2)
            {
                Assert.IsNotNull(r);
                _logger.Trace("\r\nId: {0} Desc: {1}", ObjectConverter.ToString(r.RowId), r.Description);
            }
        }

        private void CheckDescs(DbInternal db, IEnumerable<string> descs)
        {
            IEnumerable<string> selected = db.CommandF((cmd) => { return SelectDescs(cmd); });
            Assert.IsNotNull(selected);
            Assert.IsTrue(descs.SequenceEqual(selected));
        }

        private void CheckEntries(DbInternal db, IEnumerable<string> entries)
        {
            IEnumerable<string> selected = db.CommandF((cmd) => { return SelectEntries(cmd); });
            Assert.IsNotNull(selected);
            Assert.IsTrue(entries.Distinct().SequenceEqual(selected));
        }

        private IEnumerable<long> InsertDescs(DbCommand cmd, IEnumerable<string> descs)
        {
            IDbRowHandler<TestRow> handler = DbRowHandler<TestRow>.Create(cmd, _model, _factory.Builder);
            IEnumerable<TestRow> rows = descs.Select(x => new TestRow() { Description = x }).ToArray();
            Assert.AreEqual(handler.InsertAll(rows), MultipleResult.Success);
            return rows.Select(x => (long)x.RowId);
        }

        private IEnumerable<string> SelectDescs(DbCommand cmd)
        {
            IDbRowHandler<TestRow> handler = DbRowHandler<TestRow>.Create(cmd, _model, _factory.Builder);
            return handler.SelectAll().OrderBy(x => x.RowId).Select(x => x.Description);
        }

        private IEnumerable<string> SelectDescs(DbCommand cmd, TimeSpan d0)
        {
            IDbRowHandler<TestRow> handler = DbRowHandler<TestRow>.Create(cmd, _model, _factory.Builder);
            return handler.SelectAll(null, (r) => { Task.WaitAll(Task.Delay(d0)); }).OrderBy(x => x.RowId).Select(x => x.Description);
        }

        private IEnumerable<string> SelectEntries(DbCommand cmd)
        {
            IDbRowHandler<RoamingProfileValue> handler = DbRowHandler<RoamingProfileValue>.Create(cmd, _model, _factory.Builder);
            return handler.SelectAll().OrderBy(x => x.RowId).Select(x => x.ProfileEntry);
        }

        [Test]
        public void TestLinqSelect()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            db.Factory.TraceFlag = true;
            TestDataContext dc = new TestDataContext(db);
            dc.CreateDatabase();

            IEnumerable<SurveyRow> surveys2 =
                from s in dc.Surveys
                join m in (from d in dc.SurveyMobileDetailses
                           where d.MobileWorkflow == SurveyMobileWorkFlow.Employee
                           select d)
                on s.SurveyId equals m.SurveyId
                select s;
            surveys2 = surveys2.ToArray();

            var mobileDetail2 = dc.SurveyMobileDetailses.Where(x => x.MobileWorkflow == null);
            var details2 = mobileDetail2.ToArray();
            Assert.IsNotNull(details2);

            IEnumerable<SurveyMobileDetailsRow> mobileDetail = dc.SurveyMobileDetailses.Where(x => x.MobileWorkflow == SurveyMobileWorkFlow.Employee);
            var details = mobileDetail.ToArray();

            Assert.IsNotNull(details);
            IEnumerable<SurveyRow> surveys = (from s in dc.Surveys
                                              from m in mobileDetail
                                              where s.SurveyId == m.SurveyId
                                              select s);
            IEnumerable<SurveyRow> surveys3 = surveys.ToArray();
            Assert.IsNotNull(surveys3);

            string[] surveyIds = dc.SurveyMobileDetailses.Where(x => x.MobileWorkflow == SurveyMobileWorkFlow.Employee).Select(x => x.SurveyId).ToArray();
            int count = dc.Surveys.Where(x => surveyIds.Contains(x.SurveyId)).Count();
            Assert.AreEqual(0, count);

            IEnumerable<string> ids = dc.SurveyMobileDetailses.Where(x => x.MobileWorkflow == SurveyMobileWorkFlow.Employee).Select(x => x.SurveyId);
            int count2 = dc.Surveys.Where(x => ids.Contains(x.SurveyId)).Count();
            Assert.AreEqual(0, count2);

            int count3 = (from s in dc.Surveys
                          join m in dc.SurveyMobileDetailses on s.SurveyId equals m.SurveyId
                          where m.MobileWorkflow == SurveyMobileWorkFlow.Employee
                          select s).Count();
            Assert.AreEqual(0, count3);

            int count4 = (from s in dc.Surveys
                          join m in
                               (from d in dc.SurveyMobileDetailses where d.MobileWorkflow == SurveyMobileWorkFlow.Employee select d)
                          on s.SurveyId equals m.SurveyId
                          select s).Count();
            Assert.AreEqual(0, count4);
        }

        [Test]
        public void TestLinqUpdate()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            db.Factory.TraceFlag = true;
            TestDataContext dc = new TestDataContext(db);
            dc.CreateDatabase();
            TestRow t = new TestRow() { Description = "Well I'll be" };
            dc.Tests.InsertOnSubmit(t);
            AppsTestRow a = new AppsTestRow() { OptionalEnum = TestEnum.FourthOne };
            dc.AppsTests.InsertOnSubmit(a);
            IChangeSet s = dc.GetChangeSet();
            Assert.IsNotNull(s);
            Assert.IsNotNull(s.Deletes);
            Assert.IsFalse(s.Deletes.Any());
            Assert.IsNotNull(s.Updates);
            Assert.IsFalse(s.Updates.Any());
            Assert.IsNotNull(s.Inserts);
            Assert.IsTrue(s.Inserts.Any());
            // AppsTestRow.Description is mandatory but not set.
            try
            {
                dc.SubmitChanges();
                Assert.Fail();
            }
            catch (DbException)
            {
                // Success
            }
            Assert.AreEqual(0, t.RowId);
            Assert.AreEqual(0, a.RowId);
            a.Description = "!";
            dc.SubmitChanges();
            Assert.AreEqual(1, t.RowId);
            Assert.AreEqual(1, a.RowId);
            Assert.IsFalse(dc.GetChangeSet().Inserts.Any());

            // just attach but don't change anything
            dc.Tests.Attach(t);
            dc.AppsTests.Attach(a);
            Assert.IsTrue(dc.GetChangeSet().Updates.Any());
            dc.SubmitChanges();
            Assert.IsFalse(dc.GetChangeSet().Updates.Any());
            dc.Tests.Attach(t);
            dc.AppsTests.Attach(a);
            t.Description = "Wotcha";
            a.OptionalEnum = TestEnum.ThirdOne;
            TestRow t0 = dc.Tests.GetOriginalRow(t);
            Assert.AreEqual(t.RowId, t0.RowId);
            Assert.AreEqual("Well I'll be", t0.Description);
            AppsTestRow a0 = dc.AppsTests.GetOriginalRow(a);
            Assert.AreEqual(TestEnum.FourthOne, a0.OptionalEnum);
            dc.SubmitChanges();
            dc.AppsTests.Attach(a);
            a.OptionalEnum = null;
            a.OptionalStamp = new DateTime(2000, 1, 7, 9, 12, 37);
            dc.SubmitChanges();
            a.OptionalEnum = TestEnum.FirstOne;
            a = (from x in dc.AppsTests select x).First();
            Assert.IsFalse(a.OptionalEnum.HasValue);
        }

        [Test]
        public void CreateAppsDatabase()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            TestDataContext dc = new TestDataContext(db);
            dc.CreateDatabase();
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Maintainability", "CA1506:AvoidExcessiveClassCoupling")]
        [Test]
        public void TestAppsSelect()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            TestDataContext dc = new TestDataContext(db);
            dc.CreateDatabase();
            //string woNo = "wotcha";
            //var query = from material in dc.MobileMaterialRequisitionLines
            //            join salesPart in dc.SalesParts on new { material.Contract, material.CatalogNo } equals new { salesPart.Contract, salesPart.CatalogNo }
            //            where material.WoNo == woNo
            //            select new Models.WoMaterial()
            //            {
            //                Material = material,
            //                SalesPart = salesPart
            //            };
            //IEnumerable<Models.WoMaterial> materials = query.ToArray();
            //Assert.IsNotNull(materials);

            var separateObjects = from o in dc.EquipmentObjects
                                  join swo in dc.MobileSeparateWorkOrders on
                                      new { o.Contract, o.MchCode } equals new { Contract = swo.MchCodeContract, swo.MchCode }
                                  select o;

            var routeObjects = from o in dc.EquipmentObjects
                               join ra in dc.MobileRouteActions on
                                   new { o.Contract, o.MchCode } equals new { Contract = ra.MchCodeContract, ra.MchCode }
                               select o;

            var objects = separateObjects.Union(routeObjects).ToArray();
            Assert.IsNotNull(objects);
        }

        [Test]
        public void TestAppsTransition()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            TestDataContext dc = new TestDataContext(db);
            dc.CreateDatabase();
            EquipmentCriticalityRow e = new EquipmentCriticalityRow
            {
                Criticality = "urgent",
                Description = "do it now"
            };
            dc.EquipmentCriticalities.InsertOnSubmit(e);
            EquipmentCriticalityRow e0 = new EquipmentCriticalityRow
            {
                Criticality = "california",
                Description = "do it whenever"
            };
            dc.EquipmentCriticalities.InsertOnSubmit(e0);
            dc.SubmitChanges();
            dc.EquipmentCriticalities.Attach(e);
            e.Description = "do it NOW or you're fired";
            dc.EquipmentCriticalities.Attach(e0);
            e0.Description = "the laid back approach";
            dc.SubmitChanges();
            dc.EquipmentCriticalities.DeleteOnSubmit(e0);
            dc.SubmitChanges();
        }

        [ColumnSyncRule]
        [ColumnSyncRule(RowType = typeof(AppsTestRow), PropertyName = "AnswerId", Sync = SyncRule.Never)]
        [ColumnSyncRule(RowType = typeof(AppsTestRow), PropertyName = "Description", Sync = SyncRule.Always)]
        private static class SyncOverride
        { }

        [Test]
        public void TestTransitionSync()
        {
            MappingSource source = new AttributeMappingSource(null, _logger, typeof(SyncOverride));
            DbInternal db = new DbInternal(_factory, TestConnectionString, source, _logger);
            //m_factory.TraceFlag = true;
            TestAppsContext dc = new TestAppsContext(db);
            dc.CreateDatabase();
            AppsTestRow r = new AppsTestRow() { AnswerId = 25L, Description = "?" };
            dc.AppsTests.InsertOnSubmit(r);
            dc.SubmitChanges();

            IEnumerable<TransitionRowField> fields;
            fields = (from s in dc.TransitionChanges where s.TransitionRowId == 1 select s).ToArray();
            Assert.IsNotNull(fields.FirstOrDefault(x => x.FieldName == "description"));
            Assert.IsNull(fields.FirstOrDefault(x => x.FieldName == "answer_id"));

            dc.AppsTests.Attach(r);
            r.OptionalStamp = DateTime.Now;
            dc.SubmitChanges();

            fields = (from s in dc.TransitionChanges where s.TransitionRowId == 2 select s).ToArray();
            Assert.IsNotNull(fields.FirstOrDefault(x => x.FieldName == "description"));
            Assert.IsNull(fields.FirstOrDefault(x => x.FieldName == "answer_id"));
            Assert.IsNotNull(fields.FirstOrDefault(x => x.FieldName == "optional_stamp"));

            dc.AppsTests.DeleteOnSubmit(r);
            dc.SubmitChanges();
            fields = (from s in dc.TransitionChanges where s.TransitionRowId == 3 select s).ToArray();
            Assert.IsNotNull(fields.FirstOrDefault(x => x.FieldName == "description"));
            Assert.IsNull(fields.FirstOrDefault(x => x.FieldName == "answer_id"));
        }

        [Test]
        public void TestBytes()
        {
            TestSerialise(null);
            TestSerialise(123456);
            TestSerialise("hello world \u2024");
            TestSerialise(1.56789);
            TestSerialise(true);
            TestSerialise(TestEnum.SecondOne);
            TestSerialise(DateTimeOffset.Now);
            TestSerialise(TimeSpan.FromHours(14.56789));
            TestSerialise(Guid.NewGuid());
            TestSerialise(1234987u);
            TestSerialise(987398578768768L);
            TestSerialise(1238763UL);
            TestSerialise(DateTime.Now);
            TestSerialise(new decimal(123.45678));
            TestSerialise(string.Empty);
            TestSerialise(new byte[0]);
            TestSerialise(new byte[] { 1, 2, 3, 4, 5 });
        }

        private static void TestSerialise(object value)
        {
            byte[] bytes = BinarySerializerHelper.ObjectToByteArray(value);
            if (value == null)
            {
                Assert.IsNull(bytes);
            }
            else
            {
                Assert.IsNotNull(bytes);
                Assert.IsTrue(bytes.Length > 0);
            }
            object result = BinarySerializerHelper.ByteArrayToObject(bytes);
            byte[] bValue = value as byte[];
            if (bValue != null)
            {
                Assert.IsTrue(bValue.SequenceEqual((byte[])result));
            }
            else
            {
                Assert.AreEqual(value, result);
            }
        }

        [Test]
        public void TestExportImport()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            //db.Factory.TraceFlag = true;
            TestDataContext dc = new TestDataContext(db);
            dc.CreateDatabase();
            string[] descs = new string[] { "hello", "world", "wotcha", "cock", "0 < 1", "1 > 0" };
            foreach (string desc in descs)
            {
                TestRow t = new TestRow() { Description = desc };
                dc.Tests.InsertOnSubmit(t);
            }
            AppsTestRow[] a = new AppsTestRow[]
            {
                new AppsTestRow()
                {
                    AnswerId = 1,
                    Description = "woo hoo",
                    OptionalEnum = TestEnum.ThirdOne
                },
                new AppsTestRow()
                {
                    AnswerId = 5,
                    Description = "What's up?",
                    OptionalEnum = TestEnum.SecondOne
                },
                new AppsTestRow()
                {
                    AnswerId = 9,
                    Description = "The quick brown fox jumps over the lazy dog.",
                    OptionalStamp = DateTime.Now
                },
                new AppsTestRow()
                {
                    AnswerId = 19,
                    Description = "To be or not to be that is the question",
                }
            };
            dc.AppsTests.InsertAllOnSubmit(a);
            dc.SubmitChanges();

            string export = ExportTestData(db);
            _logger.Trace(export);

            // TODO: Fix recreation of database
            //dc.CreateDatabase();
            //Assert.IsFalse(dc.Tests.Any());

            //ImportTestData(db, export);

            //string[] t0 = (from x in dc.Tests orderby x.RowId select x.Description).ToArray();
            //Assert.IsTrue(descs.SequenceEqual(t0));
            //IEnumerable<AppsTestRow> a0 = (from x in dc.AppsTests orderby x.AnswerId select x).ToArray();
            //Assert.IsTrue(a.SequenceEqual(a0, AppsTestRowComparer.Instance));
        }

        private class AppsTestRowComparer : IEqualityComparer<AppsTestRow>
        {
            public static IEqualityComparer<AppsTestRow> Instance { get { return g_instance; } }

            protected AppsTestRowComparer()
            { }

            private static IEqualityComparer<AppsTestRow> g_instance = new AppsTestRowComparer();

            public bool Equals(AppsTestRow x, AppsTestRow y)
            {
                if (x == y) return true;
                if (x == null || y == null) return false;
                return x.AnswerId == y.AnswerId &&
                    x.Description == y.Description &&
                    x.OptionalStamp == y.OptionalStamp &&
                    x.OptionalEnum == y.OptionalEnum;
            }

            public int GetHashCode(AppsTestRow obj)
            {
                return obj != null ? obj.AnswerId.GetHashCode() ^
                    (obj.Description != null ? obj.Description.GetHashCode() : 0) ^
                    obj.OptionalEnum.GetHashCode() ^ obj.OptionalStamp.GetHashCode() :
                    0;
            }
        }

        private static string ExportTestData(DbInternal db)
        {
            TestDataContext dc1 = new TestDataContext(db);
            StringBuilder sb = new StringBuilder();
            using (XmlWriter writer = XmlWriter.Create(sb, new XmlWriterSettings() { Indent = true }))
            {
                dc1.ExportData(writer);
            }
            return sb.ToString();
        }

        private static void ImportTestData(DbInternal db, string xml)
        {
            TestDataContext dc = new TestDataContext(db);
            StringReader s = null;
            try
            {
                s = new StringReader(xml);
                using (XmlReader r = XmlReader.Create(s))
                {
                    s = null;
                    dc.ImportData(r);
                }
            }
            finally
            {
                if (s != null)
                {
                    s.Dispose();
                    s = null;
                }
            }
            dc.SubmitChanges(false);
        }

        [Test]
        public void TestSelectAllDeferred()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            db.CreateDatabase(_model);
            Assert.AreEqual(db.GetStatus(), DbStatus.Valid);
            IEnumerable<TestRow> rows = CreateTestRows(new string[] { "hello1", "world1", "wotcha1", "cock1" });

            db.TransactionA((cmd) => { InsertDescs(cmd, rows, TimeSpan.Zero); });

            SelectDescsDeferred(db, rows);
        }
        
        private void SelectDescsDeferred(DbInternal db, IEnumerable<TestRow> rows)
        {
            IEnumerable<string> descs = db.CommandF((cmd) => { return SelectDescsDeferred(cmd); });
            Assert.IsNotNull(descs);
            Assert.IsTrue(descs.SequenceEqual(rows.Select(x => x.Description)));
        }

        private IEnumerable<string> SelectDescsDeferred(DbCommand cmd)
        {
            IDbRowHandler<TestRow> handler = DbRowHandler<TestRow>.Create(cmd, _model, _factory.Builder);

            List<TestRow> results = new List<TestRow>();
            foreach (var item in handler.SelectAllDeferred())
            {
                results.Add(item);
            }

            return results.OrderBy(x => x.RowId).Select(x => x.Description);
        }

        [Test]
        public void TestDatabaseThreads()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            db.CreateDatabase(_model);
            Assert.AreEqual(db.GetStatus(), DbStatus.Valid);
            IEnumerable<TestRow> r1 = CreateTestRows(new string[] { "hello1", "world1", "wotcha1", "cock1" });
            IEnumerable<TestRow> r2 = CreateTestRows(new string[] { "hello2", "world2", "wotcha2", "cock2", "howdy2", "boys2" });
            TimeSpan d0 = TimeSpan.FromSeconds(1);
            TimeSpan d1 = TimeSpan.FromSeconds(0.3);
            TimeSpan d2 = TimeSpan.FromSeconds(0.3);
            TimeSpan d3 = TimeSpan.FromSeconds(0.5);
            TestThreads(db, r1, r2, d0, d1, d2, d3);
            foreach (TestRow r in r1.Concat(r2))
            {
                Assert.AreNotEqual(0, r.RowId);
            }
        }

        private void TestThreads(DbInternal db, IEnumerable<TestRow> r1, IEnumerable<TestRow> r2, TimeSpan d0, TimeSpan d1, TimeSpan d2, TimeSpan d3)
        {
            // start the first task
            Task t1 = Task.Run(() => { InsertDescs(db, r1, d1); });
            Task.WaitAll(Task.Delay(d0));
            Task t1a = Task.Run(() => { SelectDescs(db, d3); });
            // delay a bit before starting the second task
            Task.WaitAll(Task.Delay(d0));
            Task t2 = Task.Run(() => { InsertDescs(db, r2, d2); });
            Task.WaitAll(Task.Delay(d0));
            Task t3 = Task.Run(() => { SelectDescs(db, r1, r2); });
            // wait for the tasks to complete.
            Task.WaitAll(t1, t1a, t2, t3);
        }

        private static IEnumerable<TestRow> CreateTestRows(IEnumerable<string> descs)
        {
            return descs.Select(x => new TestRow() { Description = x }).ToArray();
        }

        private void InsertDescs(DbInternal db, IEnumerable<TestRow> rows, TimeSpan delay)
        {
            db.TransactionA((cmd) => { InsertDescs(cmd, rows, delay); });
        }

        private void InsertDescs(DbCommand cmd, IEnumerable<TestRow> rows, TimeSpan delay)
        {
            IDbRowHandler<TestRow> handler = DbRowHandler<TestRow>.Create(cmd, _model, _factory.Builder);
            foreach (TestRow row in rows)
            {
                Task.WaitAll(Task.Delay(delay));
                handler.InsertRow(row);
            }
        }

        private void SelectDescs(DbInternal db, TimeSpan d0)
        {
            IEnumerable<string> descs = db.CommandF((cmd) => { return SelectDescs(cmd, d0); });
            Assert.IsNotNull(descs);
        }

        private void SelectDescs(DbInternal db, IEnumerable<TestRow> r1, IEnumerable<TestRow> r2)
        {
            IEnumerable<string> descs = db.CommandF((cmd) => { return SelectDescs(cmd); });
            Assert.IsNotNull(descs);
            // we don't know whether the select will be executed before or after the second insert.
            // it all depends on which thread polls first.
            Assert.IsTrue(descs.SequenceEqual(r1.Select(x => x.Description)) ||
                descs.SequenceEqual(r1.Select(x => x.Description).Concat(r2.Select(x => x.Description))));
        }

        [Test]
        public void TestDatabaseLockingWithConcurrentThreads()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            db.CreateDatabase(_model);
            Assert.AreEqual(db.GetStatus(), DbStatus.Valid);

            Task scalarCommandsTask = Task.Run(() =>
            {
                ExecuteScalarCommands(db);
            });

            Task scalarTransactionsTask = Task.Run(() =>
            {
                ExecuteScalarTransactions(db);
            });

            Task.WaitAll(scalarCommandsTask, scalarTransactionsTask);
            if (scalarCommandsTask.IsFaulted)
            {
                throw scalarCommandsTask.Exception;
            }

            if (scalarTransactionsTask.IsFaulted)
            {
                throw scalarTransactionsTask.Exception;
            }
        }

        private void ExecuteScalarCommands(DbInternal db)
        {
            for (int i = 0; i < 1000; i++)
            {
                db.CommandA((cmd) =>
                {
                    cmd.CommandText = "SELECT COUNT(*) FROM sqlite_master";
                    cmd.ExecuteScalar();
                });
            }
        }

        private void ExecuteScalarTransactions(DbInternal db)
        {
            for (int i = 0; i < 1000; i++)
            {
                db.TransactionA((cmd) =>
                {
                    cmd.CommandText = "SELECT COUNT(*) FROM sqlite_master";
                    cmd.ExecuteScalar();
                });
            }
        }

        //FIXME: [Test]
        public void UpdateViaRowId()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            db.CreateDatabase(_model);
            Assert.AreEqual(db.GetStatus(), DbStatus.Valid);

            TestDataContext dc = new TestDataContext(db);
            AppsTestRow testRow = new AppsTestRow() { AnswerId = 5, Description = "Hello", OptionalEnum = TestEnum.FourthOne };
            dc.AppsTests.InsertOnSubmit(testRow);
            dc.SubmitChanges(false);

            Assert.Greater(testRow.RowId, 0);

            // Change AnswerId so it no longer matches the row - the sync process could update 
            // the server primary key (AnswerId) on a row in the background but we still 
            // want changes to save based on the row ID when the user makes changes in the UI
            testRow.AnswerId = 6;

            dc.AppsTests.Attach(testRow);
            testRow.OptionalEnum = TestEnum.SecondOne;
            dc.SubmitChanges(false);

            AppsTestRow savedRow = dc.AppsTests.FirstOrDefault(x => x.RowId == testRow.RowId);

            Assert.AreEqual(savedRow.AnswerId, 5);
            Assert.AreEqual(savedRow.OptionalEnum, TestEnum.SecondOne);
        }

        [Test]
        public void UpdateViaServerKey()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            db.CreateDatabase(_model);
            Assert.AreEqual(db.GetStatus(), DbStatus.Valid);

            TestDataContext dc = new TestDataContext(db);
            AppsTestRow testRow = new AppsTestRow() { AnswerId = 5, Description = "Hello", OptionalEnum = TestEnum.FourthOne };
            dc.AppsTests.InsertOnSubmit(testRow);
            dc.SubmitChanges(false);

            long insertedRowId = testRow.RowId;
            Assert.Greater(insertedRowId, 0);

            // Reset the row so RowId no longer matches the row - the server key (AnswerId) should 
            // then be used to update the row instead
            testRow = new AppsTestRow() { AnswerId = 5, Description = "Hello", OptionalEnum = TestEnum.FourthOne };

            dc.AppsTests.Attach(testRow);
            testRow.OptionalEnum = TestEnum.SecondOne;
            dc.SubmitChanges(false);

            AppsTestRow savedRow = dc.AppsTests.First(x => x.AnswerId == 5);

            Assert.AreEqual(savedRow.RowId, insertedRowId);
            Assert.AreEqual(savedRow.AnswerId, 5);
            Assert.AreEqual(savedRow.OptionalEnum, TestEnum.SecondOne);
        }

        //FIXME: [Test]
        public void UpdateServerKey()
        {
            DbInternal db = new DbInternal(_factory, TestConnectionString, _source, _logger);
            _factory.TraceFlag = true;
            db.CreateDatabase(_model);
            Assert.AreEqual(db.GetStatus(), DbStatus.Valid);

            TestDataContext dc = new TestDataContext(db);
            AppsTestRow testRow = new AppsTestRow() { AnswerId = 5, Description = "Hello", OptionalEnum = TestEnum.FourthOne };
            dc.AppsTests.InsertOnSubmit(testRow);
            dc.SubmitChanges(false);

            dc.AppsTests.Attach(testRow);
            testRow.AnswerId = 6;
            testRow.OptionalEnum = TestEnum.SecondOne;
            dc.SubmitChanges(false);

            AppsTestRow savedRow = dc.AppsTests.First(x => x.RowId == testRow.RowId);

            Assert.AreEqual(savedRow.AnswerId, 6);
            Assert.AreEqual(savedRow.OptionalEnum, TestEnum.SecondOne);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~DatabaseTests()
        {
            Dispose(false);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2213:DisposableFieldsShouldBeDisposed", MessageId = "m_logger",
            Justification="False Positive")]
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                UsefulExtensions.ThreadSafeDispose(ref _logger);
                UsefulExtensions.ThreadSafeDispose(ref _factory);
            }
        }
    }
}
