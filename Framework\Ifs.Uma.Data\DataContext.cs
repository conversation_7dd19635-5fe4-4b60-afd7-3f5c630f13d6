﻿using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;

namespace Ifs.Uma.Data
{
    /// <summary>
    /// Derive from DataContext to build the application-specific Schema.
    /// Create a new DataContext for each Transition (Unit of Work)
    /// </summary>
    public partial class DataContext : DataContextBase
    {
        public DataContext(DbInternal db)
            : base(db)
        {
        }

        // This file is here just to define all the System tables in one place.
        // Any functionality that does not use any of these System tables belongs in DataContextBase
        // Any functionality that uses any of these system tables belongs in a separate file (partial class)

        public Table<Transition> Transitions { get { return GetTable<Transition>(); } }
        public Table<TransitionRow> TransitionRows { get { return GetTable<TransitionRow>(); } }
        public Table<TransitionRowField> TransitionChanges { get { return GetTable<TransitionRowField>(); } }
    }
}
