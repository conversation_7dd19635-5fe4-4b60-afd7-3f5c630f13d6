﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  2011-10-24 PKULLK Created.
#endregion

using System;
using System.Net;
using System.Net.Http;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Cloud.Client.Exceptions
{
    /// <summary>
    /// Represents an error that is encountered when accessing the cloud
    /// </summary>
    public class CloudException : Exception
    {
        #region Constants
        private static readonly char[] AppErrorHeaderValueSeparator = new char[] { ':' };
        #endregion

        #region Initialization
        private CloudException(string message, Exception cause)
            : base(message, cause)
        {
        }

        private CloudException(string message)
            : base(message)
        {
        }
        #endregion

        #region Factory methods
        
        /// <summary>
        /// Creates an exception from given cause
        /// </summary>
        /// <param name="cloudMessage">The error message</param>
        /// <param name="statusCode">Status code</param>
        /// <param name="cause">Cause</param>
        /// <returns>CloudException</returns>
        public static CloudException FromCause(string cloudMessage, HttpStatusCode statusCode, Exception cause, bool applicationError)
        {
            string message;
            CloudErrorType errorType;
            DecodeErrorMessage(cloudMessage, out message, out errorType);
            return new CloudException(message, cause)
            { 
                ErrorType = errorType, 
                StatusCode = statusCode,
                IsApplicationError = applicationError
            };
        }

        public static CloudException FromException(Exception ex)
        {
            if (ex is HttpRequestException)
            {
                if (!PlatformServices.Provider.IsNetworkAvailable())
                {
                    return FromCause(Strings.DeviceHasNoNetworkConnection, HttpStatusCode.InternalServerError, ex, false);
                }
                else
                {
                    return FromCause(Strings.ServerIsUnreachable, HttpStatusCode.InternalServerError, ex, false);
                }
            }
            else
            {
                return FromCause(Strings.ServerCommunicationWentWrong, HttpStatusCode.InternalServerError, ex, false);
            }
        }

        public CloudException Clone()
        {
            return new CloudException(Message, InnerException)
            {
                ErrorType = ErrorType,
                StatusCode = StatusCode
            };
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets the status code associated with this error
        /// </summary>
        public HttpStatusCode StatusCode { get; private set; }

        /// <summary>
        /// Gets the type of this error
        /// </summary>
        public CloudErrorType ErrorType { get; private set; }

        public bool IsApplicationError { get; private set; }

        /// <summary>
        /// Gets whether it is appropriate to retry a cloud call when this error was encountered.
        /// </summary>
        public bool ShouldRetry
        {
            get { return ErrorType == CloudErrorType.Unknown; }
        }

        public bool IsCredentialsError
        {
            get { return ErrorType == CloudErrorType.InvalidAuthorizationHeader || ErrorType == CloudErrorType.TokenRefreshRequired; }
        }

        public bool ShouldRetryAfterReauthentication
        {
            // Could be a CredentialsError or a SecurityError where the user does not have permissions
            // We cannot tell the difference between the two. The call should be tried again after
            // successfully reauthenticating and if it is still an issue then it is a permissions problem
            get { return StatusCode == HttpStatusCode.Unauthorized; }
        }

        #endregion

        #region Implementation methods
        private static void DecodeErrorMessage(string cloudMessage, out string message, out CloudErrorType errorType)
        {
            string[] errorComponents = cloudMessage.Split(AppErrorHeaderValueSeparator, StringSplitOptions.None);

            if (errorComponents.Length > 1)
            {
                bool errorTypeUnrecognised;
                errorType = DecodeErrorType(errorComponents[0], out errorTypeUnrecognised);

                if (errorTypeUnrecognised)
                {
                    //Display whole message as we don't know how to parse it
                    message = cloudMessage;
                }
                else
                {
                    message = errorComponents[1];
                    for (int i = 2; i < errorComponents.Length; i++)
                    {
                        message = message + ":" + errorComponents[i];
                    }
                }
            }
            else
            {
                errorType = CloudErrorType.Unknown;
                message = cloudMessage;
            }
        }

        private static CloudErrorType DecodeErrorType(string errorType, out bool errorTypeUnrecognised)
        {
            errorTypeUnrecognised = false;

            if (string.IsNullOrWhiteSpace(errorType))
            {
                errorTypeUnrecognised = true;
                return CloudErrorType.Unknown;
            }

            // IMPORTANT:
            // These constants are originally defined in the cloud framework
            // Should they are changed at cloud side, make sure to update here to reflect the changes.

            switch (errorType)
            {
#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
                case "ERROR_PROGATED_SYSTEM_EXCEPTION":        return CloudErrorType.PropagatedSystemError;
                case "ERROR_PROPAGATED_APPLICATION_EXCEPTION": return CloudErrorType.PropagatedApplicationError;

                case "EROR_RESOURCE_EXCEPTION":                return CloudErrorType.ResourceException;
                case "ERROR_APP_NOT_FOUND":                    return CloudErrorType.AppNotFound;
                case "ERROR_APP_RESOURCE_NOT_FOUND":           return CloudErrorType.AppResourceNotFound;
                case "ERROR_APP_VERSION":                      return CloudErrorType.AppVersion;
                case "ERROR_APP_CONFIG_NOT_FOUND":             return CloudErrorType.AppConfigurationNotFound;

                case "ERROR_INVALID_AUTHORIZATION_HEADER":     return CloudErrorType.InvalidAuthorizationHeader;
                case "ERROR_SYSTEM_DOES_NOT_EXIST":            return CloudErrorType.SystemDoesNotExist;
                case "ERROR_SYSTEM_BLOCKED":                   return CloudErrorType.SystemBlocked;
                case "ERROR_SESSION_TIMEOUT":                  return CloudErrorType.SessionTimeout;
                case "ERROR_SECURITY_EXCEPTION":               return CloudErrorType.SecurityError;

                case "APP_DEVICE_REGISTRATION_REQUEST_SENT":   return CloudErrorType.AppDeviceRegistrationRequestSent;
                case "APP_DEVICE_PENDING_APPROVAL":            return CloudErrorType.AppDevicePendingApproval;
                case "ERROR_APP_DEVICE_REJECTED":              return CloudErrorType.AppDeviceRejected;

                case "ERROR_CODE_REQUIRED":                    return CloudErrorType.SecretCodeRequired;
                case "ERROR_RENEW_SECRET":                     return CloudErrorType.RenewSecretKey;
                case "ERROR_TFA_REQUIRED":                     return CloudErrorType.TwoFactorAuthenticationRequired;
                case "ERROR_INVALID_CODE":                     return CloudErrorType.InvalidSecretCode;
                case "ERROR_OPEN_ID_TOKEN_EXPIRED":            return CloudErrorType.TokenRefreshRequired;
                case "ERROR_CLOUD_UNKNOWN_EXCEPTION":          return CloudErrorType.Unknown;
                case "ERROR_UNKNOWN":                          return CloudErrorType.Unknown;
                case "ERROR_META_DATA_TIMEOUT":                return CloudErrorType.MetaDataTimeOut;
                case "DATABASE_ERROR":                         return CloudErrorType.DatabaseError;
                case "ODATA_PROVIDER_ERROR":                   return CloudErrorType.ODataProviderError;
                case "REQUEST_ERROR":                          return CloudErrorType.RequestError;
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row
                default:
                    errorTypeUnrecognised = true;
                    return CloudErrorType.Unknown;
            }
        }
        #endregion
    }  
}
