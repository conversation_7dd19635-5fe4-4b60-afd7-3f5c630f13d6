﻿using System.Collections.Generic;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Tests;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution
{
    [TestFixture]
    public class ExecuteSetTests : FrameworkTest
    {
        [Test]
        public void SetByName()
        {
            var vars = new Dictionary<string, object>();
            vars["Var1"] = 3;

            var args = new CpiSetCallArgs();
            args.Name = "Var1";

            object result = DoSet(args, vars);
            Assert.AreEqual(3, result);
        }

        [Test]
        public void SetByValue()
        {
            var vars = new Dictionary<string, object>();
            var args = new CpiSetCallArgs();
            args.Value = 5.0;

            object result = DoSet(args, vars);
            Assert.AreEqual(5.0, result);
        }

        [Test]
        public void SetByExpression()
        {
            var vars = new Dictionary<string, object>();
            vars["Var1"] = "TestVal";

            object result = DoSetExpression(@"{'var':'Var1'}", vars);

            Assert.AreEqual("TestVal", result);
        }

        private object DoSetExpression(string json, Dictionary<string, object> vars)
        {
            JObject exp = JObject.Parse(json);

            var args = new CpiSetCallArgs();
            args.Expression = new CpiExpression { JsonLogic = exp };
            return DoSet(args, vars);
        }

        private object DoSet(CpiSetCallArgs args, Dictionary<string, object> vars)
        {
            TestExecutor executor = Resolve<TestExecutor>();

            ExecuteResult result = executor.Call(TestOfflineProjection, CpiExecuteCallMethod.Set, args, vars);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return result.Value;
        }
    }
}
