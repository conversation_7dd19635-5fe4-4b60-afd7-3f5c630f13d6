﻿using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Services;

namespace Ifs.Uma.Framework.Data
{
    public sealed class FileSelectorState
    {
        private readonly ViewState _viewState;
        private readonly string _fileSelectorName;

        public FileSelectorState(ViewState viewState, string fileSelectorName)
        {
            _viewState = viewState ?? new ViewState();
            _fileSelectorName = fileSelectorName ?? "UploadQueue";
        }
        
        public string UploadingFile
        {
            get => _viewState.TryGetValue(StateName("uploadingFile"), out object value) ? value as string : null;
            set => _viewState.Assign(StateName("uploadingFile"), value);
        }

        public IEnumerable<PickedFile> PickedFiles
        {
            get => _viewState.TryGetValue(StateName("$PickedFiles"), out object value) ? value as IEnumerable<PickedFile> : null;
            set => _viewState.Assign(StateName("$PickedFiles"), value);
        }

        public long? Count
        {
            get
            {
                _viewState.TryGetValue(StateName("count"), out object value);
                
                if (MetadataExtensions.TryConvertToType(CpiDataType.Integer, value, out object converted))
                {
                    return (long)converted;
                }

                return null;
            }
            set => _viewState.Assign(StateName("count"), value);
        }

        public bool? IsEmpty
        {
            get
            {
                _viewState.TryGetValue(StateName("isEmpty"), out object value);
                
                if (MetadataExtensions.TryConvertToType(CpiDataType.Boolean, value, out object converted))
                {
                    return (bool)converted;
                }

                return null;
            }
            set => _viewState.Assign(StateName("isEmpty"), value);
        }

        public string FileList
        {
            get
            {
                _viewState.TryGetValue(StateName("fileList"), out object value);

                if (value is string fileList)
                {
                    return fileList;
                }

                return null;
            }
            set => _viewState.Assign(StateName("fileList"), value);
        }

        public string InvalidFileList
        {
            get
            {
                _viewState.TryGetValue(StateName("invalidFileList"), out object value);

                if (value is string invalidFileList)
                {
                    return invalidFileList;
                }

                return null;
            }
            set => _viewState.Assign(StateName("invalidFileList"), value);
        }

        public string AcceptedExtensions
        {
            get
            {
                _viewState.TryGetValue(StateName("acceptedExtensions"), out object value);

                if (value is string acceptedExtensions)
                {
                    return acceptedExtensions;
                }

                return null;
            }
            set => _viewState.Assign(StateName("acceptedExtensions"), value);
        }

        public string StateName([CallerMemberName]string propertyName = null)
        {
            return ViewState.ViewStatePrefix + _fileSelectorName + "." + propertyName;
        }
    }
}
