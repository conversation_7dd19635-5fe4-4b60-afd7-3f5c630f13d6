﻿using System;

namespace Ifs.Uma.Comm.TouchApps
{
    public interface IIfsConnection : IDisposable
    {
        TouchAppsComms TouchAppsComms { get; }
    }

    public class IfsConnection : IIfsConnection
    {
        public TouchAppsComms TouchAppsComms { get; private set; }

        public IfsConnection(TouchAppsComms touchAppsComms)
        {
            if (touchAppsComms == null) throw new ArgumentNullException(nameof(touchAppsComms));
            
            TouchAppsComms = touchAppsComms;
        }

        public void Dispose()
        {
            if (TouchAppsComms != null)
            {
                TouchAppsComms.Dispose();
                TouchAppsComms = null;
            }
        }
    }
}
