
$changeLogDir = "$PSScriptRoot\Unreleased"

# JIRA ticket Number

$ticketNo = Read-Host -Prompt 'Enter JIRA ticket [8646 or MOBOFF-8646]'

if ([String]::IsNullOrWhiteSpace($ticketNo)) 
{
  $ticketNo = $null;
}
elseif (!($ticketNo -match '^[a-zA-Z]'))
{
  $ticketNo = "MOBOFF-$ticketNo";
}

Write-Host "Ticket: $ticketNo" -ForegroundColor Green

# Platform

$platform = Read-Host -Prompt 'Enter platform [A]ndroid [i/I]OS [W]indows [S]hared'

if ($platform -like 'A') 
{
  $platform = "Android";
}
elseif (($platform -like 'i') -or ($platform -like 'I'))
{
  $platform = "iOS";
}
elseif ($platform -like 'W') 
{
  $platform = "Windows";
}
elseif ($platform -like 'S') 
{
  $platform = "Shared";
}
else
{
	Write-Host "Invalid platform given" -ForegroundColor Red
	exit;
}

# Description

Write-Host "Platform: $platform" -ForegroundColor Green

$description = Read-Host -Prompt 'Enter description'

# File Creation

New-Item -ItemType Directory -Force -Path $changeLogDir | Out-Null

$filename = "$(${ticketNo}.ToUpper())-$($description.ToLower())"
$filename = $filename -replace " ", "-"
$filename = $filename -Replace '[^0-9a-zA-Z\-]',''
$filename = $filename.subString(0, [System.Math]::Min(200, $filename.Length)) 
$filename = "$filename.md"
Write-Host "File: $filename" -ForegroundColor Green

$contents = "#### $platform`r`n[$ticketNo](https://ifsdev.atlassian.net/browse/$ticketNo): $description"
$contents | Set-Content "$changeLogDir\$filename"

# Write-Host "Opening file..." -ForegroundColor Green
# & "$changeLogDir\$filename"

if ($LastExitCode -ne 0) { Pause }
