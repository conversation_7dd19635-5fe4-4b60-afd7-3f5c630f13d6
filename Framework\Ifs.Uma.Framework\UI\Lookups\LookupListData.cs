﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Data;
using Ifs.Uma.UI.Filters;
using Ifs.Uma.UI.Lists;
using Ifs.Uma.UI.Services;
using Ifs.Uma.UI.Summaries;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Lookups
{
    public class LookupListData : ListDataBase<LookupItem>, IOnDemandList
    {
        private readonly ILogger _logger;
        private readonly IDialogService _dialogService;
        private readonly IOnDemandDataHandler _onDemandDataHandler;
        public const string RecentlyUsedItemsSection = "\u25CF";
        public const string FavoritesSection = "\u2605";
        public const string RemainingItemsSection = "\u25CB";
        public const string NoneSection = "";

        public const string RecentlyUsedSettingsName = "RecentlyUsed";
        public const string FavoriteSettingsName = "Favorite";

        private const int MaximumRecentlyUsedItems = 3;
        
        public LookupRequest Request { get; private set; }

        private string _title = string.Empty;
        public string Title
        {
            get { return _title; }
            private set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged(() => Title);
                }
            }
        }

        private CardDef _itemCardDef;
        public CardDef ItemCardDef
        {
            get => _itemCardDef;
            set => SetProperty(ref _itemCardDef, value);
        }

        private SummaryDef _itemSummaryDef;
        public SummaryDef ItemSummaryDef
        {
            get => _itemSummaryDef;
            set => SetProperty(ref _itemSummaryDef, value);
        }

        private ObservableCollection<LookupDialogFilter> _filters = new ObservableCollection<LookupDialogFilter>();
        public ObservableCollection<LookupDialogFilter> Filters
        {
            get { return _filters; }
            set
            {
                if (_filters != value)
                {
                    _filters = value;
                    OnPropertyChanged(() => Filters);
                }
            }
        }
        
        public LookupDialogFilter SelectedFilter
        {
            get { return _filters?.FirstOrDefault(x => x.IsEnabled); }
            set
            {
                if (value != null)
                {
                    value.IsEnabled = true;
                    OnPropertyChanged(() => SelectedFilter);
                }
            }
        }

        public LookupItem UnknownItem { get; } = new LookupItem(LookupItemType.Unknown, string.Empty);
        public LookupItem SearchItem { get; } = new LookupItem(LookupItemType.Search, string.Empty);

        public ViewableGrouping<LookupItemType, LookupItem> RecentlyUsedItems { get; } = new ViewableGrouping<LookupItemType, LookupItem>(LookupItemType.RecentlyUsed);
        public ViewableGrouping<LookupItemType, LookupItem> FavoriteItems { get; } = new ViewableGrouping<LookupItemType, LookupItem>(LookupItemType.Favorite);

        private int _specialItemCount;

        private IEnumerable<string> _recentlyUsedItemIds;
        private IEnumerable<string> _favoriteItemIds;
        private bool _hasLoadedQuickItems;

        public LookupListData(ILogger logger, IDialogService dialogService, IOnDemandDataHandler onDemandDataHandler) 
            : base()
        {
            _logger = logger;
            _onDemandDataHandler = onDemandDataHandler;
            _dialogService = dialogService;
            FilterManager.AutoRefresh = false;
            FilterManager.FilterChanged += FilterManagerOnFilterChanged;
            FilterManager.CalculateTally += FilterManager_CalculateTally;
            Items.CollectionChanged += Items_CollectionChanged;
        }

        private void Items_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (HasItems && e.Action == System.Collections.Specialized.NotifyCollectionChangedAction.Reset)
            {
                foreach (LookupItem lookupItem in Items.Where(i => i.ItemType == LookupItemType.Item))
                {
                    if (IsItemInFavorites(lookupItem))
                    {
                        lookupItem.SetFavoriteActions();
                    }
                    else if (IsItemInRecentlyUsed(lookupItem))
                    {
                        lookupItem.SetRecentlyUsedActions();
                    }
                    else
                    {
                        lookupItem.SetDefaultActions();
                    }
                }
            }
        }

        private void FilterManager_CalculateTally(object sender, ItemTallyEventArgs e)
        {
            int quickItemsCount = Items.Where(x => x.ItemType == LookupItemType.Favorite || x.ItemType == LookupItemType.RecentlyUsed).Count();

            bool showTotal = Request != null && !(Request.QueryOnFilter || Request.QueryOnSearch);
            if (!showTotal)
            {
                e.Total = null;
            }
            else if (e.Total.HasValue)
            {
                e.Total = e.Total.Value - _specialItemCount - quickItemsCount;
            }

            if (e.Count.HasValue)
            {
                e.Count = e.Count.Value - quickItemsCount;
            }
        }

        public async Task LoadRequest(LookupRequest request)
        {
            Request = request;
            Title = request.Title;
            if (request.GroupSelector != null)
            {
                TitleSelector = x => request.GroupSelector(x.Item);
            }
            else if (request.QuickItemSettings != null && request.QuickItemSettings.Options != Lookups.QuickItemOptions.None)
            {
                SectionSelector = x => DefaultGroupSelector(x);
            }

            UnknownItem.Text = request.AllowUnknown ? request.UnknownItem : null;

            if (Request.Filters != null)
            {
                Filters = new ObservableCollection<LookupDialogFilter>();

                foreach (var filter in Request.Filters)
                {
                    LookupDialogFilter dialogFilter = new LookupDialogFilter(filter);
                    FilterManager.AddFilter(dialogFilter);
                    Filters.Add(dialogFilter);
                }
            }

            SelectedFilter = Filters?.FirstOrDefault();

            ItemCardDef = Request.CardDef;
            ItemSummaryDef = Request.SummaryDef;

            UpdateItemIds();
            
            if (request.SortOptions != null)
            {
                foreach (ListSortOption sortOption in request.SortOptions)
                {
                    SortOptions.Add(sortOption);
                }

                SortOptions.Settings = Request.Settings;
                SortOptions.LoadFromSettings(false);
            }
            
            await UpdateAsync();

            // If the developer hasn't specified a Summary Retriever, then we have to do things in a bit unoptimized way
            // This has to be done after we load all items by calling UpdateAsync above
            // It will then manually select the Quick Select Items from the entire list of items
            if (Request.ItemRetriever == null)
            {
                LoadQuickSelectItemsWithoutSummaryRetriever();
            }
        }

        protected void UpdateItemIds()
        {
            if (Request.Settings != null && Request.QuickItemSettings != null)
            {
                _recentlyUsedItemIds = Request.Settings.GetList(RecentlyUsedSettingsName);
                _favoriteItemIds = Request.Settings.GetList(FavoriteSettingsName);
            }
        }

        protected override int OnSortItem(LookupItem x, LookupItem y)
        {
            int? comparisonResult = LookupItem.TryCompareLookupItemTypes(x.ItemType, y.ItemType);
            if (!comparisonResult.HasValue)
            {
                return base.OnSortItem(x, y);
            }
            else
            {
                return comparisonResult.Value;
            }
        }

        private async Task LoadQuickSelectItems(CancellationToken cancelToken)
        {
            if (Request.QuickItemSettings == null || 
                Request.QuickItemSettings.Options == Lookups.QuickItemOptions.None || 
                Request.ItemRetriever == null || 
                Request.ItemIdRetriever == null ||
                _hasLoadedQuickItems)
            {
                return;
            }

            _hasLoadedQuickItems = true;

            List<string> itemIdsToQuery = new List<string>();

            if (Request.QuickItemSettings.Options.HasFlag(Lookups.QuickItemOptions.Favorites))
            {
                if (_favoriteItemIds != null && Request.ItemRetriever != null)
                {
                    itemIdsToQuery.AddRange(_favoriteItemIds);
                }
            }

            if (Request.QuickItemSettings.Options.HasFlag(Lookups.QuickItemOptions.RecentlyUsed))
            {
                if (_recentlyUsedItemIds != null && Request.ItemRetriever != null)
                {
                    itemIdsToQuery.AddRange(_recentlyUsedItemIds);
                }
            }

            if (itemIdsToQuery.Any())
            {
                var results = await Request.ItemRetriever(itemIdsToQuery.Distinct(), cancelToken);
                List<object> recentlyUsedItems = new List<object>();

                FavoriteItems.Clear();
                foreach (object item in results)
                {
                    string identifier = Request.ItemIdRetriever(item);
                    if (!string.IsNullOrWhiteSpace(identifier))
                    {
                        if (_favoriteItemIds?.Contains(identifier) == true)
                        {
                            LookupItem favoriteItem = new LookupItem(LookupItemType.Favorite, item);
                            favoriteItem.SetFavoriteActions();
                            FavoriteItems.Add(favoriteItem);
                        }

                        if (_recentlyUsedItemIds?.Contains(identifier) == true)
                        {
                            recentlyUsedItems.Add(item);
                        }
                    }
                }

                // We have to sort the recently used the ids in order they were saved in the profile,
                // or otherwise end up replacing the wrong item when we reach the max number of items allowed
                if (_recentlyUsedItemIds != null && _recentlyUsedItemIds.Any())
                {
                    List<string> ids = _recentlyUsedItemIds.ToList();
                    recentlyUsedItems = recentlyUsedItems.OrderBy(x => ids.IndexOf(Request.ItemIdRetriever(x))).ToList();
                }

                RecentlyUsedItems.Clear();
                foreach (object item in recentlyUsedItems)
                {
                    LookupItem recentlyUsedItem = new LookupItem(LookupItemType.RecentlyUsed, item);
                    recentlyUsedItem.SetRecentlyUsedActions();
                    RecentlyUsedItems.Add(recentlyUsedItem);
                }
            }
            else
            {
                FavoriteItems.Clear();
                RecentlyUsedItems.Clear();
            }
        }

        private void LoadQuickSelectItemsWithoutSummaryRetriever()
        {
            if (Request.QuickItemSettings == null || Request.QuickItemSettings.Options == Lookups.QuickItemOptions.None)
            {
                return;
            }

            RecentlyUsedItems.Clear();
            FavoriteItems.Clear();

            ILookupItemKey summaryKeyItem;
            foreach (var item in Items.ToArray()) // So as not to modify the original collection
            {
                summaryKeyItem = item.Item as ILookupItemKey;
                if (summaryKeyItem != null)
                {
                    if (Request.QuickItemSettings.Options.HasFlag(Lookups.QuickItemOptions.RecentlyUsed) && _recentlyUsedItemIds != null &&
                        _recentlyUsedItemIds.Contains(summaryKeyItem.KeyForItem))
                    {
                        LookupItem newRecentlyUsedItem = new LookupItem(LookupItemType.RecentlyUsed, item.Item);
                        RecentlyUsedItems.Add(newRecentlyUsedItem);

                        if (_favoriteItemIds == null || !_favoriteItemIds.Contains(summaryKeyItem.KeyForItem)) // Don't show if it's already in the Favorites list
                        {
                            Items.Add(newRecentlyUsedItem);
                        }
                    }

                    if (Request.QuickItemSettings.Options.HasFlag(Lookups.QuickItemOptions.Favorites) && _favoriteItemIds != null &&
                        _favoriteItemIds.Contains(summaryKeyItem.KeyForItem))
                    {
                        LookupItem newFavoriteItem = new LookupItem(LookupItemType.Favorite, item.Item);
                        FavoriteItems.Add(newFavoriteItem);
                        Items.Add(newFavoriteItem);
                    }
                }
            }
        }

        public void UpdateQuickSelectItems()
        {
            if (Request.QuickItemSettings == null || Request.ItemIdRetriever == null || Request.Settings == null)
            {
                return;
            }

            ISettings settings = Request.Settings;

            if (Request.QuickItemSettings.Options.HasFlag(QuickItemOptions.RecentlyUsed))
            {
                settings.Clear(RecentlyUsedSettingsName);

                foreach (LookupItem item in RecentlyUsedItems)
                {
                    string identifier = Request.ItemIdRetriever(item.Item);
                    if (!string.IsNullOrWhiteSpace(identifier))
                    {
                        settings.AddToList(RecentlyUsedSettingsName, identifier, MaximumRecentlyUsedItems);
                    }
                }
            }

            if (Request.QuickItemSettings.Options.HasFlag(QuickItemOptions.Favorites))
            {
                foreach (LookupItem item in FavoriteItems)
                {
                    string identifier = Request.ItemIdRetriever(item.Item);
                    if (!string.IsNullOrWhiteSpace(identifier))
                    {
                        settings.AddToList(FavoriteSettingsName, identifier);
                    }
                }
            }

            UpdateItemIds();
        }

        protected override async Task OnUpdateAsync(CancellationToken cancelToken)
        {
            // If the ItemRetriever is defined, we can pre-load Quick Select Items before we start loading the remaining stuff
            if (Request.ItemRetriever != null)
            {
                await LoadQuickSelectItems(cancelToken);
            }

            LookupQuery query = new LookupQuery()
            {
                SearchTerm = SearchTerm,
                Filter = SelectedFilter?.LookupFilter
            };

            IEnumerable selectedSortOptions = SortOptions.Where(s => s.SortOrder != null);
            foreach (ListSortOption sortOption in selectedSortOptions)
            {
                query.SortOptions.Add(sortOption);
            }

            Items.Clear();
            SelectedItem = null;
            _specialItemCount = 0;

            if (Request.AllowNull)
            {
                Items.Add(LookupItem.None);
                UpdateSpecialItemsCount(LookupItem.None);
            }

            if (Request.AllowUnknown)
            {
                Items.Add(SearchItem);
                UpdateSpecialItemsCount(SearchItem);

                if (!string.IsNullOrEmpty(UnknownItem.Text))
                {
                    Items.Add(UnknownItem);
                    UpdateSpecialItemsCount(UnknownItem);
                    SelectedItem = UnknownItem;
                }
            }

            foreach (var item in FavoriteItems)
            {
                Items.Add(item);
            }

            if (Request.ItemIdRetriever != null)
            {
                foreach (LookupItem item in RecentlyUsedItems)
                {
                    string identifier = Request.ItemIdRetriever(item.Item);
                    if (!string.IsNullOrWhiteSpace(identifier)
                        && (_favoriteItemIds == null || !_favoriteItemIds.Contains(identifier))) // Don't show if it's already in the Favorites list
                    {
                        Items.Add(item);
                    }
                }
            }

            if (Request.AllowNull && SelectedItem == null)
            {
                SelectedItem = LookupItem.None;
            }

            IDataLoader<LookupItem> loader = Request.DataProvider(query).Select(x => new LookupItem(LookupItemType.Item, x));
            await AddDataLoaderItemsAsync(loader, cancelToken, x => x.ItemType == LookupItemType.Item && Request.SelectedItemPredicate(x.Item));
        }

        protected override async Task<bool> OnHandleUpdateException(Exception ex)
        {
            _logger.HandleException(ExceptionType.Unexpected, ex);
            await _dialogService.ShowException(ex);
            return true;
        }
        protected override async Task OnHandleUpdateOffline()
        {
            await _dialogService.Alert(string.Empty, Strings.YouMustBeOnline);
        }

        private void UpdateSpecialItemsCount(LookupItem item)
        {
            if (Items.Contains(item))
            {
                _specialItemCount++;
            }
        }

        private async void FilterManagerOnFilterChanged(object sender, EventArgs eventArgs)
        {
            if (Request.QueryOnFilter)
            {
                await UpdateAsync();
            }
            else
            {
                RefreshFilter();
            }
        }

        protected override void OnSearchTermInputChanged()
        {
            base.OnSearchTermInputChanged();

            SearchItem.Text = SearchTermInput;
        }

        protected override async void OnSearchTermChanged()
        {
            if (Request.QueryOnSearch)
            {
                await UpdateAsync();
            }
            else
            {
                base.OnSearchTermChanged();
            }
        }

        protected override bool OnFilterItem(LookupItem item)
        {
            if (item.ItemType == LookupItemType.Search && string.IsNullOrEmpty(SearchTermInput))
            {
                return false;
            }

            return base.OnFilterItem(item);
        }

        protected override bool OnMatchSearchTerm(LookupItem item)
        {
            if (item.ItemType != LookupItemType.Item)
            {
                return item.Text != null && item.Text.IndexOf(SearchTerm, StringComparison.CurrentCultureIgnoreCase) >= 0;
            }

            if (Request.QuerySearchOnly && Request.QueryOnSearch)
            {
                return true;
            }

            if (ItemSummaryDef != null)
            {
                return ItemSummaryDef.MatchFilter(SearchTerm, item.Item);
            }
            
            return ItemCardDef.MatchFilter(SearchTerm, item.Item);
        }
        
        public LookupResult<T> GetResult<T>()
        {
            if (SelectedItem == null || SelectedItem.ItemType == LookupItemType.None)
            {
                return LookupResult<T>.Create(default(T), SelectedFilter?.LookupFilter as LookupFilter<T>);
            }
            else if (SelectedItem.ItemType == LookupItemType.Search || SelectedItem.ItemType == LookupItemType.Unknown)
            {
                return LookupResult<T>.CreateUnknown(SelectedItem.Text);
            }
            else
            {
                return LookupResult<T>.Create((T)SelectedItem.Item, SelectedFilter?.LookupFilter as LookupFilter<T>);
            }
        }

        private static Func<object, object> DefaultGroupSelector
        {
            get
            {
                return (x) =>
                {
                    LookupItem item = x as LookupItem;

                    if (x != null)
                    {
                        if (item.ItemType == LookupItemType.RecentlyUsed)
                        {
                            return RecentlyUsedItemsSection;
                        }
                        else if (item.ItemType == LookupItemType.Favorite)
                        {
                            return FavoritesSection;
                        }
                        else if (item.ItemType == LookupItemType.None)
                        {
                            return NoneSection;
                        }
                    }

                    return RemainingItemsSection;
                };
            }
        }

        private bool IsItemInFavorites(LookupItem item)
        {
            string itemIdentifier = Request.ItemIdRetriever?.Invoke(item.Item);
            if (string.IsNullOrWhiteSpace(itemIdentifier))
            {
                return false;
            }

            if (FavoriteItems != null)
            {
                foreach (LookupItem favoriteItem in FavoriteItems)
                {
                    string favoriteItemIdentifier = Request.ItemIdRetriever(favoriteItem.Item);
                    if (itemIdentifier.Equals(favoriteItemIdentifier))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private bool IsItemInRecentlyUsed(LookupItem item)
        {
            string itemIdentifier = Request.ItemIdRetriever?.Invoke(item.Item);
            if (string.IsNullOrWhiteSpace(itemIdentifier))
            {
                return false;
            }

            if (RecentlyUsedItems != null)
            {
                foreach (var recentlyUsedItem in RecentlyUsedItems)
                {
                    string recentlyUsedItemId = Request.ItemIdRetriever(recentlyUsedItem.Item);
                    if (itemIdentifier.Equals(recentlyUsedItemId))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public void FavoriteItem(LookupItem item)
        {
            if (FavoriteItems.Any(l => l.Item == item.Item))
                return;

            LookupItem itemToAddActions = item.ItemType == LookupItemType.Item ? item : GetMatchingItemFromMainList(item);
            itemToAddActions?.SetFavoriteActions();

            // Do not forget a recently used item, just remove it from the list
            // if the user is going to favorite it.
            RemoveRecentlyUsedItemFromMainList(item);

            LookupItem newFavorite = new LookupItem(LookupItemType.Favorite, item.Item);
            FavoriteItems.Add(newFavorite);
            Items.Add(newFavorite);

            if (Request?.QuickItemSettings?.Options.HasFlag(QuickItemOptions.Favorites) == true)
            {
                UpdateQuickSelectItems();
            }
        }

        public void SetCheckedItem(LookupItem item)
        {
            if (item == null)
            {
                return;
            }

            if (item.ItemType == LookupItemType.None)
            {
                item.IsSelected = true;
                return;
            }

            foreach (var itemToSelect in Items)
            {
                itemToSelect.IsSelected = Request.SelectedItemPredicate(itemToSelect.Item);
            }
        }

        public void ForgetItem(LookupItem item)
        {
            LookupItem itemToRemove = GetMatchingItemFromRecentlyUsedItems(item);
            RemoveRecentlyUsedItemFromMainList(item); // Just removes from main list
            RecentlyUsedItems.Remove(itemToRemove); // remove it from the recently used list all together

            LookupItem itemToAddActions = item.ItemType == LookupItemType.Item ? item : GetMatchingItemFromMainList(item);
            itemToAddActions?.SetDefaultActions();

            if (Request?.QuickItemSettings.Options.HasFlag(QuickItemOptions.Favorites) == true)
            {
                UpdateQuickSelectItems();
            }
        }

        public void UnfavoriteItem(LookupItem item)
        {
            // Now let's remove item from the favorites.
            LookupItem itemToRemove = null;
            if (item.ItemType == LookupItemType.Favorite)
            {
                // Item can already be identified as a Favorite.
                itemToRemove = item;
            }
            else
            {
                // We have performed the action on an item that is also in the Favorites list, so extract the item from
                // the Favorites list by using ETag to compare items and remove it from that list.
                itemToRemove = GetMatchingItemFromFavorites(item);
            }

            if (itemToRemove != null)
            {
                FavoriteItems.Remove(itemToRemove);
                Items.Remove(itemToRemove);

                ISettings settings = Request.Settings;
                string identifier = Request.ItemIdRetriever?.Invoke(itemToRemove.Item);
                settings.RemoveFromList(FavoriteSettingsName, identifier);
            }

            // Restore this item as a recently used item,
            // if it already exists within the RecentlyUsedItems list
            LookupItem recentItem = GetMatchingItemFromRecentlyUsedItems(item);
            LookupItem mainListItem = GetMatchingItemFromMainList(item);
            if (recentItem != null)
            {
                Items.Add(recentItem);
                mainListItem.SetRecentlyUsedActions();
            }
            else
            {
                mainListItem?.SetDefaultActions();
            }

            if (Request?.QuickItemSettings?.Options.HasFlag(QuickItemOptions.Favorites) == true)
            {
                UpdateQuickSelectItems();
            }
        }

        private void RemoveRecentlyUsedItemFromMainList(LookupItem item)
        {
            if (item.ItemType == LookupItemType.RecentlyUsed)
            {
                Items.Remove(item);
            }
            else if (GetMatchingItemFromRecentlyUsedItems(item) is LookupItem recentlyUsedItem)
            {
                if (recentlyUsedItem != null)
                {
                    Items.Remove(recentlyUsedItem);
                }
            }
        }

        public LookupItem GetMatchingItemFromMainList(LookupItem lookupItem)
        {
            string itemIdentifier = Request.ItemIdRetriever?.Invoke(lookupItem.Item);
            if (string.IsNullOrWhiteSpace(itemIdentifier))
            {
                return null;
            }

            foreach (LookupItem item in Items.Where(i => i.ItemType == LookupItemType.Item).ToArray())
            {
                string itemId = Request.ItemIdRetriever(item.Item);
                if (!string.IsNullOrWhiteSpace(itemId) && itemId.Equals(itemIdentifier))
                {
                    return item;
                }
            }

            return null;
        }

        public LookupItem GetMatchingItemFromFavorites(LookupItem item)
        {
            string itemIdentifier = Request.ItemIdRetriever?.Invoke(item.Item);
            if (string.IsNullOrWhiteSpace(itemIdentifier))
            {
                return null;
            }

            foreach (var favoriteItem in FavoriteItems)
            {
                string favoriteItemId = Request.ItemIdRetriever(favoriteItem.Item);
                if (!string.IsNullOrWhiteSpace(favoriteItemId) && favoriteItemId.Equals(itemIdentifier))
                {
                    return favoriteItem;
                }
            }

            return null;
        }

        public LookupItem GetMatchingItemFromRecentlyUsedItems(LookupItem item)
        {
            string itemIdentifier = Request.ItemIdRetriever?.Invoke(item.Item);
            if (string.IsNullOrWhiteSpace(itemIdentifier))
            {
                return null;
            }

            foreach (LookupItem recentlyUsedItem in RecentlyUsedItems)
            {
                string recentlyUsedItemId = Request.ItemIdRetriever(recentlyUsedItem.Item);
                if (!string.IsNullOrWhiteSpace(recentlyUsedItemId) && recentlyUsedItemId.Equals(itemIdentifier))
                {
                    return recentlyUsedItem;
                }
            }

            return null;
        }

        public string GetItemLabel(LookupItem item)
        {
            string displayText = item.Text;

            if (item.ItemType == LookupItemType.None || item.ItemType == LookupItemType.Search)
            {
                return displayText;
            }

            if (ItemSummaryDef != null)
            {
                ILookupItemKey summaryKeyItem = item.Item as ILookupItemKey;

                SummaryDefItem firstItem = ItemSummaryDef.Items.FirstOrDefault(x => x.Column == 0 && x.Row == 0);

                if (firstItem != null)
                {
                    displayText = firstItem.GetDisplayValue(item.Item);
                }
            }

            if (ItemCardDef != null && item.Item is ViewData viewData)
            {
                displayText = ItemCardDef.GetHeaderText(viewData);
            }

            return displayText;
        }

        public async Task SearchModeChangedAsync()
        {
            // Notify the data provider the seach mode has changed 
            Request.SearchModeChanged.Execute(null);
            // Refresh the list
            await UpdateAsync();
        }

        public async Task DownLoadItemAsync(RemoteRow row, string projectionName)
        {
            if (_onDemandDataHandler.IsRecordInLocalDb(row))
            {
                return;
            }

            ExecuteResult result = await _onDemandDataHandler.DownloadRecordToLocalDatabase(row, projectionName, Request.EntitySetName);

            if (result.Value.Equals(ExecuteResult.No))
            {
                await _dialogService.Alert(Strings.DownloadFailed, Strings.DownloadFailed + " for " + row.TableName);
            }
        }
    }

    public class LookupDialogFilter : Filter<LookupItem>
    {
        public LookupFilter LookupFilter { get; }

        public LookupDialogFilter(LookupFilter filter) 
            : base(filter.Name, filter.Predicate == null ? null : CreatePredicate(filter))
        {
            LookupFilter = filter;
        }

        private static Predicate<LookupItem> CreatePredicate(LookupFilter filter)
        {
            return li => filter.Predicate.Invoke(li.Item);
        }
    }
}
