﻿using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteCreateTests : ProcedureTest
    {
        private const string TstCustomerEntityName = "TstCustomer";
        private const string CustomerNameAttributeName = "CustomerName";
        private static readonly string TstCustomerTableName = RemoteNaming.ToTableName(TstCustomerEntityName);

        [Test]
        public async Task CreateAndPrepare()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "CreateCustomer", null);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            RemoteRow row = result.Value as RemoteRow;
            Assert.IsNotNull(row);
            Assert.AreEqual(0, row.RowId);
            Assert.AreEqual(TstCustomerTableName, row.TableName);
            Assert.AreEqual("TestPrepareValue", row[CustomerNameAttributeName]);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteCreateSchema", null);
        }
    }
}
