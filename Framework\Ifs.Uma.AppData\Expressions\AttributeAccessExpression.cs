﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.Metadata;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class AttributeAccessExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.Column;
        public override Type Type => typeof(DynamicValue);
        public AttributePathInfo Attribute { get; }

        internal AttributeAccessExpression(AttributePathInfo attribute)
        {
            Attribute = attribute ?? throw new ArgumentNullException(nameof(attribute));
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitAttributeAccessExpression(this);
        }

        public override string ToString()
        {
            return Attribute.ToString();
        }
    }

    public partial class IfsExpression
    {
        public static AttributeAccessExpression AttributeAccess(AttributePathInfo attribute)
        {
            return new AttributeAccessExpression(attribute);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitAttributeAccessExpression(AttributeAccessExpression exp)
        {
            return exp;
        }
    }
}
