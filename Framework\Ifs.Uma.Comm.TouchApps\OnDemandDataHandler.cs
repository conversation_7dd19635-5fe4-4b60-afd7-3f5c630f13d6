﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Comm.TouchApps;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData
{
    public interface IOnDemandDataHandler
    {
        Task<ExecuteResult> DownloadRecordToLocalDatabase(RemoteRow remoteRow, string projectionName, string entityName);

        bool IsRecordInLocalDb(RemoteRow remoteRow);
    }

    public sealed class OnDemandDataHandler : IOnDemandDataHandler
    {
        private readonly IMetadata _metadata;
        private readonly IOnlineDataHandler _onlineDataHandler;
        private readonly IDatabaseController _databaseController;
        private readonly TouchAppsComms _touchAppsComms;

        public OnDemandDataHandler(IMetadata metadata, IOnlineDataHandler onlineDataHandler, IDatabaseController db, IIfsConnection ifsConnection)
        {
            _metadata = metadata;
            _onlineDataHandler = onlineDataHandler;
            _databaseController = db;
            _touchAppsComms = ifsConnection.TouchAppsComms;
        }

        #region PublicMethods
        public async Task<ExecuteResult> DownloadRecordToLocalDatabase(RemoteRow remoteRow, string projectionName, string entitySetName)
        {
            try
            {
                string entityName = RemoteNaming.ToEntityName(remoteRow.TableName);
                ObjPrimaryKey objPrimaryKey = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, remoteRow);

                if (CheckEntityDeliveryMethod(entityName))
                {
                    EntityQuery query = entitySetName != null ? PrepareEntityQueryFromEntitySet(projectionName, entitySetName, objPrimaryKey) : PrepareEntityQueryFromEntity(projectionName, entityName, objPrimaryKey);
                    query.Expand = GetReferencesToExpand(projectionName, entityName);

                    EntityQueryResult result = await GetParentRecordWithReferenceAsync(query);

                    IEnumerable<RemoteRow> rowsToBeInserted = PrepareRecordsToBeInserted(result);

                    await TrackRecord(query.DataSource.EntityName, objPrimaryKey.ToKeyRef()).ConfigureAwait(false);

                    InsertRecordsToLocalDb(rowsToBeInserted);
                }
                return ExecuteResult.Yes;
            } 
            catch (Exception e)
            {
                Logger.Current.Error("On Demand Download failed on " + e.Message);
                return ExecuteResult.No;
            }
        }
        #endregion

        #region PrivateMethods
        private async Task<EntityQueryResult> GetParentRecordWithReferenceAsync(EntityQuery query)
        {
            EntityQueryResult result = await _onlineDataHandler.GetRecordsAsync(query, new CancellationToken());
            return result;
        }

        private EntityQuery PrepareEntityQueryFromEntitySet(string projectionName, string entitySetName, ObjPrimaryKey objPrimaryKey)
        {
            EntityDataSource dataSource = EntityDataSource.FromEntitySet(_metadata, projectionName, entitySetName);
            return PrepareEntityQuery(dataSource, objPrimaryKey);
        }

        private EntityQuery PrepareEntityQueryFromEntity(string projectionName, string entitytName, ObjPrimaryKey objPrimaryKey)
        {
            EntityDataSource dataSource = EntityDataSource.FromEntity(_metadata, projectionName, entitytName);
            return PrepareEntityQuery(dataSource, objPrimaryKey);
        }

        private EntityQuery PrepareEntityQuery(EntityDataSource dataSource, ObjPrimaryKey objPrimaryKey)
        {
            EntityQuery query = new EntityQuery(dataSource);
            query.SetFilter(objPrimaryKey);

            return query;
        }

        private async Task TrackRecord(string entityName, string keyRef)
        {
            await _touchAppsComms.AddOnDemandEntityResourceBaseAsync(entityName, keyRef);
        }

        private IEnumerable<RemoteRow> PrepareRecordsToBeInserted(EntityQueryResult result)
        {
            EntityRecord record = result.Records.FirstOrDefault();

            if (record == null)
            {
                return Enumerable.Empty<RemoteRow>();
            }
               
            List<RemoteRow> recordsToBeInserted = new List<RemoteRow>
            {
                record.Row
            };

            if (record.References != null)
            {
                recordsToBeInserted.AddRange(record.References.Values.Where(reference => reference != null));
            }

            return recordsToBeInserted;
        }

        private void InsertRecordsToLocalDb(IEnumerable<RemoteRow> rowsToBeInserted)
        {
            FwDataContext dataContext = _databaseController.CreateDataContext();

            if (dataContext != null)
            {
                rowsToBeInserted.ToList().ForEach(row =>
                {
                    IMetaTable metaTable = _metadata.MetaModel.GetTable(row.TableName);
                    dataContext.InsertOrIgnoreCacheTable(metaTable, row);
                });
            }
        }

        private bool CheckEntityDeliveryMethod(string entityName)
        {
            FwDataContext dataContext = _databaseController.CreateDataContext();

            if (dataContext != null)
            {
                ServerSyncRule serverSyncRule = dataContext.ServerSyncRules.FirstOrDefault(x => x.Entity == entityName);
                return serverSyncRule != null && serverSyncRule.DeliveryMethod.Equals("NEW_BATCH");
            }

            return false;
        }

        private IEnumerable<string> GetReferencesToExpand(string projectionName, string entityName)
        {
            Dictionary<string, CpiReference> entityReferences = _metadata.FindReferences(projectionName, entityName);
            IEnumerable<string> onDemandReferences = _metadata.GetOnDemandReferences(entityName);

            if (onDemandReferences == null && entityReferences == null)
            {
                return Enumerable.Empty<string>();
            }
                
            return entityReferences.Where(x => onDemandReferences.Contains(x.Value.Target)).Select(x => x.Key);
        }

        public bool IsRecordInLocalDb(RemoteRow remoteRow)
        {
            FwDataContext dataContext = _databaseController.CreateDataContext();
            ObjPrimaryKey objPrimaryKey = ObjPrimaryKey.FromPrimaryKey(_metadata.MetaModel, remoteRow);

            if (dataContext != null)
            {
                return dataContext.RecordExists(objPrimaryKey);
            }

            return false;
        }
        #endregion
    }
}
