{"name": "FndTstOffline", "version": "1706901162:1948287535", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {"Customers": {"name": "Customers", "entity": "TstCustomer", "array": true}}, "enumerations": {}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}, "CustomerName": {"datatype": "Text", "keygeneration": "User"}}, "references": {}, "arrays": {}, "actions": {}, "functions": {}}}, "structures": {}, "actions": {}, "functions": {}}, "component": "FNDTST", "layout": {"lists": {}, "cards": {}, "selectors": {}, "pages": {}, "groups": {}, "menus": {}, "commands": {}}}