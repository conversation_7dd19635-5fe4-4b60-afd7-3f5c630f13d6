﻿using System.Linq.Expressions;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Metadata;
using Ifs.Uma.Tests;
using Ifs.Uma.Tests.TestClasses;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests
{
    [TestFixture]
    public class AggregateQuerySqlTests : FrameworkTest
    {
        [Test]
        public void Count()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstCustomerType");
            EntityQuery from = new EntityQuery(source);
            AggregateQuery query = AggregateQuery.CreateCount(from);

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT COUNT(@p0) FROM tst_customer_type t0 @p0='1'");
        }

        [Test]
        public void CountLimit()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstCustomerType");
            EntityQuery from = new EntityQuery(source);
            from.Take = 5;
            AggregateQuery query = AggregateQuery.CreateCount(from);

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT COUNT(@p0) FROM (" +
                    "SELECT t0.row_id " +
                    "FROM tst_customer_type t0 " +
                    "ORDER BY t0.type_id ASC " +
                    "FETCH FIRST 5 ROWS ONLY" +
                ") tA @p0='1'");
        }

        [Test]
        public void CountLimitOfflineQuery()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstCustomerOfflineQuery");
            EntityQuery from = new EntityQuery(source);
            from.Take = 5;
            AggregateQuery query = AggregateQuery.CreateCount(from);

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT COUNT(@p0) FROM (" +
                    "SELECT t0.customer_no " +
                    "FROM tst_customer_offline_query t0 " +
                    "ORDER BY t0.customer_no ASC " +
                    "FETCH FIRST 5 ROWS ONLY" +
                ") tA @p0='1'");
        }

        [Test]
        public void Min()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);

            AggregateQuery query = new AggregateQuery(from, AggregateType.Minimum, "ColA");

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT MIN(t0.col_a) FROM tst_expand t0");
        }

        [Test]
        public void Maximum()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);

            AggregateQuery query = new AggregateQuery(from, AggregateType.Maximum, "ColA");

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT MAX(t0.col_a) FROM tst_expand t0");
        }

        [Test]
        public void Sum()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);

            AggregateQuery query = new AggregateQuery(from, AggregateType.Sum, "ColA");

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT SUM(t0.col_a) FROM tst_expand t0");
        }

        [Test]
        public void Average()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);

            AggregateQuery query = new AggregateQuery(from, AggregateType.Average, "ColA");

            string sql = GetSql(query);
            Assert.AreEqual(sql, "SELECT AVG(t0.col_a) FROM tst_expand t0");
        }

        [Test]
        public void Offset()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);
            from.Skip = 20;
            from.Take = 10;

            AggregateQuery query = new AggregateQuery(from, AggregateType.Minimum, "ColA");

            string sql = GetSql(query);
            Assert.AreEqual(sql,
                "SELECT MIN(tA.col_a) " +
                "FROM (" +
                    "SELECT t0.col_a " +
                    "FROM tst_expand t0 " +
                    "ORDER BY t0.id ASC " +
                    "OFFSET 20 ROWS " +
                    "FETCH FIRST 10 ROWS ONLY) tA");
        }

        [Test]
        public void OffsetRef()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);
            from.Skip = 20;
            from.Take = 10;

            AggregateQuery query = new AggregateQuery(from, AggregateType.Minimum, "ExpandNumRef.Col1");

            string sql = GetSql(query);
            Assert.AreEqual(sql,
                "SELECT MIN(tA.expand_num_ref$col1) " +
                "FROM (" +
                    "SELECT t0$ExpandNumRef.col1 AS expand_num_ref$col1 " +
                    "FROM tst_expand t0 " +
                    "LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id " +
                    "ORDER BY t0.id ASC " +
                    "OFFSET 20 ROWS " +
                    "FETCH FIRST 10 ROWS ONLY) tA");
        }

        [Test]
        public void Ref()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);
            AggregateQuery query = new AggregateQuery(from, AggregateType.Minimum, "ExpandNumRef.Col1");

            string sql = GetSql(query);
            Assert.AreEqual(sql,
                "SELECT MIN(t0$ExpandNumRef.col1) " +
                "FROM tst_expand t0 " +
                "LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id");
        }

        [Test]
        public void AggExpression()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);

            Expression exp = Expression.MakeBinary(ExpressionType.Add, IfsExpression.VarAccess("ColA"), IfsExpression.VarAccess("ExpandNumRef.Col1"));

            AggregateQuery query = new AggregateQuery(from, AggregateType.Minimum, exp);

            string sql = GetSql(query);
            Assert.AreEqual(sql,
                "SELECT MIN((t0.col_a+t0$ExpandNumRef.col1)) " +
                "FROM tst_expand t0 " +
                "LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id");
        }

        [Test]
        public void AggExpressionOffset()
        {
            IMetadata metadata = Resolve<IMetadata>();
            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, "TstExpand");
            EntityQuery from = new EntityQuery(source);
            from.Skip = 20;
            from.Take = 10;

            Expression exp = Expression.MakeBinary(ExpressionType.Add, IfsExpression.VarAccess("ColA"), IfsExpression.VarAccess("ExpandNumRef.Col1"));

            AggregateQuery query = new AggregateQuery(from, AggregateType.Minimum, exp);

            string sql = GetSql(query);
            Assert.AreEqual(sql,
                "SELECT MIN((tA.col_a+tA.expand_num_ref$col1)) " +
                "FROM (" +
                    "SELECT t0.col_a, t0$ExpandNumRef.col1 AS expand_num_ref$col1 " +
                    "FROM tst_expand t0 " +
                    "LEFT OUTER JOIN tst_expand_num t0$ExpandNumRef ON t0$ExpandNumRef.id=t0.id " +
                    "ORDER BY t0.id ASC " +
                    "OFFSET 20 ROWS " +
                    "FETCH FIRST 10 ROWS ONLY) tA");
        }
        
        private static string GetSql(AggregateQuery query)
        {
            PreparedAggregateQuery preparedQuery = query.Prepare();
            return TestSqlBuilder.WriteSql(preparedQuery.SelectSpec);
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();

            PrepareDatabase<FwDataContext>("EntityQuerySchema", "EntityQueryData");
        }
    }
}
