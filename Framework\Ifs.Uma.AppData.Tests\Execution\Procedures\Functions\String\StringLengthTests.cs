﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringLengthTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringLengthTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("HelloWorld", ExpectedResult = 10)]
        [TestCase(1, ExpectedResult = 1)]
        [TestCase(1.1, ExpectedResult = 3)]
        [TestCase(true, ExpectedResult = 4)]
        [TestCase(null, ExpectedResult = null)]
        public async Task<long?> String_Length(object input)
        {
            _params["TextInput"] = input;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_Length", _params);
            CheckResult(result);
            return result?.Value as long?;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
