using Ifs.Uma.Database;

namespace Ifs.Uma.Data.Tests
{
    public class TestDataContext : DataContext
    {
        public TestDataContext(DbInternal db)
            : base(db)
        {
        }

        public Table<SyncedRow> SyncedRows
        {
            get { return GetTable<SyncedRow>(); }
        }
    }

    [Table(Name = "synced")]
    [Index(Name = "ix_myid", Columns = "MyId", Unique = true)]
    public class SyncedRow : RowBase
    {
        [Column]
        public string MyId { get; set; }
        [Column(Mandatory = true)]
        public string Name { get; set; }
    }
}
