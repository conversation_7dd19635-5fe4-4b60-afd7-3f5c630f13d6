﻿using Ifs.Uma.Services.Attachments.Documents;
using Ifs.Uma.Services.Attachments.Media;
using Ifs.Uma.Services.Sync;
using Ifs.Uma.Services.Transactions;
#if SIGNATURE_SERVICE
using Ifs.Uma.Signing.Transactions;
#endif

namespace Ifs.Uma.Framework.Services
{
    // An ISyncController is always available and can be used to request a sync
    // even if a SyncService has not been setup - it will just do nothing
    // This keeps the code simpler since checks are not needed in Try Me mode.
    internal class SyncController : ISyncController
    {
        private readonly IResolver _resolver;
        private readonly ITransactionSyncDataHandler _transactionSyncDataHandler;

        public SyncController(IResolver resolver, ITransactionSyncDataHandler transactionSyncDataHandler)
        {
            _resolver = resolver;
            _transactionSyncDataHandler = transactionSyncDataHandler;
        }

        public void RequestSync()
        {
            RequestSync(false, false);
        }

        public async void RequestSync(bool includeAttachments, bool retryFailed)
        {
            if (retryFailed)
            {
                await _transactionSyncDataHandler.RetryFailingTransactions();
            }

#if SIGNATURE_SERVICE
            if (_resolver.TryResolve(out ISignatureSyncService svc) && svc is SignatureSyncService signatureSyncService)
            {
                await signatureSyncService.SyncTriggerer.TriggerNowAsync();
            }
#endif

            if (_resolver.TryResolve(out ITransactionSyncService transactionSyncService))
            {
                transactionSyncService.RequestSync();
            }

            if (includeAttachments)
            {
                if (_resolver.TryResolve(out IDocumentSyncService docSyncService))
                {
                    docSyncService.RequestSync();
                }

                if (_resolver.TryResolve(out IMediaSyncService mediaSyncService))
                {
                    mediaSyncService.RequestSync();
                }
            }
        }
    }
}
