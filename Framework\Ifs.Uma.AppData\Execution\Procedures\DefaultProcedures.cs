﻿using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Execution.Procedures
{
    internal static class DefaultProcedures
    {
        public static CpiProc CreateSaveProc(string name, bool insert)
        {
            return new CpiProc
            {
                Name = name,
                Type = insert ? ProcedureType.EntityInsert : ProcedureType.EntityUpdate,
                Parameters = new[] { new CpiParam { Name = ProcedureExecutor.RecordVarName } },
                Layers = new[]
                {
                    new CpiProcLayer
                    {
                        Execute = new[]
                        {
                            new CpiExecute
                            {
                                Call = new CpiExecuteCall
                                {
                                    Method = CpiExecuteCallMethod.Save,
                                    Args = new CpiSaveCallArgs
                                    {
                                        Name = ProcedureExecutor.RecordVarName,
                                        Send = true
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }

        public static CpiProc CreateDeleteProc(string name)
        {
            return new CpiProc
            {
                Name = name,
                Type = ProcedureType.EntityDelete,
                Parameters = new[] { new CpiParam { Name = ProcedureExecutor.RecordVarName } },
                Layers = new[]
                {
                    new CpiProcLayer
                    {
                        Execute = new[]
                        {
                            new CpiExecute
                            {
                                Call = new CpiExecuteCall
                                {
                                    Method = CpiExecuteCallMethod.Delete,
                                    Args = new CpiDeleteCallArgs
                                    {
                                        Name = ProcedureExecutor.RecordVarName,
                                        Send = true
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }

        public static CpiProc CreatePerformActionProc(string name, IReadOnlyDictionary<string, object> parameters)
        {
            string[] paramNames = parameters?.Keys.ToArray() ?? new string[0];

            return new CpiProc
            {
                Name = name,
                Type = ProcedureType.Action,
                Parameters = paramNames.Select(x => new CpiParam { Name = x }).ToArray(),
                Layers = new[]
                {
                    new CpiProcLayer
                    {
                        Execute = new[]
                        {
                            new CpiExecute
                            {
                                Call = new CpiExecuteCall
                                {
                                    Method = CpiExecuteCallMethod.PerformAction
                                }
                            }
                        }
                    }
                }
            };
        }
    }
}
