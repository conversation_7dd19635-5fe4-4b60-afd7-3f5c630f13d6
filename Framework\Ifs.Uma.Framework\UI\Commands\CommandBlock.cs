﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Attachments;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Commands
{
    public class CommandBlock : ObservableBase
    {
        private readonly IMetadata _metadata;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IExpressionRunner _expressionRunner;

        private bool IsCustomCommandBlock { get; }

        private PageData _pageData;
        public PageData PageData
        {
            get => _pageData;
            set => SetProperty(ref _pageData, value, OnPageDataChanged);
        }

        private ViewData _viewData;
        public ViewData ViewData
        {
            get => _viewData;
            set => SetProperty(ref _viewData, value, OnViewDataChanged);
        }

        private AttachmentCommandItem _attachmentCommand;
        public AttachmentCommandItem AttachmentCommand
        {
            get => _attachmentCommand;
            private set => SetProperty(ref _attachmentCommand, value, OnAttachmentCommandChanged);
        }

        public ViewableCollection<CommandGroup> CommandGroups { get; }

        public event EventHandler VisibilityChanged;
        public event EventHandler<CommandEnabledChangedEventArgs> EnabledChanged;
        public event EventHandler<CommandExecutedEventArgs> CommandExecuted;

        public List<FwCommandItem> Commands { get; } = new List<FwCommandItem>();

        private readonly Debouncer _debouncer = new Debouncer();

        public UpdatingState UpdatingState { get; } = new UpdatingState();

        public CommandBlock(UpdatingState parentUpdatingState, IMetadata metadata, ICommandExecutor commandExecutor, IExpressionRunner expressionRunner, bool isCustomCommandBlock = false)
        {
            _metadata = metadata;
            _commandExecutor = commandExecutor == null ? null : WrappedCommandExecutor.Create(commandExecutor, AfterCommandExecute);
            _expressionRunner = expressionRunner;
            IsCustomCommandBlock = isCustomCommandBlock;

            CommandGroups = new ViewableCollection<CommandGroup>();
            CommandGroups.Filter = x => x.IsVisible;

            UpdatingState.ParentState = parentUpdatingState;
            UpdatingState.IsAnythingUpdatingChanged += UpdatingState_IsAnythingUpdatingChanged;
        }

        private void UpdatingState_IsAnythingUpdatingChanged(object sender, EventArgs e)
        {
            _ = TriggerUpdate(false);
        }

        public void Load(string projectionName, IEnumerable<CpiCommandGroup> commandGroups, bool visibleWhenDisabled, bool includeRelatedPages, bool includeGlobal,
            string[] commandsToIgnore = null, AttachmentCommandItem attachmentCommand = null)
        {
            using (CommandGroups.DeferRefresh())
            {
                CommandGroups.Clear();
                Commands.Clear();

                AddCommands(projectionName, commandGroups, visibleWhenDisabled, includeRelatedPages, includeGlobal, commandsToIgnore, attachmentCommand);

                UpdateStates();
            }
        }

        protected void AddCommands(string projectionName, IEnumerable<CpiCommandGroup> commandGroups, bool visibleWhenDisabled, bool includeRelatedPages, bool includeGlobal,
            string[] commandsToIgnore = null, AttachmentCommandItem attachmentCommand = null)
        {
            if (commandGroups != null)
            {
                foreach (CpiCommandGroup cpiCommandGroup in commandGroups)
                {
                    CommandGroup commandGroup = LoadCommandGroup(projectionName, cpiCommandGroup, visibleWhenDisabled, includeRelatedPages, includeGlobal, commandsToIgnore);
                    CommandGroups.Add(commandGroup);
                }
            }

            if (attachmentCommand != null)
            {
                attachmentCommand.CalculateCount = false;
                CommandGroup group = new CommandGroup
                {
                    attachmentCommand
                };
                CommandGroups.Add(group);
                AttachmentCommand = attachmentCommand;
            }
            else
            {
                AttachmentCommand = null;
            }
        }

        private CommandGroup LoadCommandGroup(string projectionName, CpiCommandGroup cpiCommandGroup, bool visibleWhenDisabled, bool includeRelatedPages, bool includeGlobal, string[] commandsToIgnore)
        {
            CommandGroup group = new CommandGroup();
            if (cpiCommandGroup.CommandNames != null)
            {
                foreach (string commandName in cpiCommandGroup.CommandNames)
                {
                    if (commandsToIgnore != null && commandsToIgnore.Contains(commandName))
                    {
                        continue;
                    }

                    CpiCommandItem command = LoadCommand(projectionName, commandName, includeRelatedPages, includeGlobal);

                    if (command != null)
                    {
                        command.VisibleWhenDisabled = visibleWhenDisabled;
                        command.PropertyChanged += Command_PropertyChanged;
                        AddCommand(command);
                        group.Add(command);
                    }
                }
            }

            return group;
        }

        private void Command_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(CpiCommandItem.IsEnabled))
            {
                EnabledChanged?.Invoke(this, new CommandEnabledChangedEventArgs((CpiCommandItem)sender));
            }
            else if (e.PropertyName == nameof(CpiCommandItem.IsVisible))
            {
                VisibilityChanged?.Invoke(this, new CommandEnabledChangedEventArgs((CpiCommandItem)sender));
            }
        }

        private CpiCommandItem LoadCommand(string projectionName, string commandName, bool includeRelatedPages, bool includeGlobal)
        {
            CpiCommand command = _metadata.FindCommand(projectionName, commandName);

            if (command == null ||
                (!includeRelatedPages && CommandExecutor.IsRelatedPageCommand(command)) ||
                (!includeGlobal && command.Selection == CpiCommandSelection.Global) || (includeGlobal && command.Selection == CpiCommandSelection.SingleRecord))
            {
                return null;
            }

            return new CpiCommandItem(_commandExecutor, _expressionRunner, projectionName, command);
        }

        protected void AddCommand(FwCommandItem commandItem)
        {
            if (ViewData != null)
            {
                commandItem.ViewData = ViewData;
            }
            else
            {
                commandItem.PageData = PageData;
            }

            commandItem.IsExecuting.ParentState = UpdatingState;
            Commands.Add(commandItem);
        }

        private void UpdateCommandData()
        {
            foreach (FwCommandItem commandItem in Commands)
            {
                if (ViewData != null)
                {
                    commandItem.ViewData = ViewData;
                }
                else
                {
                    commandItem.PageData = PageData;
                }
            }

            if (AttachmentCommand != null)
            {
                if (ViewData != null)
                {
                    AttachmentCommand.ViewData = ViewData;
                }
                else
                {
                    AttachmentCommand.PageData = PageData;
                }
            }
        }

        private Task<ExecuteResult> AfterCommandExecute(ViewData data, CpiCommand command, CommandOptions options, ExecuteResult result)
        {
            CommandExecuted?.Invoke(this, new CommandExecutedEventArgs(command, result));
            return Task.FromResult(result);
        }

        private void OnPageDataChanged(PageData oldValue, PageData newValue)
        {
            if (oldValue != null)
            {
                oldValue.RecordDataChanged -= DataChanged;
            }

            if (newValue != null)
            {
                newValue.RecordDataChanged += DataChanged;
                ViewData = null;
            }

            _ = TriggerUpdate(true);
        }

        private void OnViewDataChanged(ViewData oldValue, ViewData newValue)
        {
            if (oldValue != null)
            {
                oldValue.Record.DataChanged -= DataChanged;
            }

            if (newValue != null)
            {
                newValue.Record.DataChanged += DataChanged;
                PageData = null;
            }

            _ = TriggerUpdate(true);
        }

        private void DataChanged(object sender, EventArgs e)
        {
            _ = TriggerUpdate(false);
        }

        public async Task TriggerUpdate(bool isUpdateCommandData = false)
        {
            if (isUpdateCommandData)
            {
                await _debouncer.DebounceAsync(() => PerformUpdates(), DeviceInfo.OperatingSystem == Ifs.Uma.Utility.OperatingSystem.Windows ? 500 : 1000);
            }
            else
            {
                await _debouncer.DebounceAsync(() => UpdateStatesTask(), DeviceInfo.OperatingSystem == Ifs.Uma.Utility.OperatingSystem.Windows ? 500 : 1000);
            }
        }

        private Task UpdateStatesTask()
        {
            UpdateStates();
            return Task.CompletedTask;
        }

        private Task PerformUpdates()
        {
            UpdateCommandData();
            UpdateStates();
            return Task.CompletedTask;
        }

        protected virtual void UpdateStates()
        {
            bool shouldFireVisibilityChanged = false;

            foreach (FwCommandItem commandItem in Commands)
            {
                bool visibilityBefore = commandItem.IsVisible;
                bool enabledBefore = commandItem.IsEnabled;

                commandItem.UpdateStates(UpdatingState.IsAnythingUpdating);

                if (commandItem.IsVisible != visibilityBefore || IsCustomCommandBlock)
                {
                    shouldFireVisibilityChanged = true;
                }

                if (commandItem.IsEnabled != enabledBefore)
                {
                    EnabledChanged?.Invoke(this, new CommandEnabledChangedEventArgs(commandItem));
                }
            }

            CommandGroups.RefreshFilter();

            if (shouldFireVisibilityChanged)
            {
                VisibilityChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        private void OnAttachmentCommandChanged(AttachmentCommandItem oldValue, AttachmentCommandItem newValue)
        {
            if (oldValue != null)
            {
                oldValue.PageData = null;
                oldValue.ViewData = null;
                oldValue.PropertyChanged -= AttachmentCommand_PropertyChanged;
            }

            if (newValue != null)
            {
                newValue.PropertyChanged += AttachmentCommand_PropertyChanged;

                if (ViewData != null)
                {
                    newValue.ViewData = ViewData;
                }
                else
                {
                    newValue.PageData = PageData;
                }
            }
        }

        private void AttachmentCommand_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(AttachmentCommand.IsEnabled))
            {
                EnabledChanged?.Invoke(this, new CommandEnabledChangedEventArgs(AttachmentCommand));
            }
            else if (e.PropertyName == nameof(AttachmentCommand.IsVisible))
            {
                CommandGroups.RefreshFilter();
                VisibilityChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        public virtual void GetSelectAttributes(ICollection<string> attributes)
        {
            foreach (FwCommandItem item in Commands)
            {
                item.GetSelectAttributes(attributes);
            }
        }
    }

    public class CommandEnabledChangedEventArgs : EventArgs
    {
        public CommandItem CommandItem { get; }

        public CommandEnabledChangedEventArgs(CommandItem commandItem)
        {
            CommandItem = commandItem;
        }
    }

    public class CommandExecutedEventArgs : EventArgs
    {
        public CpiCommand CpiCommand { get; }
        public ExecuteResult Result { get; }

        public CommandExecutedEventArgs(CpiCommand cpiCommand, ExecuteResult result)
        {
            CpiCommand = cpiCommand;
            Result = result;
        }
    }
}
