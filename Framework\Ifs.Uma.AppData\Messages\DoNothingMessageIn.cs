﻿using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Messages
{
    internal sealed class DoNothingMessageIn : MessageIn
    {
        public DoNothingMessageIn(MessageType messageType)
            : base(messageType)
        {
        }

        protected override void OnExecute(FwDataContext ctx, DbCommand command, IClientKeysMapper clientKeysMapper,
            ILogger logger, DataChangeSet dataChangeSet, TransitionRow transitionRow, bool isInitializing)
        {
        }
    }
}
