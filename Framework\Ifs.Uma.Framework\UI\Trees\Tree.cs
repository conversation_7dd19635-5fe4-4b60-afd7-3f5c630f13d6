﻿using System;
using System.Collections.Specialized;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.Framework.UI.Trees.Nodes;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Trees
{
    public class Tree : ViewableCollection<Node>
    {
        public const int PageSize = 50;

        public Tree(Node rootNode)
        {
            RootNode = rootNode;
        }

        private Node _rootNode;

        public Node RootNode
        {
            get
            {
                return _rootNode;
            }
            set
            {
                if (_rootNode != value)
                {
                    if (_rootNode != null)
                    {
                        _rootNode.TreeChildren.CollectionChanged -= TreeChildren_CollectionChanged;
                        Clear();
                    }
                    _rootNode = value;
                    for (int i = 0; i < _rootNode.TreeChildren.Count; i++)
                    {
                        Insert(i, _rootNode.TreeChildren[i]);
                    }
                    _rootNode.TreeChildren.CollectionChanged += TreeChildren_CollectionChanged;
                }
            }
        }

        public event EventHandler LoadedMoreItems;

        public bool LoadMoreLock { private get; set; }

        public UpdatingState LoadingState { get; } = new UpdatingState();

        public async Task<int> LoadMoreNodes()
        {
            int r = 0;
            if (!LoadMoreLock)
            {
                LoadMoreLock = true;
                using (LoadingState.BeginUpdating())
                {
                    r = await _rootNode.LoadChildren(PageSize);
                }
                LoadedMoreItems?.Invoke(this, EventArgs.Empty);
                LoadMoreLock = false;
            }
            return r;
        }

        public static Node GetSelectedNode(ViewableCollection<Node> tree, TreePage treePage)
        {
            if (treePage.SelectedNode == null)
            {
                return null;
            }

            Node node = tree.FirstOrDefault(x => x.Id == treePage.SelectedNode.Id);

            // This could happen if the tree has been reset/changed during the life-cycle of the tree page.
            if (node != null && treePage.SelectedNode != node)
            {
                treePage.SelectedNode = node;
            }

            return node;
        }

        public Node GetNodeWithId(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return null;
            }

            return this.FirstOrDefault(x => x.Id == id);
        }

        private void TreeChildren_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                for (int i = 0; i < e.NewItems.Count; i++)
                {
                    int nodeIndex = e.NewStartingIndex + i;
                    Insert(nodeIndex, (Node)e.NewItems[i]);
                }
            }
            else if (e.Action == NotifyCollectionChangedAction.Remove && e.OldItems != null)
            {
                for (int i = 0; i < e.OldItems.Count; i++)
                {
                    Remove((Node)e.OldItems[i]);
                }
            }
        }
    }
}
