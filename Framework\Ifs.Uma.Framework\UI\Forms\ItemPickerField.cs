﻿using System.Collections.Generic;
using Ifs.Uma.AppData;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Fields;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Data;
using System.Linq;
using System.Collections;
using System;
using Ifs.Uma.AppData.AttributeExpressions;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Expressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Forms
{
    public class ItemPickerField : ComboMultiSelectField
    {
        private EntityDataSource _dataSource;
        private ViewData _viewData;
        private RecordData _recordData;

        protected override bool IgnoreIfItemsSourceIsNull => true;

        public IExpressionRunner ExpressionRunner { get; set; }

        public override object Value
        {
            get
            {
                object baseValue = base.Value; // avoid multiple calls to base.Value
                if (baseValue is List<object> enumerableBaseValue)
                {
                    return GetItemsFromItemsSource(enumerableBaseValue);
                }
                else if (baseValue is string stringValue)
                {
                    return GetItemsFromItemsSource(stringValue);
                }
                else
                {
                    return baseValue;
                }
            }
            set
            {
                if (value is IEnumerable selectableItems)
                {
                    base.Value = SelectableItemsToString(selectableItems) ?? value;
                }
                else
                {
                    base.Value = value;
                }
            }
        }

        public IMetadata Metadata { get; set; }

        public IDataHandler DataHandler { get; set; }
        
        public ILogger Logger { get; set; }

        public CpiItemPicker CpiItemPicker { get; set; }

        public async Task UpdateStates(ViewData viewData)//Pass in ViewData rather than RecordData (ViewData contains Record Data and means when we call FromJsonLogic we can pass in a value provider)
        {
            _viewData = viewData;
            _recordData = _viewData?.Record;

            EntityDataSource newDataSource = GetDatasource(_recordData);
            if (EntityDataSource.IsTheSame(_dataSource, newDataSource))
            {
                return;
            }

            _dataSource = newDataSource;

            if (base.Value is string stringValues) // base.Value as we don't need it to process any of the items for us
            {
                List<string> deserializedValues = JsonConvert.DeserializeObject(stringValues, typeof(List<string>)) as List<string>;

                List<ObjPrimaryKey> primaryKeys = new List<ObjPrimaryKey>();
                foreach (string itemId in deserializedValues)
                {
                    ObjPrimaryKey objPrimaryKey = ObjPrimaryKey.FromKeyRef(Metadata.MetaModel, _dataSource.Table.TableName, itemId);
                    if (objPrimaryKey != null)
                    {
                        primaryKeys.Add(objPrimaryKey);
                    }
                }

                AttributeExpression filterExpression = AttributeExpression.FromObjPrimaryKeys(primaryKeys);
                await LoadData(filterExpression);
            }
        }

        public override Task<object> GetItemsSourceAsync() => LoadData();

        private async Task<object> LoadData(AttributeExpression filterExpression = null)
        {
            EntityQuery query = new EntityQuery(_dataSource);

            bool enableOrdering = CpiItemPicker.OrderBy != null && CpiItemPicker.EnableOrdering.HasValue ? CpiItemPicker.EnableOrdering.Value : true;
            if (enableOrdering)
            {
                query.AddSorts(CpiItemPicker.OrderBy);
            }
            query.FilterExpression = filterExpression;
            JToken offlineFilter = CpiItemPicker.OfflineFilter;
            if (offlineFilter != null)
            {
                try
                {
                    AttributeExpression attributeExpression = AttributeExpression.FromJsonLogic(offlineFilter, _viewData);
                    query.FilterExpression = attributeExpression;
                }
                catch (Exception e)
                {
                    Logger.HandleException(ExceptionType.Recoverable, e); 
                }
            }
            List<SelectableItem<object>> selectableItems = new List<SelectableItem<object>>();
            try
            {
                EntityQueryResult result = await DataHandler.GetRecordsAsync(query, CancellationToken.None);
                if (result?.Records != null)
                {
                    RecordData entityRecordData = _recordData.CreateNew();
                    foreach (EntityRecord record in result.Records)
                    {
                        entityRecordData.LoadRecord(_dataSource.ProjectionName, record);
                        string displayValue = ExpressionRunner.InterpolateString(CpiItemPicker.DisplayValue, entityRecordData);
                        selectableItems.Add(new SelectableItem<object>(displayValue, record.Row));
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.HandleException(ExceptionType.Recoverable, ex);
            }

            ItemsSource = selectableItems;
            return selectableItems;
        }

        private EntityDataSource GetDatasource(RecordData recordData)
        {
            // Check if the dataSource is a function..
            if (!string.IsNullOrWhiteSpace(CpiItemPicker.DatasourceFunction) && CpiItemPicker.DatasourceFunctionParams != null)
            {
                Dictionary<string, object> parameterValues = new Dictionary<string, object>();
                recordData?.ExtractFunctionParameters(CpiItemPicker.DatasourceFunctionParams, parameterValues);
                return FunctionDataSource.Create(Metadata, CpiItemPicker.DatasourceProjection, CpiItemPicker.DatasourceFunction, parameterValues);
            }

            // Try and get the dataSource from the entity set if it isn't a function
            return EntityDataSource.FromEntitySet(Metadata, CpiItemPicker.DatasourceProjection, CpiItemPicker.DatasourceEntitySet);
        }

        private string SelectableItemsToString(IEnumerable selectableItems)
        {
            List<string> identifiersToSerialize = new List<string>();
            foreach (ISelectableItem selectedValue in selectableItems)
            {
                RemoteRow selectedRemoteRow = (RemoteRow)selectedValue.Value;
                string rowIdentifier = ObjPrimaryKey.FromPrimaryKey(Metadata.MetaModel, selectedRemoteRow)?.ToKeyRef();

                if (string.IsNullOrWhiteSpace(rowIdentifier))
                {
                    identifiersToSerialize.Add(selectedValue.Label);
                }
                else
                {
                    identifiersToSerialize.Add(rowIdentifier);
                }
            }

            if (identifiersToSerialize.Count == 0)
            {
                return null;
            }

            // Should end up with a string like:
            // ["COMPANY=COM12^CUSTOMER_NO=CUST105^","COMPANY=COM1^CUSTOMER_NO=CUST169^"]
            return JsonConvert.SerializeObject(identifiersToSerialize);
        }

        protected override object ProcessStringBaseValue(string stringBaseValue)
        {
            if (ItemsSource == null)
            {
                return stringBaseValue;
            }
            else
            {
                return GetItemsFromItemsSource(stringBaseValue);
            }
        }

        private IEnumerable<ISelectableItem> GetItemsFromItemsSource(string itemsIds)
        {
            List<string> itemsIdsList = JsonConvert.DeserializeObject(itemsIds, typeof(List<string>)) as List<string>;
            return GetItemsFromItemsSource(itemsIdsList);
        }

        private IEnumerable<ISelectableItem> GetItemsFromItemsSource(IList itemIds)
        {
            if (itemIds?.Count == 0 || ItemsSource == null)
                return null;

            List<ISelectableItem> selectedItems = new List<ISelectableItem>();
            List<string> itemIdsList = itemIds.Cast<string>().ToList();

            if (ItemsSource is IEnumerable<ISelectableItem> selectableItems)
            {
                foreach (ISelectableItem selectableItem in selectableItems)
                {
                    if (selectableItem.Value is RemoteRow remoteRow)
                    {
                        string rowIdentifier = ObjPrimaryKey.FromPrimaryKey(Metadata.MetaModel, remoteRow)?.ToKeyRef();

                        // Try identify either with the primaryKey or with the Label
                        if (itemIdsList.Contains(rowIdentifier)
                            || (rowIdentifier == null && itemIdsList.Contains(selectableItem.Label)))
                        {
                            selectedItems.Add(selectableItem);
                        }

                        if (selectedItems.Count == itemIdsList.Count)
                        {
                            break;
                        }
                    }
                }
            }

            return selectedItems;
        }
    }
}
