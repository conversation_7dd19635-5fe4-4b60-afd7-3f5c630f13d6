﻿#if REMOTE_ASSISTANCE
using System;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    public class CallHistoryItem
    {
        public int LogId { get; }
        public int EventId { get; }
        public int ActiveGroupUserCount { get; }
        public string FromFndUserId { get; }
        public string FromFndUserName { get; }
        public string ToFndUserName { get; }
        public string ReceiverUserId { get; }
        public string ReceiverName { get; }
        public string ToGroupName { get; }
        public RemoteLogEventType? EventTypeDb { get; }
        public RemoteAssistDeclineType? DeclinedReasonCodeDb { get; }
        public string CallStatus { get; }
        public RemoteAssistCallType? CallTypeDb { get; }
        public DateTime Timestamp { get; }

        public CallHistoryItem(string logId, string eventId, string activeGroupUserCount, string fromFndUserId, string fromFndUserName, string receiverUserId, string receiverName, string toGroupName, string toFndUserName, string eventType, string declinedReasonCode, string callType, string callStatus, string timestamp)
        {
            if (int.TryParse(logId, out int logIdResult))
            {
                LogId = logIdResult;
            }

            if (int.TryParse(eventId, out int eventIdResult))
            {
                EventId = eventIdResult;
            }

            if (int.TryParse(activeGroupUserCount, out int activeGroupUserCountResult))
            {
                ActiveGroupUserCount = activeGroupUserCountResult;
            }

            FromFndUserId = fromFndUserId;
            FromFndUserName = fromFndUserName;
            ReceiverUserId = receiverUserId;
            ReceiverName = receiverName;
            ToGroupName = toGroupName;
            ToFndUserName = toFndUserName;
            CallStatus = callStatus;

            if (Enum.TryParse(eventType, out RemoteLogEventType eventTypeResult))
            {
                EventTypeDb = eventTypeResult;
            }

            if (Enum.TryParse(declinedReasonCode, out RemoteAssistDeclineType declinedReasonCodeResult))
            {
                DeclinedReasonCodeDb = declinedReasonCodeResult;
            }

            if (Enum.TryParse(callType, out RemoteAssistCallType callTypeResult))
            {
                CallTypeDb = callTypeResult;
            }

            if (DateTime.TryParse(timestamp, out DateTime timestampResult))
            {
                Timestamp = timestampResult;
            }
        }

        public enum RemoteLogEventType
        {
            Dialed,
            Requested,
            NotAvailable,
            Accepted,
            Declined,
            Cancelled,
            Joined,
            Left
        }

        public enum RemoteAssistDeclineType
        {
            Declined,
            Busy,
            NotAnswered,
            ExpertNotAvailable
        }

        public enum RemoteAssistCallType
        {
            Outgoing,
            Incoming
        }
    }
}
#endif
