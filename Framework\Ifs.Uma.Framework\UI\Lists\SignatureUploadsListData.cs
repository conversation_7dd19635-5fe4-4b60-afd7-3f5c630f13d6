﻿#if SIGNATURE_SERVICE
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Localization;
using Ifs.Uma.Signing.Transactions;
using Ifs.Uma.UI.Services;
using Unity.Attributes;

namespace Ifs.Uma.Framework.UI.Lists
{
    public class SignatureUploadsListData : ListData<DigitalSignatureAndDocument>
    {
        private readonly IDataContextProvider _db;
        private readonly IDialogService _dialogService;
        private ISignatureSyncService _signatureSyncService;

        public SignatureUploadsListData(IDataContextProvider db, [OptionalDependency] IDialogService dialogService) 
            : base(null)
        {
            _db = db;
            _dialogService = dialogService;
        }

        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                Resolver.TryResolve(out _signatureSyncService);
                UpdateAsync();
            }
            else
            {
                _signatureSyncService = null;
                CancelUpdateAsync();
            }
        }

        protected override async void OnSelectedItemChanged()
        {
            base.OnSelectedItemChanged();

            DigitalSignatureAndDocument selectedItem = SelectedItem;
            if (selectedItem?.ErrorMessage != null && selectedItem?.ErrorMessage != SignatureSyncService.ValidationIndicationTotalPassed && _dialogService != null &&
                await HandleFailedTransaction(selectedItem))
            {
                await UpdateAsync();
                _signatureSyncService.RequestSync();
            }

            SelectedItem = null;
        }

        private async Task<bool> HandleFailedTransaction(DigitalSignatureAndDocument item)
        {
            if (item is null)
            {
                throw new ArgumentNullException(nameof(item));
            }

            //Prevent multiple dialogs to be shown at the same time
            await _dialogService.WaitForDialogsToClose(CancellationToken.None);

            List<string> actions = new List<string>();
            actions.Add(Strings.Delete);
            actions.Add(Strings.Retry);
            actions.Add(Strings.Cancel);

            int selectedOption = await _dialogService.ShowAsync(string.Format("{0} - {1}", item.LuName, item.KeyRef), item.ErrorMessage, actions.ToArray());
            string selectedAction = actions.ElementAt(selectedOption);

            if (selectedAction.Equals(Strings.Delete))
            {
                _signatureSyncService.DeleteSignatureTransaction(item.Guid);
                return true;
            }
            else if (selectedAction.Equals(Strings.Retry))
            {
                await _signatureSyncService.RetrySigning(item.Guid);
                return true;
            }

            return false;
        }

        protected override async Task OnUpdateAsync()
        {
            DigitalSignatureAndDocument[] rows = null;

            await Task.Run(() =>
            {
                try
                {
                    FwDataContext ctx = _db.CreateDataContext();
                    rows = ctx.DigitalSignatureAndDocument.Where(x => x.ErrorMessage != null).ToArray();
                }
                catch (Exception)
                {
                    rows = new DigitalSignatureAndDocument[0];
                }
            });

            using (Items.DeferRefresh())
            {
                Items.Clear();

                if (rows != null)
                {
                    foreach (DigitalSignatureAndDocument row in rows)
                    {
                        Items.Add(row);
                    }
                }
            }
        }
    }
}
#endif
