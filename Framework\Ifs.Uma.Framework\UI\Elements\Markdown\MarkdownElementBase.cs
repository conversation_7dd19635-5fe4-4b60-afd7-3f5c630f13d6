﻿using Ifs.Uma.UI.Controls;

namespace Ifs.Uma.Framework.UI.Elements.Markdown
{
    public abstract class MarkdownElementBase : ElementBase
    {
        private MarkdownData _markdown;
        public MarkdownData Markdown
        {
            get => _markdown;
            protected set => SetProperty(ref _markdown, value);
        }

        protected MarkdownElementBase()
        {
            HasHeader = false;
            _markdown = new MarkdownData();
        }
    }
}
