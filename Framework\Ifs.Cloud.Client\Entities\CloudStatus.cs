﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  2012-02-02 PKULLK Created.
#endregion

using Ifs.Cloud.Client.Utils;
using System;
using System.Runtime.Serialization;
using System.Text;

namespace Ifs.Cloud.Client.Entities
{
    /// <summary>
    /// Data contract to cloud resource CloudStatus
    /// </summary>
    [DataContract]
    public class CloudStatus
    {
        #region Constants
#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
        public const string StatusOk               = "OK";
        public const string StatusApplicationError = "ApplicationError";
        public const string StatusSystemError      = "SystemError";
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row
        #endregion

        #region Data Bound Properties
        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public string Source { get; set; }

        [DataMember]
        public string Message { get; set; }

        [DataMember]
        public string InvocationUser { get; set; }

        [DataMember]
        public string InvocationTime { get; set; }
        #endregion

        #region Application Specific Properties
        private DateTime? InvocationTimeValue => string.IsNullOrEmpty(InvocationTime) ? (DateTime?)null : CloudTypeConverter.SerialToDateTime(InvocationTime);

        private string StatusPrompt
        {
            get
            {
                switch (Status)
                {
#pragma warning disable SA1025 // Code must not contain multiple whitespace in a row
                    case StatusOk:               return "OK";
                    case StatusApplicationError: return "Applicaiton Error";
                    case StatusSystemError:      return "System Error";
                    default:                     return Status; // unknown status - just use it as it is...
#pragma warning restore SA1025 // Code must not contain multiple whitespace in a row
                }
            }
        }
        #endregion

        #region Overrides
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("Status : ").Append(StatusPrompt).AppendLine();
            sb.Append("Message: ").Append(Message).AppendLine();
            sb.Append("Source : ").Append(Source).AppendLine();
            sb.Append("Invocation User: ").Append(InvocationUser).AppendLine();
            sb.Append("Invocation Time: ").Append(InvocationTimeValue.HasValue ? InvocationTimeValue.Value.ToString("s") : "N/A").AppendLine();

            return sb.ToString();
        }
        #endregion
    }
}
