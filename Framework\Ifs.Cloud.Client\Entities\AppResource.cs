﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Ifs.Cloud.Client.Entities
{
    [DataContract]
    public abstract class AppResource : BaseResource
    {
        protected virtual string ResourceSection { get; } = "system";

        public virtual bool TimeZoneAwareResource { get; set; }

        public virtual string SiteTimeZone { get; set; }      

        public override string GetResourceName(ClientInfo clientInfo)
        {
            //return clientInfo.AppName + "/" + ResourceSection + "/" + ResourceName;
            return ResourceName;
        }
    }

    [DataContract]
    public abstract class InMessageResource : BaseResource 
    {
        protected virtual string ResourceSection { get; } = "system";
        public string EntityName { get; set; }
        public string ProjectionName { get; set; }
    }

    [DataContract]
    public class ActivateResourceBase : BaseResource
    {
        protected virtual string ResourceSection { get; } = "system";
        public override string ResourceName => "MobileClientRuntime.svc/ActivateDevice";
        
        public override string GetResourceName(ClientInfo clientInfo)
        {
            return ResourceName;
        }
    }

    [DataContract]
    public class UsernameResource : BaseResource
    {
        protected virtual string ResourceSection { get; } = "system";
        public override string ResourceName => "MobileClientRuntime.svc/GetCurrentUserId()";
        public override bool SingleResponse { get { return true; } }

        [DataMember(Name = "@odata.context")]
        public string Context { get; set; }

        [DataMember(Name = "value")]
        public string Value { get; set; }

        public override string GetResourceName(ClientInfo clientInfo)
        {
            return ResourceName;
        }
    }

    [DataContract]
    public class GetServerVersionResource : BaseResource
    {
        protected virtual string ResourceSection { get; } = "system";

        public override string ResourceName => $"/mob/ifsapplications/projection/$version";

        public override bool SingleResponse { get { return true; } }

        public override bool FullNameDefined { get { return true; } }

        [DataMember(Name = "Implementation-Version")]
        public string ImplementationVersion { get; set; }
    }

    [DataContract]
    public abstract class AuthResource : BaseResource
    {
        protected virtual string ResourceSection { get; } = "system";

        public override string GetResourceName(ClientInfo clientInfo)
        {
            return ResourceName;
        }
    }

    [DataContract]
    public class ODataWrapperClass
    {
        [DataMember(Name = "@odata.context")]
        public string Context { get; set; }

        [DataMember(Name = "value")]
        public string Value { get; set; }
    }

    [DataContract]
    public class ODataWrapperClassArray<T>
    {
        [DataMember(Name = "@odata.context")]
        public string Context { get; set; }

        [DataMember(Name = "value")]
        public T[] Value { get; set; }
    }

    [DataContract]
    public class ODataDebugWrapper
    {
        [DataMember(Name = "ifs-trace")]
        public Dictionary<string, string> Trace { get; set; }

        [DataMember(Name = "request")]
        public Dictionary<string, string> Request { get; set; }

        [DataMember(Name = "response")]
        public ODataDebugResponse Response { get; set; }

        [DataMember(Name = "server")]
        public Dictionary<string, string> Server { get; set; }
    }

    [DataContract]
    public class ODataDebugResponse
    {
        [DataMember(Name = "body")]
        public string Body { get; set; }

        [DataMember(Name = "headers")]
        public Dictionary<string, string> Headers { get; set; }

        [DataMember(Name = "status")]
        public Dictionary<string, string> Status { get; set; }
    }

    [DataContract]
    public class ODataErrorResponse
    {
        [DataMember(Name = "error")]
        public Error Error { get; set; }
    }

    [DataContract]
    public class Error
    {
        [DataMember(Name = "code")]
        public string Code { get; set; }

        [DataMember(Name = "message")]
        public string Message { get; set; }

        [DataMember(Name = "details")]
        public ErrorDetail[] Details { get; set; }
    }

    [DataContract]
    public class ErrorDetail
    {
        [DataMember(Name = "code")]
        public string Code { get; set; }

        [DataMember(Name = "message")]
        public string Message { get; set; }
    }
}
