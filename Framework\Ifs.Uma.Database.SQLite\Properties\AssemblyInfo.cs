﻿using System;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// General Information about an assembly is controlled through the following 
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.

[assembly: AssemblyTitle("IFS UMA Database (SQLite)")]
[assembly: AssemblyDescription("Portable SQLite database classes")]
[assembly: AssemblyProduct("Ifs.Uma.Database.SQLite")]

[assembly: CLSCompliant(true)]
[assembly: ComVisible(false)]

[assembly: AssemblyFileVersion("*******")]