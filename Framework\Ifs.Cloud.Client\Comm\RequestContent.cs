﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  xxxx-xx-xx SUIJLK Created.

#endregion

using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Linq;
using Ifs.Cloud.Client.Interfaces;

namespace Ifs.Cloud.Client.Comm
{
    /// <summary>
    /// Represents the contents of a REST cloud call
    /// T should be of type Ifs.WindowstStore.CloudClient.Entities.BaseResource
    /// </summary>    
    public class RequestContent<T> where T : Entities.BaseResource
    {
        private ResourceSerializeWrapper _wrapper;
        private Type _baseResourceType;
        //private bool binaryGetRequest = false;

        /// <summary>
        /// Constructor of RequestContent
        /// </summary>
        /// <param name="baseResourceRef">instance of T that the request would use</param>
        public RequestContent(T baseResourceRef)
        {
            Resource = baseResourceRef;
            _wrapper = new ResourceSerializeWrapper(Resource);            
            _baseResourceType = baseResourceRef.GetType();
        }
        
        /// <summary>
        /// Page
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; } = -1;

        /// <summary>
        /// The instance or the Resource T set to this RequestContent
        /// </summary>
        public T Resource { get; private set; }

        public void ResetResource(T resource)
        {
            Resource = resource;
            _wrapper = new ResourceSerializeWrapper(Resource);
            _baseResourceType = resource.GetType();
        }

        public string GetResourceAsUrlParam()
        {
            PropertyInfo[] p = Resource.GetType().GetRuntimeProperties().ToArray();

            Dictionary<string, string> nameValuePair = new Dictionary<string, string>();

            foreach (PropertyInfo prop in p)
            {
                //IList<CustomAttributeData> attributes = (IList<CustomAttributeData>)prop. CustomAttributes;
                Attribute[] attributes = Array.ConvertAll(prop.GetCustomAttributes(false), x => x as Attribute);
                if (attributes != null)
                {
                    bool containsDataMember = false;
                    foreach (Attribute custAtt in attributes)
                    {
                        if (custAtt is DataMemberAttribute)
                        {
                            containsDataMember = true;
                            break;
                        }
                    }
                    if (containsDataMember)
                    {
                        object val = prop.GetValue(Resource, null);
                        if (val != null)
                        {
                            nameValuePair.Add(prop.Name, val.ToString());
                        }
                    }
                }
            }
            return Utils.Formatter.ToQueryString(nameValuePair);
        }
               
        /// <summary>
        /// Returns this content object as a <code>ByteArrayContent</code>
        /// </summary>
        /// <returns>ByteArrayContent</returns>
        public ByteArrayContent ToByteArray()
        {
            ICustomResourceSerializer resource = Resource as ICustomResourceSerializer;
            if (resource != null)
            {
                return new ByteArrayContent(Encoding.UTF8.GetBytes(resource.SerializeToJsonString()));
            }

            DataContractJsonSerializer serializer = new DataContractJsonSerializer(typeof(T));
            using (MemoryStream stream = new MemoryStream())
            {
                serializer.WriteObject(stream, Resource);
                stream.Flush();
                return new ByteArrayContent(stream.ToArray());
            }
        }

        internal ByteArrayContent ToByteArrayContent()
        {
            List<Type> types = new List<Type>();
            types.Add(_baseResourceType);
            types.Add(typeof(List<string>));
            types.Add(typeof(Entities.BaseResource));
            DataContractJsonSerializer serializer = new DataContractJsonSerializer(typeof(ResourceSerializeWrapper), types);            
            using (MemoryStream stream = new MemoryStream())
            {
                serializer.WriteObject(stream, _wrapper);
                stream.Flush();
                return new ByteArrayContent(stream.ToArray());
            }
        }
        
        /// <summary>
        /// The wrapper that would create the request parameters
        /// </summary>
        [DataContract]
        public class ResourceSerializeWrapper
        {
            private readonly Entities.BaseResource _res;
            private readonly Dictionary<string, object> _resourceParams = new Dictionary<string, object>();

            public ResourceSerializeWrapper(Entities.BaseResource resource)
            {
                _res = resource;
            }

            [DataMember]
            public Dictionary<string, object> Parameters => GetSetValues();

            private Dictionary<string, object> GetSetValues()
            {                
                PropertyInfo[] p = _res.GetType().GetRuntimeProperties().ToArray();
                
                foreach (PropertyInfo prop in p)
                {
                    Attribute[] attributes = Array.ConvertAll(prop.GetCustomAttributes(false), x => x as Attribute);
                    if (attributes != null)
                    {
                        bool containsDataMember = false;
                        foreach (Attribute custAtt in attributes)
                        {
                            if (custAtt is DataMemberAttribute)
                            {
                                containsDataMember = true;
                                break;
                            }
                        }
                        if (containsDataMember)
                        {
                            object val = prop.GetValue(_res, null);
                            if (val != null)
                            {
                                _resourceParams[prop.Name] = val;
                            }
                            else
                            {
                                if (_resourceParams.ContainsKey(prop.Name))
                                {
                                    _resourceParams.Remove(prop.Name);
                                }
                            }
                        }
                    }
                }
                return _resourceParams;
            }
        }
    }
}
