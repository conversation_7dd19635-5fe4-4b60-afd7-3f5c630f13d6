﻿using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Localization;

namespace Ifs.Uma.AppData.Execution
{
    public sealed class UnfinishedTransactionsException : ProcedureException
    {
        public TransactionWaitResult WaitResult { get; }

        public UnfinishedTransactionsException(TransactionWaitResult waitResult)
            : base(GetMessage(waitResult))
        {
            WaitResult = waitResult;
        }

        private static string GetMessage(TransactionWaitResult waitResult)
        {
            if (waitResult == TransactionWaitResult.FailingTransactions)
            {
                return Strings.UnfinishedTransactionsFailing;
            }
            else
            {
                return Strings.UnfinishedTransactionsWaiting;
            }
        }
    }
}
