﻿using System;
using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class ExistsExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.Exists;
        public override Type Type => typeof(bool);

        public Expression Expression { get; }

        internal ExistsExpression(Expression expression)
        {
            Expression = expression ?? throw new ArgumentNullException(nameof(expression));
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitExistsExpression(this);
        }

        public override string ToString()
        {
            return Expression.ToString();
        }

        public ExistsExpression Update(Expression expression)
        {
            if (expression == Expression)
            {
                return this;
            }

            return new ExistsExpression(expression);
        }
    }

    public partial class IfsExpression
    {
        public static ExistsExpression Exists(Expression expression)
        {
            return new ExistsExpression(expression);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitExistsExpression(ExistsExpression exp)
        {
            Expression updated = Visit(exp.Expression);
            return exp.Update(updated);
        }
    }
}
