﻿using System;
using System.Linq;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Execution.Procedures.Functions.System
{
    internal sealed class SystemGetServerKeys1 : SystemFunction
    {
        public const string FunctionName = "GetServerKeys";

        private static IMetaTable _table;

        public SystemGetServerKeys1()
            : base(FunctionName, 3)
        {
        }
        
        public static ClientKeysMap SetupData(ProcedureContext context, FuncParam[] parameters)
        {
            string tableName = parameters[0].GetString();
            string clientKey = parameters[1].GetString();

            _table = context.Metadata.MetaModel.GetTable(tableName);
            ClientKeysMap mapping = context.DbDataContext.ClientKeysMap.FirstOrDefault(x => x.TableName == tableName && x.ClientKeys == clientKey);

            return mapping;
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            string columnName = parameters[2].GetString();

            ClientKeysMap mapping = SetupData(context, parameters);

            if (mapping != null)
            {
                if (!string.IsNullOrEmpty(columnName))
                {
                    ObjPrimaryKey clientKeys = ObjPrimaryKey.FromKeySeparatedValues(_table, mapping.ClientKeys);

                    foreach (Tuple<IMetaDataMember, object> keyAttribute in clientKeys.Values)
                    {
                        if (keyAttribute.Item1.ColumnName == columnName)
                        {
                            return keyAttribute.Item2;
                        }
                    }
                }

                return mapping.ServerKeys;
            }

            return string.Empty;
        }
    }

    internal sealed class SystemGetServerKeys2 : SystemFunction
    {
        public const string FunctionName = "GetServerKeys";

        public SystemGetServerKeys2()
            : base(FunctionName, 2)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            ClientKeysMap mapping = SystemGetServerKeys1.SetupData(context, parameters);

            if (mapping != null)
            {
                return mapping.ServerKeys;
            }

            return string.Empty;
        }
    }
}
