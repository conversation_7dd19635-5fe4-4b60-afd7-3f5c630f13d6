﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Ifs.Cloud.Client.Exceptions
{
    /// <summary>
    /// Represents the possible types of errors that can occur
    /// when trying to access the cloud
    /// </summary>
    /// <remarks>These constants are originally defined in the cloud framework.<br/>
    /// Should they are changed at cloud side, make sure to update here to reflect the changes.</remarks>
    public enum CloudErrorType
    {
        /// <summary>
        /// A business logic error caused from the application
        /// </summary>
        PropagatedApplicationError,

        /// <summary>
        /// An error that occured outside the cloud (possibly in the extended server, database, etc.)
        /// </summary>
        PropagatedSystemError,

        /// <summary>
        /// An exception thrown from a resource
        /// </summary>
        ResourceException,

        /// <summary>
        /// Application cannot be found
        /// </summary>
        AppNotFound,

        /// <summary>
        /// Resource cannot be found
        /// </summary>
        AppResourceNotFound,

        /// <summary>
        /// Given application version is not supported
        /// </summary>
        AppVersion,

        /// <summary>
        /// Application has not been configured yet
        /// </summary>
        AppConfigurationNotFound,

        /// <summary>
        /// Invalid auth header
        /// </summary>
        InvalidAuthorizationHeader,

        /// <summary>
        /// A request to use this app on this device has been sent
        /// </summary>
        AppDeviceRegistrationRequestSent,

        /// <summary>
        /// Usage of this app on this device is pending approval
        /// </summary>
        AppDevicePendingApproval,

        /// <summary>
        /// Usage of this app on this device has been refused
        /// </summary>
        AppDeviceRejected,

        /// <summary>
        /// Cannot find a system to match given System ID
        /// </summary>
        SystemDoesNotExist,

        /// <summary>
        /// System has been blocked
        /// </summary>
        SystemBlocked,

        /// <summary>
        /// Session timeout
        /// </summary>
        SessionTimeout,

        /// <summary>
        /// Security error
        /// </summary>
        SecurityError,

        /// <summary>
        /// Two Factor Authentication Required
        /// </summary>
        TwoFactorAuthenticationRequired,

        // <summary>
        // Recreate the Secret Key
        // </summary>
        RenewSecretKey,

        // <summary>
        // Invalid Secret Code
        // </summary>
        InvalidSecretCode,

        // <summary>
        // Secret code required
        // </summary>
        SecretCodeRequired,

        // <summary>
        // Wait & retry fetching metadata required
        // </summary>
        MetaDataTimeOut,

        /// <summary>
        /// OpenID token refresh required
        /// </summary>
        TokenRefreshRequired,

        /// <summary>
        /// Only Log error required
        /// </summary>
        DatabaseError,

        /// <summary>
        /// Only Log error required
        /// </summary>
        ODataProviderError,

        /// <summary>
        /// Only Log error required
        /// </summary>
        RequestError,

        /// <summary>
        /// Unknown error
        /// </summary>
        Unknown
    }      
}
