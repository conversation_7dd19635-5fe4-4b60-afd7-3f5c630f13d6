﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Adds relationship to query results depending on policy
    /// </summary>
    internal class RelationshipIncluder : DbExpressionVisitor
    {
        QueryMapper mapper;
        QueryPolicy policy;
        ScopedDictionary<MemberInfo, bool> includeScope = new ScopedDictionary<MemberInfo, bool>(null);

        private RelationshipIncluder(QueryMapper mapper)
        {
            this.mapper = mapper;
            this.policy = mapper.Translator.Police.Policy;
        }

        public static Expression Include(QueryMapper mapper, Expression expression)
        {
            return new RelationshipIncluder(mapper).Visit(expression);
        }

        protected override Expression VisitProjection(ProjectionExpression node)
        {
            if (node == null) return null;
            Expression projector = this.Visit(node.Projector);
            return node.Update(node.Select, projector, node.Aggregator);
        }

        protected override Expression VisitEntity(EntityExpression node)
        {
            var save = this.includeScope;
            this.includeScope = new ScopedDictionary<MemberInfo, bool>(this.includeScope);
            try
            {
                if (this.mapper.HasIncludedMembers(node))
                {
                    node = this.mapper.IncludeMembers(
                        node,
                        m =>
                        {
                            if (m.MemberInfo == null)
                            {
                                // MM: can only do relationships based only members
                                return false;
                            }

                            if (this.includeScope.ContainsKey(m.MemberInfo))
                            {
                                return false;
                            }

                            if (this.policy.IsIncluded(m.MemberInfo))
                            {
                                this.includeScope.Add(m.MemberInfo, true);
                                return true;
                            }

                            return false;
                        });
                }
                return base.VisitEntity(node);
            }
            finally
            {
                this.includeScope = save;
            }
        }
    }
}
