﻿#if REMOTE_ASSISTANCE
using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;

namespace Ifs.Uma.Framework.RemoteAssistance
{
    public interface IRemoteAssistanceService
    {
        TimeSpan Timeout { get; }
        bool IsOnline { get; }
        bool IsDialler { get; set; }
        bool IsEUdatacenter();
        string CurrentSessionID { get; set; }
        RemoteAssistanceSessionInfo Session { get; set; }
        Task<ExecuteResult> CreateSessionAsync(string fndUserId, string groupId, string refUrl, string luName, string keyRef);
        Task<AcceptCallStructure> HandleCallAcceptAsync(string requestId, string sessionToken);
        Task<ExecuteResult> HandleCallDeclineAsync(string requestId, string sessionToken, string customMessage, string reasonCode);
        Task<ExecuteResult> HandleCallCancelAsync(bool? isTimeout = false);
        Task<ExecuteResult> HandleSessionStartedAsync(string callId);
        Task<ExecuteResult> HandleSessionEndedAsync();
        Task<bool> CheckRemoteAssistanceEnabledAsync();
        Task SaveScreenCapturesToMediaLibrary(byte[] imageByteArray, string keyRef, string luName);
    }
}
#endif
