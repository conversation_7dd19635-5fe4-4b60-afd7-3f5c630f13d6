﻿using System;
using System.Collections.ObjectModel;
using System.Linq.Expressions;
using System.Text;
using Ifs.Uma.AppData.Execution.Procedures.Functions.Date;
using Ifs.Uma.AppData.Execution.Procedures.Functions.String;
using Ifs.Uma.AppData.Execution.Procedures.Functions.System;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Database.Expressions
{
    internal sealed class SqlExpressionWriter : IfsExpressionVisitor
    {
        private readonly SqlBuilder _builder;
        private readonly IStatementInfo _info;
        private readonly SqlWriteMode _mode;
        private readonly StringBuilder _sb;

        public static Expression WriteSql(Expression expression, StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
        {
            if (expression == null) throw new ArgumentNullException(nameof(expression));
            if (sb == null) throw new ArgumentNullException(nameof(sb));
            if (info == null) throw new ArgumentNullException(nameof(info));
            if (builder == null) throw new ArgumentNullException(nameof(builder));

            IfsExpressionVisitor visitor = new SqlExpressionWriter(sb, info, builder, mode);
            return visitor.Visit(expression);
        }

        private SqlExpressionWriter(StringBuilder sb, IStatementInfo info, SqlBuilder builder, SqlWriteMode mode)
        {
            _sb = sb;
            _info = info;
            _builder = builder;
            _mode = mode;
        }

        protected internal override Expression VisitVarAccessExpression(VarAccessExpression exp)
        {
            // Variables and columns should have already been replaced by Constants and 
            // DbColumnSpecs by the time we use SqlWriter
            throw new ExpressionException($"Failed to resolve expression identifier '{exp.PropertyPath}'");
        }

        protected internal override Expression VisitAttributeAccessExpression(AttributeAccessExpression exp)
        {
            // Columns should have already been replaced by DbColumnSpecs the time we use SqlWriter
            throw new ExpressionException($"Failed to resolve attribute identifier '{exp.Attribute.Path}'");
        }

        protected internal override Expression VisitDbColumnSpecExpression(DbColumnSpecExpression exp)
        {
            exp.ColumnSpec.WriteSql(_sb, _info, _builder, _mode);
            return exp;
        }

        protected internal override Expression VisitDbSelectSpecExpression(DbSelectSpecExpression exp)
        {
            exp.SelectSpec.WriteSql(_sb, _info, _builder, _mode);
            return exp;
        }

        protected internal override Expression VisitExistsExpression(ExistsExpression exp)
        {
            _sb.Append(_builder.ExistsOperator);
            _sb.Append(_builder.OpenBracket);
            Visit(exp.Expression);
            _sb.Append(_builder.CloseBracket);
            return exp;
        }

        protected override Expression VisitConstant(ConstantExpression node)
        {
            object value = node.Value;
            if (value is DynamicValue)
            {
                value = ((DynamicValue)value).Value;
            }

            if (value == null)
            {
                _sb.Append(_builder.NullLiteral);
            }
            else
            {
                _info.WriteParameter(_sb, value.GetType(), value, -1);
            }

            return node;
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            switch (node.NodeType)
            {
                case ExpressionType.Not:
                    _sb.Append(_builder.NotOperator);
                    Visit(node.Operand);
                    break;
                case ExpressionType.Convert:
                    Visit(node.Operand);
                    break;
                default:
                    throw new NotSupportedException("Unsupported Unary Operator: " + node.NodeType);
            }

            return node;
        }

        protected override Expression VisitBinary(BinaryExpression node)
        {
            _sb.Append(_builder.OpenBracket);

            string op = GetOperator(node);

            bool written = false;
            if (node.NodeType == ExpressionType.Equal)
            {
                written = TryCheckNulls(node.Left, node.Right, _builder.IsNullOperator);
            }
            else if (node.NodeType == ExpressionType.NotEqual)
            {
                written = TryCheckNulls(node.Left, node.Right, _builder.IsNotNullOperator);
            }

            if (!written)
            {
                Visit(node.Left);
                _sb.Append(op);
                Visit(node.Right);
            }

            _sb.Append(_builder.CloseBracket);
            return node;
        }

        private string GetOperator(BinaryExpression b)
        {
            switch (b.NodeType)
            {
                case ExpressionType.AndAlso:
                    return _builder.AndOperand;
                case ExpressionType.OrElse:
                    return _builder.OrOperand;
                case ExpressionType.Equal:
                    return _builder.EqualOperator;
                case ExpressionType.NotEqual:
                    return _builder.NotEqualOperator;
                case ExpressionType.LessThan:
                    return _builder.LessThanOperator;
                case ExpressionType.LessThanOrEqual:
                    return _builder.NotGreaterThanOperator;
                case ExpressionType.GreaterThan:
                    return _builder.GreaterThanOperator;
                case ExpressionType.GreaterThanOrEqual:
                    return _builder.NotLessThanOperator;
                case ExpressionType.Add:
                    return IsStringChecker.IsString(b) ? _builder.ConcatOperator : _builder.AddOperator;
                case ExpressionType.Subtract:
                    return _builder.SubtractOperator;
                case ExpressionType.Multiply:
                    return _builder.MultiplyOperator;
                case ExpressionType.Divide:
                    return _builder.DivideOperator;
                case ExpressionType.Modulo:
                    return _builder.ModuloOperator;
                default:
                    throw new NotSupportedException("Unsupported Binary Operator: " + b.NodeType);
            }
        }

        protected internal override Expression VisitInExpression(InExpression exp)
        {
            Visit(exp.Expression);

            _sb.Append(_builder.InOperator);
            _sb.Append(_builder.OpenBracket);

            bool first = true;
            foreach (Expression inExpression in exp.InExpressions)
            {
                if (first)
                {
                    first = false;
                }
                else
                {
                    _sb.Append(_builder.CommaSeparator);
                }

                Visit(inExpression);
            }

            _sb.Append(_builder.CloseBracket);

            return exp;
        }

        protected internal override Expression VisitLikeExpression(LikeExpression exp)
        {
            _sb.Append(_builder.OpenBracket);

            Visit(exp.Expression);
            _sb.Append(_builder.LikeClause);
            Visit(exp.ValueExpression);

            _sb.Append(_builder.CloseBracket);

            return exp;
        }

        private bool TryCheckNulls(Expression left, Expression right, string nullString)
        {
            bool result = false;
            if (IsNull(right))
            {
                Visit(left);
                _sb.Append(nullString);
                result = true;
            }
            else if (IsNull(left))
            {
                Visit(right);
                _sb.Append(nullString);
                result = true;
            }
            return result;
        }

        private bool IsNull(Expression exp)
        {
            if (exp is ConstantExpression constExp)
            {
                object value = constExp.Value;
                if (value is DynamicValue)
                {
                    value = ((DynamicValue)value).Value;
                }

                return value == null;
            }

            return false;
        }

        protected internal override Expression VisitMethod(MethodExpression exp)
        {
            switch (exp.MethodName)
            {
                case DateTimeFunction.FunctionNamespace + "." + DateTimeAddYears.FunctionName:
                    VisitFunction(_builder.IfsDateTimeAddYearsFunction, exp.Arguments);
                    break;
                case DateTimeFunction.FunctionNamespace + "." + DateTimeAddMonths.FunctionName:
                    VisitFunction(_builder.IfsDateTimeAddMonthsFunction, exp.Arguments);
                    break;
                case DateTimeFunction.FunctionNamespace + "." + DateTimeAddDays.FunctionName:
                    VisitFunction(_builder.IfsDateTimeAddDaysFunction, exp.Arguments);
                    break;
                case DateTimeFunction.FunctionNamespace + "." + DateTimeAddHours.FunctionName:
                    VisitFunction(_builder.IfsDateTimeAddHoursFunction, exp.Arguments);
                    break;
                case DateTimeFunction.FunctionNamespace + "." + DateTimeAddMinutes.FunctionName:
                    VisitFunction(_builder.IfsDateTimeAddMinutesFunction, exp.Arguments);
                    break;
                case DateTimeFunction.FunctionNamespace + "." + DateTimeAddSeconds.FunctionName:
                    VisitFunction(_builder.IfsDateTimeAddSecondsFunction, exp.Arguments);
                    break;
                case DateTimeFunction.FunctionNamespace + "." + DateTimeTimestamp.FunctionName:
                    _sb.Append(_builder.IfsDateTimeNow);
                    break;
                case DateTimeFunction.FunctionNamespace + "." + DateTimeTimestampUtc.FunctionName:
                    _sb.Append(_builder.IfsDateTimeUtcNow);
                    break;
                case SystemFunction.FunctionNamespace + "." + SystemGetUserName.FunctionName:
                    _sb.Append(_builder.OpenBracket);
                    _sb.Append(_builder.SelectClause);
                    _sb.Append(StringExtensions.ToLowerCaseUnderscore(nameof(DatabaseInfo.ActivatedUser)));
                    _sb.Append(_builder.FromClause);
                    _sb.Append(DatabaseInfo.TableName);
                    _sb.Append(_builder.CloseBracket);
                    break;
                case SystemFunction.FunctionNamespace + "." + SystemDateTime.FunctionName:
                    _sb.Append(_builder.IfsDateTimeNow);
                    break;
                case DateTimeFunction.FunctionNamespace + "." + DateTimeDate.FunctionName:
                    {
                        if (exp.Arguments.Count == 0)
                        {
                            _sb.Append(_builder.IfsDateTimeNowDate);
                        }
                        else
                        {
                            VisitFunction(_builder.IfsDateTimeDateFunction, exp.Arguments);
                        }
                        break;
                    }
                case DateTimeFunction.FunctionNamespace + "." + DateTimeTime1.FunctionName:
                    VisitFunction(_builder.IfsDateTimeTimeFunction, exp.Arguments);
                    break;
                case StringFunction.FunctionNamespace + "." + StringLike.FunctionName:

                    if (exp.Arguments.Count != 2)
                    {
                        throw new NotSupportedException("Invalid query args: " + exp.MethodName);
                    }
                    
                    VisitLikeExpression(IfsExpression.Like(exp.Arguments[0], exp.Arguments[1]));
                    break;
                case InternalFunctions.DbFormatDateTimeFunctionName:
                    VisitFunction(_builder.IfsDateTimeFormatFunction, exp.Arguments);
                    break;
                default:
                    throw new NotSupportedException("Unsupported query method: " + exp.MethodName);
            }

            return exp;
        }

        private void VisitFunction(string functionName, ReadOnlyCollection<Expression> args)
        {
            _sb.Append(functionName);
            _sb.Append(_builder.FunctionProlog);

            bool first = true;
            foreach (Expression inExpression in args)
            {
                if (first)
                {
                    first = false;
                }
                else
                {
                    _sb.Append(_builder.CommaSeparator);
                }

                Visit(inExpression);
            }

            _sb.Append(_builder.FunctionEpilog);
        }

        private sealed class IsStringChecker : IfsExpressionVisitor
        {
            private bool _foundString;

            public static bool IsString(Expression exp)
            {
                IsStringChecker checker = new IsStringChecker();
                checker.Visit(exp);
                return checker._foundString;
            }

            protected override Expression VisitConstant(ConstantExpression node)
            {
                if (node.Value is string)
                {
                    _foundString = true;
                }
                else if (node.Value is DynamicValue && ((DynamicValue)node.Value).Value is string)
                {
                    _foundString = true;
                }

                return base.VisitConstant(node);
            }
        }
    }
}
