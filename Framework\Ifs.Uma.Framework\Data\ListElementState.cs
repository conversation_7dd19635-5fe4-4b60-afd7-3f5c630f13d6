﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Text;

namespace Ifs.Uma.Framework.Data
{
    public sealed class ListElementState
    {
        private readonly ViewState _viewState;
        private readonly string _listElementName;

        public ListElementState(ViewState viewState, string listElementName)
        {
            _viewState = viewState ?? new ViewState();
            _listElementName = listElementName;
        }

        public string Selection
        {
            get => _viewState.TryGetValue(StateName("selection"), out object value) ? value as string : null;
            set => _viewState.Assign(StateName("selection"), value);
        }

        public string StateName([CallerMemberName] string propertyName = null)
        {
            return ViewState.ViewStatePrefix + _listElementName + "." + propertyName;
        }
    }
}
