﻿using System;
using System.Globalization;
using Ifs.Uma.Database;
using Ifs.Uma.Utility.Formatters;

namespace Ifs.Uma.AppData.Formatters
{
    public sealed class DateFormatter : IValueFormatter
    {
        private readonly DateFormats _format;

        public DateFormatter(DateFormats format)
        {
            _format = format;
        }

        public string Format(object value)
        {
            if (value is DateTime dValue)
            {
                if (_format == DateFormats.TimestampUtc)
                {
                    return FormatUtcToLocal(dValue);
                }
                else
                {
                    return Format(dValue, _format);
                }
            }

            return OrdinalValueFormatter.Instance.Format(value);
        }

        public static string Format(DateTime value)
        {
            if (value.Year == 1 && value.Month == 1 && value.Day == 1)
            {
                return Format(value, DateFormats.Time);
            }
            else if (value.Hour == 0 && value.Minute == 0 && value.Second == 0)
            {
                return Format(value, DateFormats.Date);
            }
            else
            {
                return Format(value, DateFormats.Timestamp);
            }
        }

        public static string Format(DateTime value, DateFormats dateFormat)
        {
            string formatString = DateFormatToFormatString(dateFormat);
            string formatted = value.ToString(formatString, CultureInfo.CurrentCulture);

            if (dateFormat == DateFormats.Timestamp)
            {
                // Remove left to right unicode marks so that the date will wrap 
                // wherever there is a space rather than between times / dates
                string spacePlusleftToRightMark = new string(new[] { ' ', (char)8206 });
                formatted = formatted.Replace(spacePlusleftToRightMark, " ");
            }

            return formatted;
        }
   
        private string FormatUtcToLocal(DateTime value)
        {
            //Converting the value into device time zone value.
            DateTime clientDateTime = value.ToClientLocalTime();

            string formattedValue = clientDateTime.ToString("g", CultureInfo.CurrentCulture);

            // Remove left to right unicode marks so that the date will wrap 
            // wherever there is a space rather than between times / dates
            string spacePlusleftToRightMark = new string(new[] { ' ', (char)8206 });
            formattedValue = formattedValue.Replace(spacePlusleftToRightMark, " ");

            return formattedValue;
        }

        public static string DateFormatToFormatString(DateFormats dateFormat)
        {
            switch (dateFormat)
            {
                case DateFormats.Date:
                    return "d";
                case DateFormats.Time:
                    return "t";
                default:
                    return "g";
            }
        }
    }
}
