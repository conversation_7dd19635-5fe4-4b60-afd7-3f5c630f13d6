﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.App;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Transactions;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Services
{
    internal sealed class SynchronizationEndedEvent : TransactionSyncEventWatcher
    {
        private const string EventName = "SynchronizationEnded";
        private const string ChangedEntitiesParamName = "ChangedEntities";
        private const string RowIdsParamName = "RowIds";

        private readonly IProcedureExecutor _procedureExecutor;
        private readonly IMetadata _metadata;
        private readonly ITouchApp _touchApp;
        private readonly IToastService _toastService;
        private SemaphoreSlim _eventLock = new SemaphoreSlim(1);

        private CancellationTokenSource _stopToken;
        private int _longProcessingTimeCount;

        public SynchronizationEndedEvent(IProcedureExecutor proceureExecutor, IMetadata metadata, ILogger logger, ITransactionSyncService service,
            ITouchApp touchApp, IToastService toastService)
            : base(logger, service, ThreadOption.BackgroundThread)
        {
            _procedureExecutor = proceureExecutor;
            _metadata = metadata;
            _touchApp = touchApp;
            _toastService = toastService;
        }
        
        protected override void OnStart()
        {
            _stopToken = new CancellationTokenSource();

            base.OnStart();
        }

        protected override void OnStop()
        {
            CancellationTokenSource stopToken = Interlocked.Exchange(ref _stopToken, null);
            stopToken?.Cancel();

            base.OnStop();
        }

        protected override bool OnMatchSyncEventFilter(TransactionSyncEventArgs e)
        {
            return e.EventType == SyncEventType.SyncEnded && !e.SyncChangeSet.IsEmpty;
        }

        internal void NotifyInitializationCompleted()
        {
            Task.Run(async () =>
            {
                try
                {
                    foreach (CpiProjection projection in _metadata.CpiMetaData.GetProjections())
                    {
                        List<string> changedEntities = _metadata.MetaModel.GetTableNames()
                            .Select(RemoteNaming.ToEntityName)
                            .Where(x => _metadata.FindStructure(projection.Name, x) == null)
                            .ToList();

                        await FireEvent(projection.Name, changedEntities, null); // Don't send the entire list of rowids after init
                    }
                }
                catch (Exception ex)
                {
                    Logger.HandleException(ExceptionType.Unexpected, ex);
                }
            });
        }

        protected override void OnSyncEvent(TransactionSyncEventArgs e)
        {
            List<string> changedEntities = new List<string>();
            foreach (IMetaTable table in e.SyncChangeSet.EffectedTables)
            {
                string entityName = RemoteNaming.ToEntityName(table.TableName);
                changedEntities.Add(entityName);
            }

            List<FndRecordRowId> rowIds = new List<FndRecordRowId>();
            foreach (Tuple<string, long> item in e.SyncChangeSet.GetChangedRowIds())
            {
                FndRecordRowId rowId = new FndRecordRowId();
                rowId.RecordTableName = item.Item1;
                rowId.RecordRowId = item.Item2;
                rowIds.Add(rowId);
            }

            foreach (CpiProjection projection in _metadata.CpiMetaData.GetProjections())
            {
                _ = FireEvent(projection.Name, changedEntities, rowIds);
            }
        }

        private async Task FireEvent(string projectionName, List<string> changedEntities, List<FndRecordRowId> rowIds)
        {
            if (_eventLock == null)
            {
                return;
            }

            await _eventLock.WaitAsync();
            try
            {
                Dictionary<string, object> parameters = new Dictionary<string, object>();
                parameters[ChangedEntitiesParamName] = changedEntities;

                Stopwatch sw = Stopwatch.StartNew();

                if (_procedureExecutor != null && _stopToken?.Token != null)
                {
                    if (_stopToken?.Token != null)
                    {
                        string procName = ProcedureType.Event.GetFullProcedureName(EventName);
                        CpiProc proc = _metadata.FindProcedure(projectionName, procName);

                        if (proc.Parameters.Any(x => x.Name == RowIdsParamName))
                        {
                            parameters[RowIdsParamName] = rowIds;
                        }

                        ExecuteResult result = await _procedureExecutor.CallEventAsync(projectionName, EventName, parameters, _stopToken.Token);

                        if (result.Exception != null)
                        {
                            LogException(result.Exception);
                        }
                    }
                }

                sw.Stop();

                CheckElapsedTime(sw.ElapsedMilliseconds);
            }
            catch (OperationCanceledException)
            { }
            finally
            {
                _eventLock?.Release();
            }
        }

        private void CheckElapsedTime(long ms)
        {
            if (ms < 100)
            {
                _longProcessingTimeCount = 0;
                return;
            }

            string message = $"Event<SynchronizationEnded> took a long time ({ms}ms) and is slowing down the application. This event should take less than 100ms.";
            Logger.Warning(message);

            if (_touchApp.DeveloperMode)
            {
                if (ms > 500)
                {
                    _longProcessingTimeCount++;
                    if (_longProcessingTimeCount > 3)
                    {
                        _longProcessingTimeCount = 0;

                        UiContext.Post(_ => _toastService.Show(ToastType.Warning, message), null);
                    }
                }
                else
                {
                    _longProcessingTimeCount = 0;
                }
            }
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            if (disposing)
            {
                if (_eventLock != null)
                {
                    _eventLock.Dispose();
                    _eventLock = null;
                }
            }
        }
    }
}
