﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.StringExpressions
{
    public sealed class InterpolatedString
    {
        private const string AttributeStart = "${";
        private const string AttributeEnd = "}";
        private const string ExtressionStart = "#{";
        private const string ExtressionEnd = "}";
        private const string URLFilterAttribute = "'";

        private readonly ILogger _logger;
        private readonly List<InterpolatedStringPart> _parts = new List<InterpolatedStringPart>();

        public bool IsBasicString => _parts.Count == 0 || (_parts.Count == 1 && _parts[0].String != null);

        public InterpolatedString(ILogger logger, string str)
            : this(logger, str, null)
        {
        }

        public InterpolatedString(ILogger logger, string str, Func<Expression, Expression> mutator)
        {
            if (!string.IsNullOrEmpty(str))
            {
                AddParts(str, 0, mutator);
            }

            _logger = logger;
        }

        private void AddParts(string str, int start, Func<Expression, Expression> mutator)
        {
            int attrStart = str.IndexOf(AttributeStart, start);
            if (attrStart == start)
            {
                AddAttributePart(str, attrStart + 2, mutator);
                return;
            }

            int expStart = str.IndexOf(ExtressionStart, start);
            if (expStart == start)
            {
                AddExpressionPart(str, expStart + 2, mutator);
                return;
            }

            int readTo = -1;
            if (attrStart >= 0)
            {
                readTo = attrStart;
            }

            if (expStart >= 0 && (expStart < readTo || readTo == -1))
            {
                readTo = expStart;
            }

            if (readTo >= 0)
            {
                InterpolatedStringPart part = InterpolatedStringPart.FromString(str.Substring(start, readTo - start));
                _parts.Add(part);
                AddParts(str, readTo, mutator);
            }
            else
            {
                string partStr = str.Substring(start);
                if (partStr.Length > 0)
                {
                    InterpolatedStringPart part = InterpolatedStringPart.FromString(partStr);
                    _parts.Add(part);
                }
            }
        }

        private void AddAttributePart(string str, int start, Func<Expression, Expression> mutator)
        {
            int attrEnd = str.IndexOf(AttributeEnd, start);
            if (attrEnd > 0)
            {
                InterpolatedStringPart part = InterpolatedStringPart.FromAttribute(str.Substring(start, attrEnd - start));
                _parts.Add(part);
                AddParts(str, attrEnd + 1, mutator);
            }
            else
            {
                InterpolatedStringPart part = InterpolatedStringPart.FromString(AttributeStart + str.Substring(start));
                _parts.Add(part);
            }
        }

        private void AddExpressionPart(string str, int start, Func<Expression, Expression> mutator)
        {
            int expEnd = str.IndexOf(ExtressionEnd, start);
            if (expEnd > 0)
            {
                InterpolatedStringPart part = InterpolatedStringPart.FromExpression(_logger, str.Substring(start, expEnd - start), mutator);
                _parts.Add(part);
                AddParts(str, expEnd + 1, mutator);
            }
            else
            {
                InterpolatedStringPart part = InterpolatedStringPart.FromString(ExtressionStart + str.Substring(start));
                _parts.Add(part);
            }
        }

        public string GetString(IStringExpressionValueProvider valueProvider, bool localize, bool isUrl = false, IMetaTable metaTable = null)
        {
            if (_parts.Count == 1 && _parts[0].String != null)
            {
                return _parts[0].String;
            }

            valueProvider = valueProvider ?? EmptyStringExpressionValueProvider.Instance;

            StringBuilder sb = new StringBuilder();
            foreach (InterpolatedStringPart part in _parts)
            {
                if (metaTable != null && !string.IsNullOrEmpty(part.Attribute))
                {
                    IMetaDataMember column = metaTable.FindMemberByPropertyName(part.Attribute);

                    if (column != null && (column.DateFormat == DateFormats.Timestamp || column.DateFormat == DateFormats.TimestampUtc || column.DateFormat == DateFormats.Time) && column.ColumnType == typeof(DateTime?))
                    {
                        DateTime dateTime;

                        if (DateTime.TryParse(part.GetString(_logger, valueProvider, localize, isUrl), out dateTime))
                        {
                            string clientDateTime = column.DateFormat == DateFormats.Time ? dateTime.ToClientLocalTime().ToShortTimeString() : dateTime.ToClientLocalTime().ToString();                            
                            sb.Append(clientDateTime);
                        }
                    }
                    else
                    {
                        sb.Append(part.GetString(_logger, valueProvider, localize, isUrl));
                    }
                }
                else
                {
                    sb.Append(part.GetString(_logger, valueProvider, localize, isUrl));
                }
            }

            return sb.ToString();
        }

        private sealed class EmptyStringExpressionValueProvider : IStringExpressionValueProvider
        {
            public static IStringExpressionValueProvider Instance { get; } = new EmptyStringExpressionValueProvider();

            public bool TryGetFormattedValue(string propertyName, out string value)
            {
                value = null;
                return false;
            }

            public bool TryGetValue(string propertyName, out object value)
            {
                value = null;
                return false;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            foreach (InterpolatedStringPart part in _parts)
            {
                sb.Append(part.ToString());
            }

            return sb.ToString();
        }

        private class InterpolatedStringPart
        {
            public string String { get; private set; }
            public string Attribute { get; private set; }
            public Expression Expression { get; private set; }
            public Func<IStringExpressionValueProvider, object> ExpressionMethod { get; private set; }

            public static InterpolatedStringPart FromString(string str)
            {
                InterpolatedStringPart part = new InterpolatedStringPart();
                part.String = str;
                return part;
            }

            public static InterpolatedStringPart FromAttribute(string str)
            {
                InterpolatedStringPart part = new InterpolatedStringPart();
                part.Attribute = str;
                return part;
            }

            public static InterpolatedStringPart FromExpression(ILogger logger, string str, Func<Expression, Expression> mutator)
            {
                try
                {
                    Expression exp = IfsExpression.FromString(str);

                    InterpolatedStringPart part = new InterpolatedStringPart();
                    exp = StringMethodReplacer.Rewrite(exp);

                    if (mutator != null)
                    {
                        exp = mutator(exp);
                    }

                    if (exp is VarAccessExpression varAccess)
                    {
                        part.Attribute = varAccess.PropertyPath;
                    }
                    else
                    {
                        part.Expression = exp;

                        ParameterExpression vpParam = Expression.Parameter(typeof(IStringExpressionValueProvider), "ValueProvider");
                        exp = StringValueProviderReplacer.Rewrite(exp, vpParam);
                        exp = Expression.Convert(exp, typeof(object));

                        DisallowVarChecker.Check(exp);
                        DisallowMethodChecker.Check(exp);

                        part.ExpressionMethod = Expression.Lambda<Func<IStringExpressionValueProvider, object>>(exp, vpParam).Compile();
                    }

                    return part;
                }
                catch (Exception ex)
                {
                    logger?.HandleException(ExceptionType.Recoverable, ex);
                    InterpolatedStringPart part = new InterpolatedStringPart();
                    part.String = "##INVALID##";
                    return part;
                }
            }

            public string GetString(ILogger logger, IStringExpressionValueProvider valueProvider, bool localize, bool isUrl)
            {
                if (String != null)
                {
                    return String;
                }

                if (Attribute != null)
                {
                    double result = 0;

                    if (localize)
                    {
                        if (valueProvider.TryGetFormattedValue(Attribute, out string value))
                        {
                            if (isUrl)
                            {
                                try
                                {
                                    result = Convert.ToDouble(value);
                                }
                                catch
                                {
                                }
                                
                                if (result > 0)
                                {
                                    return value;
                                }
                                else
                                {
                                    return string.Format("{0}{1}{2}", URLFilterAttribute, value, URLFilterAttribute); //If the value in a url is alpha numeric put it in single quotes
                                }
                            }
                            else
                            {
                                return value;
                            }
                        }
                    }
                    else if (valueProvider.TryGetValue(Attribute, out object value))
                    {
                        if (isUrl)
                        {
                            try
                            {
                                result = ObjectConverter.ToDouble(value);
                            }
                            catch
                            {
                            }

                            if (result > 0)
                            {
                                return ObjectConverter.ToString(value);
                            }
                            else
                            {
                                return string.Format("{0}{1}{2}", URLFilterAttribute, ObjectConverter.ToString(value), URLFilterAttribute); //If the value in a url is alpha numeric put it in single quotes
                            }
                        }
                        else
                        {
                            return ObjectConverter.ToString(value);
                        }                        
                    }

                    return string.Empty;
                }

                if (ExpressionMethod != null)
                {
                    try
                    {
                        object value = ExpressionMethod(valueProvider);
                        value = value is DynamicValue dv ? dv.Value : value;

                        return localize
                            ? AttributeFormatter.FormatValue(value)
                            : ObjectConverter.ToString(value);
                    }
                    catch (Exception ex)
                    {
                        logger?.HandleException(ExceptionType.Recoverable, ex);
                        return "##INVALID##";
                    }
                }

                return string.Empty;
            }

            public override string ToString()
            {
                if (String != null)
                {
                    return String;
                }
                else if (Attribute != null)
                {
                    return AttributeStart + Attribute + AttributeEnd;
                }
                else if (Expression != null)
                {
                    return ExtressionStart + Expression + ExtressionEnd;
                }

                return string.Empty;
            }
        }
    }
}
