// Shared
Platforms=iOS
Name=IFS Maintenance for Aviation
AppName=FLMaintApp
RedirectUri=ifsmobileflm
RemoteAssistance=true
SignatureService=true
LocationEnabled=false
LidarService=true
PushNotification=true

// iOS
iOSDisplayName=IFS Maintenance for Aviation
// Below must be the one having the InHouse suffix, the build script will remove it when building the store app
BundleIdentifier=com.ifs.cloud.FLMaint.InHouse
BundleName=IFS Maintenance for Aviation

// NOTE: iOS usage descriptions are always needed since we don't remove code for things like media attachments and location APIs
// So it's okay to add some dummy text for apps that don't really use those features
NSLocationWhenInUseUsageDescription=This application requires location services to work
NSLocationAlwaysAndWhenInUseUsageDescription=This application requires location services to work
NSCameraUsageDescription=This application requires access to the camera to scan barcodes
NSPhotoLibraryUsageDescription=This app needs access to photos
NSPhotoLibraryAddUsageDescription=This app needs access to save media and documents
NSMicrophoneUsageDescription=This is used for remote assistance calls

// Android
AndroidDisplayName=IFS Maintenance for Aviation
AndroidPackageName=com.ifs.cloud.FLMaint

// Windows
WindowsDisplayName=IFS Maintenance for Aviation
WindowsDescription=IFS Mobile Maintenance application provides maintenance teams with the capability to connect to the M&amp;E system using their mobile devices and perform maintenance on an aircraft, recording work as it is performed, even if they temporarily lose connectivity.
WindowsShortName=MMaint
IdentityName=IFS.IFSForwardLineMaintenance
PhoneProductId=a09c007e-8848-49e1-9ff9-4fa933bc7130
// Below are in Package.StoreAssociation and Package.xml
ReservedName=IFS Forward/Line Maintenance
LandingUrl=https://developer.microsoft.com/dashboard/Application?appId=9N3HLKFXQLN7