﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Runtime.CompilerServices;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Data
{
    #region INotifyPropertyChanging 

    // For some reason, the portable subset library does not include INotifyPropertyChanging
    // Just replicate it here for now...
    // See whether we need it...

    public class PropertyChangingEventArgs : EventArgs
    {
        public PropertyChangingEventArgs(string propertyName)
        {
            PropertyName = propertyName;
        }

        public string PropertyName { get; private set; }
    }

    public interface INotifyPropertyChanging
    {
        event EventHandler<PropertyChangingEventArgs> PropertyChanging;
    }

    #endregion

    /// <summary>
    /// All IFS rows have the RowId column.
    /// I've also added INotifyPropertyChanged together with helper methods for the inheriting classes
    /// INotifyPropertyChanging is a bit nastier.  Maybe easier as INotifyRowChanging
    /// This would allow a "before" image to be captured (by a DataContext) just before we changed values.
    /// </summary>
    public class RowBase : INotifyPropertyChanged, INotifyPropertyChanging, IFastMemberAccess
    {
        #region Standard IFS columns

        private long _rowId;
        [Column(Storage=nameof(_rowId), PrimaryKey=true, AutoIncrement=true)]
        public long RowId
        {
            get { return _rowId; }
            // RowId can only be set by adding a row
            protected set { SetProperty(ref _rowId, value); } 
        }

        #endregion

        #region Values

        public object this[string name]
        {
            get { return GetProperty(name); }
            set { SetProperty(name, value); }
        }

        #endregion

        #region INotifyPropertyChanged Members

        public event PropertyChangedEventHandler PropertyChanged;

        #endregion

        #region INotifyPropertyChanging Members

        public event EventHandler<PropertyChangingEventArgs> PropertyChanging;

        #endregion

        #region Implementation

        private readonly IDictionary<string, object> _values;

        protected RowBase()
        {
            _values = new Dictionary<string, object>();
        }

        [SuppressMessage("Microsoft.Design", "CA1026:DefaultParametersShouldNotBeUsed",
            Justification="default parameter is required for a CallerMemberName attribute to compile")]
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName]string name = null)
        {
            if (!EqualityComparer<T>.Default.Equals(field, value))
            {
                SendPropertyChanging(name);
                field = value;
                SendPropertyChanged(name);
                return true;
            }

            return false;
        }

        protected object GetProperty(string name)
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));

            object result;
            Type type = GetType();
            PropertyInfo prop = TypeHelper.GetPropertyInfoForGet(type, name);
            if (prop != null)
            {
                result = prop.GetValue(this);
            }
            else
            {
                result = GetCustomProperty(name);
            }
            return result;
        }
        
        protected void SetProperty(string name, object value)
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
            Type type = GetType();
            PropertyInfo prop = TypeHelper.GetPropertyInfoForSet(type, name);
            if (prop != null)
            {
                prop.SetValue(this, value);
            }
            else
            {
                SetCustomProperty(name, value);
            }
        }

        public object GetMemberValue(IMetaDataMember member)
        {
            if (member.Storage != null)
            {
                return member.Storage.GetValue(this);
            }

            if (member.Property != null)
            {
                return member.Property.GetValue(this, null);
            }

            return GetCustomProperty(member.PropertyName);
        }

        protected object GetCustomProperty(string name)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));

            object result;
            Type type = GetType();
            ISystemData systemData = SystemData.Instance;
            string propertyName = systemData == null ? name : systemData.NormalisePropertyName(type, name);
            if (!_values.TryGetValue(propertyName, out result))
            {
                // find a default value for the custom field.
                result = systemData?.GetDefaultValue(type, propertyName);
            }

            return result;
        }

        public void SetMemberValue(IMetaDataMember member, object value)
        {
            if (member.Property != null)
            {
                member.Property.SetValue(this, value, null);
            }
            else if (member.Storage != null)
            {
                member.Storage.SetValue(this, value);
            }
            else
            {
                SetCustomProperty(member.PropertyName, value);
            }
        }

        protected void SetCustomProperty(string name, object value)
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));

            // validate the name and the value for a custom field
            ISystemData systemData = SystemData.Instance;
            Type type = GetType();
            string propertyName = systemData == null ? name : systemData.NormalisePropertyName(type, name);
            object newValue = systemData == null ? value : systemData.ValidateCustomValue(type, propertyName, value);
            object oldValue;
            if (!_values.TryGetValue(propertyName, out oldValue) ||
                !Equals(oldValue, newValue))
            {
                SendPropertyChanging(propertyName);
                _values[propertyName] = newValue;
                SendPropertyChanged(propertyName);
            }
        }

        protected void SendPropertyChanging(string propertyName)
        {
            PropertyChanging?.Invoke(this, new PropertyChangingEventArgs(propertyName));
        }

        protected void SendPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
