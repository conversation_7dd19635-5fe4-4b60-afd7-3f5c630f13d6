﻿using Ifs.Cloud.Client.Comm;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Comm.TouchApps
{
    public class TouchAppsAuthenticator : BaseCloudAuthenticator
    {
        public static TouchAppsAuthenticator Instance { get; } = new TouchAppsAuthenticator();

        private TouchAppsAuthenticator()
        {
        }

        protected override string GetAuthenticationTokenImpl(string systemId, string userName, string password, string publicKey, string certString)
        {
            return PlatformServices.GetAuthenticationToken(systemId, userName, password, publicKey, certString);
        }

        protected override string GetAuthenticationAccessTokenAndKeyImpl(string accessToken, string publicKey, string certificationString, out string key)
        {
            return PlatformServices.GetAuthenticationAccessTokenAndKey(accessToken, publicKey, certificationString, out key);
        }
    }
}
