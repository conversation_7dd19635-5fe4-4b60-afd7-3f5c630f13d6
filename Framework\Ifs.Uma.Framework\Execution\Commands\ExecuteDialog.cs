﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Dialogs;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.Framework.Execution.Commands
{
    internal class ExecuteDialog
    {
        private readonly IResolver _resolver;
        private readonly ILogger _logger;
        private readonly IEventAggregator _eventAggregator;
        private readonly IDataHandler _data;

        public ExecuteDialog(IResolver resolver, ILogger logger, IEventAggregator eventAggregator,  IDataHandler data)
        {
            _resolver = resolver;
            _logger = logger;
            _eventAggregator = eventAggregator;
            _data = data;
        }

        public async Task<ExecuteResult> ExecuteAsync(CommandContext context, CpiDialogCallArgs args)
        {
            CpiDialog dialog = context.Metadata.FindDialog(args.Projection, args.Name);

            if (dialog == null)
            {
                throw new InvalidOperationException($"Could not open dialog {args.Name}");
            }

            RecordData dialogData = new RecordData(_logger, context.Metadata, _data);

            await dialogData.LoadNewRecordAsync(args.Projection, dialog.Entity);

            if (args.Input != null)
            {
                context.ReadParamsInto(args.Input, dialogData);
            }

            ExecuteResult ret = await ShowDialog(dialog, dialogData);

            if (!ret.Is(ExecuteResult.Cancel) && args.Output != null)
            {
                context.Assign(args.Output, dialogData);
            }

            return ret;
        }

        private Task<ExecuteResult> ShowDialog(CpiDialog dialog, RecordData record)
        {
            ElementDialog dialogData = _resolver.Resolve<ElementDialog>();
            return dialogData.ShowAsync(dialog, record);
        }
    }
}
