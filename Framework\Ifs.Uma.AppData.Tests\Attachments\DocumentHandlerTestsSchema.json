{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"name": "FndTstOffline", "service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {"TstCustomer": {"name": "TstCustomer", "hasETag": true, "CRUD": "Create,Read,Update,Delete", "luname": "TstCustomer", "ludependencies": ["TstCustomer"], "keys": ["CustomerNo"], "transactionGroup": "C: ${CustomerNo}", "attributes": {"CustomerNo": {"datatype": "Text", "keygeneration": "User"}}}}, "procedures": {}}}