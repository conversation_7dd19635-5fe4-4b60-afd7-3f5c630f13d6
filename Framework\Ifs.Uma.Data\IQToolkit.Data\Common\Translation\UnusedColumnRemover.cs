﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Removes column declarations in SelectExpression's that are not referenced
    /// </summary>
    internal class UnusedColumnRemover : DbExpressionVisitor
    {
        Dictionary<TableAlias, HashSet<string>> allColumnsUsed;
        bool retainAllColumns;

        private UnusedColumnRemover()
        {
            this.allColumnsUsed = new Dictionary<TableAlias, HashSet<string>>();
        }

        public static Expression Remove(Expression expression) 
        {
            return new UnusedColumnRemover().Visit(expression);
        }

        private void MarkColumnAsUsed(TableAlias alias, string name)
        {
            HashSet<string> columns;
            if (!this.allColumnsUsed.TryGetValue(alias, out columns))
            {
                columns = new HashSet<string>();
                this.allColumnsUsed.Add(alias, columns);
            }
            columns.Add(name);
        }

        private bool IsColumnUsed(TableAlias alias, string name)
        {
            HashSet<string> columnsUsed;
            if (this.allColumnsUsed.TryGetValue(alias, out columnsUsed))
            {
                if (columnsUsed != null)
                {
                    return columnsUsed.Contains(name);
                }
            }
            return false;
        }

        private void ClearColumnsUsed(TableAlias alias)
        {
            this.allColumnsUsed[alias] = new HashSet<string>();
        }

        protected override Expression VisitColumn(ColumnExpression node)
        {
            if (node == null) return null;
            MarkColumnAsUsed(node.Alias, node.Name);
            return node;
        }

        protected override Expression VisitSubquery(SubqueryExpression node) 
        {
            if (node == null) return null;
            DbExpressionType subqueryDbNodeType = node.GetDbNodeType();
            if ((subqueryDbNodeType == DbExpressionType.Scalar ||
                subqueryDbNodeType == DbExpressionType.In) &&
                node.Select != null) 
            {
                System.Diagnostics.Debug.Assert(node.Select.Columns.Count == 1);
                MarkColumnAsUsed(node.Select.Alias, node.Select.Columns[0].Name);
            }
            return base.VisitSubquery(node);
        }

        protected override Expression VisitSelect(SelectExpression node)
        {
            if (node == null) return null;
            // visit column projection first
            ReadOnlyCollection<ColumnDeclaration> columns = node.Columns;
            var wasRetained = this.retainAllColumns;
            this.retainAllColumns = false;
            List<ColumnDeclaration> alternate = null;
            for (int i = 0, n = columns.Count; i < n; i++)
            {
                ColumnDeclaration decl = columns[i];
                if (wasRetained || node.IsDistinct || IsColumnUsed(node.Alias, decl.Name))
                {
                    Expression expr = this.Visit(decl.Expression);
                    decl = decl.Update(expr);
                }
                else
                {
                    decl = null;  // null means it gets omitted
                }
                if (decl != columns[i] && alternate == null)
                {
                    alternate = new List<ColumnDeclaration>();
                    for (int j = 0; j < i; j++)
                    {
                        alternate.Add(columns[j]);
                    }
                }
                if (decl != null && alternate != null)
                {
                    alternate.Add(decl);
                }
            }
            if (alternate != null)
            {
                columns = alternate.AsReadOnly();
            }
            Expression take = this.Visit(node.Take);
            Expression skip = this.Visit(node.Skip);
            IEnumerable<Expression> groupBy = this.VisitExpressionList(node.GroupBy);
            IEnumerable<OrderExpression> orderBy = this.VisitOrderBy(node.OrderBy);
            Expression where = this.Visit(node.Where);
            Expression from = this.Visit(node.From);
            ClearColumnsUsed(node.Alias);
            node = node.Update(columns, from, where, orderBy, groupBy, skip, take);
            this.retainAllColumns = wasRetained;
            return node;
        }

        protected override Expression VisitAggregate(AggregateExpression node)
        {
            if (node == null) return null;
            // COUNT(*) forces all columns to be retained in subquery
            if (node.AggregateName == "Count" && node.Argument == null)
            {
                this.retainAllColumns = true;
            }
            return base.VisitAggregate(node);
        }

        protected override Expression VisitProjection(ProjectionExpression node)
        {
            if (node == null) return null;
            // visit mapping in reverse order
            Expression projector = this.Visit(node.Projector);
            SelectExpression select = VisitAndConvert(node.Select, "UnusedColumnRemover.VisitProjection");
            return node.Update(select, projector, node.Aggregator);
        }

        protected override Expression VisitClientJoin(ClientJoinExpression node)
        {
            if (node == null) return null;
            var innerKey = this.VisitExpressionList(node.InnerKey);
            var outerKey = this.VisitExpressionList(node.OuterKey);
            ProjectionExpression projection = VisitAndConvert(node.Projection, "UnusedColumnRemover.VisitClientJoin");
            if (projection != node.Projection || innerKey != node.InnerKey || outerKey != node.OuterKey)
            {
                return new ClientJoinExpression(projection, outerKey, innerKey);
            }
            return node;
        }

        protected override Expression VisitJoin(JoinExpression node)
        {
            if (node == null) return null;
            if (node.Join == JoinType.SingletonLeftOuter)
            {
                // first visit right side w/o looking at condition
                Expression right = Visit(node.Right);
                AliasedExpression ax = right as AliasedExpression;
                if (ax != null && !allColumnsUsed.ContainsKey(ax.Alias))
                {
                    // if nothing references the alias on the right, then the join is redundant
                    return Visit(node.Left);
                }
                // otherwise do it the right way
                Expression cond = Visit(node.On);
                Expression left = Visit(node.Left);
                right = this.Visit(node.Right);
                return node.Update(node.Join, left, right, cond);
            }
            else
            {
                // visit join in reverse order
                Expression condition = Visit(node.On);
                Expression right = VisitSource(node.Right);
                Expression left = VisitSource(node.Left);
                return node.Update(node.Join, left, right, condition);
            }
        }
    }
}