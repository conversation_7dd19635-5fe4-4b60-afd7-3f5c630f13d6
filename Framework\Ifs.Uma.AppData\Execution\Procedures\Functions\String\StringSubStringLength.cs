﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.String
{
    internal sealed class StringSubStringLength : StringFunction
    {
        public const string FunctionName = "SubStringLength";

        public StringSubStringLength()
            : base(FunctionName, 3, true)
        {
        }

        protected override object OnExecuteStringFunction(ProcedureContext context, FuncParam[] parameters, string stringToModify)
        {
            int? startPos = (int?)parameters[1].GetInteger();
            int? endPos = (int?)parameters[2].GetInteger();

            if (!startPos.HasValue || startPos.Value < 1 || !endPos.HasValue || endPos < startPos)
            {
                return stringToModify;
            }

            if (endPos.Value > stringToModify.Length)
            {
                endPos = stringToModify.Length;
            }

            return !string.IsNullOrEmpty(stringToModify) ? stringToModify.Substring(startPos.Value - 1, endPos.Value - startPos.Value + 1) : null;
        }
    }
}
