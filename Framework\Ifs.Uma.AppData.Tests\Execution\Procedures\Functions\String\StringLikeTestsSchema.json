{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<String_Like>": {"name": "String_Like", "type": "Function", "params": [{"name": "TextInput", "dataType": "Text"}, {"name": "LikePattern", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "String", "name": "Like", "paramsArray": ["${TextInput}", "${LikePattern}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}