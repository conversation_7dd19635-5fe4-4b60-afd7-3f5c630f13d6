﻿namespace Ifs.Uma.AppData.Execution.Procedures.Functions.Convert
{
    internal sealed class ConvertToTimestamp : FwFunction
    {
        public const string FunctionNamespace = "Convert";
        public const string FunctionName = "ToTimestamp";

        public ConvertToTimestamp()
            : base(FunctionNamespace, FunctionName, 1)
        {
        }

        protected override object OnExecute(ProcedureContext context, FuncParam[] parameters)
        {
            return parameters[0].GetTimestamp();
        }
    }
}
