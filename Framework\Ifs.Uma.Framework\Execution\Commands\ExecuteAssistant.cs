﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.UI.Assistants;
using Ifs.Uma.Framework.UI.DynamicAssistants;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Framework.Execution.Commands
{
    internal class ExecuteAssistant
    {
        private readonly IResolver _resolver;
        private readonly INavigator _navigator;
        private readonly IAssistantDialogService _assistantDialogService;

        public ExecuteAssistant(IResolver resolver, INavigator navigator, IAssistantDialogService assistantDialogService)
        {
            _resolver = resolver;
            _navigator = navigator;
            _assistantDialogService = assistantDialogService;
        }

        public async Task<ExecuteResult> ExecuteAsync(CommandContext context, CpiAssistantCallArgs args)
        {
            // In dialog assistants and in navigate, the client name is present in metadata, but for pages, it's projection
            string clientOrProjection = args.Client ?? args.Projection ?? context.ProjectionName; // Assuming 'client' and 'projection' would be the same in the metadata

            CpiAssistant assistant = context.Metadata.FindAssistant(clientOrProjection, args.Name);

            if (assistant == null)
            {
                throw new InvalidOperationException($"Could not open assistant {args.Name}");
            }

            ExecuteResult ret = ExecuteResult.None;
            if (!assistant.IsDynamic)
            {
                AssistantPage assistantPage = _resolver.Resolve<AssistantPage>();
                ExecuteResult result = await assistantPage.Data.DefaultViewData.Record.LoadNewRecordAsync(clientOrProjection, assistant.Entity);

                result.CheckFailure();

                ProcessInput(context, assistant, args.Input, assistantPage.Data.DefaultViewData.Record);

                if (await assistantPage.LoadAssistantAsModal(clientOrProjection, assistant))
                {
                    ret = await _assistantDialogService.RunAsModalAsync(assistantPage);

                    if (ret != null && !ret.Is(ExecuteResult.Cancel))
                    {
                        ProcessOutput(context, assistant, args.Output, assistantPage.Data.DefaultViewData.Record);
                    }
                }
            }
            else
            {
                DynamicAssistantPage dynamicAssistantPage = _resolver.Resolve<DynamicAssistantPage>();
                ExecuteResult result = await dynamicAssistantPage.Data.DefaultViewData.Record.LoadNewRecordAsync(clientOrProjection, assistant.Entity);

                result.CheckFailure();

                ProcessInput(context, assistant, args.Input, dynamicAssistantPage.Data.DefaultViewData.Record);

                if (await dynamicAssistantPage.LoadAssistantAsModal(clientOrProjection, assistant))
                {
                    ret = await _assistantDialogService.RunAsModalAsync(dynamicAssistantPage);

                    if (ret != null && !ret.Is(ExecuteResult.Cancel))
                    {
                        ProcessOutput(context, assistant, args.Output, dynamicAssistantPage.Data.DefaultViewData.Record);
                    }
                }
            }
            
            return ret;
        }

        // Due to changes done in TEUXX-19159, the inputs/outputs sent to a normal/dynamic assistant are now sent as a JSON array.
        // This means that we no longer get key-value pairs like before, and need to match the parameters in the sequence they are
        // defined. We have to support both formats, hence the check for either JObject (old format) or JArray (new format). We're
        // considering this in both the ProcessInput and ProcessOutput methods below.

        private void ProcessInput(CommandContext context, CpiAssistant assistant, object input, RecordData record)
        {
            if (input != null)
            {
                Dictionary<string, string> values = new Dictionary<string, string>();

                if (input is JObject inputDict)
                {
                    values = inputDict.ToObject<Dictionary<string, string>>();
                }
                else if (input is JArray inputArray && assistant.Input != null)
                {
                    for (int i = 0; i < assistant.Input.Length; i++)
                    {
                        string key = inputArray[i].ToString();
                        if (!string.IsNullOrEmpty(key) && context.TryGetParamValue(key, out object value))
                        {
                            values.Add(assistant.Input[i], ObjectConverter.ToString(value));
                        }
                    }
                }
                else
                {
                    throw new InvalidOperationException($"Inputs for assistant {assistant.Name} are in the wrong format");
                }

                context.ReadParamsInto(values, record);
            }
        }

        private void ProcessOutput(CommandContext context, CpiAssistant assistant, object output, RecordData record)
        {
            if (output != null)
            {
                Dictionary<string, string> values = new Dictionary<string, string>();

                if (output is JObject outputDict)
                {
                    values = outputDict.ToObject<Dictionary<string, string>>();
                }
                else if (output is JArray outputArray && assistant.Output != null)
                {
                    for (int i = 0; i < assistant.Output.Length; i++)
                    {
                        string key = outputArray[i].ToString();
                        if (!string.IsNullOrEmpty(key))
                        {
                            values.Add(key, "${" + assistant.Output[i] + "}");
                        }
                    }
                }
                else
                {
                    throw new InvalidOperationException($"Outputs for assistant {assistant.Name} are in the wrong format");
                }

                context.Assign(values, record);
            }
        }
    }
}
