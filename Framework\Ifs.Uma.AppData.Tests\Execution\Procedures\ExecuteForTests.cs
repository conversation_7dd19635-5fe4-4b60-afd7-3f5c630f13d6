﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures
{
    [TestFixture]
    public class ExecuteForTests : ProcedureTest
    {
        private const string TstCustomerEntityName = "TstCustomer";
        private static readonly string TstCustomerTableName = RemoteNaming.ToTableName(TstCustomerEntityName);
        private const string CustomerNoAttributeName = "CustomerNo";

        [Test]
        public async Task ForEntitySet()
        {
            string result = await DoFor("ForCustomers");
            Assert.AreEqual("#500#501", result); 
        }

        [Test]
        public async Task ForFromArray()
        {
            RemoteRow customerRow = GetCustomer("501");

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Customer"] = customerRow;

            string result = await DoFor("ForCustomerAddress", parameters);
            Assert.AreEqual("#Test Address 3#Test Address 4#Test Address 5", result);
        }

        [Test]
        public async Task ForFromArrayWithWhere()
        {
            RemoteRow customerRow = GetCustomer("501");

            Dictionary<string, object> parameters = new Dictionary<string, object>();
            parameters["Customer"] = customerRow;

            string result = await DoFor("ForFromArrayWithWhere", parameters);
            Assert.AreEqual("#Test Address 4", result);
        }

        [Test]
        public async Task ForWhereAliased()
        {
            string result = await DoFor("ForWhereAliased");
            Assert.AreEqual("#501", result);
        }

        [Test]
        public async Task ForWithReturn()
        {
            string result = await DoFor("ForWithReturn");
            Assert.AreEqual("#A", result);
        }

        [Test]
        public async Task ForOrderBy()
        {
            string result = await DoFor("ForOrderBy");
            Assert.AreEqual("#Test Address 2#Test Address 1#Test Address 5#Test Address 4#Test Address 3", result);
        }

        [Test]
        public async Task ForWithError()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "ForWithError", null);

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Failed);

            ProcedureErrorException e = result.Exception as ProcedureErrorException;
            Assert.IsNotNull(e);
            Assert.AreEqual("Errored '#A'", e.Message);
        }

        [Test]
        public async Task ForWithBreak()
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, "FnBreakForLoop", null);
            result.CheckFailure();
            Assert.AreEqual(3, result.Value);
        }

        private async Task<string> DoFor(string functionName, Dictionary<string, object> parameters = null)
        {
            IProcedureExecutor executor = Resolve<IProcedureExecutor>();

            ExecuteResult result = await executor.CallFunctionAsync(TestOfflineProjection, functionName, parameters);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return (string)result.Value;
        }

        private RemoteRow GetCustomer(string customerNo)
        {
            IMetadata metadata = Resolve<IMetadata>();

            EntityDataSource source = EntityDataSource.FromEntity(metadata, TestOfflineProjection, TstCustomerEntityName);
            EntityQuery query = new EntityQuery(source);
            query.AddFilter(CustomerNoAttributeName, customerNo);

            IDataContextProvider dataContextProvider = Resolve<IDataContextProvider>();
            FwDataContext ctx = (FwDataContext)dataContextProvider.CreateDataContext();
            return ctx.Query(query).FirstOrDefault()?.Row;
        }

        protected override void OnErrorLogged(string message)
        {
            bool expected = message.Contains(nameof(ProcedureErrorException)) || message.Contains("ForWithError");
            if (!expected)
            {
                base.OnErrorLogged(message);
            }
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            
            PrepareDatabase<FwDataContext>("Execution.Procedures.ExecuteForSchema", "Execution.Procedures.CustomerData");
        }
    }
}
