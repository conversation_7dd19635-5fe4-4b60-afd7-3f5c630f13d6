﻿// Copyright (c) Microsoft Corporation.  All rights reserved.
// This source code is made available under the terms of the Microsoft Public License (MS-PL)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Utility;

namespace IQToolkit
{
    /// <summary>
    /// A basic abstract LINQ query provider
    /// </summary>
    internal abstract class QueryProvider : IQueryProvider, IQueryText
    {
        protected QueryProvider()
        {
        }

        public IQueryable<S> CreateQuery<S>(Expression expression)
        {
            return new Query<S>(this, expression);
        }

        public IQueryable CreateQuery(Expression expression)
        {
            if (expression == null) throw new ArgumentNullException("expression");
            Type elementType = TypeHelper.GetElementType(expression.Type);
            try
            {
                return (IQueryable)Activator.CreateInstance(typeof(Query<>).MakeGenericType(elementType), new object[] { this, expression });
            }
            catch (TargetInvocationException tie)
            {
                throw tie.InnerException;
            }
        }

        public S Execute<S>(Expression expression)
        {
            return (S)ExecuteInner(expression);
        }

        public object Execute(Expression expression)
        {
            return ExecuteInner(expression);
        }

        public abstract string GetQueryText(Expression expression);
        protected abstract object ExecuteInner(Expression expression);
    }
}
