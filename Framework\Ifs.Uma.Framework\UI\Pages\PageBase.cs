﻿using System;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.UI.Observables;
using Ifs.Uma.Framework.UI.RelatedPages;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Images;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Prism.Events;
using ExceptionType = Ifs.Uma.Utility.ExceptionType;

namespace Ifs.Uma.Framework.UI.Pages
{
    public abstract class PageBase : EditingViewModel
    {
        public IDialogService DialogService { get; }

        public bool IsSubPage { get; set; } = false;

        public string ProjectionName { get; protected set; }

        public string Name { get; protected set; }

        private string _title;

        public string Title
        {
            get
            {
                return _title;
            }
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged(nameof(Title));
                }
            }
        }

        private string _breadcrumbLabel;
        public string BreadcrumbLabel
        {
            get
            {
                return _breadcrumbLabel;
            }
            protected set
            {
                SetProperty(ref _breadcrumbLabel, value);
            }        
        }

        public RelatedPagesList RelatedPages { get; protected set; }

        private IEventAggregator _eventAggregator;

        private PageClassification? _classification;
        public PageClassification? Classification
        {
            get { return _classification; }
            protected set { SetProperty(ref _classification, value); }
        }

        public UpdatingState UpdatingState { get; } = new UpdatingState();

        public PageBase(IEventAggregator eventAggregator, IDialogService dialogService)
            : base(eventAggregator, dialogService)
        {
            if (dialogService == null)
                throw new ArgumentNullException(nameof(dialogService));
            _eventAggregator = eventAggregator;
            DialogService = dialogService;
            UpdatingState.IsAnythingUpdatingChanged += UpdatingState_IsAnythingUpdatingChanged;
        }

        private void UpdatingState_IsAnythingUpdatingChanged(object sender, EventArgs e)
        {
            if (_eventAggregator != null)
            {
                if (UpdatingState.IsUpdating)
                {
                    DisableGroup();
                }
                else if (!HasChanges)
                {
                    EnableGroup();
                }
            }
        }
        
        protected override void OnIsActiveChanged()
        {
            base.OnIsActiveChanged();

            if (IsActive)
            {
                _eventAggregator.GetEvent<DataChangedEvent>().Subscribe(OnStoredDataChanged, ThreadOption.UIThread);
            }
            else
            {
                _eventAggregator.GetEvent<DataChangedEvent>().Unsubscribe(OnStoredDataChanged);
            }
        }

        private void OnStoredDataChanged(DataChangedEventArgs args)
        {
            OnStoredDataChangedAsync(args.ChangeSet);
        }

        protected virtual void OnStoredDataChangedAsync(DataChangeSet changeSet)
        {
        }

        protected async Task HandleException(Exception ex)
        {
            Logger.HandleException(ExceptionType.Unexpected, ex);
            await DialogService.ShowException(ex, title: Title);
        }

        protected async Task<bool> CheckExecuteResult(ExecuteResult result, bool notifyOffline = true)
        {
            if (result.Failed)
            {
                if (result.IsOffline)
                {
                    if (notifyOffline)
                    {
                        await DialogService.Alert(string.Empty, Strings.YouMustBeOnline);
                    }
                }
                else if (result.Exception?.Message != null)
                {
                    await DialogService.ShowException(result.Exception, title: Title);
                }

                return false;
            }

            return true;
        }

        public abstract Task<bool> LoadPageAsync(NavigationParameter parameter);
    }
}
