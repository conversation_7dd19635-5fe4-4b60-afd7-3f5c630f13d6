﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Database;

namespace IQToolkit.Data.Common
{
    /// <summary>
    /// Defines query execution and materialization policies. 
    /// </summary>
    internal class QueryTranslator
    {
        QueryLinguist linguist;
        QueryMapper mapper;
        QueryPolice police;

        public QueryTranslator(QueryLanguage language, IMetaModel model, QueryPolicy policy)
        {
            if (language == null) throw new ArgumentNullException("language");
            if (model == null) throw new ArgumentNullException("model");
            if (policy == null) throw new ArgumentNullException("policy");
            this.linguist = language.CreateLinguist(this);
            this.mapper = model.CreateMapper(this);
            this.police = policy.CreatePolice(this);
        }

        public QueryLinguist Linguist
        {
            get { return this.linguist; }
        }

        public QueryMapper Mapper
        {
            get { return this.mapper; }
        }

        public QueryPolice Police
        {
            get { return this.police; }
        }

        public virtual Expression Translate(Expression expression)
        {
            // pre-evaluate local sub-trees
            expression = PartialEvaluator.Eval(expression, this.mapper.Model.CanBeEvaluatedLocally);

            // Restore enum values - ms converts enums to the underlying type in the expression tree
            expression = EnumRewriter.Rewrite(expression);

            // apply mapping (binds LINQ operators too)
            expression = this.mapper.Translate(expression);

            // any policy specific translations or validations
            expression = this.police.Translate(expression);

            // any language specific translations or validations
            expression = this.linguist.Translate(expression);

            return expression;
        }
    }
}