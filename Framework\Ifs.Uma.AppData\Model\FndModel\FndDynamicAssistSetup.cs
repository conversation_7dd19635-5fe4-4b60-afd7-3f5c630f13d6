﻿using Ifs.Uma.Data;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName)]
    public class FndDynamicAssistSetup : RemoteRow
    {
        public const string DbTableName = "fnd_dynamic_assist_setup";

        [Column]
        public string Label { get; set; }

        [Column]
        public string Description { get; set; }

        [Column]
        public string Entity { get; set; }

        [Column]
        public string InitMetaFunction { get; set; }

        [Column]
        public string StepsMetaFunction { get; set; }

        [Column]
        public string FinishMetaFunction { get; set; }

        [Column]
        public string NextStepFunction { get; set; } // Not used in web client but present in the structure

        [Column]
        public double? NumberOfSteps { get; set; }

        [Column]
        public double? StartStep { get; set; }

        [Column]
        public bool? ResumeSupported { get; set; }

        public FndDynamicAssistSetup()
            : base(DbTableName)
        {
        }
    }
}
