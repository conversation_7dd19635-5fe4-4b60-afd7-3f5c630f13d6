﻿using System;
using SQLitePCL;

namespace Ifs.Uma.Database.SQLite.CustomFunctions
{
    internal abstract class DateTimeAddInteger : SQLiteCustomFunction
    {
        public DateTimeAddInteger(string name)
            : base(name, true, 2)
        {
        }

        protected sealed override void OnExecute(sqlite3_context ctx, sqlite3_value[] args)
        {
            if (raw.sqlite3_value_type(args[0]) == raw.SQLITE_NULL)
            {
                raw.sqlite3_result_null(ctx);
                return;
            }

            long dateTicks = raw.sqlite3_value_int64(args[0]);
            int value = raw.sqlite3_value_int(args[1]);

            DateTime dateTime = new DateTime(dateTicks, DateTimeKind.Unspecified);
            dateTime = Add(dateTime, value);

            raw.sqlite3_result_int64(ctx, dateTime.Ticks);
        }

        protected abstract DateTime Add(DateTime ts, int value);
    }

    internal abstract class DateTimeAddNumber : SQLiteCustomFunction
    {
        public DateTimeAddNumber(string name)
            : base(name, true, 2)
        {
        }

        protected sealed override void OnExecute(sqlite3_context ctx, sqlite3_value[] args)
        {
            if (raw.sqlite3_value_type(args[0]) == raw.SQLITE_NULL)
            {
                raw.sqlite3_result_null(ctx);
                return;
            }

            long dateTicks = raw.sqlite3_value_int64(args[0]);
            double value = raw.sqlite3_value_double(args[1]);

            DateTime dateTime = new DateTime(dateTicks, DateTimeKind.Unspecified);
            dateTime = Add(dateTime, value);

            raw.sqlite3_result_int64(ctx, dateTime.Ticks);
        }

        protected abstract DateTime Add(DateTime ts, double value);
    }

    internal sealed class DateTimeAddYears : DateTimeAddInteger
    {
        public const string FunctionName = "ifs_datetime_add_years";

        public DateTimeAddYears() 
            : base(FunctionName)
        {
        }

        protected override DateTime Add(DateTime ts, int value)
        {
            return ts.AddYears(value);
        }
    }

    internal sealed class DateTimeAddMonths : DateTimeAddInteger
    {
        public const string FunctionName = "ifs_datetime_add_months";

        public DateTimeAddMonths()
            : base(FunctionName)
        {
        }

        protected override DateTime Add(DateTime ts, int value)
        {
            return ts.AddMonths(value);
        }
    }

    internal sealed class DateTimeAddDays : DateTimeAddNumber
    {
        public const string FunctionName = "ifs_datetime_add_days";

        public DateTimeAddDays()
            : base(FunctionName)
        {
        }

        protected override DateTime Add(DateTime ts, double value)
        {
            return ts.AddDays(value);
        }
    }

    internal sealed class DateTimeAddHours : DateTimeAddNumber
    {
        public const string FunctionName = "ifs_datetime_add_hours";

        public DateTimeAddHours()
            : base(FunctionName)
        {
        }

        protected override DateTime Add(DateTime ts, double value)
        {
            return ts.AddHours(value);
        }
    }

    internal sealed class DateTimeAddMinutes : DateTimeAddNumber
    {
        public const string FunctionName = "ifs_datetime_add_minutes";

        public DateTimeAddMinutes()
            : base(FunctionName)
        {
        }

        protected override DateTime Add(DateTime ts, double value)
        {
            return ts.AddMinutes(value);
        }
    }

    internal sealed class DateTimeAddSeconds : DateTimeAddInteger
    {
        public const string FunctionName = "ifs_datetime_add_seconds";

        public DateTimeAddSeconds()
            : base(FunctionName)
        {
        }

        protected override DateTime Add(DateTime ts, int value)
        {
            return ts.AddSeconds(value);
        }
    }
}
