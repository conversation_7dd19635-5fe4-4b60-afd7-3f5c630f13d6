﻿using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Database;
using Ifs.Uma.AppData.Online;
using Ifs.Uma.Metadata;
using Ifs.Uma.Utility;
using Prism.Events;

namespace Ifs.Uma.AppData.Attachments
{
    public sealed class AlwaysOfflineOnlineAttachmentHandler : OnlineAttachmentHandlerBase
    {
        public AlwaysOfflineOnlineAttachmentHandler(IDatabaseController db,
            ILogger logger, IPerfLogger perfLogger, IEventAggregator eventAggregator, IMetadata metadata)
            : base(db, logger, perfLogger, eventAggregator, metadata)
        {
        }

        protected override Task<OnlineResponse<AttachmentInfo>> DownloadAttachments(string entityName, string clientKeyRef, string serverKeyRef, bool includeDocuments, bool includeMedia, CancellationToken cancellationToken)
        {
            return Task.FromResult(OnlineResponse<AttachmentInfo>.Offline);
        }
    }
}
