﻿using System;
using System.Collections.Generic;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Tests;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution
{
    [TestFixture]
    public class ExecuteIfTests : FrameworkTest
    {
        [Test]
        public void BasicIf()
        {
            var vars = new Dictionary<string, object>();
            vars["Var1"] = 3;

            var args = new CpiIfCallArgs();

            JObject exp = JObject.Parse("{\">\":[ {\"+\": [{\"var\":\"Var1\"}, 5]}, 6 ]}");

            args.Expression = new CpiExpression { JsonLogic = exp };

            bool result = DoIf(args, vars);
            Assert.AreEqual(true, result);
        }

        [Test]
        public void DateTimeIf()
        {
            Dictionary<string, object> vars = new Dictionary<string, object>();
            vars["D1"] = new DateTime(2018, 07, 19, 09, 00, 00);
            vars["D2"] = new DateTime(2018, 07, 19, 10, 00, 00);
            
            CpiIfCallArgs args = new CpiIfCallArgs();
            JObject exp = JObject.Parse("{\">\":[ {\"var\":\"D2\"} , {\"var\":\"D1\"} ]}");
            args.Expression = new CpiExpression { JsonLogic = exp };
            bool result = DoIf(args, vars);
            Assert.AreEqual(true, result);
            
            args = new CpiIfCallArgs();
            exp = JObject.Parse("{\"<\":[ {\"var\":\"D2\"} , {\"var\":\"D1\"} ]}");
            args.Expression = new CpiExpression { JsonLogic = exp };
            result = DoIf(args, vars);
            Assert.AreEqual(false, result);
        }

        private bool DoIf(CpiIfCallArgs args, Dictionary<string, object> vars)
        {
            TestExecutor executor = Resolve<TestExecutor>();

            ExecuteResult result = executor.Call(TestOfflineProjection, CpiExecuteCallMethod.If, args, vars);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return result == ExecuteResult.True;
        }
    }
}
