﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI;
using Ifs.Uma.UI.Assets;
using Ifs.Uma.UI.Icons;
using Ifs.Uma.UI.Images;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.UI.Trees.Nodes
{
    public class Node : ObservableBase
    {
        public string ProjectionName { get; }

        public Node Parent { get; }

        protected NodeContext Context { get; }

        private IList<Node> _children = new List<Node>();

        public ViewData ViewData { get; }

        public EntityDataSource DataSource { get; }

        public CpiTreeNode CpiTreeNode { get; protected set; }

        public const int DefaultMaxDepth = 5;

        private int MaxDepth
        {
            get
            {
                return _ignoreMaxDepth ? int.MaxValue : DefaultMaxDepth;
            }
        }

        public virtual string Title
        {
            get
            {
                return Context.ExpressionRunner.InterpolateString(CpiTreeNode.Label, ViewData.Record);
            }
        }

        public virtual UmaImage Icon { get; private set; } = AssetUtils.Blank;

        private string _id;

        public string Id
        {
            get
            {
                if (_id == null)
                {
                    _id = string.Empty;
                    RemoteRow row = ViewData?.Record?.GetRemoteRow();
                    if (row == null)
                    {
                        _id = Guid.NewGuid().ToString();
                    }
                    else
                    {
                        if (Parent != null)
                        {
                            _id = Parent.Id;
                        }
                        _id += row.TableName + row.GetRowIdentifier(Context.Metadata.MetaModel);
                    }
                }
                return _id;
            }

            protected set
            {
                _id = value;
            }
        }

        private bool _isPromotableToRoot;

        public virtual bool IsPromotableToRoot
        {
            get { return _isPromotableToRoot; }
            set { SetProperty(ref _isPromotableToRoot, value); }
        }

        public virtual UmaImage SecondActionIcon { get; } = IconUtils.Pin;

        private bool _isSecondActionVisible;

        public virtual bool IsSecondActionVisible
        {
            get { return _isSecondActionVisible; }
            set { SetProperty(ref _isSecondActionVisible, value); }
        }

        private bool _showIsRootPin;

        public virtual bool ShowIsRootPin
        {
            get { return _showIsRootPin; }
            set { SetProperty(ref _showIsRootPin, value); }
        }

        private void UpdateActionProperties()
        {
            if (MainAction == TreeStructureMainAction.PinNode)
            {
                ShowIsRootPin = false;
                IsSecondActionVisible = true;
            }
            else
            {
                ShowIsRootPin = _isRoot;
                IsSecondActionVisible = _isPromotableToRoot;
            }
        }

        private bool _couldBeMoreChildren;

        public bool CouldBeMoreChildren
        {
            get { return _couldBeMoreChildren; }
            set { SetProperty(ref _couldBeMoreChildren, value); }
        }

        private bool _showCollapsibleImage;

        public bool ShowCollapsibleImage
        {
            get { return _showCollapsibleImage; }
            set
            {
                if (_showCollapsibleImage != value)
                {
                    SetProperty(ref _showCollapsibleImage, value);
                }
            }
        }

        public ObservableCollection<Node> TreeChildren { get; } = new ObservableCollection<Node>();

        private int _relativeDepth;

        public int RelativeDepth
        {
            get { return _relativeDepth; }
            set
            {
                SetProperty(ref _relativeDepth, value);
                UpdateCouldBeMoreChildren();
            }
        }

        private int _breadcrumbDepth;

        public int BreadcrumbDepth
        {
            get { return _breadcrumbDepth; }
            set
            {
                SetProperty(ref _breadcrumbDepth, value);
            }
        }

        private bool _isRoot;

        public bool IsRoot
        {
            get { return _isRoot; }
            set
            {
                SetProperty(ref _isRoot, value);
                IsPromotableToRoot = !_isRoot ? State.CouldHaveChildren : false;
                UpdateRelativeDepth();
                TrimTreeChildrenToMaxDepth();
                UpdateCouldBeMoreChildren();
                UpdateActionProperties();
            }
        }

        public UpdatingState LoadingState { get; } = new UpdatingState();

        public bool IsLoading
        {
            get
            {
                return LoadingState.IsUpdating;
            }
        }

        private void UpdateCouldBeMoreChildren()
        {
            if (!_isRoot && RelativeDepth >= MaxDepth)
            {
                CouldBeMoreChildren = State.CouldHaveChildren;
                return;
            }
            CouldBeMoreChildren = false;
        }

        public void UpdateRelativeDepth()
        {
            if (Parent == null || IsRoot)
            {
                RelativeDepth = 0;
            }
            else
            {
                RelativeDepth = Parent.RelativeDepth + 1;
            }

            foreach (Node child in _children)
            {
                child.UpdateRelativeDepth();
            }
        }

        public void TrimTreeChildrenToMaxDepth()
        {
            if (RelativeDepth >= MaxDepth)
            {
                foreach (Node child in _children)
                {
                    TreeChildren.Remove(child);
                    child.TrimTreeChildrenToMaxDepth();
                }
            }
            else
            {
                foreach (Node child in _children)
                {
                    if (!_isCollapsed && !TreeChildren.Contains(child))
                    {
                        TreeChildren.Add(child);
                    }

                    child.TrimTreeChildrenToMaxDepth();
                }
            }
        }

        public Command ToggleCollapseCommand { get; }

        private UmaImage _collapsibleImage;

        public UmaImage CollapsibleImage
        {
            get { return _collapsibleImage; }
            set { SetProperty(ref _collapsibleImage, value); }
        }

        private bool _isSelected;

        public bool IsSelected
        {
            get => _isSelected;
            internal set => SetProperty(ref _isSelected, value);
        }

        private MaxDepthNode _maxDepthNode;

        private bool _ignoreMaxDepth;

        protected EntityRecord Record { get; }

        public Node(Node parent, NodeContext context, EntityRecord record, CpiTreeNode cpiTreeNode, bool ignoreMaxDepth = false)
        {
            if (ignoreMaxDepth)
            {
                _ignoreMaxDepth = true;
            }

            Parent = parent;
            Context = context;
            ProjectionName = context?.ProjectionName;
            Record = record;
            CpiTreeNode = cpiTreeNode;
            LoadingState.ParentState = parent?.LoadingState;

            if (MainAction == TreeStructureMainAction.PinNode)
            {
                SecondActionIcon = IconUtils.CaretForward;
            }

            IEnumerable<CpiTreeConnection> connections = CpiTreeNode?.Connections?.Select(x => x.Connection);

            // Depth 0 means this node is the root so no context/data is provided or used, instead this node will have it's children injected from TreeRootNode.
            // This is also used for LoadMoreNodes
            if (Parent == null || context == null)
            {
                State = new NodeState(null, null, null, Context?.Tree, connections);
                IsRoot = false;
                return;
            }

            RecordData recordData = new RecordData(Context.Logger, Context.Metadata, Context.DataHandler);
            recordData.LoadRecord(ProjectionName, Record);

            ViewData = new ViewData(Context.PageData, recordData);
            DataSource = EntityDataSource.FromEntity(Context.Metadata, ProjectionName, ViewData.Record.EntityName);
            State = new NodeState(Context.Metadata, ViewData.Record, DataSource, Context.Tree, connections);

            string pk = ObjPrimaryKey.FromPrimaryKey(Context.Metadata.MetaModel, Record.Row)?.ToKeyRef();
            TreePage page = Context.PageData.Page as TreePage;
            if (page.NodeToExpand == null && page?.FilteredRecordKey?.ToKeyRef() == pk)
            {
                // If the current node being initialized is the one that's being set as the filtered record of the tree page,
                // then this is the last node we should expand automatically. The NodeToExpand property is set to indicate this.
                page.NodeToExpand = this;
            }

            CalculateIcon();

            ToggleCollapseCommand = Command.FromAsyncMethod(ToggleCollapse);

            if (State.HasOnlineOnlyConnection && !State.IsOnlineNodeExpandable(parent))
            {
                _isCollapsed = true;
                _collapsibleImage = IconUtils.TriangleForward;
            }
            else
            {
                _collapsibleImage = IconUtils.TriangleDown;
            }

            IsRoot = false;
        }

        private void CalculateIcon()
        {
            if (CpiTreeNode?.IconSet?.Icons == null || CpiTreeNode?.IconSet?.Icons.Length == 0)
            {
                return;
            }
            if (CpiTreeNode != null && CpiTreeNode?.IconSet?.Icons != null)
            {
                foreach (CpiIcon cpiIcon in CpiTreeNode?.IconSet?.Icons?.SelectMany(x => x.Values) ?? Enumerable.Empty<CpiIcon>())
                {
                    if (string.IsNullOrWhiteSpace(cpiIcon.Name))
                    {
                        continue;
                    }
                    if (Context.ExpressionRunner.RunCheck(cpiIcon.OfflineExpression ?? cpiIcon.Expression, ViewData, true))
                    {
                        Dictionary<string, CpiExpression>[] emphases = CpiTreeNode.IconSet?.OfflineEmphasis ?? CpiTreeNode.IconSet?.Emphasis;
                        string emphasis = Context.ExpressionRunner.GetEmphasis(emphases, ViewData);
                        UmaColor iconColor = UmaColor.FromEmphasis(emphasis) ?? UmaColors.Black;
                        Icon = IconUtils.Load(cpiIcon.Name, iconColor);
                        return;
                    }
                }
            }
        }

        private bool _isCollapsed;

        private bool _isFirstExpand = true;

        public bool PreventLoadingChildren { get; set; }

        private async Task ToggleCollapse()
        {
            if (_isCollapsed)
            {
                await Expand();
            }
            else
            {
                Collapse();
            }
        }

        public async Task Expand()
        {
            if (!_isCollapsed)
            {
                return;
            }

            _isCollapsed = false;

            TreePage page = Context.PageData.Page as TreePage;
            if (page != null)
            {
                page.NodeToExpand = this;
            }

            if (_children.Count == 0)
            {
                PreventLoadingChildren = false;
                await LoadChildren(Tree.PageSize);
            }
            else
            {
                foreach (Node child in _children)
                {
                    TreeChildren.Add(child);
                    foreach (Node childsChild in child.TreeChildren)
                    {
                        TreeChildren.Add(childsChild);
                    }
                }
            }

            if (_children.Count > 0)
            {
                CollapsibleImage = IconUtils.TriangleDown;
            }
            else
            {
                ShowCollapsibleImage = false;
            }

            if (State.HasOnlineOnlyConnection && _isFirstExpand)
            {
                _isFirstExpand = false;
                await OnlineOnlyLoadChildren();
            }
        }

        private void Collapse()
        {
            if (_isCollapsed)
            {
                return;
            }

            _isCollapsed = true;

            foreach (Node child in _children)
            {
                foreach (Node childsChild in child.TreeChildren)
                {
                    TreeChildren.Remove(childsChild);
                }
                TreeChildren.Remove(child);
            }

            CollapsibleImage = IconUtils.TriangleForward;
        }

        private LoadMoreNode _loadMoreNode;

        public async Task OnlineOnlyLoadChildren()
        {
            if (!State.HasOnlineOnlyConnection)
            {
                return;
            }

            if (_loadMoreNode != null)
            {
                _children.Remove(_loadMoreNode);
                TreeChildren.Remove(_loadMoreNode);
            }

            await ForceLoadChildren(Tree.PageSize);

            if (State.NextQuery != null || LoadedNodeQueue?.Count > 0)
            {
                _loadMoreNode = new LoadMoreNode(this);
                TreeChildren.Add(_loadMoreNode);
                _children.Add(_loadMoreNode);
            }
        }

        public async Task RefreshData()
        {
            // This probably means the record has been deleted and so the data cannot be refreshed
            if (ViewData?.Record?.GetRemoteRow() == null)
            {
                return;
            }

            EntityQuery query = new EntityQuery(DataSource);
            query.SelectAttributes = GetNodeAttributes(ProjectionName, CpiTreeNode);
            query.SetFilter(ViewData.Record.ToPrimaryKey());
            await ViewData.Record.LoadRecordAsync(query, false);
        }

        /// <summary>
        /// Returns the total number of nodes loaded from this method call and it's subsequent calls to child node's LoadChildren methods.
        /// </summary>
        public async Task<int> LoadChildren(int minToLoad)
        {
            // This node has a "load more" node and should not automatically load more children.
            if (_loadMoreNode != null || PreventLoadingChildren)
            {
                return 0;
            }

            return await ForceLoadChildren(minToLoad);
        }

        private async Task<int> ForceLoadChildren(int minToLoad)
        {
            if (_isCollapsed)
            {
                ShowCollapsibleImage = true;
                return 0;
            }

            int haveLoaded = 0;

            foreach (Node child in _children)
            {
                int childCount = await child.LoadChildren(minToLoad);
                minToLoad -= childCount;
                haveLoaded += childCount;

                if (minToLoad <= 0)
                {
                    break;
                }
            }

            while (minToLoad > 0)
            {
                Node node = await LoadNextNode();

                if (node != null)
                {
                    TreeChildren.Add(node);
                    ShowCollapsibleImage = true;
                    _children.Add(node);
                    node.TreeChildren.CollectionChanged += (s, e) => TreeChildren_CollectionChanged(node, e);
                    minToLoad -= 1;
                    haveLoaded += 1;

                    TreePage page = Context.PageData.Page as TreePage;
                    if (minToLoad > 0 && !node.Parent.EqualsNode(page?.NodeToExpand))
                    {
                        int childCount = await node.LoadChildren(minToLoad);
                        minToLoad -= childCount;
                        haveLoaded += childCount;
                    }
                    else
                    {
                        node.PreventLoadingChildren = true;
                        node.Collapse();
                        node.ShowCollapsibleImage = true;
                    }

                    if (minToLoad <= 0)
                    {
                        break;
                    }
                }
                else
                {
                    break;
                }
            }

            UpdateRelativeDepth();
            await UpdateMaxDepthNode();

            return haveLoaded;
        }

        public static async Task PromoteNode(Node node, TreePage page, Tree tree)
        {
            if (node.IsPromotableToRoot)
            {
                node.PreventLoadingChildren = false;

                // LoadMoreLock is only needed for Windows but it's perfectly okay to have it here really.
                // Basically the Windows list was trying to load more nodes when clearing the list before it had chance to set the correct data.
                // This is fixed by stopping LoadMoreNodes from being run until we're ready.
                tree.LoadMoreLock = true;
                if (node is ShellNode shellNode)
                {
                    node = await shellNode.Parent.ReplaceChildShellNode(shellNode);
                }

                page.NodeToExpand = node;
                page.RootNode = node;
                tree.RootNode = node;
                await page.RootNode.Expand();
                tree.LoadMoreLock = false;
                await tree.LoadMoreNodes();
            }
        }

        private async Task UpdateMaxDepthNode()
        {
            // This means we're at the maximum depth and should insert a max depth node to ensure the user understands there could be more nodes inside this one.
            if (CouldBeMoreChildren && _maxDepthNode == null && RelativeDepth == MaxDepth)
            {
                int? childRecordCount = await GetChildRecordsCount();

                if (_children.Count > 0)
                {
                    _maxDepthNode = new MaxDepthNode(this, _children.Count);
                }
                else if (childRecordCount != 0)
                {
                    _maxDepthNode = new MaxDepthNode(this, childRecordCount);
                }
            }
            // If the relative depth isn't exactly the max depth then a max depth node shouldn't be shown and should be removed.
            else if (RelativeDepth != MaxDepth && _maxDepthNode != null)
            {
                _children.Remove(_maxDepthNode);
                TreeChildren.Remove(_maxDepthNode);
                _maxDepthNode = null;
            }

            if (_maxDepthNode != null)
            {
                if (!TreeChildren.Contains(_maxDepthNode))
                {
                    TreeChildren.Add(_maxDepthNode);
                }

                ShowCollapsibleImage = false;

                if (!_children.Contains(_maxDepthNode))
                {
                    _children.Add(_maxDepthNode);
                }
            }
        }

        private async Task<Node> LoadNextNode()
        {
            await LoadItems();

            if (LoadedNodeQueue.Count > 0)
            {
                return LoadedNodeQueue.Dequeue();
            }

            return null;
        }

        private Queue<Node> LoadedNodeQueue { get; } = new Queue<Node>();

        private async Task LoadItems()
        {
            if (RelativeDepth >= MaxDepth)
            {
                return;
            }

            if (State.AreMoreItemsToLoad)
            {
                IEnumerable<Node> nodes = await GetChildNodes();
                foreach (Node node in nodes)
                {
                    LoadedNodeQueue.Enqueue(node);
                }
            }
        }

        public async Task<Node> ReplaceChildShellNode(ShellNode existingShellChild)
        {
            int childIndex = _children.IndexOf(existingShellChild);

            if (childIndex == -1)
            {
                throw new Exception($"Node {existingShellChild.Id} is not a child of {Id} so cannot be replaced");
            }

            // Ensure all children are removed from TreeChildren
            Collapse();

            NotifyCollectionChangedEventHandler handler = (s, e) => { TreeChildren_CollectionChanged(_children[childIndex], e); };
            // Remove existing child event listener
            _children[childIndex].TreeChildren.CollectionChanged -= handler;

            Node newChild = existingShellChild.ConvertToNode();
            _children[childIndex] = newChild;

            _children[childIndex].TreeChildren.CollectionChanged += handler;

            await Expand();

            return newChild;
        }

        private void TreeChildren_CollectionChanged(Node node, NotifyCollectionChangedEventArgs e)
        {
            int startIndex = TreeChildren.IndexOf(node);

            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                for (int i = 0; i < e.NewItems.Count; i++)
                {
                    int nodeIndex = startIndex + 1 + e.NewStartingIndex + i;
                    if (nodeIndex > TreeChildren.Count)
                    {
                        continue;
                    }
                    TreeChildren.Insert(nodeIndex, (Node)e.NewItems[i]);
                }
            }
            else if (e.Action == NotifyCollectionChangedAction.Remove && e.OldItems != null)
            {
                for (int i = 0; i < e.OldItems.Count; i++)
                {
                    TreeChildren.Remove((Node)e.OldItems[i]);
                }
            }
        }

        public NodeState State { get; protected set; }

        public virtual TreeStructureMainAction MainAction
        {
            get
            {
                return Parent.MainAction;
            }
        }

        protected IDisposable BeginUpdating()
        {
            return LoadingState.BeginUpdating();
        }

        protected virtual async Task<IEnumerable<Node>> GetChildNodes()
        {
            List<Node> children = new List<Node>();

            if (CpiTreeNode?.Connections == null || !CpiTreeNode.Connections.Any() || State.CurrentConnection == null)
            {
                return children;
            }

            using (BeginUpdating())
            {
                // TODO: Add support for other connection types - http://techblogs/uxx/?p=4371

                if (State.NextQuery == null && State.CurrentConnection?.Query != null)
                {
                    children = await GetChildrenForQuery(State.CurrentConnection.Node, State.CurrentConnection.Query);
                }
                else if (State.NextQuery != null)
                {
                    children = await GetChildrenForQuery(State.CurrentConnection.Node, State.NextQuery);
                }
            }
            return children;
        }

        protected async Task<List<Node>> GetChildrenForQuery(CpiTreeNode childCpiNode, EntityQuery query)
        {
            List<Node> children = new List<Node>();
            try
            {
                EntityQueryResult result = await Context.DataHandler.GetRecordsAsync(query, CancellationToken.None);
                State.NextQuery = result?.GetNextQuery();

                foreach (EntityRecord record in result?.Records ?? Enumerable.Empty<EntityRecord>())
                {
                    Node node = new Node(this, Context, record, childCpiNode);
                    children.Add(node);
                }
            }
            catch (Exception exception)
            {
                await HandleException(exception);
            }
            return children;
        }

        private async Task<int?> GetChildRecordsCount()
        {
            try
            {
                EntityQuery query = null;
                if (State.NextQuery == null && State.CurrentConnection?.Query != null)
                {
                    query = State.CurrentConnection.Query;
                }
                else if (State.NextQuery != null)
                {
                    query = State.NextQuery;
                }

                if (query != null)
                {
                    return await Context.DataHandler.CountRecordsAsync(query);
                }
            }
            catch (Exception exception)
            {
                await HandleException(exception);
            }
            return null;
        }

        private async Task HandleException(Exception exception)
        {
            Context.Logger.HandleException(Utility.ExceptionType.Unexpected, exception);
            await Context.DialogService.ShowException(exception);
        }

        public static HashSet<string> GetNodeAttributes(string projectionName, CpiTreeNode cpiNode)
        {
            HashSet<string> attributes = new HashSet<string>();
            AttributeFinder.FindInLabel(attributes, cpiNode.Label);
            Dictionary<string, CpiExpression>[] emphases = cpiNode.IconSet?.OfflineEmphasis ?? cpiNode.IconSet?.Emphasis;
            IEnumerable<CpiExpression> emphasis = emphases?.SelectMany(x => x.Values);
            AttributeFinder.FindInEmphasis(attributes, projectionName, emphasis);
            return attributes;
        }

        public IList<Node> GetFamilyTree()
        {
            List<Node> ancestors = new List<Node> { this };
            if (Parent == null)
            {
                return ancestors;
            }
            ancestors.AddRange(Parent.GetFamilyTree());
            return ancestors;
        }

        public override string ToString()
        {
            return $"{RelativeDepth}: {Title}";
        }

        public bool EqualsNode(object obj)
        {
            if (obj is Node node)
            {
                return Id == node.Id;
            }
            return false;
        }
    }
}
