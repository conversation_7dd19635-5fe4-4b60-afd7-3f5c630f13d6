﻿using System;
using Ifs.Uma.Metadata;
using Ifs.Uma.UI.Observables;

namespace Ifs.Uma.Framework.Data
{
    public sealed class AttributeValue : ObservableBase
    {
        public event EventHandler ValueChanged;

        private object _valueInternal;

        public object Value
        {
            get => _valueInternal;
            set
            {
                Record?.Assign(AttributePath.Path, value);
            }
        }

        private RecordData _record;
        public RecordData Record
        {
            get => _record;
            set
            {
                RecordData oldValue = _record;
                if (SetProperty(ref _record, value))
                {
                    OnRecordDataChanged(oldValue, value);
                }
            }
        }

        public AttributePath AttributePath { get; }

        internal static AttributeValue Create(string attributePath, RecordData record)
        {
            AttributePath path = attributePath == null ? null : AttributePath.Create(attributePath);
            return path == null ? null : new AttributeValue(path, record);
        }

        internal AttributeValue(AttributePath attributePath, RecordData record)
        {
            AttributePath = attributePath ?? throw new ArgumentNullException(nameof(attributePath));
            Record = record;
        }

        private void OnRecordDataChanged(RecordData oldValue, RecordData newValue)
        {
            if (oldValue != null)
            {
                if (AttributePath.RefName != null)
                {
                    RecordRef recordRef = oldValue.GetReference(AttributePath.RefName);
                    recordRef.RecordLoaded -= DataChanged;
                }
                else
                {
                    oldValue.DataChanged -= DataChanged;
                }
            }
            
            if (newValue != null)
            {
                if (AttributePath.RefName != null)
                {
                    RecordRef recordRef = newValue.GetReference(AttributePath.RefName);
                    recordRef.RecordLoaded += DataChanged;
                }
                else
                {
                    newValue.DataChanged += DataChanged;
                }
            }

            UpdateValue();
        }

        private void DataChanged(object sender, EventArgs e)
        {
            UpdateValue();
        }

        private void UpdateValue()
        {
            object value = null;
            Record?.TryGetRecordValue(AttributePath, out value);

            if (SetProperty(ref _valueInternal, value, nameof(Value)))
            {
                ValueChanged?.Invoke(this, EventArgs.Empty);
            }
        }
    }
}
