﻿using Ifs.Uma.Data;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Model
{
    [Table(Name = DbTableName, Class = MetaTableClass.AppRemote)]
    [Index(Name = "ix_edm_file", Unique = true, Columns =
            nameof(DocClass) + "," +
            nameof(DocNo) + "," +
            nameof(DocSheet) + "," +
            nameof(DocRev) + "," +            
            nameof(DocType) + "," +
            nameof(FileNo)
        )]
    [Relation(ReferencedRowType = typeof(DocIssue),
        Columns =
            nameof(DocClass) + "," +
            nameof(DocNo) + "," +
            nameof(DocSheet) + "," +
            nameof(DocRev),
        ReferencedColumns =
            nameof(DocIssue.DocClass) + "," +
            nameof(DocIssue.DocNo) + "," +
            nameof(DocIssue.DocSheet) + "," +
            nameof(DocIssue.DocRev)
        )]
    public class EdmFile : RemoteRow
    {
        public const string DbTableName = FwDataContext.FwTablePrefix + "edm_file";

        #region Field Definitions

        private string _docClass;
        private string _docNo;
        private string _docSheet;
        private string _docRev;        
        private string _docType;
        private string _fileName;
        private long? _fileNo;
        private string _fileType;
        private AttachmentStatus? _attachmentStatus;
        private string _failReason;

        #endregion

        public EdmFile()
            : base(DbTableName)
        {
            EntitySetName = "EdmFiles";
        }

        #region Property Definitions

        // The order of ServerPrimaryKey columns listed here must match the server
        // for client primary keys to work

        [Column(Storage = nameof(_docClass), Mandatory = true, ServerPrimaryKey = true)]
        public string DocClass
        {
            get => _docClass;
            set => SetProperty(ref _docClass, value);
        }

        [Column(Storage = nameof(_docNo), Mandatory = true, ServerPrimaryKey = true)]
        public string DocNo
        {
            get => _docNo;
            set => SetProperty(ref _docNo, value);
        }
        
        [Column(Storage = nameof(_docSheet), Mandatory = true, ServerPrimaryKey = true)]
        public string DocSheet
        {
            get => _docSheet;
            set => SetProperty(ref _docSheet, value);
        }

        [Column(Storage = nameof(_docRev), Mandatory = true, ServerPrimaryKey = true)]
        public string DocRev
        {
            get => _docRev;
            set => SetProperty(ref _docRev, value);
        }

        [Column(Storage = nameof(_docType), Mandatory = true, ServerPrimaryKey = true)]
        public string DocType
        {
            get => _docType;
            set => SetProperty(ref _docType, value);
        }

        [Column(Storage = nameof(_fileName))]
        public string FileName
        {
            get => _fileName;
            set => SetProperty(ref _fileName, value);
        }

        [Column(Storage = nameof(_fileNo), Mandatory = true, ServerPrimaryKey = true)]
        public long? FileNo
        {
            get => _fileNo;
            set => SetProperty(ref _fileNo, value);
        }

        [Column(Storage = nameof(_fileType))]
        public string FileType
        {
            get => _fileType;
            set => SetProperty(ref _fileType, value);
        }

        [Column(Storage = nameof(_attachmentStatus))]
        public AttachmentStatus? AttachmentStatus
        {
            get => _attachmentStatus;
            set => SetProperty(ref _attachmentStatus, value);
        }

        [Column(Storage = nameof(_failReason))]
        public string FailReason
        {
            get => _failReason;
            set => SetProperty(ref _failReason, value);
        }

        #endregion
    }
}
