﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Expressions;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution
{
    [TestFixture]
    public class IfsApiMethodLogicTest
    {
        #region DateDiffApiTest
        [Test(Description = "Evaluate DateDiff method for ideal scenario")]
        [TestCase("year", "2018-07-19T19:00:00", "2012-07-19T19:00:00", 6)]
        [TestCase("year", "2008-07-19T19:00:00", "2012-07-19T19:00:00", 4)]
        [TestCase("year", "2018-07-19", "2022-07-21", 4)]
        [TestCase("year", "2018-07-19T09:00:00", "2018-07-19T10:20:00", 0)]
        [TestCase("month", "2008-07-19T19:00:00", "2008-10-29T19:00:00", 3)]
        [TestCase("month", "2008-07-12T19:00:00", "2008-10-09T19:00:00", 2)]
        [TestCase("day", "2018-07-19T09:00:00", "2018-07-21T09:00:00", 2)]
        [TestCase("day", "2018-07-19T09:00:00", "2018-07-19T09:00:00", 0)]
        [TestCase("day", "2018-07-19", "2018-07-21", 2)]
        [TestCase("hour", "2018-07-19T09:00:00", "2018-07-20T09:00:00", 24)]
        [TestCase("hour", "2018-07-19T09:00:00", "2018-07-20T08:00:00", 23)]
        [TestCase("hour", "2018-07-19T09:00:00", "2018-07-19T19:00:00", 10)]
        [TestCase("minute", "2018-07-19T09:00:00", "2018-07-19T19:00:00", 600)]
        [TestCase("minute", "2018-07-19T09:00:00", "2018-07-19T10:20:00", 80)]
        public void DateDiffExpressionLogicTest(string unit, string sd, string ed, int expectedResult)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("startDate", sd);
            vp.Add("endDate", ed);

            Expression expr = CreateExpression("{\"method\":[\"api.dateDiff\",[\"" + unit + "\", {\"var\":\"startDate\"}, {\"var\":\"endDate\"}]],\"@type\":\"JsonLogic\"}", vp);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();
            Assert.AreEqual(expectedResult, result);
        }

        [Test(Description = "Test checks if method throws correct exception for invalid parameter")]
        [TestCase("month", "210", "2018-07-19T19:00:00", typeof(ArgumentNullException))]
        [TestCase("day", "2018-07-19T09:00:00Z", "2018-07-19T19:00:00", typeof(ArgumentNullException))]
        [TestCase("thh", "2018-07-19T09:00:00", "2018-07-19T19:00:00", typeof(NotSupportedException))]
        public void DateDiffExpressionErrorTest(string unit, string sd, string ed, Type expectedExceptionType)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("startDate", sd);
            vp.Add("endDate", ed);

            Expression expr = CreateExpression("{\"method\":[\"api.dateDiff\",[\"" + unit + "\", {\"var\":\"startDate\"}, {\"var\":\"endDate\"}]],\"@type\":\"JsonLogic\"}", vp);

            Exception ex = Assert.Throws(expectedExceptionType, () =>
            {
                Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
                method();
            });
        }

        #endregion

        #region MatchExpressionLogicTest 
        [TestCase("x", "/[a-g]/", false)]
        [TestCase("abc", "/[a-g]{3}/", true)]
        [TestCase("abcdef", "/^[a-f]+$/i", true)]
        [TestCase("ABCDEF", "/^[a-f]+$/i", true)]
        [TestCase("ABCDEF", "/^[a-f]+$/", false)]
        [TestCase("Hello\nWorld", "/^World$/m", true)]
        [TestCase("Hello World", "/world$/i", true)]
        [TestCase("Hello World", "/^world$/", false)]
        [TestCase("hello\nworld", "/^world$/m", true)]
        [TestCase("hello\nworld", "/^world$/", false)]
        [TestCase("hello.world", "/hello\\.world$/", true)]
        [TestCase("hello\nworld", "/hello.world$/s", true)]
        [TestCase("hello-world", "/^[a-zA-Z0-9_]+$/i", false)]
        [TestCase("https://example.com", @"/^https?://[^\s/$.?#].[^\s]*$/", true)]
        [TestCase("http//example.com", @"/^https?://[^\s/$.?#].[^\s]*$/", false)]
        [TestCase("<EMAIL>", @"/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/i", true)]
        [TestCase("abc@=gmail.com", @"/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/i", false)]
        [TestCase("ABCdef", "/^[a-f]+$/i", true)]
        [TestCase("ABCDEF", "/^[a-f]+$/i", true)]
        [TestCase("abcdef", "/^[a-f]+$/m", true)]
        [TestCase("123\n456", "/^456$/m", true)]
        [TestCase("123\n456", "/^456$/", false)]
        public void MatchExpressionLogicTest(string input, string pattern, bool expectedResult)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("input", input);
            vp.Add("pattern", pattern);

            Expression expr = CreateExpression("{\"method\":[\"api.match\", [{\"var\":\"input\"}, {\"var\":\"pattern\"}]], \"@type\":\"JsonLogic\"}", vp);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();
            Assert.AreEqual(expectedResult, result);
        }

        [Test(Description = "Test checks if Match method throws correct exception for invalid parameters")]
        [TestCase(null, "pattern", typeof(ArgumentException))]
        [TestCase("string", null, typeof(ArgumentException))]
        [TestCase("", "pattern", typeof(ArgumentException))]
        [TestCase("string", "", typeof(ArgumentException))]
        [TestCase("string", "[invalid-regex", typeof(ArgumentException))]
        public void MatchExpressionErrorTest(string input, string pattern, Type expectedExceptionType)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("input", input);
            vp.Add("pattern", pattern);

            Expression expr = CreateExpression("{\"method\":[\"api.match\", [{\"var\":\"input\"}, {\"var\":\"pattern\"}]], \"@type\":\"JsonLogic\"}", vp);

            Exception ex = Assert.Throws(expectedExceptionType, () =>
            {
                Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
                method();
            });
        }
        #endregion

        #region ContainsApiTest
        [Test(Description = "Check if a value exists in a list of values")]
        [TestCase("Barcode", new string[] { "ID", "Name", "Barcode" }, true)]
        [TestCase("Barcode", new string[] { "ID", "Name", "QRCode" }, false)]
        [TestCase("Name", new string[] { "ID", "Name", "Barcode" }, true)]
        [TestCase("Name", new string[] { "ID", "QRCode", "Barcode" }, false)]
        [TestCase("QRCode", new string[] { "ID", "Name", "QRCode", "Barcode" }, true)]
        [TestCase("QRCode", new string[] { "ID", "Name", "Barcode" }, false)]
        [TestCase("ID", new string[] { "ID", "Name", "QRCode", "Barcode" }, true)]
        [TestCase("ID", new string[] { "Name", "QRCode", "Barcode" }, false)]
        [TestCase("Address", new string[] { "ID", "Name", "QRCode", "Barcode" }, false)]
        [TestCase("Address", new string[] { "ID", "Name", "Address", "QRCode", "Barcode" }, true)]

        public void ContainsApiExpressionLogicTest(string value, string[] list, bool expectedResult)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("value", value);
            vp.Add("list", list);

            Expression expr = CreateExpression("{\"method\":[\"api.contains\",[{\"var\":\"value\"}, {\"var\":\"list\"}]],\"@type\":\"JsonLogic\"}", vp);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();
            Assert.AreEqual(expectedResult, result);
        }

        [Test(Description = "Test checks if method throws correct exception for invalid parameter")]
        [TestCase("QRcode", null, typeof(ArgumentException))]
        [TestCase("QRcode", new object[] { null }, typeof(ArgumentException))]
        [TestCase("12345", new object[] { "Barcode", "QRcode", 12345 }, typeof(ArgumentException))]

        public void ContainsApiExpressionErrorTest(string value, object[] list, Type expectedExceptionType)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("value", value);
            vp.Add("list", list);

            Expression expr = CreateExpression("{\"method\":[\"api.contains\",[{\"var\":\"value\"}, {\"var\":\"list\"}]],\"@type\":\"JsonLogic\"}", vp);

            Exception ex = Assert.Throws(expectedExceptionType, () =>
            {
                Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
                method();
            });
        }

        #endregion

        #region ContainsCsvTest
        [Test]
        [TestCase("#TODAY#")]
        [TestCase("#TOMORROW#")]
        [TestCase("#YESTERDAY#")]
        public void ContainsCsvExpressionLogicTest(string value)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("value", value);
            object dateTime = GetDateTime(value);
            Expression expr = CreateExpression("{\"method\":[\"api.resolveCSV\",[{\"var\":\"value\"}]], \"@type\":\"JsonLogic\"}", vp);
            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();
            Assert.AreEqual(dateTime, result);
        }
        private static object GetDateTime(string param)
        {
            switch (param)
            {
                case "#TODAY#":
                    return DateTime.Today;
                case "#TOMORROW#":
                    return DateTime.Today.AddDays(1);
                case "#YESTERDAY#":
                    return DateTime.Today.AddDays(-1);
                default:
                    return null;
            }
        }
        [Test]
        [TestCase("INVALID", typeof(ArgumentException))]
        public void ContainsCsvExpressionErrorTest(string value, Type expectedExceptionType)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("value", value);
            Expression expr = CreateExpression("{\"method\":[\"api.resolveCSV\",[{\"var\":\"value\"}]], \"@type\":\"JsonLogic\"}", vp);
            Exception ex = Assert.Throws(expectedExceptionType, () =>
            {
                Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
                method();
            });
        }
        #endregion

        #region SubstringTest
        [Test]
        [TestCase("CUS123", "0", "3", "CUS", true)]
        [TestCase("ABC123", "0", "3", "CUS", false)]
        [TestCase("ABC123", "0", "3", "ABC", true)]
        [TestCase("abc123", "0", "3", "ABC", false)]
        [TestCase("ABCD123", "0", "4", "ABCD", true)]
        [TestCase("ABC123", "0", "4", "ABCD", false)]

        public void ContainsSubstringExpressionLogicTest(string description, string startIndex, string index, string value, bool expectedOutput)
        {
            ValueProvider vp = new ValueProvider();
            vp.Add("description", description);
            vp.Add("startIndex", startIndex);
            vp.Add("index", index);
            vp.Add("value", value);

            Expression expr = CreateExpression("{\"==\":[{\"method\":[\"api.substring\",[{\"var\":\"description\"},{\"var\": \"startIndex\"},{\"var\": \"index\"}]]},{\"var\": \"value\"}],\"@type\": \"JsonLogic\"}", vp);

            Func<object> method = Expression.Lambda<Func<object>>(Expression.Convert(expr, typeof(object))).Compile();
            object result = method();
            Assert.AreEqual(expectedOutput, result);
        }
        #endregion

        #region Private methods
        private Expression CreateExpression(string json, ValueProvider vp)
        {
            JObject obj = JObject.Parse(json);
            Expression expr = IfsExpression.FromJsonLogic(obj);
            expr = ValueReplacer.Rewrite(expr, vp);
            expr = InToIfRewriter.Rewrite(expr);

            return expr;
        }
        #endregion

        #region valueProvider
        private class ValueProvider : IExpressionValueProvider
        {
            private readonly Dictionary<string, object> _values = new Dictionary<string, object>();

            public void Add(string propertyName, object value)
            {
                _values[propertyName] = value;
            }

            public bool TryGetValue(string propertyName, out object value)
            {
                return _values.TryGetValue(propertyName, out value);
            }

            public bool TryCallMethod(string methodName, object[] args, out object result)
            {
                throw new NotImplementedException();
            }
        }
        #endregion
    }
}
