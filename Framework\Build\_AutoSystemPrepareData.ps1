$solutionDir = "../"

Write-Output "============ Start - Prepare Test Data"

Push-Location $solutionDir

packages\NUnit.ConsoleRunner.3.9.0\tools\nunit3-console.exe --x86 --labels=All "--result=TestResult.xml;format=nunit2" `
	"Ifs.Uma.System.Prepare.Test\bin\Release\Ifs.Uma.System.Prepare.Test.dll"

if ($LastExitCode -ne 0) { Exit $LastExitCode }

Pop-Location

Write-Output "============ End - Prepare Test Data"

