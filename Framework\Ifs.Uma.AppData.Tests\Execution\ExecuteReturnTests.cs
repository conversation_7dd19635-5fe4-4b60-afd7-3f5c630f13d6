﻿using System.Collections.Generic;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Tests;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution
{
    [TestFixture]
    public class ExecuteReturnTests : FrameworkTest
    {
        [Test]
        public void ReturnVariable()
        {
            var vars = new Dictionary<string, object>();
            vars["Var1"] = 3;

            var args = new CpiReturnCallArgs();

            args.Name = "Var1";

            object result = DoReturn(args, vars);
            Assert.AreEqual(3, result);
        }

        [Test]
        public void ReturnLiteral()
        {
            var vars = new Dictionary<string, object>();

            var args = new CpiReturnCallArgs();

            args.Value = 3;

            object result = DoReturn(args, vars);
            Assert.AreEqual(3, result);
        }

        [Test]
        public void ReturnExpression()
        {
            var vars = new Dictionary<string, object>();
            vars["Var1"] = 3;

            var args = new CpiReturnCallArgs();

            JObject exp = JObject.Parse(@"{'var':'Var1'}");
            args.Return = new CpiExpression { JsonLogic = exp };

            object result = DoReturn(args, vars);
            Assert.AreEqual(3, result);
        }

        [Test]
        public void Exit()
        {
            var vars = new Dictionary<string, object>();

            var args = new CpiExitCallArgs();

            args.Return = "CANCEL";

            object result = DoExit(args, vars);
            Assert.AreEqual("CANCEL", result);
        }

        private object DoReturn(CpiReturnCallArgs args, Dictionary<string, object> vars)
        {
            TestExecutor executor = Resolve<TestExecutor>();

            ExecuteResult result = executor.Call(TestOfflineProjection, CpiExecuteCallMethod.Return, args, vars);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return result.Value;
        }

        private object DoExit(CpiExitCallArgs args, Dictionary<string, object> vars)
        {
            TestExecutor executor = Resolve<TestExecutor>();

            ExecuteResult result = executor.Call(TestOfflineProjection, CpiExecuteCallMethod.Exit, args, vars);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Failed);

            return result.Value;
        }
    }
}
