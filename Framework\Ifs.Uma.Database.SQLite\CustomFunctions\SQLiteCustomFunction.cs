﻿using System;
using System.Collections.Generic;
using SQLitePCL;

namespace Ifs.Uma.Database.SQLite.CustomFunctions
{
    internal abstract class SQLiteCustomFunction
    {
        public static IReadOnlyCollection<SQLiteCustomFunction> CreateFunctions()
        {
            List<SQLiteCustomFunction> functions = new List<SQLiteCustomFunction>();
            functions.Add(new DateTimeAddYears());
            functions.Add(new DateTimeAddMonths());
            functions.Add(new DateTimeAddDays());
            functions.Add(new DateTimeAddHours());
            functions.Add(new DateTimeAddMinutes());
            functions.Add(new DateTimeAddSeconds());
            functions.Add(new DateTimeDate());
            functions.Add(new DateTimeTime());
            functions.Add(new DateTimeFormat());
            return functions;
        }
        
        public string Name { get; }
        public bool Deterministic { get; }
        public int ArgCount { get; }

        public SQLiteCustomFunction(string name, bool deterministic, int argCount)
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));

            Name = name;
            Deterministic = deterministic;
            ArgCount = argCount;
        }

        internal int Register(sqlite3 db)
        {
            int flags = Deterministic ? raw.SQLITE_DETERMINISTIC : 0;
            return raw.sqlite3_create_function(db, Name, ArgCount, flags, null, Execute);
        }

        private void Execute(sqlite3_context ctx, object user_data, sqlite3_value[] args)
        {
            try
            {
                OnExecute(ctx, args);
            }
            catch (Exception ex)
            {
                raw.sqlite3_result_error(ctx, ex.Message);
            }
        }

        protected abstract void OnExecute(sqlite3_context ctx, sqlite3_value[] args);
    }
}
