﻿using Ifs.Uma.Data;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using IQToolkit.Data.Common;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using ActualQueryCache = IQToolkit.QueryCache;

namespace Ifs.Uma.Data.Tests
{
    [TestFixture]
    public class QueryCacheTests : DataContextTest<DataContext>
    {
        [Test]
        public void CheckAlteredSubQueryIsNewQuery()
        {
            ActualQueryCache queryCache = ActualQueryCache.Instance;

            Expression a = CreateExpression(null);

            object[] args;
            queryCache.Find(a, true, out args);
            
            Expression b = CreateExpression("cat");

            bool found = queryCache.Find(b, false, out args) != null;

            Assert.IsFalse(found);
            Assert.IsTrue(args.Length == 2);
            Assert.AreEqual(args[1], "cat");

            queryCache.Find(b, true, out args);
            
            Expression c = CreateExpression("dog");

            found = queryCache.Find(c, false, out args) != null;

            Assert.IsTrue(found);
            Assert.IsTrue(args.Length == 2);
            Assert.AreEqual(args[1], "dog");
        }

        private Expression CreateExpression(string table)
        {
            DataContext dc = CreateDataContext();

            var tc = dc.TransitionRows.Where(x => x.Operation == OperationType.Update);

            if (!string.IsNullOrEmpty(table))
            {
                tc = tc.Where(x => x.TableName == table);
            }

            var transitionIds = tc.Select(x => x.TransitionId).Distinct();
            var t = dc.TransitionChanges.Where(x => transitionIds.Contains(x.TransitionRowId));
            return t.Expression;
        }
    }
}
