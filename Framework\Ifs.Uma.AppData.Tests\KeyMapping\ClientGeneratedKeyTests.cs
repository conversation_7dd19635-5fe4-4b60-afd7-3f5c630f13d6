﻿using NUnit.Framework;
using NUnit.Framework.Internal;

namespace Ifs.Uma.AppData.Tests.KeyMapping
{
    [TestFixture]
    public class ClientGeneratedKeyTests : DataContextTest<TestDataContext>
    {
        [Test]
        public void CreateFromEmpty()
        {
            TestDataContext ctx = CreateDataContext();

            CityRow city = new CityRow();

            string id1 = ctx.GenerateClientKey<CityRow>(nameof(city.CityId)) as string;
            Assert.AreEqual(id1, "-1");

            string id2 = ctx.GenerateClientKey<CityRow>(nameof(city.CityId)) as string;
            Assert.AreEqual(id2, "-2", "Should have remembered -1 has been used, so generates -2");
        }

        [Test]
        public void CreateFromExisting()
        {
            TestDataContext ctx = CreateDataContext();

            CityRow city1 = new CityRow();
            city1.CityId = "-1";
            city1.CityName = "Gotham City";
            ctx.Cities.InsertOnSubmit(city1);
            ctx.SubmitChanges(false);

            string id2 = ctx.GenerateClientKey<CityRow>(nameof(city1.CityId)) as string;
            Assert.AreEqual(id2, "-2", "Should find -1 taken and so generate -2");

            CityRow city3 = new CityRow();
            city3.CityId = "-3";
            city3.CityName = "Metropolis";
            ctx.Cities.InsertOnSubmit(city3);
            ctx.SubmitChanges(false);

            string id4 = ctx.GenerateClientKey<CityRow>(nameof(city1.CityId)) as string;
            Assert.AreEqual(id4, "-4", "Should remember -2 has been used, find -3 taken and so generate -4 instead");

            CityRow city5 = new CityRow();
            city5.CityId = "-10";
            city5.CityName = "Quahog";
            ctx.Cities.InsertOnSubmit(city5);
            ctx.SubmitChanges(false);

            string id6 = ctx.GenerateClientKey<CityRow>(nameof(city1.CityId)) as string;
            Assert.AreEqual(id6, "-5", "Should remember -4 has been used so generate -5 instead. Existing row with -10 should have no effect");
        }

        [Test]
        public void Replace()
        {
            TestDataContext ctx = CreateDataContext();

            CityRow city1 = new CityRow();
            city1.CityId = "-1";
            city1.CityName = "Ankh-Morpork";
            ctx.Cities.InsertOnSubmit(city1);
            ctx.SubmitChanges(false);

            string id2 = ctx.GenerateClientKey<CityRow>(nameof(city1.CityId)) as string;
            Assert.AreEqual(id2, "-2", "Should find -1 taken, so generate -2");

            ctx.Cities.DeleteOnSubmit(city1);
            ctx.SubmitChanges(false);

            string id3 = ctx.GenerateClientKey<CityRow>(nameof(city1.CityId)) as string;
            Assert.AreEqual(id3, "-3", "Regardless of -1 being deleted, should remember -2 has been used and generate -3");
        }
    }
}
