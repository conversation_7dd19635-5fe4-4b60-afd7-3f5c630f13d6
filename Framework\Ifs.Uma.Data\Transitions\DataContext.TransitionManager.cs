﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Ifs.Uma.Data.Transitions;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Data
{
    public partial class DataContext
    {
        internal override void InsertTransitions(DbCommand command, DateTime transitionStart,
            IEnumerable<TrackedRow> transitionData)
        {
            Transition transition = new Transition() { Timestamp = transitionStart };
            DbRowHandler<Transition>.Create(command, Model, Builder).InsertRow(transition);
            IEnumerable<TransitionRowField> fields = PrepareForTransitionRowCreation(command, transitionData, transition, transitionStart);
            DbRowHandler<TransitionRowField>.Create(command, Model, Builder).InsertAll(fields, true);
        }

        private IEnumerable<TransitionRowField> PrepareForTransitionRowCreation(DbCommand command, IEnumerable<TrackedRow> transitionData, Transition transition, DateTime transitionStart)
        {
            // This method decides the TransitionRowField creation based on Non-Lob and Lob Attributes, in Update, Insert and Delete Entity TransitionData
            IDbRowHandler<TransitionRow> rowHandler = DbRowHandler<TransitionRow>.Create(command, Model, Builder);
            List<TransitionRowField> fields = new List<TransitionRowField>();

            foreach (TrackedRow data in transitionData)
            {
                if (data.Action == SubmitAction.Update)
                {
                    // For Entity Update - IMetaDataMember DataMembers in TransitionData are considered 
                    Tuple<List<IMetaDataMember>, List<IMetaDataMember>> modifiedDataMembers = GetModifiedDataMembers(data.ModifiedMembers);

                    List<IMetaDataMember> modifiedLobDataMembers = modifiedDataMembers.Item1;           // Binary and LongText ModifiedDataMembers
                    List<IMetaDataMember> modifiedNonLobDataMembers = modifiedDataMembers.Item2;        // Other ModifiedDataMembers (Excluding - Binary and LongText) and (Including - ObjId, ObjVersion and ServerPrimaryKeys)

                    if (modifiedNonLobDataMembers.Any())
                    {
                        // If there are Non-Lob ModifiedDataMembers, those will be updated with the already created TransitionRow in InsertTransitions()
                        fields.AddRange(CreateNonLobTransitionRowFields(data, transition.TransitionId, rowHandler, modifiedNonLobDataMembers));
                    }

                    if (modifiedLobDataMembers.Any())
                    {
                        // If there are Lob ModifiedDataMembers, separate new TransitionRow is created for each Lob ModifiedDataMember
                        foreach (IMetaDataMember modifiedLobDataMember in modifiedLobDataMembers)
                        {
                            Transition lobTransition = new Transition() { Timestamp = transitionStart };
                            DbRowHandler<Transition>.Create(command, Model, Builder).InsertRow(lobTransition);
                            IEnumerable<TransitionRowField> lobTransitionFields = CreateLobTransitionRowFields(data, lobTransition.TransitionId, rowHandler, modifiedLobDataMember);
                            DbRowHandler<TransitionRowField>.Create(command, Model, Builder).InsertAll(lobTransitionFields, true);
                        }
                    }
                }
                else if (data.Action == SubmitAction.Insert)
                {
                    // For Entity Insert - IMetaDataMember DataMembers in TransitionData are considered 
                    Tuple<List<IMetaDataMember>, List<IMetaDataMember>> newDataMembers = GetDataMembers(data.Table.DataMembers);

                    List<IMetaDataMember> keyMembers = newDataMembers.Item1;                                                    // ServerPrimaryKey, ObjId, ObjVersion - Key IMetaDataMember DataMembers
                    List<IMetaDataMember> lobMembers = newDataMembers.Item2;                                                    // Binary and LongText IMetaDataMember DataMembers
                    List<IMetaDataMember> dataMembersWithKeyMembers = (data.Table.DataMembers.Except(lobMembers)).ToList();     // Other IMetaDataMember DataMembers (Excluding - Binary and LongText) and (Including - ObjId, ObjVersion and ServerPrimaryKeys)

                    if (dataMembersWithKeyMembers.Except(keyMembers).Any())
                    {
                        // If there are Non-Lob DataMembers, those will be inserted with the already created TransitionRow in InsertTransitions()
                        fields.AddRange(CreateNonLobTransitionRowFields(data, transition.TransitionId, rowHandler, dataMembersWithKeyMembers));
                    }

                    if (lobMembers.Any())
                    {
                        // If there are Lob DataMembers, separate new TransitionRow is created for each Lob DataMember
                        foreach (IMetaDataMember lobMember in lobMembers)
                        {
                            RowBase row = (RowBase)data.Current;
                            object value = lobMember.GetValue(row);

                            // New TransitionRow is created only for not-null Lob values. Since DataMembers are considered here instead of ModifiedDataMembers
                            if (value != null)
                            {
                                Transition lobTransition = new Transition() { Timestamp = transitionStart };
                                DbRowHandler<Transition>.Create(command, Model, Builder).InsertRow(lobTransition);
                                IEnumerable<TransitionRowField> lobTransitionFields = CreateLobTransitionRowFields(data, lobTransition.TransitionId, rowHandler, lobMember);
                                DbRowHandler<TransitionRowField>.Create(command, Model, Builder).InsertAll(lobTransitionFields, true);
                            }
                        }
                    }
                }
                else if (data.Action == SubmitAction.Delete)
                {
                    // For Entity Delete - Key IMetaDataMember DataMembers in TransitionData are considered
                    Tuple<List<IMetaDataMember>, List<IMetaDataMember>> dataMembers = GetDataMembers(data.Table.DataMembers);

                    List<IMetaDataMember> keyMembers = dataMembers.Item1;  // ServerPrimaryKey, ObjId, ObjVersion - Key IMetaDataMember DataMembers  

                    // Key IMetaDataMember DataMembers will be inserted with the already created TransitionRow in InsertTransitions()
                    fields.AddRange(CreateNonLobTransitionRowFields(data, transition.TransitionId, rowHandler, keyMembers));
                }
            }

            InsertPerformances(transition.TransitionId, rowHandler, fields);
            return fields.AsEnumerable();
        }

        private IEnumerable<TransitionRowField> CreateLobTransitionRowFields(TrackedRow data, long transitionId, IDbRowHandler<TransitionRow> rowHandler, IMetaDataMember lobDataMember)
        {
            // This method returns TransitionRowFields created for Lob DataMembers to Update
            TransitionRow transitionRow = CreateTransitionRow(data, transitionId);
            rowHandler.InsertRow(transitionRow);

            RowBase row = (RowBase)data.Current;
            IEnumerable<IMetaDataMember> key = data.Table.FirstUniqueIndexColumns();

            ICollection<TransitionRowField> fields = new List<TransitionRowField>();
            foreach (IMetaDataMember member in data.Table.DataMembers)
            {
                if (!member.AutoIncrement && member.Sync != SyncRule.Never) //AutoIncrement trumps SyncRule.Always
                {
                    // Logic here is to add Key Members and updatable Lob DataMember to create TransitionRowFields
                    bool alwaysSync = member.Sync == SyncRule.Always;
                    if (alwaysSync || (key != null && key.Contains(member)) || (lobDataMember == member))
                    {
                        object value = member.GetValue(row);

                        if (member.IsBinary || member.IsLongText)
                        {
                            // There could be both null and not-null updates for Lob TransitionRow
                            TransitionRowField transitionRowField = CreateTransitionRowField(transitionRow.RowId, member.ColumnName);

                            if (member.IsBinary)
                            {
                                transitionRowField.DataType = "Binary";
                            }
                            else if (member.IsLongText)
                            {
                                transitionRowField.DataType = "LongText";
                            }

                            transitionRowField.NewValue = ToByteArray(value);
                            fields.Add(transitionRowField);
                        }
                        else if (alwaysSync || value != null)
                        {
                            // Key fields are needed to update the Lob TransitionRow
                            TransitionRowField transitionRowField = CreateTransitionRowField(transitionRow.RowId, member.ColumnName);

                            // Check for DateTime ColumnTypes before adding the TransitionRowField value
                            if (member.ColumnType == typeof(DateTime?))
                            {
                                transitionRowField.NewValue = GetDateTimeNewValue(member, value);
                            }
                            else
                            {
                                transitionRowField.NewValue = ToByteArray(value);
                            }

                            fields.Add(transitionRowField);
                        }
                    }
                }
            }

            InsertPerformances(transitionId, rowHandler, fields);
            return fields.AsEnumerable();
        }

        private ICollection<TransitionRowField> CreateNonLobTransitionRowFields(TrackedRow data, long transitionId, IDbRowHandler<TransitionRow> rowHandler, List<IMetaDataMember> dataMembers)
        {
            // This method returns TransitionRowFields created for Non-Lob IMetaDataMember DataMembers
            TransitionRow transitionRow = CreateTransitionRow(data, transitionId);
            rowHandler.InsertRow(transitionRow);

            RowBase row = (RowBase)data.Current;

            ICollection<TransitionRowField> fields = new List<TransitionRowField>();
            foreach (IMetaDataMember member in data.Table.DataMembers)
            {
                if (!member.AutoIncrement && member.Sync != SyncRule.Never) //AutoIncrement trumps SyncRule.Always
                {
                    bool alwaysSync = member.Sync == SyncRule.Always;
                    if (alwaysSync || (dataMembers.Any(x => x.PropertyName == member.PropertyName)))
                    {
                        object value = member.GetValue(row);
                        if (alwaysSync || value != null || data.Action == SubmitAction.Update)
                        {
                            TransitionRowField transitionRowField = CreateTransitionRowField(transitionRow.RowId, member.ColumnName);

                            // Adding Prefix 'CfEnum_' in Custom Field values.
                            // Custom fields need to be handled differently in the JSON.
                            // Client should add the prefix to the CF values to get around OData limit which specifies Enum values can't start with a digit.
                            if (member.Enumeration != null && member.ColumnName.StartsWith("cf_"))
                            {
                                transitionRowField.NewValue = value != null ? ToByteArray("CfEnum_" + value) : ToByteArray(null);
                            }
                            else if (member.ColumnType == typeof(DateTime?)) // Check for DateTime ColumnTypes before adding the TransitionRowField value
                            {
                                transitionRowField.NewValue = GetDateTimeNewValue(member, value);
                            }
                            else
                            {
                                transitionRowField.NewValue = ToByteArray(value);
                            }

                            fields.Add(transitionRowField);
                        }
                    }
                }
            }

            InsertPerformances(transitionId, rowHandler, fields);
            return fields;
        }

        private void InsertPerformances(long transitionId, IDbRowHandler<TransitionRow> rowHandler, ICollection<TransitionRowField> fields)
        {
            foreach (Performance perform in Performances)
            {
                TransitionRow tr = new TransitionRow()
                {
                    TransitionId = transitionId,
                    SyncState = SyncState.Unsent,
                    Operation = OperationType.Perform,
                    ProjectionName = perform.ProjectionName,
                    TableName = perform.Method,
                    TransactionGroup = perform.TransactionGroup,
                    EntitySetName = perform.EntitySetName,
                    ArraySource = perform.ArraySourceName,
                    PrimaryKeyString = perform.PrimaryKeyString,
                    SessionId = perform.SessionId
                };

                rowHandler.InsertRow(tr);
                if (perform.Arguments != null)
                {
                    foreach (KeyValuePair<string, object> kvp in perform.Arguments)
                    {
                        TransitionRowField f = new TransitionRowField()
                        {
                            TransitionRowId = tr.RowId,
                            FieldName = kvp.Key,
                            DataType = perform.ParamDataTypes?.ContainsKey(kvp.Key) == true ? perform.ParamDataTypes[kvp.Key]?.ToString() : null
                        };

                        if (kvp.Value != null && f.DataType == "Date")
                        {
                            f.NewValue = ToByteArray(((DateTime)kvp.Value).Date.ToString(ObjectConverter.DateFormat));
                        }
                        else if (kvp.Value != null && f.DataType == "Time")
                        {
                            f.NewValue = ToByteArray(((DateTime)kvp.Value).ToString(ObjectConverter.TimeFormat));
                        }
                        else
                        {
                            f.NewValue = ToByteArray(kvp.Value);
                        }

                        fields.Add(f);
                    }
                }
            }
        }

        private Tuple<List<IMetaDataMember>, List<IMetaDataMember>> GetModifiedDataMembers(IEnumerable<ModifiedMemberInfo> modifiedDataMembers)
        {
            // This method returns Binary and LongText ModifiedDataMembers from all ModifiedDataMembers in an Update
            List<IMetaDataMember> modifiedLobDataMembers = new List<IMetaDataMember>(); //Item1
            List<IMetaDataMember> modifiedNonLobDataMembers = new List<IMetaDataMember>(); //Item2

            foreach (ModifiedMemberInfo dataMember in modifiedDataMembers)
            {
                if (dataMember.Member.IsBinary || dataMember.Member.IsLongText)
                {
                    modifiedLobDataMembers.Add(dataMember.Member);
                }
                else
                {
                    modifiedNonLobDataMembers.Add(dataMember.Member);
                }
            }

            return Tuple.Create(modifiedLobDataMembers, modifiedNonLobDataMembers);
        }

        private Tuple<List<IMetaDataMember>, List<IMetaDataMember>> GetDataMembers(IEnumerable<IMetaDataMember> dataMembers)
        {
            // This method returns Binary and LongText ModifiedDataMembers from all ModifiedDataMembers in an Update
            List<IMetaDataMember> keyDataMembers = new List<IMetaDataMember>(); //Item1
            List<IMetaDataMember> newLobDataMembers = new List<IMetaDataMember>(); //Item2

            foreach (IMetaDataMember dataMember in dataMembers)
            {
                if (dataMember.ServerPrimaryKey || dataMember.PropertyName == nameof(RemoteRow.ObjId) || dataMember.PropertyName == nameof(RemoteRow.ObjVersion))
                {
                    keyDataMembers.Add(dataMember);
                }
                else if (dataMember.IsBinary || dataMember.IsLongText)
                {
                    newLobDataMembers.Add(dataMember);
                }
            }

            return Tuple.Create(keyDataMembers, newLobDataMembers);
        }

        private TransitionRow CreateTransitionRow(TrackedRow data, long transitionId)
        {
            // This method returns a new TransitionRow for each Non-Lob Update, Non-Lob Insert, Lob Update or Lob Insert
            RowBase row = (RowBase)data.Current;
            RemoteRow remoteRow = row as RemoteRow;
            SubmitAction action = data.Action;

            TransitionRow transitionRow = new TransitionRow()
            {
                TransitionId = transitionId,
                SyncState = SyncState.Unsent,
                ModifiedRowId = row.RowId,
                ProjectionName = data.ProjectionName,
                TableName = data.Table.TableName,
                EntitySetName = remoteRow.EntitySetName,
                ArraySource = remoteRow.ArraySourceName,
                PrimaryKeyString = remoteRow.PrimaryKeyString,
                TransactionGroup = GetTransactionGroup(data.Table, row),
                Operation = action.ToOperationType(),
                SessionId = remoteRow.SessionId
            };

            return transitionRow;
        }

        private TransitionRowField CreateTransitionRowField(long rowId, string columnName)
        {
            // This method returns a new TransitionRowField for each DataMember attribute in Non-Lob/Lob, Insert, Update or  Delete
            TransitionRowField transitionRowField = new TransitionRowField()
            {
                TransitionRowId = rowId,
                FieldName = columnName,
            };

            return transitionRowField;
        }

        private byte[] GetDateTimeNewValue(IMetaDataMember member, object value)
        {
            // This method returns TransitionRowField value for member.ColumnType either Date or Time
            byte[] newValue = ToByteArray(value);
            if (value != null)
            {
                if (member.DateFormat == DateFormats.Date)
                {
                    newValue = ToByteArray(((DateTime)value).Date.ToString(ObjectConverter.DateFormat));
                }
                else if (member.DateFormat == DateFormats.Time)
                {
                    newValue = ToByteArray(((DateTime)value).ToString(ObjectConverter.TimeFormat));
                }
            }

            return newValue;
        }

        private byte[] ToByteArray(object value)
        {
            if (value != null)
            {
                Type valueType = value.GetType();
                if (valueType.GetTypeInfo().IsEnum)
                {
                    // convert an Enumeration to its ServerValue string
                    // it will serialise much shorter
                    // and we won't need to convert it when we send the transition
                    value = GetEnumeration(valueType).ServerValue(value);
                }
            }
            return BinarySerializerHelper.ObjectToByteArray(value);
        }

        protected virtual string GetTransactionGroup(IMetaTable table, object row)
        {
            return table.GetTransactionGroup(row);
        }

        public void InitializeAppTables(DbCommand command)
        {
            ClearTransitions(command);
            DropAppsTables(command);
            RecreateAppsTables(command);
        }

        public void ClearTransitions(DbCommand command)
        {
            ClearTable<TransitionRowField>(command);
            ClearTable<TransitionRow>(command);
            ClearTable<Transition>(command);
        }
    }
}
