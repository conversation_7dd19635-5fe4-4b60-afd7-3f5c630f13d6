﻿using System;
using System.Linq.Expressions;
using Ifs.Uma.Database;

namespace Ifs.Uma.AppData.Expressions
{
    public sealed class SortExpression : IfsExpression
    {
        public override IfsExpressionType IfsNodeType => IfsExpressionType.Sort;

        public Expression Expression { get; }
        public ESortOrder SortOrder { get; }
       
        internal SortExpression(Expression expression, ESortOrder sortOrder)
        {
            Expression = expression ?? throw new ArgumentNullException(nameof(expression));
            SortOrder = sortOrder;
        }

        protected override Expression Accept(IfsExpressionVisitor visitor)
        {
            return visitor.VisitSortExpression(this);
        }
         
        public override string ToString()
        {
            return Expression + " " + SortOrder.ToString();
        }

        public SortExpression Update(Expression expression, ESortOrder sortOrder)
        {
            if (Expression == expression &&
                SortOrder == sortOrder)
            {
                return this;
            }

            return new SortExpression(expression, sortOrder);
        }
    }

    public partial class IfsExpression
    {
        public static SortExpression QuerySort(string attributePath, ESortOrder sortOrder)
        {
            return new SortExpression(VarAccess(attributePath), sortOrder);
        }

        public static SortExpression QuerySort(Expression expression, ESortOrder sortOrder)
        {
            return new SortExpression(expression, sortOrder);
        }
    }

    public partial class IfsExpressionVisitor
    {
        protected internal virtual Expression VisitSortExpression(SortExpression exp)
        {
            Expression expression = Visit(exp.Expression);
            return exp.Update(expression, exp.SortOrder);
        }
    }
}
