﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.AppData.Attachments.Documents;
using Ifs.Uma.AppData.Attachments.Media;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.AppData.Formatters;
using Ifs.Uma.AppData.StringExpressions;
using Ifs.Uma.Database;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Services.Parameters;
using Ifs.Uma.UI.Navigation;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.UI.Services;
using Ifs.Uma.Utility;
using Ifs.Uma.Utility.Formatters;

namespace Ifs.Uma.Framework.UI.Cards
{
    public delegate object CardValueProvider(AttributePath attributePath, object datacontext);

    public interface ICardDefCreator
    {
        CardDef CreateCardDef(string projectionName, CpiSelector selector, CardValueProvider valueProvider);
        CardDef CreateCardDef(string projectionName, CpiList list, CardValueProvider valueProvider, ICommandExecutor commandExecutor, bool unlimitedFields);
        CardDef CreateCardDef(string projectionName, CpiCard card, CpiList relatedList, CardValueProvider valueProvider, ICommandExecutor commandExecutor);
    }

    public sealed class CardDefCreator : ICardDefCreator
    {
        private readonly IMetadata _metadata;
        private readonly IExpressionRunner _expressionRunner;
        private readonly IMediaHandler _mediaHandler;
        private readonly IDataHandler _dataHandler;
        private readonly ILogger _logger;
        private readonly IDocumentHandler _documentHandler;
        private readonly INavigator _navigator;
        private readonly IDialogService _dialogService;
        private readonly IAppParameters _appParameters;

        public CardDefCreator(IMetadata metadata, IExpressionRunner expressionRunner, IMediaHandler mediaHandler, IDataHandler dataHandler,
            ILogger logger, IDocumentHandler documentHandler, INavigator navigator, IDialogService dialogService, IAppParameters appParameters)
        {
            _metadata = metadata;
            _expressionRunner = expressionRunner;
            _mediaHandler = mediaHandler;
            _dataHandler = dataHandler;
            _logger = logger;
            _documentHandler = documentHandler;
            _navigator = navigator;
            _dialogService = dialogService;
            _appParameters = appParameters;
        }

        private CardDef NewCardDef(string projectionName, string cardName, ICommandExecutor commandExecutor, CpiAttachments attachmentsConfig)
        {
            CardDef cardDef = new CardDef(_metadata, _expressionRunner, _mediaHandler, _documentHandler, _navigator, _logger, _dialogService);
            cardDef.Load(projectionName, cardName, commandExecutor, attachmentsConfig);
            return cardDef;
        }

        private CardDefItem NewCardDefItem()
        {
            return new CardDefItem(_metadata, _expressionRunner, _mediaHandler, _dataHandler, _logger, _appParameters);
        }

        public CardDef CreateCardDef(string projectionName, CpiCard card, CpiList relatedList, CardValueProvider valueProvider, ICommandExecutor commandExecutor)
        {
            if (card == null)
                throw new ArgumentNullException(nameof(card));

            CardDef cardDef = NewCardDef(projectionName, card.Name, commandExecutor, relatedList?.Attachments);

            CardDefItem labelItem = CardLabelToCardDefitem(projectionName, card);
            if (labelItem != null)
            {
                cardDef.Items.Add(labelItem);
            }

            if (card.Content != null)
            {
                foreach (CpiCardContent cardContent in card.Content)
                {
                    CardDefItem contentItem = CardContentToCardDefItem(projectionName, cardContent, valueProvider);
                    if (contentItem != null)
                    {
                        cardDef.Items.Add(contentItem);
                    }
                }
            }

            List<CpiCommandGroup> commandGroups = new List<CpiCommandGroup>();

            if (relatedList?.CommandGroups != null)
            {
                commandGroups.AddRange(relatedList.CommandGroups);
            }

            if (card.CommandGroups != null)
            {
                commandGroups.AddRange(card.CommandGroups);
            }

            cardDef.CommandGroups = commandGroups.ToArray();
            cardDef.FluidLayout = card.MobileFluidLayout ?? false;

            return cardDef;
        }

        private static CpiFieldContent[] OrderByRank(CpiFieldContent[] content, string[] fieldRanking)
        {
            if (fieldRanking == null)
            {
                return content;
            }

            int GetRanking(CpiFieldContent field)
            {
                int index = field.Field == null ? -1 : Array.IndexOf(fieldRanking, field.Field?.Attribute);
                return index < 0 ? fieldRanking.Length : index;
            }

            return content.OrderBy(GetRanking).ToArray();
        }

        public CardDef CreateCardDef(string projectionName, CpiSelector selector, CardValueProvider valueProvider)
        {
            if (selector == null)
                throw new ArgumentNullException(nameof(selector));

            CardDef cardDef = NewCardDef(projectionName, selector.Name, new DoNothingCommandExecutor(), null);

            foreach (CpiFieldContent content in selector.Content)
            {
                CardDefItem contentItem = FieldContentToCardDefItem(projectionName, content, valueProvider);
                if (contentItem != null)
                {
                    cardDef.Items.Add(contentItem);
                }
            }

            return cardDef;
        }

        private static CpiFieldContent[] SimplifyListContent(CpiFieldContent[] content, string[] fieldRanking, bool unlimitedFields)
        {
            int imageCount = 1;
            int textCount = 4;
            int otherCount = 2;

            List<CpiFieldContent> selectedItems = new List<CpiFieldContent>();
            foreach (CpiFieldContent field in OrderByRank(content, fieldRanking))
            {
                if (field.Field == null)
                {
                    continue;
                }

                if (field.Field.Control == CpiControlType.Image)
                {
                    if (unlimitedFields || imageCount > 0)
                    {
                        imageCount--;
                        selectedItems.Add(field);
                    }
                }
                else if (field.Field.Datatype == CpiDataType.Text)
                {
                    if (unlimitedFields || textCount > 0)
                    {
                        textCount--;
                        selectedItems.Add(field);
                    }
                }
                else
                {
                    if (unlimitedFields || otherCount > 0)
                    {
                        otherCount--;
                        selectedItems.Add(field);
                    }
                }
            }

            // fieldRanking is used to choose which fields to display
            // we still want the fields to appear in the existing order though
            return selectedItems.OrderBy(x => Array.IndexOf(content, x)).ToArray();
        }

        public CardDef CreateCardDef(string projectionName, CpiList list, CardValueProvider valueProvider, ICommandExecutor commandExecutor, bool unlimitedFields)
        {
            if (list == null)
                throw new ArgumentNullException(nameof(list));

            CardDef cardDef = NewCardDef(projectionName, list.Name, commandExecutor, list.Attachments);

            if (list.Content != null)
            {
                bool foundHeader = false;
                foreach (CpiFieldContent content in SimplifyListContent(list.Content, list.FieldRanking, unlimitedFields))
                {
                    CardDefItem contentItem = FieldContentToCardDefItem(projectionName, content, valueProvider);
                    if (contentItem != null)
                    {
                        if (!foundHeader && content.Field.Datatype == CpiDataType.Text)
                        {
                            foundHeader = true;
                            contentItem.Id = CardDefItem.HeaderLabelId;
                        }

                        cardDef.Items.Add(contentItem);
                    }
                }
            }

            List<CpiCommandGroup> commandGroups = new List<CpiCommandGroup>();

            if (list.CommandGroups != null)
            {
                commandGroups.AddRange(list.CommandGroups);
            }

            cardDef.CommandGroups = commandGroups.ToArray();

            return cardDef;
        }

        private CardDefItem CardLabelToCardDefitem(string projectionName, CpiCard card)
        {
            if (!string.IsNullOrWhiteSpace(card.Label))
            {
                CardDefItem item = NewCardDefItem();
                item.ProjectionName = projectionName;
                item.Id = CardDefItem.HeaderLabelId;
                item.Converter = new LabelConvertor(_expressionRunner, _metadata);
                item.ConverterParameter = card.Label;
                return item;
            }

            return null;
        }

        private CardDefItem FieldContentToCardDefItem(string projectionName, CpiFieldContent fieldContent, CardValueProvider valueProvider)
        {
            if (fieldContent.Field != null)
            {
                return FieldToCardDefItem(projectionName, fieldContent.Field, valueProvider);
            }

            return null;
        }

        private CardDefItem CardContentToCardDefItem(string projectionName, CpiCardContent cardContent, CardValueProvider valueProvider)
        {
            if (cardContent.Field != null)
            {
                return FieldToCardDefItem(projectionName, cardContent.Field, valueProvider);
            }
            else if (cardContent.MarkdownText != null)
            {
                CardDefItem item = NewCardDefItem();
                item.MarkdownText = cardContent.MarkdownText;
                return item;
            }
            else if (cardContent.ImageViewer != null)
            {
                CpiImageViewer imageViewer = _metadata.FindImageViewer(projectionName, cardContent.ImageViewer);
                if (imageViewer != null && imageViewer.Media != null)
                {
                    CardDefItem item = NewCardDefItem();
                    item.ImageViewer = imageViewer;
                    item.Visibility = cardContent.Override?.OfflineVisible ?? cardContent.Override?.Visible;
                    return item;
                }
            }

            return null;
        }

        private CardDefItem FieldToCardDefItem(string projectionName, CpiField field, CardValueProvider valueProvider)
        {
            CardDefItem cardDefItem = NewCardDefItem();
            cardDefItem.ProjectionName = projectionName;
            cardDefItem.Id = field.Attribute;
            cardDefItem.Field = field;
            cardDefItem.ShowLabel = field.ShowLabel ?? true;

            if (cardDefItem.ControlType != CardDefItem.ControlTypes.Image)
            {
                cardDefItem.Converter = new ValueFormatterConvertor(AttributeFormatter.For(_metadata, projectionName, field));
            }

            AttributePath attributePath = AttributePath.Create(field.Attribute);

            if (attributePath != null)
            {
                cardDefItem.ValueProvider = x => valueProvider(attributePath, x);
            }

            if ((field.Datatype == CpiDataType.Timestamp || field.Datatype == CpiDataType.Date || field.Datatype == CpiDataType.Time) && _metadata.IsEntityKnownTimeZone(field.Entity, projectionName))
            {
                cardDefItem.TimeStampIsInUtc = true;
                string pathForPrimaryTimeZone = field.DisplayTimeZones?.FirstOrDefault()?.TimeZoneAttribute;
                if (pathForPrimaryTimeZone != null)
                {
                    AttributePath attributeForPrimaryTimeZone = AttributePath.Create(pathForPrimaryTimeZone);
                    if (attributeForPrimaryTimeZone != null)
                    {
                        cardDefItem.PrimaryTimeZoneProvider = x => valueProvider(attributeForPrimaryTimeZone, x);
                    }
                }

                if (field.DisplayTimeZones != null)
                {
                    foreach (string timeZone in field.DisplayTimeZones.Skip(1).Where(x => x.TimeZoneAttribute != null).Select(x => x.TimeZoneAttribute))
                    {
                        AttributePath path = AttributePath.Create(timeZone);
                        if (path != null)
                        {
                            cardDefItem.AdditionalTimeZoneProviders.Add(x => valueProvider(path, x));
                        }
                    }
                }
            }

            return cardDefItem;
        }

        private sealed class DoNothingCommandExecutor : ICommandExecutor
        {
            public PageBase Page { get; set; }

            public Task<ExecuteResult> ExecuteAsync(string projectionName, ViewData data, CpiCommand command, CommandOptions options = null)
            {
                return Task.FromResult(ExecuteResult.None);
            }

            public void GetStates(string projectionName, ViewData viewData, CpiCommand command, bool enabledOnEmpty, out bool isVisible, out bool isEnabled)
            {
                isVisible = false;
                isEnabled = false;
            }
        }

        private sealed class ValueFormatterConvertor : IBindValueConverter
        {
            private readonly IValueFormatter _formatter;

            public ValueFormatterConvertor(IValueFormatter formatter)
            {
                _formatter = formatter;
            }

            public object Convert(object value, Type targetType, object parameter, string language)
            {
                return _formatter.Format(value);
            }

            public object ConvertBack(object value, Type targetType, object parameter, string language)
            {
                throw new InvalidOperationException();
            }
        }

        internal sealed class LabelConvertor : IBindValueConverter
        {
            private readonly IExpressionRunner _expressionRunner;
            private readonly IMetadata _metadata;

            public LabelConvertor(IExpressionRunner expressionRunner, IMetadata metadata)
            {
                _expressionRunner = expressionRunner;
                _metadata = metadata;
            }

            public object Convert(object value, Type targetType, object parameter, string language)
            {
                if (value is ViewData viewData)
                {
                    bool timezoneAware = _metadata.IsEntityKnownTimeZone(RemoteNaming.ToEntityName(viewData.Record.EntityName), viewData.Record.ProjectionName);
                    IMetaTable metaTable = _metadata.MetaModel.GetTable(viewData.Record.GetRemoteRow().TableName);

                    return timezoneAware ? _expressionRunner.InterpolateString((string)parameter, viewData.Record, true, false, metaTable) : _expressionRunner.InterpolateString((string)parameter, viewData.Record);
                }

                if (value is IStringExpressionValueProvider vp)
                {
                    return _expressionRunner.InterpolateString((string)parameter, vp);
                }

                return value?.ToString();
            }

            public object ConvertBack(object value, Type targetType, object parameter, string language)
            {
                throw new InvalidOperationException();
            }
        }
    }
}
