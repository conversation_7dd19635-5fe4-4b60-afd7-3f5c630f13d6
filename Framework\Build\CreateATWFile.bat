@setlocal enableextensions enabledelayedexpansion
setlocal EnableDelayedExpansion
(set \n=^
%=This is Mandatory Space=%
)
@echo off

set FWBRANCH=%FW_BRANCH%
set TESTFILE=../TestResult.xml
set TEMPLATEFILE=ATW/Template.xml
set "OUTTEXTFILE=ATW/Result.xml"
set TOOLNAME=Fndmot Delivery Tests
set COMPONENT=FNDMOT
set COMMONTESTNAME=FNDMOT Test
break>"%OUTTEXTFILE%"
set "t=   " 

:generateResult
echo "START - Generating ATW result XML"
set /a totalErrors=0

echo ^<?xml version="1.0" encoding="utf-8"?^>>>"%OUTTEXTFILE%"
echo %t%^<Result tool="%TOOLNAME%" documentVersion="3.0"^>>>"%OUTTEXTFILE%"

CALL :generateResultSummary

IF %totalErrors% gtr 0 (CALL :generateResultDetails)

echo %t%^</Result^>>>"%OUTTEXTFILE%"
echo "END - Generating ATW result XML"

CALL :copyFileToAzure

EXIT /B 0

:: Write result summary to ATW file
:generateResultSummary
echo "Writing ATW result summary"
FOR /F "tokens=* USEBACKQ" %%F IN (`call Xpath.bat "%TESTFILE%" "//test-results[0]/@total"`) DO (
	SET total=%%F
)
FOR /F "tokens=* USEBACKQ" %%F IN (`call Xpath.bat "%TESTFILE%" "//test-results[0]/@failures"`) DO (
	SET failures=%%F
)
FOR /F "tokens=* USEBACKQ" %%F IN (`call Xpath.bat "%TESTFILE%" "//test-results[0]/@errors"`) DO (
	SET errors=%%F
)
FOR /F "tokens=* USEBACKQ" %%F IN (`call Xpath.bat "%TESTFILE%" "//test-results[0]/@skipped"`) DO (
	SET skipped=%%F
)
for /F "usebackq tokens=1,2 delims==" %%i in (`wmic os get LocalDateTime /VALUE 2^>NUL`) do if '.%%i.'=='.LocalDateTime.' set ldt=%%j
set currentDateTime=%ldt:~0,4%-%ldt:~4,2%-%ldt:~6,2% %ldt:~8,2%:%ldt:~10,2%:%ldt:~12,6%

set /a "totalErrors=%failures%+%errors%";
IF %totalErrors% gtr 0 (SET result=Failure) ELSE (SET result=Success)

echo %t%%t%^<ResultSummary^>>>"%OUTTEXTFILE%"
echo %t%%t%%t%^<UtcEndTime^>%currentDateTime%^</UtcEndTime^>>>"%OUTTEXTFILE%"
echo %t%%t%%t%^<ErrorCount^>%totalErrors%^</ErrorCount^>>>"%OUTTEXTFILE%"
echo %t%%t%%t%^<WarningCount^>%skipped%^</WarningCount^>>>"%OUTTEXTFILE%"
echo %t%%t%%t%^<InfoCount^>0^</InfoCount^>>>"%OUTTEXTFILE%"
echo %t%%t%%t%^<Status^>%result%^</Status^>>>"%OUTTEXTFILE%"
echo %t%%t%^</ResultSummary^>>>"%OUTTEXTFILE%"
EXIT /B 0

:: Write result details if there are any failures
:generateResultDetails
echo "Writing ATW result details"
echo %t%%t%^<ResultDetails^>>>"%OUTTEXTFILE%"

FOR /F "tokens=* USEBACKQ" %%F IN (`call Xpath.bat "%TESTFILE%" "//test-case[@result='Error' or @result='Failure']/@name"`) DO (
 SET desc=
 SET str=
 FOR /F "tokens=* USEBACKQ" %%D IN (`call Xpath.bat "%TESTFILE%" "//test-case[@name='%%F']/failure/message"`) DO (SET desc=%%D)
 FOR /F "tokens=* USEBACKQ" %%R IN (`call Xpath.bat "%TESTFILE%" "//test-case[@name='%%F']/failure/stack-trace"`) DO (
	IF "!str!" EQU "" (SET str=!str! %%R) else (SET str=!str!!\n!%%R)
 )
 IF NOT "!desc!" EQU "" (SET "desc=!desc:<=!")
 IF NOT "!desc!" EQU "" (SET "desc=!desc:>=!")
 
 IF NOT "!str!" EQU "" (SET "str=!str:<=!")
 IF NOT "!str!" EQU "" (SET "str=!str:>=!")
 
 echo %t%%t%%t%^<ResultDetail^>>>"%OUTTEXTFILE%"
 echo %t%%t%%t%%t%^<TestName^>%COMMONTESTNAME%^</TestName^>>>"%OUTTEXTFILE%"
 echo %t%%t%%t%%t%^<Component^>%COMPONENT%^</Component^>>>"%OUTTEXTFILE%"
 echo %t%%t%%t%%t%^<ResultType^>Error^</ResultType^> >>"%OUTTEXTFILE%"
 echo %t%%t%%t%%t%^<Summary^>>>"%OUTTEXTFILE%"
 echo %t%%t%%t%%t%^<ObjectName^>%%F^</ObjectName^>>>"%OUTTEXTFILE%"
 echo %t%%t%%t%%t%^<Description^>!desc!^</Description^>>>"%OUTTEXTFILE%"
 echo %t%%t%%t%%t%^<Stacktrace^>!str!^</Stacktrace^>>>"%OUTTEXTFILE%"
 echo %t%%t%%t%%t%^</Summary^>>>"%OUTTEXTFILE%"
 echo %t%%t%%t%^</ResultDetail^>>>"%OUTTEXTFILE%"
)
echo %t%%t%^</ResultDetails^>>>"%OUTTEXTFILE%"
EXIT /B 0

:: Copy fndmot atw result xml file to azure storage
:copyFileToAzure
echo "START - Copying ATW result XML to Azure storage"

if %FWBRANCH%=="fw-dev" (azcopy copy "%OUTTEXTFILE%" "https://ifsalebasereleasesa.file.core.windows.net/simpleshare/FndmotDeliveryTest_fwdev?sp=rcwdl&st=2020-12-24T04:41:54Z&se=2023-12-25T04:41:00Z&sv=2019-12-12&sig=cIp6YD0%2BSiX0gwEZLpRzXjaH35ESfB3JSim7MCjN1ng%3D&sr=s" --recursive=true)
if %FWBRANCH%=="fw-21r1" (azcopy copy "%OUTTEXTFILE%" "https://ifsalebasereleasesa.file.core.windows.net/simpleshare/FndmotDeliveryTest_fw21rr1?sp=rcwdl&st=2020-12-24T04:41:54Z&se=2023-12-25T04:41:00Z&sv=2019-12-12&sig=cIp6YD0%2BSiX0gwEZLpRzXjaH35ESfB3JSim7MCjN1ng%3D&sr=s" --recursive=true)
if %FWBRANCH%=="fw-noble" (azcopy copy "%OUTTEXTFILE%" "https://ifsalebasereleasesa.file.core.windows.net/simpleshare/FndmotDeliveryTest_noble?sp=rcwdl&st=2020-12-24T04:41:54Z&se=2023-12-25T04:41:00Z&sv=2019-12-12&sig=cIp6YD0%2BSiX0gwEZLpRzXjaH35ESfB3JSim7MCjN1ng%3D&sr=s" --recursive=true)

echo "END - Copying ATW result XML to Azure storage"
EXIT /B 0

endlocal