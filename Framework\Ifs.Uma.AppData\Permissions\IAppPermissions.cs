﻿using System.Collections.Generic;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Permissions
{
    public interface IAppPermissions
    {
        bool IsNavigatorEntryGranted(string projectionName, string navigatorEntryName);

        bool IsCommandGranted(string projectionName, CpiCommand command);

        bool IsEntityReadGranted(string projectionName, IEnumerable<string> entities);

        bool IsEntityWriteGranted(string projectionName, IEnumerable<string> entities);

        bool IsClientFeatureGranted(string featureName);
    }
}
