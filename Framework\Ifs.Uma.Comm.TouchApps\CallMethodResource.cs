﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.KeyMapping;
using Ifs.Uma.AppData.Messages;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Newtonsoft.Json.Linq;

namespace Ifs.Uma.Comm.TouchApps
{
    public enum MethodType
    {
        BoundFunction = 0,
        BoundAction = 1,
        Action = 2,
        Function = 3
    }

    internal abstract class CallMethodResource : AppResource, ICustomResourceSerializer
    {
        public override string ResourceName => GetResourceUrl();
        
        public int DeviceId { get; set; }
        public MethodType MethodType { get; set; }

        public string Projection { get; set; }
        public string PrimaryKeyString { get; set; }
        public string EntitySetName { get; set; }

        public string MethodName { get; set; }
        
        public JObject Data { get; set; }

        public CpiTypeInfo ReturnType { get; set; }

        public IMetadata Metadata { get; set; }

        public IClientKeysMapper ClientKeysMapper { get; set; }

        public string SerializeToJsonString()
        {
            if (MethodType != MethodType.Function)
            {
                return MessageUtils.JObjectToString(Data);
            }
            return string.Empty;
        }

        public virtual object DeserializeJsonString(string jsonString)
        {
            JObject jObj = JObject.Parse(jsonString);

            if (jObj != null)
            {
                JArray jArray = jObj.GetValue("value") as JArray;
                object result;
                if (jArray != null)
                {
                    result = MessageUtils.ParseToken(Metadata, ClientKeysMapper, ReturnType, jArray);
                    return new ExecuteResult(result);
                }
                else
                {
                    try
                    {
                        result = MessageUtils.ParseToken(Metadata, ClientKeysMapper, ReturnType, jObj);
                    }
                    catch (Exception)
                    {
                        //TO-DO:This can be removed after server supports all data structures
                        result = jObj.GetValue("value") as object;
                    }

                    return new ExecuteResult(result);
                }
            }

            return ExecuteResult.None;
        }

        public object EmptyResponseHandling(bool isSuccess)
        {
            return isSuccess ? ExecuteResult.True : ExecuteResult.False; 
        }

        private string GetResourceUrl()
        {
            if (MethodType == MethodType.BoundAction || MethodType == MethodType.BoundFunction || MethodType == MethodType.Function)
            {
                string url = Projection + ".svc";
                if (!string.IsNullOrEmpty(EntitySetName))
                {
                    url += "\\" + EntitySetName;
                }

                if (!string.IsNullOrEmpty(PrimaryKeyString))
                {
                    PrimaryKeyString = PrimaryKeyString.Replace("^", ",");
                    PrimaryKeyString = PrimaryKeyString.Replace("/", "%2F");
                    PrimaryKeyString = PrimaryKeyString.Remove(PrimaryKeyString.Length - 1);
                    PrimaryKeyString = PrimaryKeyString.Replace("\'", string.Empty);
                    PrimaryKeyString = PrimaryKeyString.UnderscoreToCamelCase();
                    PrimaryKeyString = RemoteNaming.ToServerColumnName(PrimaryKeyString);

                    if (!string.IsNullOrEmpty(PrimaryKeyString))
                    {
                        url = url + "(" + PrimaryKeyString + ")";
                    }
                }

                if (!string.IsNullOrEmpty(MethodName))
                {
                    if (MethodType == MethodType.Function)
                    {
                        url = url + "/" + MethodName;
                    }
                    else
                    {
                        url = url + "/" + "IfsApp." + Projection + "." + MethodName;
                    }
                }
                
                if (MethodType == MethodType.BoundFunction || MethodType == MethodType.Function)
                {
                    url = url + "(" + GetData() + ")";
                }

                return url;
            }
            else
            {
                string url = Projection + ".svc\\" + MethodName;
                return url;
            }
        }

        private string GetData()
        {
            string dataString = string.Empty;
            Dictionary<string, string> values = Data.ToObject<Dictionary<string, string>>();
            CpiFunction function = Metadata.FindFunction(Projection, MethodName);
            int i = 0;
            foreach (KeyValuePair<string, string> s in values)
            {
                CpiParam param = function?.Parameters.Where(x => x.Name == s.Key).FirstOrDefault();
                bool isParamDateType = param?.DataType.ToType() == typeof(DateTime);
                string paramValue = s.Value;

                if (isParamDateType)
                {
                    paramValue = GetDateString(s.Value, param.DataType);
                }

                if (param?.DataType != CpiDataType.Number && !isParamDateType)
                {
                    paramValue = "'" + s.Value + "'";
                }

                if (param?.DataType == CpiDataType.Number && paramValue == null)
                {
                    paramValue = "null";
                }
  
                dataString += s.Key + "=" + paramValue;
                if (i != values.Count - 1)
                {
                    dataString += ",";
                }

                i++;
            }

            return dataString;
        }

        protected string GetDateString(string value, CpiDataType type)
        {
            string[] allowedFormats = { "G", "s", "o" };
            if (DateTime.TryParseExact(value, allowedFormats, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
            {
                // If the parse succeeded, return the DateTime object's ToString() representation in the desired format.
                // please refer to the below link before changing the formats.
                //https://www.odata.org/documentation/odata-version-3-0/url-conventions/
                switch (type)
                {
                    case CpiDataType.Date:
                        return date.ToString(ObjectConverter.DateFormat, CultureInfo.InvariantCulture);
                    case CpiDataType.TimestampUtc:
                    case CpiDataType.Timestamp:
                        return date.ToString(ObjectConverter.DateTimeZoneFormat, CultureInfo.InvariantCulture);
                    case CpiDataType.Time:
                        return date.ToString(ObjectConverter.TimeFormat, CultureInfo.InvariantCulture);
                    default:
                        Logger.Current.Warning("Failed to convert the given value {0} into type {1}.", value, type.ToString());
                        return value;
                }
            }
            else
            {
                Logger.Current.Warning("Failed to convert the given value {0} into a date.", value);
                return value;
            }
        }
    }
}
