﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Execution;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.AppData.Model;
using NUnit.Framework;

namespace Ifs.Uma.AppData.Tests.Execution.Procedures.Functions.String
{
    [TestFixture]
    public class StringRegexCountTests : ProcedureTest
    {
        private Dictionary<string, object> _params;
        private IProcedureExecutor _executor;

        [OneTimeSetUp]
        public void Initialize()
        {
            _params = new Dictionary<string, object>();
        }

        protected override void BeforeTest()
        {
            base.BeforeTest();
            PrepareDatabase<FwDataContext>("Execution.Procedures.Functions.String.StringRegexCountTestsSchema", null);
            _executor = Resolve<IProcedureExecutor>();
        }

        [Test]
        [TestCase("a,b,c", "[^,]+", ExpectedResult = 3)]
        [TestCase("a,b,c", null, ExpectedResult = null)]
        [TestCase("a,b,c", "", ExpectedResult = null)]
        [TestCase(null, null, ExpectedResult = null)]
        public async Task<long?> String_RegexCount(object input, string regexPattern)
        {
            _params["TextInput"] = input;
            _params["RegexPattern"] = regexPattern;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexCount", _params);
            CheckResult(result);

            return result?.Value as long?;
        }

        [Test]
        [TestCase("a,b,c", "[^,]+", 2, ExpectedResult = 2)]
        [TestCase("a,b,c", "[^,]+", 6, ExpectedResult = 0)]
        [TestCase("a,b,c", "[^,]+", null, ExpectedResult = 3)]
        [TestCase(null, null, null, ExpectedResult = null)]
        public async Task<long?> String_RegexCount3(object input, string regexPattern, int position)
        {
            _params["TextInput"] = input;
            _params["RegexPattern"] = regexPattern;
            _params["Position"] = position;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexCount3", _params);
            CheckResult(result);

            return result?.Value as long?;
        }

        [Test]
        [TestCase("a,B,c", "[b]", 2, "in", ExpectedResult = 1)]
        [TestCase("a,B,c", "[b]", 2, null, ExpectedResult = 0)]
        [TestCase("a,B,c", "[B]", 2, null, ExpectedResult = 1)]
        [TestCase(null, null, null, null, ExpectedResult = null)]
        public async Task<long?> String_RegexCount4(object input, string regexPattern, int position, string regexOptions)
        {
            _params["TextInput"] = input;
            _params["RegexPattern"] = regexPattern;
            _params["Position"] = position;
            _params["RegexOptions"] = regexOptions;

            ExecuteResult result = await _executor.CallFunctionAsync(TestOfflineProjection, "String_RegexCount4", _params);
            CheckResult(result);

            return result?.Value as long?;
        }

        private static void CheckResult(ExecuteResult result)
        {
            Assert.IsNotNull(result);
            result.CheckFailure();
        }

        protected override void AfterTest()
        {
            _params.Clear();
            base.AfterTest();
        }
    }
}
