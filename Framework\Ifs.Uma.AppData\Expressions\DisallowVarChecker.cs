﻿using System.Linq.Expressions;

namespace Ifs.Uma.AppData.Expressions
{
    internal sealed class DisallowVarChecker : IfsExpressionVisitor
    {
        public static void Check(Expression expression)
        {
            DisallowVarChecker visitor = new DisallowVarChecker();
            visitor.Visit(expression);
        }

        protected internal override Expression VisitVarAccessExpression(VarAccessExpression exp)
        {
            throw new ExpressionException($"Failed to read expression identifier '{exp.PropertyPath}'");
        }
    }
}
