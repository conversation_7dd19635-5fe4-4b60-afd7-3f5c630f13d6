using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Database.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Data;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData
{
    public sealed class ArrayDataSource : EntityDataSource
    {
        public EntityDataSource ParentSource { get; }
        public ObjPrimaryKey ParentKey { get; }
        public ObjKey ArrayKey { get; }
        public string ArrayPropertyName { get; }

        private JsonWhereExpression _arrayWhereExpression;

        private ArrayDataSource(IMetadata metadata, string projectionName, RecordType recordType, 
            EntityDataSource parentSource, ObjPrimaryKey parentKey, string arrayPropertyName, ObjKey arrayKey, CpiArray array)
            : base(metadata, projectionName, recordType)
        {
            if (parentSource == null) throw new ArgumentNullException(nameof(parentSource));
            if (parentKey == null) throw new ArgumentNullException(nameof(parentKey));
            if (arrayPropertyName == null) throw new ArgumentNullException(nameof(arrayPropertyName));
            if (arrayKey == null) throw new ArgumentNullException(nameof(arrayKey));
            if (array == null) throw new ArgumentNullException(nameof(array));

            ParentSource = parentSource;
            ParentKey = parentKey;
            ArrayPropertyName = arrayPropertyName;
            ArrayKey = arrayKey;

            if (array.Filter != null)
            {
                _arrayWhereExpression = new JsonWhereExpression(this, array.Filter);
            }
        }

        internal static ArrayDataSource Create(EntityDataSource parentSource, RemoteRow row, string arrayPropertyName)
        {
            if (parentSource == null) throw new ArgumentNullException(nameof(parentSource));
            if (row == null) throw new ArgumentNullException(nameof(row));
            if (arrayPropertyName == null) throw new ArgumentNullException(nameof(arrayPropertyName));
            if (row.TableName != parentSource.Table.TableName)
            {
                throw new ArgumentException($"{nameof(row)} Table must be the same as parent datasource Table", nameof(row));
            }

            CpiArray array = parentSource.Metadata.FindArray(parentSource.ProjectionName, parentSource.EntityName, arrayPropertyName);

            if (array == null)
            {
                return null;
            }

            IMetadata metadata = parentSource.Metadata;
            RecordType recordType = metadata.GetRecordType(parentSource.ProjectionName, array.Target);

            if (recordType == null)
            {
                return null;
            }

            ObjKey arrayKey = metadata.CreateObjKey(parentSource.ProjectionName, array, row);

            if (arrayKey == null)
            {
                return null;
            }

            ObjPrimaryKey parentKey = ObjPrimaryKey.FromPrimaryKey(metadata.MetaModel, row);

            if (parentKey == null)
            {
                return null;
            }

            return new ArrayDataSource(metadata, parentSource.ProjectionName, recordType, parentSource, parentKey, arrayPropertyName, arrayKey, array);
        }

        internal override QueryExpression ToQueryExpression(string fromAlias)
        {
            QueryExpression baseExpression = base.ToQueryExpression(fromAlias);
            Expression where = baseExpression.Where;
            
            Expression whereKey = null;
            foreach (Tuple<IMetaDataMember, object> keyValue in ArrayKey.Values)
            {
                AttributePathInfo attribute = AttributePathInfo.Get(Metadata, ProjectionName, EntityName, keyValue.Item1.PropertyName);
                Expression exp = Expression.Equal(IfsExpression.AttributeAccess(attribute), IfsExpression.Value(keyValue.Item2));
                whereKey = whereKey == null ? exp : Expression.AndAlso(whereKey, exp);
            }

            if (whereKey != null)
            {
                where = where == null ? whereKey : Expression.AndAlso(where, whereKey);
            }

            if (_arrayWhereExpression?.Expression != null)
            {
                where = where == null ? _arrayWhereExpression.Expression : Expression.AndAlso(where, _arrayWhereExpression.Expression);
            }

            return IfsExpression.Query(
                baseExpression.From,
                baseExpression.Joins,
                where, 
                baseExpression.Sorts,
                baseExpression.Distinct, 
                baseExpression.ResultColumns);
        }
        
        public override bool IsTheSameAs(EntityDataSource dataSource)
        {
            ArrayDataSource arrayDataSource = dataSource as ArrayDataSource;
            if (arrayDataSource == null) return false;
            if (arrayDataSource.ArrayPropertyName != ArrayPropertyName) return false;
            if (!EqualityComparer<ObjKey>.Default.Equals(arrayDataSource.ArrayKey, ArrayKey)) return false;

            return ParentSource.IsTheSameAs(arrayDataSource.ParentSource);
        }
    }
}
