﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.UI.Data;

namespace Ifs.Uma.Framework.Data
{
    internal delegate Task<EntityQueryResult> GetRecordsFunc(EntityQuery query, CancellationToken cancelToken);

    internal sealed class EntityQueryDataLoader : IDataLoader<EntityRecord>
    {
        public bool Started { get; private set; }
        public bool IsLoading { get; private set; }
        
        private EntityQuery _query;
        private CancellationTokenSource _cts;
        private GetRecordsFunc _getRecords;

        public EntityQueryDataLoader(IDataHandler data, EntityQuery query)
        {
            _query = query;
            _getRecords = data.GetRecordsAsync;
        }

        public EntityQueryDataLoader(EntityQuery query, GetRecordsFunc getRecords)
        {
            _query = query;
            _getRecords = getRecords;
        }

        public void Begin(CancellationToken cancelToken)
        {
            if (Started) throw new InvalidOperationException();

            Started = true;
            IsLoading = true;
            
            _cts = CancellationTokenSource.CreateLinkedTokenSource(cancelToken);
        }

        async Task<IDataLoadResult<EntityRecord>> IDataLoader<EntityRecord>.ReadAsync(int minimumItems)
        {
            try
            {
                IsLoading = true;
                EntityQueryResult result = await _getRecords(_query, _cts.Token);

                if (result.DataSourceOffline)
                {
                    return DataLoadResult<EntityRecord>.Offline;
                }

                _query = result.GetNextQuery();

                bool hasMoreResults = result.HasMoreResults && _query != null;
                return new DataLoadResult<EntityRecord>(result.Records, !hasMoreResults);
            }
            catch (OperationCanceledException)
            {
                return DataLoadResult<EntityRecord>.Cancelled;
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        async Task<IDataLoadResult<object>> IDataLoader.ReadAsync(int minimumItems)
        {
            return await ((IDataLoader<EntityRecord>)this).ReadAsync(minimumItems);
        }

        public void Dispose()
        {
            _cts?.Cancel();
            _cts = null;
        }
    }
}
