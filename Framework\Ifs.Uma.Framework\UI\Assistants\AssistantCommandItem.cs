﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.Data;
using Ifs.Uma.Framework.Execution.Commands;
using Ifs.Uma.Framework.UI.Commands;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Icons;

namespace Ifs.Uma.Framework.UI.Assistants
{
    public sealed class AssistantCommandItem : FwCommandItem
    {
        private readonly IAssistantRunner _assistantRunner;
        private readonly IExpressionRunner _expressionRunner;

        public AssistantCommand CommandType { get; }
        public AssistantStep CurrentStep { get; set; }
        public bool IsDynamicAssistant { get; set; }
        
        private readonly CpiCommand _cpiCommand;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IMetadata _metadata;

        public AssistantCommandItem(AssistantCommand commandType, IAssistantRunner assistantRunner, IExpressionRunner expressionRunner, ICommandExecutor commandExecutor)
        {
            if (assistantRunner == null) throw new ArgumentNullException(nameof(assistantRunner));
            _assistantRunner = assistantRunner;
            _expressionRunner = expressionRunner;
            _commandExecutor = commandExecutor;
            _metadata = Resolver.Resolve<IMetadata>();

            CommandType = commandType;
            IsVisible = true;

            switch (commandType)
            {
                case AssistantCommand.Previous:
                    Text = Strings.Previous;
                    break;
                case AssistantCommand.Next:
                    Text = Strings.Next;
                    break;
                case AssistantCommand.Finish:
                    Text = Strings.Finish;
                    break;
                case AssistantCommand.Cancel:
                    Text = Strings.Cancel;
                    break;
                case AssistantCommand.Restart:
                    Text = Strings.Restart;
                    break;
            }
        }

        public AssistantCommandItem(AssistantCommand commandType, IAssistantRunner assistantRunner, IExpressionRunner expressionRunner, ICommandExecutor commandExecutor, CpiCommand cpiCommand)
            : this(commandType, assistantRunner, expressionRunner, commandExecutor)
        {
            _cpiCommand = cpiCommand;
            
            if (!string.IsNullOrWhiteSpace(cpiCommand?.Label))
            {
                Text = cpiCommand.Label;
            }

            if (!string.IsNullOrWhiteSpace(cpiCommand?.Icon))
            {
                Icon = IconUtils.Load(cpiCommand.Icon);
            }
            else if (commandType == AssistantCommand.Finish)
            {
                Icon = IconUtils.Check; // Default icon
            }
        }

        protected override async Task OnExecuteAsync()
        {
            await _assistantRunner.ExecuteAsync(CommandType);
        }

        protected override void OnUpdateStates(ViewData viewData, bool isUpdating)
        {
            if (isUpdating)
            {
                IsEnabled = false;
                return;
            }

            if (IsDynamicAssistant)
            {
                IsEnabled = true;
                return; // Commands for dynamic assistants are handled in DynamicAssistantPage class
            }

            if (viewData == null || CurrentStep == null)
            {
                IsEnabled = false;
                IsVisible = false;
                return;
            }

            bool isEnabled = true;

            //Get enabled state from expression. Visible state can not be set via an expression for assistant commands
            CpiCommand command = CommandType == AssistantCommand.Next ? CurrentStep.CpiStep.NextCommand : _cpiCommand;
            if (command != null)
            {
                _commandExecutor.GetStates(ProjectionName, viewData, command, true, out _, out isEnabled);
            }

            if (isEnabled)
            {
                if (CommandType == AssistantCommand.Restart)
                {
                    // Restart command is handled in DynamicAssistantPage class for dynamic assistants
                    IsEnabled = !CurrentStep.IsModal && CurrentStep?.IsCompletionStep == true;
                    IsVisible = IsEnabled;
                    return;
                }

                if (CommandType == AssistantCommand.Next)
                {
                    //Special case: still show next button even if not enabled
                    IsVisible = !CurrentStep.IsCompletionStep && !CurrentStep.IsLastEnabledStep();
                    IsEnabled = IsVisible && CurrentStep.CalculateIsValid();
                    return;
                }

                if (CommandType == AssistantCommand.Finish)
                {
                    //We only show the finish command if we're on the last step in UXX offline
                    IsVisible = !CurrentStep.IsCompletionStep && CurrentStep.IsLastEnabledStep();
                    IsEnabled = IsVisible && CurrentStep.CalculateIsValid();
                    return;
                }

                if (CommandType == AssistantCommand.Previous)
                {
                    isEnabled = !CurrentStep.IsCompletionStep && !CurrentStep.IsFirstEnabledStep();
                }
                else if (CommandType == AssistantCommand.Cancel)
                {
                    bool defaultEnabled = viewData.Record.HasChanges || !CurrentStep.IsFirstEnabledStep() || CurrentStep.IsModal;

                    // If Enabled expression is specified by the developer, then it takes precedence
                    isEnabled = _expressionRunner.RunCheck(command?.OfflineEnabled ?? command?.Enabled, viewData, defaultEnabled);
                }
            }

            IsEnabled = isEnabled;
            IsVisible = IsEnabled;
        }

        public override void GetSelectAttributes(ICollection<string> attributes)
        {
            base.GetSelectAttributes(attributes);

            AttributeFinder.FindInCommand(attributes, _metadata, ProjectionName, _cpiCommand);
        }
    }

    public enum AssistantCommand
    {
        Previous,
        Next,
        Finish,
        Cancel,
        Restart
    }
}
