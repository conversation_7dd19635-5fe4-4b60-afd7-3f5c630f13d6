﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ifs.Uma.AppData;
using Ifs.Uma.Framework.UI.Pages;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Data;
using Ifs.Uma.UI.Observables;
using Ifs.Uma.Utility;

namespace Ifs.Uma.Framework.Data
{
    public sealed class PageData : ObservableBase, IDisposable
    {
        public event EventHandler RecordDataChanged;

        public ViewData DefaultViewData { get; }

        public string DefaultBindingName { get; set; }

        public ViewState ViewState { get; }

        public EntityDataSource DataSource { get; set; }
        public PageValues Filter { get; set; }
        public string[] SelectAttributes { get; set; }
        public CpiCrudActions CrudActions { get; set; }
        public string StateIndicator { get; set; }
        public ISettings PageSettings { get; set; }

        private ViewData _editingViewData;
        public ViewData EditingViewData
        {
            get => _editingViewData;
            private set
            {
                if (SetProperty(ref _editingViewData, value))
                {
                    OnPropertyChanged(nameof(HasChanges));
                }
            }
        }

        public bool IsReport { get; set; }

        public EditMode EditMode { get; } = new EditMode();

        public bool HasChanges
        {
            get => _editingViewData != null || EditMode.State != EditModeState.NotEditing;
        }

        private readonly Dictionary<string, ViewData> _viewDatas = new Dictionary<string, ViewData>();

        public TaskTracker BackgroundTasks { get; } = new TaskTracker();

        public PageBase Page { get; set; }

        public PageData(ILogger logger, IMetadata metadata, IDataHandler data)
            : this(new RecordData(logger, metadata, data))
        {
        }

        public PageData(RecordData recordData)
        {
            DefaultViewData = new ViewData(this, recordData);
            ViewState = new ViewState();
            EditMode.StateChanged += EditMode_StateChanged;

            if (DefaultViewData.Record != null)
            {
                DefaultViewData.Record.DataChanged += Record_DataChanged;
            }

            AddViewData(string.Empty, DefaultViewData);
        }

        private void EditMode_StateChanged(object sender, EventArgs e)
        {
            OnPropertyChanged(nameof(HasChanges));
        }

        public ViewData GetViewData(string bindingName)
        {
            if (bindingName == null || bindingName == DefaultBindingName)
            {
                bindingName = string.Empty;
            }

            if (!_viewDatas.TryGetValue(bindingName, out ViewData viewData))
            {
                RecordData record = DefaultViewData.Record.CreateNew();
                record.DataChanged += Record_DataChanged;
                viewData = new ViewData(this, record) { Parent = DefaultViewData };
                AddViewData(bindingName, viewData);
            }

            return viewData;
        }

        private void AddViewData(string bindingName, ViewData viewData)
        {
            _viewDatas[bindingName] = viewData;

            if (viewData.Record != null)
            {
                viewData.Record.PropertyChanged += Record_PropertyChanged;
            }
        }

        private void Record_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(RecordData.HasChanges))
            {
                UpdateEditingViewData();
            }
        }

        private void UpdateEditingViewData()
        {
            EditingViewData = _viewDatas.Values.FirstOrDefault(x => x.Record != null && x.Record.HasChanges);
        }

        private void Record_DataChanged(object sender, EventArgs e)
        {
            RecordDataChanged?.Invoke(this, EventArgs.Empty);
        }

        public async Task WaitForBackgroundTasks()
        {
            foreach (ViewData viewData in _viewDatas.Values.ToArray())
            {
                await viewData.Record.BackgroundTasks.WaitForCompletion();
            }

            await BackgroundTasks.WaitForCompletion();
        }

        #region IDisposable Support

        private bool _disposed = false;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (_disposed)
            {
                return;
            }

            if (disposing)
            {
                // Unsubscribe from events
                if (DefaultViewData?.Record != null)
                {
                    DefaultViewData.Record.DataChanged -= Record_DataChanged;
                }

                EditMode.StateChanged -= EditMode_StateChanged;

                foreach (var viewData in _viewDatas.Values)
                {
                    if (viewData.Record != null)
                    {
                        viewData.Record.PropertyChanged -= Record_PropertyChanged;
                        viewData.Record.Dispose();
                    }
                }

                // Dispose of disposable objects
                BackgroundTasks?.Dispose();
            }

            _disposed = true;
        }

        // Ensure that Dispose is called when the object is finalized
        ~PageData()
        {
            Dispose();
        }

        #endregion
    }
}
