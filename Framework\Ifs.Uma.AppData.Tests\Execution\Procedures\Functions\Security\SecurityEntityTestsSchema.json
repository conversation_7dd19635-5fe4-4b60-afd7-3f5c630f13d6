{"name": "FndTstOffline", "version": "1706901162:1948287535", "component": "FNDTST", "projection": {"name": "FndTstOffline", "service": "FndTstOffline.svc", "version": "1948287535", "contains": {}, "entities": {}, "procedures": {"Function<Security_IsEntityReadGranted>": {"name": "Security_IsEntityReadGranted", "type": "Function", "params": [{"name": "EntityName", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Security", "name": "IsEntityReadGranted", "paramsArray": ["${EntityName}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}, "Function<Security_IsEntityWriteGranted>": {"name": "Security_IsEntityWriteGranted", "type": "Function", "params": [{"name": "EntityName", "dataType": "Text"}], "layers": [{"vars": [{"name": "Result", "dataType": "Boolean"}], "execute": [{"call": {"method": "proc", "args": {"namespace": "Security", "name": "IsEntityWriteGranted", "paramsArray": ["${EntityName}"]}}, "assign": "Result"}, {"call": {"method": "return", "args": {"name": "Result"}}}]}]}}}}