﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Ifs.Cloud.Client.Exceptions
{
    /// <summary>
    /// Dynamically constructs queues for application defined categories
    /// Fire events on queues for events enqueue and eh,, dequeue?
    /// TODO: Make recoverable?
    /// </summary>
    public class ErrorCategoryQueueManager
    {
        private static List<string> categories = new List<string>();
        private static List<ErrorQueue> qu = new List<ErrorQueue>();

        public static void EnqueueErrorInfo(string category, ErrorInfo ei)
        {
            if (categories.Contains(category))
            {
            }
        }
    }

    internal class ErrorQueueItem
    {
        internal string Catagory { get; set; }
        internal ErrorInfo ErrInfo { get; set; }
    }

    internal class ErrorQueue : Queue<ErrorQueueItem>
    {
    }
}
