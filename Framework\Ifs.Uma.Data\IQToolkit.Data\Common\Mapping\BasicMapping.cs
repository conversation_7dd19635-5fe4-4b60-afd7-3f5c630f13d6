﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Ifs.Uma.Database;
using Ifs.Uma.Utility;
using Ifs.Uma.Data;

namespace IQToolkit.Data.Common
{
    internal static class MetaDataMemberExtensions2
    {
        public static Expression MakeMemberAccessExpression(this IMetaDataMember member, Expression instance)
        {
            if (member == null) throw new ArgumentNullException("member");
            if (instance == null) throw new ArgumentNullException("instance");
            if (member.Storage != null)
            {
                return Expression.MakeMemberAccess(instance, member.Storage);
            }
            if (member.Property != null)
            {
                return Expression.MakeMemberAccess(instance, member.Property);
            }
            if (member.StringIndexer != null && !string.IsNullOrEmpty(member.PropertyName))
            {
                return Expression.Property(instance, member.StringIndexer, Expression.Constant(member.PropertyName));
            }
            throw new InvalidOperationException(string.Format(System.Globalization.CultureInfo.InvariantCulture,
                "Member '{0}' not found on row of type '{1}'", member.PropertyName, instance.Type.FullName));
        }
    }

    internal static class MetaModelExtensions2
    {
        public static QueryMapper CreateMapper(this IMetaModel model, QueryTranslator translator)
        {
            return new BasicMapper(model, translator);
        }

        public static bool CanBeEvaluatedLocally(this IMetaModel model, Expression expression)
        {
            if (expression == null) throw new ArgumentNullException("expression");
            // any operation on a query can't be done locally
            ConstantExpression cex = expression as ConstantExpression;
            if (cex != null)
            {
                IQueryable query = cex.Value as IQueryable;
                if (query != null && query.Provider == model) //JVB: there's no way an IQueryProvider can be an IMetaModel 
                    return false;
            }
            MethodCallExpression mc = expression as MethodCallExpression;
            if (mc != null &&
                (mc.Method.DeclaringType == typeof(Enumerable) ||
                 mc.Method.DeclaringType == typeof(Queryable) ||
                 mc.Method.DeclaringType == typeof(TableQueryable)))
            {
                return false;
            }
            if (expression.NodeType == ExpressionType.Convert &&
                expression.Type == typeof(object))
                return true;
            return expression.NodeType != ExpressionType.Parameter &&
                   expression.NodeType != ExpressionType.Lambda &&
                   expression.NodeType != ExpressionType.New;
        }
    }

    internal class BasicMapper : QueryMapper
    {
        IMetaModel model;
        QueryTranslator translator;

        public BasicMapper(IMetaModel model, QueryTranslator translator)
        {
            this.model = model;
            this.translator = translator;
        }

        public override IMetaModel Model
        {
            get { return this.model; }
        }

        public override QueryTranslator Translator
        {
            get { return this.translator; }
        }

        public override ProjectionExpression GetQueryExpression(IMetaTable entity)
        {
            if (entity == null) throw new ArgumentNullException("entity");
            var tableAlias = new TableAlias();
            var selectAlias = new TableAlias();
            var table = new TableExpression(tableAlias, entity);

            Expression projector = this.GetEntityExpression(table, entity);
            var pc = ColumnProjector.ProjectColumns(projector, null, selectAlias, tableAlias);

            var proj = new ProjectionExpression(
                new SelectExpression(selectAlias, pc.Columns, table, null),
                pc.Projector
                );

            return (ProjectionExpression)this.Translator.Police.ApplyPolicy(proj, entity);
        }

        public override EntityExpression GetEntityExpression(Expression root, IMetaTable entity)
        {
            if (entity == null) throw new ArgumentNullException("entity");
            // must be some complex type constructed from multiple columns
            var assignments = new List<EntityAssignment>();
            foreach (IMetaDataMember mi in entity.DataMembers)
            {
                Expression me = this.GetMemberExpression(root, entity, mi);
                if (me != null)
                {
                    assignments.Add(new EntityAssignment(mi, me));
                }
            }
            return new EntityExpression(entity, BuildEntityExpression(entity, assignments));
        }

        internal class EntityAssignment
        {
            public IMetaDataMember Member { get; private set; }
            public Expression Expression { get; private set; }
            public EntityAssignment(IMetaDataMember member, Expression expression)
            {
                if (expression == null) throw new ArgumentNullException("expression");
                Member = member;
                Expression = expression;
            }
        }

        protected virtual Expression BuildEntityExpression(IMetaTable entity, IList<EntityAssignment> assignments)
        {
            if (entity == null) throw new ArgumentNullException("entity");
            if (assignments == null) throw new ArgumentNullException("assignments");
            NewExpression newExpression = entity.CreateRowExpression();
            Expression result;
            if (assignments.Count > 0)
            {
                ParameterExpression entityVar = Expression.Variable(newExpression.Type, "entityVar");

                List<MemberBinding> memberBindings = new List<MemberBinding>();
                List<Expression> blockExpressions = new List<Expression>();

                foreach (EntityAssignment assignment in assignments)
                {
                    Expression memberAccess = assignment.Member.MakeMemberAccessExpression(entityVar);

                    MemberExpression mex = memberAccess as MemberExpression;
                    if (mex != null)
                    {
                        memberBindings.Add(Expression.Bind(mex.Member, assignment.Expression));
                    }
                    else
                    {
                        Expression assignExp = assignment.Expression;
                        if (memberAccess.Type != assignExp.Type)
                        {
                            assignExp = Expression.Convert(assignExp, memberAccess.Type);
                        }

                        blockExpressions.Add(Expression.Assign(memberAccess, assignExp));
                    }
                }

                if (blockExpressions.Count == 0)
                {
                    result = Expression.MemberInit(newExpression, memberBindings.ToArray());
                }
                else
                {
                    // First expression in block creates the entity
                    blockExpressions.Insert(0, Expression.Assign(entityVar, Expression.MemberInit(newExpression, memberBindings.ToArray())));
                    // Last expression in a block is returned
                    blockExpressions.Add(entityVar);

                    result = Expression.Block(new ParameterExpression[] { entityVar }, blockExpressions);
                }
            }
            else
            {
                result = newExpression;
            }
            return result;
        }

        protected virtual ConstructorBindResult BindConstructor(ConstructorInfo cons, IList<EntityAssignment> assignments)
        {
            if (cons == null) throw new ArgumentNullException("cons");
            var ps = cons.GetParameters();
            var args = new Expression[ps.Length];
            var mis = new MemberInfo[ps.Length];
            HashSet<EntityAssignment> members = new HashSet<EntityAssignment>(assignments);
            HashSet<EntityAssignment> used = new HashSet<EntityAssignment>();

            for (int i = 0, n = ps.Length; i < n; i++)
            {
                ParameterInfo p = ps[i];
                TypeInfo pInfo = p.ParameterType.GetTypeInfo();
                var assignment = members.FirstOrDefault(a => a.Member.MemberInfo != null &&
                    p.Name == a.Member.PropertyName
                    && pInfo.IsAssignableFrom(a.Expression.Type.GetTypeInfo()));
                if (assignment == null)
                {
                    assignment = members.FirstOrDefault(a => a.Member.MemberInfo != null &&
                        string.Equals(p.Name, a.Member.PropertyName, StringComparison.OrdinalIgnoreCase)
                        && pInfo.IsAssignableFrom(a.Expression.Type.GetTypeInfo()));
                }
                if (assignment != null)
                {
                    args[i] = assignment.Expression;
                    if (mis != null)
                        mis[i] = assignment.Member.MemberInfo;
                    used.Add(assignment);
                }
                else
                {
                    MemberInfo[] mems = null; // cons.DeclaringType.GetMember(p.Name, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase);
                    if (mems != null && mems.Length > 0)
                    {
                        args[i] = Expression.Constant(TypeHelper.GetDefault(p.ParameterType), p.ParameterType);
                        mis[i] = mems[0];
                    }
                    else
                    {
                        // unknown parameter, does not match any member
                        return null;
                    }
                }
            }

            members.ExceptWith(used);

            return new ConstructorBindResult(Expression.New(cons, args, mis), members);
        }

        protected class ConstructorBindResult
        {
            public NewExpression Expression { get; private set; }
            public ReadOnlyCollection<EntityAssignment> Remaining { get; private set; }
            public ConstructorBindResult(NewExpression expression, IEnumerable<EntityAssignment> remaining)
            {
                this.Expression = expression;
                this.Remaining = remaining.ToReadOnly();
            }
        }

        public override bool HasIncludedMembers(EntityExpression entity)
        {
            if (entity == null) throw new ArgumentNullException("entity");
            var policy = this.translator.Police.Policy;
            foreach (var mi in entity.Entity.DataMembers)
            {
                // MM: Relationships only on MemberInfos
                if (mi.MemberInfo != null && policy.IsIncluded(mi.MemberInfo))
                    return true;
            }
            return false;
        }

        public override EntityExpression IncludeMembers(EntityExpression entity, Func<IMetaDataMember, bool> fnIsIncluded)
        {
            if (entity == null) throw new ArgumentNullException("entity");
            if (fnIsIncluded == null) throw new ArgumentNullException("fnIsIncluded");
            var assignments = BasicMapper.GetAssignments(entity.Expression).ToDictionary(ma => ma.Member.PropertyName);
            bool anyAdded = false;
            foreach (var mi in entity.Entity.DataMembers)
            {
                EntityAssignment ea;
                bool okayToInclude = !assignments.TryGetValue(mi.PropertyName, out ea);
                if (okayToInclude && fnIsIncluded(mi))
                {
                    ea = new EntityAssignment(mi, this.GetMemberExpression(entity.Expression, entity.Entity, mi));
                    assignments[mi.PropertyName] = ea;
                    anyAdded = true;
                }
            }
            if (anyAdded)
            {
                return new EntityExpression(entity.Entity, this.BuildEntityExpression(entity.Entity, assignments.Values.ToList()));
            }
            return entity;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA1801:ReviewUnusedParameters", MessageId = "newOrMemberInit")]
        private static IEnumerable<EntityAssignment> GetAssignments(Expression newOrMemberInit)
        {
            // MM: Rleationships not supported
            //var assignments = new List<EntityAssignment>();
            //var minit = newOrMemberInit as MemberInitExpression;
            //if (minit != null)
            //{
            //    assignments.AddRange(minit.Bindings.OfType<MemberAssignment>().Select(a => new EntityAssignment(a.Member, a.Expression)));
            //    newOrMemberInit = minit.NewExpression;
            //}
            //var nex = newOrMemberInit as NewExpression;
            //if (nex != null && nex.Members != null)
            //{
            //    assignments.AddRange(
            //        Enumerable.Range(0, nex.Arguments.Count)
            //                  .Where(i => nex.Members[i] != null)
            //                  .Select(i => new EntityAssignment(nex.Members[i], nex.Arguments[i]))
            //                  );
            //}
            return new List<EntityAssignment>();
        }

        public override Expression GetMemberExpression(Expression root, IMetaTable entity, IMetaDataMember member)
        {
            if (member == null) throw new ArgumentNullException("member");
            AliasedExpression aliasedRoot = root as AliasedExpression;
            if (aliasedRoot != null && member != null)
            {
                return new ColumnExpression(member.ColumnType, aliasedRoot.Alias, member.ColumnName);
            }

            return BindMappingMember(root, member);
        }
        
        private IEnumerable<ColumnAssignment> GetColumnAssignments(Expression table, Expression instance, IMetaTable entity, Func<IMetaTable, IMetaDataMember, bool> fnIncludeColumn)
        {
            foreach (var m in entity.DataMembers)
            {
                if (fnIncludeColumn(entity, m))
                {
                    yield return new ColumnAssignment(
                        (ColumnExpression)this.GetMemberExpression(table, entity, m),
                        m.MakeMemberAccessExpression(instance)
                        );
                }
            }
        }

        protected virtual IMetaDataMember GetMappingMember(IMetaTable entity, Expression expression)
        {
            MemberExpression mex = expression as MemberExpression;
            return mex != null ? entity.GetMember(mex.Member) : null;
        }

        protected virtual Expression BindMappingMember(Expression source, IMetaDataMember member)
        {
            if (member == null) throw new ArgumentNullException("member");
            return QueryBinder.BindMember(source, member.MemberInfo);
        }

        protected virtual Expression GetInsertResult(IMetaTable entity, Expression instance, LambdaExpression selector, Dictionary<IMetaDataMember, Expression> map)
        {
            if (selector == null) throw new ArgumentNullException("selector");
            var tableAlias = new TableAlias();
            var tex = new TableExpression(tableAlias, entity);
            var aggregator = Aggregator.GetAggregator(selector.Body.Type, typeof(IEnumerable<>).MakeGenericType(selector.Body.Type));

            Expression where;
            DeclarationCommand genIdCommand = null;
            IMetaDataMember autoIncrement = entity.AutoIncrement();
            if (autoIncrement != null)
            {
                if (map == null || !map.ContainsKey(autoIncrement))
                {
                    var localMap = new Dictionary<IMetaDataMember, Expression>();
                    genIdCommand = this.GetGeneratedIdCommand(entity, autoIncrement, localMap);
                    map = localMap;
                }

                // is this just a retrieval of one generated id member?
                IMetaDataMember member = GetMappingMember(entity, selector.Body);
                if (member != null && member.PrimaryKey && member.AutoIncrement)
                {
                    if (genIdCommand != null)
                    {
                        // just use the select from the genIdCommand
                        return new ProjectionExpression(
                            genIdCommand.Source,
                            new ColumnExpression(member.ColumnType, genIdCommand.Source.Alias, genIdCommand.Source.Columns[0].Name),
                            aggregator
                            );
                    }
                    else
                    {
                        TableAlias alias = new TableAlias();
                        return new ProjectionExpression(
                            new SelectExpression(alias, new[] { new ColumnDeclaration(string.Empty, map[member]) }, null, null),
                            new ColumnExpression(member.ColumnType, alias, string.Empty),
                            aggregator
                            );
                    }
                }

                where = this.GetMemberExpression(tex, entity, autoIncrement).Equal(map[autoIncrement]);
            }
            else
            {
                where = this.GetIdentityCheck(tex, entity, instance);
            }

            Expression typeProjector = this.GetEntityExpression(tex, entity);
            Expression selection = DbExpressionReplacer.Replace(selector.Body, selector.Parameters[0], typeProjector);
            TableAlias newAlias = new TableAlias();
            var pc = ColumnProjector.ProjectColumns(selection, null, newAlias, tableAlias);
            var pe = new ProjectionExpression(
                new SelectExpression(newAlias, pc.Columns, tex, where),
                pc.Projector,
                aggregator
                );

            if (genIdCommand != null)
            {
                return new BlockCommand(genIdCommand, pe);
            }
            return pe;
        }

        protected virtual DeclarationCommand GetGeneratedIdCommand(IMetaTable entity, IMetaDataMember autoIncrement, Dictionary<IMetaDataMember, Expression> map)
        {
            if (autoIncrement == null) throw new ArgumentNullException("autoIncrement");
            var columns = new List<ColumnDeclaration>();
            var decls = new List<VariableDeclaration>();
            var alias = new TableAlias();
            Expression genId = this.translator.Linguist.Language.GetGeneratedIdExpression(autoIncrement);
            string name = autoIncrement.PropertyName;
            columns.Add(new ColumnDeclaration(name, genId));
            decls.Add(new VariableDeclaration(name, new ColumnExpression(genId.Type, alias, name)));
            if (map != null)
            {
                var vex = new VariableExpression(name, autoIncrement.ColumnType);
                map.Add(autoIncrement, vex);
            }
            var select = new SelectExpression(alias, columns, null, null);
            return new DeclarationCommand(decls, select);
        }

        protected virtual Expression GetIdentityCheck(Expression root, IMetaTable entity, Expression instance)
        {
            if (entity == null) throw new ArgumentNullException("entity");
            return entity.PrimaryKey
            .Select(m => this.GetMemberExpression(root, entity, m).Equal(m.MakeMemberAccessExpression(instance)))
            .Aggregate((x, y) => x.And(y));
        }

        protected virtual Expression GetEntityExistsTest(IMetaTable entity, Expression instance)
        {
            ProjectionExpression tq = this.GetQueryExpression(entity);
            Expression where = this.GetIdentityCheck(tq.Select, entity, instance);
            return new ExistsExpression(new SelectExpression(new TableAlias(), null, tq.Select, where));
        }

        protected virtual Expression GetEntityStateTest(IMetaTable entity, Expression instance, LambdaExpression updateCheck)
        {
            if (updateCheck == null) throw new ArgumentNullException("updateCheck");
            ProjectionExpression tq = this.GetQueryExpression(entity);
            Expression where = this.GetIdentityCheck(tq.Select, entity, instance);
            Expression check = DbExpressionReplacer.Replace(updateCheck.Body, updateCheck.Parameters[0], tq.Projector);
            where = where.And(check);
            return new ExistsExpression(new SelectExpression(new TableAlias(), null, tq.Select, where));
        }
    }
}
