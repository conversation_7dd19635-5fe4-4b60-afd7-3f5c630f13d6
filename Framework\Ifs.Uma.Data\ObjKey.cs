﻿using Ifs.Uma.Database;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Ifs.Uma.Data
{
    public sealed class ObjKey : IEquatable<ObjKey>
    {
        private readonly IMetaTable _table;
        private readonly Tuple<IMetaDataMember, object>[] _values;

        public IMetaTable Table => _table;
        public IEnumerable<Tuple<IMetaDataMember, object>> Values => _values;

        public static ObjKey FromRow(IMetaModel metaModel, IEnumerable<IMetaDataMember> members, object row)
        {
            if (metaModel == null) throw new ArgumentNullException(nameof(metaModel));
            if (row == null) throw new ArgumentNullException(nameof(row));

            RemoteRow remoteRow = row as RemoteRow;
            IMetaTable table = remoteRow == null ? metaModel.GetTable(row.GetType()) : metaModel.GetTable(remoteRow.TableName);

            if (table == null) throw new ArgumentException(nameof(row));

            List<Tuple<IMetaDataMember, object>> values = new List<Tuple<IMetaDataMember, object>>();

            foreach (IMetaDataMember member in members)
            {
                if (values.Any(x => x.Item1 == member))
                {
                    return null;
                }

                object value = member.GetValue(row);

                if (value == null)
                {
                    return null;
                }

                values.Add(Tuple.Create(member, value));
            }

            return new ObjKey(table, values.ToArray());
        }

        public static ObjKey FromValues(IMetaTable table, IEnumerable<KeyValuePair<string, object>> data)
        {
            if (table == null) throw new ArgumentNullException(nameof(table));
            if (data == null) throw new ArgumentNullException(nameof(data));
            
            List<Tuple<IMetaDataMember, object>> values = new List<Tuple<IMetaDataMember, object>>();

            foreach (KeyValuePair<string, object> kvp in data)
            {
                if (kvp.Value == null)
                {
                    return null;
                }

                IMetaDataMember member = table.FindMemberByColumnName(kvp.Key);

                if (member == null || values.Any(x => x.Item1 == member))
                {
                    return null;
                }

                values.Add(Tuple.Create(member, kvp.Value));
            }

            return new ObjKey(table, values.ToArray());
        }
        
        private ObjKey(IMetaTable table, Tuple<IMetaDataMember, object>[] values)
        {
            _table = table;
            Array.Sort(values, (x, y) => x.Item1.Index.CompareTo(y.Item1.Index));
            _values = values;
        }

        public override bool Equals(object obj)
        {
            ObjKey other = obj as ObjKey;

            if (other != null)
            {
                return Equals(other);
            }

            return false;
        }

        public bool Equals(ObjKey other)
        {
            if (other == null)
            {
                return false;
            }

            if (object.ReferenceEquals(this, other))
            {
                return true;
            }

            if (_table != other._table) return false;
            if (_values.Length != other._values.Length) return false;

            for (int i = 0; i < _values.Length; i++)
            {
                if (!object.Equals(_values[i], other._values[i]))
                {
                    return false;
                }
            }

            return true;
        }

        public override int GetHashCode()
        {
            int hc = _table.GetHashCode();
            hc = CombineHashCodes(hc, _values.Length.GetHashCode());

            for (int i = 0; i < _values.Length; i++)
            {
                object value = _values[i];
                hc = CombineHashCodes(hc, value == null ? 13 : value.GetHashCode());
            }

            return hc;
        }

        private static int CombineHashCodes(int h1, int h2)
        {
            return ((h1 << 5) + h1) ^ h2;
        }
    }
}
