﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Database.Expressions
{
    internal static class QuerySelectSpec
    {
        public static ISelectSpec CreateForView(IMetadata metadata, IMetaTable table)
        {
            string entityName = RemoteNaming.ToEntityName(table.TableName);
            CpiEntity entityFromAppMeta = null;
            CpiEntity entityFromProjection = null;
            string projectionName = null;

            metadata.CpiMetaData.App?.SyncEntities?.Entities?.TryGetValue(entityName, out entityFromAppMeta);

            if (entityFromAppMeta == null)
            {
                Logger.Current.Warning("The query {0} was not found in app meta. Searching in projection meta instead.", entityName);
            }

            foreach (CpiProjection projection in metadata.CpiMetaData.GetProjections())
            {
                entityFromProjection = metadata.FindEntity(projection.Name, entityName);
                if (entityFromProjection != null)
                {
                    projectionName = projection.Name;
                    break;
                }
            }

            if (entityFromAppMeta?.IsView() == true || entityFromProjection?.IsView() == true)
            {
                QueryExpression expression = IfsExpression.FromCpiQuery(entityFromAppMeta?.OfflineQuery ?? entityFromProjection?.OfflineQuery);
                ISelectSpec selectSpec = Create(expression, new QueryScope(projectionName, entityFromProjection?.LuName, metadata)); // LU name is only contained in projection meta
                ValidateViewAttributes(table, selectSpec);
                return selectSpec;
            }

            return null;
        }

        private static void ValidateViewAttributes(IMetaTable table, ISelectSpec selectSpec)
        {
            if (selectSpec.Columns != null)
            {
                foreach (IMetaDataMember member in table.DataMembers)
                {
                    // Custom fields won't be in this list because of changes done in RemoteMetaModel constructor

                    ISelectColumnSpec columnSelect = selectSpec.Columns.FirstOrDefault(x =>
                        x.ColumnAlias == member.ColumnName ||
                        (x.ColumnAlias == null && x.ColumnName == member.ColumnName));

                    if (columnSelect != null)
                    {
                        continue;
                    }

                    throw new InvalidMetadataException($"Missing attribute '{member.PropertyName}' from offline query '{RemoteNaming.ToEntityName(table.TableName)}'");
                }
            }
        }

        public static ISelectSpec Create(QueryExpression query, QueryScope scope)
        {
            if (query.UnionSelects != null)
            {
                IEnumerable<UnionSelectInformation> unionSelectInformation = CreateUnionSpec(query, scope);

                return SqlSpec.CreateSelect(unionSelectInformation);
            }
            else
            {
                ITableNameSpec from = PrepareFrom(query, scope);
                List<IJoinSpec> joins = PrepareJoins(query, scope);
                IEnumerable<IWhereElement> where = PrepareWhereColumns(query, scope);
                IOrderBy orderBy = PrepareOrderBy(query, scope);
                IEnumerable<ISelectColumnSpec> columns = PrepareSelectColumns(query, scope);

                IEnumerable<IJoinSpec> implicitJoins = scope.GetImplicitJoins();
                if (implicitJoins != null)
                {
                    joins.AddRange(implicitJoins);
                }

                return SqlSpec.CreateSelect(columns, from, joins, where, null, orderBy, query.Distinct);
            }
        }

        private static IEnumerable<UnionSelectInformation> CreateUnionSpec(QueryExpression query, QueryScope scope)
        {
            IEnumerable<UnionSelectExpression> unionSelects = query.UnionSelects;
            List<UnionSelectInformation> unionSelectInformation = new List<UnionSelectInformation>();

            foreach (UnionSelectExpression unionSelectExpression in unionSelects)
            {
                ITableNameSpec tableNameSpec = GetTableSpec(scope.Metadata.GetTableForEntityName(unionSelectExpression.From), null);
                IEnumerable<IUnionColumnSpec> columns = GetColumnSpecForUnion(unionSelectExpression.From, unionSelectExpression.Colunms, scope);

                unionSelectInformation.Add(new UnionSelectInformation() { TableNameSpec = tableNameSpec, Columns = columns });
            }
            return unionSelectInformation;
        }

        private static IEnumerable<IUnionColumnSpec> GetColumnSpecForUnion(string entityName, IEnumerable<ResultColumnExpression> resultColumns, QueryScope scope)
        {
            List<IUnionColumnSpec> unionColumnSpecs = new List<IUnionColumnSpec>();
            foreach (ResultColumnExpression resultColumn in resultColumns)
            {
                IUnionColumnSpec columnSpec = scope.GetColumnSpec(entityName, resultColumn.ResultColumn.ToString());
                unionColumnSpecs.Add(columnSpec);
            }
            return unionColumnSpecs;
        }

        private static ITableNameSpec PrepareFrom(QueryExpression query, QueryScope scope)
        {
            IMetaTable fromTable = scope.Metadata.GetTableForEntityName(query.From.Entity);

            if (fromTable == null)
            {
                throw new InvalidMetadataException($"Query contains invalid FROM entity '{query.From.Entity} {query.From.Alias}'");
            }

            scope.AddAlias(query.From.Alias, fromTable);

            return GetTableSpec(fromTable, scope.GetFullAlias(query.From.Alias));
        }

        private static List<IJoinSpec> PrepareJoins(QueryExpression query, QueryScope scope)
        {
            IMetadata metadata = scope.Metadata;

            List<IJoinSpec> joins = new List<IJoinSpec>();

            foreach (JoinExpression join in query.Joins)
            {
                IMetaTable joinTable = metadata.GetTableForEntityName(join.Entity);

                if (joinTable == null)
                {
                    throw new InvalidMetadataException($"Query contains invalid JOIN entity '{join.Entity} {join.Alias}'");
                }

                scope.AddAlias(join.Alias, joinTable);
            }

            foreach (JoinExpression join in query.Joins)
            {
                IMetaTable joinTable = metadata.GetTableForEntityName(join.Entity);
                ITableNameSpec table = GetTableSpec(joinTable, scope.GetFullAlias(join.Alias));
                ISqlExpression onExpression = PrepareExpression(join.On, scope);
                IJoinSpec joinSpec = JoinSpec.Create(join.JoinType, table, onExpression);
                joins.Add(joinSpec);
            }

            return joins;
        }

        private static ITableNameSpec GetTableSpec(IMetaTable metaTable, string alias)
        {
            return TableSpec.Create(metaTable.TableName, alias);
        }

        private static IEnumerable<IWhereElement> PrepareWhereColumns(QueryExpression query, QueryScope scope)
        {
            if (query.Where == null)
            {
                return null;
            }

            ISqlExpression whereExpression = PrepareExpression(query.Where, scope);
            return new[] { WhereElement.Create(EOperand.And, whereExpression) };
        }

        private static IOrderBy PrepareOrderBy(QueryExpression query, QueryScope scope)
        {
            List<ISortColumnSpec> sorts = new List<ISortColumnSpec>();

            foreach (SortExpression item in query.Sorts)
            {
                Expression resultExpression = QueryRetargeter.Rewrite(item.Expression, scope);
                if (resultExpression is DbColumnSpecExpression dbColumnSpec)
                {
                    sorts.Add(ColumnSpec.Create(dbColumnSpec.ColumnSpec.ColumnName, item.SortOrder, dbColumnSpec.ColumnSpec.TableAlias));
                }
                else
                {
                    throw new InvalidMetadataException($"Query '{scope.Name}' ORDER BY does not support expressions");
                }
            }

            return OrderBy.Create(sorts, 0, 0);
        }
        
        private static IEnumerable<ISelectColumnSpec> PrepareSelectColumns(QueryExpression query, QueryScope scope)
        {
            List<ISelectColumnSpec> selectColumns = new List<ISelectColumnSpec>();

            foreach (ResultColumnExpression item in query.ResultColumns)
            {
                if (item.Alias != null && !scope.ValidateIdentifier(item.Alias))
                {
                    throw new InvalidMetadataException($"Query '{scope.Name}' contains invalid alias identifier '{item.Alias}'");
                }

                string columnAlias = item.Alias == null ? null : RemoteNaming.ToColumnName(item.Alias);
                
                Expression resultExpression = QueryRetargeter.Rewrite(item.ResultColumn, scope);
                if (resultExpression is DbColumnSpecExpression dbColumnSpec)
                {
                    if (columnAlias == null)
                    {
                        selectColumns.Add(dbColumnSpec.ColumnSpec);
                    }
                    else
                    {
                        selectColumns.Add(ColumnSpec.Create(dbColumnSpec.ColumnSpec.ColumnName, dbColumnSpec.ColumnSpec.TableAlias, columnAlias));
                    }
                }
                else
                {
                    if (columnAlias == null)
                    {
                        throw new InvalidMetadataException($"Query '{scope.Name}' SELECT column expressions must have an AS clause");
                    }

                    ISqlExpression resultColumnExpression = SqlExpression.Create(resultExpression);
                    selectColumns.Add(ColumnSpec.Create(resultColumnExpression, columnAlias));
                }
            }

            return selectColumns;
        }

        private static ISqlExpression PrepareExpression(Expression expression, QueryScope scope)
        {
            expression = QueryRetargeter.Rewrite(expression, scope);
            return SqlExpression.Create(expression);
        }
    }
}
