﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ifs.Uma.Database;
using IQToolkit.Data;
using IQToolkit.Data.Common;

namespace Ifs.Uma.Data
{
    public interface IDbEntityProvider
    {
        void CommandA(Action<DbCommand> act);
        T CommandF<T>(Func<DbCommand, T> func);
        void TransactionA(Action<DbCommand> act);
        T TransactionF<T>(Func<DbCommand, T> func);
    }

    /// <summary>
    /// DbEntityProvider is used by a DataContext to access the database
    /// </summary>
    internal class DbEntityProvider : EntityProvider, IMapEnumeration, IDbEntityProvider
    {
        //JVB: Keep the DbInternal value hidden.
        //If required, add additional properties here like Builder and GetEnumeration
        private readonly DbInternal _db;

        public DbEntityProvider(DbInternal db, IMetaModel model, QueryLanguage language)
            : base(model, language)
        {
            if (db == null) throw new ArgumentNullException("db");
            if (db.MappingSource == null) throw new ArgumentOutOfRangeException("db");
            _db = db;
        }

        public SqlBuilder Builder { get { return _db.Factory.Builder; } }

        public IMetaEnumeration GetEnumeration(Type enumType)
        {
            return _db.MappingSource.GetEnumeration(enumType);
        }

        protected override QueryExecutor CreateExecutor()
        {
            return new Executer(this);
        }

        //JVB: use these wrappers of the DbInternal equivalents to allow commands to be re-entrant

        public override T CommandF<T>(Func<DbCommand, T> func)
        {
            if (func == null) throw new ArgumentNullException("func");
            return _command == null ? _db.CommandF((cmd) => SaveCommand(cmd, func)) : func(_command);
        }

        public override T TransactionF<T>(Func<DbCommand, T> func)
        {
            if (func == null) throw new ArgumentNullException("func");
            if (_command == null)
            {
                return _db.TransactionF((cmd) => SaveCommand(cmd, func));
            }
            if (_command.Transaction != null)
            {
                return func(_command);
            }
            // need to create a transaction around the existing connection
            T result;
            DbConnection conn = _command.Connection;
            using (DbTransaction tran = conn.BeginTransaction())
            {
                using (DbCommand cmd = conn.CreateCommand(tran))
                {
                    // swap to this command
                    DbCommand oldCmd = _command;
                    _command = cmd;
                    try
                    {
                        result = func(cmd);
                    }
                    finally
                    {
                        // swap back to the original command
                        _command = oldCmd;
                    }
                }
                tran.Commit();
            }
            return result;
        }

        public IEnumerable<T> CommandDeferred<T>(Func<DbCommand, IEnumerable<T>> func)
        {
            if (func == null) throw new ArgumentNullException("func");
            return _db.CommandDeferred(func);
        }

        public override bool Executing()
        {
            return _command != null;
        }

        public override bool ExecutingTransaction()
        {
            DbCommand cmd = _command;
            return cmd != null && cmd.Transaction != null;
        }

        private DbCommand _command;

        private T SaveCommand<T>(DbCommand cmd, Func<DbCommand, T> func)
        {
            if (cmd == null) throw new ArgumentNullException("cmd");
            if (func == null) throw new ArgumentNullException("func");
            _command = cmd;
            try
            {
                return func(cmd);
            }
            finally
            {
                _command = null;
            }
        }
    }
}
