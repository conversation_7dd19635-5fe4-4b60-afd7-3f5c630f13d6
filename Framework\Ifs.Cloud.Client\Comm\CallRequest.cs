﻿#region Copyright (c) IFS Research & Development
//
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
#endregion
#region History
//  xxxx-xx-xx SUKMLK Created.
//  2011-10-13 PKULLK Modified to be compatible with .NEt4.5 with async calls, etc.
#endregion

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ifs.Cloud.Client.Entities;
using Ifs.Cloud.Client.Exceptions;
using Ifs.Cloud.Client.Interfaces;
using Ifs.Cloud.Client.Types;
using Ifs.Cloud.Client.Utils;
using Ifs.Uma.Localization;
using Ifs.Uma.Utility;

namespace Ifs.Cloud.Client.Comm
{
    /// <summary>
    /// Represents a request for a call to IFS Cloud
    /// </summary>
    internal class CallRequest<T> where T : BaseResource
    {
        #region Constants
        public const string Page = "page";
        public const string PageSize = "pageSize";
        private const string DebugPostFix = "odata-debug=json";
        private const string MetadataRefreshError = "MI_MODIFIED_ERROR";
        private const string MetadataRetrievalError = "MI_RETRIEVAL_ERROR";

        public const int CallTimeoutSeconds = 60 * 10;
        public const int ResponseBufferMaxSize = 900000000;

        private const string AuthUrlPrefix = "/mob/ifsapplications/projection/v1/MobileClientRuntime.svc/";
        private const string BaseUrlPrefix = "/mob/ifsapplications/projection/v1/";
        private const string ResourcesUrlPrefix = "/mob/ifsapplications/projection/v1/KathCustomerInfo.svc/resource/";
        private const string UrlPrefixBinaryResource = "/mob/ifsapplications/projection/v1/KathCustomerInfo.svc/binaryresource/";

        #endregion

        #region Fields
        private readonly Uri _uri;
        private string _authorizationHeader;
        private string _openIdHeader;
        private readonly string _appInfoHeader;
        private readonly string _deviceId;
        private long _messageId;
        private string _eTag;
        private string _fileName;
        private readonly ContextProvider _ctx;
        private HttpClient _client;
        #endregion

        #region Properties
        public HttpMethodType Method { get; }
        public RequestContent<T> Content { get; }
        public DataFormat DataFormat { get; }
        public Stream DataStream { get; }

        #endregion

        #region Factory methods

        public static CallRequest<T> ForResource(ContextProvider ctx, RequestContent<T> requestor, HttpMethodType method, int page, long messageId, string eTag = "")
        {
            string url = GetResourceName(ctx, requestor);
            if (TypeHelper.CanCast(typeof(T), typeof(AuthResource)))
            {
                url = AuthUrlPrefix + url;
            }
            else if (!requestor.Resource.FullNameDefined)
            {
                url = BaseUrlPrefix + url;
            }
            return new CallRequest<T>(ctx, requestor, url, method, page, messageId, DataFormat.Json, eTag: eTag);
        }

        public static CallRequest<T> ForBinaryResource(ContextProvider ctx, RequestContent<T> requestor)
        {
            return new CallRequest<T>(ctx, requestor, UrlPrefixBinaryResource + GetResourceName(ctx, requestor), HttpMethodType.Get, 1, 1, DataFormat.Binary);
        }

        public static CallRequest<T> ForBinaryGet(ContextProvider ctx, RequestContent<T> requestor, string resourceQuery)
        {
            string url = GetResourceName(ctx, requestor);
            url = BaseUrlPrefix + url;

            return new CallRequest<T>(ctx, requestor, url + resourceQuery, HttpMethodType.Get, 1, 0, DataFormat.Binary);
        }

        public static CallRequest<T> ForBinaryPut(ContextProvider ctx, RequestContent<T> requestor, string resourceQuery, Stream stream)
        {
            string url = GetResourceName(ctx, requestor);
            if (TypeHelper.CanCast(typeof(T), typeof(AppResource)))
            {
                url = BaseUrlPrefix + url;
            }
            else
            {
                url = ResourcesUrlPrefix + url;
            }

            return new CallRequest<T>(ctx, requestor, url + resourceQuery, HttpMethodType.Put, 1, 1, DataFormat.Binary, stream);
        }

        public static CallRequest<T> ForLobPatch(ContextProvider ctx, RequestContent<T> requestor, string resourceQuery, string eTag, long messageId, Stream stream, DataFormat dataFormat, bool offline = true, string fileName = null)
        {
            string url = GetResourceName(ctx, requestor);
            if (TypeHelper.CanCast(typeof(T), typeof(AppResource)))
            {
                url = BaseUrlPrefix + url;
            }
            else
            {
                url = ResourcesUrlPrefix + url;
            }

            return new CallRequest<T>(ctx, requestor, url + resourceQuery, HttpMethodType.Patch, 1, offline ? messageId : 0, dataFormat, stream, eTag) { _fileName = fileName };
        }
        #endregion

        #region Constructor

        private CallRequest(ContextProvider ctx, RequestContent<T> content, string urlPath, HttpMethodType method, int page, long messageId, DataFormat dataFormat, Stream dataStream = null, string eTag = "", string fileName = "")
        {
            Method = method;
            DataFormat = dataFormat;
            _ctx = ctx;
            _messageId = messageId;
            _eTag = eTag;
            string url = _ctx.BaseUrl + urlPath;
            Content = content;
            UriBuilder uriBuilder = new UriBuilder(url);
            DataStream = dataStream;

            if (dataFormat == DataFormat.Json)
            {
                string queryString = null;

                if (content.Resource is IQueryStringProvider qsp)
                {
                    queryString = qsp.GetQueryString();
                }
                else if (Method == HttpMethodType.Get || (Method == HttpMethodType.Put && DataStream != null))
                {
                    //queryString = ToQueryString(page, pageSize) + "&" + content.GetResourceAsUrlParam();
                }
                else
                {
                    //queryString = ToQueryString(page, pageSize);
                }

                LoggerManager.Instance.LoggingLevel = string.IsNullOrEmpty(_ctx.DeviceId) ? MessageType.Information : LoggerManager.Instance.LoggingLevel;

                if (LoggerManager.Instance.LoggingLevel == MessageType.Trace)
                {
                    queryString = string.IsNullOrEmpty(queryString) ? DebugPostFix : queryString + "&" + DebugPostFix;
                }

                uriBuilder.Query = queryString;
            }

            _uri = uriBuilder.Uri;

            if (_ctx.ClientInfo != null)
            {
                _appInfoHeader = $"{_ctx.ClientInfo.AppName}";
            }

            if (!string.IsNullOrEmpty(_ctx.DeviceId))
            {
                _deviceId = _ctx.DeviceId;
            }

            if (_ctx.SessionId != null)
            {
                SetAuthorizationHeader(AuthenticationType.Session, ctx.SessionId);
            }
        }

        private static string ToQueryString(int page, int pageSize)
        {
            if (page > 0)
            {
                return Formatter.ToQueryString(Page, page, PageSize, pageSize);
            }

            return Formatter.ToQueryString(PageSize, pageSize);
        }

        #endregion

        #region Methods
        /// <summary>
        /// Sets the given authorization header
        /// </summary>
        /// <param name="type">header type</param>
        /// <param name="value">header value</param>
        public void SetAuthorizationHeader(AuthenticationType type, string value)
        {
            switch (type)
            {
                case AuthenticationType.Authentication:
                    _authorizationHeader = "Basic " + value;
                    break;

                case AuthenticationType.Session:
                    _authorizationHeader = value;
                    break;

                case AuthenticationType.OpenIdAuthentication:
                    _openIdHeader = value;
                    break;

                default:
                    throw new ArgumentException($"Unknown authentication type: '{type}'", nameof(type));
            }
        }

        public Task<CallResponse> ExecuteRequestWithRetryAsync()
        {
            return ExecuteRequestWithRetryAsync(CancellationToken.None);
        }

        /// <summary>
        /// Executes this call request
        /// </summary>
        /// <returns>the response from the cloud call</returns>
        public async Task<CallResponse> ExecuteRequestWithRetryAsync(CancellationToken cancellationToken)
        {
            try
            {
                return await ExecuteRequestAsync(cancellationToken).ConfigureAwait(false);
            }
            catch (CloudException ce)
            {
                if (ce.ShouldRetryAfterReauthentication)
                {
                    await _ctx.Reauthenticate().ConfigureAwait(false);
                    SetAuthorizationHeader(AuthenticationType.Session, _ctx.SessionId);

                    return await ExecuteRequestAsync(cancellationToken).ConfigureAwait(false);
                }
                else if ((ce.StatusCode == HttpStatusCode.InternalServerError || ce.StatusCode == HttpStatusCode.ServiceUnavailable) && (ce.Message.ToUpper().Contains(MetadataRefreshError) || ce.Message.ToUpper().Contains(MetadataRetrievalError)))
                {
                    //Repeat the call if the server throws a 500 or 503 error with MI_MODIFIED_ERROR or MI_RETRIEVAL_ERROR message
                    return await ExecuteRequestAsync(cancellationToken).ConfigureAwait(false);
                }

                throw ce;
            }
        }

        public async Task<CallResponse> ExecuteRequestAsync(CancellationToken cancellationToken)
        {
            HttpRequestMessage message = new HttpRequestMessage(Method.GetMethod(), _uri);
            if (!string.IsNullOrEmpty(_ctx.SessionId))
            {
                if (!_ctx.DevMode)
                {
                    message.Headers.Add(IfsCloudHttpHeaderFields.AuthorizationHeader, "Bearer " + _ctx.SessionId);
                    message.Headers.TryAddWithoutValidation("User-Agent", string.Empty);
                }
            }
            if (!string.IsNullOrEmpty(_openIdHeader) && !_ctx.DevMode)
            {
                message.Headers.Add(IfsCloudHttpHeaderFields.AuthorizationHeaderOpenId, _openIdHeader);
            }
            if (_appInfoHeader != null)
            {
                message.Headers.Add(IfsCloudHttpHeaderFields.AppInfoHeader, _appInfoHeader);
            }
            if (_deviceId != null)
            {
                message.Headers.Add(IfsCloudHttpHeaderFields.DeviceIdHeader, _deviceId);
            }
            if (_messageId != 0)
            {
                message.Headers.Add(IfsCloudHttpHeaderFields.MessageIdHeader, _messageId.ToString());
            }
            if (Content?.Resource is ICanUseCompression canUseCompression)
            {
                message.Headers.Add(IfsCloudHttpHeaderFields.UseCompression, canUseCompression.ShouldUseCompression() ? "True" : "False");
            }
            if (!string.IsNullOrEmpty(_eTag))
            {
                message.Headers.TryAddWithoutValidation(IfsCloudHttpHeaderFields.ETagHeader, _eTag);
            }
            else
            {
                message.Headers.TryAddWithoutValidation(IfsCloudHttpHeaderFields.ETagHeader, "*");
            }
            if (!string.IsNullOrEmpty(_fileName))
            {
                message.Headers.TryAddWithoutValidation(IfsCloudHttpHeaderFields.ContentDisposition, "filename=" + Convert.ToBase64String(Encoding.UTF8.GetBytes(_fileName)));
            }

            message.Headers.Add(IfsCloudHttpHeaderFields.LocaleHeader, CultureInfo.CurrentCulture.Name);
            if ((Method == HttpMethodType.Put || Method == HttpMethodType.Patch) && DataStream != null)
            {
                message.Content = new StreamContent(DataStream);

                if (DataFormat == DataFormat.LongText)
                {
                    message.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("text/plain");
                }
                else
                {
                    message.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/octet-stream");
                }
            }
            else if (Method == HttpMethodType.Post || Method == HttpMethodType.Put || Method == HttpMethodType.Patch)
            {
                ByteArrayContent requestContent = Content.ToByteArray();
                message.Content = requestContent;
                //message.Headers.Add("content-type", "application/json");
                message.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            }

            if (TypeHelper.CanCast(typeof(T), typeof(AppResource)))
            {
                AppResource resource = (AppResource)(object)Content.Resource;
                if (resource.TimeZoneAwareResource)
                {
                    message.Headers.Add(IfsCloudHttpHeaderFields.KnownTimeZoneEnabled, "true");
                }
                if (!string.IsNullOrEmpty(resource.SiteTimeZone))
                {
                    message.Headers.Add(IfsCloudHttpHeaderFields.ObjSiteTimeZoneResource, resource.SiteTimeZone);
                }
            }

            // GetHttpHandler will only return a handler on Android (AndroidClientHandler).
            // Other platforms will use the default HttpClientHandler.
            // This resolves bug TMFW-1317 (Android SSL Hanging) and highlights that there is a bug in the default System.Net.Http.HttpClientHandler that results in an empty body begin returned with a successful status
            // A bug has been submitted to Xamarin: https://bugzilla.xamarin.com/show_bug.cgi?id=51182
            HttpClientHandler handler = new HttpClientHandler();
            handler.AllowAutoRedirect = false;
            handler.AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate;
            handler.CookieContainer = _ctx.CookieContainer;
            WebProxy.Setup(handler);

            _client = LoggerManager.SecondaryLoggingEnabled ? new HttpClient(new HttpLoggingHandler(handler)) : new HttpClient(handler);
            _client.MaxResponseContentBufferSize = ResponseBufferMaxSize;
            _client.Timeout = TimeSpan.FromSeconds(CallTimeoutSeconds);

            HttpResponseMessage response = null;

            try
            {
                response = await _client.SendAsync(message, HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
            }
            catch (Exception e)
            {
                //TODO: dispose of this properly at some point, but for a workaround dispose manually here
                response?.Dispose();
                handler.Dispose();

                throw CloudException.FromException(e);
            }

            CallResponse errorResponse = new CallResponse(response, handler);

#if DEBUG
            ////DEBUG - Simulate an Invalid Authorization Exception - will cause parent method to retry
            //errorResponse.Dispose();
            //throw CloudException.FromCause("ERROR_INVALID_AUTHORIZATION_HEADER:Invalid authorization header.", HttpStatusCode.Unauthorized, new Exception());

            ////DEBUG - Simulate a Bad Request error
            //errorResponse.Dispose();
            //throw CloudException.FromCause("ERROR_APP_RESOURCE_NOT_FOUND:Fake debug error", HttpStatusCode.BadRequest, null);

            ////DEBUG - Simulate a 500 error - should only be logged and not prompt a dialog
            //errorResponse.Dispose();
            //throw CloudException.FromCause("Unknown error occurred", HttpStatusCode.InternalServerError, null, true);
#endif

            if (response.IsSuccessStatusCode)
            {
                return errorResponse;
            }
            else
            {
                string errorMessage;
                bool applicationError = false;
                if (!TryGetErrorMessageFromHeaders(errorResponse, out errorMessage))
                {
                    string errorContent = await errorResponse.ConsumeContentAsStringAsync();
                    byte[] byteError = await errorResponse.GetBinaryContent();

                    if (IsHtml(errorContent))
                    {
                        errorMessage = CreateErrorMessageForResponse(response);
                    }
                    else
                    {
                        string errorMessageFromJson = HandleOdataErrorMessages(byteError, out applicationError);
                        if (!string.IsNullOrEmpty(errorMessageFromJson))
                        {
                            errorMessage = errorMessageFromJson;
                        }
                        else if (!string.IsNullOrEmpty(errorContent))
                        {
                            // if it's just an error message rather than one wrapped in string
                            errorMessage = errorContent;
                        }
                        else
                        {
                            errorMessage = CreateErrorMessageForResponse(response);
                        }
                    }
                }

                CloudException exception = CloudException.FromCause(errorMessage, response.StatusCode, null, applicationError);

                //TODO: dispose of this properly at some point, but for a workaround dispose manually here
                errorResponse.Dispose();

                throw exception;
            }
        }

        private bool TryGetErrorMessageFromHeaders(CallResponse response, out string message)
        {
            string[] headersToCheck =
            {
                IfsCloudHttpHeaderFields.AppErrorHeader,
                IfsCloudHttpHeaderFields.CloudResourceErrorHeader,
                IfsCloudHttpHeaderFields.CloudAuthorizationErrorHeader,
                IfsCloudHttpHeaderFields.CloudErrorHeader
            };

            foreach (string header in headersToCheck)
            {
                List<string> value;

                if (response.Headers.TryGetValue(header, out value))
                {
                    message = string.Concat(value);

                    if (!message.ContainsNonASCIICharacters())
                    {
                        return true;
                    }
                }
            }

            message = null;
            return false;
        }

        private string HandleOdataErrorMessages(byte[] errorMessage, out bool applicationError)
        {
            applicationError = false;

            try
            {
                ODataErrorResponse errorObject = ContentSerializationHelper.DeserializeJsonBinary<ODataErrorResponse>(errorMessage);
                if (errorObject != null)
                {
                    if (errorObject.Error?.Code == "DATABASE_ERROR" || errorObject.Error?.Code == "ODATA_PROVIDER_ERROR" || errorObject.Error?.Code == "REQUEST_ERROR")
                    {
                        applicationError = true;
                    }

                    if (errorObject.Error?.Details != null && errorObject.Error.Details.Length > 0)
                    {
                        string message = errorObject.Error.Details[0].Message;

                        for (int i = 0; i < 2; i++)
                        {
                            if (message.Contains(":"))
                            {
                                message = message.Substring(message.IndexOf(":") + 1, message.Length - message.IndexOf(":") - 1).Trim();
                            }
                        }

                        return message;
                    }
                }
            }
            catch (System.Runtime.Serialization.SerializationException ex)
            {
                Logger.Current.Error(ex.Message);
                return Encoding.UTF8.GetString(errorMessage, 0, errorMessage.Length);
            }

            return string.Empty;
        }

        private string CreateErrorMessageForResponse(HttpResponseMessage response)
        {
            switch (response.StatusCode)
            {
                case HttpStatusCode.Moved:
                case HttpStatusCode.Redirect:
                    string errorMessage = Strings.ServerIsNoLongerAvailable;

                    if (_uri.IsAbsoluteUri && (response.Headers.Location?.IsAbsoluteUri ?? false) //Otherwise Scheme doesn't exist
                        &&
                        string.Equals(_uri.Scheme, "http", StringComparison.OrdinalIgnoreCase)
                        &&
                        string.Equals(response.Headers.Location.Scheme, "https", StringComparison.OrdinalIgnoreCase)
                        &&
                        Uri.Compare(_uri, response.Headers.Location, UriComponents.HttpRequestUrl & ~UriComponents.Scheme, UriFormat.SafeUnescaped, StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        //Request and response URIs are the same apart from the request is HTTP and the response is HTTPS
                        errorMessage += " " + Strings.TryHttpsInsteadOfHttp;
                    }
                    else
                    {
                        errorMessage += " " + Strings.PleaseContactAdministrator;
                    }
                    return errorMessage;
                default:
                    return $"{(int)response.StatusCode}: {response.ReasonPhrase}";
            }
        }

        public async Task CancelRequestAsync()
        {
            if (_client != null && _uri != null)
            {
                await _client.DeleteAsync(_uri);
            }
        }
        #endregion

        #region Util

        private static string GetResourceName(ContextProvider ctx, RequestContent<T> requestor)
        {
            ClientInfo clientInfo = ctx?.ClientInfo;
            return requestor.Resource.GetResourceName(clientInfo);
        }

        private static bool IsHtml(string text)
        {
            return text.IndexOf("<!DOCTYPE html PUBLIC", StringComparison.OrdinalIgnoreCase) >= 0 ||
                   text.IndexOf("<html>", StringComparison.OrdinalIgnoreCase) >= 0;
        }
        #endregion

        private class WebProxy : IWebProxy
        {
            // ReSharper disable once StaticMemberInGenericType
            private static readonly Uri DevProxyUri = null; //new Uri("http://***********:8888");

            #region Implementation

            public ICredentials Credentials
            {
                get { return new NetworkCredential(); }
                set { }
            }

            public Uri GetProxy(Uri destination)
            {
                return new Uri(DevProxyUri, destination.PathAndQuery);
            }

            public bool IsBypassed(Uri host)
            {
                return false;
            }

            public static void Setup(HttpClientHandler handler)
            {
                if (PlatformServices.Provider != null)
                {
                    IWebProxy proxy = PlatformServices.Provider.GetSystemWebProxy() as IWebProxy;
                    if (proxy != null && handler.SupportsProxy)
                    {
                        handler.Proxy = proxy;
                        handler.UseProxy = true;
                    }
                }

#if DEBUG
                if (DevProxyUri != null && handler.SupportsProxy)
                {
                    handler.Proxy = new WebProxy();
                    handler.UseProxy = true;
                }
#endif
            }

            #endregion
        }
    }
}
