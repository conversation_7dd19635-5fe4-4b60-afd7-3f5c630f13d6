﻿using System;
using System.IO;

namespace Ifs.Uma.AppData.StringExpressions
{
    internal sealed class Lexer : IDisposable
    {
        private readonly TextReader _reader;
        private readonly TokenDefinition[] _tokenDefinitions;

        public Token CurrentToken { get; private set; }

        private int _lineNumber;
        private int _position;
        private string _lineRemaining;

        public Lexer(TextReader reader)
        {
            _reader = reader;
            _tokenDefinitions = TokenDefinition.GetDefinitions();

            NextLine();
        }

        private void NextLine()
        {
            do
            {
                _lineRemaining = _reader.ReadLine();
                ++_lineNumber;
                _position = 0;
            }
            while (_lineRemaining != null && _lineRemaining.Length == 0);
        }

        public bool Next()
        {
            while (true)
            {
                ReadToken();

                if (CurrentToken.Type == TokenType.Eof)
                {
                    return false;
                }

                if (CurrentToken.Type != TokenType.Space)
                {
                    return true;
                }
            }
        }

        private void ReadToken()
        {
            if (_lineRemaining == null)
            {
                CurrentToken = new Token(TokenType.Eof, null);
                return;
            }

            foreach (TokenDefinition def in _tokenDefinitions)
            {
                int matched = def.Match(_lineRemaining);
                if (matched > 0)
                {
                    _position += matched;
                    CurrentToken = new Token(def.LexerToken, _lineRemaining.Substring(0, matched));
                    _lineRemaining = _lineRemaining.Substring(matched);

                    if (_lineRemaining.Length == 0)
                    {
                        NextLine();
                    }

                    return;
                }
            }

            throw new Exception($"Unable to match against any tokens at line {_lineNumber} position {_position} \"{_lineRemaining}\"");
        }

        public void Dispose()
        {
            _reader.Dispose();
        }
    }
}
