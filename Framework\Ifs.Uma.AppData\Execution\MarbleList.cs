﻿using System;
using System.Collections.Generic;
using System.Linq;
using Ifs.Uma.AppData.Execution.Procedures;
using Ifs.Uma.Data;
using Ifs.Uma.Metadata.Cpi;

namespace Ifs.Uma.AppData.Execution
{
    public sealed class MarbleList : List<object>
    {
        public CpiTypeInfo TypeInfo { get; }

        public MarbleList(CpiTypeInfo typeInfo)
        {
            TypeInfo = typeInfo ?? throw new ArgumentNullException(nameof(typeInfo));
        }

        public MarbleList(IEnumerable<object> collection, CpiTypeInfo typeInfo)
            : base(collection)
        {
            TypeInfo = typeInfo;
        }

        public MarbleList Clone()
        {
            return new MarbleList(this, TypeInfo);
        }

        public bool TryConvertAndAdd(object value)
        {
            if (Equals(value, string.Empty))
            {
                value = null;
            }

            if (!ExecutionUtils.TryConvert(value, TypeInfo.DataType, TypeInfo.SubType, false, out value))
            {
                return false;
            }

            Add(value);
            return true;
        }

        // This method will do a thorough comparison of entities inside a list, than just a .NET object comparison which won't match sometimes
        public int GetIndex(ProcedureContext context, object value)
        {
            if (value is RemoteRow remoteRow)
            {
                List<RemoteRow> rows = this.Cast<RemoteRow>().ToList();

                // First, check if there's a match with the object key
                int result = remoteRow.ObjKey != null ? rows.FindIndex(x => x.ObjKey == remoteRow.ObjKey) : -1;

                if (result < 0)
                {
                    // If not found, go through the list and look for a record with a matching primary key
                    string valuePk = remoteRow.GetPrimaryKeysString(context.Metadata.MetaModel, context.ProjectionName);
                    int counter = 0;
                    foreach (RemoteRow row in rows)
                    {
                        if (row.GetPrimaryKeysString(context.Metadata.MetaModel, context.ProjectionName) == valuePk)
                        {
                            return counter;
                        }

                        counter++;
                    }
                }

                return result;
            }

            return IndexOf(value);
        }
    }
}
