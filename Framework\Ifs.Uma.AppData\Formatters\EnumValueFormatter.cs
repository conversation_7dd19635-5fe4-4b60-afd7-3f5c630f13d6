﻿using System.Collections.Generic;
using Ifs.Uma.Database;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;
using Ifs.Uma.Utility.Formatters;

namespace Ifs.Uma.AppData.Formatters
{
    internal sealed class EnumValueFormatter : IValueFormatter
    {
        private readonly Dictionary<object, string> _labels = new Dictionary<object, string>();

        public EnumValueFormatter(CpiEnumeration enumeration)
        {
            if (enumeration?.Labels != null)
            {
                foreach (CpiEnumerationLabel enumerationLabel in enumeration.Labels)
                {
                    _labels[enumerationLabel.Value ?? string.Empty] = enumerationLabel.Label ?? string.Empty;
                }
            }
        }

        public EnumValueFormatter(IMetaEnumeration enumeration)
        {
            if (enumeration?.Values != null)
            {
                foreach (var enumValue in enumeration.Values)
                {
                    _labels[enumValue.LocalValue ?? string.Empty] = enumValue.DisplayName ?? enumValue.ServerValue ?? string.Empty;
                }
            }
        }

        public string Format(object value)
        {
            string dbValue = ObjectConverter.ToString(value);
            if (dbValue != null && _labels.TryGetValue(dbValue, out string label))
            {
                return label;
            }

            return dbValue;
        }
    }
}
