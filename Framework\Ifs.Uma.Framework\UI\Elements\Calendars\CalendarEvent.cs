﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Framework.UI.Cards;
using Ifs.Uma.Localization;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.UI.Controls;
using Ifs.Uma.Utility;
using Newtonsoft.Json;

namespace Ifs.Uma.Framework.UI.Elements.Calendars
{
    public class CalendarEvent
    {
        public static CalendarEvent CreateEvent(DateTime startTime, DateTime endTime, CardData cardData, IExpressionRunner expressionRunner, CpiCard card, CpiCalendarEvent calendarEvent, CalendarElement calendarElement)
        {
            if (startTime == null || endTime == null || calendarEvent.Views == null)
            {
                return null;
            }

            List<CalendarEventField> calendarEventFields = new List<CalendarEventField>();
            foreach (CpiField field in calendarEvent.Views.Agenda.Content.Where(x => x.Field.Control == CpiControlType.Field).Select(x => x.Field))
            {
                if (cardData.TryGetValue(field.Attribute, out object id))
                {
                    CalendarEventField eventField = new CalendarEventField(id?.ToString(), field.ShowLabel.GetValueOrDefault() ? field.Label : string.Empty);
                    calendarEventFields.Add(eventField);
                }
            }
            string aStartTime = startTime.ToShortTimeString();
            string aEndTime = endTime.ToShortTimeString();
            bool isSameYear = false;
            if (string.Compare(startTime.Year.ToString(), endTime.Year.ToString(), StringComparison.OrdinalIgnoreCase) == 0)
            {
                isSameYear = true;
            }

            string txtLongRunningActivityStartText = $" {Strings.Till} {endTime:MMM d}";
            string txtLongRunningActivityStartLongText = $" {Strings.Till} {endTime:MMM d yyyy}";
            string txtLongRunningActivityBetweenText = $"{Strings.AllDayTill} - {endTime:MMM d}";
            string txtLongRunningActivityBetweenLongText = $"{Strings.AllDayTill} - {endTime:MMM d yyyy}";
            string txtLongRunningActivityGenericText = $"{startTime:MMM d} " + aStartTime + " - " + $"{endTime:MMM d} " + aEndTime;
            string txtLongRunningActivityGenericLongText = $"{startTime:MMM d yyyy} " + aStartTime + " - " + $"{endTime:MMM d yyyy} " + aEndTime;

            string pattern1 = @"d{1,2}/M{1,2}";
            string pattern2 = @"d{1,2}-M{1,2}";
            string pattern3 = @"y{2,4}/M{1,2}/d{1,2}";
            string pattern4 = @"y{2,4}-M{1,2}-d{1,2}";

            if (Regex.IsMatch(CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern, pattern1)
                || Regex.IsMatch(CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern, pattern2))
            {
                txtLongRunningActivityStartText = $" {Strings.Till} {endTime:d MMM}";
                txtLongRunningActivityStartLongText = $" {Strings.Till} {endTime:d MMM yyyy}";
                txtLongRunningActivityBetweenText = $"{Strings.AllDayTill} - {endTime:d MMM}";
                txtLongRunningActivityBetweenLongText = $"{Strings.AllDayTill} - {endTime:d MMM yyyy}";
                txtLongRunningActivityGenericText = $"{startTime:d MMM} " + aStartTime + " - " + $"{endTime:d MMM} " + aEndTime;
                txtLongRunningActivityGenericLongText = $"{startTime:d MMM yyyy} " + aStartTime + " - " + $"{endTime:d MMM yyyy} " + aEndTime;
            }
            else if (Regex.IsMatch(CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern, pattern3)
                || Regex.IsMatch(CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern, pattern4))
            {
                txtLongRunningActivityStartLongText = $" {Strings.Till} {endTime:yyyy MMM d}";
                txtLongRunningActivityBetweenLongText = $"{Strings.AllDayTill} - {endTime:yyyy MMM d}";
                txtLongRunningActivityGenericLongText = $"{startTime:yyyy MMM d} " + aStartTime + " - " + $"{endTime:yyyy MMM d} " + aEndTime;
            }

            return new CalendarEvent()
            {
                StartTime = startTime,
                EndTime = endTime,
                CpiCard = card,
                CardData = cardData,
                Emphasis = UmaColor.FromEmphasis(expressionRunner.GetEmphasis(calendarEvent.Emphasis, cardData))?.ToHtmlHex(),
                Agenda = calendarEvent.Views.Agenda,
                Day = calendarEvent.Views.Day,
                Month = calendarEvent.Views.Month,
                TimeLine = calendarEvent.Views.Timeline,
                TimeLineMonth = calendarEvent.Views.TimelineMonth,
                TimeLineWeek = calendarEvent.Views.TimelineWeek,
                TimeLineWorkWeek = calendarEvent.Views.TimelineWorkWeek,
                Week = calendarEvent.Views.Week,
                WorkWeek = calendarEvent.Views.WorkWeek,
                AppointmentDateTime = $"{startTime.ToShortTimeString()} - {endTime.ToShortTimeString()}",
                IsAllDay = (endTime - startTime).TotalHours >= 24,
                IsLongRunning = startTime.DayOfYear != endTime.DayOfYear,
                LongRunningActivityStartText = txtLongRunningActivityStartText,
                LongRunningActivityStartLongText = txtLongRunningActivityStartLongText,
                LongRunningActivityEndText = $"{Strings.Ends} ",
                LongRunningActivityBetweenText = txtLongRunningActivityBetweenText,
                LongRunningActivityBetweenLongText = txtLongRunningActivityBetweenLongText,
                LongRunningActivityGenericText = txtLongRunningActivityGenericText,
                LongRunningActivityGenericLongText = txtLongRunningActivityGenericLongText,
                IsSameYear = isSameYear,
                TotalDaysInCalendarEvent = (int)Math.Floor((endTime.Date.AddDays(1).AddTicks(-1) - startTime.Date).TotalDays),
                CalendarEventFields = calendarEventFields,
                CpiCalendarEvent = calendarEvent,
                IsTimelineIndicator = false,
                CalendarElement = calendarElement
            };
        }

        public List<CalendarEventField> CalendarEventFields { get; private set; }

        public DateTime StartTime { get; private set; }

        public DateTime EndTime { get; private set; }

        [JsonIgnore]
        public CpiCard CpiCard { get; private set; }

        [JsonIgnore]
        public CardData CardData { get; private set; }

        public string Emphasis { get; private set; }

        public string ViewLabel { get; private set; }

        [JsonIgnore]
        public CpiCalendarAgenda Agenda { get; private set; }

        [JsonIgnore]
        public CpiCalendarDay Day { get; private set; }

        [JsonIgnore]
        public CpiCalendarMonth Month { get; private set; }

        [JsonIgnore]
        public CpiCalendarTimeline TimeLine { get; private set; }

        [JsonIgnore]
        public CpiCalendarTimelineMonth TimeLineMonth { get; private set; }

        [JsonIgnore]
        public CpiCalendarTimelineWeek TimeLineWeek { get; private set; }

        [JsonIgnore]
        public CpiCalendarTimelineWorkWeek TimeLineWorkWeek { get; private set; }

        [JsonIgnore]
        public CpiCalendarWeek Week { get; private set; }

        [JsonIgnore]
        public CpiCalendarWorkWeek WorkWeek { get; private set; }

        [JsonIgnore]
        public CpiCalendarEvent CpiCalendarEvent { get; private set; }

        public bool IsAllDay { get; set; }

        public bool IsLongRunning { get; set; }

        public Badge AppointmentBadge { get; set; }

        public string AppointmentDateTime { get; set; }

        public string LongRunningActivityStartText { get; set; }

        public string LongRunningActivityStartLongText { get; set; }

        public string LongRunningActivityEndText { get; set; }

        public string LongRunningActivityBetweenText { get; set; }

        public string LongRunningActivityBetweenLongText { get; set; }

        public string LongRunningActivityGenericText { get; set; }

        public string LongRunningActivityGenericLongText { get; set; }

        public bool IsSameYear { get; set; }

        public int TotalDaysInCalendarEvent { get; set; }

        public bool IsTimelineIndicator { get; set; }

        public CalendarElement CalendarElement { get; set; }

        public override bool Equals(object obj)
        {
            return Equals(obj as CalendarEvent);
        }

        public bool Equals(CalendarEvent other)
        {
            char[] charsToTrim = { ',', ' ' };
            string sLabel = string.Empty;

            foreach (CalendarEventField item in CalendarEventFields)
            {
                sLabel = string.IsNullOrEmpty(item.Label) ? (sLabel + item.Id + ", ") : (sLabel + item.Label + ": " + item.Id + ", ");
            }

            sLabel = sLabel.TrimEnd(charsToTrim);

            string sComparer = string.Empty;

            foreach (CalendarEventField item in other.CalendarEventFields)
            {
                sComparer = string.IsNullOrEmpty(item.Label) ? (sComparer + item.Id + ", ") : (sComparer + item.Label + ": " + item.Id + ", ");
            }

            sComparer = sComparer.TrimEnd(charsToTrim);

            return other != null &&
                   string.Compare(sLabel, sComparer) == 0 &&
                   StartTime == other.StartTime &&
                   EndTime == other.EndTime;
        }

        public override int GetHashCode()
        {
            char[] charsToTrim = { ',', ' ' };
            string sLabel = string.Empty;
            foreach (CalendarEventField item in CalendarEventFields)
            {
                sLabel = string.IsNullOrEmpty(item.Label) ? (sLabel + item.Id + ", ") : (sLabel + item.Label + ": " + item.Id + ", ");
            }
            sLabel = sLabel.TrimEnd(charsToTrim);
            return StartTime.GetHashCode() ^ EndTime.GetHashCode() ^ sLabel.GetHashCode();
        }
    }

    public class CalendarEventField
    {
        public CalendarEventField(string id, string label)
        {
            Id = id;
            Label = label;
        }

        public string Id { get; private set; }

        public string Label { get; private set; }
    }
}
