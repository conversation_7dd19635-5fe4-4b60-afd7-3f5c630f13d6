using System;
using Ifs.Uma.AppData.Expressions;
using Ifs.Uma.Metadata.Cpi;
using Ifs.Uma.Utility;

namespace Ifs.Uma.AppData.Execution
{
    public abstract class Executor<T> where T : ExecutionContext
    {
        protected ILogger Logger { get; }
        protected IExpressionRunner ExpressionRunner { get; }

        protected Executor(ILogger logger, IExpressionRunner expressionRunner)
        {
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
            ExpressionRunner = expressionRunner ?? throw new ArgumentNullException(nameof(expressionRunner));
        }

        protected ExecuteResult ExecuteCommonCall(T context, CpiExecuteCallMethod method, CpiExecuteCallArgs args)
        {
            switch (method)
            {
                case CpiExecuteCallMethod.If:
                    return ExecuteIf(context, (CpiIfCallArgs)args);
                case CpiExecuteCallMethod.Return:
                    return ExecuteReturn(context, (CpiReturnCallArgs)args);
                case CpiExecuteCallMethod.Exit:
                    return ExecuteExit((CpiExitCallArgs)args);
                case CpiExecuteCallMethod.Set:
                    return ExecuteSet(context, (CpiSetCallArgs)args);
                case CpiExecuteCallMethod.Break:
                    return ExecuteBreak(context, (CpiBreakCallArgs)args);
                default:
                    return ExecuteResult.None;
            }
        }

        private ExecuteResult ExecuteIf(T context, CpiIfCallArgs executeArgs)
        {
            CpiExpression expression = executeArgs.OfflineExpression ?? executeArgs.Expression;
            if (expression != null)
            {
                bool result = RunExpressionCheck(context, expression, true);

                if (result)
                {
                    return ExecuteResult.True;
                }
            }

            return ExecuteResult.False;
        }

        private ExecuteResult ExecuteBreak(T context, CpiBreakCallArgs args)
        {
            return new ExecuteResult(null, true, true);
        }

        protected bool RunExpressionCheck(T context, CpiExpression expression, bool nullValue)
        {
            if (expression == null) return nullValue;
            bool? quickCheck = expression.QuickCheck();
            if (quickCheck.HasValue) return quickCheck.Value;

            bool? result = ExpressionRunner.Run(expression, context) as bool?;
            return result.GetValueOrDefault(false);
        }

        private ExecuteResult ExecuteReturn(T context, CpiReturnCallArgs args)
        {
            object value = null;

            if (args.Value != null)
            {
                value = args.Value;
            }
            else if (!string.IsNullOrEmpty(args.Name))
            {
                value = context.GetValue(args.Name);
            }
            else if (args.Return != null)
            {
                value = ExpressionRunner.Run(args.Return, context);
            }

            if (Equals(value, string.Empty))
            {
                value = null;
            }

            return new ExecuteResult(value, true);
        }
        
        private ExecuteResult ExecuteExit(CpiExitCallArgs args)
        {
            return new ExecuteResult(args?.Return, true);
        }

        private ExecuteResult ExecuteSet(T context, CpiSetCallArgs args)
        {
            object value = null;

            if (args.Value != null)
            {
                string argsValueString = args.Value as string;
                if (argsValueString != null)
                {
                    value = context.InterpolateString(argsValueString, true);
                }
                else
                {
                    value = args.Value;
                }
            }
            else if (!string.IsNullOrEmpty(args.Name))
            {
                value = context.GetValue(args.Name);
            }
            else if (args.Expression != null)
            {
                value = ExpressionRunner.Run(args.Expression, context);
            }

            if (Equals(value, string.Empty))
            {
                value = null;
            }

            return new ExecuteResult(value);
        }

        protected static ExecutionException Fail(T context, string message)
        {
            if (context == null)
            {
                return new ExecutionException(message);
            }

            return context.Fail(message);
        }
    }
}
